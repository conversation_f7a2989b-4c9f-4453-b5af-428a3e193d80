(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-096b"],{"9/SQ":function(e,t){e.exports='/* Jison generated parser */\nvar jsonlint = (function(){\nvar parser = {trace: function trace() { },\nyy: {},\nsymbols_: {"error":2,"JSONString":3,"STRING":4,"JSONNumber":5,"NUMBER":6,"JSONNullLiteral":7,"NULL":8,"JSONBooleanLiteral":9,"TRUE":10,"FALSE":11,"JSONText":12,"JSONValue":13,"EOF":14,"JSONObject":15,"JSONArray":16,"{":17,"}":18,"JSONMemberList":19,"JSONMember":20,":":21,",":22,"[":23,"]":24,"JSONElementList":25,"$accept":0,"$end":1},\nterminals_: {2:"error",4:"STRING",6:"NUMBER",8:"NULL",10:"TRUE",11:"FALSE",14:"EOF",17:"{",18:"}",21:":",22:",",23:"[",24:"]"},\nproductions_: [0,[3,1],[5,1],[7,1],[9,1],[9,1],[12,2],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[15,2],[15,3],[20,3],[19,1],[19,3],[16,2],[16,3],[25,1],[25,3]],\nperformAction: function anonymous(yytext,yyleng,yylineno,yy,yystate,$$,_$) {\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 1: // replace escaped characters with actual character\n          this.$ = yytext.replace(/\\\\(\\\\|")/g, "$"+"1")\n                     .replace(/\\\\n/g,\'\\n\')\n                     .replace(/\\\\r/g,\'\\r\')\n                     .replace(/\\\\t/g,\'\\t\')\n                     .replace(/\\\\v/g,\'\\v\')\n                     .replace(/\\\\f/g,\'\\f\')\n                     .replace(/\\\\b/g,\'\\b\');\n        \nbreak;\ncase 2:this.$ = Number(yytext);\nbreak;\ncase 3:this.$ = null;\nbreak;\ncase 4:this.$ = true;\nbreak;\ncase 5:this.$ = false;\nbreak;\ncase 6:return this.$ = $$[$0-1];\nbreak;\ncase 13:this.$ = {};\nbreak;\ncase 14:this.$ = $$[$0-1];\nbreak;\ncase 15:this.$ = [$$[$0-2], $$[$0]];\nbreak;\ncase 16:this.$ = {}; this.$[$$[$0][0]] = $$[$0][1];\nbreak;\ncase 17:this.$ = $$[$0-2]; $$[$0-2][$$[$0][0]] = $$[$0][1];\nbreak;\ncase 18:this.$ = [];\nbreak;\ncase 19:this.$ = $$[$0-1];\nbreak;\ncase 20:this.$ = [$$[$0]];\nbreak;\ncase 21:this.$ = $$[$0-2]; $$[$0-2].push($$[$0]);\nbreak;\n}\n},\ntable: [{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],12:1,13:2,15:7,16:8,17:[1,14],23:[1,15]},{1:[3]},{14:[1,16]},{14:[2,7],18:[2,7],22:[2,7],24:[2,7]},{14:[2,8],18:[2,8],22:[2,8],24:[2,8]},{14:[2,9],18:[2,9],22:[2,9],24:[2,9]},{14:[2,10],18:[2,10],22:[2,10],24:[2,10]},{14:[2,11],18:[2,11],22:[2,11],24:[2,11]},{14:[2,12],18:[2,12],22:[2,12],24:[2,12]},{14:[2,3],18:[2,3],22:[2,3],24:[2,3]},{14:[2,4],18:[2,4],22:[2,4],24:[2,4]},{14:[2,5],18:[2,5],22:[2,5],24:[2,5]},{14:[2,1],18:[2,1],21:[2,1],22:[2,1],24:[2,1]},{14:[2,2],18:[2,2],22:[2,2],24:[2,2]},{3:20,4:[1,12],18:[1,17],19:18,20:19},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:23,15:7,16:8,17:[1,14],23:[1,15],24:[1,21],25:22},{1:[2,6]},{14:[2,13],18:[2,13],22:[2,13],24:[2,13]},{18:[1,24],22:[1,25]},{18:[2,16],22:[2,16]},{21:[1,26]},{14:[2,18],18:[2,18],22:[2,18],24:[2,18]},{22:[1,28],24:[1,27]},{22:[2,20],24:[2,20]},{14:[2,14],18:[2,14],22:[2,14],24:[2,14]},{3:20,4:[1,12],20:29},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:30,15:7,16:8,17:[1,14],23:[1,15]},{14:[2,19],18:[2,19],22:[2,19],24:[2,19]},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:31,15:7,16:8,17:[1,14],23:[1,15]},{18:[2,17],22:[2,17]},{18:[2,15],22:[2,15]},{22:[2,21],24:[2,21]}],\ndefaultActions: {16:[2,6]},\nparseError: function parseError(str, hash) {\n    throw new Error(str);\n},\nparse: function parse(input) {\n    var self = this,\n        stack = [0],\n        vstack = [null], // semantic value stack\n        lstack = [], // location stack\n        table = this.table,\n        yytext = \'\',\n        yylineno = 0,\n        yyleng = 0,\n        recovering = 0,\n        TERROR = 2,\n        EOF = 1;\n\n    //this.reductionCount = this.shiftCount = 0;\n\n    this.lexer.setInput(input);\n    this.lexer.yy = this.yy;\n    this.yy.lexer = this.lexer;\n    if (typeof this.lexer.yylloc == \'undefined\')\n        this.lexer.yylloc = {};\n    var yyloc = this.lexer.yylloc;\n    lstack.push(yyloc);\n\n    if (typeof this.yy.parseError === \'function\')\n        this.parseError = this.yy.parseError;\n\n    function popStack (n) {\n        stack.length = stack.length - 2*n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n\n    function lex() {\n        var token;\n        token = self.lexer.lex() || 1; // $end = 1\n        // if token isn\'t its numeric value, convert\n        if (typeof token !== \'number\') {\n            token = self.symbols_[token] || token;\n        }\n        return token;\n    }\n\n    var symbol, preErrorSymbol, state, action, a, r, yyval={},p,len,newState, expected;\n    while (true) {\n        // retreive state number from top of stack\n        state = stack[stack.length-1];\n\n        // use default actions if available\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol == null)\n                symbol = lex();\n            // read action for current state and first input\n            action = table[state] && table[state][symbol];\n        }\n\n        // handle parse error\n        _handle_error:\n        if (typeof action === \'undefined\' || !action.length || !action[0]) {\n\n            if (!recovering) {\n                // Report error\n                expected = [];\n                for (p in table[state]) if (this.terminals_[p] && p > 2) {\n                    expected.push("\'"+this.terminals_[p]+"\'");\n                }\n                var errStr = \'\';\n                if (this.lexer.showPosition) {\n                    errStr = \'Parse error on line \'+(yylineno+1)+":\\n"+this.lexer.showPosition()+"\\nExpecting "+expected.join(\', \') + ", got \'" + this.terminals_[symbol]+ "\'";\n                } else {\n                    errStr = \'Parse error on line \'+(yylineno+1)+": Unexpected " +\n                                  (symbol == 1 /*EOF*/ ? "end of input" :\n                                              ("\'"+(this.terminals_[symbol] || symbol)+"\'"));\n                }\n                this.parseError(errStr,\n                    {text: this.lexer.match, token: this.terminals_[symbol] || symbol, line: this.lexer.yylineno, loc: yyloc, expected: expected});\n            }\n\n            // just recovered from another error\n            if (recovering == 3) {\n                if (symbol == EOF) {\n                    throw new Error(errStr || \'Parsing halted.\');\n                }\n\n                // discard current lookahead and grab another\n                yyleng = this.lexer.yyleng;\n                yytext = this.lexer.yytext;\n                yylineno = this.lexer.yylineno;\n                yyloc = this.lexer.yylloc;\n                symbol = lex();\n            }\n\n            // try to recover from error\n            while (1) {\n                // check for error recovery rule in this state\n                if ((TERROR.toString()) in table[state]) {\n                    break;\n                }\n                if (state == 0) {\n                    throw new Error(errStr || \'Parsing halted.\');\n                }\n                popStack(1);\n                state = stack[stack.length-1];\n            }\n\n            preErrorSymbol = symbol; // save the lookahead token\n            symbol = TERROR;         // insert generic error symbol as new lookahead\n            state = stack[stack.length-1];\n            action = table[state] && table[state][TERROR];\n            recovering = 3; // allow 3 real symbols to be shifted before reporting a new error\n        }\n\n        // this shouldn\'t happen, unless resolve defaults are off\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error(\'Parse Error: multiple actions possible at state: \'+state+\', token: \'+symbol);\n        }\n\n        switch (action[0]) {\n\n            case 1: // shift\n                //this.shiftCount++;\n\n                stack.push(symbol);\n                vstack.push(this.lexer.yytext);\n                lstack.push(this.lexer.yylloc);\n                stack.push(action[1]); // push state\n                symbol = null;\n                if (!preErrorSymbol) { // normal execution/no error\n                    yyleng = this.lexer.yyleng;\n                    yytext = this.lexer.yytext;\n                    yylineno = this.lexer.yylineno;\n                    yyloc = this.lexer.yylloc;\n                    if (recovering > 0)\n                        recovering--;\n                } else { // error just occurred, resume old lookahead f/ before error\n                    symbol = preErrorSymbol;\n                    preErrorSymbol = null;\n                }\n                break;\n\n            case 2: // reduce\n                //this.reductionCount++;\n\n                len = this.productions_[action[1]][1];\n\n                // perform semantic action\n                yyval.$ = vstack[vstack.length-len]; // default to $$ = $1\n                // default location, uses first token for firsts, last for lasts\n                yyval._$ = {\n                    first_line: lstack[lstack.length-(len||1)].first_line,\n                    last_line: lstack[lstack.length-1].last_line,\n                    first_column: lstack[lstack.length-(len||1)].first_column,\n                    last_column: lstack[lstack.length-1].last_column\n                };\n                r = this.performAction.call(yyval, yytext, yyleng, yylineno, this.yy, action[1], vstack, lstack);\n\n                if (typeof r !== \'undefined\') {\n                    return r;\n                }\n\n                // pop off stack\n                if (len) {\n                    stack = stack.slice(0,-1*len*2);\n                    vstack = vstack.slice(0, -1*len);\n                    lstack = lstack.slice(0, -1*len);\n                }\n\n                stack.push(this.productions_[action[1]][0]);    // push nonterminal (reduce)\n                vstack.push(yyval.$);\n                lstack.push(yyval._$);\n                // goto new state = table[STATE][NONTERMINAL]\n                newState = table[stack[stack.length-2]][stack[stack.length-1]];\n                stack.push(newState);\n                break;\n\n            case 3: // accept\n                return true;\n        }\n\n    }\n\n    return true;\n}};\n/* Jison generated lexer */\nvar lexer = (function(){\nvar lexer = ({EOF:1,\nparseError:function parseError(str, hash) {\n        if (this.yy.parseError) {\n            this.yy.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\nsetInput:function (input) {\n        this._input = input;\n        this._more = this._less = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \'\';\n        this.conditionStack = [\'INITIAL\'];\n        this.yylloc = {first_line:1,first_column:0,last_line:1,last_column:0};\n        return this;\n    },\ninput:function () {\n        var ch = this._input[0];\n        this.yytext+=ch;\n        this.yyleng++;\n        this.match+=ch;\n        this.matched+=ch;\n        var lines = ch.match(/\\n/);\n        if (lines) this.yylineno++;\n        this._input = this._input.slice(1);\n        return ch;\n    },\nunput:function (ch) {\n        this._input = ch + this._input;\n        return this;\n    },\nmore:function () {\n        this._more = true;\n        return this;\n    },\nless:function (n) {\n        this._input = this.match.slice(n) + this._input;\n    },\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \'...\':\'\') + past.substr(-20).replace(/\\n/g, "");\n    },\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20)+(next.length > 20 ? \'...\':\'\')).replace(/\\n/g, "");\n    },\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join("-");\n        return pre + this.upcomingInput() + "\\n" + c+"^";\n    },\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) this.done = true;\n\n        var token,\n            match,\n            tempMatch,\n            index,\n            col,\n            lines;\n        if (!this._more) {\n            this.yytext = \'\';\n            this.match = \'\';\n        }\n        var rules = this._currentRules();\n        for (var i=0;i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (!this.options.flex) break;\n            }\n        }\n        if (match) {\n            lines = match[0].match(/\\n.*/g);\n            if (lines) this.yylineno += lines.length;\n            this.yylloc = {first_line: this.yylloc.last_line,\n                           last_line: this.yylineno+1,\n                           first_column: this.yylloc.last_column,\n                           last_column: lines ? lines[lines.length-1].length-1 : this.yylloc.last_column + match[0].length}\n            this.yytext += match[0];\n            this.match += match[0];\n            this.yyleng = this.yytext.length;\n            this._more = false;\n            this._input = this._input.slice(match[0].length);\n            this.matched += match[0];\n            token = this.performAction.call(this, this.yy, this, rules[index],this.conditionStack[this.conditionStack.length-1]);\n            if (this.done && this._input) this.done = false;\n            if (token) return token;\n            else return;\n        }\n        if (this._input === "") {\n            return this.EOF;\n        } else {\n            this.parseError(\'Lexical error on line \'+(this.yylineno+1)+\'. Unrecognized text.\\n\'+this.showPosition(), \n                    {text: "", token: null, line: this.yylineno});\n        }\n    },\nlex:function lex() {\n        var r = this.next();\n        if (typeof r !== \'undefined\') {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\nbegin:function begin(condition) {\n        this.conditionStack.push(condition);\n    },\npopState:function popState() {\n        return this.conditionStack.pop();\n    },\n_currentRules:function _currentRules() {\n        return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules;\n    },\ntopState:function () {\n        return this.conditionStack[this.conditionStack.length-2];\n    },\npushState:function begin(condition) {\n        this.begin(condition);\n    }});\nlexer.options = {};\nlexer.performAction = function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\n\nvar YYSTATE=YY_START\nswitch($avoiding_name_collisions) {\ncase 0:/* skip whitespace */\nbreak;\ncase 1:return 6\nbreak;\ncase 2:yy_.yytext = yy_.yytext.substr(1,yy_.yyleng-2); return 4\nbreak;\ncase 3:return 17\nbreak;\ncase 4:return 18\nbreak;\ncase 5:return 23\nbreak;\ncase 6:return 24\nbreak;\ncase 7:return 22\nbreak;\ncase 8:return 21\nbreak;\ncase 9:return 10\nbreak;\ncase 10:return 11\nbreak;\ncase 11:return 8\nbreak;\ncase 12:return 14\nbreak;\ncase 13:return \'INVALID\'\nbreak;\n}\n};\nlexer.rules = [/^(?:\\s+)/,/^(?:(-?([0-9]|[1-9][0-9]+))(\\.[0-9]+)?([eE][-+]?[0-9]+)?\\b)/,/^(?:"(?:\\\\[\\\\"bfnrt/]|\\\\u[a-fA-F0-9]{4}|[^\\\\\\0-\\x09\\x0a-\\x1f"])*")/,/^(?:\\{)/,/^(?:\\})/,/^(?:\\[)/,/^(?:\\])/,/^(?:,)/,/^(?::)/,/^(?:true\\b)/,/^(?:false\\b)/,/^(?:null\\b)/,/^(?:$)/,/^(?:.)/];\nlexer.conditions = {"INITIAL":{"rules":[0,1,2,3,4,5,6,7,8,9,10,11,12,13],"inclusive":true}};\n\n\n;\nreturn lexer;})()\nparser.lexer = lexer;\nreturn parser;\n})();\nif (typeof require !== \'undefined\' && typeof exports !== \'undefined\') {\nexports.parser = jsonlint;\nexports.parse = function () { return jsonlint.parse.apply(jsonlint, arguments); }\nexports.main = function commonjsMain(args) {\n    if (!args[1])\n        throw new Error(\'Usage: \'+args[0]+\' FILE\');\n    if (typeof process !== \'undefined\') {\n        var source = require(\'fs\').readFileSync(require(\'path\').join(process.cwd(), args[1]), "utf8");\n    } else {\n        var cwd = require("file").path(require("file").cwd());\n        var source = cwd.join(args[1]).read({charset: "utf-8"});\n    }\n    return exports.parser.parse(source);\n}\nif (typeof module !== \'undefined\' && require.main === module) {\n  exports.main(typeof process !== \'undefined\' ? process.argv.slice(1) : require("system").args);\n}\n}'},DFAU:function(e,t,n){!function(e){"use strict";e.registerHelper("lint","json",function(t){var n=[];if(!window.jsonlint)return window.console&&window.console.error("Error: window.jsonlint not defined, CodeMirror JSON linting cannot run."),n;var r=window.jsonlint.parser||window.jsonlint;r.parseError=function(t,r){var i=r.loc;n.push({from:e.Pos(i.first_line-1,i.first_column),to:e.Pos(i.last_line-1,i.last_column),message:t})};try{r.parse(t)}catch(e){}return n})}(n("FzGJ"))},FTbg:function(e,t,n){!function(e){"use strict";e.defineMode("javascript",function(t,n){var r,i,o=t.indentUnit,l=n.statementIndent,a=n.jsonld,s=n.json||a,u=n.typescript,c=n.wordCharacters||/[\w$\xa1-\uffff]/,f=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),n=e("keyword b"),r=e("keyword c"),i=e("keyword d"),o=e("operator"),l={type:"atom",style:"atom"};return{if:e("if"),while:t,with:t,else:n,do:n,try:n,finally:n,return:i,break:i,continue:i,new:e("new"),delete:r,void:r,throw:r,debugger:e("debugger"),var:e("var"),const:e("var"),let:e("var"),function:e("function"),catch:e("catch"),for:e("for"),switch:e("switch"),case:e("case"),default:e("default"),in:o,typeof:o,instanceof:o,true:l,false:l,null:l,undefined:l,NaN:l,Infinity:l,this:e("this"),class:e("class"),super:e("atom"),yield:r,export:e("export"),import:e("import"),extends:r,await:r}}(),h=/[+\-*&%=<>!?|~^@]/,d=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function p(e,t,n){return r=e,i=n,t}function g(e,t){var n=e.next();if('"'==n||"'"==n)return t.tokenize=function(e){return function(t,n){var r,i=!1;if(a&&"@"==t.peek()&&t.match(d))return n.tokenize=g,p("jsonld-keyword","meta");for(;null!=(r=t.next())&&(r!=e||i);)i=!i&&"\\"==r;return i||(n.tokenize=g),p("string","string")}}(n),t.tokenize(e,t);if("."==n&&e.match(/^\d+(?:[eE][+\-]?\d+)?/))return p("number","number");if("."==n&&e.match(".."))return p("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(n))return p(n);if("="==n&&e.eat(">"))return p("=>","operator");if("0"==n&&e.match(/^(?:x[\da-f]+|o[0-7]+|b[01]+)n?/i))return p("number","number");if(/\d/.test(n))return e.match(/^\d*(?:n|(?:\.\d*)?(?:[eE][+\-]?\d+)?)?/),p("number","number");if("/"==n)return e.eat("*")?(t.tokenize=m,m(e,t)):e.eat("/")?(e.skipToEnd(),p("comment","comment")):Ue(e,t,1)?(function(e){for(var t,n=!1,r=!1;null!=(t=e.next());){if(!n){if("/"==t&&!r)return;"["==t?r=!0:r&&"]"==t&&(r=!1)}n=!n&&"\\"==t}}(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),p("regexp","string-2")):(e.eat("="),p("operator","operator",e.current()));if("`"==n)return t.tokenize=v,v(e,t);if("#"==n)return e.skipToEnd(),p("error","error");if(h.test(n))return">"==n&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=n&&"="!=n||e.eat("="):/[<>*+\-]/.test(n)&&(e.eat(n),">"==n&&e.eat(n))),p("operator","operator",e.current());if(c.test(n)){e.eatWhile(c);var r=e.current();if("."!=t.lastType){if(f.propertyIsEnumerable(r)){var i=f[r];return p(i.type,i.style,r)}if("async"==r&&e.match(/^(\s|\/\*.*?\*\/)*[\[\(\w]/,!1))return p("async","keyword",r)}return p("variable","variable",r)}}function m(e,t){for(var n,r=!1;n=e.next();){if("/"==n&&r){t.tokenize=g;break}r="*"==n}return p("comment","comment")}function v(e,t){for(var n,r=!1;null!=(n=e.next());){if(!r&&("`"==n||"$"==n&&e.eat("{"))){t.tokenize=g;break}r=!r&&"\\"==n}return p("quasi","string-2",e.current())}var y="([{}])";function b(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var n=e.string.indexOf("=>",e.start);if(!(n<0)){if(u){var r=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,n));r&&(n=r.index)}for(var i=0,o=!1,l=n-1;l>=0;--l){var a=e.string.charAt(l),s=y.indexOf(a);if(s>=0&&s<3){if(!i){++l;break}if(0==--i){"("==a&&(o=!0);break}}else if(s>=3&&s<6)++i;else if(c.test(a))o=!0;else{if(/["'\/]/.test(a))return;if(o&&!i){++l;break}}}o&&!i&&(t.fatArrowAt=l)}}var x={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,"jsonld-keyword":!0};function w(e,t,n,r,i,o){this.indented=e,this.column=t,this.type=n,this.prev=i,this.info=o,null!=r&&(this.align=r)}function k(e,t){for(var n=e.localVars;n;n=n.next)if(n.name==t)return!0;for(var r=e.context;r;r=r.prev)for(var n=r.vars;n;n=n.next)if(n.name==t)return!0}var C={state:null,column:null,marked:null,cc:null};function S(){for(var e=arguments.length-1;e>=0;e--)C.cc.push(arguments[e])}function L(){return S.apply(null,arguments),!0}function T(e,t){for(var n=t;n;n=n.next)if(n.name==e)return!0;return!1}function M(e){var t=C.state;if(C.marked="def",t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var r=function e(t,n){if(n){if(n.block){var r=e(t,n.prev);return r?r==n.prev?n:new A(r,n.vars,!0):null}return T(t,n.vars)?n:new A(n.prev,new O(t,n.vars),!1)}return null}(e,t.context);if(null!=r)return void(t.context=r)}else if(!T(e,t.localVars))return void(t.localVars=new O(e,t.localVars));n.globalVars&&!T(e,t.globalVars)&&(t.globalVars=new O(e,t.globalVars))}function N(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function A(e,t,n){this.prev=e,this.vars=t,this.block=n}function O(e,t){this.name=e,this.next=t}var E=new O("this",new O("arguments",null));function D(){C.state.context=new A(C.state.context,C.state.localVars,!1),C.state.localVars=E}function W(){C.state.context=new A(C.state.context,C.state.localVars,!0),C.state.localVars=null}function H(){C.state.localVars=C.state.context.vars,C.state.context=C.state.context.prev}function F(e,t){var n=function(){var n=C.state,r=n.indented;if("stat"==n.lexical.type)r=n.lexical.indented;else for(var i=n.lexical;i&&")"==i.type&&i.align;i=i.prev)r=i.indented;n.lexical=new w(r,C.stream.column(),e,null,n.lexical,t)};return n.lex=!0,n}function I(){var e=C.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function P(e){return function t(n){return n==e?L():";"==e||"}"==n||")"==n||"]"==n?S():L(t)}}function R(e,t){return"var"==e?L(F("vardef",t),ye,P(";"),I):"keyword a"==e?L(F("form"),_,R,I):"keyword b"==e?L(F("form"),R,I):"keyword d"==e?C.stream.match(/^\s*$/,!1)?L():L(F("stat"),G,P(";"),I):"debugger"==e?L(P(";")):"{"==e?L(F("}"),W,le,I,H):";"==e?L():"if"==e?("else"==C.state.lexical.info&&C.state.cc[C.state.cc.length-1]==I&&C.state.cc.pop()(),L(F("form"),_,R,I,Ce)):"function"==e?L(Ae):"for"==e?L(F("form"),Se,R,I):"class"==e||u&&"interface"==t?(C.marked="keyword",L(F("form"),De,I)):"variable"==e?u&&"declare"==t?(C.marked="keyword",L(R)):u&&("module"==t||"enum"==t||"type"==t)&&C.stream.match(/^\s*\w/,!1)?(C.marked="keyword","enum"==t?L(Ge):"type"==t?L(ce,P("operator"),ce,P(";")):L(F("form"),be,P("{"),F("}"),le,I,I)):u&&"namespace"==t?(C.marked="keyword",L(F("form"),B,le,I)):u&&"abstract"==t?(C.marked="keyword",L(R)):L(F("stat"),Q):"switch"==e?L(F("form"),_,P("{"),F("}","switch"),W,le,I,I,H):"case"==e?L(B,P(":")):"default"==e?L(P(":")):"catch"==e?L(F("form"),D,z,R,I,H):"export"==e?L(F("stat"),Ie,I):"import"==e?L(F("stat"),Re,I):"async"==e?L(R):"@"==t?L(B,R):S(F("stat"),B,P(";"),I)}function z(e){if("("==e)return L(Oe,P(")"))}function B(e,t){return j(e,t,!1)}function $(e,t){return j(e,t,!0)}function _(e){return"("!=e?S():L(F(")"),B,P(")"),I)}function j(e,t,n){if(C.state.fatArrowAt==C.stream.start){var r=n?q:X;if("("==e)return L(D,F(")"),ie(Oe,")"),I,P("=>"),r,H);if("variable"==e)return S(D,be,P("=>"),r,H)}var i=n?U:V;return x.hasOwnProperty(e)?L(i):"function"==e?L(Ae,i):"class"==e||u&&"interface"==t?(C.marked="keyword",L(F("form"),Ee,I)):"keyword c"==e||"async"==e?L(n?$:B):"("==e?L(F(")"),G,P(")"),I,i):"operator"==e||"spread"==e?L(n?$:B):"["==e?L(F("]"),je,I,i):"{"==e?oe(te,"}",null,i):"quasi"==e?S(K,i):"new"==e?L(function(e){return function(t){return"."==t?L(e?Z:J):"variable"==t&&u?L(ge,e?U:V):S(e?$:B)}}(n)):"import"==e?L(B):L()}function G(e){return e.match(/[;\}\)\],]/)?S():S(B)}function V(e,t){return","==e?L(B):U(e,t,!1)}function U(e,t,n){var r=0==n?V:U,i=0==n?B:$;return"=>"==e?L(D,n?q:X,H):"operator"==e?/\+\+|--/.test(t)||u&&"!"==t?L(r):u&&"<"==t&&C.stream.match(/^([^>]|<.*?>)*>\s*\(/,!1)?L(F(">"),ie(ce,">"),I,r):"?"==t?L(B,P(":"),i):L(i):"quasi"==e?S(K,r):";"!=e?"("==e?oe($,")","call",r):"."==e?L(ee,r):"["==e?L(F("]"),G,P("]"),I,r):u&&"as"==t?(C.marked="keyword",L(ce,r)):"regexp"==e?(C.state.lastType=C.marked="operator",C.stream.backUp(C.stream.pos-C.stream.start-1),L(i)):void 0:void 0}function K(e,t){return"quasi"!=e?S():"${"!=t.slice(t.length-2)?L(K):L(B,Y)}function Y(e){if("}"==e)return C.marked="string-2",C.state.tokenize=v,L(K)}function X(e){return b(C.stream,C.state),S("{"==e?R:B)}function q(e){return b(C.stream,C.state),S("{"==e?R:$)}function J(e,t){if("target"==t)return C.marked="keyword",L(V)}function Z(e,t){if("target"==t)return C.marked="keyword",L(U)}function Q(e){return":"==e?L(I,R):S(V,P(";"),I)}function ee(e){if("variable"==e)return C.marked="property",L()}function te(e,t){if("async"==e)return C.marked="property",L(te);if("variable"==e||"keyword"==C.style){return C.marked="property","get"==t||"set"==t?L(ne):(u&&C.state.fatArrowAt==C.stream.start&&(n=C.stream.match(/^\s*:\s*/,!1))&&(C.state.fatArrowAt=C.stream.pos+n[0].length),L(re));var n}else{if("number"==e||"string"==e)return C.marked=a?"property":C.style+" property",L(re);if("jsonld-keyword"==e)return L(re);if(u&&N(t))return C.marked="keyword",L(te);if("["==e)return L(B,ae,P("]"),re);if("spread"==e)return L($,re);if("*"==t)return C.marked="keyword",L(te);if(":"==e)return S(re)}}function ne(e){return"variable"!=e?S(re):(C.marked="property",L(Ae))}function re(e){return":"==e?L($):"("==e?S(Ae):void 0}function ie(e,t,n){function r(i,o){if(n?n.indexOf(i)>-1:","==i){var l=C.state.lexical;return"call"==l.info&&(l.pos=(l.pos||0)+1),L(function(n,r){return n==t||r==t?S():S(e)},r)}return i==t||o==t?L():L(P(t))}return function(n,i){return n==t||i==t?L():S(e,r)}}function oe(e,t,n){for(var r=3;r<arguments.length;r++)C.cc.push(arguments[r]);return L(F(t,n),ie(e,t),I)}function le(e){return"}"==e?L():S(R,le)}function ae(e,t){if(u){if(":"==e)return L(ce);if("?"==t)return L(ae)}}function se(e){if(u&&":"==e)return C.stream.match(/^\s*\w+\s+is\b/,!1)?L(B,ue,ce):L(ce)}function ue(e,t){if("is"==t)return C.marked="keyword",L()}function ce(e,t){return"keyof"==t||"typeof"==t?(C.marked="keyword",L("keyof"==t?ce:$)):"variable"==e||"void"==t?(C.marked="type",L(pe)):"string"==e||"number"==e||"atom"==e?L(pe):"["==e?L(F("]"),ie(ce,"]",","),I,pe):"{"==e?L(F("}"),ie(he,"}",",;"),I,pe):"("==e?L(ie(de,")"),fe):"<"==e?L(ie(ce,">"),ce):void 0}function fe(e){if("=>"==e)return L(ce)}function he(e,t){return"variable"==e||"keyword"==C.style?(C.marked="property",L(he)):"?"==t?L(he):":"==e?L(ce):"["==e?L(B,ae,P("]"),he):void 0}function de(e,t){return"variable"==e&&C.stream.match(/^\s*[?:]/,!1)||"?"==t?L(de):":"==e?L(ce):S(ce)}function pe(e,t){return"<"==t?L(F(">"),ie(ce,">"),I,pe):"|"==t||"."==e||"&"==t?L(ce):"["==e?L(P("]"),pe):"extends"==t||"implements"==t?(C.marked="keyword",L(ce)):void 0}function ge(e,t){if("<"==t)return L(F(">"),ie(ce,">"),I,pe)}function me(){return S(ce,ve)}function ve(e,t){if("="==t)return L(ce)}function ye(e,t){return"enum"==t?(C.marked="keyword",L(Ge)):S(be,ae,we,ke)}function be(e,t){return u&&N(t)?(C.marked="keyword",L(be)):"variable"==e?(M(t),L()):"spread"==e?L(be):"["==e?oe(be,"]"):"{"==e?oe(xe,"}"):void 0}function xe(e,t){return"variable"!=e||C.stream.match(/^\s*:/,!1)?("variable"==e&&(C.marked="property"),"spread"==e?L(be):"}"==e?S():L(P(":"),be,we)):(M(t),L(we))}function we(e,t){if("="==t)return L($)}function ke(e){if(","==e)return L(ye)}function Ce(e,t){if("keyword b"==e&&"else"==t)return L(F("form","else"),R,I)}function Se(e,t){return"await"==t?L(Se):"("==e?L(F(")"),Le,P(")"),I):void 0}function Le(e){return"var"==e?L(ye,P(";"),Me):";"==e?L(Me):"variable"==e?L(Te):S(B,P(";"),Me)}function Te(e,t){return"in"==t||"of"==t?(C.marked="keyword",L(B)):L(V,Me)}function Me(e,t){return";"==e?L(Ne):"in"==t||"of"==t?(C.marked="keyword",L(B)):S(B,P(";"),Ne)}function Ne(e){")"!=e&&L(B)}function Ae(e,t){return"*"==t?(C.marked="keyword",L(Ae)):"variable"==e?(M(t),L(Ae)):"("==e?L(D,F(")"),ie(Oe,")"),I,se,R,H):u&&"<"==t?L(F(">"),ie(me,">"),I,Ae):void 0}function Oe(e,t){return"@"==t&&L(B,Oe),"spread"==e?L(Oe):u&&N(t)?(C.marked="keyword",L(Oe)):S(be,ae,we)}function Ee(e,t){return"variable"==e?De(e,t):We(e,t)}function De(e,t){if("variable"==e)return M(t),L(We)}function We(e,t){return"<"==t?L(F(">"),ie(me,">"),I,We):"extends"==t||"implements"==t||u&&","==e?("implements"==t&&(C.marked="keyword"),L(u?ce:B,We)):"{"==e?L(F("}"),He,I):void 0}function He(e,t){return"async"==e||"variable"==e&&("static"==t||"get"==t||"set"==t||u&&N(t))&&C.stream.match(/^\s+[\w$\xa1-\uffff]/,!1)?(C.marked="keyword",L(He)):"variable"==e||"keyword"==C.style?(C.marked="property",L(u?Fe:Ae,He)):"["==e?L(B,ae,P("]"),u?Fe:Ae,He):"*"==t?(C.marked="keyword",L(He)):";"==e?L(He):"}"==e?L():"@"==t?L(B,He):void 0}function Fe(e,t){return"?"==t?L(Fe):":"==e?L(ce,we):"="==t?L($):S(Ae)}function Ie(e,t){return"*"==t?(C.marked="keyword",L(_e,P(";"))):"default"==t?(C.marked="keyword",L(B,P(";"))):"{"==e?L(ie(Pe,"}"),_e,P(";")):S(R)}function Pe(e,t){return"as"==t?(C.marked="keyword",L(P("variable"))):"variable"==e?S($,Pe):void 0}function Re(e){return"string"==e?L():"("==e?S(B):S(ze,Be,_e)}function ze(e,t){return"{"==e?oe(ze,"}"):("variable"==e&&M(t),"*"==t&&(C.marked="keyword"),L($e))}function Be(e){if(","==e)return L(ze,Be)}function $e(e,t){if("as"==t)return C.marked="keyword",L(ze)}function _e(e,t){if("from"==t)return C.marked="keyword",L(B)}function je(e){return"]"==e?L():S(ie($,"]"))}function Ge(){return S(F("form"),be,P("{"),F("}"),ie(Ve,"}"),I,I)}function Ve(){return S(be,we)}function Ue(e,t,n){return t.tokenize==g&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(n||0)))}return H.lex=!0,I.lex=!0,{startState:function(e){var t={tokenize:g,lastType:"sof",cc:[],lexical:new w((e||0)-o,0,"block",!1),localVars:n.localVars,context:n.localVars&&new A(null,null,!1),indented:e||0};return n.globalVars&&"object"==typeof n.globalVars&&(t.globalVars=n.globalVars),t},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),b(e,t)),t.tokenize!=m&&e.eatSpace())return null;var n=t.tokenize(e,t);return"comment"==r?n:(t.lastType="operator"!=r||"++"!=i&&"--"!=i?r:"incdec",function(e,t,n,r,i){var o=e.cc;for(C.state=e,C.stream=i,C.marked=null,C.cc=o,C.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;){var l=o.length?o.pop():s?B:R;if(l(n,r)){for(;o.length&&o[o.length-1].lex;)o.pop()();return C.marked?C.marked:"variable"==n&&k(e,r)?"variable-2":t}}}(t,n,r,i,e))},indent:function(t,r){if(t.tokenize==m)return e.Pass;if(t.tokenize!=g)return 0;var i,a=r&&r.charAt(0),s=t.lexical;if(!/^\s*else\b/.test(r))for(var u=t.cc.length-1;u>=0;--u){var c=t.cc[u];if(c==I)s=s.prev;else if(c!=Ce)break}for(;("stat"==s.type||"form"==s.type)&&("}"==a||(i=t.cc[t.cc.length-1])&&(i==V||i==U)&&!/^[,\.=+\-*:?[\(]/.test(r));)s=s.prev;l&&")"==s.type&&"stat"==s.prev.type&&(s=s.prev);var f=s.type,d=a==f;return"vardef"==f?s.indented+("operator"==t.lastType||","==t.lastType?s.info.length+1:0):"form"==f&&"{"==a?s.indented:"form"==f?s.indented+o:"stat"==f?s.indented+(function(e,t){return"operator"==e.lastType||","==e.lastType||h.test(t.charAt(0))||/[,.]/.test(t.charAt(0))}(t,r)?l||o:0):"switch"!=s.info||d||0==n.doubleIndentSwitch?s.align?s.column+(d?0:1):s.indented+(d?0:o):s.indented+(/^(?:case|default)\b/.test(r)?o:2*o)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:s?null:"/*",blockCommentEnd:s?null:"*/",blockCommentContinue:s?null:" * ",lineComment:s?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:s?"json":"javascript",jsonldMode:a,jsonMode:s,expressionAllowed:Ue,skipExpression:function(e){var t=e.cc[e.cc.length-1];t!=B&&t!=$||e.cc.pop()}}}),e.registerHelper("wordChars","javascript",/[\w$]/),e.defineMIME("text/javascript","javascript"),e.defineMIME("text/ecmascript","javascript"),e.defineMIME("application/javascript","javascript"),e.defineMIME("application/x-javascript","javascript"),e.defineMIME("application/ecmascript","javascript"),e.defineMIME("application/json",{name:"javascript",json:!0}),e.defineMIME("application/x-json",{name:"javascript",json:!0}),e.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),e.defineMIME("text/typescript",{name:"javascript",typescript:!0}),e.defineMIME("application/typescript",{name:"javascript",typescript:!0})}(n("FzGJ"))},FzGJ:function(e,t,n){e.exports=function(){"use strict";var e=navigator.userAgent,t=navigator.platform,n=/gecko\/\d/i.test(e),r=/MSIE \d/.test(e),i=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),o=/Edge\/(\d+)/.exec(e),l=r||i||o,a=l&&(r?document.documentMode||6:+(o||i)[1]),s=!o&&/WebKit\//.test(e),u=s&&/Qt\/\d+\.\d+/.test(e),c=!o&&/Chrome\//.test(e),f=/Opera\//.test(e),h=/Apple Computer/.test(navigator.vendor),d=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),p=/PhantomJS/.test(e),g=!o&&/AppleWebKit/.test(e)&&/Mobile\/\w+/.test(e),m=/Android/.test(e),v=g||m||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),y=g||/Mac/.test(t),b=/\bCrOS\b/.test(e),x=/win/i.test(t),w=f&&e.match(/Version\/(\d*\.\d*)/);w&&(w=Number(w[1])),w&&w>=15&&(f=!1,s=!0);var k=y&&(u||f&&(null==w||w<12.11)),C=n||l&&a>=9;function S(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var L,T=function(e,t){var n=e.className,r=S(t).exec(n);if(r){var i=n.slice(r.index+r[0].length);e.className=n.slice(0,r.index)+(i?r[1]+i:"")}};function M(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function N(e,t){return M(e).appendChild(t)}function A(e,t,n,r){var i=document.createElement(e);if(n&&(i.className=n),r&&(i.style.cssText=r),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function O(e,t,n,r){var i=A(e,t,n,r);return i.setAttribute("role","presentation"),i}function E(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function D(){var e;try{e=document.activeElement}catch(t){e=document.body||null}for(;e&&e.shadowRoot&&e.shadowRoot.activeElement;)e=e.shadowRoot.activeElement;return e}function W(e,t){var n=e.className;S(t).test(n)||(e.className+=(n?" ":"")+t)}function H(e,t){for(var n=e.split(" "),r=0;r<n.length;r++)n[r]&&!S(n[r]).test(t)&&(t+=" "+n[r]);return t}L=document.createRange?function(e,t,n,r){var i=document.createRange();return i.setEnd(r||e,n),i.setStart(e,t),i}:function(e,t,n){var r=document.body.createTextRange();try{r.moveToElementText(e.parentNode)}catch(e){return r}return r.collapse(!0),r.moveEnd("character",n),r.moveStart("character",t),r};var F=function(e){e.select()};function I(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function P(e,t,n){for(var r in t||(t={}),e)!e.hasOwnProperty(r)||!1===n&&t.hasOwnProperty(r)||(t[r]=e[r]);return t}function R(e,t,n,r,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=r||0,l=i||0;;){var a=e.indexOf("\t",o);if(a<0||a>=t)return l+(t-o);l+=a-o,l+=n-l%n,o=a+1}}g?F=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:l&&(F=function(e){try{e.select()}catch(e){}});var z=function(){this.id=null};function B(e,t){for(var n=0;n<e.length;++n)if(e[n]==t)return n;return-1}z.prototype.set=function(e,t){clearTimeout(this.id),this.id=setTimeout(t,e)};var $=30,_={toString:function(){return"CodeMirror.Pass"}},j={scroll:!1},G={origin:"*mouse"},V={origin:"+move"};function U(e,t,n){for(var r=0,i=0;;){var o=e.indexOf("\t",r);-1==o&&(o=e.length);var l=o-r;if(o==e.length||i+l>=t)return r+Math.min(l,t-i);if(i+=o-r,r=o+1,(i+=n-i%n)>=t)return r}}var K=[""];function Y(e){for(;K.length<=e;)K.push(X(K)+" ");return K[e]}function X(e){return e[e.length-1]}function q(e,t){for(var n=[],r=0;r<e.length;r++)n[r]=t(e[r],r);return n}function J(){}function Z(e,t){var n;return Object.create?n=Object.create(e):(J.prototype=e,n=new J),t&&P(t,n),n}var Q=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function ee(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||Q.test(e))}function te(e,t){return t?!!(t.source.indexOf("\\w")>-1&&ee(e))||t.test(e):ee(e)}function ne(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var re=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function ie(e){return e.charCodeAt(0)>=768&&re.test(e)}function oe(e,t,n){for(;(n<0?t>0:t<e.length)&&ie(e.charAt(t));)t+=n;return t}function le(e,t,n){for(var r=t>n?-1:1;;){if(t==n)return t;var i=(t+n)/2,o=r<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:n;e(o)?n=o:t=o+r}}function ae(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var n=e;!n.lines;)for(var r=0;;++r){var i=n.children[r],o=i.chunkSize();if(t<o){n=i;break}t-=o}return n.lines[t]}function se(e,t,n){var r=[],i=t.line;return e.iter(t.line,n.line+1,function(e){var o=e.text;i==n.line&&(o=o.slice(0,n.ch)),i==t.line&&(o=o.slice(t.ch)),r.push(o),++i}),r}function ue(e,t,n){var r=[];return e.iter(t,n,function(e){r.push(e.text)}),r}function ce(e,t){var n=t-e.height;if(n)for(var r=e;r;r=r.parent)r.height+=n}function fe(e){if(null==e.parent)return null;for(var t=e.parent,n=B(t.lines,e),r=t.parent;r;t=r,r=r.parent)for(var i=0;r.children[i]!=t;++i)n+=r.children[i].chunkSize();return n+t.first}function he(e,t){var n=e.first;e:do{for(var r=0;r<e.children.length;++r){var i=e.children[r],o=i.height;if(t<o){e=i;continue e}t-=o,n+=i.chunkSize()}return n}while(!e.lines);for(var l=0;l<e.lines.length;++l){var a=e.lines[l],s=a.height;if(t<s)break;t-=s}return n+l}function de(e,t){return t>=e.first&&t<e.first+e.size}function pe(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function ge(e,t,n){if(void 0===n&&(n=null),!(this instanceof ge))return new ge(e,t,n);this.line=e,this.ch=t,this.sticky=n}function me(e,t){return e.line-t.line||e.ch-t.ch}function ve(e,t){return e.sticky==t.sticky&&0==me(e,t)}function ye(e){return ge(e.line,e.ch)}function be(e,t){return me(e,t)<0?t:e}function xe(e,t){return me(e,t)<0?e:t}function we(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function ke(e,t){if(t.line<e.first)return ge(e.first,0);var n=e.first+e.size-1;return t.line>n?ge(n,ae(e,n).text.length):function(e,t){var n=e.ch;return null==n||n>t?ge(e.line,t):n<0?ge(e.line,0):e}(t,ae(e,t.line).text.length)}function Ce(e,t){for(var n=[],r=0;r<t.length;r++)n[r]=ke(e,t[r]);return n}var Se=!1,Le=!1;function Te(e,t,n){this.marker=e,this.from=t,this.to=n}function Me(e,t){if(e)for(var n=0;n<e.length;++n){var r=e[n];if(r.marker==t)return r}}function Ne(e,t){for(var n,r=0;r<e.length;++r)e[r]!=t&&(n||(n=[])).push(e[r]);return n}function Ae(e,t){if(t.full)return null;var n=de(e,t.from.line)&&ae(e,t.from.line).markedSpans,r=de(e,t.to.line)&&ae(e,t.to.line).markedSpans;if(!n&&!r)return null;var i=t.from.ch,o=t.to.ch,l=0==me(t.from,t.to),a=function(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker,a=null==o.from||(l.inclusiveLeft?o.from<=t:o.from<t);if(a||o.from==t&&"bookmark"==l.type&&(!n||!o.marker.insertLeft)){var s=null==o.to||(l.inclusiveRight?o.to>=t:o.to>t);(r||(r=[])).push(new Te(l,o.from,s?null:o.to))}}return r}(n,i,l),s=function(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker,a=null==o.to||(l.inclusiveRight?o.to>=t:o.to>t);if(a||o.from==t&&"bookmark"==l.type&&(!n||o.marker.insertLeft)){var s=null==o.from||(l.inclusiveLeft?o.from<=t:o.from<t);(r||(r=[])).push(new Te(l,s?null:o.from-t,null==o.to?null:o.to-t))}}return r}(r,o,l),u=1==t.text.length,c=X(t.text).length+(u?i:0);if(a)for(var f=0;f<a.length;++f){var h=a[f];if(null==h.to){var d=Me(s,h.marker);d?u&&(h.to=null==d.to?null:d.to+c):h.to=i}}if(s)for(var p=0;p<s.length;++p){var g=s[p];if(null!=g.to&&(g.to+=c),null==g.from){var m=Me(a,g.marker);m||(g.from=c,u&&(a||(a=[])).push(g))}else g.from+=c,u&&(a||(a=[])).push(g)}a&&(a=Oe(a)),s&&s!=a&&(s=Oe(s));var v=[a];if(!u){var y,b=t.text.length-2;if(b>0&&a)for(var x=0;x<a.length;++x)null==a[x].to&&(y||(y=[])).push(new Te(a[x].marker,null,null));for(var w=0;w<b;++w)v.push(y);v.push(s)}return v}function Oe(e){for(var t=0;t<e.length;++t){var n=e[t];null!=n.from&&n.from==n.to&&!1!==n.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Ee(e){var t=e.markedSpans;if(t){for(var n=0;n<t.length;++n)t[n].marker.detachLine(e);e.markedSpans=null}}function De(e,t){if(t){for(var n=0;n<t.length;++n)t[n].marker.attachLine(e);e.markedSpans=t}}function We(e){return e.inclusiveLeft?-1:0}function He(e){return e.inclusiveRight?1:0}function Fe(e,t){var n=e.lines.length-t.lines.length;if(0!=n)return n;var r=e.find(),i=t.find(),o=me(r.from,i.from)||We(e)-We(t);if(o)return-o;var l=me(r.to,i.to)||He(e)-He(t);return l||t.id-e.id}function Ie(e,t){var n,r=Le&&e.markedSpans;if(r)for(var i=void 0,o=0;o<r.length;++o)(i=r[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!n||Fe(n,i.marker)<0)&&(n=i.marker);return n}function Pe(e){return Ie(e,!0)}function Re(e){return Ie(e,!1)}function ze(e,t){var n,r=Le&&e.markedSpans;if(r)for(var i=0;i<r.length;++i){var o=r[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!n||Fe(n,o.marker)<0)&&(n=o.marker)}return n}function Be(e,t,n,r,i){var o=ae(e,t),l=Le&&o.markedSpans;if(l)for(var a=0;a<l.length;++a){var s=l[a];if(s.marker.collapsed){var u=s.marker.find(0),c=me(u.from,n)||We(s.marker)-We(i),f=me(u.to,r)||He(s.marker)-He(i);if(!(c>=0&&f<=0||c<=0&&f>=0)&&(c<=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?me(u.to,n)>=0:me(u.to,n)>0)||c>=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?me(u.from,r)<=0:me(u.from,r)<0)))return!0}}}function $e(e){for(var t;t=Pe(e);)e=t.find(-1,!0).line;return e}function _e(e,t){var n=ae(e,t),r=$e(n);return n==r?t:fe(r)}function je(e,t){if(t>e.lastLine())return t;var n,r=ae(e,t);if(!Ge(e,r))return t;for(;n=Re(r);)r=n.find(1,!0).line;return fe(r)+1}function Ge(e,t){var n=Le&&t.markedSpans;if(n)for(var r=void 0,i=0;i<n.length;++i)if((r=n[i]).marker.collapsed){if(null==r.from)return!0;if(!r.marker.widgetNode&&0==r.from&&r.marker.inclusiveLeft&&Ve(e,t,r))return!0}}function Ve(e,t,n){if(null==n.to){var r=n.marker.find(1,!0);return Ve(e,r.line,Me(r.line.markedSpans,n.marker))}if(n.marker.inclusiveRight&&n.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==n.to&&(null==i.to||i.to!=n.from)&&(i.marker.inclusiveLeft||n.marker.inclusiveRight)&&Ve(e,t,i))return!0}function Ue(e){for(var t=0,n=(e=$e(e)).parent,r=0;r<n.lines.length;++r){var i=n.lines[r];if(i==e)break;t+=i.height}for(var o=n.parent;o;o=(n=o).parent)for(var l=0;l<o.children.length;++l){var a=o.children[l];if(a==n)break;t+=a.height}return t}function Ke(e){if(0==e.height)return 0;for(var t,n=e.text.length,r=e;t=Pe(r);){var i=t.find(0,!0);r=i.from.line,n+=i.from.ch-i.to.ch}for(r=e;t=Re(r);){var o=t.find(0,!0);n-=r.text.length-o.from.ch,r=o.to.line,n+=r.text.length-o.to.ch}return n}function Ye(e){var t=e.display,n=e.doc;t.maxLine=ae(n,n.first),t.maxLineLength=Ke(t.maxLine),t.maxLineChanged=!0,n.iter(function(e){var n=Ke(e);n>t.maxLineLength&&(t.maxLineLength=n,t.maxLine=e)})}var Xe=null;function qe(e,t,n){var r;Xe=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==n?r=i:Xe=i),o.from==t&&(o.from!=o.to&&"before"!=n?r=i:Xe=i)}return null!=r?r:Xe}var Je=function(){var e="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",t="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function n(n){return n<=247?e.charAt(n):1424<=n&&n<=1524?"R":1536<=n&&n<=1785?t.charAt(n-1536):1774<=n&&n<=2220?"r":8192<=n&&n<=8203?"w":8204==n?"b":"L"}var r=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,i=/[stwN]/,o=/[LRr]/,l=/[Lb1n]/,a=/[1n]/;function s(e,t,n){this.level=e,this.from=t,this.to=n}return function(e,t){var u="ltr"==t?"L":"R";if(0==e.length||"ltr"==t&&!r.test(e))return!1;for(var c=e.length,f=[],h=0;h<c;++h)f.push(n(e.charCodeAt(h)));for(var d=0,p=u;d<c;++d){var g=f[d];"m"==g?f[d]=p:p=g}for(var m=0,v=u;m<c;++m){var y=f[m];"1"==y&&"r"==v?f[m]="n":o.test(y)&&(v=y,"r"==y&&(f[m]="R"))}for(var b=1,x=f[0];b<c-1;++b){var w=f[b];"+"==w&&"1"==x&&"1"==f[b+1]?f[b]="1":","!=w||x!=f[b+1]||"1"!=x&&"n"!=x||(f[b]=x),x=w}for(var k=0;k<c;++k){var C=f[k];if(","==C)f[k]="N";else if("%"==C){var S=void 0;for(S=k+1;S<c&&"%"==f[S];++S);for(var L=k&&"!"==f[k-1]||S<c&&"1"==f[S]?"1":"N",T=k;T<S;++T)f[T]=L;k=S-1}}for(var M=0,N=u;M<c;++M){var A=f[M];"L"==N&&"1"==A?f[M]="L":o.test(A)&&(N=A)}for(var O=0;O<c;++O)if(i.test(f[O])){var E=void 0;for(E=O+1;E<c&&i.test(f[E]);++E);for(var D="L"==(O?f[O-1]:u),W="L"==(E<c?f[E]:u),H=D==W?D?"L":"R":u,F=O;F<E;++F)f[F]=H;O=E-1}for(var I,P=[],R=0;R<c;)if(l.test(f[R])){var z=R;for(++R;R<c&&l.test(f[R]);++R);P.push(new s(0,z,R))}else{var B=R,$=P.length;for(++R;R<c&&"L"!=f[R];++R);for(var _=B;_<R;)if(a.test(f[_])){B<_&&P.splice($,0,new s(1,B,_));var j=_;for(++_;_<R&&a.test(f[_]);++_);P.splice($,0,new s(2,j,_)),B=_}else++_;B<R&&P.splice($,0,new s(1,B,R))}return"ltr"==t&&(1==P[0].level&&(I=e.match(/^\s+/))&&(P[0].from=I[0].length,P.unshift(new s(0,0,I[0].length))),1==X(P).level&&(I=e.match(/\s+$/))&&(X(P).to-=I[0].length,P.push(new s(0,c-I[0].length,c)))),"rtl"==t?P.reverse():P}}();function Ze(e,t){var n=e.order;return null==n&&(n=e.order=Je(e.text,t)),n}var Qe=[],et=function(e,t,n){if(e.addEventListener)e.addEventListener(t,n,!1);else if(e.attachEvent)e.attachEvent("on"+t,n);else{var r=e._handlers||(e._handlers={});r[t]=(r[t]||Qe).concat(n)}};function tt(e,t){return e._handlers&&e._handlers[t]||Qe}function nt(e,t,n){if(e.removeEventListener)e.removeEventListener(t,n,!1);else if(e.detachEvent)e.detachEvent("on"+t,n);else{var r=e._handlers,i=r&&r[t];if(i){var o=B(i,n);o>-1&&(r[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function rt(e,t){var n=tt(e,t);if(n.length)for(var r=Array.prototype.slice.call(arguments,2),i=0;i<n.length;++i)n[i].apply(null,r)}function it(e,t,n){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),rt(e,n||t.type,e,t),ct(t)||t.codemirrorIgnore}function ot(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var n=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),r=0;r<t.length;++r)-1==B(n,t[r])&&n.push(t[r])}function lt(e,t){return tt(e,t).length>0}function at(e){e.prototype.on=function(e,t){et(this,e,t)},e.prototype.off=function(e,t){nt(this,e,t)}}function st(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function ut(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function ct(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function ft(e){st(e),ut(e)}function ht(e){return e.target||e.srcElement}function dt(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),y&&e.ctrlKey&&1==t&&(t=3),t}var pt,gt,mt=function(){if(l&&a<9)return!1;var e=A("div");return"draggable"in e||"dragDrop"in e}();function vt(e){if(null==pt){var t=A("span","​");N(e,A("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(pt=t.offsetWidth<=1&&t.offsetHeight>2&&!(l&&a<8))}var n=pt?A("span","​"):A("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return n.setAttribute("cm-text",""),n}function yt(e){if(null!=gt)return gt;var t=N(e,document.createTextNode("AخA")),n=L(t,0,1).getBoundingClientRect(),r=L(t,1,2).getBoundingClientRect();return M(e),!(!n||n.left==n.right)&&(gt=r.right-n.right<3)}var bt=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,n=[],r=e.length;t<=r;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),l=o.indexOf("\r");-1!=l?(n.push(o.slice(0,l)),t+=l+1):(n.push(o),t=i+1)}return n}:function(e){return e.split(/\r\n?|\n/)},xt=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},wt=function(){var e=A("div");return"oncopy"in e||(e.setAttribute("oncopy","return;"),"function"==typeof e.oncopy)}(),kt=null,Ct={},St={};function Lt(e){if("string"==typeof e&&St.hasOwnProperty(e))e=St[e];else if(e&&"string"==typeof e.name&&St.hasOwnProperty(e.name)){var t=St[e.name];"string"==typeof t&&(t={name:t}),(e=Z(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Lt("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Lt("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Tt(e,t){t=Lt(t);var n=Ct[t.name];if(!n)return Tt(e,"text/plain");var r=n(e,t);if(Mt.hasOwnProperty(t.name)){var i=Mt[t.name];for(var o in i)i.hasOwnProperty(o)&&(r.hasOwnProperty(o)&&(r["_"+o]=r[o]),r[o]=i[o])}if(r.name=t.name,t.helperType&&(r.helperType=t.helperType),t.modeProps)for(var l in t.modeProps)r[l]=t.modeProps[l];return r}var Mt={};function Nt(e,t){var n=Mt.hasOwnProperty(e)?Mt[e]:Mt[e]={};P(t,n)}function At(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var n={};for(var r in t){var i=t[r];i instanceof Array&&(i=i.concat([])),n[r]=i}return n}function Ot(e,t){for(var n;e.innerMode&&(n=e.innerMode(t))&&n.mode!=e;)t=n.state,e=n.mode;return n||{mode:e,state:t}}function Et(e,t,n){return!e.startState||e.startState(t,n)}var Dt=function(e,t,n){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=n};Dt.prototype.eol=function(){return this.pos>=this.string.length},Dt.prototype.sol=function(){return this.pos==this.lineStart},Dt.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},Dt.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},Dt.prototype.eat=function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},Dt.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},Dt.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},Dt.prototype.skipToEnd=function(){this.pos=this.string.length},Dt.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},Dt.prototype.backUp=function(e){this.pos-=e},Dt.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=R(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?R(this.string,this.lineStart,this.tabSize):0)},Dt.prototype.indentation=function(){return R(this.string,null,this.tabSize)-(this.lineStart?R(this.string,this.lineStart,this.tabSize):0)},Dt.prototype.match=function(e,t,n){if("string"!=typeof e){var r=this.string.slice(this.pos).match(e);return r&&r.index>0?null:(r&&!1!==t&&(this.pos+=r[0].length),r)}var i=function(e){return n?e.toLowerCase():e},o=this.string.substr(this.pos,e.length);if(i(o)==i(e))return!1!==t&&(this.pos+=e.length),!0},Dt.prototype.current=function(){return this.string.slice(this.start,this.pos)},Dt.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},Dt.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},Dt.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var Wt=function(e,t){this.state=e,this.lookAhead=t},Ht=function(e,t,n,r){this.state=t,this.doc=e,this.line=n,this.maxLookAhead=r||0,this.baseTokens=null,this.baseTokenPos=1};function Ft(e,t,n,r){var i=[e.state.modeGen],o={};Gt(e,t.text,e.doc.mode,n,function(e,t){return i.push(e,t)},o,r);for(var l=n.state,a=function(r){n.baseTokens=i;var a=e.state.overlays[r],s=1,u=0;n.state=!0,Gt(e,t.text,a.mode,n,function(e,t){for(var n=s;u<e;){var r=i[s];r>e&&i.splice(s,1,e,i[s+1],r),s+=2,u=Math.min(e,r)}if(t)if(a.opaque)i.splice(n,s-n,e,"overlay "+t),s=n+2;else for(;n<s;n+=2){var o=i[n+1];i[n+1]=(o?o+" ":"")+"overlay "+t}},o),n.state=l,n.baseTokens=null,n.baseTokenPos=1},s=0;s<e.state.overlays.length;++s)a(s);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function It(e,t,n){if(!t.styles||t.styles[0]!=e.state.modeGen){var r=Pt(e,fe(t)),i=t.text.length>e.options.maxHighlightLength&&At(e.doc.mode,r.state),o=Ft(e,t,r);i&&(r.state=i),t.stateAfter=r.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),n===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function Pt(e,t,n){var r=e.doc,i=e.display;if(!r.mode.startState)return new Ht(r,!0,t);var o=function(e,t,n){for(var r,i,o=e.doc,l=n?-1:t-(e.doc.mode.innerMode?1e3:100),a=t;a>l;--a){if(a<=o.first)return o.first;var s=ae(o,a-1),u=s.stateAfter;if(u&&(!n||a+(u instanceof Wt?u.lookAhead:0)<=o.modeFrontier))return a;var c=R(s.text,null,e.options.tabSize);(null==i||r>c)&&(i=a-1,r=c)}return i}(e,t,n),l=o>r.first&&ae(r,o-1).stateAfter,a=l?Ht.fromSaved(r,l,o):new Ht(r,Et(r.mode),o);return r.iter(o,t,function(n){Rt(e,n.text,a);var r=a.line;n.stateAfter=r==t-1||r%5==0||r>=i.viewFrom&&r<i.viewTo?a.save():null,a.nextLine()}),n&&(r.modeFrontier=a.line),a}function Rt(e,t,n,r){var i=e.doc.mode,o=new Dt(t,e.options.tabSize,n);for(o.start=o.pos=r||0,""==t&&zt(i,n.state);!o.eol();)Bt(i,o,n.state),o.start=o.pos}function zt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var n=Ot(e,t);return n.mode.blankLine?n.mode.blankLine(n.state):void 0}}function Bt(e,t,n,r){for(var i=0;i<10;i++){r&&(r[0]=Ot(e,n).mode);var o=e.token(t,n);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}Ht.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},Ht.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},Ht.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},Ht.fromSaved=function(e,t,n){return t instanceof Wt?new Ht(e,At(e.mode,t.state),n,t.lookAhead):new Ht(e,At(e.mode,t),n)},Ht.prototype.save=function(e){var t=!1!==e?At(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new Wt(t,this.maxLookAhead):t};var $t=function(e,t,n){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=n};function _t(e,t,n,r){var i,o=e.doc,l=o.mode;t=ke(o,t);var a,s=ae(o,t.line),u=Pt(e,t.line,n),c=new Dt(s.text,e.options.tabSize,u);for(r&&(a=[]);(r||c.pos<t.ch)&&!c.eol();)c.start=c.pos,i=Bt(l,c,u.state),r&&a.push(new $t(c,i,At(o.mode,u.state)));return r?a:new $t(c,i,u.state)}function jt(e,t){if(e)for(;;){var n=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!n)break;e=e.slice(0,n.index)+e.slice(n.index+n[0].length);var r=n[1]?"bgClass":"textClass";null==t[r]?t[r]=n[2]:new RegExp("(?:^|s)"+n[2]+"(?:$|s)").test(t[r])||(t[r]+=" "+n[2])}return e}function Gt(e,t,n,r,i,o,l){var a=n.flattenSpans;null==a&&(a=e.options.flattenSpans);var s,u=0,c=null,f=new Dt(t,e.options.tabSize,r),h=e.options.addModeClass&&[null];for(""==t&&jt(zt(n,r.state),o);!f.eol();){if(f.pos>e.options.maxHighlightLength?(a=!1,l&&Rt(e,t,r,f.pos),f.pos=t.length,s=null):s=jt(Bt(n,f,r.state,h),o),h){var d=h[0].name;d&&(s="m-"+(s?d+" "+s:d))}if(!a||c!=s){for(;u<f.start;)u=Math.min(f.start,u+5e3),i(u,c);c=s}f.start=f.pos}for(;u<f.pos;){var p=Math.min(f.pos,u+5e3);i(p,c),u=p}}var Vt=function(e,t,n){this.text=e,De(this,t),this.height=n?n(this):1};function Ut(e){e.parent=null,Ee(e)}Vt.prototype.lineNo=function(){return fe(this)},at(Vt);var Kt={},Yt={};function Xt(e,t){if(!e||/^\s*$/.test(e))return null;var n=t.addModeClass?Yt:Kt;return n[e]||(n[e]=e.replace(/\S+/g,"cm-$&"))}function qt(e,t){var n=O("span",null,null,s?"padding-right: .1px":null),r={pre:O("pre",[n],"CodeMirror-line"),content:n,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:(l||s)&&e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,a=void 0;r.pos=0,r.addToken=Zt,yt(e.display.measure)&&(a=Ze(o,e.doc.direction))&&(r.addToken=Qt(r.addToken,a)),r.map=[];var u=t!=e.display.externalMeasured&&fe(o);tn(o,r,It(e,o,u)),o.styleClasses&&(o.styleClasses.bgClass&&(r.bgClass=H(o.styleClasses.bgClass,r.bgClass||"")),o.styleClasses.textClass&&(r.textClass=H(o.styleClasses.textClass,r.textClass||""))),0==r.map.length&&r.map.push(0,0,r.content.appendChild(vt(e.display.measure))),0==i?(t.measure.map=r.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(r.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(s){var c=r.content.lastChild;(/\bcm-tab\b/.test(c.className)||c.querySelector&&c.querySelector(".cm-tab"))&&(r.content.className="cm-tab-wrap-hack")}return rt(e,"renderLine",e,t.line,r.pre),r.pre.className&&(r.textClass=H(r.pre.className,r.textClass||"")),r}function Jt(e){var t=A("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function Zt(e,t,n,r,i,o,s){if(t){var u,c=e.splitSpaces?function(e,t){if(e.length>1&&!/  /.test(e))return e;for(var n=t,r="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!n||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),r+=o,n=" "==o}return r}(t,e.trailingSpace):t,f=e.cm.state.specialChars,h=!1;if(f.test(t)){u=document.createDocumentFragment();for(var d=0;;){f.lastIndex=d;var p=f.exec(t),g=p?p.index-d:t.length-d;if(g){var m=document.createTextNode(c.slice(d,d+g));l&&a<9?u.appendChild(A("span",[m])):u.appendChild(m),e.map.push(e.pos,e.pos+g,m),e.col+=g,e.pos+=g}if(!p)break;d+=g+1;var v=void 0;if("\t"==p[0]){var y=e.cm.options.tabSize,b=y-e.col%y;(v=u.appendChild(A("span",Y(b),"cm-tab"))).setAttribute("role","presentation"),v.setAttribute("cm-text","\t"),e.col+=b}else"\r"==p[0]||"\n"==p[0]?((v=u.appendChild(A("span","\r"==p[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",p[0]),e.col+=1):((v=e.cm.options.specialCharPlaceholder(p[0])).setAttribute("cm-text",p[0]),l&&a<9?u.appendChild(A("span",[v])):u.appendChild(v),e.col+=1);e.map.push(e.pos,e.pos+1,v),e.pos++}}else e.col+=t.length,u=document.createTextNode(c),e.map.push(e.pos,e.pos+t.length,u),l&&a<9&&(h=!0),e.pos+=t.length;if(e.trailingSpace=32==c.charCodeAt(t.length-1),n||r||i||h||s){var x=n||"";r&&(x+=r),i&&(x+=i);var w=A("span",[u],x,s);return o&&(w.title=o),e.content.appendChild(w)}e.content.appendChild(u)}}function Qt(e,t){return function(n,r,i,o,l,a,s){i=i?i+" cm-force-border":"cm-force-border";for(var u=n.pos,c=u+r.length;;){for(var f=void 0,h=0;h<t.length&&!((f=t[h]).to>u&&f.from<=u);h++);if(f.to>=c)return e(n,r,i,o,l,a,s);e(n,r.slice(0,f.to-u),i,o,null,a,s),o=null,r=r.slice(f.to-u),u=f.to}}}function en(e,t,n,r){var i=!r&&n.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!r&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",n.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function tn(e,t,n){var r=e.markedSpans,i=e.text,o=0;if(r)for(var l,a,s,u,c,f,h,d=i.length,p=0,g=1,m="",v=0;;){if(v==p){s=u=c=f=a="",h=null,v=1/0;for(var y=[],b=void 0,x=0;x<r.length;++x){var w=r[x],k=w.marker;"bookmark"==k.type&&w.from==p&&k.widgetNode?y.push(k):w.from<=p&&(null==w.to||w.to>p||k.collapsed&&w.to==p&&w.from==p)?(null!=w.to&&w.to!=p&&v>w.to&&(v=w.to,u=""),k.className&&(s+=" "+k.className),k.css&&(a=(a?a+";":"")+k.css),k.startStyle&&w.from==p&&(c+=" "+k.startStyle),k.endStyle&&w.to==v&&(b||(b=[])).push(k.endStyle,w.to),k.title&&!f&&(f=k.title),k.collapsed&&(!h||Fe(h.marker,k)<0)&&(h=w)):w.from>p&&v>w.from&&(v=w.from)}if(b)for(var C=0;C<b.length;C+=2)b[C+1]==v&&(u+=" "+b[C]);if(!h||h.from==p)for(var S=0;S<y.length;++S)en(t,0,y[S]);if(h&&(h.from||0)==p){if(en(t,(null==h.to?d+1:h.to)-p,h.marker,null==h.from),null==h.to)return;h.to==p&&(h=!1)}}if(p>=d)break;for(var L=Math.min(d,v);;){if(m){var T=p+m.length;if(!h){var M=T>L?m.slice(0,L-p):m;t.addToken(t,M,l?l+s:s,c,p+M.length==v?u:"",f,a)}if(T>=L){m=m.slice(L-p),p=L;break}p=T,c=""}m=i.slice(o,o=n[g++]),l=Xt(n[g++],t.cm.options)}}else for(var N=1;N<n.length;N+=2)t.addToken(t,i.slice(o,o=n[N]),Xt(n[N+1],t.cm.options))}function nn(e,t,n){this.line=t,this.rest=function(e){for(var t,n;t=Re(e);)e=t.find(1,!0).line,(n||(n=[])).push(e);return n}(t),this.size=this.rest?fe(X(this.rest))-n+1:1,this.node=this.text=null,this.hidden=Ge(e,t)}function rn(e,t,n){for(var r,i=[],o=t;o<n;o=r){var l=new nn(e.doc,ae(e.doc,o),o);r=o+l.size,i.push(l)}return i}var on=null,ln=null;function an(e,t){var n=tt(e,t);if(n.length){var r,i=Array.prototype.slice.call(arguments,2);on?r=on.delayedCallbacks:ln?r=ln:(r=ln=[],setTimeout(sn,0));for(var o=function(e){r.push(function(){return n[e].apply(null,i)})},l=0;l<n.length;++l)o(l)}}function sn(){var e=ln;ln=null;for(var t=0;t<e.length;++t)e[t]()}function un(e,t,n,r){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?hn(e,t):"gutter"==o?pn(e,t,n,r):"class"==o?dn(e,t):"widget"==o&&gn(e,t,r)}t.changes=null}function cn(e){return e.node==e.text&&(e.node=A("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),l&&a<8&&(e.node.style.zIndex=2)),e.node}function fn(e,t){var n=e.display.externalMeasured;return n&&n.line==t.line?(e.display.externalMeasured=null,t.measure=n.measure,n.built):qt(e,t)}function hn(e,t){var n=t.text.className,r=fn(e,t);t.text==t.node&&(t.node=r.pre),t.text.parentNode.replaceChild(r.pre,t.text),t.text=r.pre,r.bgClass!=t.bgClass||r.textClass!=t.textClass?(t.bgClass=r.bgClass,t.textClass=r.textClass,dn(e,t)):n&&(t.text.className=n)}function dn(e,t){!function(e,t){var n=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(n&&(n+=" CodeMirror-linebackground"),t.background)n?t.background.className=n:(t.background.parentNode.removeChild(t.background),t.background=null);else if(n){var r=cn(t);t.background=r.insertBefore(A("div",null,n),r.firstChild),e.display.input.setUneditable(t.background)}}(e,t),t.line.wrapClass?cn(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var n=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=n||""}function pn(e,t,n,r){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=cn(t);t.gutterBackground=A("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px; width: "+r.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var l=cn(t),a=t.gutter=A("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px");if(e.display.input.setUneditable(a),l.insertBefore(a,t.text),t.line.gutterClass&&(a.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=a.appendChild(A("div",pe(e.options,n),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+r.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var s=0;s<e.options.gutters.length;++s){var u=e.options.gutters[s],c=o.hasOwnProperty(u)&&o[u];c&&a.appendChild(A("div",[c],"CodeMirror-gutter-elt","left: "+r.gutterLeft[u]+"px; width: "+r.gutterWidth[u]+"px"))}}}function gn(e,t,n){t.alignable&&(t.alignable=null);for(var r=t.node.firstChild,i=void 0;r;r=i)i=r.nextSibling,"CodeMirror-linewidget"==r.className&&t.node.removeChild(r);vn(e,t,n)}function mn(e,t,n,r){var i=fn(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),dn(e,t),pn(e,t,n,r),vn(e,t,r),t.node}function vn(e,t,n){if(yn(e,t.line,t,n,!0),t.rest)for(var r=0;r<t.rest.length;r++)yn(e,t.rest[r],t,n,!1)}function yn(e,t,n,r,i){if(t.widgets)for(var o=cn(n),l=0,a=t.widgets;l<a.length;++l){var s=a[l],u=A("div",[s.node],"CodeMirror-linewidget");s.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),bn(s,u,n,r),e.display.input.setUneditable(u),i&&s.above?o.insertBefore(u,n.gutter||n.text):o.appendChild(u),an(s,"redraw")}}function bn(e,t,n,r){if(e.noHScroll){(n.alignable||(n.alignable=[])).push(t);var i=r.wrapperWidth;t.style.left=r.fixedPos+"px",e.coverGutter||(i-=r.gutterTotalWidth,t.style.paddingLeft=r.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-r.gutterTotalWidth+"px"))}function xn(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!E(document.body,e.node)){var n="position: relative;";e.coverGutter&&(n+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(n+="width: "+t.display.wrapper.clientWidth+"px;"),N(t.display.measure,A("div",[e.node],null,n))}return e.height=e.node.parentNode.offsetHeight}function wn(e,t){for(var n=ht(t);n!=e.wrapper;n=n.parentNode)if(!n||1==n.nodeType&&"true"==n.getAttribute("cm-ignore-events")||n.parentNode==e.sizer&&n!=e.mover)return!0}function kn(e){return e.lineSpace.offsetTop}function Cn(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Sn(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=N(e.measure,A("pre","x")),n=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,r={left:parseInt(n.paddingLeft),right:parseInt(n.paddingRight)};return isNaN(r.left)||isNaN(r.right)||(e.cachedPaddingH=r),r}function Ln(e){return $-e.display.nativeBarWidth}function Tn(e){return e.display.scroller.clientWidth-Ln(e)-e.display.barWidth}function Mn(e){return e.display.scroller.clientHeight-Ln(e)-e.display.barHeight}function Nn(e,t,n){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};for(var r=0;r<e.rest.length;r++)if(e.rest[r]==t)return{map:e.measure.maps[r],cache:e.measure.caches[r]};for(var i=0;i<e.rest.length;i++)if(fe(e.rest[i])>n)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}function An(e,t,n,r){return Dn(e,En(e,t),n,r)}function On(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[ar(e,t)];var n=e.display.externalMeasured;return n&&t>=n.lineN&&t<n.lineN+n.size?n:void 0}function En(e,t){var n=fe(t),r=On(e,n);r&&!r.text?r=null:r&&r.changes&&(un(e,r,n,nr(e)),e.curOp.forceUpdate=!0),r||(r=function(e,t){var n=fe(t=$e(t)),r=e.display.externalMeasured=new nn(e.doc,t,n);r.lineN=n;var i=r.built=qt(e,r);return r.text=i.pre,N(e.display.lineMeasure,i.pre),r}(e,t));var i=Nn(r,t,n);return{line:t,view:r,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function Dn(e,t,n,r,i){t.before&&(n=-1);var o,s=n+(r||"");return t.cache.hasOwnProperty(s)?o=t.cache[s]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(function(e,t,n){var r=e.options.lineWrapping,i=r&&Tn(e);if(!t.measure.heights||r&&t.measure.width!=i){var o=t.measure.heights=[];if(r){t.measure.width=i;for(var l=t.text.firstChild.getClientRects(),a=0;a<l.length-1;a++){var s=l[a],u=l[a+1];Math.abs(s.bottom-u.bottom)>2&&o.push((s.bottom+u.top)/2-n.top)}}o.push(n.bottom-n.top)}}(e,t.view,t.rect),t.hasHeights=!0),(o=function(e,t,n,r){var i,o=Fn(t.map,n,r),s=o.node,u=o.start,c=o.end,f=o.collapse;if(3==s.nodeType){for(var h=0;h<4;h++){for(;u&&ie(t.line.text.charAt(o.coverStart+u));)--u;for(;o.coverStart+c<o.coverEnd&&ie(t.line.text.charAt(o.coverStart+c));)++c;if((i=l&&a<9&&0==u&&c==o.coverEnd-o.coverStart?s.parentNode.getBoundingClientRect():In(L(s,u,c).getClientRects(),r)).left||i.right||0==u)break;c=u,u-=1,f="right"}l&&a<11&&(i=function(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!function(e){if(null!=kt)return kt;var t=N(e,A("span","x")),n=t.getBoundingClientRect(),r=L(t,0,1).getBoundingClientRect();return kt=Math.abs(n.left-r.left)>1}(e))return t;var n=screen.logicalXDPI/screen.deviceXDPI,r=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*n,right:t.right*n,top:t.top*r,bottom:t.bottom*r}}(e.display.measure,i))}else{var d;u>0&&(f=r="right"),i=e.options.lineWrapping&&(d=s.getClientRects()).length>1?d["right"==r?d.length-1:0]:s.getBoundingClientRect()}if(l&&a<9&&!u&&(!i||!i.left&&!i.right)){var p=s.parentNode.getClientRects()[0];i=p?{left:p.left,right:p.left+tr(e.display),top:p.top,bottom:p.bottom}:Hn}for(var g=i.top-t.rect.top,m=i.bottom-t.rect.top,v=(g+m)/2,y=t.view.measure.heights,b=0;b<y.length-1&&!(v<y[b]);b++);var x=b?y[b-1]:0,w=y[b],k={left:("right"==f?i.right:i.left)-t.rect.left,right:("left"==f?i.left:i.right)-t.rect.left,top:x,bottom:w};return i.left||i.right||(k.bogus=!0),e.options.singleCursorHeightPerLine||(k.rtop=g,k.rbottom=m),k}(e,t,n,r)).bogus||(t.cache[s]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var Wn,Hn={left:0,right:0,top:0,bottom:0};function Fn(e,t,n){for(var r,i,o,l,a,s,u=0;u<e.length;u+=3)if(a=e[u],s=e[u+1],t<a?(i=0,o=1,l="left"):t<s?o=1+(i=t-a):(u==e.length-3||t==s&&e[u+3]>t)&&(i=(o=s-a)-1,t>=s&&(l="right")),null!=i){if(r=e[u+2],a==s&&n==(r.insertLeft?"left":"right")&&(l=n),"left"==n&&0==i)for(;u&&e[u-2]==e[u-3]&&e[u-1].insertLeft;)r=e[2+(u-=3)],l="left";if("right"==n&&i==s-a)for(;u<e.length-3&&e[u+3]==e[u+4]&&!e[u+5].insertLeft;)r=e[(u+=3)+2],l="right";break}return{node:r,start:i,end:o,collapse:l,coverStart:a,coverEnd:s}}function In(e,t){var n=Hn;if("left"==t)for(var r=0;r<e.length&&(n=e[r]).left==n.right;r++);else for(var i=e.length-1;i>=0&&(n=e[i]).left==n.right;i--);return n}function Pn(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function Rn(e){e.display.externalMeasure=null,M(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)Pn(e.display.view[t])}function zn(e){Rn(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function Bn(){return c&&m?-(document.body.getBoundingClientRect().left-parseInt(getComputedStyle(document.body).marginLeft)):window.pageXOffset||(document.documentElement||document.body).scrollLeft}function $n(){return c&&m?-(document.body.getBoundingClientRect().top-parseInt(getComputedStyle(document.body).marginTop)):window.pageYOffset||(document.documentElement||document.body).scrollTop}function _n(e){var t=0;if(e.widgets)for(var n=0;n<e.widgets.length;++n)e.widgets[n].above&&(t+=xn(e.widgets[n]));return t}function jn(e,t,n,r,i){if(!i){var o=_n(t);n.top+=o,n.bottom+=o}if("line"==r)return n;r||(r="local");var l=Ue(t);if("local"==r?l+=kn(e.display):l-=e.display.viewOffset,"page"==r||"window"==r){var a=e.display.lineSpace.getBoundingClientRect();l+=a.top+("window"==r?0:$n());var s=a.left+("window"==r?0:Bn());n.left+=s,n.right+=s}return n.top+=l,n.bottom+=l,n}function Gn(e,t,n){if("div"==n)return t;var r=t.left,i=t.top;if("page"==n)r-=Bn(),i-=$n();else if("local"==n||!n){var o=e.display.sizer.getBoundingClientRect();r+=o.left,i+=o.top}var l=e.display.lineSpace.getBoundingClientRect();return{left:r-l.left,top:i-l.top}}function Vn(e,t,n,r,i){return r||(r=ae(e.doc,t.line)),jn(e,r,An(e,r,t.ch,i),n)}function Un(e,t,n,r,i,o){function l(t,l){var a=Dn(e,i,t,l?"right":"left",o);return l?a.left=a.right:a.right=a.left,jn(e,r,a,n)}r=r||ae(e.doc,t.line),i||(i=En(e,r));var a=Ze(r,e.doc.direction),s=t.ch,u=t.sticky;if(s>=r.text.length?(s=r.text.length,u="before"):s<=0&&(s=0,u="after"),!a)return l("before"==u?s-1:s,"before"==u);function c(e,t,n){var r=a[t],i=1==r.level;return l(n?e-1:e,i!=n)}var f=qe(a,s,u),h=Xe,d=c(s,f,"before"==u);return null!=h&&(d.other=c(s,h,"before"!=u)),d}function Kn(e,t){var n=0;t=ke(e.doc,t),e.options.lineWrapping||(n=tr(e.display)*t.ch);var r=ae(e.doc,t.line),i=Ue(r)+kn(e.display);return{left:n,right:n,top:i,bottom:i+r.height}}function Yn(e,t,n,r,i){var o=ge(e,t,n);return o.xRel=i,r&&(o.outside=!0),o}function Xn(e,t,n){var r=e.doc;if((n+=e.display.viewOffset)<0)return Yn(r.first,0,null,!0,-1);var i=he(r,n),o=r.first+r.size-1;if(i>o)return Yn(r.first+r.size-1,ae(r,o).text.length,null,!0,1);t<0&&(t=0);for(var l=ae(r,i);;){var a=Qn(e,l,i,t,n),s=ze(l,a.ch+(a.xRel>0?1:0));if(!s)return a;var u=s.find(1);if(u.line==i)return u;l=ae(r,i=u.line)}}function qn(e,t,n,r){r-=_n(t);var i=t.text.length,o=le(function(t){return Dn(e,n,t-1).bottom<=r},i,0);return i=le(function(t){return Dn(e,n,t).top>r},o,i),{begin:o,end:i}}function Jn(e,t,n,r){n||(n=En(e,t));var i=jn(e,t,Dn(e,n,r),"line").top;return qn(e,t,n,i)}function Zn(e,t,n,r){return!(e.bottom<=n)&&(e.top>n||(r?e.left:e.right)>t)}function Qn(e,t,n,r,i){i-=Ue(t);var o=En(e,t),l=_n(t),a=0,s=t.text.length,u=!0,c=Ze(t,e.doc.direction);if(c){var f=(e.options.lineWrapping?function(e,t,n,r,i,o,l){var a=qn(e,t,r,l),s=a.begin,u=a.end;/\s/.test(t.text.charAt(u-1))&&u--;for(var c=null,f=null,h=0;h<i.length;h++){var d=i[h];if(!(d.from>=u||d.to<=s)){var p=1!=d.level,g=Dn(e,r,p?Math.min(u,d.to)-1:Math.max(s,d.from)).right,m=g<o?o-g+1e9:g-o;(!c||f>m)&&(c=d,f=m)}}return c||(c=i[i.length-1]),c.from<s&&(c={from:s,to:c.to,level:c.level}),c.to>u&&(c={from:c.from,to:u,level:c.level}),c}:function(e,t,n,r,i,o,l){var a=le(function(a){var s=i[a],u=1!=s.level;return Zn(Un(e,ge(n,u?s.to:s.from,u?"before":"after"),"line",t,r),o,l,!0)},0,i.length-1),s=i[a];if(a>0){var u=1!=s.level,c=Un(e,ge(n,u?s.from:s.to,u?"after":"before"),"line",t,r);Zn(c,o,l,!0)&&c.top>l&&(s=i[a-1])}return s})(e,t,n,o,c,r,i);u=1!=f.level,a=u?f.from:f.to-1,s=u?f.to:f.from-1}var h,d,p=null,g=null,m=le(function(t){var n=Dn(e,o,t);return n.top+=l,n.bottom+=l,!!Zn(n,r,i,!1)&&(n.top<=i&&n.left<=r&&(p=t,g=n),!0)},a,s),v=!1;if(g){var y=r-g.left<g.right-r,b=y==u;m=p+(b?0:1),d=b?"after":"before",h=y?g.left:g.right}else{u||m!=s&&m!=a||m++,d=0==m?"after":m==t.text.length?"before":Dn(e,o,m-(u?1:0)).bottom+l<=i==u?"after":"before";var x=Un(e,ge(n,m,d),"line",t,o);h=x.left,v=i<x.top||i>=x.bottom}return m=oe(t.text,m,1),Yn(n,m,d,v,r-h)}function er(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==Wn){Wn=A("pre");for(var t=0;t<49;++t)Wn.appendChild(document.createTextNode("x")),Wn.appendChild(A("br"));Wn.appendChild(document.createTextNode("x"))}N(e.measure,Wn);var n=Wn.offsetHeight/50;return n>3&&(e.cachedTextHeight=n),M(e.measure),n||1}function tr(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=A("span","xxxxxxxxxx"),n=A("pre",[t]);N(e.measure,n);var r=t.getBoundingClientRect(),i=(r.right-r.left)/10;return i>2&&(e.cachedCharWidth=i),i||10}function nr(e){for(var t=e.display,n={},r={},i=t.gutters.clientLeft,o=t.gutters.firstChild,l=0;o;o=o.nextSibling,++l)n[e.options.gutters[l]]=o.offsetLeft+o.clientLeft+i,r[e.options.gutters[l]]=o.clientWidth;return{fixedPos:rr(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:n,gutterWidth:r,wrapperWidth:t.wrapper.clientWidth}}function rr(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function ir(e){var t=er(e.display),n=e.options.lineWrapping,r=n&&Math.max(5,e.display.scroller.clientWidth/tr(e.display)-3);return function(i){if(Ge(e.doc,i))return 0;var o=0;if(i.widgets)for(var l=0;l<i.widgets.length;l++)i.widgets[l].height&&(o+=i.widgets[l].height);return n?o+(Math.ceil(i.text.length/r)||1)*t:o+t}}function or(e){var t=e.doc,n=ir(e);t.iter(function(e){var t=n(e);t!=e.height&&ce(e,t)})}function lr(e,t,n,r){var i=e.display;if(!n&&"true"==ht(t).getAttribute("cm-not-content"))return null;var o,l,a=i.lineSpace.getBoundingClientRect();try{o=t.clientX-a.left,l=t.clientY-a.top}catch(t){return null}var s,u=Xn(e,o,l);if(r&&1==u.xRel&&(s=ae(e.doc,u.line).text).length==u.ch){var c=R(s,s.length,e.options.tabSize)-s.length;u=ge(u.line,Math.max(0,Math.round((o-Sn(e.display).left)/tr(e.display))-c))}return u}function ar(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var n=e.display.view,r=0;r<n.length;r++)if((t-=n[r].size)<0)return r}function sr(e){e.display.input.showSelection(e.display.input.prepareSelection())}function ur(e,t){void 0===t&&(t=!0);for(var n=e.doc,r={},i=r.cursors=document.createDocumentFragment(),o=r.selection=document.createDocumentFragment(),l=0;l<n.sel.ranges.length;l++)if(t||l!=n.sel.primIndex){var a=n.sel.ranges[l];if(!(a.from().line>=e.display.viewTo||a.to().line<e.display.viewFrom)){var s=a.empty();(s||e.options.showCursorWhenSelecting)&&cr(e,a.head,i),s||hr(e,a,o)}}return r}function cr(e,t,n){var r=Un(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=n.appendChild(A("div"," ","CodeMirror-cursor"));if(i.style.left=r.left+"px",i.style.top=r.top+"px",i.style.height=Math.max(0,r.bottom-r.top)*e.options.cursorHeight+"px",r.other){var o=n.appendChild(A("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));o.style.display="",o.style.left=r.other.left+"px",o.style.top=r.other.top+"px",o.style.height=.85*(r.other.bottom-r.other.top)+"px"}}function fr(e,t){return e.top-t.top||e.left-t.left}function hr(e,t,n){var r=e.display,i=e.doc,o=document.createDocumentFragment(),l=Sn(e.display),a=l.left,s=Math.max(r.sizerWidth,Tn(e)-r.sizer.offsetLeft)-l.right,u="ltr"==i.direction;function c(e,t,n,r){t<0&&(t=0),t=Math.round(t),r=Math.round(r),o.appendChild(A("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==n?s-e:n)+"px;\n                             height: "+(r-t)+"px"))}function f(t,n,r){var o,l,f=ae(i,t),h=f.text.length;function d(n,r){return Vn(e,ge(t,n),"div",f,r)}function p(t,n,r){var i=Jn(e,f,null,t),o="ltr"==n==("after"==r)?"left":"right",l="after"==r?i.begin:i.end-(/\s/.test(f.text.charAt(i.end-1))?2:1);return d(l,o)[o]}var g=Ze(f,i.direction);return function(e,t,n,r){if(!e)return r(t,n,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var l=e[o];(l.from<n&&l.to>t||t==n&&l.to==t)&&(r(Math.max(l.from,t),Math.min(l.to,n),1==l.level?"rtl":"ltr",o),i=!0)}i||r(t,n,"ltr")}(g,n||0,null==r?h:r,function(e,t,i,f){var m="ltr"==i,v=d(e,m?"left":"right"),y=d(t-1,m?"right":"left"),b=null==n&&0==e,x=null==r&&t==h,w=0==f,k=!g||f==g.length-1;if(y.top-v.top<=3){var C=(u?b:x)&&w,S=(u?x:b)&&k,L=C?a:(m?v:y).left,T=S?s:(m?y:v).right;c(L,v.top,T-L,v.bottom)}else{var M,N,A,O;m?(M=u&&b&&w?a:v.left,N=u?s:p(e,i,"before"),A=u?a:p(t,i,"after"),O=u&&x&&k?s:y.right):(M=u?p(e,i,"before"):a,N=!u&&b&&w?s:v.right,A=!u&&x&&k?a:y.left,O=u?p(t,i,"after"):s),c(M,v.top,N-M,v.bottom),v.bottom<y.top&&c(a,v.bottom,null,y.top),c(A,y.top,O-A,y.bottom)}(!o||fr(v,o)<0)&&(o=v),fr(y,o)<0&&(o=y),(!l||fr(v,l)<0)&&(l=v),fr(y,l)<0&&(l=y)}),{start:o,end:l}}var h=t.from(),d=t.to();if(h.line==d.line)f(h.line,h.ch,d.ch);else{var p=ae(i,h.line),g=ae(i,d.line),m=$e(p)==$e(g),v=f(h.line,h.ch,m?p.text.length+1:null).end,y=f(d.line,m?0:null,d.ch).start;m&&(v.top<y.top-2?(c(v.right,v.top,null,v.bottom),c(a,y.top,y.left,y.bottom)):c(v.right,v.top,y.left-v.right,v.bottom)),v.bottom<y.top&&c(a,v.bottom,null,y.top)}n.appendChild(o)}function dr(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var n=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval(function(){return t.cursorDiv.style.visibility=(n=!n)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function pr(e){e.state.focused||(e.display.input.focus(),mr(e))}function gr(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,vr(e))},100)}function mr(e,t){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(rt(e,"focus",e,t),e.state.focused=!0,W(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),s&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),dr(e))}function vr(e,t){e.state.delayingBlurEvent||(e.state.focused&&(rt(e,"blur",e,t),e.state.focused=!1,T(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function yr(e){for(var t=e.display,n=t.lineDiv.offsetTop,r=0;r<t.view.length;r++){var i=t.view[r],o=void 0;if(!i.hidden){if(l&&a<8){var s=i.node.offsetTop+i.node.offsetHeight;o=s-n,n=s}else{var u=i.node.getBoundingClientRect();o=u.bottom-u.top}var c=i.line.height-o;if(o<2&&(o=er(t)),(c>.005||c<-.005)&&(ce(i.line,o),br(i.line),i.rest))for(var f=0;f<i.rest.length;f++)br(i.rest[f])}}}function br(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var n=e.widgets[t],r=n.node.parentNode;r&&(n.height=r.offsetHeight)}}function xr(e,t,n){var r=n&&null!=n.top?Math.max(0,n.top):e.scroller.scrollTop;r=Math.floor(r-kn(e));var i=n&&null!=n.bottom?n.bottom:r+e.wrapper.clientHeight,o=he(t,r),l=he(t,i);if(n&&n.ensure){var a=n.ensure.from.line,s=n.ensure.to.line;a<o?(o=a,l=he(t,Ue(ae(t,a))+e.wrapper.clientHeight)):Math.min(s,t.lastLine())>=l&&(o=he(t,Ue(ae(t,s))-e.wrapper.clientHeight),l=s)}return{from:o,to:Math.max(l,o+1)}}function wr(e){var t=e.display,n=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var r=rr(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=r+"px",l=0;l<n.length;l++)if(!n[l].hidden){e.options.fixedGutter&&(n[l].gutter&&(n[l].gutter.style.left=o),n[l].gutterBackground&&(n[l].gutterBackground.style.left=o));var a=n[l].alignable;if(a)for(var s=0;s<a.length;s++)a[s].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=r+i+"px")}}function kr(e){if(!e.options.lineNumbers)return!1;var t=e.doc,n=pe(e.options,t.first+t.size-1),r=e.display;if(n.length!=r.lineNumChars){var i=r.measure.appendChild(A("div",[A("div",n)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,l=i.offsetWidth-o;return r.lineGutter.style.width="",r.lineNumInnerWidth=Math.max(o,r.lineGutter.offsetWidth-l)+1,r.lineNumWidth=r.lineNumInnerWidth+l,r.lineNumChars=r.lineNumInnerWidth?n.length:-1,r.lineGutter.style.width=r.lineNumWidth+"px",si(e),!0}return!1}function Cr(e,t){var n=e.display,r=er(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:n.scroller.scrollTop,o=Mn(e),l={};t.bottom-t.top>o&&(t.bottom=t.top+o);var a=e.doc.height+Cn(n),s=t.top<r,u=t.bottom>a-r;if(t.top<i)l.scrollTop=s?0:t.top;else if(t.bottom>i+o){var c=Math.min(t.top,(u?a:t.bottom)-o);c!=i&&(l.scrollTop=c)}var f=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:n.scroller.scrollLeft,h=Tn(e)-(e.options.fixedGutter?n.gutters.offsetWidth:0),d=t.right-t.left>h;return d&&(t.right=t.left+h),t.left<10?l.scrollLeft=0:t.left<f?l.scrollLeft=Math.max(0,t.left-(d?0:10)):t.right>h+f-3&&(l.scrollLeft=t.right+(d?0:10)-h),l}function Sr(e,t){null!=t&&(Mr(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function Lr(e){Mr(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function Tr(e,t,n){null==t&&null==n||Mr(e),null!=t&&(e.curOp.scrollLeft=t),null!=n&&(e.curOp.scrollTop=n)}function Mr(e){var t=e.curOp.scrollToPos;if(t){e.curOp.scrollToPos=null;var n=Kn(e,t.from),r=Kn(e,t.to);Nr(e,n,r,t.margin)}}function Nr(e,t,n,r){var i=Cr(e,{left:Math.min(t.left,n.left),top:Math.min(t.top,n.top)-r,right:Math.max(t.right,n.right),bottom:Math.max(t.bottom,n.bottom)+r});Tr(e,i.scrollLeft,i.scrollTop)}function Ar(e,t){Math.abs(e.doc.scrollTop-t)<2||(n||ai(e,{top:t}),Or(e,t,!0),n&&ai(e),ni(e,100))}function Or(e,t,n){t=Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t),(e.display.scroller.scrollTop!=t||n)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function Er(e,t,n,r){t=Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth),(n?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!r||(e.doc.scrollLeft=t,wr(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function Dr(e){var t=e.display,n=t.gutters.offsetWidth,r=Math.round(e.doc.height+Cn(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?n:0,docHeight:r,scrollHeight:r+Ln(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:n}}var Wr=function(e,t,n){this.cm=n;var r=this.vert=A("div",[A("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=A("div",[A("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");r.tabIndex=i.tabIndex=-1,e(r),e(i),et(r,"scroll",function(){r.clientHeight&&t(r.scrollTop,"vertical")}),et(i,"scroll",function(){i.clientWidth&&t(i.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,l&&a<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};Wr.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,n=e.scrollHeight>e.clientHeight+1,r=e.nativeBarWidth;if(n){this.vert.style.display="block",this.vert.style.bottom=t?r+"px":"0";var i=e.viewHeight-(t?r:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=n?r+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(n?r:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==r&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:n?r:0,bottom:t?r:0}},Wr.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},Wr.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},Wr.prototype.zeroWidthHack=function(){var e=y&&!d?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new z,this.disableVert=new z},Wr.prototype.enableZeroWidthBar=function(e,t,n){e.style.pointerEvents="auto",t.set(1e3,function r(){var i=e.getBoundingClientRect(),o="vert"==n?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1);o!=e?e.style.pointerEvents="none":t.set(1e3,r)})},Wr.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var Hr=function(){};function Fr(e,t){t||(t=Dr(e));var n=e.display.barWidth,r=e.display.barHeight;Ir(e,t);for(var i=0;i<4&&n!=e.display.barWidth||r!=e.display.barHeight;i++)n!=e.display.barWidth&&e.options.lineWrapping&&yr(e),Ir(e,Dr(e)),n=e.display.barWidth,r=e.display.barHeight}function Ir(e,t){var n=e.display,r=n.scrollbars.update(t);n.sizer.style.paddingRight=(n.barWidth=r.right)+"px",n.sizer.style.paddingBottom=(n.barHeight=r.bottom)+"px",n.heightForcer.style.borderBottom=r.bottom+"px solid transparent",r.right&&r.bottom?(n.scrollbarFiller.style.display="block",n.scrollbarFiller.style.height=r.bottom+"px",n.scrollbarFiller.style.width=r.right+"px"):n.scrollbarFiller.style.display="",r.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(n.gutterFiller.style.display="block",n.gutterFiller.style.height=r.bottom+"px",n.gutterFiller.style.width=t.gutterWidth+"px"):n.gutterFiller.style.display=""}Hr.prototype.update=function(){return{bottom:0,right:0}},Hr.prototype.setScrollLeft=function(){},Hr.prototype.setScrollTop=function(){},Hr.prototype.clear=function(){};var Pr={native:Wr,null:Hr};function Rr(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&T(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new Pr[e.options.scrollbarStyle](function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),et(t,"mousedown",function(){e.state.focused&&setTimeout(function(){return e.display.input.focus()},0)}),t.setAttribute("cm-not-content","true")},function(t,n){"horizontal"==n?Er(e,t):Ar(e,t)},e),e.display.scrollbars.addClass&&W(e.display.wrapper,e.display.scrollbars.addClass)}var zr=0;function Br(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:null,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++zr},function(e){on?on.ops.push(e):e.ownsGroup=on={ops:[e],delayedCallbacks:[]}}(e.curOp)}function $r(e){var t=e.curOp;!function(e,t){var n=e.ownsGroup;if(n)try{!function(e){var t=e.delayedCallbacks,n=0;do{for(;n<t.length;n++)t[n].call(null);for(var r=0;r<e.ops.length;r++){var i=e.ops[r];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(n<t.length)}(n)}finally{on=null,t(n)}}(t,function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;!function(e){for(var t=e.ops,n=0;n<t.length;n++)_r(t[n]);for(var r=0;r<t.length;r++)jr(t[r]);for(var i=0;i<t.length;i++)Gr(t[i]);for(var o=0;o<t.length;o++)Vr(t[o]);for(var l=0;l<t.length;l++)Ur(t[l])}(e)})}function _r(e){var t=e.cm,n=t.display;!function(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=Ln(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=Ln(e)+"px",t.scrollbarsClipped=!0)}(t),e.updateMaxLine&&Ye(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<n.viewFrom||e.scrollToPos.to.line>=n.viewTo)||n.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new ii(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function jr(e){e.updatedDisplay=e.mustUpdate&&oi(e.cm,e.update)}function Gr(e){var t=e.cm,n=t.display;e.updatedDisplay&&yr(t),e.barMeasure=Dr(t),n.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=An(t,n.maxLine,n.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(n.scroller.clientWidth,n.sizer.offsetLeft+e.adjustWidthTo+Ln(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,n.sizer.offsetLeft+e.adjustWidthTo-Tn(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=n.input.prepareSelection())}function Vr(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&Er(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var n=e.focus&&e.focus==D();e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,n),(e.updatedDisplay||e.startHeight!=t.doc.height)&&Fr(t,e.barMeasure),e.updatedDisplay&&ui(t,e.barMeasure),e.selectionChanged&&dr(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),n&&pr(e.cm)}function Ur(e){var t=e.cm,n=t.display,r=t.doc;if(e.updatedDisplay&&li(t,e.update),null==n.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(n.wheelStartX=n.wheelStartY=null),null!=e.scrollTop&&Or(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&Er(t,e.scrollLeft,!0,!0),e.scrollToPos){var i=function(e,t,n,r){var i;null==r&&(r=0),e.options.lineWrapping||t!=n||(t=t.ch?ge(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t,n="before"==t.sticky?ge(t.line,t.ch+1,"before"):t);for(var o=0;o<5;o++){var l=!1,a=Un(e,t),s=n&&n!=t?Un(e,n):a;i={left:Math.min(a.left,s.left),top:Math.min(a.top,s.top)-r,right:Math.max(a.left,s.left),bottom:Math.max(a.bottom,s.bottom)+r};var u=Cr(e,i),c=e.doc.scrollTop,f=e.doc.scrollLeft;if(null!=u.scrollTop&&(Ar(e,u.scrollTop),Math.abs(e.doc.scrollTop-c)>1&&(l=!0)),null!=u.scrollLeft&&(Er(e,u.scrollLeft),Math.abs(e.doc.scrollLeft-f)>1&&(l=!0)),!l)break}return i}(t,ke(r,e.scrollToPos.from),ke(r,e.scrollToPos.to),e.scrollToPos.margin);!function(e,t){if(!it(e,"scrollCursorIntoView")){var n=e.display,r=n.sizer.getBoundingClientRect(),i=null;if(t.top+r.top<0?i=!0:t.bottom+r.top>(window.innerHeight||document.documentElement.clientHeight)&&(i=!1),null!=i&&!p){var o=A("div","​",null,"position: absolute;\n                         top: "+(t.top-n.viewOffset-kn(e.display))+"px;\n                         height: "+(t.bottom-t.top+Ln(e)+n.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(o),o.scrollIntoView(i),e.display.lineSpace.removeChild(o)}}}(t,i)}var o=e.maybeHiddenMarkers,l=e.maybeUnhiddenMarkers;if(o)for(var a=0;a<o.length;++a)o[a].lines.length||rt(o[a],"hide");if(l)for(var s=0;s<l.length;++s)l[s].lines.length&&rt(l[s],"unhide");n.wrapper.offsetHeight&&(r.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&rt(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function Kr(e,t){if(e.curOp)return t();Br(e);try{return t()}finally{$r(e)}}function Yr(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Br(e);try{return t.apply(e,arguments)}finally{$r(e)}}}function Xr(e){return function(){if(this.curOp)return e.apply(this,arguments);Br(this);try{return e.apply(this,arguments)}finally{$r(this)}}}function qr(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);Br(t);try{return e.apply(this,arguments)}finally{$r(t)}}}function Jr(e,t,n,r){null==t&&(t=e.doc.first),null==n&&(n=e.doc.first+e.doc.size),r||(r=0);var i=e.display;if(r&&n<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)Le&&_e(e.doc,t)<i.viewTo&&Qr(e);else if(n<=i.viewFrom)Le&&je(e.doc,n+r)>i.viewFrom?Qr(e):(i.viewFrom+=r,i.viewTo+=r);else if(t<=i.viewFrom&&n>=i.viewTo)Qr(e);else if(t<=i.viewFrom){var o=ei(e,n,n+r,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=r):Qr(e)}else if(n>=i.viewTo){var l=ei(e,t,t,-1);l?(i.view=i.view.slice(0,l.index),i.viewTo=l.lineN):Qr(e)}else{var a=ei(e,t,t,-1),s=ei(e,n,n+r,1);a&&s?(i.view=i.view.slice(0,a.index).concat(rn(e,a.lineN,s.lineN)).concat(i.view.slice(s.index)),i.viewTo+=r):Qr(e)}var u=i.externalMeasured;u&&(n<u.lineN?u.lineN+=r:t<u.lineN+u.size&&(i.externalMeasured=null))}function Zr(e,t,n){e.curOp.viewChanged=!0;var r=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(r.externalMeasured=null),!(t<r.viewFrom||t>=r.viewTo)){var o=r.view[ar(e,t)];if(null!=o.node){var l=o.changes||(o.changes=[]);-1==B(l,n)&&l.push(n)}}}function Qr(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function ei(e,t,n,r){var i,o=ar(e,t),l=e.display.view;if(!Le||n==e.doc.first+e.doc.size)return{index:o,lineN:n};for(var a=e.display.viewFrom,s=0;s<o;s++)a+=l[s].size;if(a!=t){if(r>0){if(o==l.length-1)return null;i=a+l[o].size-t,o++}else i=a-t;t+=i,n+=i}for(;_e(e.doc,n)!=n;){if(o==(r<0?0:l.length-1))return null;n+=r*l[o-(r<0?1:0)].size,o+=r}return{index:o,lineN:n}}function ti(e){for(var t=e.display.view,n=0,r=0;r<t.length;r++){var i=t[r];i.hidden||i.node&&!i.changes||++n}return n}function ni(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,I(ri,e))}function ri(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var n=+new Date+e.options.workTime,r=Pt(e,t.highlightFrontier),i=[];t.iter(r.line,Math.min(t.first+t.size,e.display.viewTo+500),function(o){if(r.line>=e.display.viewFrom){var l=o.styles,a=o.text.length>e.options.maxHighlightLength?At(t.mode,r.state):null,s=Ft(e,o,r,!0);a&&(r.state=a),o.styles=s.styles;var u=o.styleClasses,c=s.classes;c?o.styleClasses=c:u&&(o.styleClasses=null);for(var f=!l||l.length!=o.styles.length||u!=c&&(!u||!c||u.bgClass!=c.bgClass||u.textClass!=c.textClass),h=0;!f&&h<l.length;++h)f=l[h]!=o.styles[h];f&&i.push(r.line),o.stateAfter=r.save(),r.nextLine()}else o.text.length<=e.options.maxHighlightLength&&Rt(e,o.text,r),o.stateAfter=r.line%5==0?r.save():null,r.nextLine();if(+new Date>n)return ni(e,e.options.workDelay),!0}),t.highlightFrontier=r.line,t.modeFrontier=Math.max(t.modeFrontier,r.line),i.length&&Kr(e,function(){for(var t=0;t<i.length;t++)Zr(e,i[t],"text")})}}var ii=function(e,t,n){var r=e.display;this.viewport=t,this.visible=xr(r,e.doc,t),this.editorIsHidden=!r.wrapper.offsetWidth,this.wrapperHeight=r.wrapper.clientHeight,this.wrapperWidth=r.wrapper.clientWidth,this.oldDisplayWidth=Tn(e),this.force=n,this.dims=nr(e),this.events=[]};function oi(e,t){var n=e.display,r=e.doc;if(t.editorIsHidden)return Qr(e),!1;if(!t.force&&t.visible.from>=n.viewFrom&&t.visible.to<=n.viewTo&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo)&&n.renderedView==n.view&&0==ti(e))return!1;kr(e)&&(Qr(e),t.dims=nr(e));var i=r.first+r.size,o=Math.max(t.visible.from-e.options.viewportMargin,r.first),l=Math.min(i,t.visible.to+e.options.viewportMargin);n.viewFrom<o&&o-n.viewFrom<20&&(o=Math.max(r.first,n.viewFrom)),n.viewTo>l&&n.viewTo-l<20&&(l=Math.min(i,n.viewTo)),Le&&(o=_e(e.doc,o),l=je(e.doc,l));var a=o!=n.viewFrom||l!=n.viewTo||n.lastWrapHeight!=t.wrapperHeight||n.lastWrapWidth!=t.wrapperWidth;!function(e,t,n){var r=e.display;0==r.view.length||t>=r.viewTo||n<=r.viewFrom?(r.view=rn(e,t,n),r.viewFrom=t):(r.viewFrom>t?r.view=rn(e,t,r.viewFrom).concat(r.view):r.viewFrom<t&&(r.view=r.view.slice(ar(e,t))),r.viewFrom=t,r.viewTo<n?r.view=r.view.concat(rn(e,r.viewTo,n)):r.viewTo>n&&(r.view=r.view.slice(0,ar(e,n)))),r.viewTo=n}(e,o,l),n.viewOffset=Ue(ae(e.doc,n.viewFrom)),e.display.mover.style.top=n.viewOffset+"px";var u=ti(e);if(!a&&0==u&&!t.force&&n.renderedView==n.view&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo))return!1;var c=function(e){if(e.hasFocus())return null;var t=D();if(!t||!E(e.display.lineDiv,t))return null;var n={activeElt:t};if(window.getSelection){var r=window.getSelection();r.anchorNode&&r.extend&&E(e.display.lineDiv,r.anchorNode)&&(n.anchorNode=r.anchorNode,n.anchorOffset=r.anchorOffset,n.focusNode=r.focusNode,n.focusOffset=r.focusOffset)}return n}(e);return u>4&&(n.lineDiv.style.display="none"),function(e,t,n){var r=e.display,i=e.options.lineNumbers,o=r.lineDiv,l=o.firstChild;function a(t){var n=t.nextSibling;return s&&y&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),n}for(var u=r.view,c=r.viewFrom,f=0;f<u.length;f++){var h=u[f];if(h.hidden);else if(h.node&&h.node.parentNode==o){for(;l!=h.node;)l=a(l);var d=i&&null!=t&&t<=c&&h.lineNumber;h.changes&&(B(h.changes,"gutter")>-1&&(d=!1),un(e,h,c,n)),d&&(M(h.lineNumber),h.lineNumber.appendChild(document.createTextNode(pe(e.options,c)))),l=h.node.nextSibling}else{var p=mn(e,h,c,n);o.insertBefore(p,l)}c+=h.size}for(;l;)l=a(l)}(e,n.updateLineNumbers,t.dims),u>4&&(n.lineDiv.style.display=""),n.renderedView=n.view,function(e){if(e&&e.activeElt&&e.activeElt!=D()&&(e.activeElt.focus(),e.anchorNode&&E(document.body,e.anchorNode)&&E(document.body,e.focusNode))){var t=window.getSelection(),n=document.createRange();n.setEnd(e.anchorNode,e.anchorOffset),n.collapse(!1),t.removeAllRanges(),t.addRange(n),t.extend(e.focusNode,e.focusOffset)}}(c),M(n.cursorDiv),M(n.selectionDiv),n.gutters.style.height=n.sizer.style.minHeight=0,a&&(n.lastWrapHeight=t.wrapperHeight,n.lastWrapWidth=t.wrapperWidth,ni(e,400)),n.updateLineNumbers=null,!0}function li(e,t){for(var n=t.viewport,r=!0;(r&&e.options.lineWrapping&&t.oldDisplayWidth!=Tn(e)||(n&&null!=n.top&&(n={top:Math.min(e.doc.height+Cn(e.display)-Mn(e),n.top)}),t.visible=xr(e.display,e.doc,n),!(t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)))&&oi(e,t);r=!1){yr(e);var i=Dr(e);sr(e),Fr(e,i),ui(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function ai(e,t){var n=new ii(e,t);if(oi(e,n)){yr(e),li(e,n);var r=Dr(e);sr(e),Fr(e,r),ui(e,r),n.finish()}}function si(e){var t=e.display.gutters.offsetWidth;e.display.sizer.style.marginLeft=t+"px"}function ui(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Ln(e)+"px"}function ci(e){var t=e.display.gutters,n=e.options.gutters;M(t);for(var r=0;r<n.length;++r){var i=n[r],o=t.appendChild(A("div",null,"CodeMirror-gutter "+i));"CodeMirror-linenumbers"==i&&(e.display.lineGutter=o,o.style.width=(e.display.lineNumWidth||1)+"px")}t.style.display=r?"":"none",si(e)}function fi(e){var t=B(e.gutters,"CodeMirror-linenumbers");-1==t&&e.lineNumbers?e.gutters=e.gutters.concat(["CodeMirror-linenumbers"]):t>-1&&!e.lineNumbers&&(e.gutters=e.gutters.slice(0),e.gutters.splice(t,1))}ii.prototype.signal=function(e,t){lt(e,t)&&this.events.push(arguments)},ii.prototype.finish=function(){for(var e=0;e<this.events.length;e++)rt.apply(null,this.events[e])};var hi=0,di=null;function pi(e){var t=e.wheelDeltaX,n=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==n&&e.detail&&e.axis==e.VERTICAL_AXIS?n=e.detail:null==n&&(n=e.wheelDelta),{x:t,y:n}}function gi(e){var t=pi(e);return t.x*=di,t.y*=di,t}function mi(e,t){var r=pi(t),i=r.x,o=r.y,l=e.display,a=l.scroller,u=a.scrollWidth>a.clientWidth,c=a.scrollHeight>a.clientHeight;if(i&&u||o&&c){if(o&&y&&s)e:for(var h=t.target,d=l.view;h!=a;h=h.parentNode)for(var p=0;p<d.length;p++)if(d[p].node==h){e.display.currentWheelTarget=h;break e}if(i&&!n&&!f&&null!=di)return o&&c&&Ar(e,Math.max(0,a.scrollTop+o*di)),Er(e,Math.max(0,a.scrollLeft+i*di)),(!o||o&&c)&&st(t),void(l.wheelStartX=null);if(o&&null!=di){var g=o*di,m=e.doc.scrollTop,v=m+l.wrapper.clientHeight;g<0?m=Math.max(0,m+g-50):v=Math.min(e.doc.height,v+g+50),ai(e,{top:m,bottom:v})}hi<20&&(null==l.wheelStartX?(l.wheelStartX=a.scrollLeft,l.wheelStartY=a.scrollTop,l.wheelDX=i,l.wheelDY=o,setTimeout(function(){if(null!=l.wheelStartX){var e=a.scrollLeft-l.wheelStartX,t=a.scrollTop-l.wheelStartY,n=t&&l.wheelDY&&t/l.wheelDY||e&&l.wheelDX&&e/l.wheelDX;l.wheelStartX=l.wheelStartY=null,n&&(di=(di*hi+n)/(hi+1),++hi)}},200)):(l.wheelDX+=i,l.wheelDY+=o))}}l?di=-.53:n?di=15:c?di=-.7:h&&(di=-1/3);var vi=function(e,t){this.ranges=e,this.primIndex=t};vi.prototype.primary=function(){return this.ranges[this.primIndex]},vi.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var n=this.ranges[t],r=e.ranges[t];if(!ve(n.anchor,r.anchor)||!ve(n.head,r.head))return!1}return!0},vi.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new yi(ye(this.ranges[t].anchor),ye(this.ranges[t].head));return new vi(e,this.primIndex)},vi.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},vi.prototype.contains=function(e,t){t||(t=e);for(var n=0;n<this.ranges.length;n++){var r=this.ranges[n];if(me(t,r.from())>=0&&me(e,r.to())<=0)return n}return-1};var yi=function(e,t){this.anchor=e,this.head=t};function bi(e,t){var n=e[t];e.sort(function(e,t){return me(e.from(),t.from())}),t=B(e,n);for(var r=1;r<e.length;r++){var i=e[r],o=e[r-1];if(me(o.to(),i.from())>=0){var l=xe(o.from(),i.from()),a=be(o.to(),i.to()),s=o.empty()?i.from()==i.head:o.from()==o.head;r<=t&&--t,e.splice(--r,2,new yi(s?a:l,s?l:a))}}return new vi(e,t)}function xi(e,t){return new vi([new yi(e,t||e)],0)}function wi(e){return e.text?ge(e.from.line+e.text.length-1,X(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function ki(e,t){if(me(e,t.from)<0)return e;if(me(e,t.to)<=0)return wi(t);var n=e.line+t.text.length-(t.to.line-t.from.line)-1,r=e.ch;return e.line==t.to.line&&(r+=wi(t).ch-t.to.ch),ge(n,r)}function Ci(e,t){for(var n=[],r=0;r<e.sel.ranges.length;r++){var i=e.sel.ranges[r];n.push(new yi(ki(i.anchor,t),ki(i.head,t)))}return bi(n,e.sel.primIndex)}function Si(e,t,n){return e.line==t.line?ge(n.line,e.ch-t.ch+n.ch):ge(n.line+(e.line-t.line),e.ch)}function Li(e){e.doc.mode=Tt(e.options,e.doc.modeOption),Ti(e)}function Ti(e){e.doc.iter(function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)}),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,ni(e,100),e.state.modeGen++,e.curOp&&Jr(e)}function Mi(e,t){return 0==t.from.ch&&0==t.to.ch&&""==X(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function Ni(e,t,n,r){function i(e){return n?n[e]:null}function o(e,n,i){!function(e,t,n,r){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),Ee(e),De(e,n);var i=r?r(e):1;i!=e.height&&ce(e,i)}(e,n,i,r),an(e,"change",e,t)}function l(e,t){for(var n=[],o=e;o<t;++o)n.push(new Vt(u[o],i(o),r));return n}var a=t.from,s=t.to,u=t.text,c=ae(e,a.line),f=ae(e,s.line),h=X(u),d=i(u.length-1),p=s.line-a.line;if(t.full)e.insert(0,l(0,u.length)),e.remove(u.length,e.size-u.length);else if(Mi(e,t)){var g=l(0,u.length-1);o(f,f.text,d),p&&e.remove(a.line,p),g.length&&e.insert(a.line,g)}else if(c==f)if(1==u.length)o(c,c.text.slice(0,a.ch)+h+c.text.slice(s.ch),d);else{var m=l(1,u.length-1);m.push(new Vt(h+c.text.slice(s.ch),d,r)),o(c,c.text.slice(0,a.ch)+u[0],i(0)),e.insert(a.line+1,m)}else if(1==u.length)o(c,c.text.slice(0,a.ch)+u[0]+f.text.slice(s.ch),i(0)),e.remove(a.line+1,p);else{o(c,c.text.slice(0,a.ch)+u[0],i(0)),o(f,h+f.text.slice(s.ch),d);var v=l(1,u.length-1);p>1&&e.remove(a.line+1,p-1),e.insert(a.line+1,v)}an(e,"change",e,t)}function Ai(e,t,n){!function e(r,i,o){if(r.linked)for(var l=0;l<r.linked.length;++l){var a=r.linked[l];if(a.doc!=i){var s=o&&a.sharedHist;n&&!s||(t(a.doc,s),e(a.doc,r,s))}}}(e,null,!0)}function Oi(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,or(e),Li(e),Ei(e),e.options.lineWrapping||Ye(e),e.options.mode=t.modeOption,Jr(e)}function Ei(e){("rtl"==e.doc.direction?W:T)(e.display.lineDiv,"CodeMirror-rtl")}function Di(e){this.done=[],this.undone=[],this.undoDepth=1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e||1}function Wi(e,t){var n={from:ye(t.from),to:wi(t),text:se(e,t.from,t.to)};return Ri(e,n,t.from.line,t.to.line+1),Ai(e,function(e){return Ri(e,n,t.from.line,t.to.line+1)},!0),n}function Hi(e){for(;e.length;){var t=X(e);if(!t.ranges)break;e.pop()}}function Fi(e,t,n,r){var i=e.history;i.undone.length=0;var o,l,a=+new Date;if((i.lastOp==r||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&i.lastModTime>a-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=function(e,t){return t?(Hi(e.done),X(e.done)):e.done.length&&!X(e.done).ranges?X(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),X(e.done)):void 0}(i,i.lastOp==r)))l=X(o.changes),0==me(t.from,t.to)&&0==me(t.from,l.to)?l.to=wi(t):o.changes.push(Wi(e,t));else{var s=X(i.done);for(s&&s.ranges||Pi(e.sel,i.done),o={changes:[Wi(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(n),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=a,i.lastOp=i.lastSelOp=r,i.lastOrigin=i.lastSelOrigin=t.origin,l||rt(e,"historyAdded")}function Ii(e,t,n,r){var i=e.history,o=r&&r.origin;n==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||function(e,t,n,r){var i=t.charAt(0);return"*"==i||"+"==i&&n.ranges.length==r.ranges.length&&n.somethingSelected()==r.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}(e,o,X(i.done),t))?i.done[i.done.length-1]=t:Pi(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=n,r&&!1!==r.clearRedo&&Hi(i.undone)}function Pi(e,t){var n=X(t);n&&n.ranges&&n.equals(e)||t.push(e)}function Ri(e,t,n,r){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,n),Math.min(e.first+e.size,r),function(n){n.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=n.markedSpans),++o})}function zi(e){if(!e)return null;for(var t,n=0;n<e.length;++n)e[n].marker.explicitlyCleared?t||(t=e.slice(0,n)):t&&t.push(e[n]);return t?t.length?t:null:e}function Bi(e,t){var n=function(e,t){var n=t["spans_"+e.id];if(!n)return null;for(var r=[],i=0;i<t.text.length;++i)r.push(zi(n[i]));return r}(e,t),r=Ae(e,t);if(!n)return r;if(!r)return n;for(var i=0;i<n.length;++i){var o=n[i],l=r[i];if(o&&l)e:for(var a=0;a<l.length;++a){for(var s=l[a],u=0;u<o.length;++u)if(o[u].marker==s.marker)continue e;o.push(s)}else l&&(n[i]=l)}return n}function $i(e,t,n){for(var r=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)r.push(n?vi.prototype.deepCopy.call(o):o);else{var l=o.changes,a=[];r.push({changes:a});for(var s=0;s<l.length;++s){var u=l[s],c=void 0;if(a.push({from:u.from,to:u.to,text:u.text}),t)for(var f in u)(c=f.match(/^spans_(\d+)$/))&&B(t,Number(c[1]))>-1&&(X(a)[f]=u[f],delete u[f])}}}return r}function _i(e,t,n,r){if(r){var i=e.anchor;if(n){var o=me(t,i)<0;o!=me(n,i)<0?(i=t,t=n):o!=me(t,n)<0&&(t=n)}return new yi(i,t)}return new yi(n||t,t)}function ji(e,t,n,r,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),Yi(e,new vi([_i(e.sel.primary(),t,n,i)],0),r)}function Gi(e,t,n){for(var r=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)r[o]=_i(e.sel.ranges[o],t[o],null,i);var l=bi(r,e.sel.primIndex);Yi(e,l,n)}function Vi(e,t,n,r){var i=e.sel.ranges.slice(0);i[t]=n,Yi(e,bi(i,e.sel.primIndex),r)}function Ui(e,t,n,r){Yi(e,xi(t,n),r)}function Ki(e,t,n){var r=e.history.done,i=X(r);i&&i.ranges?(r[r.length-1]=t,Xi(e,t,n)):Yi(e,t,n)}function Yi(e,t,n){Xi(e,t,n),Ii(e,e.sel,e.cm?e.cm.curOp.id:NaN,n)}function Xi(e,t,n){(lt(e,"beforeSelectionChange")||e.cm&&lt(e.cm,"beforeSelectionChange"))&&(t=function(e,t,n){var r={ranges:t.ranges,update:function(t){this.ranges=[];for(var n=0;n<t.length;n++)this.ranges[n]=new yi(ke(e,t[n].anchor),ke(e,t[n].head))},origin:n&&n.origin};return rt(e,"beforeSelectionChange",e,r),e.cm&&rt(e.cm,"beforeSelectionChange",e.cm,r),r.ranges!=t.ranges?bi(r.ranges,r.ranges.length-1):t}(e,t,n));var r=n&&n.bias||(me(t.primary().head,e.sel.primary().head)<0?-1:1);qi(e,Zi(e,t,r,!0)),n&&!1===n.scroll||!e.cm||Lr(e.cm)}function qi(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=e.cm.curOp.selectionChanged=!0,ot(e.cm)),an(e,"cursorActivity",e))}function Ji(e){qi(e,Zi(e,e.sel,null,!1))}function Zi(e,t,n,r){for(var i,o=0;o<t.ranges.length;o++){var l=t.ranges[o],a=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],s=eo(e,l.anchor,a&&a.anchor,n,r),u=eo(e,l.head,a&&a.head,n,r);(i||s!=l.anchor||u!=l.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new yi(s,u))}return i?bi(i,t.primIndex):t}function Qi(e,t,n,r,i){var o=ae(e,t.line);if(o.markedSpans)for(var l=0;l<o.markedSpans.length;++l){var a=o.markedSpans[l],s=a.marker;if((null==a.from||(s.inclusiveLeft?a.from<=t.ch:a.from<t.ch))&&(null==a.to||(s.inclusiveRight?a.to>=t.ch:a.to>t.ch))){if(i&&(rt(s,"beforeCursorEnter"),s.explicitlyCleared)){if(o.markedSpans){--l;continue}break}if(!s.atomic)continue;if(n){var u=s.find(r<0?1:-1),c=void 0;if((r<0?s.inclusiveRight:s.inclusiveLeft)&&(u=to(e,u,-r,u&&u.line==t.line?o:null)),u&&u.line==t.line&&(c=me(u,n))&&(r<0?c<0:c>0))return Qi(e,u,t,r,i)}var f=s.find(r<0?-1:1);return(r<0?s.inclusiveLeft:s.inclusiveRight)&&(f=to(e,f,r,f.line==t.line?o:null)),f?Qi(e,f,t,r,i):null}}return t}function eo(e,t,n,r,i){var o=r||1,l=Qi(e,t,n,o,i)||!i&&Qi(e,t,n,o,!0)||Qi(e,t,n,-o,i)||!i&&Qi(e,t,n,-o,!0);return l||(e.cantEdit=!0,ge(e.first,0))}function to(e,t,n,r){return n<0&&0==t.ch?t.line>e.first?ke(e,ge(t.line-1)):null:n>0&&t.ch==(r||ae(e,t.line)).text.length?t.line<e.first+e.size-1?ge(t.line+1,0):null:new ge(t.line,t.ch+n)}function no(e){e.setSelection(ge(e.firstLine(),0),ge(e.lastLine()),j)}function ro(e,t,n){var r={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return r.canceled=!0}};return n&&(r.update=function(t,n,i,o){t&&(r.from=ke(e,t)),n&&(r.to=ke(e,n)),i&&(r.text=i),void 0!==o&&(r.origin=o)}),rt(e,"beforeChange",e,r),e.cm&&rt(e.cm,"beforeChange",e.cm,r),r.canceled?null:{from:r.from,to:r.to,text:r.text,origin:r.origin}}function io(e,t,n){if(e.cm){if(!e.cm.curOp)return Yr(e.cm,io)(e,t,n);if(e.cm.state.suppressEdits)return}if(!(lt(e,"beforeChange")||e.cm&&lt(e.cm,"beforeChange"))||(t=ro(e,t,!0))){var r=Se&&!n&&function(e,t,n){var r=null;if(e.iter(t.line,n.line+1,function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var n=e.markedSpans[t].marker;!n.readOnly||r&&-1!=B(r,n)||(r||(r=[])).push(n)}}),!r)return null;for(var i=[{from:t,to:n}],o=0;o<r.length;++o)for(var l=r[o],a=l.find(0),s=0;s<i.length;++s){var u=i[s];if(!(me(u.to,a.from)<0||me(u.from,a.to)>0)){var c=[s,1],f=me(u.from,a.from),h=me(u.to,a.to);(f<0||!l.inclusiveLeft&&!f)&&c.push({from:u.from,to:a.from}),(h>0||!l.inclusiveRight&&!h)&&c.push({from:a.to,to:u.to}),i.splice.apply(i,c),s+=c.length-3}}return i}(e,t.from,t.to);if(r)for(var i=r.length-1;i>=0;--i)oo(e,{from:r[i].from,to:r[i].to,text:i?[""]:t.text,origin:t.origin});else oo(e,t)}}function oo(e,t){if(1!=t.text.length||""!=t.text[0]||0!=me(t.from,t.to)){var n=Ci(e,t);Fi(e,t,n,e.cm?e.cm.curOp.id:NaN),so(e,t,n,Ae(e,t));var r=[];Ai(e,function(e,n){n||-1!=B(r,e.history)||(ho(e.history,t),r.push(e.history)),so(e,t,null,Ae(e,t))})}}function lo(e,t,n){var r=e.cm&&e.cm.state.suppressEdits;if(!r||n){for(var i,o=e.history,l=e.sel,a="undo"==t?o.done:o.undone,s="undo"==t?o.undone:o.done,u=0;u<a.length&&(i=a[u],n?!i.ranges||i.equals(e.sel):i.ranges);u++);if(u!=a.length){for(o.lastOrigin=o.lastSelOrigin=null;;){if(!(i=a.pop()).ranges){if(r)return void a.push(i);break}if(Pi(i,s),n&&!i.equals(e.sel))return void Yi(e,i,{clearRedo:!1});l=i}var c=[];Pi(l,s),s.push({changes:c,generation:o.generation}),o.generation=i.generation||++o.maxGeneration;for(var f=lt(e,"beforeChange")||e.cm&&lt(e.cm,"beforeChange"),h=function(n){var r=i.changes[n];if(r.origin=t,f&&!ro(e,r,!1))return a.length=0,{};c.push(Wi(e,r));var o=n?Ci(e,r):X(a);so(e,r,o,Bi(e,r)),!n&&e.cm&&e.cm.scrollIntoView({from:r.from,to:wi(r)});var l=[];Ai(e,function(e,t){t||-1!=B(l,e.history)||(ho(e.history,r),l.push(e.history)),so(e,r,null,Bi(e,r))})},d=i.changes.length-1;d>=0;--d){var p=h(d);if(p)return p.v}}}}function ao(e,t){if(0!=t&&(e.first+=t,e.sel=new vi(q(e.sel.ranges,function(e){return new yi(ge(e.anchor.line+t,e.anchor.ch),ge(e.head.line+t,e.head.ch))}),e.sel.primIndex),e.cm)){Jr(e.cm,e.first,e.first-t,t);for(var n=e.cm.display,r=n.viewFrom;r<n.viewTo;r++)Zr(e.cm,r,"gutter")}}function so(e,t,n,r){if(e.cm&&!e.cm.curOp)return Yr(e.cm,so)(e,t,n,r);if(t.to.line<e.first)ao(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);ao(e,i),t={from:ge(e.first,0),to:ge(t.to.line+i,t.to.ch),text:[X(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:ge(o,ae(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=se(e,t.from,t.to),n||(n=Ci(e,t)),e.cm?function(e,t,n){var r=e.doc,i=e.display,o=t.from,l=t.to,a=!1,s=o.line;e.options.lineWrapping||(s=fe($e(ae(r,o.line))),r.iter(s,l.line+1,function(e){if(e==i.maxLine)return a=!0,!0})),r.sel.contains(t.from,t.to)>-1&&ot(e),Ni(r,t,n,ir(e)),e.options.lineWrapping||(r.iter(s,o.line+t.text.length,function(e){var t=Ke(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,a=!1)}),a&&(e.curOp.updateMaxLine=!0)),function(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var n=e.first,r=t-1;r>n;r--){var i=ae(e,r).stateAfter;if(i&&(!(i instanceof Wt)||r+i.lookAhead<t)){n=r+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,n)}}(r,o.line),ni(e,400);var u=t.text.length-(l.line-o.line)-1;t.full?Jr(e):o.line!=l.line||1!=t.text.length||Mi(e.doc,t)?Jr(e,o.line,l.line+1,u):Zr(e,o.line,"text");var c=lt(e,"changes"),f=lt(e,"change");if(f||c){var h={from:o,to:l,text:t.text,removed:t.removed,origin:t.origin};f&&an(e,"change",e,h),c&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(h)}e.display.selForContextMenu=null}(e.cm,t,r):Ni(e,t,r),Xi(e,n,j)}}function uo(e,t,n,r,i){var o;r||(r=n),me(r,n)<0&&(n=(o=[r,n])[0],r=o[1]),"string"==typeof t&&(t=e.splitLines(t)),io(e,{from:n,to:r,text:t,origin:i})}function co(e,t,n,r){n<e.line?e.line+=r:t<e.line&&(e.line=t,e.ch=0)}function fo(e,t,n,r){for(var i=0;i<e.length;++i){var o=e[i],l=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var a=0;a<o.ranges.length;a++)co(o.ranges[a].anchor,t,n,r),co(o.ranges[a].head,t,n,r)}else{for(var s=0;s<o.changes.length;++s){var u=o.changes[s];if(n<u.from.line)u.from=ge(u.from.line+r,u.from.ch),u.to=ge(u.to.line+r,u.to.ch);else if(t<=u.to.line){l=!1;break}}l||(e.splice(0,i+1),i=0)}}}function ho(e,t){var n=t.from.line,r=t.to.line,i=t.text.length-(r-n)-1;fo(e.done,n,r,i),fo(e.undone,n,r,i)}function po(e,t,n,r){var i=t,o=t;return"number"==typeof t?o=ae(e,we(e,t)):i=fe(t),null==i?null:(r(o,i)&&e.cm&&Zr(e.cm,i,n),o)}function go(e){this.lines=e,this.parent=null;for(var t=0,n=0;n<e.length;++n)e[n].parent=this,t+=e[n].height;this.height=t}function mo(e){this.children=e;for(var t=0,n=0,r=0;r<e.length;++r){var i=e[r];t+=i.chunkSize(),n+=i.height,i.parent=this}this.size=t,this.height=n,this.parent=null}yi.prototype.from=function(){return xe(this.anchor,this.head)},yi.prototype.to=function(){return be(this.anchor,this.head)},yi.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},go.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var n=e,r=e+t;n<r;++n){var i=this.lines[n];this.height-=i.height,Ut(i),an(i,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,n){this.height+=n,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var r=0;r<t.length;++r)t[r].parent=this},iterN:function(e,t,n){for(var r=e+t;e<r;++e)if(n(this.lines[e]))return!0}},mo.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var n=0;n<this.children.length;++n){var r=this.children[n],i=r.chunkSize();if(e<i){var o=Math.min(t,i-e),l=r.height;if(r.removeInner(e,o),this.height-=l-r.height,i==o&&(this.children.splice(n--,1),r.parent=null),0==(t-=o))break;e=0}else e-=i}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof go))){var a=[];this.collapse(a),this.children=[new go(a)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,n){this.size+=t.length,this.height+=n;for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,n),i.lines&&i.lines.length>50){for(var l=i.lines.length%25+25,a=l;a<i.lines.length;){var s=new go(i.lines.slice(a,a+=25));i.height-=s.height,this.children.splice(++r,0,s),s.parent=this}i.lines=i.lines.slice(0,l),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=e.children.splice(e.children.length-5,5),n=new mo(t);if(e.parent){e.size-=n.size,e.height-=n.height;var r=B(e.parent.children,e);e.parent.children.splice(r+1,0,n)}else{var i=new mo(e.children);i.parent=e,e.children=[i,n],e=i}n.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,n){for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<o){var l=Math.min(t,o-e);if(i.iterN(e,l,n))return!0;if(0==(t-=l))break;e=0}else e-=o}}};var vo=function(e,t,n){if(n)for(var r in n)n.hasOwnProperty(r)&&(this[r]=n[r]);this.doc=e,this.node=t};function yo(e,t,n){Ue(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&Sr(e,n)}vo.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,n=this.line,r=fe(n);if(null!=r&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(n.widgets=null);var o=xn(this);ce(n,Math.max(0,n.height-o)),e&&(Kr(e,function(){yo(e,n,-o),Zr(e,r,"widget")}),an(e,"lineWidgetCleared",e,this,r))}},vo.prototype.changed=function(){var e=this,t=this.height,n=this.doc.cm,r=this.line;this.height=null;var i=xn(this)-t;i&&(Ge(this.doc,r)||ce(r,r.height+i),n&&Kr(n,function(){n.curOp.forceUpdate=!0,yo(n,r,i),an(n,"lineWidgetChanged",n,e,fe(r))}))},at(vo);var bo=0,xo=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++bo};function wo(e,t,n,r,i){if(r&&r.shared)return function(e,t,n,r,i){(r=P(r)).shared=!1;var o=[wo(e,t,n,r,i)],l=o[0],a=r.widgetNode;return Ai(e,function(e){a&&(r.widgetNode=a.cloneNode(!0)),o.push(wo(e,ke(e,t),ke(e,n),r,i));for(var s=0;s<e.linked.length;++s)if(e.linked[s].isParent)return;l=X(o)}),new ko(o,l)}(e,t,n,r,i);if(e.cm&&!e.cm.curOp)return Yr(e.cm,wo)(e,t,n,r,i);var o=new xo(e,i),l=me(t,n);if(r&&P(r,o,!1),l>0||0==l&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=O("span",[o.replacedWith],"CodeMirror-widget"),r.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),r.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(Be(e,t.line,t,n,o)||t.line!=n.line&&Be(e,n.line,t,n,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");Le=!0}o.addToHistory&&Fi(e,{from:t,to:n,origin:"markText"},e.sel,NaN);var a,s=t.line,u=e.cm;if(e.iter(s,n.line+1,function(e){u&&o.collapsed&&!u.options.lineWrapping&&$e(e)==u.display.maxLine&&(a=!0),o.collapsed&&s!=t.line&&ce(e,0),function(e,t){e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],t.marker.attachLine(e)}(e,new Te(o,s==t.line?t.ch:null,s==n.line?n.ch:null)),++s}),o.collapsed&&e.iter(t.line,n.line+1,function(t){Ge(e,t)&&ce(t,0)}),o.clearOnEnter&&et(o,"beforeCursorEnter",function(){return o.clear()}),o.readOnly&&(Se=!0,(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++bo,o.atomic=!0),u){if(a&&(u.curOp.updateMaxLine=!0),o.collapsed)Jr(u,t.line,n.line+1);else if(o.className||o.title||o.startStyle||o.endStyle||o.css)for(var c=t.line;c<=n.line;c++)Zr(u,c,"text");o.atomic&&Ji(u.doc),an(u,"markerAdded",u,o)}return o}xo.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&Br(e),lt(this,"clear")){var n=this.find();n&&an(this,"clear",n.from,n.to)}for(var r=null,i=null,o=0;o<this.lines.length;++o){var l=this.lines[o],a=Me(l.markedSpans,this);e&&!this.collapsed?Zr(e,fe(l),"text"):e&&(null!=a.to&&(i=fe(l)),null!=a.from&&(r=fe(l))),l.markedSpans=Ne(l.markedSpans,a),null==a.from&&this.collapsed&&!Ge(this.doc,l)&&e&&ce(l,er(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var s=0;s<this.lines.length;++s){var u=$e(this.lines[s]),c=Ke(u);c>e.display.maxLineLength&&(e.display.maxLine=u,e.display.maxLineLength=c,e.display.maxLineChanged=!0)}null!=r&&e&&this.collapsed&&Jr(e,r,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&Ji(e.doc)),e&&an(e,"markerCleared",e,this,r,i),t&&$r(e),this.parent&&this.parent.clear()}},xo.prototype.find=function(e,t){var n,r;null==e&&"bookmark"==this.type&&(e=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],l=Me(o.markedSpans,this);if(null!=l.from&&(n=ge(t?o:fe(o),l.from),-1==e))return n;if(null!=l.to&&(r=ge(t?o:fe(o),l.to),1==e))return r}return n&&{from:n,to:r}},xo.prototype.changed=function(){var e=this,t=this.find(-1,!0),n=this,r=this.doc.cm;t&&r&&Kr(r,function(){var i=t.line,o=fe(t.line),l=On(r,o);if(l&&(Pn(l),r.curOp.selectionChanged=r.curOp.forceUpdate=!0),r.curOp.updateMaxLine=!0,!Ge(n.doc,i)&&null!=n.height){var a=n.height;n.height=null;var s=xn(n)-a;s&&ce(i,i.height+s)}an(r,"markerChanged",r,e)})},xo.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=B(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},xo.prototype.detachLine=function(e){if(this.lines.splice(B(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},at(xo);var ko=function(e,t){this.markers=e,this.primary=t;for(var n=0;n<e.length;++n)e[n].parent=this};function Co(e){return e.findMarks(ge(e.first,0),e.clipPos(ge(e.lastLine())),function(e){return e.parent})}function So(e){for(var t=function(t){var n=e[t],r=[n.primary.doc];Ai(n.primary.doc,function(e){return r.push(e)});for(var i=0;i<n.markers.length;i++){var o=n.markers[i];-1==B(r,o.doc)&&(o.parent=null,n.markers.splice(i--,1))}},n=0;n<e.length;n++)t(n)}ko.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();an(this,"clear")}},ko.prototype.find=function(e,t){return this.primary.find(e,t)},at(ko);var Lo=0,To=function(e,t,n,r,i){if(!(this instanceof To))return new To(e,t,n,r,i);null==n&&(n=0),mo.call(this,[new go([new Vt("",null)])]),this.first=n,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=n;var o=ge(n,0);this.sel=xi(o),this.history=new Di(null),this.id=++Lo,this.modeOption=t,this.lineSep=r,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),Ni(this,{from:o,to:o,text:e}),Yi(this,xi(o),j)};To.prototype=Z(mo.prototype,{constructor:To,iter:function(e,t,n){n?this.iterN(e-this.first,t-e,n):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var n=0,r=0;r<t.length;++r)n+=t[r].height;this.insertInner(e-this.first,t,n)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=ue(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:qr(function(e){var t=ge(this.first,0),n=this.first+this.size-1;io(this,{from:t,to:ge(n,ae(this,n).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&Tr(this.cm,0,0),Yi(this,xi(t),j)}),replaceRange:function(e,t,n,r){t=ke(this,t),n=n?ke(this,n):t,uo(this,e,t,n,r)},getRange:function(e,t,n){var r=se(this,ke(this,e),ke(this,t));return!1===n?r:r.join(n||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(de(this,e))return ae(this,e)},getLineNumber:function(e){return fe(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=ae(this,e)),$e(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return ke(this,e)},getCursor:function(e){var t=this.sel.primary();return null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:qr(function(e,t,n){Ui(this,ke(this,"number"==typeof e?ge(e,t||0):e),null,n)}),setSelection:qr(function(e,t,n){Ui(this,ke(this,e),ke(this,t||e),n)}),extendSelection:qr(function(e,t,n){ji(this,ke(this,e),t&&ke(this,t),n)}),extendSelections:qr(function(e,t){Gi(this,Ce(this,e),t)}),extendSelectionsBy:qr(function(e,t){var n=q(this.sel.ranges,e);Gi(this,Ce(this,n),t)}),setSelections:qr(function(e,t,n){if(e.length){for(var r=[],i=0;i<e.length;i++)r[i]=new yi(ke(this,e[i].anchor),ke(this,e[i].head));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),Yi(this,bi(r,t),n)}}),addSelection:qr(function(e,t,n){var r=this.sel.ranges.slice(0);r.push(new yi(ke(this,e),ke(this,t||e))),Yi(this,bi(r,r.length-1),n)}),getSelection:function(e){for(var t,n=this.sel.ranges,r=0;r<n.length;r++){var i=se(this,n[r].from(),n[r].to());t=t?t.concat(i):i}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],n=this.sel.ranges,r=0;r<n.length;r++){var i=se(this,n[r].from(),n[r].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[r]=i}return t},replaceSelection:function(e,t,n){for(var r=[],i=0;i<this.sel.ranges.length;i++)r[i]=e;this.replaceSelections(r,t,n||"+input")},replaceSelections:qr(function(e,t,n){for(var r=[],i=this.sel,o=0;o<i.ranges.length;o++){var l=i.ranges[o];r[o]={from:l.from(),to:l.to(),text:this.splitLines(e[o]),origin:n}}for(var a=t&&"end"!=t&&function(e,t,n){for(var r=[],i=ge(e.first,0),o=i,l=0;l<t.length;l++){var a=t[l],s=Si(a.from,i,o),u=Si(wi(a),i,o);if(i=a.to,o=u,"around"==n){var c=e.sel.ranges[l],f=me(c.head,c.anchor)<0;r[l]=new yi(f?u:s,f?s:u)}else r[l]=new yi(s,s)}return new vi(r,e.sel.primIndex)}(this,r,t),s=r.length-1;s>=0;s--)io(this,r[s]);a?Ki(this,a):this.cm&&Lr(this.cm)}),undo:qr(function(){lo(this,"undo")}),redo:qr(function(){lo(this,"redo")}),undoSelection:qr(function(){lo(this,"undo",!0)}),redoSelection:qr(function(){lo(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,n=0,r=0;r<e.done.length;r++)e.done[r].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++n;return{undo:t,redo:n}},clearHistory:function(){this.history=new Di(this.history.maxGeneration)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:$i(this.history.done),undone:$i(this.history.undone)}},setHistory:function(e){var t=this.history=new Di(this.history.maxGeneration);t.done=$i(e.done.slice(0),null,!0),t.undone=$i(e.undone.slice(0),null,!0)},setGutterMarker:qr(function(e,t,n){return po(this,e,"gutter",function(e){var r=e.gutterMarkers||(e.gutterMarkers={});return r[t]=n,!n&&ne(r)&&(e.gutterMarkers=null),!0})}),clearGutter:qr(function(e){var t=this;this.iter(function(n){n.gutterMarkers&&n.gutterMarkers[e]&&po(t,n,"gutter",function(){return n.gutterMarkers[e]=null,ne(n.gutterMarkers)&&(n.gutterMarkers=null),!0})})}),lineInfo:function(e){var t;if("number"==typeof e){if(!de(this,e))return null;if(t=e,!(e=ae(this,e)))return null}else if(null==(t=fe(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:qr(function(e,t,n){return po(this,e,"gutter"==t?"gutter":"class",function(e){var r="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass";if(e[r]){if(S(n).test(e[r]))return!1;e[r]+=" "+n}else e[r]=n;return!0})}),removeLineClass:qr(function(e,t,n){return po(this,e,"gutter"==t?"gutter":"class",function(e){var r="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass",i=e[r];if(!i)return!1;if(null==n)e[r]=null;else{var o=i.match(S(n));if(!o)return!1;var l=o.index+o[0].length;e[r]=i.slice(0,o.index)+(o.index&&l!=i.length?" ":"")+i.slice(l)||null}return!0})}),addLineWidget:qr(function(e,t,n){return function(e,t,n,r){var i=new vo(e,n,r),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),po(e,t,"widget",function(t){var n=t.widgets||(t.widgets=[]);if(null==i.insertAt?n.push(i):n.splice(Math.min(n.length-1,Math.max(0,i.insertAt)),0,i),i.line=t,o&&!Ge(e,t)){var r=Ue(t)<e.scrollTop;ce(t,t.height+xn(i)),r&&Sr(o,i.height),o.curOp.forceUpdate=!0}return!0}),o&&an(o,"lineWidgetAdded",o,i,"number"==typeof t?t:fe(t)),i}(this,e,t,n)}),removeLineWidget:function(e){e.clear()},markText:function(e,t,n){return wo(this,ke(this,e),ke(this,t),n,n&&n.type||"range")},setBookmark:function(e,t){var n={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return wo(this,e=ke(this,e),e,n,"bookmark")},findMarksAt:function(e){var t=[],n=ae(this,(e=ke(this,e)).line).markedSpans;if(n)for(var r=0;r<n.length;++r){var i=n[r];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,n){e=ke(this,e),t=ke(this,t);var r=[],i=e.line;return this.iter(e.line,t.line+1,function(o){var l=o.markedSpans;if(l)for(var a=0;a<l.length;a++){var s=l[a];null!=s.to&&i==e.line&&e.ch>=s.to||null==s.from&&i!=e.line||null!=s.from&&i==t.line&&s.from>=t.ch||n&&!n(s.marker)||r.push(s.marker.parent||s.marker)}++i}),r},getAllMarks:function(){var e=[];return this.iter(function(t){var n=t.markedSpans;if(n)for(var r=0;r<n.length;++r)null!=n[r].from&&e.push(n[r].marker)}),e},posFromIndex:function(e){var t,n=this.first,r=this.lineSeparator().length;return this.iter(function(i){var o=i.text.length+r;if(o>e)return t=e,!0;e-=o,++n}),ke(this,ge(n,t))},indexFromPos:function(e){var t=(e=ke(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var n=this.lineSeparator().length;return this.iter(this.first,e.line,function(e){t+=e.text.length+n}),t},copy:function(e){var t=new To(ue(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,n=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<n&&(n=e.to);var r=new To(ue(this,t,n),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(r.history=this.history),(this.linked||(this.linked=[])).push({doc:r,sharedHist:e.sharedHist}),r.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n],i=r.find(),o=e.clipPos(i.from),l=e.clipPos(i.to);if(me(o,l)){var a=wo(e,o,l,r.primary,r.primary.type);r.markers.push(a),a.parent=r}}}(r,Co(this)),r},unlinkDoc:function(e){if(e instanceof Cl&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t){var n=this.linked[t];if(n.doc==e){this.linked.splice(t,1),e.unlinkDoc(this),So(Co(this));break}}if(e.history==this.history){var r=[e.id];Ai(e,function(e){return r.push(e.id)},!0),e.history=new Di(null),e.history.done=$i(this.history.done,r),e.history.undone=$i(this.history.undone,r)}},iterLinkedDocs:function(e){Ai(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):bt(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:qr(function(e){"rtl"!=e&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter(function(e){return e.order=null}),this.cm&&function(e){Kr(e,function(){Ei(e),Jr(e)})}(this.cm))})}),To.prototype.eachLine=To.prototype.iter;var Mo=0;function No(e){var t=this;if(Ao(t),!it(t,e)&&!wn(t.display,e)){st(e),l&&(Mo=+new Date);var n=lr(t,e,!0),r=e.dataTransfer.files;if(n&&!t.isReadOnly())if(r&&r.length&&window.FileReader&&window.File)for(var i=r.length,o=Array(i),a=0,s=function(e,r){if(!t.options.allowDropFileTypes||-1!=B(t.options.allowDropFileTypes,e.type)){var l=new FileReader;l.onload=Yr(t,function(){var e=l.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(e)&&(e=""),o[r]=e,++a==i){var s={from:n=ke(t.doc,n),to:n,text:t.doc.splitLines(o.join(t.doc.lineSeparator())),origin:"paste"};io(t.doc,s),Ki(t.doc,xi(n,wi(s)))}}),l.readAsText(e)}},u=0;u<i;++u)s(r[u],u);else{if(t.state.draggingText&&t.doc.sel.contains(n)>-1)return t.state.draggingText(e),void setTimeout(function(){return t.display.input.focus()},20);try{var c=e.dataTransfer.getData("Text");if(c){var f;if(t.state.draggingText&&!t.state.draggingText.copy&&(f=t.listSelections()),Xi(t.doc,xi(n,n)),f)for(var h=0;h<f.length;++h)uo(t.doc,"",f[h].anchor,f[h].head,"drag");t.replaceSelection(c,"around","paste"),t.display.input.focus()}}catch(e){}}}}function Ao(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Oo(e){if(document.getElementsByClassName)for(var t=document.getElementsByClassName("CodeMirror"),n=0;n<t.length;n++){var r=t[n].CodeMirror;r&&e(r)}}var Eo=!1;function Do(){Eo||(function(){var e;et(window,"resize",function(){null==e&&(e=setTimeout(function(){e=null,Oo(Wo)},100))}),et(window,"blur",function(){return Oo(vr)})}(),Eo=!0)}function Wo(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var Ho={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",127:"Delete",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Fo=0;Fo<10;Fo++)Ho[Fo+48]=Ho[Fo+96]=String(Fo);for(var Io=65;Io<=90;Io++)Ho[Io]=String.fromCharCode(Io);for(var Po=1;Po<=12;Po++)Ho[Po+111]=Ho[Po+63235]="F"+Po;var Ro={};function zo(e){var t,n,r,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var l=0;l<o.length-1;l++){var a=o[l];if(/^(cmd|meta|m)$/i.test(a))i=!0;else if(/^a(lt)?$/i.test(a))t=!0;else if(/^(c|ctrl|control)$/i.test(a))n=!0;else{if(!/^s(hift)?$/i.test(a))throw new Error("Unrecognized modifier name: "+a);r=!0}}return t&&(e="Alt-"+e),n&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),r&&(e="Shift-"+e),e}function Bo(e){var t={};for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];if(/^(name|fallthrough|(de|at)tach)$/.test(n))continue;if("..."==r){delete e[n];continue}for(var i=q(n.split(" "),zo),o=0;o<i.length;o++){var l=void 0,a=void 0;o==i.length-1?(a=i.join(" "),l=r):(a=i.slice(0,o+1).join(" "),l="...");var s=t[a];if(s){if(s!=l)throw new Error("Inconsistent bindings for "+a)}else t[a]=l}delete e[n]}for(var u in t)e[u]=t[u];return e}function $o(e,t,n,r){var i=(t=Vo(t)).call?t.call(e,r):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&n(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return $o(e,t.fallthrough,n,r);for(var o=0;o<t.fallthrough.length;o++){var l=$o(e,t.fallthrough[o],n,r);if(l)return l}}}function _o(e){var t="string"==typeof e?e:Ho[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function jo(e,t,n){var r=e;return t.altKey&&"Alt"!=r&&(e="Alt-"+e),(k?t.metaKey:t.ctrlKey)&&"Ctrl"!=r&&(e="Ctrl-"+e),(k?t.ctrlKey:t.metaKey)&&"Cmd"!=r&&(e="Cmd-"+e),!n&&t.shiftKey&&"Shift"!=r&&(e="Shift-"+e),e}function Go(e,t){if(f&&34==e.keyCode&&e.char)return!1;var n=Ho[e.keyCode];return null!=n&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(n=e.code),jo(n,e,t))}function Vo(e){return"string"==typeof e?Ro[e]:e}function Uo(e,t){for(var n=e.doc.sel.ranges,r=[],i=0;i<n.length;i++){for(var o=t(n[i]);r.length&&me(o.from,X(r).to)<=0;){var l=r.pop();if(me(l.from,o.from)<0){o.from=l.from;break}}r.push(o)}Kr(e,function(){for(var t=r.length-1;t>=0;t--)uo(e.doc,"",r[t].from,r[t].to,"+delete");Lr(e)})}function Ko(e,t,n){var r=oe(e.text,t+n,n);return r<0||r>e.text.length?null:r}function Yo(e,t,n){var r=Ko(e,t.ch,n);return null==r?null:new ge(t.line,r,n<0?"after":"before")}function Xo(e,t,n,r,i){if(e){var o=Ze(n,t.doc.direction);if(o){var l,a=i<0?X(o):o[0],s=i<0==(1==a.level),u=s?"after":"before";if(a.level>0||"rtl"==t.doc.direction){var c=En(t,n);l=i<0?n.text.length-1:0;var f=Dn(t,c,l).top;l=le(function(e){return Dn(t,c,e).top==f},i<0==(1==a.level)?a.from:a.to-1,l),"before"==u&&(l=Ko(n,l,1))}else l=i<0?a.to:a.from;return new ge(r,l,u)}}return new ge(r,i<0?n.text.length:0,i<0?"before":"after")}Ro.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Ro.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Ro.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-D":"delWordAfter","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Ro.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Ro.default=y?Ro.macDefault:Ro.pcDefault;var qo={selectAll:no,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),j)},killLine:function(e){return Uo(e,function(t){if(t.empty()){var n=ae(e.doc,t.head.line).text.length;return t.head.ch==n&&t.head.line<e.lastLine()?{from:t.head,to:ge(t.head.line+1,0)}:{from:t.head,to:ge(t.head.line,n)}}return{from:t.from(),to:t.to()}})},deleteLine:function(e){return Uo(e,function(t){return{from:ge(t.from().line,0),to:ke(e.doc,ge(t.to().line+1,0))}})},delLineLeft:function(e){return Uo(e,function(e){return{from:ge(e.from().line,0),to:e.from()}})},delWrappedLineLeft:function(e){return Uo(e,function(t){var n=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:n},"div");return{from:r,to:t.from()}})},delWrappedLineRight:function(e){return Uo(e,function(t){var n=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div");return{from:t.from(),to:r}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(ge(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(ge(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy(function(t){return Jo(e,t.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy(function(t){return Zo(e,t.head)},{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy(function(t){return function(e,t){var n=ae(e.doc,t),r=function(e){for(var t;t=Re(e);)e=t.find(1,!0).line;return e}(n);return r!=n&&(t=fe(r)),Xo(!0,e,n,t,-1)}(e,t.head.line)},{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy(function(t){var n=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div")},V)},goLineLeft:function(e){return e.extendSelectionsBy(function(t){var n=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:n},"div")},V)},goLineLeftSmart:function(e){return e.extendSelectionsBy(function(t){var n=e.cursorCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:n},"div");return r.ch<e.getLine(r.line).search(/\S/)?Zo(e,t.head):r},V)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"char")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],n=e.listSelections(),r=e.options.tabSize,i=0;i<n.length;i++){var o=n[i].from(),l=R(e.getLine(o.line),o.ch,r);t.push(Y(r-l%r))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return Kr(e,function(){for(var t=e.listSelections(),n=[],r=0;r<t.length;r++)if(t[r].empty()){var i=t[r].head,o=ae(e.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new ge(i.line,i.ch-1)),i.ch>0)i=new ge(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),ge(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var l=ae(e.doc,i.line-1).text;l&&(i=new ge(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+l.charAt(l.length-1),ge(i.line-1,l.length-1),i,"+transpose"))}n.push(new yi(i,i))}e.setSelections(n)})},newlineAndIndent:function(e){return Kr(e,function(){for(var t=e.listSelections(),n=t.length-1;n>=0;n--)e.replaceRange(e.doc.lineSeparator(),t[n].anchor,t[n].head,"+input");t=e.listSelections();for(var r=0;r<t.length;r++)e.indentLine(t[r].from().line,null,!0);Lr(e)})},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function Jo(e,t){var n=ae(e.doc,t),r=$e(n);return r!=n&&(t=fe(r)),Xo(!0,e,r,t,1)}function Zo(e,t){var n=Jo(e,t.line),r=ae(e.doc,n.line),i=Ze(r,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(0,r.text.search(/\S/)),l=t.line==n.line&&t.ch<=o&&t.ch;return ge(n.line,l?0:o,n.sticky)}return n}function Qo(e,t,n){if("string"==typeof t&&!(t=qo[t]))return!1;e.display.input.ensurePolled();var r=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n&&(e.display.shift=!1),i=t(e)!=_}finally{e.display.shift=r,e.state.suppressEdits=!1}return i}var el=new z;function tl(e,t,n,r){var i=e.state.keySeq;if(i){if(_o(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:el.set(50,function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())}),nl(e,i+" "+t,n,r))return!0}return nl(e,t,n,r)}function nl(e,t,n,r){var i=function(e,t,n){for(var r=0;r<e.state.keyMaps.length;r++){var i=$o(t,e.state.keyMaps[r],n,e);if(i)return i}return e.options.extraKeys&&$o(t,e.options.extraKeys,n,e)||$o(t,e.options.keyMap,n,e)}(e,t,r);return"multi"==i&&(e.state.keySeq=t),"handled"==i&&an(e,"keyHandled",e,t,n),"handled"!=i&&"multi"!=i||(st(n),dr(e)),!!i}function rl(e,t){var n=Go(t,!0);return!!n&&(t.shiftKey&&!e.state.keySeq?tl(e,"Shift-"+n,t,function(t){return Qo(e,t,!0)})||tl(e,n,t,function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return Qo(e,t)}):tl(e,n,t,function(t){return Qo(e,t)}))}var il=null;function ol(e){var t=this;if(t.curOp.focus=D(),!it(t,e)){l&&a<11&&27==e.keyCode&&(e.returnValue=!1);var n=e.keyCode;t.display.shift=16==n||e.shiftKey;var r=rl(t,e);f&&(il=r?n:null,!r&&88==n&&!wt&&(y?e.metaKey:e.ctrlKey)&&t.replaceSelection("",null,"cut")),18!=n||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||function(e){var t=e.display.lineDiv;function n(e){18!=e.keyCode&&e.altKey||(T(t,"CodeMirror-crosshair"),nt(document,"keyup",n),nt(document,"mouseover",n))}W(t,"CodeMirror-crosshair"),et(document,"keyup",n),et(document,"mouseover",n)}(t)}}function ll(e){16==e.keyCode&&(this.doc.sel.shift=!1),it(this,e)}function al(e){var t=this;if(!(wn(t.display,e)||it(t,e)||e.ctrlKey&&!e.altKey||y&&e.metaKey)){var n=e.keyCode,r=e.charCode;if(f&&n==il)return il=null,void st(e);if(!f||e.which&&!(e.which<10)||!rl(t,e)){var i=String.fromCharCode(null==r?n:r);"\b"!=i&&(function(e,t,n){return tl(e,"'"+n+"'",t,function(t){return Qo(e,t,!0)})}(t,e,i)||t.display.input.onKeyPress(e))}}}var sl,ul,cl=function(e,t,n){this.time=e,this.pos=t,this.button=n};function fl(e){var t=this,n=t.display;if(!(it(t,e)||n.activeTouch&&n.input.supportsTouch()))if(n.input.ensurePolled(),n.shift=e.shiftKey,wn(n,e))s||(n.scroller.draggable=!1,setTimeout(function(){return n.scroller.draggable=!0},100));else if(!pl(t,e)){var r=lr(t,e),i=dt(e),o=r?function(e,t){var n=+new Date;return ul&&ul.compare(n,e,t)?(sl=ul=null,"triple"):sl&&sl.compare(n,e,t)?(ul=new cl(n,e,t),sl=null,"double"):(sl=new cl(n,e,t),ul=null,"single")}(r,i):"single";window.focus(),1==i&&t.state.selectingText&&t.state.selectingText(e),r&&function(e,t,n,r,i){var o="Click";return"double"==r?o="Double"+o:"triple"==r&&(o="Triple"+o),tl(e,jo(o=(1==t?"Left":2==t?"Middle":"Right")+o,i),i,function(t){if("string"==typeof t&&(t=qo[t]),!t)return!1;var r=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r=t(e,n)!=_}finally{e.state.suppressEdits=!1}return r})}(t,i,r,o,e)||(1==i?r?function(e,t,n,r){l?setTimeout(I(pr,e),0):e.curOp.focus=D();var i,o=function(e,t,n){var r=e.getOption("configureMouse"),i=r?r(e,t,n):{};if(null==i.unit){var o=b?n.shiftKey&&n.metaKey:n.altKey;i.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line"}return(null==i.extend||e.doc.extend)&&(i.extend=e.doc.extend||n.shiftKey),null==i.addNew&&(i.addNew=y?n.metaKey:n.ctrlKey),null==i.moveOnDrag&&(i.moveOnDrag=!(y?n.altKey:n.ctrlKey)),i}(e,n,r),u=e.doc.sel;e.options.dragDrop&&mt&&!e.isReadOnly()&&"single"==n&&(i=u.contains(t))>-1&&(me((i=u.ranges[i]).from(),t)<0||t.xRel>0)&&(me(i.to(),t)>0||t.xRel<0)?function(e,t,n,r){var i=e.display,o=!1,u=Yr(e,function(t){s&&(i.scroller.draggable=!1),e.state.draggingText=!1,nt(i.wrapper.ownerDocument,"mouseup",u),nt(i.wrapper.ownerDocument,"mousemove",c),nt(i.scroller,"dragstart",f),nt(i.scroller,"drop",u),o||(st(t),r.addNew||ji(e.doc,n,null,null,r.extend),s||l&&9==a?setTimeout(function(){i.wrapper.ownerDocument.body.focus(),i.input.focus()},20):i.input.focus())}),c=function(e){o=o||Math.abs(t.clientX-e.clientX)+Math.abs(t.clientY-e.clientY)>=10},f=function(){return o=!0};s&&(i.scroller.draggable=!0),e.state.draggingText=u,u.copy=!r.moveOnDrag,i.scroller.dragDrop&&i.scroller.dragDrop(),et(i.wrapper.ownerDocument,"mouseup",u),et(i.wrapper.ownerDocument,"mousemove",c),et(i.scroller,"dragstart",f),et(i.scroller,"drop",u),gr(e),setTimeout(function(){return i.input.focus()},20)}(e,r,t,o):function(e,t,n,r){var i=e.display,o=e.doc;st(t);var l,a,s=o.sel,u=s.ranges;if(r.addNew&&!r.extend?(a=o.sel.contains(n),l=a>-1?u[a]:new yi(n,n)):(l=o.sel.primary(),a=o.sel.primIndex),"rectangle"==r.unit)r.addNew||(l=new yi(n,n)),n=lr(e,t,!0,!0),a=-1;else{var c=hl(e,n,r.unit);l=r.extend?_i(l,c.anchor,c.head,r.extend):c}r.addNew?-1==a?(a=u.length,Yi(o,bi(u.concat([l]),a),{scroll:!1,origin:"*mouse"})):u.length>1&&u[a].empty()&&"char"==r.unit&&!r.extend?(Yi(o,bi(u.slice(0,a).concat(u.slice(a+1)),0),{scroll:!1,origin:"*mouse"}),s=o.sel):Vi(o,a,l,G):(a=0,Yi(o,new vi([l],0),G),s=o.sel);var f=n;function h(t){if(0!=me(f,t))if(f=t,"rectangle"==r.unit){for(var i=[],u=e.options.tabSize,c=R(ae(o,n.line).text,n.ch,u),h=R(ae(o,t.line).text,t.ch,u),d=Math.min(c,h),p=Math.max(c,h),g=Math.min(n.line,t.line),m=Math.min(e.lastLine(),Math.max(n.line,t.line));g<=m;g++){var v=ae(o,g).text,y=U(v,d,u);d==p?i.push(new yi(ge(g,y),ge(g,y))):v.length>y&&i.push(new yi(ge(g,y),ge(g,U(v,p,u))))}i.length||i.push(new yi(n,n)),Yi(o,bi(s.ranges.slice(0,a).concat(i),a),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var b,x=l,w=hl(e,t,r.unit),k=x.anchor;me(w.anchor,k)>0?(b=w.head,k=xe(x.from(),w.anchor)):(b=w.anchor,k=be(x.to(),w.head));var C=s.ranges.slice(0);C[a]=function(e,t){var n=t.anchor,r=t.head,i=ae(e.doc,n.line);if(0==me(n,r)&&n.sticky==r.sticky)return t;var o=Ze(i);if(!o)return t;var l=qe(o,n.ch,n.sticky),a=o[l];if(a.from!=n.ch&&a.to!=n.ch)return t;var s,u=l+(a.from==n.ch==(1!=a.level)?0:1);if(0==u||u==o.length)return t;if(r.line!=n.line)s=(r.line-n.line)*("ltr"==e.doc.direction?1:-1)>0;else{var c=qe(o,r.ch,r.sticky),f=c-l||(r.ch-n.ch)*(1==a.level?-1:1);s=c==u-1||c==u?f<0:f>0}var h=o[u+(s?-1:0)],d=s==(1==h.level),p=d?h.from:h.to,g=d?"after":"before";return n.ch==p&&n.sticky==g?t:new yi(new ge(n.line,p,g),r)}(e,new yi(ke(o,k),b)),Yi(o,bi(C,a),G)}}var d=i.wrapper.getBoundingClientRect(),p=0;function g(t){e.state.selectingText=!1,p=1/0,st(t),i.input.focus(),nt(i.wrapper.ownerDocument,"mousemove",m),nt(i.wrapper.ownerDocument,"mouseup",v),o.history.lastSelOrigin=null}var m=Yr(e,function(t){0!==t.buttons&&dt(t)?function t(n){var l=++p,a=lr(e,n,!0,"rectangle"==r.unit);if(a)if(0!=me(a,f)){e.curOp.focus=D(),h(a);var s=xr(i,o);(a.line>=s.to||a.line<s.from)&&setTimeout(Yr(e,function(){p==l&&t(n)}),150)}else{var u=n.clientY<d.top?-20:n.clientY>d.bottom?20:0;u&&setTimeout(Yr(e,function(){p==l&&(i.scroller.scrollTop+=u,t(n))}),50)}}(t):g(t)}),v=Yr(e,g);e.state.selectingText=v,et(i.wrapper.ownerDocument,"mousemove",m),et(i.wrapper.ownerDocument,"mouseup",v)}(e,r,t,o)}(t,r,o,e):ht(e)==n.scroller&&st(e):2==i?(r&&ji(t.doc,r),setTimeout(function(){return n.input.focus()},20)):3==i&&(C?gl(t,e):gr(t)))}}function hl(e,t,n){if("char"==n)return new yi(t,t);if("word"==n)return e.findWordAt(t);if("line"==n)return new yi(ge(t.line,0),ke(e.doc,ge(t.line+1,0)));var r=n(e,t);return new yi(r.from,r.to)}function dl(e,t,n,r){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(t){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;r&&st(t);var l=e.display,a=l.lineDiv.getBoundingClientRect();if(o>a.bottom||!lt(e,n))return ct(t);o-=a.top-l.viewOffset;for(var s=0;s<e.options.gutters.length;++s){var u=l.gutters.childNodes[s];if(u&&u.getBoundingClientRect().right>=i){var c=he(e.doc,o),f=e.options.gutters[s];return rt(e,n,e,c,f,t),ct(t)}}}function pl(e,t){return dl(e,t,"gutterClick",!0)}function gl(e,t){wn(e.display,t)||function(e,t){return!!lt(e,"gutterContextMenu")&&dl(e,t,"gutterContextMenu",!1)}(e,t)||it(e,t,"contextmenu")||e.display.input.onContextMenu(t)}function ml(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),zn(e)}cl.prototype.compare=function(e,t,n){return this.time+400>e&&0==me(t,this.pos)&&n==this.button};var vl={toString:function(){return"CodeMirror.Init"}},yl={},bl={};function xl(e){ci(e),Jr(e),wr(e)}function wl(e,t,n){var r=n&&n!=vl;if(!t!=!r){var i=e.display.dragFunctions,o=t?et:nt;o(e.display.scroller,"dragstart",i.start),o(e.display.scroller,"dragenter",i.enter),o(e.display.scroller,"dragover",i.over),o(e.display.scroller,"dragleave",i.leave),o(e.display.scroller,"drop",i.drop)}}function kl(e){e.options.lineWrapping?(W(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(T(e.display.wrapper,"CodeMirror-wrap"),Ye(e)),or(e),Jr(e),zn(e),setTimeout(function(){return Fr(e)},100)}function Cl(e,t){var r=this;if(!(this instanceof Cl))return new Cl(e,t);this.options=t=t?P(t):{},P(yl,t,!1),fi(t);var i=t.value;"string"==typeof i?i=new To(i,t.mode,null,t.lineSeparator,t.direction):t.mode&&(i.modeOption=t.mode),this.doc=i;var o=new Cl.inputStyles[t.inputStyle](this),u=this.display=new function(e,t,r){var i=this;this.input=r,i.scrollbarFiller=A("div",null,"CodeMirror-scrollbar-filler"),i.scrollbarFiller.setAttribute("cm-not-content","true"),i.gutterFiller=A("div",null,"CodeMirror-gutter-filler"),i.gutterFiller.setAttribute("cm-not-content","true"),i.lineDiv=O("div",null,"CodeMirror-code"),i.selectionDiv=A("div",null,null,"position: relative; z-index: 1"),i.cursorDiv=A("div",null,"CodeMirror-cursors"),i.measure=A("div",null,"CodeMirror-measure"),i.lineMeasure=A("div",null,"CodeMirror-measure"),i.lineSpace=O("div",[i.measure,i.lineMeasure,i.selectionDiv,i.cursorDiv,i.lineDiv],null,"position: relative; outline: none");var o=O("div",[i.lineSpace],"CodeMirror-lines");i.mover=A("div",[o],null,"position: relative"),i.sizer=A("div",[i.mover],"CodeMirror-sizer"),i.sizerWidth=null,i.heightForcer=A("div",null,null,"position: absolute; height: "+$+"px; width: 1px;"),i.gutters=A("div",null,"CodeMirror-gutters"),i.lineGutter=null,i.scroller=A("div",[i.sizer,i.heightForcer,i.gutters],"CodeMirror-scroll"),i.scroller.setAttribute("tabIndex","-1"),i.wrapper=A("div",[i.scrollbarFiller,i.gutterFiller,i.scroller],"CodeMirror"),l&&a<8&&(i.gutters.style.zIndex=-1,i.scroller.style.paddingRight=0),s||n&&v||(i.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(i.wrapper):e(i.wrapper)),i.viewFrom=i.viewTo=t.first,i.reportedViewFrom=i.reportedViewTo=t.first,i.view=[],i.renderedView=null,i.externalMeasured=null,i.viewOffset=0,i.lastWrapHeight=i.lastWrapWidth=0,i.updateLineNumbers=null,i.nativeBarWidth=i.barHeight=i.barWidth=0,i.scrollbarsClipped=!1,i.lineNumWidth=i.lineNumInnerWidth=i.lineNumChars=null,i.alignWidgets=!1,i.cachedCharWidth=i.cachedTextHeight=i.cachedPaddingH=null,i.maxLine=null,i.maxLineLength=0,i.maxLineChanged=!1,i.wheelDX=i.wheelDY=i.wheelStartX=i.wheelStartY=null,i.shift=!1,i.selForContextMenu=null,i.activeTouch=null,r.init(i)}(e,i,o);for(var c in u.wrapper.CodeMirror=this,ci(this),ml(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),Rr(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:!1,cutIncoming:!1,selectingText:!1,draggingText:!1,highlight:new z,keySeq:null,specialChars:null},t.autofocus&&!v&&u.input.focus(),l&&a<11&&setTimeout(function(){return r.display.input.reset(!0)},20),function(e){var t=e.display;et(t.scroller,"mousedown",Yr(e,fl)),et(t.scroller,"dblclick",l&&a<11?Yr(e,function(t){if(!it(e,t)){var n=lr(e,t);if(n&&!pl(e,t)&&!wn(e.display,t)){st(t);var r=e.findWordAt(n);ji(e.doc,r.anchor,r.head)}}}):function(t){return it(e,t)||st(t)}),C||et(t.scroller,"contextmenu",function(t){return gl(e,t)});var n,r={end:0};function i(){t.activeTouch&&(n=setTimeout(function(){return t.activeTouch=null},1e3),(r=t.activeTouch).end=+new Date)}function o(e,t){if(null==t.left)return!0;var n=t.left-e.left,r=t.top-e.top;return n*n+r*r>400}et(t.scroller,"touchstart",function(i){if(!it(e,i)&&!function(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}(i)&&!pl(e,i)){t.input.ensurePolled(),clearTimeout(n);var o=+new Date;t.activeTouch={start:o,moved:!1,prev:o-r.end<=300?r:null},1==i.touches.length&&(t.activeTouch.left=i.touches[0].pageX,t.activeTouch.top=i.touches[0].pageY)}}),et(t.scroller,"touchmove",function(){t.activeTouch&&(t.activeTouch.moved=!0)}),et(t.scroller,"touchend",function(n){var r=t.activeTouch;if(r&&!wn(t,n)&&null!=r.left&&!r.moved&&new Date-r.start<300){var l,a=e.coordsChar(t.activeTouch,"page");l=!r.prev||o(r,r.prev)?new yi(a,a):!r.prev.prev||o(r,r.prev.prev)?e.findWordAt(a):new yi(ge(a.line,0),ke(e.doc,ge(a.line+1,0))),e.setSelection(l.anchor,l.head),e.focus(),st(n)}i()}),et(t.scroller,"touchcancel",i),et(t.scroller,"scroll",function(){t.scroller.clientHeight&&(Ar(e,t.scroller.scrollTop),Er(e,t.scroller.scrollLeft,!0),rt(e,"scroll",e))}),et(t.scroller,"mousewheel",function(t){return mi(e,t)}),et(t.scroller,"DOMMouseScroll",function(t){return mi(e,t)}),et(t.wrapper,"scroll",function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0}),t.dragFunctions={enter:function(t){it(e,t)||ft(t)},over:function(t){it(e,t)||(function(e,t){var n=lr(e,t);if(n){var r=document.createDocumentFragment();cr(e,n,r),e.display.dragCursor||(e.display.dragCursor=A("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),N(e.display.dragCursor,r)}}(e,t),ft(t))},start:function(t){return function(e,t){if(l&&(!e.state.draggingText||+new Date-Mo<100))ft(t);else if(!it(e,t)&&!wn(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!h)){var n=A("img",null,null,"position: fixed; left: 0; top: 0;");n.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",f&&(n.width=n.height=1,e.display.wrapper.appendChild(n),n._top=n.offsetTop),t.dataTransfer.setDragImage(n,0,0),f&&n.parentNode.removeChild(n)}}(e,t)},drop:Yr(e,No),leave:function(t){it(e,t)||Ao(e)}};var s=t.input.getField();et(s,"keyup",function(t){return ll.call(e,t)}),et(s,"keydown",Yr(e,ol)),et(s,"keypress",Yr(e,al)),et(s,"focus",function(t){return mr(e,t)}),et(s,"blur",function(t){return vr(e,t)})}(this),Do(),Br(this),this.curOp.forceUpdate=!0,Oi(this,i),t.autofocus&&!v||this.hasFocus()?setTimeout(I(mr,this),20):vr(this),bl)bl.hasOwnProperty(c)&&bl[c](r,t[c],vl);kr(this),t.finishInit&&t.finishInit(this);for(var d=0;d<Sl.length;++d)Sl[d](r);$r(this),s&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(u.lineDiv).textRendering&&(u.lineDiv.style.textRendering="auto")}Cl.defaults=yl,Cl.optionHandlers=bl;var Sl=[];function Ll(e,t,n,r){var i,o=e.doc;null==n&&(n="add"),"smart"==n&&(o.mode.indent?i=Pt(e,t).state:n="prev");var l=e.options.tabSize,a=ae(o,t),s=R(a.text,null,l);a.stateAfter&&(a.stateAfter=null);var u,c=a.text.match(/^\s*/)[0];if(r||/\S/.test(a.text)){if("smart"==n&&((u=o.mode.indent(i,a.text.slice(c.length),a.text))==_||u>150)){if(!r)return;n="prev"}}else u=0,n="not";"prev"==n?u=t>o.first?R(ae(o,t-1).text,null,l):0:"add"==n?u=s+e.options.indentUnit:"subtract"==n?u=s-e.options.indentUnit:"number"==typeof n&&(u=s+n),u=Math.max(0,u);var f="",h=0;if(e.options.indentWithTabs)for(var d=Math.floor(u/l);d;--d)h+=l,f+="\t";if(h<u&&(f+=Y(u-h)),f!=c)return uo(o,f,ge(t,0),ge(t,c.length),"+input"),a.stateAfter=null,!0;for(var p=0;p<o.sel.ranges.length;p++){var g=o.sel.ranges[p];if(g.head.line==t&&g.head.ch<c.length){var m=ge(t,c.length);Vi(o,p,new yi(m,m));break}}}Cl.defineInitHook=function(e){return Sl.push(e)};var Tl=null;function Ml(e){Tl=e}function Nl(e,t,n,r,i){var o=e.doc;e.display.shift=!1,r||(r=o.sel);var l,a=e.state.pasteIncoming||"paste"==i,s=bt(t),u=null;if(a&&r.ranges.length>1)if(Tl&&Tl.text.join("\n")==t){if(r.ranges.length%Tl.text.length==0){u=[];for(var c=0;c<Tl.text.length;c++)u.push(o.splitLines(Tl.text[c]))}}else s.length==r.ranges.length&&e.options.pasteLinesPerSelection&&(u=q(s,function(e){return[e]}));for(var f=r.ranges.length-1;f>=0;f--){var h=r.ranges[f],d=h.from(),p=h.to();h.empty()&&(n&&n>0?d=ge(d.line,d.ch-n):e.state.overwrite&&!a?p=ge(p.line,Math.min(ae(o,p.line).text.length,p.ch+X(s).length)):Tl&&Tl.lineWise&&Tl.text.join("\n")==t&&(d=p=ge(d.line,0))),l=e.curOp.updateInput;var g={from:d,to:p,text:u?u[f%u.length]:s,origin:i||(a?"paste":e.state.cutIncoming?"cut":"+input")};io(e.doc,g),an(e,"inputRead",e,g)}t&&!a&&Ol(e,t),Lr(e),e.curOp.updateInput=l,e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=!1}function Al(e,t){var n=e.clipboardData&&e.clipboardData.getData("Text");if(n)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||Kr(t,function(){return Nl(t,n,0,null,"paste")}),!0}function Ol(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var n=e.doc.sel,r=n.ranges.length-1;r>=0;r--){var i=n.ranges[r];if(!(i.head.ch>100||r&&n.ranges[r-1].head.line==i.head.line)){var o=e.getModeAt(i.head),l=!1;if(o.electricChars){for(var a=0;a<o.electricChars.length;a++)if(t.indexOf(o.electricChars.charAt(a))>-1){l=Ll(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(ae(e.doc,i.head.line).text.slice(0,i.head.ch))&&(l=Ll(e,i.head.line,"smart"));l&&an(e,"electricInput",e,i.head.line)}}}function El(e){for(var t=[],n=[],r=0;r<e.doc.sel.ranges.length;r++){var i=e.doc.sel.ranges[r].head.line,o={anchor:ge(i,0),head:ge(i+1,0)};n.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:n}}function Dl(e,t){e.setAttribute("autocorrect","off"),e.setAttribute("autocapitalize","off"),e.setAttribute("spellcheck",!!t)}function Wl(){var e=A("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; outline: none"),t=A("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return s?e.style.width="1000px":e.setAttribute("wrap","off"),g&&(e.style.border="1px solid black"),Dl(e),t}function Hl(e,t,n,r,i){var o=t,l=n,a=ae(e,t.line);function s(r){var o;if(null==(o=i?function(e,t,n,r){var i=Ze(t,e.doc.direction);if(!i)return Yo(t,n,r);n.ch>=t.text.length?(n.ch=t.text.length,n.sticky="before"):n.ch<=0&&(n.ch=0,n.sticky="after");var o=qe(i,n.ch,n.sticky),l=i[o];if("ltr"==e.doc.direction&&l.level%2==0&&(r>0?l.to>n.ch:l.from<n.ch))return Yo(t,n,r);var a,s=function(e,n){return Ko(t,e instanceof ge?e.ch:e,n)},u=function(n){return e.options.lineWrapping?(a=a||En(e,t),Jn(e,t,a,n)):{begin:0,end:t.text.length}},c=u("before"==n.sticky?s(n,-1):n.ch);if("rtl"==e.doc.direction||1==l.level){var f=1==l.level==r<0,h=s(n,f?1:-1);if(null!=h&&(f?h<=l.to&&h<=c.end:h>=l.from&&h>=c.begin)){var d=f?"before":"after";return new ge(n.line,h,d)}}var p=function(e,t,r){for(var o=function(e,t){return t?new ge(n.line,s(e,1),"before"):new ge(n.line,e,"after")};e>=0&&e<i.length;e+=t){var l=i[e],a=t>0==(1!=l.level),u=a?r.begin:s(r.end,-1);if(l.from<=u&&u<l.to)return o(u,a);if(u=a?l.from:s(l.to,-1),r.begin<=u&&u<r.end)return o(u,a)}},g=p(o+r,r,c);if(g)return g;var m=r>0?c.end:s(c.begin,-1);return null==m||r>0&&m==t.text.length||!(g=p(r>0?0:i.length-1,r,u(m)))?null:g}(e.cm,a,t,n):Yo(a,t,n))){if(r||!function(){var r=t.line+n;return!(r<e.first||r>=e.first+e.size)&&(t=new ge(r,t.ch,t.sticky),a=ae(e,r))}())return!1;t=Xo(i,e.cm,a,t.line,n)}else t=o;return!0}if("char"==r)s();else if("column"==r)s(!0);else if("word"==r||"group"==r)for(var u=null,c="group"==r,f=e.cm&&e.cm.getHelper(t,"wordChars"),h=!0;!(n<0)||s(!h);h=!1){var d=a.text.charAt(t.ch)||"\n",p=te(d,f)?"w":c&&"\n"==d?"n":!c||/\s/.test(d)?null:"p";if(!c||h||p||(p="s"),u&&u!=p){n<0&&(n=1,s(),t.sticky="after");break}if(p&&(u=p),n>0&&!s(!h))break}var g=eo(e,t,o,l,!0);return ve(o,g)&&(g.hitSide=!0),g}function Fl(e,t,n,r){var i,o,l=e.doc,a=t.left;if("page"==r){var s=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),u=Math.max(s-.5*er(e.display),3);i=(n>0?t.bottom:t.top)+n*u}else"line"==r&&(i=n>0?t.bottom+3:t.top-3);for(;(o=Xn(e,a,i)).outside;){if(n<0?i<=0:i>=l.height){o.hitSide=!0;break}i+=5*n}return o}var Il=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new z,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function Pl(e,t){var n=On(e,t.line);if(!n||n.hidden)return null;var r=ae(e.doc,t.line),i=Nn(n,r,t.line),o=Ze(r,e.doc.direction),l="left";if(o){var a=qe(o,t.ch);l=a%2?"right":"left"}var s=Fn(i.map,t.ch,l);return s.offset="right"==s.collapse?s.end:s.start,s}function Rl(e,t){return t&&(e.bad=!0),e}function zl(e,t,n){var r;if(t==e.display.lineDiv){if(!(r=e.display.lineDiv.childNodes[n]))return Rl(e.clipPos(ge(e.display.viewTo-1)),!0);t=null,n=0}else for(r=t;;r=r.parentNode){if(!r||r==e.display.lineDiv)return null;if(r.parentNode&&r.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==r)return Bl(o,t,n)}}function Bl(e,t,n){var r=e.text.firstChild,i=!1;if(!t||!E(r,t))return Rl(ge(fe(e.line),0),!0);if(t==r&&(i=!0,t=r.childNodes[n],n=0,!t)){var o=e.rest?X(e.rest):e.line;return Rl(ge(fe(o),o.text.length),i)}var l=3==t.nodeType?t:null,a=t;for(l||1!=t.childNodes.length||3!=t.firstChild.nodeType||(l=t.firstChild,n&&(n=l.nodeValue.length));a.parentNode!=r;)a=a.parentNode;var s=e.measure,u=s.maps;function c(t,n,r){for(var i=-1;i<(u?u.length:0);i++)for(var o=i<0?s.map:u[i],l=0;l<o.length;l+=3){var a=o[l+2];if(a==t||a==n){var c=fe(i<0?e.line:e.rest[i]),f=o[l]+r;return(r<0||a!=t)&&(f=o[l+(r?1:0)]),ge(c,f)}}}var f=c(l,a,n);if(f)return Rl(f,i);for(var h=a.nextSibling,d=l?l.nodeValue.length-n:0;h;h=h.nextSibling){if(f=c(h,h.firstChild,0))return Rl(ge(f.line,f.ch-d),i);d+=h.textContent.length}for(var p=a.previousSibling,g=n;p;p=p.previousSibling){if(f=c(p,p.firstChild,-1))return Rl(ge(f.line,f.ch+g),i);g+=p.textContent.length}}Il.prototype.init=function(e){var t=this,n=this,r=n.cm,i=n.div=e.lineDiv;function o(e){if(!it(r,e)){if(r.somethingSelected())Ml({lineWise:!1,text:r.getSelections()}),"cut"==e.type&&r.replaceSelection("",null,"cut");else{if(!r.options.lineWiseCopyCut)return;var t=El(r);Ml({lineWise:!0,text:t.text}),"cut"==e.type&&r.operation(function(){r.setSelections(t.ranges,0,j),r.replaceSelection("",null,"cut")})}if(e.clipboardData){e.clipboardData.clearData();var o=Tl.text.join("\n");if(e.clipboardData.setData("Text",o),e.clipboardData.getData("Text")==o)return void e.preventDefault()}var l=Wl(),a=l.firstChild;r.display.lineSpace.insertBefore(l,r.display.lineSpace.firstChild),a.value=Tl.text.join("\n");var s=document.activeElement;F(a),setTimeout(function(){r.display.lineSpace.removeChild(l),s.focus(),s==i&&n.showPrimarySelection()},50)}}Dl(i,r.options.spellcheck),et(i,"paste",function(e){it(r,e)||Al(e,r)||a<=11&&setTimeout(Yr(r,function(){return t.updateFromDOM()}),20)}),et(i,"compositionstart",function(e){t.composing={data:e.data,done:!1}}),et(i,"compositionupdate",function(e){t.composing||(t.composing={data:e.data,done:!1})}),et(i,"compositionend",function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)}),et(i,"touchstart",function(){return n.forceCompositionEnd()}),et(i,"input",function(){t.composing||t.readFromDOMSoon()}),et(i,"copy",o),et(i,"cut",o)},Il.prototype.prepareSelection=function(){var e=ur(this.cm,!1);return e.focus=this.cm.state.focused,e},Il.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},Il.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},Il.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,r=t.doc.sel.primary(),i=r.from(),o=r.to();if(t.display.viewTo==t.display.viewFrom||i.line>=t.display.viewTo||o.line<t.display.viewFrom)e.removeAllRanges();else{var l=zl(t,e.anchorNode,e.anchorOffset),a=zl(t,e.focusNode,e.focusOffset);if(!l||l.bad||!a||a.bad||0!=me(xe(l,a),i)||0!=me(be(l,a),o)){var s=t.display.view,u=i.line>=t.display.viewFrom&&Pl(t,i)||{node:s[0].measure.map[2],offset:0},c=o.line<t.display.viewTo&&Pl(t,o);if(!c){var f=s[s.length-1].measure,h=f.maps?f.maps[f.maps.length-1]:f.map;c={node:h[h.length-1],offset:h[h.length-2]-h[h.length-3]}}if(u&&c){var d,p=e.rangeCount&&e.getRangeAt(0);try{d=L(u.node,u.offset,c.offset,c.node)}catch(e){}d&&(!n&&t.state.focused?(e.collapse(u.node,u.offset),d.collapsed||(e.removeAllRanges(),e.addRange(d))):(e.removeAllRanges(),e.addRange(d)),p&&null==e.anchorNode?e.addRange(p):n&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},Il.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},Il.prototype.showMultipleSelections=function(e){N(this.cm.display.cursorDiv,e.cursors),N(this.cm.display.selectionDiv,e.selection)},Il.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},Il.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return E(this.div,t)},Il.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()||this.showSelection(this.prepareSelection(),!0),this.div.focus())},Il.prototype.blur=function(){this.div.blur()},Il.prototype.getField=function(){return this.div},Il.prototype.supportsTouch=function(){return!0},Il.prototype.receivedFocus=function(){var e=this;this.selectionInEditor()?this.pollSelection():Kr(this.cm,function(){return e.cm.curOp.selectionChanged=!0}),this.polling.set(this.cm.options.pollInterval,function t(){e.cm.state.focused&&(e.pollSelection(),e.polling.set(e.cm.options.pollInterval,t))})},Il.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},Il.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=this.getSelection(),t=this.cm;if(m&&c&&this.cm.options.gutters.length&&function(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var n=zl(t,e.anchorNode,e.anchorOffset),r=zl(t,e.focusNode,e.focusOffset);n&&r&&Kr(t,function(){Yi(t.doc,xi(n,r),j),(n.bad||r.bad)&&(t.curOp.selectionChanged=!0)})}}},Il.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,n,r=this.cm,i=r.display,o=r.doc.sel.primary(),l=o.from(),a=o.to();if(0==l.ch&&l.line>r.firstLine()&&(l=ge(l.line-1,ae(r.doc,l.line-1).length)),a.ch==ae(r.doc,a.line).text.length&&a.line<r.lastLine()&&(a=ge(a.line+1,0)),l.line<i.viewFrom||a.line>i.viewTo-1)return!1;l.line==i.viewFrom||0==(e=ar(r,l.line))?(t=fe(i.view[0].line),n=i.view[0].node):(t=fe(i.view[e].line),n=i.view[e-1].node.nextSibling);var s,u,c=ar(r,a.line);if(c==i.view.length-1?(s=i.viewTo-1,u=i.lineDiv.lastChild):(s=fe(i.view[c+1].line)-1,u=i.view[c+1].node.previousSibling),!n)return!1;for(var f=r.doc.splitLines(function(e,t,n,r,i){var o="",l=!1,a=e.doc.lineSeparator(),s=!1;function u(){l&&(o+=a,s&&(o+=a),l=s=!1)}function c(e){e&&(u(),o+=e)}function f(t){if(1==t.nodeType){var n=t.getAttribute("cm-text");if(n)return void c(n);var o,h=t.getAttribute("cm-marker");if(h){var d=e.findMarks(ge(r,0),ge(i+1,0),function(e){return function(t){return t.id==e}}(+h));return void(d.length&&(o=d[0].find(0))&&c(se(e.doc,o.from,o.to).join(a)))}if("false"==t.getAttribute("contenteditable"))return;var p=/^(pre|div|p|li|table|br)$/i.test(t.nodeName);if(!/^br$/i.test(t.nodeName)&&0==t.textContent.length)return;p&&u();for(var g=0;g<t.childNodes.length;g++)f(t.childNodes[g]);/^(pre|p)$/i.test(t.nodeName)&&(s=!0),p&&(l=!0)}else 3==t.nodeType&&c(t.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;f(t),t!=n;)t=t.nextSibling,s=!1;return o}(r,n,u,t,s)),h=se(r.doc,ge(t,0),ge(s,ae(r.doc,s).text.length));f.length>1&&h.length>1;)if(X(f)==X(h))f.pop(),h.pop(),s--;else{if(f[0]!=h[0])break;f.shift(),h.shift(),t++}for(var d=0,p=0,g=f[0],m=h[0],v=Math.min(g.length,m.length);d<v&&g.charCodeAt(d)==m.charCodeAt(d);)++d;for(var y=X(f),b=X(h),x=Math.min(y.length-(1==f.length?d:0),b.length-(1==h.length?d:0));p<x&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)++p;if(1==f.length&&1==h.length&&t==l.line)for(;d&&d>l.ch&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)d--,p++;f[f.length-1]=y.slice(0,y.length-p).replace(/^\u200b+/,""),f[0]=f[0].slice(d).replace(/\u200b+$/,"");var w=ge(t,d),k=ge(s,h.length?X(h).length-p:0);return f.length>1||f[0]||me(w,k)?(uo(r.doc,f,w,k,"+input"),!0):void 0},Il.prototype.ensurePolled=function(){this.forceCompositionEnd()},Il.prototype.reset=function(){this.forceCompositionEnd()},Il.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},Il.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()},80))},Il.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||Kr(this.cm,function(){return Jr(e.cm)})},Il.prototype.setUneditable=function(e){e.contentEditable="false"},Il.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||Yr(this.cm,Nl)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},Il.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},Il.prototype.onContextMenu=function(){},Il.prototype.resetPosition=function(){},Il.prototype.needsContentAttribute=!0;var $l=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new z,this.hasSelection=!1,this.composing=null};$l.prototype.init=function(e){var t=this,n=this,r=this.cm;this.createField(e);var i=this.textarea;function o(e){if(!it(r,e)){if(r.somethingSelected())Ml({lineWise:!1,text:r.getSelections()});else{if(!r.options.lineWiseCopyCut)return;var t=El(r);Ml({lineWise:!0,text:t.text}),"cut"==e.type?r.setSelections(t.ranges,null,j):(n.prevInput="",i.value=t.text.join("\n"),F(i))}"cut"==e.type&&(r.state.cutIncoming=!0)}}e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),g&&(i.style.width="0px"),et(i,"input",function(){l&&a>=9&&t.hasSelection&&(t.hasSelection=null),n.poll()}),et(i,"paste",function(e){it(r,e)||Al(e,r)||(r.state.pasteIncoming=!0,n.fastPoll())}),et(i,"cut",o),et(i,"copy",o),et(e.scroller,"paste",function(t){wn(e,t)||it(r,t)||(r.state.pasteIncoming=!0,n.focus())}),et(e.lineSpace,"selectstart",function(t){wn(e,t)||st(t)}),et(i,"compositionstart",function(){var e=r.getCursor("from");n.composing&&n.composing.range.clear(),n.composing={start:e,range:r.markText(e,r.getCursor("to"),{className:"CodeMirror-composing"})}}),et(i,"compositionend",function(){n.composing&&(n.poll(),n.composing.range.clear(),n.composing=null)})},$l.prototype.createField=function(e){this.wrapper=Wl(),this.textarea=this.wrapper.firstChild},$l.prototype.prepareSelection=function(){var e=this.cm,t=e.display,n=e.doc,r=ur(e);if(e.options.moveInputWithCursor){var i=Un(e,n.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),l=t.lineDiv.getBoundingClientRect();r.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+l.top-o.top)),r.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+l.left-o.left))}return r},$l.prototype.showSelection=function(e){var t=this.cm,n=t.display;N(n.cursorDiv,e.cursors),N(n.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},$l.prototype.reset=function(e){if(!this.contextMenuPending&&!this.composing){var t=this.cm;if(t.somethingSelected()){this.prevInput="";var n=t.getSelection();this.textarea.value=n,t.state.focused&&F(this.textarea),l&&a>=9&&(this.hasSelection=n)}else e||(this.prevInput=this.textarea.value="",l&&a>=9&&(this.hasSelection=null))}},$l.prototype.getField=function(){return this.textarea},$l.prototype.supportsTouch=function(){return!1},$l.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!v||D()!=this.textarea))try{this.textarea.focus()}catch(e){}},$l.prototype.blur=function(){this.textarea.blur()},$l.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},$l.prototype.receivedFocus=function(){this.slowPoll()},$l.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},$l.prototype.fastPoll=function(){var e=!1,t=this;t.pollingFast=!0,t.polling.set(20,function n(){var r=t.poll();r||e?(t.pollingFast=!1,t.slowPoll()):(e=!0,t.polling.set(60,n))})},$l.prototype.poll=function(){var e=this,t=this.cm,n=this.textarea,r=this.prevInput;if(this.contextMenuPending||!t.state.focused||xt(n)&&!r&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=n.value;if(i==r&&!t.somethingSelected())return!1;if(l&&a>=9&&this.hasSelection===i||y&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||r||(r="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var s=0,u=Math.min(r.length,i.length);s<u&&r.charCodeAt(s)==i.charCodeAt(s);)++s;return Kr(t,function(){Nl(t,i.slice(s),r.length-s,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?n.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},$l.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},$l.prototype.onKeyPress=function(){l&&a>=9&&(this.hasSelection=null),this.fastPoll()},$l.prototype.onContextMenu=function(e){var t=this,n=t.cm,r=n.display,i=t.textarea,o=lr(n,e),u=r.scroller.scrollTop;if(o&&!f){var c=n.options.resetSelectionOnContextMenu;c&&-1==n.doc.sel.contains(o)&&Yr(n,Yi)(n.doc,xi(o),j);var h=i.style.cssText,d=t.wrapper.style.cssText;t.wrapper.style.cssText="position: absolute";var p,g=t.wrapper.getBoundingClientRect();if(i.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-g.top-5)+"px; left: "+(e.clientX-g.left-5)+"px;\n      z-index: 1000; background: "+(l?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",s&&(p=window.scrollY),r.input.focus(),s&&window.scrollTo(null,p),r.input.reset(),n.somethingSelected()||(i.value=t.prevInput=" "),t.contextMenuPending=!0,r.selForContextMenu=n.doc.sel,clearTimeout(r.detectingSelectAll),l&&a>=9&&v(),C){ft(e);var m=function(){nt(window,"mouseup",m),setTimeout(y,20)};et(window,"mouseup",m)}else setTimeout(y,50)}function v(){if(null!=i.selectionStart){var e=n.somethingSelected(),o="​"+(e?i.value:"");i.value="⇚",i.value=o,t.prevInput=e?"":"​",i.selectionStart=1,i.selectionEnd=o.length,r.selForContextMenu=n.doc.sel}}function y(){if(t.contextMenuPending=!1,t.wrapper.style.cssText=d,i.style.cssText=h,l&&a<9&&r.scrollbars.setScrollTop(r.scroller.scrollTop=u),null!=i.selectionStart){(!l||l&&a<9)&&v();var e=0,o=function(){r.selForContextMenu==n.doc.sel&&0==i.selectionStart&&i.selectionEnd>0&&"​"==t.prevInput?Yr(n,no)(n):e++<10?r.detectingSelectAll=setTimeout(o,500):(r.selForContextMenu=null,r.input.reset())};r.detectingSelectAll=setTimeout(o,200)}}},$l.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e},$l.prototype.setUneditable=function(){},$l.prototype.needsContentAttribute=!1,function(e){var t=e.optionHandlers;function n(n,r,i,o){e.defaults[n]=r,i&&(t[n]=o?function(e,t,n){n!=vl&&i(e,t,n)}:i)}e.defineOption=n,e.Init=vl,n("value","",function(e,t){return e.setValue(t)},!0),n("mode",null,function(e,t){e.doc.modeOption=t,Li(e)},!0),n("indentUnit",2,Li,!0),n("indentWithTabs",!1),n("smartIndent",!0),n("tabSize",4,function(e){Ti(e),zn(e),Jr(e)},!0),n("lineSeparator",null,function(e,t){if(e.doc.lineSep=t,t){var n=[],r=e.doc.first;e.doc.iter(function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(-1==o)break;i=o+t.length,n.push(ge(r,o))}r++});for(var i=n.length-1;i>=0;i--)uo(e.doc,t,n[i],ge(n[i].line,n[i].ch+t.length))}}),n("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b-\u200f\u2028\u2029\ufeff]/g,function(e,t,n){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),n!=vl&&e.refresh()}),n("specialCharPlaceholder",Jt,function(e){return e.refresh()},!0),n("electricChars",!0),n("inputStyle",v?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),n("spellcheck",!1,function(e,t){return e.getInputField().spellcheck=t},!0),n("rtlMoveVisually",!x),n("wholeLineUpdateBefore",!0),n("theme","default",function(e){ml(e),xl(e)},!0),n("keyMap","default",function(e,t,n){var r=Vo(t),i=n!=vl&&Vo(n);i&&i.detach&&i.detach(e,r),r.attach&&r.attach(e,i||null)}),n("extraKeys",null),n("configureMouse",null),n("lineWrapping",!1,kl,!0),n("gutters",[],function(e){fi(e.options),xl(e)},!0),n("fixedGutter",!0,function(e,t){e.display.gutters.style.left=t?rr(e.display)+"px":"0",e.refresh()},!0),n("coverGutterNextToScrollbar",!1,function(e){return Fr(e)},!0),n("scrollbarStyle","native",function(e){Rr(e),Fr(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)},!0),n("lineNumbers",!1,function(e){fi(e.options),xl(e)},!0),n("firstLineNumber",1,xl,!0),n("lineNumberFormatter",function(e){return e},xl,!0),n("showCursorWhenSelecting",!1,sr,!0),n("resetSelectionOnContextMenu",!0),n("lineWiseCopyCut",!0),n("pasteLinesPerSelection",!0),n("readOnly",!1,function(e,t){"nocursor"==t&&(vr(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)}),n("disableInput",!1,function(e,t){t||e.display.input.reset()},!0),n("dragDrop",!0,wl),n("allowDropFileTypes",null),n("cursorBlinkRate",530),n("cursorScrollMargin",0),n("cursorHeight",1,sr,!0),n("singleCursorHeightPerLine",!0,sr,!0),n("workTime",100),n("workDelay",100),n("flattenSpans",!0,Ti,!0),n("addModeClass",!1,Ti,!0),n("pollInterval",100),n("undoDepth",200,function(e,t){return e.doc.history.undoDepth=t}),n("historyEventDelay",1250),n("viewportMargin",10,function(e){return e.refresh()},!0),n("maxHighlightLength",1e4,Ti,!0),n("moveInputWithCursor",!0,function(e,t){t||e.display.input.resetPosition()}),n("tabindex",null,function(e,t){return e.display.input.getField().tabIndex=t||""}),n("autofocus",null),n("direction","ltr",function(e,t){return e.doc.setDirection(t)},!0)}(Cl),function(e){var t=e.optionHandlers,n=e.helpers={};e.prototype={constructor:e,focus:function(){window.focus(),this.display.input.focus()},setOption:function(e,n){var r=this.options,i=r[e];r[e]==n&&"mode"!=e||(r[e]=n,t.hasOwnProperty(e)&&Yr(this,t[e])(this,n,i),rt(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](Vo(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,n=0;n<t.length;++n)if(t[n]==e||t[n].name==e)return t.splice(n,1),!0},addOverlay:Xr(function(t,n){var r=t.token?t:e.getMode(this.options,t);if(r.startState)throw new Error("Overlays may not be stateful.");!function(e,t,n){for(var r=0,i=n(t);r<e.length&&n(e[r])<=i;)r++;e.splice(r,0,t)}(this.state.overlays,{mode:r,modeSpec:t,opaque:n&&n.opaque,priority:n&&n.priority||0},function(e){return e.priority}),this.state.modeGen++,Jr(this)}),removeOverlay:Xr(function(e){for(var t=this.state.overlays,n=0;n<t.length;++n){var r=t[n].modeSpec;if(r==e||"string"==typeof e&&r.name==e)return t.splice(n,1),this.state.modeGen++,void Jr(this)}}),indentLine:Xr(function(e,t,n){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),de(this.doc,e)&&Ll(this,e,t,n)}),indentSelection:Xr(function(e){for(var t=this.doc.sel.ranges,n=-1,r=0;r<t.length;r++){var i=t[r];if(i.empty())i.head.line>n&&(Ll(this,i.head.line,e,!0),n=i.head.line,r==this.doc.sel.primIndex&&Lr(this));else{var o=i.from(),l=i.to(),a=Math.max(n,o.line);n=Math.min(this.lastLine(),l.line-(l.ch?0:1))+1;for(var s=a;s<n;++s)Ll(this,s,e);var u=this.doc.sel.ranges;0==o.ch&&t.length==u.length&&u[r].from().ch>0&&Vi(this.doc,r,new yi(o,u[r].to()),j)}}}),getTokenAt:function(e,t){return _t(this,e,t)},getLineTokens:function(e,t){return _t(this,ge(e),t,!0)},getTokenTypeAt:function(e){e=ke(this.doc,e);var t,n=It(this,ae(this.doc,e.line)),r=0,i=(n.length-1)/2,o=e.ch;if(0==o)t=n[2];else for(;;){var l=r+i>>1;if((l?n[2*l-1]:0)>=o)i=l;else{if(!(n[2*l+1]<o)){t=n[2*l+2];break}r=l+1}}var a=t?t.indexOf("overlay "):-1;return a<0?t:0==a?null:t.slice(0,a-1)},getModeAt:function(t){var n=this.doc.mode;return n.innerMode?e.innerMode(n,this.getTokenAt(t).state).mode:n},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var r=[];if(!n.hasOwnProperty(t))return r;var i=n[t],o=this.getModeAt(e);if("string"==typeof o[t])i[o[t]]&&r.push(i[o[t]]);else if(o[t])for(var l=0;l<o[t].length;l++){var a=i[o[t][l]];a&&r.push(a)}else o.helperType&&i[o.helperType]?r.push(i[o.helperType]):i[o.name]&&r.push(i[o.name]);for(var s=0;s<i._global.length;s++){var u=i._global[s];u.pred(o,this)&&-1==B(r,u.val)&&r.push(u.val)}return r},getStateAfter:function(e,t){var n=this.doc;return Pt(this,(e=we(n,null==e?n.first+n.size-1:e))+1,t).state},cursorCoords:function(e,t){var n=this.doc.sel.primary();return Un(this,null==e?n.head:"object"==typeof e?ke(this.doc,e):e?n.from():n.to(),t||"page")},charCoords:function(e,t){return Vn(this,ke(this.doc,e),t||"page")},coordsChar:function(e,t){return Xn(this,(e=Gn(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=Gn(this,{top:e,left:0},t||"page").top,he(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,n){var r,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),r=ae(this.doc,e)}else r=e;return jn(this,r,{top:0,left:0},t||"page",n||i).top+(i?this.doc.height-Ue(r):0)},defaultTextHeight:function(){return er(this.display)},defaultCharWidth:function(){return tr(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,n,r,i){var o=this.display,l=(e=Un(this,ke(this.doc,e))).bottom,a=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),o.sizer.appendChild(t),"over"==r)l=e.top;else if("above"==r||"near"==r){var s=Math.max(o.wrapper.clientHeight,this.doc.height),u=Math.max(o.sizer.clientWidth,o.lineSpace.clientWidth);("above"==r||e.bottom+t.offsetHeight>s)&&e.top>t.offsetHeight?l=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=s&&(l=e.bottom),a+t.offsetWidth>u&&(a=u-t.offsetWidth)}t.style.top=l+"px",t.style.left=t.style.right="","right"==i?(a=o.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?a=0:"middle"==i&&(a=(o.sizer.clientWidth-t.offsetWidth)/2),t.style.left=a+"px"),n&&function(e,t){var n=Cr(e,t);null!=n.scrollTop&&Ar(e,n.scrollTop),null!=n.scrollLeft&&Er(e,n.scrollLeft)}(this,{left:a,top:l,right:a+t.offsetWidth,bottom:l+t.offsetHeight})},triggerOnKeyDown:Xr(ol),triggerOnKeyPress:Xr(al),triggerOnKeyUp:ll,triggerOnMouseDown:Xr(fl),execCommand:function(e){if(qo.hasOwnProperty(e))return qo[e].call(null,this)},triggerElectric:Xr(function(e){Ol(this,e)}),findPosH:function(e,t,n,r){var i=1;t<0&&(i=-1,t=-t);for(var o=ke(this.doc,e),l=0;l<t&&!(o=Hl(this.doc,o,i,n,r)).hitSide;++l);return o},moveH:Xr(function(e,t){var n=this;this.extendSelectionsBy(function(r){return n.display.shift||n.doc.extend||r.empty()?Hl(n.doc,r.head,e,t,n.options.rtlMoveVisually):e<0?r.from():r.to()},V)}),deleteH:Xr(function(e,t){var n=this.doc.sel,r=this.doc;n.somethingSelected()?r.replaceSelection("",null,"+delete"):Uo(this,function(n){var i=Hl(r,n.head,e,t,!1);return e<0?{from:i,to:n.head}:{from:n.head,to:i}})}),findPosV:function(e,t,n,r){var i=1,o=r;t<0&&(i=-1,t=-t);for(var l=ke(this.doc,e),a=0;a<t;++a){var s=Un(this,l,"div");if(null==o?o=s.left:s.left=o,(l=Fl(this,s,i,n)).hitSide)break}return l},moveV:Xr(function(e,t){var n=this,r=this.doc,i=[],o=!this.display.shift&&!r.extend&&r.sel.somethingSelected();if(r.extendSelectionsBy(function(l){if(o)return e<0?l.from():l.to();var a=Un(n,l.head,"div");null!=l.goalColumn&&(a.left=l.goalColumn),i.push(a.left);var s=Fl(n,a,e,t);return"page"==t&&l==r.sel.primary()&&Sr(n,Vn(n,s,"div").top-a.top),s},V),i.length)for(var l=0;l<r.sel.ranges.length;l++)r.sel.ranges[l].goalColumn=i[l]}),findWordAt:function(e){var t=this.doc,n=ae(t,e.line).text,r=e.ch,i=e.ch;if(n){var o=this.getHelper(e,"wordChars");"before"!=e.sticky&&i!=n.length||!r?++i:--r;for(var l=n.charAt(r),a=te(l,o)?function(e){return te(e,o)}:/\s/.test(l)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!te(e)};r>0&&a(n.charAt(r-1));)--r;for(;i<n.length&&a(n.charAt(i));)++i}return new yi(ge(e.line,r),ge(e.line,i))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?W(this.display.cursorDiv,"CodeMirror-overwrite"):T(this.display.cursorDiv,"CodeMirror-overwrite"),rt(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==D()},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:Xr(function(e,t){Tr(this,e,t)}),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Ln(this)-this.display.barHeight,width:e.scrollWidth-Ln(this)-this.display.barWidth,clientHeight:Mn(this),clientWidth:Tn(this)}},scrollIntoView:Xr(function(e,t){null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:ge(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?function(e,t){Mr(e),e.curOp.scrollToPos=t}(this,e):Nr(this,e.from,e.to,e.margin)}),setSize:Xr(function(e,t){var n=this,r=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=r(e)),null!=t&&(this.display.wrapper.style.height=r(t)),this.options.lineWrapping&&Rn(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){Zr(n,i,"widget");break}++i}),this.curOp.forceUpdate=!0,rt(this,"refresh",this)}),operation:function(e){return Kr(this,e)},startOperation:function(){return Br(this)},endOperation:function(){return $r(this)},refresh:Xr(function(){var e=this.display.cachedTextHeight;Jr(this),this.curOp.forceUpdate=!0,zn(this),Tr(this,this.doc.scrollLeft,this.doc.scrollTop),si(this),(null==e||Math.abs(e-er(this.display))>.5)&&or(this),rt(this,"refresh",this)}),swapDoc:Xr(function(e){var t=this.doc;return t.cm=null,Oi(this,e),zn(this),this.display.input.reset(),Tr(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,an(this,"swapDoc",this,t),t}),getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},at(e),e.registerHelper=function(t,r,i){n.hasOwnProperty(t)||(n[t]=e[t]={_global:[]}),n[t][r]=i},e.registerGlobalHelper=function(t,r,i,o){e.registerHelper(t,r,o),n[t]._global.push({pred:i,val:o})}}(Cl);var _l="iter insert remove copy getEditor constructor".split(" ");for(var jl in To.prototype)To.prototype.hasOwnProperty(jl)&&B(_l,jl)<0&&(Cl.prototype[jl]=function(e){return function(){return e.apply(this.doc,arguments)}}(To.prototype[jl]));return at(To),Cl.inputStyles={textarea:$l,contenteditable:Il},Cl.defineMode=function(e){Cl.defaults.mode||"null"==e||(Cl.defaults.mode=e),function(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Ct[e]=t}.apply(this,arguments)},Cl.defineMIME=function(e,t){St[e]=t},Cl.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),Cl.defineMIME("text/plain","null"),Cl.defineExtension=function(e,t){Cl.prototype[e]=t},Cl.defineDocExtension=function(e,t){To.prototype[e]=t},Cl.fromTextArea=function(e,t){if((t=t?P(t):{}).value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var n=D();t.autofocus=n==e||null!=e.getAttribute("autofocus")&&n==document.body}function r(){e.value=a.getValue()}var i;if(e.form&&(et(e.form,"submit",r),!t.leaveSubmitMethodAlone)){var o=e.form;i=o.submit;try{var l=o.submit=function(){r(),o.submit=i,o.submit(),o.submit=l}}catch(e){}}t.finishInit=function(t){t.save=r,t.getTextArea=function(){return e},t.toTextArea=function(){t.toTextArea=isNaN,r(),e.parentNode.removeChild(t.getWrapperElement()),e.style.display="",e.form&&(nt(e.form,"submit",r),"function"==typeof e.form.submit&&(e.form.submit=i))}},e.style.display="none";var a=Cl(function(t){return e.parentNode.insertBefore(t,e.nextSibling)},t);return a},function(e){e.off=nt,e.on=et,e.wheelEventPixels=gi,e.Doc=To,e.splitLines=bt,e.countColumn=R,e.findColumn=U,e.isWordChar=ee,e.Pass=_,e.signal=rt,e.Line=Vt,e.changeEnd=wi,e.scrollbarModel=Pr,e.Pos=ge,e.cmpPos=me,e.modes=Ct,e.mimeModes=St,e.resolveMode=Lt,e.getMode=Tt,e.modeExtensions=Mt,e.extendMode=Nt,e.copyState=At,e.startState=Et,e.innerMode=Ot,e.commands=qo,e.keyMap=Ro,e.keyName=Go,e.isModifierKey=_o,e.lookupKey=$o,e.normalizeKeyMap=Bo,e.StringStream=Dt,e.SharedTextMarker=ko,e.TextMarker=xo,e.LineWidget=vo,e.e_preventDefault=st,e.e_stopPropagation=ut,e.e_stop=ft,e.addClass=W,e.contains=E,e.rmClass=T,e.keyNames=Ho}(Cl),Cl.version="5.39.2",Cl}()},KCx4:function(e,t,n){},Q29l:function(e,t){e.exports=function(e){function t(e){"undefined"!=typeof console&&(console.error||console.log)("[Script Loader]",e)}try{"undefined"!=typeof execScript&&"undefined"!=typeof attachEvent&&"undefined"==typeof addEventListener?execScript(e):"undefined"!=typeof eval?eval.call(null,e):t("EvalError: No eval function available")}catch(e){t(e)}}},VIyY:function(e,t,n){n("Q29l")(n("9/SQ"))},hiQM:function(e,t,n){!function(e){"use strict";var t="CodeMirror-lint-markers";function n(e){e.parentNode&&e.parentNode.removeChild(e)}function r(t,r,i){var o=function(t,n){var r=document.createElement("div");function i(t){if(!r.parentNode)return e.off(document,"mousemove",i);r.style.top=Math.max(0,t.clientY-r.offsetHeight-5)+"px",r.style.left=t.clientX+5+"px"}return r.className="CodeMirror-lint-tooltip",r.appendChild(n.cloneNode(!0)),document.body.appendChild(r),e.on(document,"mousemove",i),i(t),null!=r.style.opacity&&(r.style.opacity=1),r}(t,r);function l(){e.off(i,"mouseout",l),o&&(function(e){e.parentNode&&(null==e.style.opacity&&n(e),e.style.opacity=0,setTimeout(function(){n(e)},600))}(o),o=null)}var a=setInterval(function(){if(o)for(var e=i;;e=e.parentNode){if(e&&11==e.nodeType&&(e=e.host),e==document.body)return;if(!e){l();break}}if(!o)return clearInterval(a)},400);e.on(i,"mouseout",l)}function i(e,t,n){this.marked=[],this.options=t,this.timeout=null,this.hasGutter=n,this.onMouseOver=function(t){!function(e,t){var n=t.target||t.srcElement;if(/\bCodeMirror-lint-mark-/.test(n.className)){for(var i=n.getBoundingClientRect(),o=(i.left+i.right)/2,l=(i.top+i.bottom)/2,a=e.findMarksAt(e.coordsChar({left:o,top:l},"client")),u=[],c=0;c<a.length;++c){var f=a[c].__annotation;f&&u.push(f)}u.length&&function(e,t){for(var n=t.target||t.srcElement,i=document.createDocumentFragment(),o=0;o<e.length;o++){var l=e[o];i.appendChild(s(l))}r(t,i,n)}(u,t)}}(e,t)},this.waitingFor=0}function o(e){var n=e.state.lint;n.hasGutter&&e.clearGutter(t);for(var r=0;r<n.marked.length;++r)n.marked[r].clear();n.marked.length=0}function l(t,n,i,o){var l=document.createElement("div"),a=l;return l.className="CodeMirror-lint-marker-"+n,i&&((a=l.appendChild(document.createElement("div"))).className="CodeMirror-lint-marker-multiple"),0!=o&&e.on(a,"mouseover",function(e){r(e,t,a)}),l}function a(e,t){return"error"==e?e:t}function s(e){var t=e.severity;t||(t="error");var n=document.createElement("div");return n.className="CodeMirror-lint-message-"+t,void 0!==e.messageHTML?n.innerHTML=e.messageHTML:n.appendChild(document.createTextNode(e.message)),n}function u(t){var n=t.state.lint,r=n.options,i=r.options||r,o=r.getAnnotations||t.getHelper(e.Pos(0,0),"lint");if(o)if(r.async||o.async)!function(t,n,r){var i=t.state.lint,o=++i.waitingFor;function l(){o=-1,t.off("change",l)}t.on("change",l),n(t.getValue(),function(n,r){t.off("change",l),i.waitingFor==o&&(r&&n instanceof e&&(n=r),t.operation(function(){c(t,n)}))},r,t)}(t,o,i);else{var l=o(t.getValue(),i,t);if(!l)return;l.then?l.then(function(e){t.operation(function(){c(t,e)})}):t.operation(function(){c(t,l)})}}function c(e,n){o(e);for(var r=e.state.lint,i=r.options,u=function(e){for(var t=[],n=0;n<e.length;++n){var r=e[n],i=r.from.line;(t[i]||(t[i]=[])).push(r)}return t}(n),c=0;c<u.length;++c){var f=u[c];if(f){for(var h=null,d=r.hasGutter&&document.createDocumentFragment(),p=0;p<f.length;++p){var g=f[p],m=g.severity;m||(m="error"),h=a(h,m),i.formatAnnotation&&(g=i.formatAnnotation(g)),r.hasGutter&&d.appendChild(s(g)),g.to&&r.marked.push(e.markText(g.from,g.to,{className:"CodeMirror-lint-mark-"+m,__annotation:g}))}r.hasGutter&&e.setGutterMarker(c,t,l(d,h,f.length>1,r.options.tooltips))}}i.onUpdateLinting&&i.onUpdateLinting(n,u,e)}function f(e){var t=e.state.lint;t&&(clearTimeout(t.timeout),t.timeout=setTimeout(function(){u(e)},t.options.delay||500))}e.defineOption("lint",!1,function(n,r,l){if(l&&l!=e.Init&&(o(n),!1!==n.state.lint.options.lintOnChange&&n.off("change",f),e.off(n.getWrapperElement(),"mouseover",n.state.lint.onMouseOver),clearTimeout(n.state.lint.timeout),delete n.state.lint),r){for(var a=n.getOption("gutters"),s=!1,c=0;c<a.length;++c)a[c]==t&&(s=!0);var h=n.state.lint=new i(n,function(e,t){return t instanceof Function?{getAnnotations:t}:(t&&!0!==t||(t={}),t)}(0,r),s);!1!==h.options.lintOnChange&&n.on("change",f),0!=h.options.tooltips&&"gutter"!=h.options.tooltips&&e.on(n.getWrapperElement(),"mouseover",h.onMouseOver),u(n)}}),e.defineExtension("performLint",function(){this.state.lint&&u(this)})}(n("FzGJ"))},rLTS:function(e,t,n){},rseV:function(e,t,n){}}]);
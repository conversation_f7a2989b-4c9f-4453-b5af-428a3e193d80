(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-c81b"],{"+VRM":function(t,e,a){},"/f1G":function(t,e,a){t.exports={default:a("nhzr"),__esModule:!0}},"0yGE":function(t,e,a){},"2YvL":function(t,e,a){},"7Jb6":function(t,e,a){"use strict";var i=a("KC8t");a.n(i).a},"8xqd":function(t,e,a){},E8gZ:function(t,e,a){var i=a("jmDH"),s=a("w6GO"),r=a("NsO/"),l=a("NV0k").f;t.exports=function(t){return function(e){for(var a,n=r(e),o=s(n),c=o.length,_=0,u=[];c>_;)a=o[_++],i&&!l.call(n,a)||u.push(t?[a,n[a]]:n[a]);return u}}},GhRN:function(t,e,a){},Hnwm:function(t,e,a){},IGa7:function(t,e,a){"use strict";var i=a("2YvL");a.n(i).a},J3Fk:function(t,e,a){"use strict";a.r(e);var i=a("FyfS"),s=a.n(i),r=a("QbLZ"),l=a.n(r),n=a("14Xm"),o=a.n(n),c=a("/f1G"),_=a.n(c),u=a("D3Ub"),p=a.n(u),d=a("aS6R"),v=a("EJiy"),h=a.n(v),m=a("glbJ"),f={props:{couponDialogVisible:{type:Boolean,default:!1},selectCouponType:{type:String,default:""}},components:{},data:function(){return{search:{kwd:""},tableData:[],current_page:1,total:1,per_page:1}},computed:{},created:function(){},mounted:function(){this.getActivityList(1)},methods:{searchs:function(t){this.getActivityList(t)},select:function(t){t&&this.$emit("couponOptionData",{data:t,couponType:this.selectCouponType}),this.$emit("couponChangeVisible",!1)},getActivityList:function(t){var e=this;return p()(o.a.mark(function a(){var i;return o.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,$http.pc_post("plugin/CustomerIncrease/searchCoupon",l()({page:t},e.search));case 2:1==(i=a.sent).result?(e.tableData=i.data.data,e.total=i.data.total,e.per_page=i.data.per_page,e.current_page=i.data.current_page):e.$message.error(i.msg);case 4:case"end":return a.stop()}},a,e)}))()}}},y=(a("MqNE"),a("KHd+")),g=Object(y.a)(f,function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"group_coupon_dialog"},[a("el-dialog",{attrs:{center:"",title:"选择优惠券",visible:t.couponDialogVisible,"before-close":t.select,width:"50%"},on:{"update:visible":function(e){t.couponDialogVisible=e}}},[a("div",{staticStyle:{display:"flex",width:"95%"}},[a("el-input",{attrs:{placeholder:"请输入优惠券ID/名称"},model:{value:t.search.kwd,callback:function(e){t.$set(t.search,"kwd",e)},expression:"search.kwd"}}),t._v(" "),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(e){t.searchs(1)}}},[t._v("搜索")])],1),t._v(" "),a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData}},[a("el-table-column",{attrs:{align:"center",prop:"id",label:"ID"}}),t._v(" "),a("el-table-column",{attrs:{prop:"name",align:"center",label:"优惠券名称"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{plain:""},on:{click:function(a){t.select(e.row)}}},[t._v("选择")])]}}])})],1),t._v(" "),a("div",{staticClass:"vue-page"},[a("el-row",[a("el-col",{attrs:{align:"right"}},[a("el-pagination",{attrs:{layout:"prev, pager, next,jumper",total:t.total,"page-size":t.per_page,"current-page":t.current_page,background:""},on:{"current-change":t.searchs}})],1)],1)],1)],1)],1)},[],!1,null,"70941c2c",null);g.options.__file="group_coupon_dialog.vue";var b=g.exports,x={props:{selectMemberVisible:{type:Boolean,default:!1},selectCouponType:{type:String,default:""}},components:{},data:function(){return{search:{kwd:""},tableData:[],oneShow:!1}},computed:{},created:function(){},mounted:function(){this.getActivityList()},methods:{searchs:function(){this.getActivityList()},select:function(t){t&&this.$emit("selectMemberData",{data:t,couponType:this.selectCouponType}),this.$emit("selectMemberList",!1)},getActivityList:function(){var t=this;return p()(o.a.mark(function e(){var a;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,$http.pc_post("plugin/CustomerIncrease/searchStaff",l()({is_qy_wx:1,disabled:1},t.search));case 2:1==(a=e.sent).result?(t.tableData=a.data,t.oneShow||t.$emit("memberList",t.tableData),t.oneShow=!0):(t.$message.error(a.msg),t.oneShow=!0);case 4:case"end":return e.stop()}},e,t)}))()}}},w=(a("iYYW"),Object(y.a)(x,function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"group_coupon_dialog"},[a("el-dialog",{attrs:{center:"",title:"选择成员",visible:t.selectMemberVisible,"before-close":t.select,width:"50%"},on:{"update:visible":function(e){t.selectMemberVisible=e}}},[a("div",{staticStyle:{display:"flex",width:"95%"}},[a("el-input",{attrs:{placeholder:"企业微信员工姓名/手机号"},model:{value:t.search.kwd,callback:function(e){t.$set(t.search,"kwd",e)},expression:"search.kwd"}}),t._v(" "),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:t.searchs}},[t._v("搜索")])],1),t._v(" "),a("el-table",{staticStyle:{width:"100%",height:"500px","overflow-y":"auto"},attrs:{data:t.tableData}},[a("el-table-column",{attrs:{align:"center",prop:"id",label:"ID"}}),t._v(" "),a("el-table-column",{attrs:{prop:"name",align:"center",label:"成员名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"mobile",align:"center",label:"手机号码"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{plain:""},on:{click:function(a){t.select(e.row)}}},[t._v("选择")])]}}])})],1)],1)],1)},[],!1,null,"5e32f1be",null));w.options.__file="select_member_dialog.vue";var C=w.exports,k={components:{group_coupon_dialog:b,Tinymce:m.a,select_member_dialog:C},props:{poster_list:{type:Array,default:[]},member_level:{type:Array,default:[]},activity_basis_form:{type:Object,default:{}},type_name_arr:{type:Object,default:{}}},data:function(){return{search:{reward_type:1,level_ids:[],desc_text:"",poster_id:"",name:"",bg_color:"",start_time:"",end_time:"",auto_poster:0,step_rule:[],level_limit:1,staff_ids:[],auto_level_id:""},activeName:"1",couponDialogVisible:!1,selectCouponIndex:"",selectMemberVisible:!1,rules:{name:[{required:!0,message:"请输入活动名称",trigger:"blur"}]},tinymce_info:"",state_name:""}},mounted:function(){_()(this.activity_basis_form).length>0&&(this.$set(this.search,"reward_type",""==this.activity_basis_form.reward_type?this.activity_basis_form.reward_type:1),this.$set(this.search,"level_ids",this.activity_basis_form.level_ids?_()(this.activity_basis_form.level_ids):[]),this.$set(this.search,"desc_text",this.activity_basis_form.desc_text?this.activity_basis_form.desc_text:""),this.$set(this.search,"poster_id",this.activity_basis_form.poster_id?this.activity_basis_form.poster_id:""),this.$set(this.search,"name",this.activity_basis_form.name?this.activity_basis_form.name:""),this.$set(this.search,"bg_color",this.activity_basis_form.bg_color?this.activity_basis_form.bg_color:""),this.$set(this.search,"start_time",this.activity_basis_form.start_time?1e3*this.activity_basis_form.start_time:""),this.$set(this.search,"end_time",this.activity_basis_form.end_time&&-1!==this.activity_basis_form.end_time?1e3*this.activity_basis_form.end_time:""),this.$set(this.search,"auto_poster",this.activity_basis_form.auto_poster?this.activity_basis_form.auto_poster:0),this.$set(this.search,"level_limit",""!==this.activity_basis_form.level_limit?this.activity_basis_form.level_limit:1),this.$set(this.search,"staff_ids",this.activity_basis_form.staff_ids?this.activity_basis_form.staff_ids:[]),this.$set(this.search,"auto_level_id",null!==this.activity_basis_form.auto_level_id?this.activity_basis_form.auto_level_id:""),this.state_name=this.activity_basis_form.state_name?this.activity_basis_form.state_name:"",this.filterLevelIds()),this.tinymce_info=!0},methods:{filterLevelIds:function(){var t={},e=[],a=!0,i=!1,r=void 0;try{for(var l,n=s()(this.activity_basis_form.step_rule);!(a=(l=n.next()).done);a=!0){var o=l.value;"进行中"==this.state_name&&this.$set(t,"disabled",!0),this.$set(t,"step_people",o.step_people),this.$set(t,"id",""+o.id),this.$set(t,"level",o.step_level+"级阶梯"),this.$set(t,"step_reward",{}),this.$set(t.step_reward,"point",o.step_reward.point),this.$set(t.step_reward,"balance",o.step_reward.balance),this.$set(t.step_reward,"integral",o.step_reward.integral),this.$set(t.step_reward,"love",o.step_reward.love),this.$set(t.step_reward,"member_level",o.step_reward.member_level),this.$set(t.step_reward,"coupon",o.step_reward.coupon),e.push(t),t={}}}catch(t){i=!0,r=t}finally{try{!a&&n.return&&n.return()}finally{if(i)throw r}}var c=!0,u=!1,p=void 0;try{for(var d,v=s()(e);!(c=(d=v.next()).done);c=!0){var h=d.value;this.$set(h.step_reward.member_level,"level_id",h.step_reward.member_level.level_id)}}catch(t){u=!0,p=t}finally{try{!c&&v.return&&v.return()}finally{if(u)throw p}}this.search.step_rule=e,this.activeName=_()(this.search.step_rule).length>0?this.search.step_rule[0].id:""},delete1:function(t,e){if(e)this.$message.error("活动开始之前可以编辑删除,活动开始之后原来的阶梯人数不允许编辑删除,但可以增加");else{var a=this.search.step_rule.length>0?this.search.step_rule[t].level.split("级")[0]:"";0==a.indexOf("十")&&(a="一"+a),this.reorderData(a,t),this.search.step_rule.splice(t,1),this.activeName=this.search.step_rule.length>0?this.search.step_rule[0].id:""}},reorderData:function(t,e){this.search.step_rule.forEach(function(a,i){i>e&&(a.level=t+"级阶梯",t++)})},guid:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var e=16*Math.random()|0;return("x"==t?e:3&e|8).toString(16)})},addGradient:function(){var t=this.guid(),e=this.search.step_rule.length+1+"级阶梯";this.activeName=t,this.search.step_rule.push({level:e,step_people:"",id:t,step_reward:{point:"",balance:"",integral:"",love:"",member_level:{level_id:""},coupon:[]}})},tabsHandleClick:function(t,e){},getMemberList:function(t){var e=this;if(t=t,_()(this.activity_basis_form).length>0&&t.length>0){var a=t.filter(function(t){return-1!=e.search.staff_ids.indexOf(t.id)});console.log(a,"memberLists"),this.search.staff_ids=a}},openChoice:function(){this.selectMemberVisible=!0},selectMemberList:function(t){this.selectMemberVisible=t},getSelectMemberData:function(t){_()(t.data).length>0&&(0==this.search.staff_ids.filter(function(e){return e.id==t.data.id}).length&&this.search.staff_ids.push(t.data))},delSelectMembers:function(t){this.search.staff_ids.splice(t,1)},couponChangeVisible:function(t){this.couponDialogVisible=t},selectCoupon:function(t){this.selectCouponIndex=t,this.couponDialogVisible=!0},getCouponOption:function(t){_()(t.data).length>0&&(0==this.search.step_rule[this.selectCouponIndex].step_reward.coupon.filter(function(e){return e.id==t.data.id}).length&&this.search.step_rule[this.selectCouponIndex].step_reward.coupon.push({name:t.data.name,reward_num:"",id:t.data.id}))},delCoupom:function(t,e){this.search.step_rule[t].step_reward.coupon.splice(e,1)},limitMemberLevel:function(){this.search.level_ids=[]},changeMemberLevel:function(t){console.log(t,void 0===t?"undefined":h()(t)),t||(this.search.level_ids=[])}}},S=(a("7Jb6"),Object(y.a)(k,function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"all"},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"activity-basis"},[a("div",{staticClass:"vue-main-title"},[a("div",{staticClass:"vue-main-title-left"}),t._v(" "),a("div",{staticClass:"vue-main-title-content"},[t._v("基础设置")])]),t._v(" "),a("el-form",{staticClass:"text-form-inline",attrs:{"label-width":"140px",model:t.search,rules:t.rules}},[a("el-form-item",{attrs:{label:"活动名称",prop:"name"}},[a("el-input",{staticStyle:{width:"50%"},model:{value:t.search.name,callback:function(e){t.$set(t.search,"name",e)},expression:"search.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"主题色"}},[a("el-color-picker",{model:{value:t.search.bg_color,callback:function(e){t.$set(t.search,"bg_color",e)},expression:"search.bg_color"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"开始时间",required:""}},[a("el-date-picker",{staticStyle:{width:"35%"},attrs:{type:"datetime","value-format":"timestamp",placeholder:"选择起始时间"},model:{value:t.search.start_time,callback:function(e){t.$set(t.search,"start_time",e)},expression:"search.start_time"}}),t._v(" "),a("span",{staticStyle:{margin:"40px"}},[t._v("结束时间")]),t._v(" "),a("el-date-picker",{staticStyle:{width:"35%"},attrs:{type:"datetime","value-format":"timestamp",placeholder:"选择结束时间"},model:{value:t.search.end_time,callback:function(e){t.$set(t.search,"end_time",e)},expression:"search.end_time"}}),t._v(" "),a("div",{staticClass:"tip-explain"},[t._v("说明: 结束时间可不填，若不填活动详情页也不展示结束时间;不填则活动不会自动结束，需手动结束。")])],1),t._v(" "),a("el-form-item",{attrs:{label:"活动阶梯设置"}},[a("el-table",{staticStyle:{width:"50%"},attrs:{data:t.search.step_rule,"header-cell-style":{textAlign:"center"},"cell-style":{textAlign:"center"}}},[a("el-table-column",{attrs:{prop:"level",label:"阶梯等级"}}),t._v(" "),a("el-table-column",{attrs:{label:"助力人数"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{attrs:{disabled:e.row.disabled},model:{value:e.row.step_people,callback:function(a){t.$set(e.row,"step_people",a)},expression:"scope.row.step_people"}},[a("template",{slot:"append"},[t._v("人")])],2)]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("i",{staticClass:"el-icon-delete",on:{click:function(a){t.delete1(e.$index,e.row.disabled)}}})]}}])})],1),t._v(" "),a("div",{staticClass:"add-gradient"},[a("el-button",{staticStyle:{"margin-top":"20px"},attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:t.addGradient}},[t._v("新增梯度")]),t._v(" "),a("span",{staticClass:"tip"},[t._v("活动开始之前可以编辑删除,活动开始之后原来的阶梯人数不允许编辑删除,但可以增加")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"奖励发放"}},[a("el-radio",{attrs:{label:0},model:{value:t.search.reward_type,callback:function(e){t.$set(t.search,"reward_type",e)},expression:"search.reward_type"}},[t._v("达到每一级阶梯后发放")]),t._v(" "),a("el-radio",{attrs:{label:1},model:{value:t.search.reward_type,callback:function(e){t.$set(t.search,"reward_type",e)},expression:"search.reward_type"}},[t._v("活动结束后发放")])],1),t._v(" "),a("el-form-item",{attrs:{label:"奖励设置"}},[a("el-tabs",{on:{"tab-click":t.tabsHandleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},t._l(t.search.step_rule,function(e,i){return a("el-tab-pane",{key:e.id,attrs:{label:e.level,name:e.id}},[a("el-form-item",{staticStyle:{"margin-top":"20px"},attrs:{label:"积分"}},[a("el-input",{staticStyle:{width:"40%"},model:{value:e.step_reward.point,callback:function(a){t.$set(e.step_reward,"point",a)},expression:"item.step_reward.point"}},[a("template",{slot:"append"},[t._v(t._s(t.type_name_arr.point))])],2)],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"20px"},attrs:{label:"余额"}},[a("el-input",{staticStyle:{width:"40%"},model:{value:e.step_reward.balance,callback:function(a){t.$set(e.step_reward,"balance",a)},expression:"item.step_reward.balance"}},[a("template",{slot:"append"},[t._v(t._s(t.type_name_arr.balance))])],2)],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"20px"},attrs:{label:"消费积分"}},[a("el-input",{staticStyle:{width:"42.2%"},model:{value:e.step_reward.integral,callback:function(a){t.$set(e.step_reward,"integral",a)},expression:"item.step_reward.integral"}},[a("template",{slot:"append"},[t._v(t._s(t.type_name_arr.integral))])],2)],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"20px"},attrs:{label:"爱心值"}},[a("el-input",{staticStyle:{width:"41.2%"},model:{value:e.step_reward.love,callback:function(a){t.$set(e.step_reward,"love",a)},expression:"item.step_reward.love"}},[a("template",{slot:"append"},[t._v(t._s(t.type_name_arr.love))])],2)],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"20px"},attrs:{label:"升级到指定会员等级"}},[a("el-select",{staticStyle:{width:"40%"},attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:e.step_reward.member_level.level_id,callback:function(a){t.$set(e.step_reward.member_level,"level_id",a)},expression:"item.step_reward.member_level.level_id"}},t._l(t.member_level,function(t){return a("el-option",{key:t.id,attrs:{label:t.level_name,value:t.id}})}))],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"20px"},attrs:{label:"优惠券"}},t._l(e.step_reward.coupon,function(e,s){return a("div",{key:s,staticClass:"coupon-name-card",staticStyle:{display:"flex","margin-bottom":"10px"}},[a("span",{staticClass:"coupon-name"},[t._v("优惠券名称")]),t._v(" "),a("el-input",{staticStyle:{width:"30%"},attrs:{disabled:""},model:{value:e.name,callback:function(a){t.$set(e,"name",a)},expression:"el.name"}},[a("template",{slot:"append"},[t._v("赠送")])],2),t._v(" "),a("el-input",{staticStyle:{width:"30%"},model:{value:e.reward_num,callback:function(a){t.$set(e,"reward_num",a)},expression:"el.reward_num"}},[a("template",{slot:"append"},[t._v("张")])],2),t._v(" "),a("span",{staticClass:"coupon-name-del",on:{click:function(e){t.delCoupom(i,s)}}},[t._v("x")])],1)})),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"20px"},attrs:{label:" "}},[a("el-button",{attrs:{plain:""},on:{click:function(e){t.selectCoupon(i)}}},[t._v("选择优惠券")])],1)],1)}))],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"20px"},attrs:{label:"新会员自动升级指定会员等级","label-width":"200px"}},[a("el-select",{staticStyle:{width:"40%"},attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:t.search.auto_level_id,callback:function(e){t.$set(t.search,"auto_level_id",e)},expression:"search.auto_level_id"}},t._l(t.member_level,function(t){return a("el-option",{key:t.id,attrs:{label:t.level_name,value:t.id}})}))],1),t._v(" "),a("el-form-item",{attrs:{label:"是否限制会员等级"}},[a("el-radio-group",{on:{change:t.changeMemberLevel},model:{value:t.search.level_limit,callback:function(e){t.$set(t.search,"level_limit",e)},expression:"search.level_limit"}},[a("el-radio",{attrs:{label:1}},[t._v("限制会员等级")]),t._v(" "),a("el-radio",{attrs:{label:0}},[t._v("不限制会员等级")])],1)],1),t._v(" "),t.search.level_limit?a("el-form-item",{staticStyle:{"margin-top":"20px"},attrs:{label:"活动限制"}},[a("el-checkbox-group",{model:{value:t.search.level_ids,callback:function(e){t.$set(t.search,"level_ids",e)},expression:"search.level_ids"}},t._l(t.member_level,function(e,i){return a("el-checkbox",{key:i,attrs:{label:e.id}},[t._v(t._s(e.level_name))])})),t._v(" "),a("div",{staticClass:"tip"},[t._v("指定会员等级才能生成推广海报")])],1):t._e(),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"20px"},attrs:{label:"活动介绍"}},[t.tinymce_info?a("tinymce",{model:{value:t.search.desc_text,callback:function(e){t.$set(t.search,"desc_text",e)},expression:"search.desc_text"}}):t._e()],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"20px"},attrs:{label:"分享海报"}},[a("el-select",{attrs:{placeholder:"选择海报",clearable:"",filterable:""},model:{value:t.search.poster_id,callback:function(e){t.$set(t.search,"poster_id",e)},expression:"search.poster_id"}},t._l(t.poster_list,function(t){return a("el-option",{key:t.id,attrs:{label:t.title,value:t.id}})}))],1),t._v(" "),a("el-form-item",{attrs:{label:"自动生成分享海报"}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:t.search.auto_poster,callback:function(e){t.$set(t.search,"auto_poster",e)},expression:"search.auto_poster"}}),t._v(" "),a("div",{staticClass:"tip"},[t._v("开启后活动页面会员不能手动选择生成的企业成员二维码，按照自动生成顺序，添加好友自动回复海报按添加哪个成员就回复哪个成员的。")]),t._v(" "),a("div",{staticClass:"tip"},[t._v("自动生成顺序：①该用户添加过的企业成员，按最早添加的时间顺序；②会员上线中，最近的一个企业成员；③随机一个。")])],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"20px"},attrs:{label:"裂变成员",required:""}},[a("el-button",{attrs:{type:"primary",plain:""},on:{click:t.openChoice}},[t._v("选择成员")]),t._v(" "),a("div",{staticClass:"choice-member"},t._l(t.search.staff_ids,function(e,i){return a("span",[a("span",{staticClass:"row_card"},[a("i",{staticClass:"iconfont icon-fontclass-rengezhongxin",staticStyle:{color:"#29ba9c"}}),t._v("\n                "+t._s(e.name)+"\n                "),a("i",{staticClass:"el-icon-close",on:{click:function(a){t.delSelectMembers(i,e)}}})])])}))],1)],1)],1)]),t._v(" "),a("group_coupon_dialog",{attrs:{couponDialogVisible:t.couponDialogVisible},on:{couponOptionData:t.getCouponOption,couponChangeVisible:t.couponChangeVisible}}),t._v(" "),a("select_member_dialog",{attrs:{selectMemberVisible:t.selectMemberVisible},on:{memberList:t.getMemberList,selectMemberData:t.getSelectMemberData,selectMemberList:t.selectMemberList}})],1)},[],!1,null,"a62401ba",null));S.options.__file="activity_basis.vue";var $=S.exports,L={props:["active_copywriting_form"],data:function(){return{ruleCopyPos:"",ruleCopyList:["会员昵称","活动名称"],pushCopyPos:"",pushCopyList:["会员昵称","助力会员昵称","活动名称"],noPushCopyPos:"",noPushCopyList:["会员昵称","活动名称"],repeatCopyPos:"",repeatCopyList:["会员昵称","活动名称"]}},mounted:function(){this.active_copywriting_form.common_reply_text&&(document.getElementById("ruleCopyPos").innerHTML=this.active_copywriting_form.common_reply_text),this.active_copywriting_form.success_reply_text&&(document.getElementById("pushCopyPos").innerHTML=this.active_copywriting_form.success_reply_text),this.active_copywriting_form.disabled_reply_text&&(document.getElementById("noPushCopyPos").innerHTML=this.active_copywriting_form.disabled_reply_text),this.active_copywriting_form.repeat_reply_text&&(document.getElementById("repeatCopyPos").innerHTML=this.active_copywriting_form.repeat_reply_text),this.$emit("copywritingList",this.escapeWrap())},methods:{escapeWrap:function(){return{common_reply_text:document.getElementById("ruleCopyPos").innerHTML,success_reply_text:document.getElementById("pushCopyPos").innerHTML,disabled_reply_text:document.getElementById("noPushCopyPos").innerHTML,repeat_reply_text:document.getElementById("repeatCopyPos").innerHTML}},getPos:function(t){"ruleCopyPos"==t&&(this.ruleCopyPos=window.getSelection().getRangeAt(0)),"pushCopyPos"==t&&(this.pushCopyPos=window.getSelection().getRangeAt(0)),"noPushCopyPos"==t&&(this.noPushCopyPos=window.getSelection().getRangeAt(0)),"repeatCopyPos"==t&&(this.repeatCopyPos=window.getSelection().getRangeAt(0))},addCustomerNickname:function(t,e){var a=document.createElement("span"),i=document.getElementById(e);a.innerHTML="["+t+"]",a.setAttribute("contenteditable","false"),i.appendChild(a);var s="";switch(e){case"ruleCopyPos":s=this.ruleCopyPos;break;case"pushCopyPos":s=this.pushCopyPos;break;case"noPushCopyPos":s=this.noPushCopyPos;break;case"repeatCopyPos":s=this.repeatCopyPos}if(""===s){var r=document.getElementById(e);r.focus();var l=window.getSelection();l.selectAllChildren(r),l.collapseToEnd(),s=window.getSelection().getRangeAt(0)}s.insertNode(a),this.blurChangeText()},blurChangeText:function(t){this.$emit("copywritingList",this.escapeWrap())}}},P=(a("pPnb"),Object(y.a)(L,function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"all"},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"copywriting-settings"},[a("div",{staticClass:"vue-main-title"},[a("div",{staticClass:"vue-main-title-left"}),t._v(" "),a("div",{staticClass:"vue-main-title-content"},[t._v("活动文案设置")])]),t._v(" "),a("el-form",{staticClass:"text-form-inline",attrs:{"label-width":"180px"}},[a("el-form-item",{attrs:{label:"裂变规则文案"}},[a("span",{staticClass:"fission-title"},[t._v("当用户添加企业微信成员时,企业微信自动推送此文案")]),t._v(" "),a("div",{staticStyle:{"padding-left":"10px"},attrs:{id:"ruleCopyPos",contenteditable:"true",spellcheck:"true","data-medium-editor-element":"true",role:"textbox","aria-multiline":"true"},on:{keyup:t.blurChangeText,blur:function(e){t.getPos("ruleCopyPos")}}},[a("div",[t._v("你好,[会员昵称],很高兴认识你,快来参与本次活动吧!")]),a("div",[t._v("分享下方海报,邀请好友扫码助力活动获得奖励.")]),a("div",[t._v("快去邀请好友吧~")]),a("div",[t._v("戳下方链接查看助力详情 ↓ ↓")])]),t._v(" "),a("div",{staticClass:"fission-name"},t._l(t.ruleCopyList,function(e,i){return a("el-button",{key:i,attrs:{plain:"",type:"primary"},on:{click:function(a){t.addCustomerNickname(e,"ruleCopyPos")},keyup:t.blurChangeText}},[t._v(t._s(e))])}))]),t._v(" "),a("el-form-item",{attrs:{label:"成功助力推送文案"}},[a("span",{staticClass:"fission-title"},[t._v("当B用户成功助力A用户时,企业微信自动推送此文案")]),t._v(" "),a("div",{staticStyle:{"padding-left":"10px"},attrs:{id:"pushCopyPos",contenteditable:"true",spellcheck:"true","data-medium-editor-element":"true",role:"textbox","aria-multiline":"true"},on:{keyup:t.blurChangeText,blur:function(e){t.getPos("pushCopyPos")}}},[a("div",[t._v("你好,[会员昵称],很高兴认识你,你已为好友助力成功")]),a("div",[t._v("分享下方海报,邀请好友扫码助力活动获得奖励.")]),a("div",[t._v("快去邀请好友吧~")]),a("div",[t._v("戳下方链接查看助力详情 ↓ ↓")])]),t._v(" "),a("div",{staticClass:"fission-name"},t._l(t.pushCopyList,function(e,i){return a("el-button",{key:i,attrs:{plain:"",type:"primary"},on:{click:function(a){t.addCustomerNickname(e,"pushCopyPos")},keyup:t.blurChangeText}},[t._v(t._s(e))])}))]),t._v(" "),a("el-form-item",{attrs:{label:"不满足参与活动推送文案"}},[a("span",{staticClass:"fission-title"},[t._v("当用户不满足参与活动时,企业微信自动推送此文案")]),t._v(" "),a("div",{staticStyle:{"padding-left":"10px"},attrs:{id:"noPushCopyPos",contenteditable:"true",spellcheck:"true","data-medium-editor-element":"true",role:"textbox","aria-multiline":"true"},on:{keyup:t.blurChangeText,blur:function(e){t.getPos("noPushCopyPos")}}},[a("div",[t._v("你好,[会员昵称],很高兴认识你!")]),a("div",[t._v("邀请你参与[活动名称]")]),a("div",[t._v("戳下方链接查看助力详情 ↓ ↓")])]),t._v(" "),a("div",{staticClass:"fission-name"},t._l(t.noPushCopyList,function(e,i){return a("el-button",{key:i,attrs:{plain:"",type:"primary"},on:{click:function(a){t.addCustomerNickname(e,"noPushCopyPos")},keyup:t.blurChangeText}},[t._v(t._s(e))])}))]),t._v(" "),a("el-form-item",{attrs:{label:"重复助力推送文案"}},[a("span",{staticClass:"fission-title"},[t._v("当用户多次助力好友时，企业微信自动推送此文案")]),t._v(" "),a("div",{staticStyle:{"padding-left":"10px"},attrs:{id:"repeatCopyPos",contenteditable:"true",spellcheck:"true","data-medium-editor-element":"true",role:"textbox","aria-multiline":"true"},on:{keyup:t.blurChangeText,blur:function(e){t.getPos("repeatCopyPos")}}},[a("div",[t._v("你好,[会员昵称],你已经为好友助力时，不能再重复助力哟~")]),a("div",[t._v("邀请你参与[活动名称]")]),a("div",[t._v("戳下方链接查看助力详情 ↓ ↓")])]),t._v(" "),a("div",{staticClass:"fission-name"},t._l(t.repeatCopyList,function(e,i){return a("el-button",{key:i,attrs:{plain:"",type:"primary"},on:{click:function(a){t.addCustomerNickname(e,"repeatCopyPos")},keyup:t.blurChangeText}},[t._v(t._s(e))])}))])],1)],1)])],1)])},[],!1,null,"19867462",null));P.options.__file="active_copywriting_settings.vue";var D=P.exports,T={props:{couponDialogVisible:{type:Boolean,default:!1},selectCouponType:{type:String,default:""}},components:{},data:function(){return{search:{kwd:""},tableData:[],oneShow:!1}},computed:{},created:function(){},mounted:function(){this.getTagList()},methods:{searchs:function(t){this.getTagList()},select:function(t){t&&this.$emit("couponOptionData",{data:t,couponType:this.selectCouponType}),this.$emit("couponChangeVisible",!1)},getTagList:function(){var t=this;return p()(o.a.mark(function e(){var a;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,$http.pc_post("plugin/CustomerIncrease/searchTag",l()({},t.search));case 2:1==(a=e.sent).result?(t.tableData=a.data,t.oneShow||t.$emit("tagDataList",t.tableData),t.oneShow=!0):(t.$message.error(a.msg),t.oneShow=!0);case 4:case"end":return e.stop()}},e,t)}))()}}},M=(a("t6P7"),Object(y.a)(T,function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"tag-dialog"}},[a("el-dialog",{attrs:{center:"",title:"选择标签",visible:t.couponDialogVisible,"before-close":t.select,width:"50%"},on:{"update:visible":function(e){t.couponDialogVisible=e}}},[a("div",{staticStyle:{display:"flex",width:"95%"}},[a("el-input",{attrs:{placeholder:"请输入标签名或者标签组名"},model:{value:t.search.kwd,callback:function(e){t.$set(t.search,"kwd",e)},expression:"search.kwd"}}),t._v(" "),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(e){t.searchs(1)}}},[t._v("搜索")])],1),t._v(" "),a("el-table",{staticStyle:{width:"100%",height:"500px","overflow-y":"auto"},attrs:{data:t.tableData}},[a("el-table-column",{attrs:{align:"center",prop:"id",label:"ID"}}),t._v(" "),a("el-table-column",{attrs:{prop:"name",align:"center",label:"标签名称"}}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{plain:""},on:{click:function(a){t.select(e.row)}}},[t._v("选择")])]}}])})],1)],1)],1)},[],!1,null,"7365bd60",null));M.options.__file="tag_dialog.vue";var V={components:{tag_dialog:M.exports},props:{advanced_setting:{type:Object,default:{new_state:"",fail_state:"",auto_tag:"",tag_ids:[]}}},data:function(){return{search:{new_state:"",fail_state:"",auto_tag:"",tag_ids:[]},tag_info:"",couponDialogVisible:!1}},created:function(){_()(this.advanced_setting).length>0&&(this.$set(this.search,"new_state",this.advanced_setting.new_state),this.$set(this.search,"fail_state",this.advanced_setting.fail_state),this.$set(this.search,"auto_tag",this.advanced_setting.auto_tag),this.$set(this.search,"tag_ids",this.advanced_setting.tag_ids))},mounted:function(){},methods:{changeSwitch:function(t){0==t&&(this.search.tag_ids=[])},getTagDataList:function(t){var e=this;this.$nextTick(function(){if(void 0!==e.advanced_setting.tag_ids&&_()(e.advanced_setting.tag_ids).length>0){var a=t.filter(function(t){return-1!=e.advanced_setting.tag_ids.indexOf(t.tag_id)});e.search.tag_ids=a}e.tag_info=!0})},getCouponOption:function(t){_()(t.data).length>0&&(0==this.search.tag_ids.filter(function(e){return e.id==t.data.id}).length&&this.search.tag_ids.push(t.data))},delTagList:function(t){this.search.tag_ids.splice(t,1)},couponChangeVisible:function(t){this.couponDialogVisible=t},selectTagDialog:function(){this.couponDialogVisible=!0}}},I=(a("IGa7"),Object(y.a)(V,function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"all"},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"copywriting-settings"},[a("div",{staticClass:"vue-main-title"},[a("div",{staticClass:"vue-main-title-left"}),t._v(" "),a("div",{staticClass:"vue-main-title-content"},[t._v("高级设置")])]),t._v(" "),a("el-form",{staticClass:"text-form-inline",attrs:{"label-width":"200px"}},[a("el-form-item",{attrs:{label:"新添加用户才能给好友助力"}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:t.search.new_state,callback:function(e){t.$set(t.search,"new_state",e)},expression:"search.new_state"}}),t._v(" "),a("div",{staticClass:"tip"},[t._v("开启后已是企业微信好友的用户无法给好友助力，但可以正常生成推广海报参与活动")])],1),t._v(" "),a("el-form-item",{attrs:{label:"助力失效"}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:t.search.fail_state,callback:function(e){t.$set(t.search,"fail_state",e)},expression:"search.fail_state"}}),t._v(" "),a("div",{staticClass:"tip"},[t._v("开启后活动期间删除企业微信好友的,助力失败")])],1),t._v(" "),t.tag_info?a("el-form-item",{attrs:{label:"自动打标签"}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:t.changeSwitch},model:{value:t.search.auto_tag,callback:function(e){t.$set(t.search,"auto_tag",e)},expression:"search.auto_tag"}}),t._v(" "),a("div",{staticClass:"tip"},[t._v("开启自动打标签需要先开启企业微信标签插件并且开启微信自动同步，不开启则不能自动打标签")]),t._v(" "),t.search.auto_tag?a("div",{staticClass:"tag-item"},[a("span",{staticClass:"tag-btn",on:{click:t.selectTagDialog}},[t._v("添加标签")]),t._v(" "),a("div",{staticClass:"tag-content"},t._l(t.search.tag_ids,function(e,i){return a("span",{key:i,staticClass:"tag-line"},[t._v(t._s(e.name)),a("i",{staticClass:"el-icon-close",on:{click:function(e){t.delTagList(i)}}})])}))]):t._e()],1):t._e()],1)],1)]),t._v(" "),a("tag_dialog",{attrs:{couponDialogVisible:t.couponDialogVisible},on:{tagDataList:t.getTagDataList,couponOptionData:t.getCouponOption,couponChangeVisible:t.couponChangeVisible}})],1)])},[],!1,null,"66147ac0",null));I.options.__file="advanced_setting.vue";var E=I.exports,O={components:{uploadImg:a("ErL5").a},props:["share_form"],data:function(){return{search:{share_title:"",share_text:"",share_logo:""},share_logo_url:"",uploadShow:!1}},mounted:function(){_()(this.share_form).length>0&&(this.$set(this.search,"share_title",this.share_form.share_title),this.$set(this.search,"share_text",this.share_form.share_text),this.$set(this.search,"share_logo",this.share_form.share_logo),this.share_logo_url=this.share_form.share_logo_url)},methods:{openUpload:function(){this.uploadShow=!0},sureImg:function(t,e){this.search.share_logo=e,this.share_logo_url=t},delUploadImg:function(){this.search.share_logo="",this.share_logo_url=""}}},A=(a("iW7U"),Object(y.a)(O,function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"all"},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"sharing-settings"},[a("div",{staticClass:"vue-main-title"},[a("div",{staticClass:"vue-main-title-left"}),t._v(" "),a("div",{staticClass:"vue-main-title-content"},[t._v("分享设置")])]),t._v(" "),a("el-form",{staticClass:"text-form-inline",attrs:{"label-width":"200px"}},[a("el-form-item",{attrs:{label:"分享标题"}},[a("el-input",{staticStyle:{width:"40%"},model:{value:t.search.share_title,callback:function(e){t.$set(t.search,"share_title",e)},expression:"search.share_title"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"分享描述"}},[a("div",{staticClass:"sharing_description"},[a("el-input",{staticStyle:{width:"40%"},attrs:{type:"textarea"},model:{value:t.search.share_text,callback:function(e){t.$set(t.search,"share_text",e)},expression:"search.share_text"}})],1)]),t._v(" "),a("el-form-item",{attrs:{label:"分享图标"}},[t.search.share_logo?t._e():a("div",{staticClass:"upload-box",on:{click:t.openUpload}},[a("div",{staticClass:"upload-box-member"},[a("i",{staticClass:"el-icon-plus",staticStyle:{"font-size":"32px"}})])]),t._v(" "),t.search.share_logo?a("div",{staticClass:"upload-boxed upload-boxed-item"},[a("img",{staticStyle:{width:"150px",height:"150px","border-radius":"5px",cursor:"pointer"},attrs:{src:t.share_logo_url,alt:""},on:{click:t.openUpload}}),t._v(" "),a("div",{staticClass:"upload-boxed-text"},[t._v("重新选择")]),t._v(" "),a("i",{staticClass:"el-icon-error",on:{click:t.delUploadImg}})]):t._e()])],1)],1)]),t._v(" "),a("upload-img",{attrs:{centerDialogVisible:t.uploadShow},on:{changeVisible:function(e){t.uploadShow=!1},sureImg:t.sureImg}})],1)])},[],!1,null,"bac4b21c",null));A.options.__file="sharing_settings.vue";var N=A.exports,j={components:{friend_fusion_side:d.a,activity_basis:$,active_copywriting_settings:D,advanced_setting:E,sharing_settings:N},data:function(){return{activityItemList:[{id:1,name:"活动设置"},{id:2,name:"活动文案设置"},{id:3,name:"高级设置"},{id:4,name:"分享设置"}],activityStatus:1,activeCopywritingData:{},form:{},member_level:[],poster_list:[],activity_basis_form:{},activity_basis_info:"",share_form:{},share_info:"",advanced_setting:{},advanced_info:"",active_copywriting_form:{},active_copywriting_info:"",id:"",submit_info:!1,type_name_arr:{}}},mounted:function(){this.id=this.$route.query.id?this.$route.query.id:"",this.getActivityDetail(this.id)},methods:{btnActivityStatus:function(t){this.activityStatus=t},getActivityDetail:function(t){var e=this;return p()(o.a.mark(function a(){var i;return o.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,$http.pc_post("plugin/CustomerIncrease/activityDetail",{id:t||""});case 2:1==(i=a.sent).result?(e.member_level=i.data.member_level,e.poster_list=i.data.poster_list,e.share_form.share_title=i.data.activity.share_title?i.data.activity.share_title:"",e.share_form.share_text=i.data.activity.share_text?i.data.activity.share_text:"",e.share_form.share_logo=i.data.activity.share_logo?i.data.activity.share_logo:"",e.share_form.share_logo_url=i.data.activity.share_logo_url?i.data.activity.share_logo_url:"",e.advanced_setting.new_state=i.data.activity.new_state?i.data.activity.new_state:"",e.advanced_setting.fail_state=i.data.activity.fail_state?i.data.activity.fail_state:"",e.advanced_setting.auto_tag=i.data.activity.auto_tag?i.data.activity.auto_tag:"",e.advanced_setting.tag_ids=i.data.activity.tag_ids?i.data.activity.tag_ids:[],e.active_copywriting_form.common_reply_text=i.data.activity.common_reply_text?i.data.activity.common_reply_text:"",e.active_copywriting_form.disabled_reply_text=i.data.activity.disabled_reply_text?i.data.activity.disabled_reply_text:"",e.active_copywriting_form.success_reply_text=i.data.activity.success_reply_text?i.data.activity.success_reply_text:"",e.active_copywriting_form.repeat_reply_text=i.data.activity.repeat_reply_text?i.data.activity.repeat_reply_text:"",null!==i.data.type_name_arr&&(_()(i.data.type_name_arr).length>0?e.type_name_arr=i.data.type_name_arr:(e.type_name_arr.balance="",e.type_name_arr.coupon="",e.type_name_arr.integral="",e.type_name_arr.love="",e.type_name_arr.member_level="",e.type_name_arr.point="")),e.activity_basis_form=i.data.activity,e.share_info=!0,e.advanced_info=!0,e.active_copywriting_info=!0,e.activity_basis_info=!0):(e.$message.error(i.msg),e.share_info=!0,e.advanced_info=!0);case 4:case"end":return a.stop()}},a,e)}))()},submit:function(){var t=this;return p()(o.a.mark(function e(){var a;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.submit_info){e.next=2;break}return e.abrupt("return");case 2:return t.submit_info=!0,t.form=l()({},t.escapeWrap(),t.$refs.activity_basis.search,t.$refs.advanced_setting.search,t.$refs.sharing_settings.search),e.next=6,$http.pc_post("plugin/CustomerIncrease/activityAdd",l()({id:t.id},t.filterSubmitData(t.form)));case 6:1==(a=e.sent).result?(t.$message.success(a.msg),t.$router.push(t.fun.getUrl("friendFusionActivity",{},{})),t.submit_info=!1):(t.$message.error(a.msg),t.submit_info=!1);case 8:case"end":return e.stop()}},e,t)}))()},filterSubmitData:function(t){var e=[],a=!0,i=!1,r=void 0;try{for(var l,n=s()(t.tag_ids);!(a=(l=n.next()).done);a=!0){var o=l.value;e.push(o.tag_id)}}catch(t){i=!0,r=t}finally{try{!a&&n.return&&n.return()}finally{if(i)throw r}}t.tag_ids=e;var c=!0,_=!1,u=void 0;try{for(var p,d=s()(t.step_rule);!(c=(p=d.next()).done);c=!0){var v=p.value;delete v.id,delete v.level}}catch(t){_=!0,u=t}finally{try{!c&&d.return&&d.return()}finally{if(_)throw u}}var h=[],m=!0,f=!1,y=void 0;try{for(var g,b=s()(t.staff_ids);!(m=(g=b.next()).done);m=!0){var x=g.value;h.push(x.id)}}catch(t){f=!0,y=t}finally{try{!m&&b.return&&b.return()}finally{if(f)throw y}}return t.staff_ids=h,t.start_time=t.start_time/1e3,t.end_time=t.end_time/1e3,t},escapeWrap:function(){var t=this.activeCopywritingData,e=t.common_reply_text,a=t.success_reply_text,i=t.disabled_reply_text,s=t.repeat_reply_text;return{common_reply_text:this.deleteRedundantLabels(e),success_reply_text:this.deleteRedundantLabels(a),disabled_reply_text:this.deleteRedundantLabels(i),repeat_reply_text:this.deleteRedundantLabels(s)}},deleteRedundantLabels:function(t){return t=t.replace(/&nbsp;/g," ")},getCopywritingList:function(t){this.activeCopywritingData=t}}},H=(a("Z3hj"),Object(y.a)(j,function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"all activity_editor"},[a("friend_fusion_side",{attrs:{defaultActive:"friendFusionActivity"}}),t._v(" "),a("div",{staticStyle:{width:"calc(100% - 150px)"},attrs:{id:"app"}},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"activity-editor"},[a("div",{staticClass:"vue-main-title"},t._l(t.activityItemList,function(e,i){return a("span",{key:i,class:t.activityStatus==e.id?"activity-item inactivity-item":"activity-item",on:{click:function(a){t.btnActivityStatus(e.id)}}},[t._v(t._s(e.name))])}))]),t._v(" "),this.activity_basis_info?a("div",[a("activity_basis",{directives:[{name:"show",rawName:"v-show",value:1==t.activityStatus,expression:"activityStatus == 1"}],ref:"activity_basis",attrs:{type_name_arr:t.type_name_arr,activity_basis_form:t.activity_basis_form,member_level:t.member_level,poster_list:t.poster_list}})],1):t._e(),t._v(" "),t.active_copywriting_info?a("div",[a("active_copywriting_settings",{directives:[{name:"show",rawName:"v-show",value:2==t.activityStatus,expression:"activityStatus == 2"}],ref:"active_copywriting_settings",attrs:{active_copywriting_form:t.active_copywriting_form},on:{copywritingList:t.getCopywritingList}})],1):t._e(),t._v(" "),t.advanced_info?a("div",[a("advanced_setting",{directives:[{name:"show",rawName:"v-show",value:3==t.activityStatus,expression:"activityStatus == 3"}],ref:"advanced_setting",attrs:{advanced_setting:t.advanced_setting}})],1):t._e(),t._v(" "),t.share_info?a("div",[a("sharing_settings",{directives:[{name:"show",rawName:"v-show",value:4==t.activityStatus,expression:"activityStatus == 4"}],ref:"sharing_settings",attrs:{share_form:t.share_form}})],1):t._e()])],1)],1),t._v(" "),a("div",{staticClass:"vue-page"},[a("el-row",[a("el-col",{attrs:{align:"center"}},[a("el-button",{attrs:{type:"primary"},on:{click:t.submit}},[t._v("提交")])],1)],1)],1)])},[],!1,null,"13f7a882",null));H.options.__file="activity_editor.vue";e.default=H.exports},KC8t:function(t,e,a){},MqNE:function(t,e,a){"use strict";var i=a("GhRN");a.n(i).a},OYyY:function(t,e,a){},Z3hj:function(t,e,a){"use strict";var i=a("8xqd");a.n(i).a},fW1p:function(t,e,a){var i=a("Y7ZC"),s=a("E8gZ")(!1);i(i.S,"Object",{values:function(t){return s(t)}})},iW7U:function(t,e,a){"use strict";var i=a("Hnwm");a.n(i).a},iYYW:function(t,e,a){"use strict";var i=a("OYyY");a.n(i).a},nhzr:function(t,e,a){a("fW1p"),t.exports=a("WEpk").Object.values},pPnb:function(t,e,a){"use strict";var i=a("+VRM");a.n(i).a},t6P7:function(t,e,a){"use strict";var i=a("0yGE");a.n(i).a}}]);
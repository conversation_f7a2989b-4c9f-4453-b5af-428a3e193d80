(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-7cc9"],{"4L6C":function(t,e,a){},G2Fa:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIUAAACUCAYAAAC9UOoCAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAACkxJREFUeNrsnXtu1EgQxj3D8A4ivAXikVUQEv+xJwBuAAeICCdYcQLYE+zuCZJVDrC7J9jdGyAhgYRACQ+BIECCeAYEWn/WFOsMbbe73W23298nWYOG8cTj/rmqurq6e5BYamlpaSZ9uZQe59PjXHrMJJSJFufm5q6GeGEDCxjm05efxiBQEYIxMIABVuEXWoT4wRhUgGE6fVkYuwqqB2AMNEDARfydHtNst/6AMSAQBKMSFASi32AMCATBmNRQ8d4CgWhd8+nDuRAEFOmF3GD+gWAMckAg/7Bs8yVbtmxJdu3alezYsSN77as+fvyYPHv2rPOuZJT793UbGI4cOZJMTU1l/6a8WYykSTAGYyuBGGLN5ERYhOPHjxOGnN6/f588ePCg88GnxBTzJift3bs3OXXqFIGINMYQKK5UPQFxA1wGVU+WD1QjYAzHrqNyjwNA0ELU18mTJ4MFY2gCBOKIPvcuXAoWN1QwAMWFqh/ev38/W7MHYAxNexxU/GAYQcFYoh9gDGklCEYtS0H1AwxCQTAIBcEgFASDUBAMF2AQCoJBKAgGoSAYFmAQipb05cuXYMEgFC1pY2MjWItBKFrS58+fg3UlhMKhtm7dWvmzb968CTbGIBSOoajaSICijrXwCQahcCyT0eTV1dUgeyWEokUoXr9+nR2hgUEoHGvPnj1Gn8eMMswXCQkMQuEhrjABA/kKTCB69epVMGAQCg+yKXCGxbh3717mTmwSWy7BGLEJ/cQVsBam3U70Rp48efLtO9CwaGTba7Ds9s4TCk/CpCnECrZPvcQZdfMZNqL78BhbdHV6JaHwKEzExkEoqE06duxY58AgFASDULQJxqFDhwgFtVkHDx7MFnsxGU0lFD0Q8genT5/OrEaoc3OZp2jRauzbty95+/ZtluLGynqEgsoshXRbkc1EwgpwCCB4r27NBaHosBBn+M5rYOCtyogsYwqKUFCEgiIUFKGgCAXFLqk3bdu2LZmens6qo5ArkJpKFMSgy/bp06esuGV9fb1WaRyh6IAOHDiQHUWFtXlA8DkIYDx//ryVCihC4VGyDYVp+T0Ei4Lj5cuXyaNHj3phOUZ9sA4nTpyoPfgkFub+/ftO5mkw0GwRiJmZGWejkYhFzpw5E/1Cs6PYgSgS3ABcAmIFCTDR2Gh4WAS4DBVMeA9g3L17N1qLESUUaFi4jCIYMLcCweOkJJgELGh8VEsdPnxYCQaKZe7cuUP30RUVuQw82bdv31YCoYIHgSUsgiq4hFUBNISiA4LpV/UyAAQaGDkIE8F6FIEBKxLjzgbRQaEy92hQ9BrqzNZaWVlRupEY91OLCgo0EgLEScFdmFqISSGJpUpgSZKLUAQqFRCwDq52FX769KkyqMVBKAKVKn/gcvwClkJlcWwypYSiIe3cuVMZD7iUyoVs376dUHRJHz58cPp9dWMTQkERCopQBCdVQOk6CJyamiIUXY8fXE6uyRfh6IJPQhGIVI0ji5K5kCoP4qOHQygcQ6FyIUePHnViJVQjrzHWcUYXaKpGQGEpVGMiJiqq3sIwO2OKwIWUturJRaPajlNgKF51LtwGLAWh6EAPpKheAo1rUgMByzA7O1sI0+PHj6PsfURZeYXKKnQdVQEm4gs0Mj5TFA9ggAuLipTVS8Rc9h9tjSbqJ4qKbNHoUr8pg1w48D7GT6oU5sYYS0TrPvJupEpxLawJLIdYkKqV2jFXdUed5hYwfASDUtUdIxjRj31IKR4O2xHOovxHrGD0Zi4prAUOuAiZCqiDCZ+XuSFoeAAwGXjGOA+kd7PO0cgSJCKeQHCZL5KRWeeTDSzV4H0Ao9er45l2KXVgwPrEAAXrKQwlYMQ8+5xQOAJDYhDGFAXCKrEwzV+/frU6HwuN4qgT1eMasEnbu3fvrLqb2LtrOBxm16Daxwtg3Lp1KwtcR6NR8uLFC23vBtdT1JPRaWNj47vzcJ2Ih7CRnctiIudQYEDKxXaKIpkGaFIsg5svG7S5iDdw85E2xyLr+RX4y8ZZJoVxEtdpcZktjwMTnl11jZ26D1ycSyCkcdDAWEK46jrVrib/5G8+QMMWkUWjsGXCWtu+x0lcfr9zKHz68eXlZe1q93V2+KsiQP/w4UOjv4GV+nsbaPrengANgal7bUf++J2mYPiWy6zq0HWjNdEga2trrTdCKGAgCLZd5K3R3kcRyQiGVCAhshbTD9+oszgw4dhAxWZtCCwdULQrMP4uekzosVRxhfg8eh11lyM4e/Zs/F1S0+6fmD68orhF13sASPDTNuX7AKLI1Mr7uAYJLtHoZdYAgOI6bLefjj6mcCXcZN3OfDb5B1NY0f+vspk8wIlJwWY04R7K1NQ2SuKzdd3BNrZ16h0UebcSQmQPq9FUniAqKJpcFEyC06ZEKGqYWpcqGztpeqEQ3VhMTFMHg3Uf8NEh7dUJ7d69u/T/WXnlWbrxiza6gLoYp24iS/Ik2id5OPT6+4ODQlLZOh8dYrEs4hybzKLNcs6yhqePfUwbgwJPQJF5laxm1dFE+Pc2VqQLacEzWQoSQ/quA/zGoECDY/jbhXSJLZ/d5NCsKh4m11azc+V4vrd+7pryRT+9hAIwxLpyvu398AFFJ0r8fQZVpi7Qh2zcIeIbX3FV0FDY1Gf6DpZ9CKOyvc1T6J4I6X/77of7shSxzCltFIrQnghT6TKWPvx7G+JkIEdQIO4hFD0TqrDK0tgxLUdAKCoIMKyurmqDYkLRIyGdXFZZJTPICEVPhAJiuI4yoQAnpl0HR2x2tbtAtTgqtXXdUMCgqyclFB1wBciDFAkJKpPMJVbVi21v0t5B4TJV7XoJAMYUvn7QsJmfhNR7jBvVNmopmjKxTaTHAYOuujuvru1G6BSKsh/fZHIHJt1Hyb3MXDPNXJaBGqL7GbluDNwAld82ebLqCsGfrPJS17rJEDUO2zQ2zsPvn1zQBd8f4niQc/eBuZf5wlvcEJjbJi0FbjZmuCPhZDudz/X1ymx3WfNKQAlxNLgyFFWfOjSIbu5lU5IF1UJR26WEVetBjEL1mNeOZHd8MxSVF3/s2tpN1P8yCbwBxc2qH45xvyxCURMKWbOR6p7b0A3qbYJibm5u3QQMjBoytuiW0Bs00E0JNH+vega6eLHushej8BAbjvf8JVAsmpwlC53GtKRPbII1xzRNE7chLGRQjF2IERigD2DoVo+jmocBmVNsj2UR/y2mLKzkk1c/p8e86QWgdhGHpLhDSxj1QZK5xYNacxlqMJAM8u8sLS3dSF+u8zb3Ur+mVuKadEm/KX3zhklPhIpGN8VKfAfFWJcTgywn1Xmhra+O40o1FAg00peLBKM3upy2+SbvoBwQG3+IYPTDQvwz+R+DsrPSwPNc+vJHeszwHkYHxMVJC1FqKSYsxo/p8SfvYzSCZfihCAitpZiwGhfSlwVajc4KseK1FAbtAz4w/eYUjvn05Up6XOB97kx387cUhsWqJwxs/1IKByzGpfQ4P7Ye53j/g7EIAOFfuP1xb9JI/wkwAE5cKUNF/UU7AAAAAElFTkSuQmCC"},H8vo:function(t,e,a){"use strict";var s=a("4L6C");a.n(s).a},XIe7:function(t,e,a){"use strict";a.r(e);var s={data:function(){return{showType:"",progressFlag1:!1,progressFlag2:!1,progressPercent1:0,progressPercent2:0,maxSize:104693760,contract_attach:[],contract_doc_name:"",contract_doc_url:"",add_roles_notice:"",id:"",template_id:"",contract_id:"",contract:{},keyword:"",personList:[],currIndex:1,signTableData:[],tableData:[],pickerOptions:{disabledDate:function(t){return t.getTime()<Date.now()-864e5}},ruleForm:{name:"",sign_end_date:"",contract_end_date:""},ncc_list:[],roles_list:[],rules:{name:[{required:!0,message:"请填写合同名称",trigger:"blur"}],sign_end_date:[{required:!0,message:"请选择签署截止日期",trigger:"blur"}],contract_end_date:[{required:!0,message:"请选择合同到期日期",trigger:"blur"}]}}},watch:{},created:function(){this.id=this.$route.params.id,this.id<0?(this.showType="contract",this.template_id=this.$route.query.template_id,this.template_id?(this.contract_id=this.template_id,this.getStep2()):this.getStep1()):":id"===this.id?(this.showType="contract",this.getStep1()):(this.showType="template",this.template_id=this.$route.query.template_id,this.template_id?(this.contract_id=this.id,this.getStep2()):this.getStep1()),this.getNearCc()},destroyed:function(){},methods:{deleteSingle:function(){this.contract_doc_name="",this.contract_doc_url=""},deleteList:function(t){this.contract_attach.splice(t,1)},onProgress1:function(t,e,a){this.progressPercent1=Number(t.percent.toFixed(2))},onProgress2:function(t,e,a){this.progressPercent2=Number(t.percent.toFixed(2))},beforeUpload1:function(t){if(t.size>=this.maxSize)return this.$message.error("文件大小超出10M"),!1;this.progressFlag1=!0},beforeUpload2:function(t){if(t.size>=this.maxSize)return this.$message.error("文件大小超出10M"),!1;this.progressFlag2=!0},onSuccess:function(t,e,a){100===this.progressPercent1&&(this.progressFlag1=!1,this.progressPercent1=0),this.contract_doc_name=t.data.file_name,this.contract_doc_url=t.data.file_url},onSuccess2:function(t,e,a){100===this.progressPercent2&&(this.progressFlag2=!1,this.progressPercent2=0),this.contract_attach.push(t.data)},onError:function(){this.progressFlag1=!1,this.progressFlag2=!1,this.progressPercent1=0,this.progressPercent2=0,this.$message.error("上传失败请重新上传")},handleTypeSelectChange:function(t,e){"0"==t&&(this.signTableData[e].company_name="")},handleSelectChange:function(t,e){for(var a=0;a<this.signTableData.length;a++)this.signTableData[a].role_id===t&&a!==e&&(this.$message.error("签署角色不能重复添加"),this.signTableData[e].role_id="")},toAdd:function(){if(this.signTableData.length>=3)this.$message.error("签署角色最多三个");else{var t=[1,2,3];this.signTableData.map(function(e){t=t.filter(function(t){return t!=Number(e.role_id)})});var e=t.length>0?Math.min.apply(null,t):1;this.signTableData.push({role_id:e.toString(),name:"",tel:"",sign_type:"0",company_name:""})}},toCCAdd:function(t){var e=this;if(t>=0){var a=!1;if(this.tableData.some(function(s,n){if(s.name==e.ncc_list[t].name&&s.tel==e.ncc_list[t].tel)return a=!0,!0}),a)return void this.$message.error("该抄送人信息已存在");this.tableData.push({name:this.ncc_list[t].name,tel:this.ncc_list[t].tel})}else this.tableData.push({name:"",tel:""})},selectStyle:function(){this.currIndex=1},outStyle:function(){this.currIndex=1},handleEdit:function(t,e,a){var s=this,n={},i="";"cc"==a?(i="plugin.yun-sign.frontend.contract.edit-cc",n={id:e.id,name:e.name,tel:e.tel,contract_id:this.contract_id}):(i="plugin.yun-sign.frontend.contract.edit-role",n={id:e.id,role_id:e.role_id,name:e.name,tel:e.tel,sign_type:e.sign_type,company_name:e.company_name,contract_id:this.contract_id}),$http.post(i,n,"loading").then(function(e){1===e.result?"cc"==a?(s.$set(s.tableData,t,e.data.contract_cc_info),console.log(s.tableData)):(e.data.contract_role_info.sign_type.toString(),s.$set(s.signTableData,t,e.data.contract_role_info),console.log(s.signTableData)):s.$message.error(e.msg)}).catch(function(t){console.log(t)})},handleSave:function(t,e,a){var s=this,n={},i="";"cc"==a?(i="plugin.yun-sign.frontend.contract.add-cc",n={name:e.name,tel:e.tel,contract_id:this.contract_id}):(i="plugin.yun-sign.frontend.contract.add-role",n={role_id:e.role_id,name:e.name,tel:e.tel,sign_type:e.sign_type,company_name:e.company_name,contract_id:this.contract_id}),$http.post(i,n,"loading").then(function(e){1===e.result?"cc"==a?(s.$set(s.tableData,t,e.data.contract_cc_info),console.log(s.tableData)):(e.data.contract_role_info.sign_type=e.data.contract_role_info.sign_type.toString(),s.$set(s.signTableData,t,e.data.contract_role_info),console.log(s.signTableData)):s.$message.error(e.msg)}).catch(function(t){console.log(t)})},handleDelete:function(t,e,a){var s=this;if(e.id){var n="";n="cc"==a?"plugin.yun-sign.frontend.contract.delete-cc":"plugin.yun-sign.frontend.contract.delete-role",$http.post(n,{id:e.id}).then(function(e){1===e.result?"cc"==a?s.tableData.splice(t,1):s.signTableData.splice(t,1):s.$message.error(e.msg)}).catch(function(t){console.log(t)})}else"cc"==a?this.tableData.splice(t,1):this.signTableData.splice(t,1)},handleSelect:function(t,e,a,s){t.value=t.tel,this.signTableData[e].name=t.name,this.signTableData[e].tel=t.tel,this.signTableData[e].company_name=t.company_name,this.checkRole("0",t,e,a,s),this.checkRole("1",t,e,a,s)},checkRole:function(t,e,a,s,n){var i=this;return"0"==t&&!e.tel||e.tel.length<6?(e.is_person=!1,this.signTableData[a]=e,void this.signTableData.reverse().reverse()):"1"!=t||e.company_name?void $http.post("plugin.yun-sign.frontend.contract.role-check",{post_data:{type:t,company_name:1==t?e.company_name:"",tel:1==t?"":e.tel}}).then(function(t){1===t.result?(void 0!=t.data.is_person&&(e.is_person=t.data.is_person),void 0!=t.data.is_company&&(e.is_company=t.data.is_company),i.signTableData[a]=e,s&&i.$set(i.signTableData[a],"role_id",s),n&&i.$set(i.signTableData[a],"sign_type",n),i.roles_list[a]&&i.$set(i.signTableData[a],"id",i.roles_list[a].id),i.signTableData.reverse().reverse()):console.log(t.msg)}).catch(function(t){console.log(t)}):(e.is_company=!1,this.signTableData[a]=e,void this.signTableData.reverse().reverse())},searchPerson:function(t,e){var a=this;t?$http.post("plugin.yun-sign.frontend.contract.query-all-roles-by-uid",{keyword:t}).then(function(t){1===t.result?(a.personList=t.data,e(a.personList)):a.$message.error(t.msg)}).catch(function(t){console.log(t)}):e([])},submitFormFirst:function(){var t=this;this.$refs.ruleForm.validate(function(e){if(!e)return console.log("error submit!!"),!1;var a={template_id:t.id,name:t.ruleForm.name,sign_end_date:t.ruleForm.sign_end_date,contract_end_date:t.ruleForm.contract_end_date};$http.post("plugin.yun-sign.frontend.contract.create",a,"loading").then(function(e){1===e.result?t.contract_id=e.data.contract_id:t.$message.error(e.msg)}).catch(function(t){console.log(t)})})},submitFormSecond:function(t){var e=this;if(this.ruleForm.name)if(this.ruleForm.sign_end_date)if(this.ruleForm.contract_end_date)if("contract"!==this.showType||this.contract_doc_url)if(this.signTableData.length<=0)this.$message.error("请添加签署角色");else{for(var a=0;a<this.signTableData.length;a++){if(!this.signTableData[a].role_id)return void this.$message.error("请选择角色名称");if(!this.signTableData[a].sign_type)return void this.$message.error("请选择签署主体")}var s="",n={};"contract"===this.showType?(s="plugin.yun-sign.frontend.contract.create-by-doc",n={contract_id:this.contract_id,name:this.ruleForm.name,sign_end_date:this.ruleForm.sign_end_date,contract_end_date:this.ruleForm.contract_end_date,contract_doc_url:this.contract_doc_url,contract_attach:this.contract_attach,roles:this.signTableData,ccs:this.tableData}):(s="plugin.yun-sign.frontend.contract.create-step2",n={name:this.ruleForm.name,sign_end_date:this.ruleForm.sign_end_date,contract_end_date:this.ruleForm.contract_end_date,roles:this.signTableData,ccs:this.tableData},this.template_id?(n.contract_id=this.contract_id,n.template_id=this.template_id):n.template_id=this.id),$http.post(s,n,"loading").then(function(a){1===a.result?"contract"===e.showType?1==t?e.$router.push(e.fun.getUrl("set_template",{id:a.data.contract_id},{fromCreate:1})):e.$router.replace(e.fun.getUrl("launch")):1==t?e.$router.replace(e.fun.getUrl("create_template_sign",{id:a.data.contract_id})):e.$router.replace(e.fun.getUrl("launch")):e.$message.error(a.msg)}).catch(function(t){console.log(t)})}else this.$message.error("请上传PDF合同文档！");else this.$message.error("请选择合同到期日期！");else this.$message.error("请选择签署截止日期！");else this.$message.error("请输入合同名称！")},getStep1:function(){var t=this,e={};"template"===this.showType&&(e={template_id:this.id}),$http.post("plugin.yun-sign.frontend.contract.detail-step1",e,"loading").then(function(e){1===e.result?("template"===t.showType?t.add_roles_notice=e.data.add_roles_notice:t.add_roles_notice=e.data.add_roles_doc_notice,t.contract=e.data.template_info):(t.$message.error(e.msg),t.$router.go(-1))}).catch(function(t){console.log(t)})},getStep2:function(){var t=this;$http.post("plugin.yun-sign.frontend.contract.detail-step2",{contract_id:this.contract_id},"loading").then(function(e){1===e.result?(t.add_roles_notice=e.data.add_roles_notice,t.contract_info=e.data.contract_info,t.ruleForm.name=t.contract_info.name,t.ruleForm.sign_end_date=t.contract_info.sign_end_date,t.ruleForm.contract_end_date=t.contract_info.contract_end_date,t.contract=e.data.template_info,t.roles_list=e.data.roles_list,e.data.roles_list.length>0&&e.data.roles_list.forEach(function(e){t.signTableData.push({id:e.id,role_id:e.role_id.toString(),name:e.name,tel:e.tel,sign_type:e.sign_type.toString(),company_name:e.company_name})}),e.data.cc_list.length>0&&e.data.cc_list.forEach(function(e){t.tableData.push({id:e.id,name:e.name,tel:e.tel})}),t.contract.contract_doc_url&&(t.contract_doc_name=t.contract.contract_doc_name,t.contract_doc_url=t.contract.contract_doc_url,t.contract_attach=t.contract.contract_attach)):t.$message.error(e.msg)}).catch(function(t){console.log(t)})},getNearCc:function(){var t=this;$http.post("plugin.yun-sign.frontend.contract.nearCc",{},"loading").then(function(e){1===e.result?t.ncc_list=e.data.list:t.$message.error(e.msg)}).catch(function(t){console.log(t)})},getData:function(){var t=this;$http.post("plugin.yun-sign.frontend.template.get-detail",{id:this.id},"loading").then(function(e){1===e.result?t.contract=e.data:t.$message.error(e.msg)}).catch(function(t){console.log(t)})}},components:{}},n=(a("H8vo"),a("KHd+")),i=Object(n.a)(s,function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{attrs:{id:"sign"}},[s("div",{staticStyle:{"font-size":"14px",color:"#666666","margin-bottom":"10px"}},[t._v("电子合同 -> 企业管理 -> 发起签署")]),t._v(" "),s("div",{staticClass:"box"},[t._m(0),t._v(" "),s("div",{staticStyle:{"padding-right":"50px"}},[s("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:t.ruleForm,rules:t.rules,"label-width":"140px"},nativeOn:{submit:function(t){t.preventDefault()}}},[s("el-form-item",{attrs:{label:"合同名称",prop:"name"}},[s("el-input",{attrs:{maxlength:"20",placeholder:"请输入合同名称"},model:{value:t.ruleForm.name,callback:function(e){t.$set(t.ruleForm,"name","string"==typeof e?e.trim():e)},expression:"ruleForm.name"}})],1),t._v(" "),s("el-form-item",{attrs:{label:"签署截止日期",prop:"sign_end_date"}},[s("el-date-picker",{attrs:{align:"right",format:"yyyy 年 MM 月 dd 日","value-format":"yyyy-MM-dd",type:"date",placeholder:"选择日期","picker-options":t.pickerOptions},model:{value:t.ruleForm.sign_end_date,callback:function(e){t.$set(t.ruleForm,"sign_end_date",e)},expression:"ruleForm.sign_end_date"}}),t._v(" "),s("span",{staticClass:"gray"},[t._v("所有签署方须在截止日期前完成签署")])],1),t._v(" "),s("el-form-item",{attrs:{label:"合同到期日期",prop:"contract_end_date"}},[s("el-date-picker",{attrs:{align:"right",format:"yyyy 年 MM 月 dd 日","value-format":"yyyy-MM-dd",type:"date",placeholder:"选择日期","picker-options":t.pickerOptions},model:{value:t.ruleForm.contract_end_date,callback:function(e){t.$set(t.ruleForm,"contract_end_date",e)},expression:"ruleForm.contract_end_date"}})],1)],1)],1)]),t._v(" "),"template"===t.showType?s("div",{staticClass:"box"},[t._m(1),t._v(" "),s("div",{staticClass:"template-box"},[s("el-row",[s("el-col",{attrs:{span:3}},[s("div",[t._v("模版名称")])]),t._v(" "),s("el-col",{attrs:{span:20}},[t._v(t._s(t.contract.name))])],1),t._v(" "),s("div",{staticStyle:{height:"20px"}}),t._v(" "),s("el-row",[s("el-col",{attrs:{span:3}},[s("div",[t._v("合同文档")])]),t._v(" "),s("el-col",{attrs:{span:10}},[s("div",{staticStyle:{"margin-bottom":"15px"}},[t._v("合同文件：")]),t._v(" "),s("div",{staticClass:"fileItemArea_2VWWl"},[s("div",{staticClass:"imgPlaceholder_22teb"},[s("div",{directives:[{name:"show",rawName:"v-show",value:1==t.currIndex,expression:"currIndex == 1"}],on:{mouseover:t.selectStyle,mouseout:t.outStyle}},[s("img",{attrs:{src:a("ud4p"),alt:""}})]),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:0==t.currIndex,expression:"currIndex == 0"}],on:{mouseover:t.selectStyle,mouseout:t.outStyle}},[s("img",{attrs:{src:a("G2Fa"),alt:""}})])]),t._v(" "),s("div",{staticClass:"fileInfoArea_3dGBW"},[s("p",{staticClass:"fileName_25vmY"},[t._v(t._s(t.contract.contract_doc_name))]),t._v(" "),s("p",{})])])]),t._v(" "),s("el-col",{attrs:{span:10}},[s("div",{staticStyle:{"margin-bottom":"15px"}},[t._v("附件列表：")]),t._v(" "),t._l(t.contract.contract_attach,function(e,n){return s("div",{key:n,staticClass:"fileItemArea_2VWWl"},[s("div",{staticClass:"imgPlaceholder_22teb"},[s("div",[s("img",{attrs:{src:a("ud4p"),alt:""}})])]),t._v(" "),s("div",{staticClass:"fileInfoArea_3dGBW"},[s("p",{staticClass:"fileName_25vmY"},[t._v(t._s(e.file_name))]),t._v(" "),s("p",{})])])})],2)],1)],1)]):t._e(),t._v(" "),"contract"===t.showType?s("div",{staticClass:"box"},[t._m(2),t._v(" "),s("div",{staticClass:"template-box"},[s("el-row",[s("el-col",{attrs:{span:3}},[s("div",[t._v("合同文档")])]),t._v(" "),s("el-col",{attrs:{span:10}},[t.contract_doc_name?t._e():s("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{"show-file-list":!1,"before-upload":t.beforeUpload1,"on-progress":t.onProgress1,"on-success":t.onSuccess,"on-error":t.onError,accept:"application/pdf",action:t.fun.getRealUrl("plugin.yun-sign.frontend.index.upload")}},[s("div",{directives:[{name:"show",rawName:"v-show",value:t.progressFlag1,expression:"progressFlag1"}],staticClass:"progress-box"},[s("el-progress",{attrs:{type:"circle",percentage:t.progressPercent1}})],1),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:!t.progressFlag1,expression:"!progressFlag1"}],staticClass:"fileAddItem_1Vqb7"},[s("i",{staticClass:"iconfont icon-adsystem_icon_add"}),t._v(" "),s("p",{staticClass:"defaultInfoStyle_2mgdq"},[t._v(" "+t._s(t.id<0?"上传合同文件":"添加模版"))])]),t._v(" "),s("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("支持10M以内的pdf格式文件")])]),t._v(" "),s("div",{staticStyle:{height:"20px"}}),t._v(" "),t.contract_doc_name?s("div",{staticClass:"fileItemArea_2VWWl"},[s("div",{staticClass:"imgPlaceholder_22teb"},[s("div",{directives:[{name:"show",rawName:"v-show",value:1==t.currIndex,expression:"currIndex == 1"}],on:{mouseover:t.selectStyle,mouseout:t.outStyle}},[s("img",{attrs:{src:a("ud4p"),alt:""}})]),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:0==t.currIndex,expression:"currIndex == 0"}],on:{mouseover:t.selectStyle,mouseout:t.outStyle}},[s("img",{attrs:{src:a("G2Fa"),alt:""}})])]),t._v(" "),s("div",{staticClass:"fileInfoArea_3dGBW"},[s("p",{staticClass:"fileName_25vmY"},[t._v(t._s(t.contract_doc_name))]),t._v(" "),s("p",{staticClass:"fileName_25vmY deleted",on:{click:t.deleteSingle}},[s("i",{staticClass:"el-icon-delete"}),t._v("删除")])])]):t._e()],1)],1),t._v(" "),s("el-row",[s("el-col",{attrs:{span:3}},[s("div",[t._v("模版附件")])]),t._v(" "),s("el-col",{attrs:{span:20}},[s("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{"show-file-list":!1,"before-upload":t.beforeUpload2,"on-progress":t.onProgress2,"on-success":t.onSuccess2,"on-error":t.onError,accept:"application/pdf",action:t.fun.getRealUrl("plugin.yun-sign.frontend.index.upload")}},[s("el-button",{attrs:{size:"small",type:"primary"}},[t._v("点击上传附件")]),t._v(" "),s("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("支持10M以内的pdf格式文件")])],1),t._v(" "),s("div",{staticStyle:{height:"20px"}}),t._v(" "),t._l(t.contract_attach,function(e,n){return s("div",{key:n,staticClass:"fileItemArea_2VWWl"},[s("div",{staticClass:"imgPlaceholder_22teb"},[s("div",{directives:[{name:"show",rawName:"v-show",value:1==t.currIndex,expression:"currIndex == 1"}],on:{mouseover:t.selectStyle,mouseout:t.outStyle}},[s("img",{attrs:{src:a("ud4p"),alt:""}})]),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:0==t.currIndex,expression:"currIndex == 0"}],on:{mouseover:t.selectStyle,mouseout:t.outStyle}},[s("img",{attrs:{src:a("G2Fa"),alt:""}})])]),t._v(" "),s("div",{staticClass:"fileInfoArea_3dGBW"},[s("p",{staticClass:"fileName_25vmY"},[t._v(t._s(e.file_name))]),t._v(" "),s("p",{staticClass:"fileName_25vmY deleted",on:{click:function(e){t.deleteList(n)}}},[s("i",{staticClass:"el-icon-delete"}),t._v("删除")])])])}),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:t.progressFlag2,expression:"progressFlag2"}],staticClass:"progress-box",staticStyle:{display:"inline-block"}},[s("el-progress",{attrs:{type:"circle",percentage:t.progressPercent2}})],1)],2)],1)],1)]):t._e(),t._v(" "),s("div",{staticClass:"box"},[t._m(3),t._v(" "),s("div",{staticStyle:{padding:"0 10px"}},[s("el-table",{staticStyle:{width:"100%"},attrs:{data:t.signTableData,"empty-text":"暂无签署角色"}},[s("el-table-column",{attrs:{label:"角色名称","min-width":"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-select",{attrs:{placeholder:"请选择角色名称"},on:{change:function(a){t.handleSelectChange(a,e.$index)}},model:{value:e.row.role_id,callback:function(a){t.$set(e.row,"role_id",a)},expression:"scope.row.role_id"}},[s("el-option",{attrs:{label:"甲方",value:"1"}}),t._v(" "),s("el-option",{attrs:{label:"乙方",value:"2"}}),t._v(" "),s("el-option",{attrs:{label:"丙方",value:"3"}})],1)]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"手机号","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",{staticStyle:{display:"flex","align-items":"center"}},[s("el-autocomplete",{attrs:{"popper-class":"my-autocomplete","fetch-suggestions":t.searchPerson,placeholder:"请输入手机号"},on:{blur:function(a){t.checkRole("0",e.row,e.$index)}},scopedSlots:t._u([{key:"default",fn:function(a){var n=a.item;return[s("div",{staticClass:"popover-box",on:{click:function(a){t.handleSelect(n,e.$index,e.row.role_id,e.row.sign_type)}}},[s("p",[t._v("手机："+t._s(n.tel))]),t._v(" "),s("p",[t._v("姓名："+t._s(n.name))]),t._v(" "),s("p",{staticStyle:{"white-space":"pre-wrap",color:"#33b1ff"}},[t._v("企业："+t._s(n.company_name))])])]}}]),model:{value:e.row.tel,callback:function(a){t.$set(e.row,"tel","string"==typeof a?a.trim():a)},expression:"scope.row.tel"}}),t._v(" "),e.row.tel&&e.row.is_person?s("div",{staticStyle:{"margin-left":"5px"}},[s("i",{staticClass:"iconfont el-icon-circle-check"})]):t._e(),t._v(" "),e.row.tel&&!e.row.is_person?s("div",{staticStyle:{"margin-left":"5px"}},[s("i",{staticClass:"iconfont el-icon-warning-outline"})]):t._e()],1)]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"姓名","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-input",{attrs:{placeholder:"请输入姓名"},model:{value:e.row.name,callback:function(a){t.$set(e.row,"name","string"==typeof a?a.trim():a)},expression:"scope.row.name"}})]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"签署主体","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-select",{attrs:{placeholder:"请选择签署主体"},on:{change:function(a){t.handleTypeSelectChange(a,e.$index)}},model:{value:e.row.sign_type,callback:function(a){t.$set(e.row,"sign_type",a)},expression:"scope.row.sign_type"}},[s("el-option",{attrs:{label:"个人",value:"0"}}),t._v(" "),s("el-option",{attrs:{label:"企业",value:"1"}})],1)]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"企业信息","min-width":"170"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("div",{staticStyle:{display:"flex","align-items":"center"}},[s("el-input",{attrs:{placeholder:"请输入企业名称"},on:{blur:function(a){t.checkRole("1",e.row,e.$index)}},model:{value:e.row.company_name,callback:function(a){t.$set(e.row,"company_name","string"==typeof a?a.trim():a)},expression:"scope.row.company_name"}}),t._v(" "),e.row.company_name&&e.row.is_company?s("div",{staticStyle:{"margin-left":"5px"}},[s("i",{staticClass:"iconfont el-icon-circle-check"})]):t._e(),t._v(" "),e.row.company_name&&!e.row.is_company?s("div",{staticStyle:{"margin-left":"5px"}},[s("i",{staticClass:"iconfont el-icon-warning-outline"})]):t._e()],1)]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"操作","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(a){t.handleDelete(e.$index,e.row)}}},[t._v("删除\n            ")])]}}])})],1),t._v(" "),s("div",{staticStyle:{"text-align":"center","margin-top":"10px"}},[s("el-button",{staticStyle:{width:"100%"},on:{click:t.toAdd}},[t._v("添加签署角色")])],1)],1),t._v(" "),s("div",{staticStyle:{"margin-top":"20px",padding:"0 15px"},domProps:{innerHTML:t._s(t.add_roles_notice)}})]),t._v(" "),s("div",{staticClass:"box"},[t._m(4),t._v(" "),s("div",{staticStyle:{padding:"0 10px"}},[s("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData,"empty-text":"暂无抄送人"}},[s("el-table-column",{attrs:{label:"姓名","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-input",{attrs:{placeholder:"请输入姓名"},model:{value:e.row.name,callback:function(a){t.$set(e.row,"name","string"==typeof a?a.trim():a)},expression:"scope.row.name"}})]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"手机号","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-input",{attrs:{placeholder:"请输入手机号"},model:{value:e.row.tel,callback:function(a){t.$set(e.row,"tel","string"==typeof a?a.trim():a)},expression:"scope.row.tel"}})]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"操作","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(a){t.handleDelete(e.$index,e.row,"cc")}}},[t._v("删除\n            ")])]}}])})],1),t._v(" "),s("div",{staticStyle:{"text-align":"center","margin-top":"10px"}},[s("el-button",{staticStyle:{width:"100%"},on:{click:function(e){t.toCCAdd(-1)}}},[t._v("添加抄送人")])],1),t._v(" "),t.ncc_list.length>0?s("div",{staticClass:"near-cc",staticStyle:{color:"red","line-height":"40px"}},[s("div",[s("span",[t._v("选择最近抄送人：")]),t._v(" "),t._l(t.ncc_list,function(e,a){return s("span",{key:a,staticStyle:{cursor:"pointer"},on:{click:function(e){t.toCCAdd(a)}}},[t._v("\n            "+t._s(e.name)+"\n            "),a!=t.ncc_list.length-1?s("span",[t._v("、")]):t._e()])})],2)]):t._e()],1)]),t._v(" "),s("div",{staticStyle:{"text-align":"center"}},[s("el-button",{staticStyle:{width:"200px"},attrs:{type:"primary"},on:{click:function(e){t.submitFormSecond("2")}}},[t._v("保存")]),t._v(" "),s("el-button",{staticStyle:{width:"200px"},attrs:{type:"primary"},on:{click:function(e){t.submitFormSecond("1")}}},[t._v("下一步")])],1)])},[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"right-head"},[e("div",{staticClass:"right-head-con"},[e("span",{staticClass:"green-title-line"}),this._v("合同基本信息")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"right-head"},[e("div",{staticClass:"right-head-con"},[e("span",{staticClass:"green-title-line"}),this._v("合同模版信息")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"right-head"},[e("div",{staticClass:"right-head-con"},[e("span",{staticClass:"green-title-line"}),this._v("合同模板信息")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"right-head"},[e("div",{staticClass:"right-head-con"},[e("span",{staticClass:"green-title-line"}),this._v("签署信息")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"right-head"},[e("div",{staticClass:"right-head-con"},[e("span",{staticClass:"green-title-line"}),this._v("抄送人")])])}],!1,null,"4d586198",null);i.options.__file="create_sign.vue";e.default=i.exports},ud4p:function(t,e){t.exports="data:image/png;base64,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"}}]);
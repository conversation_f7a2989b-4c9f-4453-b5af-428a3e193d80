(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-f4ed"],{"94Fy":function(t,e,a){"use strict";var n=a("MaT3");a.n(n).a},I9pr:function(t,e,a){"use strict";a.r(e);var n={name:"PlatformUserList",data:function(){return{Info:{},list:[],pageSize:0,current_page:0,total:0,form:{},search_form:{},table_loading:!1,dialogTableVisible:!1,change_pwd_id:"",id:"",rules:{}}},created:function(){this.id=this.$route.params.id,this.currentChange(1)},destroyed:function(){},methods:{toAdd:function(){1==this.is_show_add&&this.$router.push(this.fun.getUrl("add_platform"))},currentChange:function(t){var e=this;$http.post("plugin.yun-sign.frontend.contract-num-log.get-list",{page:t,start:this.search_form.start,end:this.search_form.end,account_name:this.search_form.account_name,income_type:this.search_form.income_type},"loading").then(function(t){1===t.result?(e.Info=t.data,e.total=t.data.list.total,e.pageSize=t.data.list.per_page,e.list=t.data.list.data,e.current_page=t.data.list.current_page):(e.list=[],-1===t.data.status&&e.currentChange(1),e.$message.error(t.msg))}).catch(function(t){console.error(t)})},changePwd:function(){console.log(this.change_pwd_id),this.dialogTableVisible=!1},delRow:function(t,e){var a=this;this.$confirm("确定删除吗","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){a.$message({type:"success",message:"删除成功"})}).catch(function(){a.$message({type:"info",message:"已取消删除"})})}}},r=(a("94Fy"),a("ZrdR")),l=Object(r.a)(n,function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"right"},[t._m(0),t._v(" "),a("div",{staticClass:"right-head"},[a("div",{staticClass:"right-head-con"},[a("div",{staticClass:"item"},[t._v("购买总数："+t._s(t.Info.contract_num_sum))]),t._v(" "),a("div",{staticClass:"item"},[t._v("已使用数量："+t._s(t.Info.use_num_sum))]),t._v(" "),a("div",{staticClass:"item"},[t._v("可使用数量："+t._s(t.Info.rest_num_sum))])])]),t._v(" "),a("el-form",{ref:"search_form",attrs:{inline:!0,model:t.search_form}},[a("el-row",[a("el-col",{attrs:{span:20}},[a("el-form-item",[a("el-date-picker",{attrs:{placeholder:"选择购买开始时间"},model:{value:t.search_form.start,callback:function(e){t.$set(t.search_form,"start",e)},expression:"search_form.start"}})],1),t._v(" "),a("el-form-item",[a("el-date-picker",{attrs:{placeholder:"选择购买结束时间"},model:{value:t.search_form.end,callback:function(e){t.$set(t.search_form,"end",e)},expression:"search_form.end"}})],1),t._v(" "),a("el-form-item",[a("el-select",{attrs:{placeholder:"请选择类型"},model:{value:t.search_form.income_type,callback:function(e){t.$set(t.search_form,"income_type",e)},expression:"search_form.income_type"}},[a("el-option",{attrs:{label:"购买",value:"1"}}),t._v(" "),a("el-option",{attrs:{label:"使用",value:"-1"}})],1)],1),t._v(" "),a("el-form-item",[a("el-input",{attrs:{placeholder:"请输入用户姓名或帐号"},model:{value:t.search_form.account_name,callback:function(e){t.$set(t.search_form,"account_name",e)},expression:"search_form.account_name"}})],1)],1),t._v(" "),a("el-col",{attrs:{span:3,align:"right"}},[a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(e){t.currentChange(1)}}},[t._v("查询\n          ")])],1)],1)],1)],1),t._v(" "),[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.table_loading,expression:"table_loading"}],staticStyle:{width:"100%"},attrs:{data:t.list}},[a("el-table-column",{attrs:{prop:"id",label:"ID",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"change_mode",label:"业务类型",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"has_one_person.name",label:"用户",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{width:"180px",prop:"created_at",label:"时间",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"after_num",label:"合同可用数量",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"contract_num",label:"合同数量变动",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{width:"180px",label:"有效期",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.contract_num>0?e.row.end_time:""))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"remark",label:"备注",align:"center"}})],1),t._v(" "),a("el-col",{staticStyle:{padding:"15px 5% 15px 0"},attrs:{span:24,align:"right"}},[a("el-pagination",{attrs:{layout:"prev, pager, next","current-page":t.current_page,"page-size":t.pageSize,total:t.total,background:""},on:{"current-change":t.currentChange,"update:currentPage":function(e){t.current_page=e}}})],1)],t._v(" "),a("el-dialog",{attrs:{title:"修改密码",visible:t.dialogTableVisible},on:{"update:visible":function(e){t.dialogTableVisible=e}}},[a("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"20%"}},[a("el-form-item",{attrs:{label:"原密码",prop:"pwd"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{type:"text"},model:{value:t.form.pwd,callback:function(e){t.$set(t.form,"pwd",e)},expression:"form.pwd"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"新密码",prop:"new_pwd"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{type:"text"},model:{value:t.form.new_pwd,callback:function(e){t.$set(t.form,"new_pwd",e)},expression:"form.new_pwd"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"确认新密码",prop:"again_pwd"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{type:"text"},model:{value:t.form.again_pwd,callback:function(e){t.$set(t.form,"again_pwd",e)},expression:"form.again_pwd"}})],1)],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogTableVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.changePwd()}}},[t._v("确 定")])],1)],1)],2)},[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"green-title-box",staticStyle:{"padding-bottom":"20px","border-bottom":"1px #ccc solid"}},[e("span",{staticClass:"green-title-line"}),this._v("合同账单")])}],!1,null,"942f132a",null);l.options.__file="contract_bills.vue";e.default=l.exports},MaT3:function(t,e,a){}}]);
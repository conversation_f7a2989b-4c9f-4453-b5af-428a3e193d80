/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.7.0 (2021-02-10)
 */
!function(){"use strict";var e,t,n,r,a,o,d=function(e){var t=e;return{get:function(){return t},set:function(e){t=e}}},i=tinymce.util.Tools.resolve("tinymce.PluginManager"),s=function(){},u=function(e){return function(){return e}},l=u(!1),c=u(!0),f=function(){return m},m=(e=function(e){return e.isNone()},{fold:function(e,t){return e()},is:l,isSome:l,isNone:c,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:u(null),getOrUndefined:u(undefined),or:n,orThunk:t,map:f,each:s,bind:f,exists:l,forall:c,filter:f,equals:e,equals_:e,toArray:function(){return[]},toString:u("none()")}),p=function(n){var e=u(n),t=function(){return a},r=function(e){return e(n)},a={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:c,isNone:l,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return p(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?a:m},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(l,function(e){return t(n,e)})}};return a},g={some:p,none:f,from:function(e){return null===e||e===undefined?m:p(e)}},P=function(e){return!(null===(t=e)||t===undefined);var t},v=(r="function",function(e){return typeof e===r}),h=Array.prototype.slice,y=function(e,t){for(var n=0,r=e.length;n<r;n++){if(t(e[n],n))return!0}return!1},b=function(e,t){for(var n=e.length,r=new Array(n),a=0;a<n;a++){var o=e[a];r[a]=t(o,a)}return r},x=function(e,t){for(var n=0,r=e.length;n<r;n++){t(e[n],n)}},w=v(Array.from)?Array.from:function(e){return h.call(e)},_=function(){var t=d(g.none());return{clear:function(){return t.set(g.none())},set:function(e){return t.set(g.some(e))},isSet:function(){return t.get().isSome()},on:function(e){return t.get().each(e)}}},C=function(e,t,n){return""===t||e.length>=t.length&&e.substr(n,n+t.length)===t},T=tinymce.util.Tools.resolve("tinymce.Env"),D=tinymce.util.Tools.resolve("tinymce.util.Delay"),k=tinymce.util.Tools.resolve("tinymce.util.Promise"),S=tinymce.util.Tools.resolve("tinymce.util.VK"),O=function(e,t){return e.fire("PastePlainTextToggle",{state:t})},j=tinymce.util.Tools.resolve("tinymce.util.Tools"),R=function(e){return e.getParam("paste_data_images",!1)},A=function(e){return e.getParam("paste_retain_style_properties")},I=function(e){return e.getParam("validate")},F=function(e){return e.getParam("paste_data_images",!1,"boolean")},E=function(e){return j.explode(e.getParam("images_file_types","jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp","string"))},M="x-tinymce/html",N="\x3c!-- "+M+" --\x3e",B=function(e){return-1!==e.indexOf(N)},L=tinymce.util.Tools.resolve("tinymce.html.Entities"),H=function(e,t,n){var r=e.split(/\n\n/),a=function(e,t){var n,r=[],a="<"+e;if("object"==typeof t){for(n in t)t.hasOwnProperty(n)&&r.push(n+'="'+L.encodeAllRaw(t[n])+'"');r.length&&(a+=" "+r.join(" "))}return a+">"}(t,n),o="</"+t+">",i=j.map(r,function(e){return e.split(/\n/).join("<br />")});return 1===i.length?i[0]:j.map(i,function(e){return a+e+o}).join("")},$=tinymce.util.Tools.resolve("tinymce.html.DomParser"),z=tinymce.util.Tools.resolve("tinymce.html.Serializer"),U=tinymce.util.Tools.resolve("tinymce.html.Node"),q=tinymce.util.Tools.resolve("tinymce.html.Schema"),V=function(t,e){return j.each(e,function(e){t=e.constructor===RegExp?t.replace(e,""):t.replace(e[0],e[1])}),t},K=function(e){return e=V(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/gi,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,function(e,t,n){return t||n?"\xa0":" "}],/<br class="Apple-interchange-newline">/g,/<br>$/i])},X=function(e){return/<font face="Times New Roman"|class="?Mso|style="[^"]*\bmso-|style='[^']*\bmso-|w:WordDocument/i.test(e)||/class="OutlineElement/.test(e)||/id="?docs\-internal\-guid\-/.test(e)},W=function(e){for(var a,o,i=1,n=function(e){var t="";if(3===e.type)return e.value;if(e=e.firstChild)for(;t+=n(e),e=e.next;);return t},s=function(e,t){if(3===e.type&&t.test(e.value))return e.value=e.value.replace(t,""),!1;if(e=e.firstChild)do{if(!s(e,t))return!1}while(e=e.next);return!0},u=function(e){if(e._listIgnore)e.remove();else if(e=e.firstChild)for(;u(e),e=e.next;);},t=function(e,t,n){var r=e._listLevel||i;r!==i&&(a=r<i?a&&a.parent.parent:(o=a,null)),a&&a.name===t?a.append(e):(o=o||a,a=new U(t,1),1<n&&a.attr("start",""+n),e.wrap(a)),e.name="li",i<r&&o&&o.lastChild.append(a),i=r,u(e),s(e,/^\u00a0+/),s(e,/^\s*([\u2022\u00b7\u00a7\u25CF]|\w+\.)/),s(e,/^\u00a0+/)},r=[],l=e.firstChild;null!=l;)if(r.push(l),null!==(l=l.walk()))for(;void 0!==l&&l.parent!==e;)l=l.walk();for(var c=0;c<r.length;c++)if("p"===(e=r[c]).name&&e.firstChild){var f=n(e);if(/^[\s\u00a0]*[\u2022\u00b7\u00a7\u25CF]\s*/.test(f)){t(e,"ul");continue}if(function(t){var n;return t=t.replace(/^[\u00a0 ]+/,""),j.each([/^[IVXLMCD]{1,2}\.[ \u00a0]/,/^[ivxlmcd]{1,2}\.[ \u00a0]/,/^[a-z]{1,2}[\.\)][ \u00a0]/,/^[A-Z]{1,2}[\.\)][ \u00a0]/,/^[0-9]+\.[ \u00a0]/,/^[\u3007\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d]+\.[ \u00a0]/,/^[\u58f1\u5f10\u53c2\u56db\u4f0d\u516d\u4e03\u516b\u4e5d\u62fe]+\.[ \u00a0]/],function(e){if(e.test(t))return!(n=!0)}),n}(f)){var d=/([0-9]+)\./.exec(f),m=1;d&&(m=parseInt(d[1],10)),t(e,"ol",m);continue}if(e._listLevel){t(e,"ul",1);continue}a=null}else o=a,a=null},Y=function(r,e){var a,t=A(r);t&&(a=j.makeMap(t.split(/[, ]/))),e=V(e,[/<br class="?Apple-interchange-newline"?>/gi,/<b[^>]+id="?docs-internal-[^>]*>/gi,/<!--[\s\S]+?-->/gi,/<(!|script[^>]*>.*?<\/script(?=[>\s])|\/?(\?xml(:\w+)?|img|meta|link|style|\w:\w+)(?=[\s\/>]))[^>]*>/gi,[/<(\/?)s>/gi,"<$1strike>"],[/&nbsp;/gi,"\xa0"],[/<span\s+style\s*=\s*"\s*mso-spacerun\s*:\s*yes\s*;?\s*"\s*>([\s\u00a0]*)<\/span>/gi,function(e,t){return 0<t.length?t.replace(/./," ").slice(Math.floor(t.length/2)).split("").join("\xa0"):""}]]);var n=r.getParam("paste_word_valid_elements","-strong/b,-em/i,-u,-span,-p,-ol,-ul,-li,-h1,-h2,-h3,-h4,-h5,-h6,-p/div,-a[href|name],sub,sup,strike,br,del,table[width],tr,td[colspan|rowspan|width],th[colspan|rowspan|width],thead,tfoot,tbody"),o=q({valid_elements:n,valid_children:"-li[p]"});j.each(o.elements,function(e){e.attributes["class"]||(e.attributes["class"]={},e.attributesOrder.push("class")),e.attributes.style||(e.attributes.style={},e.attributesOrder.push("style"))});var i=$({},o);i.addAttributeFilter("style",function(e){for(var t,n=e.length;n--;)(t=e[n]).attr("style",function(n,r,a,o){var i,s={},e=n.dom.parseStyle(o);return j.each(e,function(e,t){switch(t){case"mso-list":(i=/\w+ \w+([0-9]+)/i.exec(o))&&(a._listLevel=parseInt(i[1],10)),/Ignore/i.test(e)&&a.firstChild&&(a._listIgnore=!0,a.firstChild._listIgnore=!0);break;case"horiz-align":t="text-align";break;case"vert-align":t="vertical-align";break;case"font-color":case"mso-foreground":t="color";break;case"mso-background":case"mso-highlight":t="background";break;case"font-weight":case"font-style":return void("normal"!==e&&(s[t]=e));case"mso-element":if(/^(comment|comment-list)$/i.test(e))return void a.remove()}0!==t.indexOf("mso-comment")?0!==t.indexOf("mso-")&&("all"===A(n)||r&&r[t])&&(s[t]=e):a.remove()}),/(bold)/i.test(s["font-weight"])&&(delete s["font-weight"],a.wrap(new U("b",1))),/(italic)/i.test(s["font-style"])&&(delete s["font-style"],a.wrap(new U("i",1))),(s=n.dom.serializeStyle(s,a.name))||null}(r,a,t,t.attr("style"))),"span"===t.name&&t.parent&&!t.attributes.length&&t.unwrap()}),i.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)n=(t=e[r]).attr("class"),/^(MsoCommentReference|MsoCommentText|msoDel)$/i.test(n)&&t.remove(),t.attr("class",null)}),i.addNodeFilter("del",function(e){for(var t=e.length;t--;)e[t].remove()}),i.addNodeFilter("a",function(e){for(var t,n,r,a=e.length;a--;)if(n=(t=e[a]).attr("href"),r=t.attr("name"),n&&-1!==n.indexOf("#_msocom_"))t.remove();else if(n&&0===n.indexOf("file://")&&(n=(n=n.split("#")[1])&&"#"+n),n||r){if(r&&!/^_?(?:toc|edn|ftn)/i.test(r)){t.unwrap();continue}t.attr({href:n,name:r})}else t.unwrap()});var s=i.parse(e);return r.getParam("paste_convert_word_fake_lists",!0)&&W(s),e=z({validate:I(r)},o).serialize(s)},Z=function(e,t){return{content:e,cancelled:t}},G=function(e,t,n,r){var a,o,i,s,u,l,c,f,d,m,p,g,v=(a=t,o=n,i=r,e.fire("PastePreProcess",{content:a,internal:o,wordContent:i})),h=function(e,t){var n=$({},e.schema);n.addNodeFilter("meta",function(e){j.each(e,function(e){e.remove()})});var r=n.parse(t,{forced_root_block:!1,isRootContent:!0});return z({validate:I(e)},e.schema).serialize(r)}(e,v.content);return e.hasEventListeners("PastePostProcess")&&!v.isDefaultPrevented()?(u=h,l=n,c=r,p=(s=e).dom.create("div",{style:"display:none"},u),f=p,d=l,m=c,g=s.fire("PastePostProcess",{node:f,internal:d,wordContent:m}),Z(g.node.innerHTML,g.isDefaultPrevented())):Z(h,v.isDefaultPrevented())},J=function(e,t,n){var r,a,o=X(t),i=o?(a=t,(r=e).getParam("paste_enable_default_filters",!0)?Y(r,a):a):t;return G(e,i,n,o)},Q=function(e,t){return e.insertContent(t,{merge:e.getParam("paste_merge_formats",!0),paste:!0}),!0},ee=function(e){return/^https?:\/\/[\w\?\-\/+=.&%@~#]+$/i.test(e)},te=function(e,r){return ee(r)&&y(E(e),function(e){return t=r.toLowerCase(),n="."+e.toLowerCase(),C(t,n,t.length-n.length);var t,n})},ne=function(e,t,n){return!(!1!==e.selection.isCollapsed()||!ee(t))&&(a=t,o=n,(r=e).undoManager.extra(function(){o(r,a)},function(){r.execCommand("mceInsertLink",!1,a)}),!0);var r,a,o},re=function(e,t,n){return!!te(e,t)&&(a=t,o=n,(r=e).undoManager.extra(function(){o(r,a)},function(){r.insertContent('<img src="'+a+'">')}),!0);var r,a,o},ae=function(e,t,n){var r,a;n||!1===e.getParam("smart_paste",!0)?Q(e,t):(r=e,a=t,j.each([ne,re,Q],function(e){return!0!==e(r,a,Q)}))},oe=function(e){return"\n"===e||"\r"===e},ie=function(e,t){var n,r,a,o,i=(n=" ",(r=e.getParam("paste_tab_spaces",4,"number"))<=0?"":new Array(r+1).join(n)),s=t.replace(/\t/g,i);return(o={pcIsSpace:!(a=function(e,t){return-1!==" \f\t\x0B".indexOf(t)||"\xa0"===t?e.pcIsSpace||""===e.str||e.str.length===s.length-1||(n=s,(r=e.str.length+1)<n.length&&0<=r&&oe(n[r]))?{pcIsSpace:!1,str:e.str+"\xa0"}:{pcIsSpace:!0,str:e.str+" "}:{pcIsSpace:oe(t),str:e.str+t};var n,r}),str:""},x(s,function(e){o=a(o,e)}),o).str},se=function(e,t,n,r){var a=J(e,t,n);!1===a.cancelled&&ae(e,a.content,r)},ue=function(e,t,n){var r=n||B(t);se(e,t.replace(N,""),r,!1)},le=function(e,t){var n,r,a,o=e.dom.encode(t).replace(/\r\n/g,"\n"),i=ie(e,o),s=(n=i,r=e.getParam("forced_root_block"),a=e.getParam("forced_root_block_attrs"),r?H(n,!0===r?"p":r,a):n.replace(/\r?\n/g,"<br>"));se(e,s,!1,!0)},ce=function(e){var t,n={};if(e&&(!e.getData||(t=e.getData("Text"))&&0<t.length&&-1===t.indexOf("data:text/mce-internal,")&&(n["text/plain"]=t),e.types))for(var r=0;r<e.types.length;r++){var a=e.types[r];try{n[a]=e.getData(a)}catch(o){n[a]=""}}return n},fe=function(e,t){return t in e&&0<e[t].length},de=function(e){return fe(e,"text/html")||fe(e,"text/plain")},me=(a="mceclip",o=0,function(){return a+o++}),pe=function(e,t){var n,r,a,o,i,s,u,l,c,f,d,m,p,g=(n=t.uri,(r=/data:([^;]+);base64,([a-z0-9\+\/=]+)/i.exec(n))?{type:r[1],data:decodeURIComponent(r[2])}:{type:null,data:null}),v=g.data,h=g.type,y=me(),b=t.blob,x=new Image;x.src=t.uri,m=x,!(p=e.getParam("images_dataimg_filter"))||p(m)?(l=void 0,(o=(a=e.editorUpload.blobCache).getByData(v,h))?l=o:(s=(i=e.getParam("images_reuse_filename")&&P(b.name))?(c=e,f=b.name,d=f.match(/([\s\S]+?)(?:\.[a-z0-9.]+)$/i),P(d)?c.dom.encode(d[1]):null):y,u=i?b.name:undefined,l=a.create(y,b,v,s,u),a.add(l)),ue(e,'<img src="'+l.blobUri()+'">',!1)):ue(e,'<img src="'+t.uri+'">',!1)},ge=function(e){return k.all(b(e,function(r){return new k(function(e){var t=P(r.getAsFile)?r.getAsFile():r,n=new window.FileReader;n.onload=function(){e({blob:t,uri:n.result})},n.readAsDataURL(t)})}))},ve=function(e){var t=E(e);return function(r){return e=r.type,C(e,"image/",0)&&y(t,function(e){return t=e.toLowerCase(),n={jpg:"jpeg",jpe:"jpeg",jfi:"jpeg",jif:"jpeg",jfif:"jpeg",pjpeg:"jpeg",pjp:"jpeg",svg:"svg+xml"},(j.hasOwn(n,t)?"image/"+n[t]:"image/"+t)===r.type;var t,n});var e}},he=function(t,e,n){var r,a,o,i,s="paste"===e.type?e.clipboardData:e.dataTransfer;if(F(t)&&s){var u=(r=t,o=(a=s).items?b(w(a.items),function(e){return e.getAsFile()}):[],i=a.files?w(a.files):[],function(e,t){for(var n=[],r=0,a=e.length;r<a;r++){var o=e[r];t(o,r)&&n.push(o)}return n}(0<o.length?o:i,ve(r)));if(0<u.length)return e.preventDefault(),ge(u).then(function(e){n&&t.selection.setRng(n),x(e,function(e){pe(t,e)})}),!0}return!1},ye=function(e){return S.metaKeyPressed(e)&&86===e.keyCode||e.shiftKey&&45===e.keyCode},be=function(s,v,u){var l,c=_(),f=_();s.on("keyup",f.clear),s.on("keydown",function(e){var t,n=function(e){ye(e)&&!e.isDefaultPrevented()&&v.remove()};if(ye(e)&&!e.isDefaultPrevented()){if((l=e.shiftKey&&86===e.keyCode)&&T.webkit&&-1!==navigator.userAgent.indexOf("Version/"))return;if(e.stopImmediatePropagation(),c.set(e),f.set(!0),T.ie&&l)return e.preventDefault(),t=!0,void s.fire("paste",{ieFake:t});v.remove(),v.create(),s.once("keyup",n),s.once("paste",function(){s.off("keyup",n)})}});var d=function(e,t,n,r,a){var o;fe(t,"text/html")?o=t["text/html"]:(o=v.getHtml(),a=a||B(o),v.isDefaultContent(o)&&(r=!0)),o=K(o),v.remove();var i,s,u,l,c,f,d,m,p=!1===a&&!/<(?:\/?(?!(?:div|p|br|span)>)\w+|(?:(?!(?:span style="white-space:\s?pre;?">)|br\s?\/>))\w+\s[^>]+)>/i.test(o),g=te(e,o);o.length&&(!p||g)||(r=!0),(r||g)&&(o=fe(t,"text/plain")&&p?t["text/plain"]:(i=o,s=q(),u=$({},s),l="",c=s.getShortEndedElements(),f=j.makeMap("script noscript style textarea video audio iframe object"," "),d=s.getBlockElements(),m=function(e){var t=e.name,n=e;if("br"!==t){if("wbr"!==t)if(c[t]&&(l+=" "),f[t])l+=" ";else{if(3===e.type&&(l+=e.value),!e.shortEnded&&(e=e.firstChild))for(;m(e),e=e.next;);d[t]&&n.next&&(l+="\n","p"===t&&(l+="\n"))}}else l+="\n"},i=V(i,[/<!\[[^\]]+\]>/g]),m(u.parse(i)),l)),v.isDefaultContent(o)?n||e.windowManager.alert("Please use Ctrl+V/Cmd+V keyboard shortcuts to paste contents."):r?le(e,o):ue(e,o,a)};s.on("paste",function(e){var t=c.isSet()||f.isSet();t&&c.clear();var n,r,a=(n=s,ce(e.clipboardData||n.getDoc().dataTransfer)),o="text"===u.get()||l,i=fe(a,M);l=!1,!(e.isDefaultPrevented()||(r=e.clipboardData,-1!==navigator.userAgent.indexOf("Android")&&r&&r.items&&0===r.items.length))&&(de(a)||!he(s,e,v.getLastRng()||s.selection.getRng()))?(t||e.preventDefault(),!T.ie||t&&!e.ieFake||fe(a,"text/html")||(v.create(),s.dom.bind(v.getEl(),"paste",function(e){e.stopPropagation()}),s.getDoc().execCommand("Paste",!1,null),a["text/html"]=v.getHtml()),fe(a,"text/html")?(e.preventDefault(),i=i||B(a["text/html"]),d(s,a,t,o,i)):D.setEditorTimeout(s,function(){d(s,a,t,o,i)},0)):v.remove()})},xe=function(i,e,t){var s;be(i,e,t),i.parser.addNodeFilter("img",function(e,t,n){var r,a=function(e){e.attr("data-mce-object")||s===T.transparentSrc||e.remove()};if(!F(i)&&((r=n).data&&!0===r.data.paste))for(var o=e.length;o--;)(s=e[o].attr("src"))&&(0!==s.indexOf("webkit-fake-url")&&(i.getParam("allow_html_data_urls",!1,"boolean")||0!==s.indexOf("data:"))||a(e[o]))})},Pe=function(e){return T.ie&&e.inline?document.body:e.getBody()},we=function(t,e,n){var r;Pe(r=t)!==r.getBody()&&t.dom.bind(e,"paste keyup",function(e){Te(t,n)||t.fire("paste")})},_e=function(e){return e.dom.get("mcepastebin")},Ce=function(e,t){return t===e},Te=function(e,t){var n,r=_e(e);return(n=r)&&"mcepastebin"===n.id&&Ce(t,r.innerHTML)},De=function(e){var t=d(null),n="%MCEPASTEBIN%";return{create:function(){return function(e,t,n){var r=e.dom,a=e.getBody();t.set(e.selection.getRng());var o=e.dom.add(Pe(e),"div",{id:"mcepastebin","class":"mce-pastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: fixed; top: 50%; width: 10px; height: 10px; overflow: hidden; opacity: 0"},n);(T.ie||T.gecko)&&r.setStyle(o,"left","rtl"===r.getStyle(a,"direction",!0)?65535:-65535),r.bind(o,"beforedeactivate focusin focusout",function(e){e.stopPropagation()}),we(e,o,n),o.focus(),e.selection.select(o,!0)}(e,t,n)},remove:function(){return function(e,t){if(_e(e)){for(var n=void 0,r=t.get();n=e.dom.get("mcepastebin");)e.dom.remove(n),e.dom.unbind(n);r&&e.selection.setRng(r)}t.set(null)}(e,t)},getEl:function(){return _e(e)},getHtml:function(){return function(n){var t=function(e,t){e.appendChild(t),n.dom.remove(t,!0)},e=j.grep(Pe(n).childNodes,function(e){return"mcepastebin"===e.id}),r=e.shift();j.each(e,function(e){t(r,e)});for(var a=n.dom.select("div[id=mcepastebin]",r),o=a.length-1;0<=o;o--){var i=n.dom.create("div");r.insertBefore(i,a[o]),t(i,a[o])}return r?r.innerHTML:""}(e)},getLastRng:function(){return t.get()},isDefault:function(){return Te(e,n)},isDefaultContent:function(e){return e===n}}},ke=function(n,r){n.addCommand("mceTogglePlainTextPaste",function(){var e,t;e=n,"text"===(t=r).pasteFormat.get()?(t.pasteFormat.set("html"),O(e,!1)):(t.pasteFormat.set("text"),O(e,!0)),e.focus()}),n.addCommand("mceInsertClipboardContent",function(e,t){t.content&&r.pasteHtml(t.content,t.internal),t.text&&r.pasteText(t.text)})},Se=function(e,t,n){if(r=e,!1!==T.iOS||"function"!=typeof(null==r?void 0:r.setData))return!1;try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(M,t),!0}catch(a){return!1}var r},Oe=function(e,t,n,r){Se(e.clipboardData,t.html,t.text)?(e.preventDefault(),r()):n(t.html,r)},je=function(s){return function(e,t){var n=N+e,r=s.dom.create("div",{contenteditable:"false","data-mce-bogus":"all"}),a=s.dom.create("div",{contenteditable:"true"},n);s.dom.setStyles(r,{position:"fixed",top:"0",left:"-3000px",width:"1000px",overflow:"hidden"}),r.appendChild(a),s.dom.add(s.getBody(),r);var o=s.selection.getRng();a.focus();var i=s.dom.createRng();i.selectNodeContents(a),s.selection.setRng(i),D.setTimeout(function(){s.selection.setRng(o),r.parentNode.removeChild(r),t()},0)}},Re=function(e){return{html:e.selection.getContent({contextual:!0}),text:e.selection.getContent({format:"text"})}},Ae=function(e){return!e.selection.isCollapsed()||!!(t=e).dom.getParent(t.selection.getStart(),"td[data-mce-selected],th[data-mce-selected]",t.getBody());var t},Ie=function(e){var t,n;e.on("cut",(t=e,function(e){Ae(t)&&Oe(e,Re(t),je(t),function(){var e;T.browser.isChrome()||T.browser.isFirefox()?(e=t.selection.getRng(),D.setEditorTimeout(t,function(){t.selection.setRng(e),t.execCommand("Delete")},0)):t.execCommand("Delete")})})),e.on("copy",(n=e,function(e){Ae(n)&&Oe(e,Re(n),je(n),s)}))},Fe=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),Ee=function(e,t){return Fe.getCaretRangeFromPoint(t.clientX,t.clientY,e.getDoc())},Me=function(e,t){e.focus(),e.selection.setRng(t)},Ne=function(i,s,u){i.getParam("paste_block_drop",!1)&&i.on("dragend dragover draggesture dragdrop drop drag",function(e){e.preventDefault(),e.stopPropagation()}),R(i)||i.on("drop",function(e){var t=e.dataTransfer;t&&t.files&&0<t.files.length&&e.preventDefault()}),i.on("drop",function(e){var t,n,r,a,o=Ee(i,e);e.isDefaultPrevented()||u.get()||(t=s.getDataTransferItems(e.dataTransfer),n=s.hasContentType(t,M),(!s.hasHtmlOrText(t)||(r=t["text/plain"])&&0===r.indexOf("file://"))&&s.pasteImageData(e,o)||!o||!i.getParam("paste_filter_drop",!0)||(a=t["mce-internal"]||t["text/html"]||t["text/plain"])&&(e.preventDefault(),D.setEditorTimeout(i,function(){i.undoManager.transact(function(){t["mce-internal"]&&i.execCommand("Delete"),Me(i,o),a=K(a),t["text/html"]?s.pasteHtml(a,n):s.pasteText(a)})})))}),i.on("dragstart",function(e){u.set(!0)}),i.on("dragover dragend",function(e){R(i)&&!1===u.get()&&(e.preventDefault(),Me(i,Ee(i,e))),"dragend"===e.type&&u.set(!1)})},Be=function(t,n){t.on("PastePreProcess",function(e){e.content=n(t,e.content,e.internal,e.wordContent)})},Le=function(e,t){if(!X(t))return t;var n=[];j.each(e.schema.getBlockElements(),function(e,t){n.push(t)});var r=new RegExp("(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*(<\\/?("+n.join("|")+")[^>]*>)(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*","g");return t=V(t,[[r,"$1"]]),t=V(t,[[/<br><br>/g,"<BR><BR>"],[/<br>/g," "],[/<BR><BR>/g,"<br>"]])},He=function(e,t,n,r){if(r||n)return t;var l,c,f,a=e.getParam("paste_webkit_styles");return!1===e.getParam("paste_remove_styles_if_webkit",!0)||"all"===a?t:(a&&(l=a.split(/[, ]/)),t=(t=l?(c=e.dom,f=e.selection.getNode(),t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,function(e,t,n,r){var a=c.parseStyle(c.decode(n)),o={};if("none"===l)return t+r;for(var i=0;i<l.length;i++){var s=a[l[i]],u=c.getStyle(f,l[i],!0);/color/.test(l[i])&&(s=c.toHex(s),u=c.toHex(u)),u!==s&&(o[l[i]]=s)}return(o=c.serializeStyle(o,"span"))?t+' style="'+o+'"'+r:t+r})):t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3")).replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,function(e,t,n,r){return t+' style="'+n+'"'+r}))},$e=function(n,e){n.$("a",e).find("font,u").each(function(e,t){n.dom.remove(t,!0)})},ze=function(n,r){return function(t){t.setActive("text"===r.pasteFormat.get());var e=function(e){return t.setActive(e.state)};return n.on("PastePlainTextToggle",e),function(){return n.off("PastePlainTextToggle",e)}}};i.add("paste",function(e){if(!1==(!!e.hasPlugin("powerpaste",!0)&&("undefined"!=typeof window.console&&window.console.log&&window.console.log("PowerPaste is incompatible with Paste plugin! Remove 'paste' from the 'plugins' option."),!0))){var t=d(!1),n=d(e.getParam("paste_as_text",!1)?"text":"html"),r=(c=n,f=De(l=e),l.on("PreInit",function(){return xe(l,f,c)}),{pasteFormat:c,pasteHtml:function(e,t){return ue(l,e,t)},pasteText:function(e){return le(l,e)},pasteImageData:function(e,t){return he(l,e,t)},getDataTransferItems:ce,hasHtmlOrText:de,hasContentType:fe});i=e,T.webkit&&Be(i,He),T.ie&&(Be(i,Le),u=$e,(s=i).on("PastePostProcess",function(e){u(s,e.node)}));return o=r,(a=e).ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:function(){return a.execCommand("mceTogglePlainTextPaste")},onSetup:ze(a,o)}),a.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:function(){return a.execCommand("mceTogglePlainTextPaste")},onSetup:ze(a,o)}),ke(e,r),function(e){var t=e.plugins.paste,n=e.getParam("paste_preprocess");n&&e.on("PastePreProcess",function(e){n.call(t,t,e)});var r=e.getParam("paste_postprocess");r&&e.on("PastePostProcess",function(e){r.call(t,t,e)})}(e),Ie(e),Ne(e,r,t),{clipboard:r,quirks:void 0}}var a,o,i,s,u,l,c,f})}();
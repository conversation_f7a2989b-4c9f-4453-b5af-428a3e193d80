<?php

namespace app\backend\modules\menu;

use app\common\exceptions\ShopException;
use app\common\helpers\Cache;
use app\common\modules\payType\remittance\models\flows\RemittanceAuditFlow;
use app\common\services\PermissionService;

class Menu
{
    private $currentItems;
    private $items;
    private $pluginsMenu;
    private $mainMenu;
    /**
     * @var self
     */
    static $current;

    /**
     * todo route 改为参数
     * Menu constructor.
     * @throws ShopException
     */
    public function __construct()
    {
        self::$current = $this;
    }

    public function setPluginMenu($key, $value = null)
    {
        if (is_array($key)) {
            $this->pluginsMenu = array_merge($this->pluginsMenu, $key);
        } else {

            array_set($this->pluginsMenu, $key, $value);
        }

    }

    private function _getPluginMenus()
    {
        $plugins = app('plugins')->getEnabledPlugins();
        foreach ($plugins as $plugin) {
            $plugin->app()->loadMenuConfig();
        }
        return $this->pluginsMenu;
    }

    public function getPluginMenus()
    {
        if (!isset($this->pluginsMenu)) {
            $this->pluginsMenu = $this->_getPluginMenus();
        }

        return $this->pluginsMenu;
    }

    /**
     * @return Menu
     * @throws ShopException
     */
    public static function flush()
    {
        self::$current = new static();
        return self::current();
    }

    /**
     * @return menu
     * @throws \app\common\exceptions\ShopException
     */
    public static function current()
    {
        if (!isset(self::$current)) {
            return new static();
        }
        return self::$current;
    }

    /**
     * @return bool|mixed
     */
    public function isShowSecondMenu()
    {
        $menu_list = (array)$this->getItems();

        if (count($this->getCurrentItems()) >= 1) {
            return isset($menu_list[$this->getCurrentItems()[0]]['left_second_show']) ? $menu_list[$this->getCurrentItems()[0]]['left_second_show'] : false;
        }
        return false;
    }

    /**
     * 递归菜单项,设置can字段
     * @param array $menuList
     * @return array
     */
    public static function validateMenuPermit(array $menuList)
    {
        foreach ($menuList as $key => &$item) {
            $item['can'] = true;
            $item['permit'] && ($item['can'] = PermissionService::can($key));
            // 父菜单无权限时,不再验证子菜单权限
            if ($item['can'] && $item['child']) {
                $item['child'] = static::validateMenuPermit($item['child']);
            }
        }

        return $menuList;
    }

    private function mainMenu()
    {
        if (!isset($this->mainMenu)) {

            $this->mainMenu = $this->_mainMenu();
        }
        return $this->mainMenu;
    }

    private function _mainMenu()
    {
        return [
            'index' => [
                'name' => '商城',
                'url' => 'index.index',
                'urlParams' => '',
                'permit' => 0,
                'menu' => 1,
                'icon' => 'fa-home',
                'top_show' => 0,
                'left_first_show' => 0,
                'left_second_show' => 0,
                'parents' => [],
                'item' => 'index',
                'child' => [

                    'index' => [
                        'name' => '选择图标',
                        'url' => 'frame.icon.index',
                        'urlParams' => '',
                        'permit' => 0,
                        'menu' => 0,
                        'icon' => '',
                        'parents' => [],
                    ],

                    'address_get_address' => [
                        'name' => '白名单（选择地址）',
                        'url' => 'address.get-address',
                        'url_params' => '',
                        'permit' => 0,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => 'address_get_address',
                        'parents' => ['index'],
                    ],
                    'address_get_ajax_address' => [
                        'name' => '白名单（选择地址）',
                        'url' => 'address.get-ajax-address',
                        'url_params' => '',
                        'permit' => 0,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => 'address_ajax_get_address',
                        'parents' => ['index'],
                    ],
                    'white_area_list_init' => [
                        'name'       => '获取地址数据(白名单)',
                        'url'        => 'area.list.init',
                        'url_params' => '',
                        'permit'     => 0,
                        'menu'       => 0,
                        'item'       => 'area_list_init',
                        'parents'    => ['index']
                    ],
                    'white_area_list' => [
                        'name'       => '获取地址数据(白名单)',
                        'url'        => 'area.list',
                        'url_params' => '',
                        'permit'     => 0,
                        'menu'       => 0,
                        'item'       => 'area_list',
                        'parents'    => ['index']
                    ],
                    'white_area_list_open-street' => [
                        'name'       => '是否开启街道(白名单)',
                        'url'        => 'area.list.open-street',
                        'url_params' => '',
                        'permit'     => 0,
                        'menu'       => 0,
                        'item'       => 'area_list_open-street',
                        'parents'    => ['index']
                    ],

                    'upload_upload' => [
                        'name' => '上传',
                        'url' => 'upload.upload.upload',
                        'url_params' => '',
                        'permit' => 0,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => '',
                        'parents' => ['index'],
                    ],
                    'upload_fetch' => [
                        'name' => '获取网络路径',
                        'url' => 'upload.upload.fetch',
                        'url_params' => '',
                        'permit' => 0,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => '',
                        'parents' => ['index'],
                    ],
                    'upload_get_image' => [
                        'name' => '图片列表',
                        'url' => 'upload.upload.getImage',
                        'url_params' => '',
                        'permit' => 0,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => '',
                        'parents' => ['index'],
                    ],
                    'upload_get_video' => [
                        'name' => '视频列表',
                        'url' => 'upload.upload.getVideo',
                        'url_params' => '',
                        'permit' => 0,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => '',
                        'parents' => ['index'],
                    ],
                    'upload_delete' => [
                        'name' => '删除',
                        'url' => 'upload.upload.delete',
                        'url_params' => '',
                        'permit' => 0,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => '',
                        'parents' => ['index'],
                    ],
                    'new_upload' => [
                        'name' => '上传 ',
                        'url' => 'setting.shop.newUpload',
                        'url_params' => '',
                        'permit' => 0,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => '',
                        'parents' => ['index'],
                    ],
                    'get_shop_express' => [
                        'name' => '获取快递信息',
                        'url' => 'address.get-ajax-express',
                        'permit' => 0,
                        'menu' => 0,
                        'parents' => ['index'],
                    ],
                ],
            ],

//    'Survey' => [
//        'name'             => '概况',
//        'url'              => 'survey.survey.index',
//        'url_params'       => '',
//        'permit'           => 1,
//        'menu'             => 1,
//        'top_show'         => 0,
//        'left_first_show'  => 1,
//        'icon'             => 'fa-archive',
//        'sort'             => '0',
//        'item'             => 'Survey',
//        'parents'       => [],
//        'left_second_show' => 0,
//
//    ],

            'Goods' => [
                'name' => '商品',
                'url' => 'goods.goods',
                'url_params' => '',
                'permit' => 1,
                'menu' => 1,
                'top_show' => 0,
                'left_first_show' => 1,
                'left_second_show' => 1,
                'icon' => 'fa-archive',
                'sort' => '2',
                'item' => 'Goods',
                'parents' => [],
                'child' => [

                    //添加白名单
                    'goods_no_permission' => [
                        'name' => '白名单（不控制权限）',
                        'url' => '',
                        'url_params' => '',
                        'permit' => 0,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => 'goods_no_permission',
                        'parents' => ['Goods', 'goods_dispatch'],
                        'child' => [
                            'goods_search_order' => [
                                'name' => '白名单（订单商品查询）',
                                'url' => 'goods.goods.search-order',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_search_order',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goods_get_spec_tpl' => [
                                'name' => '白名单（商品规格操作）',
                                'url' => 'goods.goods.getSpecTpl',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_get_spec_tpl',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],

                            'goods_get_spec_item_tpl' => [
                                'name' => '白名单（商品规格操作）',
                                'url' => 'goods.goods.getSpecItemTpl',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_get_spec_item_tpl',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],

                            'area_area_select_city' => [
                                'name' => '白名单（选择城市）',
                                'url' => 'area.area.select-city',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'area_area_select_city',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],

                            'member_member_get_search_member' => [
                                'name' => '白名单（选择通知人）',
                                'url' => 'member.member.get-search-member',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_member_get_search_member',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'member_member_get_search_member_json' => [
                                'name' => '白名单（选择通知人）(接口形式)',
                                'url' => 'member.member.get-search-member-json',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_member_get_search_member_json',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],

                            'coupon_coupon_get_search_coupons' => [
                                'name' => '白名单（选择优惠券）',
                                'url' => 'coupon.coupon.get-search-coupons',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'coupon_coupon_get_search_coupons',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'coupon_coupon_get_search_coupons_v2' => [
                                'name' => '白名单（选择优惠券v2）',
                                'url' => 'coupon.coupon.get-search-coupons-v2',
                                'permit' => 0,
                                'menu' => 0,
                                'sort' => 0,
                                'item' => 'coupon_coupon_get_search_coupons_v2',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            //优惠券白名单
                            'coupon_no_permission' => [
                                'name' => '白名单（指定商品）',
                                'url' => 'coupon.coupon.add-param',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '2',
                                'item' => 'coupon_no_permission',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],


                            'goods_category_get_search_category' => [
                                'name' => '白名单（选择分类）',
                                'url' => 'goods.category.get-search-categorys',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '2',
                                'item' => 'goods_category_get_search_category',
                                'parents' => ['Goods', 'coupon'],
                            ],
                            'goods_category_get_category_json' => [
                                'name' => '白名单（商品选择分类）',
                                'url' => 'goods.category.get-categorys-json',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '2',
                                'item' => 'goods_category_get_search_category',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],

                            'goods_comment_search_goods' => [
                                'name' => '白名单（搜索商品）',
                                'url' => 'goods.comment.search-goods',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'comment_no_permission',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],

                            'goods_comment_search_goods_v2' => [
                                'name' => '白名单（搜索商品V2）',
                                'url' => 'goods.comment.search-goods-v2',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'comment_no_permission',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],

                            'comment_no_permission' => [
                                'name' => '白名单（搜索商品）',
                                'url' => 'goods.goods.get-search-goods',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'comment_no_permission',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goodsGetSearchGoodsLevel' => [
                                'name' => '白名单（搜索商品）',
                                'url' => 'goods.goods.get-search-goods-level',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goodsGetSearchGoodsLevel',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goodsGetSearchGoodsJson' => [
                                'name' => '白名单（搜索商品）',
                                'url' => 'goods.goods.get-search-goods-json',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goodsGetSearchGoodsLevel',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goodsGetSearchGoodsByDividendLevel' => [
                                'name' => '白名单（搜索商品）',
                                'url' => 'goods.goods.get-search-goods-by-dividend-level',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goodsGetSearchGoodsByDividendLevel',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goods_goods_my_ling_goods' => [
                                'name' => '白名单（搜索商品）',
                                'url' => 'goods.goods.getMyLinkGoods',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_my_ling_goods',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'member_member_level_search_goods' => [
                                'name' => '白名单（搜索商品）',
                                'url' => 'member.member-level.searchGoods',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_member_level_search_goods',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goods_goods_getSearchGoodsLevel' => [
                                'name' => '白名单（推广搜索商品）',
                                'url' => 'goods.goods.getSearchGoodsLevel',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_my_ling_goods',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goods_goods_getCategory' => [
                                'name' => '白名单（商品分类链接）',
                                'url' => 'goods.category.getCategoryData',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_my_category',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goods_goods_getSmallCategory' => [
                                'name' => '白名单（商品分类小程序链接）',
                                'url' => 'goods.category.getCategorySmallData',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_my_small_category',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goods_goods_uploadV2_upload' => [
                                'name' => '白名单（商城图片）',
                                'url' => 'upload.uploadV2.upload',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_my_small_category',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goods_goods_uploadV2_uploadDelete' => [
                                'name' => '白名单（删除商城浏览图片记录）',
                                'url' => 'upload.uploadV2.delete',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_my_small_category',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goods_goods_uploadV3_upload' => [
                                'name' => '白名单（商城图片）',
                                'url' => 'upload.uploadV3.upload',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_my_small_category',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goods_goods_uploadV3_getImage' => [
                                'name' => '白名单（商城图片）',
                                'url' => 'upload.uploadV3.getImage',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_my_small_category',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goods_goods_uploadV3_uploadDelete' => [
                                'name' => '白名单（删除商城浏览图片记录）',
                                'url' => 'upload.uploadV3.delete',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_my_small_category',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goods_goods_uploadV3_fetch' => [
                                'name' => '白名单（转换网络图片）',
                                'url' => 'upload.uploadV3.fetch',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_uploadV3_fetch',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'order_return_address_all_address' => [
                                'name' => '白名单（获取订单退货换货地址）',
                                'url' => 'goods.return-address.ajax-all-address',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'sort' => 0,
                                'item' => 'order_return_address_all_address',
                                'parents' => ['Goods', 'goods_no_permission'],
                            ],
                            'goods_search_brand' => [
                                'name' => '白名单（选择搜索品牌）',
                                'url' => 'goods.brand.search-brand',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_search_brand',
                                'parents' => ['Goods', 'goods_no_permission',],
                            ],
                        ],
                    ],
                    'add_goods' => [
                        'name' => '发布商品',
                        'url' => 'goods.goods.create',
                        'url_params' => '',
                        'permit' => 1,
                        'style' => 'pulish',
                        'menu' => 1,
                        'icon' => 'fa-cubes',
                        'sort' => 0,
                        'item' => 'goods_goods',
                        'parents' => ['Goods'],
                        'child' => [

                        ],
                    ],
                    'goods_widget_column' => [
                        'name' => '商品挂件获取接口',
                        'url' => 'goods.goods.widget-column',
                        'permit' => 0,
                        'menu' => 0,
                        'item' => 'goods_goods',
                        'parents' => ['Goods'],
                    ],

                    'goods_goods' => [
                        'name' => '商品列表',
                        'url' => 'goods.goods.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-cubes',
                        'sort' => 0,
                        'item' => 'goods_goods',
                        'parents' => ['Goods'],
                        'child' => [
                            'goods_goods_see' => [
                                'name' => '浏览列表',
                                'url' => 'goods.goods.goods-list',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '22',
                                'item' => 'goods_goods_see',
                                'parents' => ['Goods', 'goods_goods'],
                            ],
                            'goods_goods_get_categorys' => [
                                'name' => '选择搜索分类',
                                'url' => 'goods.category.get-categorys-json',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '22',
                                'item' => 'goods_goods_get_categorys',
                                'parents' => ['Goods', 'goods_goods'],
                            ],
                            'goods_goods_add' => [
                                'name' => '添加商品',
                                'url' => 'goods.goods.create',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'item' => 'goods_goods_add',
                                'parents' => ['Goods', 'goods_goods'],
                            ],
                            'goods_goods_edit' => [
                                'name' => '编辑商品',
                                'url' => 'goods.goods.edit',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_edit',
                                'parents' => ['Goods', 'goods_goods'],
                            ],
                            'goods_goods_destroy' => [
                                'name' => '删除商品',
                                'url' => 'goods.goods.destroy',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_destroy',
                                'parents' => ['Goods', 'goods_goods'],
                            ],
                            'goods_goods_copy' => [
                                'name' => '复制商品',
                                'url' => 'goods.goods.copy',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_copy',
                                'parents' => ['Goods', 'goods_goods'],
                            ],
                            'goods_goods_property' => [
                                'name' => '快捷属性',
                                'url' => 'goods.goods.setProperty',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_property',
                                'parents' => ['Goods', 'goods_goods'],
                            ],
                            'goods_goods_display_order' => [
                                'name' => '修改排序',
                                'url' => 'goods.goods.displayorder',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-circle',
                                'sort' => '23',
                                'item' => 'goods_goods_display_order',
                                'parents' => ['Goods', 'goods_goods'],
                            ],
                            'goods_goods_batch_destroy' => [
                                'name' => '批量删除',
                                'url' => 'goods.goods.batchDestroy',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_batch_destroy',
                                'parents' => ['Goods', 'goods_goods'],
                            ],

//                            'goods_goods_import' => [
//                                'name'       => 'excel导入商品',
//                                'url'        => 'goods.goods.import',
//                                'url_params' => '',
//                                'permit'     => 1,
//                                'menu'       => 1,
//                                'icon'       => '',
//                                'sort'       => 0,
//                                'item'       => 'goods_goods_import',
//                                'parents'    => ['Goods', 'goods_goods'],

//                            ],


                            'goods_goods_change' => [
                                'name' => '列表快捷操作',
                                'url' => 'goods.goods.change',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_goods_change',
                                'parents' => ['Goods', 'goods_goods'],
                            ],
                            'goods_goods_search' => [
                                'name' => '列表快捷操作',
                                'url' => 'goods.goods.goods-search',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'item' => 'goods_goods_search',
                                'parents' => ['Goods', 'goods_goods'],
                            ],
                            'generate_small_code' => [
                                'name' => '小程序二维码',
                                'url' => 'goods.goods.generate-small-code',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'item' => 'generate_small_code',
                                'parents' => ['Goods', 'goods_goods'],
                            ],
                        ],
                    ],
                    'goods_goods_putaway' => [
                        'name' => '商品上架',
                        'url' => 'goods.goods.setPutaway',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => 'goods_goods_putaway',
                        'parents' => ['Goods'],
                    ],
                    'goods_goods_generate' => [
                        'name' => '小程序',
                        'url' => 'goods.goods.generate-small-code',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => 'goods_goods_generate',
                        'parents' => ['Goods'],
                    ],

                    'goods_goods_batch_property' => [
                        'name' => '批量上下架',
                        'url' => 'goods.goods.batchSetProperty',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => 'goods_goods_batch_property',
                        'parents' => ['Goods'],
                    ],

                    'goods_div_from' => [
                        'name' => '商品表单',
                        'url' => 'from.div-from.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-file-excel-o',
                        'sort' => '2',
                        'item' => 'goods_div_from',
                        'parents' => ['Goods'],
                        'child' => [
                            'div_from_get_data' => [
                                'name' => '获取数据',
                                'url' => 'from.div-from.get-data',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '2',
                                'item' => 'div_from_get_data',
                                'parents' => ['Goods', 'goods_div_from'],
                            ],
                            'goods_div_from_see' => [
                                'name' => '查看内容',
                                'url' => 'from.div-from.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '2',
                                'item' => 'goods_div_from_see_one',
                                'parents' => ['Goods', 'goods_div_from'],
                                'child' => [
                                    'goods_div_from_see' => [
                                        'name' => '查看内容',
                                        'url' => 'from.div-from.index',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => '2',
                                        'item' => 'goods_div_from_see',
                                        'parents' => ['Goods', 'goods_div_from', 'goods_div_from_see_one'],
                                    ],
                                    'goods_div_from_store' => [
                                        'name' => '保存设置',
                                        'url' => 'from.div-from.store',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => '2',
                                        'item' => 'goods_div_from_store',
                                        'parents' => ['Goods', 'goods_div_from', 'goods_div_from_see_one'],
                                    ],
                                ],
                            ],

                        ],
                    ],

                    'goods_category' => [
                        'name' => '商品分类',
                        'url' => 'goods.category.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-sitemap',
                        'sort' => '2',
                        'item' => 'goods_category',
                        'parents' => ['Goods'],
                        'child' => [
                            'goods_category_category_info' => [
                                'name' => '修改页面渲染',
                                'url' => 'goods.category.category-info',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-plus',
                                'sort' => 0,
                                'item' => 'goods_category_category_info',
                                'parents' => ['Goods', 'goods_category'],
                            ],

                            'goods_category_get_search_label_v2' => [
                                'name' => '选择标签',
                                'url' => 'filtering.filtering.get-search-label-v2',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-plus',
                                'sort' => 0,
                                'item' => 'goods_category_category_info',
                                'parents' => ['Goods', 'goods_category'],
                            ],
                            'goods_category_see' => [
                                'name' => '浏览分类',
                                'url' => 'goods.category.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-plus',
                                'sort' => 0,
                                'item' => 'goods_category_see',
                                'parents' => ['Goods', 'goods_category'],
                            ],

                            'goods_category_add' => [
                                'name' => '添加分类',
                                'url' => 'goods.category.add-category',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-plus',
                                'sort' => 0,
                                'item' => 'goods_category_add',
                                'parents' => ['Goods', 'goods_category'],
                            ],

                            'goods_category_edit' => [
                                'name' => '修改分类',
                                'url' => 'goods.category.edit-category',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-edit',
                                'sort' => '2',
                                'item' => 'goods_category_edit',
                                'parents' => ['Goods', 'goods_category']
                            ],

                            'goods_category_delete' => [
                                'name' => '删除分类',
                                'url' => 'goods.category.deleted-category',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sliders',
                                'sort' => '3',
                                'item' => 'goods_category_delete',
                                'parents' => ['Goods', 'goods_category'],
                            ],

                            'many_goods_category_see' => [
                                'name' => '获取所有分类(批量修改用)',
                                'url' => 'goods.category.get-all-shop-category',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-plus',
                                'sort' => 0,
                                'item' => 'many_goods_category_see',
                                'parents' => ['Goods', 'goods_category'],
                            ],

                            'many_goods_category_edit' => [
                                'name' => '批量修改商品分类',
                                'url' => 'goods.category.change-many-goods-category',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-edit',
                                'sort' => 0,
                                'item' => 'many_goods_category_edit',
                                'parents' => ['Goods', 'goods_category'],
                            ],

                            'goods_category_batch_display' => [
                                'name' => '批量更新商品分类显示',
                                'url' => 'goods.category.batch-display',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-edit',
                                'sort' => 0,
                                'item' => 'goods_category_batch_display',
                                'parents' => ['Goods', 'goods_category'],
                            ],

                            'goods_category_batch_recommend' => [
                                'name' => '批量更新商品分类推荐',
                                'url' => 'goods.category.batch-recommend',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-edit',
                                'sort' => 0,
                                'item' => 'goods_category_batch_recommend',
                                'parents' => ['Goods', 'goods_category'],
                            ],

                        ],
                    ],

                    'goods_brand' => [
                        'name' => '品牌管理',
                        'url' => 'goods.brand.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-briefcase',
                        'sort' => '3',
                        'item' => 'goods_brand',
                        'parents' => ['Goods', 'goods_brand'],
                        'child' => [
                            'brand_brand_data' => [
                                'name' => '获取数据',
                                'url' => 'goods.brand.brand-data',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '3',
                                'item' => 'brand_brand_data',
                                'parents' => ['Goods'],
                            ],
                            'upload_v2_get_image' => [
                                'name' => '获取图片',
                                'url' => 'upload.uploadV2.get-image',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '3',
                                'item' => 'upload_v2_get_image',
                                'parents' => ['Goods'],
                            ],
                            'upload_v2_fetch' => [
                                'name' => '转换图片',
                                'url' => 'upload.uploadV2.fetch',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '3',
                                'item' => 'upload_v2_fetch',
                                'parents' => ['Goods'],
                            ],
                            'goods_brand_see' => [
                                'name' => '浏览品牌',
                                'url' => 'goods.brand.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '3',
                                'item' => 'goods_brand',
                                'parents' => ['Goods'],
                            ],

                            'goods_brand_add' => [
                                'name' => '添加品牌',
                                'url' => 'goods.brand.add',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_brand_add',
                                'parents' => ['Goods', 'goods_brand'],
                            ],

                            'goods_brand_edit' => [
                                'name' => '修改品牌',
                                'url' => 'goods.brand.edit',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '2',
                                'item' => 'goods_brand_edit',
                                'parents' => ['Goods', 'goods_brand'],
                            ],

                            'goods_brand_edit_view' => [
                                'name' => '页面渲染',
                                'url' => 'goods.brand.edit-viwe',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '2',
                                'item' => 'goods_brand_edit_view',
                                'parents' => ['Goods', 'goods_brand'],
                            ],

                            'goods_brand_delete' => [
                                'name' => '删除品牌',
                                'url' => 'goods.brand.deleted-brand',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '3',
                                'item' => 'goods_brand_delete',
                                'parents' => ['Goods', 'goods_brand'],
                            ],

                            'goods_brand_batch_recommend' => [
                                'name' => '批量更新品牌推荐',
                                'url' => 'goods.brand.batch-recommend',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_brand_batch_recommend',
                                'parents' => ['Goods', 'goods_brand'],
                            ],
                        ],
                    ],

                    'goods_dispatch' => [
                        'name' => '配送模板',
                        'url' => 'goods.dispatch.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-truck',
                        'sort' => '4',
                        'item' => 'goods_dispatch.index',
                        'parents' => ['Goods'],
                        'child' => [
                            'dispatch_dispatch_data' => [
                                'name' => '获取数据',
                                'url' => 'goods.dispatch.dispatch-data',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '4',
                                'item' => 'dispatch_dispatch_data',
                                'parents' => ['Goods', 'goods_dispatch']
                            ],
                            'goods_dispatch_edit_save' => [
                                'name' => '配送模板页面渲染',
                                'url' => 'goods.dispatch.edit-save',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '4',
                                'item' => 'goods_dispatch_edit_save',
                                'parents' => ['Goods', 'goods_dispatch']
                            ],
                            'goods_dispatch_see' => [
                                'name' => '浏览列表',
                                'url' => 'goods.dispatch.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '4',
                                'item' => 'goods_dispatch_see',
                                'parents' => ['Goods', 'goods_dispatch']
                            ],

                            'goods_dispatch_quick_edit' => [
                                'name' => '快速修改',
                                'url' => 'goods.dispatch.quick-edit',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '4',
                                'item' => 'goods_dispatch_quick_edit',
                                'parents' => ['Goods', 'goods_dispatch']
                            ],

                            'goods_dispatch_sort' => [
                                'name' => '修改排序',
                                'url' => 'goods.dispatch.sort',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '4',
                                'item' => 'goods_dispatch_sort',
                                'parents' => ['Goods', 'goods_dispatch']
                            ],

                            'goods_dispatch_add_one' => [
                                'name' => '添加模板',
                                'url' => 'goods.dispatch.add',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_dispatch_add_one',
                                'parents' => ['Goods', 'goods_dispatch'],
                            ],

                            'goods_dispatch_alter' => [
                                'name' => '修改模板',
                                'url' => 'goods.dispatch.edit',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_dispatch_alter',
                                'parents' => ['Goods', 'goods_dispatch'],
                            ],

                            'goods_dispatch_delete' => [
                                'name' => '删除模板',
                                'url' => 'goods.dispatch.delete',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_dispatch_delete',
                                'parents' => ['Goods', 'goods_dispatch'],
                            ],
                        ],
                    ],

                    'comment' => [
                        'name' => '评论管理',
                        'url' => 'goods.comment.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-columns',
                        'sort' => '5',
                        'item' => 'comment',
                        'parents' => ['Goods'],
                        'child' => [
                            'comment_comment_set' => [
                                'name' => '基础设置页面',
                                'url' => 'goods.comment.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => '',
                                'parents' => ['Goods', 'comment'],
                            ],
                            'comment_comment_save_set' => [
                                'name' => '保存基础设置',
                                'url' => 'goods.comment.saveSet',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => '',
                                'parents' => ['Goods', 'comment'],
                            ],
                            'comment_comment_audit' => [
                                'name' => '审核列表',
                                'url' => 'goods.comment.audit',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => '',
                                'parents' => ['Goods', 'comment'],
                            ],
                            'comment_comment_list' => [
                                'name' => '评论列表',
                                'url' => 'goods.comment.list',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => '',
                                'parents' => ['Goods', 'comment'],
                            ],
                            'comment_comment_data' => [
                                'name' => '获取数据',
                                'url' => 'goods.comment.comment-data',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'comment_comment_data',
                                'parents' => ['Goods', 'comment'],
                            ],
                            'goods_comment_edit_view' => [
                                'name' => '渲染评论页面',
                                'url' => 'goods.comment.edit-view',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_comment_edit_view',
                                'parents' => ['Goods', 'comment'],
                            ],

                            'goods_comment_add' => [
                                'name' => '添加评价',
                                'url' => 'goods.comment.add-comment',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_comment_add',
                                'parents' => ['Goods', 'comment'],
                            ],


                            'goods_comment_updated' => [
                                'name' => '修改评价',
                                'url' => 'goods.comment.updated',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_comment_updated',
                                'parents' => ['Goods', 'comment'],
                            ],

                            'goods_comment_reply' => [
                                'name' => '回复评价',
                                'url' => 'goods.comment.reply',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_comment_reply',
                                'parents' => ['Goods', 'comment'],
                            ],
                            'goods_comment_reply_view' => [
                                'name' => '回复评价视图',
                                'url' => 'goods.comment.reply-view',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_comment_reply_view',
                                'parents' => ['Goods', 'comment'],
                            ],

//                            'goods_comment_update' => [
//                                'name'       => '修改评价',
//                                'url'        => 'goods.comment.update',
//                                'url_params' => '',
//                                'permit'     => 1,
//                                'menu'       => 0,
//                                'icon'       => '',
//                                'sort'       => 0,
//                                'item'       => 'goods_comment_update',
//                                'parents'    => ['Goods', 'comment'],
//                            ],

                            'goods_comment_delete' => [
                                'name' => '删除评价',
                                'url' => 'goods.comment.deleted',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_comment_delete',
                                'parents' => ['Goods', 'comment'],
                            ],
                            'goods_comment_changeCommentStatus' => [
                                'name' => '更改评价状态',
                                'url' => 'goods.comment.changeCommentStatus',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => '',
                                'parents' => ['Goods', 'comment'],
                            ],
                            'goods_comment_changeAuditStatus' => [
                                'name' => '修改审核状态',
                                'url' => 'goods.comment.changeAuditStatus',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => '',
                                'parents' => ['Goods', 'comment'],
                            ],
                        ],
                    ],

                    'coupon' => [
                        'name' => '优惠券管理',
                        'url' => 'coupon.coupon.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-tags',
                        'sort' => '6',
                        'item' => 'coupon',
                        'parents' => ['Goods'],
                        'child' => [
                            'coupon_coupon_coupon_view' => [
                                'name' => '优惠券渲染页面',
                                'url' => 'coupon.coupon.coupon-view',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-ticket',
                                'sort' => '2',
                                'item' => 'coupon_coupon_coupon_view',
                                'parents' => ['Goods', 'coupon'],
                                'child' => []
                            ],
                            'coupon_coupon_log_view' => [
                                'name' => '优惠券领取发放记录渲染页面',
                                'url' => 'coupon.coupon.log-view',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-ticket',
                                'sort' => '2',
                                'item' => 'coupon_coupon_log_view',
                                'parents' => ['Goods', 'coupon'],
                                'child' => []
                            ],

                            'coupon_coupon_set_see_data' => [
                                'name' => '渲染数据',
                                'url' => 'coupon.base-set.see-data',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-ticket',
                                'sort' => '2',
                                'item' => 'coupon_coupon_set_see',
                                'parents' => ['Goods', 'coupon', 'coupon_coupon_set'],
                            ],
                            'share_log_data' => [
                                'name' => '渲染数据',
                                'url' => 'coupon.share-coupon.share-log-data',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-ticket',
                                'sort' => '2',
                                'item' => 'coupon_coupon_set_see',
                                'parents' => ['Goods', 'coupon', 'coupon_coupon_set'],
                            ], 'coupon_coupon_index_data' => [
                                'name' => '优惠券列表获取数据',
                                'url' => 'coupon.coupon.coupon-data',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => 1,
                                'item' => 'coupon_coupon_index',
                                'parents' => ['Goods', 'coupon', 'coupon_coupon_index'],
                            ],
                            'coupon_get_search_categorys_json' => [
                                'name' => '查询分类',
                                'url' => 'goods.category.get-search-categorys-json',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => 1,
                                'item' => 'coupon_coupon_index',
                                'parents' => ['Goods', 'coupon', 'coupon_coupon_index'],
                            ],

                            'coupon_get_search_goods_json' => [
                                'name' => '查询商品',
                                'url' => 'goods.goods.get-search-goods-json',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => 1,
                                'item' => 'coupon_coupon_index',
                                'parents' => ['Goods', 'coupon', 'coupon_coupon_index'],
                            ],
                            'coupon_get_search_store_json' => [
                                'name' => '查询门店',
                                'url' => 'goods.goods.get-search-store-json',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => 1,
                                'item' => 'coupon_coupon_index',
                                'parents' => ['Goods', 'coupon', 'coupon_coupon_index'],
                            ],
                            'coupon_get_search_hotel_json' => [
                                'name' => '查询酒店',
                                'url' => 'goods.goods.get-search-hotel-json',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => 1,
                                'item' => 'coupon_coupon_index',
                                'parents' => ['Goods', 'coupon', 'coupon_coupon_index'],
                            ],
                            'coupon_send_data' => [
                                'name' => '发放优惠卷',
                                'url' => 'coupon.send-coupon.send-data',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => 1,
                                'item' => 'coupon_coupon_index',
                                'parents' => ['Goods', 'coupon', 'coupon_coupon_index'],
                            ],

                            'coupon_coupon_set' => [
                                'name' => '优惠券设置',
                                'url' => 'coupon.base-set.see',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-ticket',
                                'sort' => '2',
                                'item' => 'coupon_coupon_set',
                                'parents' => ['Goods', 'coupon'],
                                'child' => [

                                    'coupon_coupon_set_see' => [
                                        'name' => '查看设置',
                                        'url' => 'coupon.base-set.see',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-ticket',
                                        'sort' => '2',
                                        'item' => 'coupon_coupon_set_see',
                                        'parents' => ['Goods', 'coupon', 'coupon_coupon_set'],
                                    ],

                                    'coupon_coupon_set_store' => [
                                        'name' => '保存设置',
                                        'url' => 'coupon.base-set.store',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-ticket',
                                        'sort' => '2',
                                        'item' => 'coupon_coupon_set_store',
                                        'parents' => ['Goods', 'coupon', 'coupon_coupon_set'],
                                    ],

                                    'coupon_notice_set_see' => [
                                        'name' => '通知开启',
                                        'url' => 'setting.default-notice.store',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-ticket',
                                        'sort' => '2',
                                        'item' => 'coupon_notice_set_see',
                                        'parents' => ['Goods', 'coupon', 'coupon_coupon_set'],
                                    ],

                                    'coupon_notice_set_close' => [
                                        'name' => '通知关闭',
                                        'url' => 'setting.default-notice.storeCancel',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-ticket',
                                        'sort' => '2',
                                        'item' => 'coupon_notice_set_close',
                                        'parents' => ['Goods', 'coupon', 'coupon_coupon_set'],
                                    ],
                                ],
                            ],

                            'coupon_coupon_create' => [
                                'name' => '创建优惠券',
                                'url' => 'coupon.coupon.create',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-ticket',
                                'sort' => '2',
                                'item' => 'coupon_coupon_create',
                                'parents' => ['Goods', 'coupon'],
                            ],

                            'coupon_coupon_edit' => [
                                'name' => '编辑优惠券',
                                'url' => 'coupon.coupon.edit',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'coupon_coupon_edit',
                                'parents' => ['Goods', 'coupon'],
                            ],

                            'coupon_coupon_destroy' => [
                                'name' => '删除优惠券',
                                'url' => 'coupon.coupon.destory',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'coupon_coupon_destory',
                                'parents' => ['Goods', 'coupon'],
                            ],

                            'coupon_send_coupon' => [
                                'name' => '发放优惠券',
                                'url' => 'coupon.send-coupon.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'coupon_send_coupon',
                                'parents' => ['Goods', 'coupon'],
                            ],

                            'coupon_coupon_index' => [
                                'name' => '优惠券列表',
                                'url' => 'coupon.coupon.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-list-ul',
                                'sort' => 1,
                                'item' => 'coupon_coupon_index',
                                'parents' => ['Goods', 'coupon'],
                            ],

                            'coupon_coupon_increment_record' => [
                                'name' => '增加优惠券数',
                                'url' => 'coupon.coupon.add-coupon-count',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-ticket',
                                'sort' => '2',
                                'item' => 'coupon_coupon_increment_record',
                                'parents' => ['Goods', 'coupon'],
                            ],

                            'coupon_coupon_log' => [
                                'name' => '领取发放记录',
                                'url' => 'coupon.coupon.log',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-pencil',
                                'sort' => '3',
                                'item' => 'coupon_coupon_log',
                                'parents' => ['Goods', 'coupon'],
                            ],
                            'share_coupon_log' => [
                                'name' => '分享领取记录',
                                'url' => 'coupon.share-coupon.log',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-pencil',
                                'sort' => '3',
                                'item' => 'share_coupon_log',
                                'parents' => ['Goods', 'coupon'],
                            ],
                            'coupon_use_log' => [
                                'name' => '使用记录',
                                'url' => 'coupon.coupon-use.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-pencil',
                                'sort' => '3',
                                'item' => 'coupon_use_log',
                                'parents' => ['Goods', 'coupon'],
                            ],
                            'coupon_use_log_search' => [
                                'name' => '使用记录分页',
                                'url' => 'coupon.coupon-use.log',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-pencil',
                                'sort' => '3',
                                'item' => 'coupon_use_log_search',
                                'parents' => ['Goods', 'coupon'],
                            ],
                            'coupon_slide_show' => [
                                'name' => '领券中心幻灯片',
                                'url' => 'coupon.slide-show.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-pencil',
                                'sort' => '3',
                                'item' => 'coupon_slide_show',
                                'parents' => ['Goods', 'coupon'],
                            ],
                            'coupon_slide_show_search' => [
                                'name' => '领券中心幻灯片分页',
                                'url' => 'coupon.slide-show.search',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-pencil',
                                'sort' => '3',
                                'item' => 'coupon_slide_show_search',
                                'parents' => ['Goods', 'coupon'],
                            ],
                            'coupon_slide_show_add' => [
                                'name' => '新增幻灯片',
                                'url' => 'coupon.slide-show.add',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-pencil',
                                'sort' => '3',
                                'item' => 'coupon_slide_show_add',
                                'parents' => ['Goods', 'coupon'],
                            ],
                            'coupon_slide_show_edit' => [
                                'name' => '编辑幻灯片',
                                'url' => 'coupon.slide-show.edit',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-pencil',
                                'sort' => '3',
                                'item' => 'coupon_slide_show_edit',
                                'parents' => ['Goods', 'coupon'],
                            ],
                            'coupon_slide_show_del' => [
                                'name' => '删除幻灯片',
                                'url' => 'coupon.slide-show.del',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-pencil',
                                'sort' => '3',
                                'item' => 'coupon_slide_show_del',
                                'parents' => ['Goods', 'coupon'],
                            ],
                            'coupon_slide_show_edit_sort' => [
                                'name' => '幻灯片修改排序',
                                'url' => 'coupon.slide-show.edit-sort',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-pencil',
                                'sort' => '3',
                                'item' => 'coupon_slide_show_edit_sort',
                                'parents' => ['Goods', 'coupon'],
                            ],
                            'getSearchStore' => [
                                'name' => '搜索门店',
                                'url' => 'goods.goods.get-search-store',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-pencil',
                                'sort' => '3',
                                'item' => 'coupon_coupon_log',
                                'parents' => ['Goods', 'coupon'],
                            ],
                            'getSearchHotel' => [
                                'name' => '搜索酒店',
                                'url' => 'goods.goods.get-search-hotel',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-pencil',
                                'sort' => '3',
                                'item' => 'coupon_coupon_log',
                                'parents' => ['Goods', 'coupon'],
                            ],
                        ],
                    ],

                    /**
                     * 搜索过滤 改名为 商品标签
                     * create 2018/3/26
                     * update 2018/5/14
                     * Author: blank
                     */
                    'search_filtering' => [
                        'name' => '商品标签',
                        'url' => 'filtering.filtering.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-sitemap',
                        'sort' => '6',
                        'item' => 'search_filtering',
                        'parents' => ['Goods'],
                        'child' => [
                            'filtering_filtering_data' => [
                                'name' => '获取数据',
                                'url' => 'filtering.filtering.filtering-data',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'filtering_filtering_data',
                                'parents' => ['Goods', 'search_filtering'],
                                'child' => []
                            ],
                            'filtering_filtering_list' => [
                                'name' => '获取子级数据',
                                'url' => 'filtering.filtering.filter-list',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'filtering_filtering_list',
                                'parents' => ['Goods', 'search_filtering'],
                                'child' => []
                            ],
                            'filtering_filtering_set_open' => [
                                'name' => '获取子级数据-设置',
                                'url' => 'filtering.filtering.setOpen',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'filtering_filtering_set_open',
                                'parents' => ['Goods', 'search_filtering'],
                                'child' => []
                            ],
                            'filtering_edit_view' => [
                                'name' => '渲染标签页面',
                                'url' => 'filtering.filtering.edit-view',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'filtering_edit_view',
                                'parents' => ['Goods', 'search_filtering'],
                                'child' => []
                            ],
                            'filtering_search' => [
                                'name' => '标签组列表',
                                'url' => 'filtering.filtering.get-search-label',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'filtering_search',
                                'parents' => ['Goods', 'search_filtering'],
                                'child' => []
                            ],
                            'filtering_group_index' => [
                                'name' => '标签组列表',
                                'url' => 'filtering.filtering.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'filtering_group_index',
                                'parents' => ['Goods', 'search_filtering'],
                                'child' => []
                            ],
                            'filtering_group_get_set' => [
                                'name' => '标签组列表',
                                'url' => 'filtering.filtering.getSet',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'sort' => '0',
                                'item' => 'filtering_group_index',
                                'parents' => ['Goods', 'search_filtering'],
                                'child' => []
                            ],
                            'filtering_group_save_set' => [
                                'name' => '商品标签保存',
                                'url' => 'filtering.filtering.saveSet',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'sort' => '0',
                                'item' => 'filtering_group_index',
                                'parents' => ['Goods', 'search_filtering'],
                                'child' => []
                            ],
                            'filtering_value_index' => [
                                'name' => '标签列表',
                                'url' => 'filtering.filtering.filter-value',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '2',
                                'item' => 'filtering_value_index',
                                'parents' => ['Goods', 'search_filtering'],
                                'child' => []
                            ],
                            'filtering_create' => [
                                'name' => '新增',
                                'url' => 'filtering.filtering.create',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '4',
                                'item' => 'goods_return_sort',
                                'parents' => ['Goods', 'search_filtering']
                            ],

                            'filtering_edit' => [
                                'name' => '编辑',
                                'url' => 'filtering.filtering.edit',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_return_add_one',
                                'parents' => ['Goods', 'search_filtering'],
                            ],

                            'filtering_del' => [
                                'name' => '删除',
                                'url' => 'filtering.filtering.del',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_return_alter',
                                'parents' => ['Goods', 'search_filtering'],
                            ],
                        ],
                    ],

                    'goods_return' => [
                        'name' => '退货地址设置',
                        'url' => 'goods.return-address.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-truck',
                        'sort' => '6',
                        'item' => 'coupon',
                        'parents' => ['Goods'],
                        'child' => [
                            'return-address-list' => [
                                'name' => '获取数据',
                                'url' => 'goods.return-address.return-address-list',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '4',
                                'item' => 'return-address-list',
                                'parents' => ['Goods', 'goods_return']
                            ],
                            'area_list_init' => [
                                'name' => '获取地址数据',
                                'url' => 'area.list.init',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '4',
                                'item' => 'area_list_init',
                                'parents' => ['Goods', 'goods_return']
                            ],
                            'area_list' => [
                                'name' => '获取地址数据',
                                'url' => 'area.list',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '4',
                                'item' => 'area_list',
                                'parents' => ['Goods', 'goods_return']
                            ],
                            'goods_return_edit_view' => [
                                'name' => '渲染退货地址页面',
                                'url' => 'goods.return-address.edit-view',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '4',
                                'item' => 'goods_return_edit_view',
                                'parents' => ['Goods', 'goods_return']
                            ],
                            'goods_return_see' => [
                                'name' => '浏览列表',
                                'url' => 'goods.return-address.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '4',
                                'item' => 'goods_return_see',
                                'parents' => ['Goods', 'goods_return']
                            ],
                            'goods_return_is_default' => [
                                'name' => '快速修改默认',
                                'url' => 'goods.return-address.is-default',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '4',
                                'item' => 'goods_return_see',
                                'parents' => ['Goods', 'goods_return']
                            ],

                            'goods_return_sort' => [
                                'name' => '修改排序',
                                'url' => 'goods.return-address.sort',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '4',
                                'item' => 'goods_return_sort',
                                'parents' => ['Goods', 'goods_return']
                            ],

                            'goods_return_add_one' => [
                                'name' => '添加模板',
                                'url' => 'goods.return-address.add',
                                'sort' => '2',
                                'item' => 'filtering_create',
                                'parents' => ['Goods', 'goods_return'],
                                'child' => []
                            ],
                            'goods_return_alter' => [
                                'name' => '修改模板',
                                'url' => 'goods.return-address.edit',
                                'sort' => '2',
                                'item' => 'filtering_edit',
                                'parents' => ['Goods', 'goods_return'],
                                'child' => []
                            ],
                            'goods_return_delete' => [
                                'name' => '删除模板',
                                'url' => 'goods.return-address.delete',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'goods_return_delete',
                                'parents' => ['Goods', 'goods_return'],
                            ],
                        ],

                    ],
                    'enough_reduce' => [
                        'name' => '满额优惠',
                        'url' => 'enoughReduce.index.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-gift',
                        'sort' => '6',
                        'item' => 'enough_reduce',
                        'parents' => ['Goods'],
                        'child' => [
                            'filtering_group_index' => [
                                'name' => '满额优惠设置',
                                'url' => 'enoughReduce.index.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_index',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'filtering_group_store' => [
                                'name' => '保存满额优惠',
                                'url' => 'enoughReduce.store.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_store',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'area_list' => [
                                'name' => '选择地区',
                                'url' => 'area.list.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'area_list',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'postage_included_category_index' => [
                                'name' => '包邮分类列表',
                                'url' => 'enoughReduce.postage-included-category.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_index',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'postage_included_category_generate' => [
                                'name' => '包邮分类-创建视图',
                                'url' => 'enoughReduce.postage-included-category.generate',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_index',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'postage_included_category_generation' => [
                                'name' => '创建包邮分类',
                                'url' => 'enoughReduce.postage-included-category.generation',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_index',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'postage_included_category_records' => [
                                'name' => '包邮分类列表数据',
                                'url' => 'enoughReduce.postage-included-category.records',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_index',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'postage_included_category_record' => [
                                'name' => '包邮分类单个数据',
                                'url' => 'enoughReduce.postage-included-category.record',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_index',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'postage_included_category_update' => [
                                'name' => '包邮分类更新',
                                'url' => 'enoughReduce.postage-included-category.update',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_index',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'postage_included_category_update' => [
                                'name' => '包邮分类更新',
                                'url' => 'enoughReduce.postage-included-category.update',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_index',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'postage_included_category_destroy' => [
                                'name' => '删除包邮分类',
                                'url' => 'enoughReduce.postage-included-category.destroy',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_index',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'postage_included_category_goods_destroy' => [
                                'name' => '删除包邮分类商品',
                                'url' => 'enoughReduce.postage-included-category.goods-destroy',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_index',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'postage_included_category_search_goods' => [
                                'name' => '搜索商品',
                                'url' => 'enoughReduce.postage-included-category.search-goods',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_index',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'full_piece_index' => [
                                'name' => '满件优惠设置',
                                'url' => 'enoughReduce.full-piece.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_index',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'full_piece_store' => [
                                'name' => '保存满件优惠',
                                'url' => 'enoughReduce.full-piece.store',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_store',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                            'full_piece_get_goods' => [
                                'name' => '满件优惠获取商品',
                                'url' => 'enoughReduce.full-piece.get-goods',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sitemap',
                                'sort' => '2',
                                'item' => 'enough_reduce_store',
                                'parents' => ['Goods'],
                                'child' => []
                            ],
                        ],
                    ],

                    'discount_set' => [
                        'name' => '批量操作',
                        'url' => 'discount.batch-discount.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-gift',
                        'sort' => '6',
                        'item' => 'discount_set',
                        'parents' => ['Goods'],
                        'child' => [
                            'goods_discount_set_all' => [
                                'name' => '折扣全局设置',
                                'url' => 'discount.batch-discount.allSet',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '2',
                                'item' => 'goods_discount_set_all',
                                'parents' => ['Goods', 'discount_set'],
                                'child' => [
                                    'goods_discount_set_all_index' => [
                                        'name' => '折扣设置',
                                        'url' => 'discount.batch-discount.index',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-sitemap',
                                        'sort' => '2',
                                        'item' => 'goods_discount_set_all_index',
                                        'parents' => ['Goods', 'discount_set', 'goods_discount_set_all'],
                                        'child' => []
                                    ],
                                    'goods_discount_set_all__store' => [
                                        'name' => '保存设置',
                                        'url' => 'discount.batch-discount.store',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-sitemap',
                                        'sort' => '2',
                                        'item' => 'goods_discount_set_all__store',
                                        'parents' => ['Goods', 'discount_set', 'goods_discount_set_all'],
                                        'child' => []
                                    ],
                                    'goods_discount_set_all_set' => [
                                        'name' => '保存折扣全局设置',
                                        'url' => 'discount.batch-discount.all-set',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-sitemap',
                                        'sort' => '2',
                                        'item' => 'goods_discount_set_all_set',
                                        'parents' => ['Goods', 'discount_set', 'goods_discount_set_all'],
                                        'child' => []
                                    ],
                                ],
                            ],

                            'goods_discount_set' => [
                                'name' => '折扣设置',
                                'url' => 'discount.batch-discount.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '2',
                                'item' => 'goods_discount_set',
                                'parents' => ['Goods', 'discount_set'],
                                'child' => [
                                    'goods_discount_set_store' => [
                                        'name' => '保存设置',
                                        'url' => 'discount.batch-discount.store',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-sitemap',
                                        'sort' => '2',
                                        'item' => 'goods_discount_set_store',
                                        'parents' => ['Goods', 'discount_set', 'goods_discount_set'],
                                        'child' => []
                                    ],
                                    'goods_discount_set_edit' => [
                                        'name' => '编辑设置',
                                        'url' => 'discount.batch-discount.update-set',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-sitemap',
                                        'sort' => '2',
                                        'item' => 'goods_discount_set_edit',
                                        'parents' => ['Goods', 'discount_set', 'goods_discount_set'],
                                        'child' => []
                                    ],
                                    'goods_discount_set' => [
                                        'name' => '编辑页面',
                                        'url' => 'discount.discount.set',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-sitemap',
                                        'sort' => '2',
                                        'item' => 'goods_discount_set_update',
                                        'parents' => ['Goods', 'discount_set', 'goods_discount_set'],
                                        'child' => []
                                    ],
                                    'goods_dispatch_select_category' => [
                                        'name' => '分类查询',
                                        'url' => 'discount.batch-discount.select-category',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-sitemap',
                                        'sort' => '2',
                                        'item' => 'goods_dispatch_select_category',
                                        'parents' => ['Goods', 'discount_set', 'goods_discount_set'],
                                        'child' => []
                                    ],
                                    'goods_dispatch_delete_set' => [
                                        'name' => '删除设置',
                                        'url' => 'discount.batch-discount.delete-set',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-sitemap',
                                        'sort' => '2',
                                        'item' => 'goods_dispatch_delete_set',
                                        'parents' => ['Goods', 'discount_set', 'goods_discount_set'],
                                        'child' => []
                                    ],
                                ],
                            ],

                            'goods_dispatch_freight' => [
                                'name' => '运费批量设置',
                                'url' => 'discount.batch-dispatch.freight',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '2',
                                'item' => 'goods_dispatch_freight-set',
                                'parents' => ['Goods', 'discount_set'],
                                'child' => [
                                    'goods_dispatch_set_freight' => [
                                        'name' => '折扣设置',
                                        'url' => 'discount.batch-dispatch.freight-set',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-sitemap',
                                        'sort' => '2',
                                        'item' => 'goods_dispatch_set_freight',
                                        'parents' => ['Goods', 'discount_set', 'goods_dispatch_freight'],
                                        'child' => []
                                    ],
                                    'goods_dispatch_select_category' => [
                                        'name' => '分类查询',
                                        'url' => 'discount.batch-dispatch.select-category',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-sitemap',
                                        'sort' => '2',
                                        'item' => 'goods_dispatch_select_category',
                                        'parents' => ['Goods', 'discount_set', 'goods_dispatch_freight'],
                                        'child' => []
                                    ],
                                    'goods_dispatch_freight_save' => [
                                        'name' => '运费设置',
                                        'url' => 'discount.batch-dispatch.freight-save',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-sitemap',
                                        'sort' => '2',
                                        'item' => 'goods_dispatch_freight_save',
                                        'parents' => ['Goods', 'discount_set', 'goods_dispatch_freight'],
                                        'child' => []
                                    ],
                                    'goods_dispatch_update_freight' => [
                                        'name' => '修改设置',
                                        'url' => 'discount.batch-dispatch.update-freight',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-sitemap',
                                        'sort' => '2',
                                        'item' => 'goods_dispatch_update_freight',
                                        'parents' => ['Goods', 'discount_set', 'goods_dispatch_freight'],
                                        'child' => []
                                    ],
                                    'goods_dispatch_freight_delete' => [
                                        'name' => '删除设置',
                                        'url' => 'discount.batch-dispatch.delete-freigh',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-sitemap',
                                        'sort' => '2',
                                        'item' => 'goods_dispatch_freight_delete-save',
                                        'parents' => ['Goods', 'discount_set', 'goods_dispatch_freight'],
                                        'child' => []
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'dispatch_type' => [
                        'name' => '配送方式',
                        'url' => 'dispatch.dispatch-type.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-truck',
                        'sort' => 0,
                        'item' => 'dispatch_type',
                        'parents' => ['Goods'],
                        'child' => [
                            'dispatch_type_getData' => [
                                'name' => '列表',
                                'url' => 'dispatch.dispatch-type.get-data',
                                'permit' => 0,
                                'menu' => 0,
                                'sort' => 0,
                                'item' => 'dispatch_type_getData',
                                'parents' => ['Goods', 'dispatch_type'],
                            ],
                            'dispatch_type_editEnable' => [
                                'name' => '开启关闭',
                                'url' => 'dispatch.dispatch-type.edit-enable',
                                'permit' => 0,
                                'menu' => 0,
                                'sort' => 0,
                                'item' => 'dispatch_type_editEnable',
                                'parents' => ['Goods', 'dispatch_type'],
                            ],
                            'dispatch_type_editSort' => [
                                'name' => '排序',
                                'url' => 'dispatch.dispatch-type.edit-sort',
                                'permit' => 0,
                                'menu' => 0,
                                'sort' => 0,
                                'item' => 'dispatch_type_editSort',
                                'parents' => ['Goods', 'dispatch_type'],
                            ],
                            'dispatch_type_bulkUpdateGoods' => [
                                'name' => '批量更新',
                                'url' => 'dispatch.dispatch-type.bulk-update-goods',
                                'permit' => 0,
                                'menu' => 0,
                                'sort' => 0,
                                'item' => 'dispatch_type_bulkUpdateGoods',
                                'parents' => ['Goods', 'dispatch_type'],
                            ],
                        ],
                    ],

                    'goods_setting' => [
                        'name' => '基础设置',
                        'url' => 'goods.goods_setting.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-gear',
                        'sort' => 0,
                        'item' => '',
                        'parents' => ['Goods'],
                        'child' => [
                            'goods_setting_index' => [
                                'name' => '基础设置',
                                'url' => 'goods.goods_setting.index',
                                'permit' => 1,
                                'menu' => 1,
                                'sort' => 0,
                                'item' => '',
                                'parents' => ['Goods', 'goods_setting'],
                            ],
                        ],
                    ],
                ],
            ],

            'Member' => [
                'name' => '会员',
                'url' => 'member.member',
                'url_params' => '',
                'permit' => 1,
                'menu' => 1,
                'top_show' => 0,
                'left_first_show' => 1,
                'left_second_show' => 1,
                'icon' => 'fa-users',
                'sort' => '3',
                'item' => 'Member',
                'parents' => [],
                'child' => [

                    'member_search' => [
                        'name' => '搜索会员',
                        'url' => 'member.member.search_member',
                        'url_params' => '',
                        'permit' => 0,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => 'member_search',
                        'parents' => ['Member'],
                    ],


                    'memberSearch' => [
                        'name' => '搜索会员(后台vue优化)',
                        'url' => 'member.member.search-member',
                        'url_params' => '',
                        'permit' => 0,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => 'memberSearch',
                        'parents' => ['Member'],
                    ],

                    'memberLevelSearch' => [
                        'name' => '搜索会员等级',
                        'url' => 'member.member.get-sub-groups',
                        'url_params' => '',
                        'permit' => 0,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 0,
                        'item' => 'memberLevelSearch',
                        'parents' => ['Member'],
                    ],

                    'member_all' => [
                        'name' => '全部会员',
                        'url' => 'member.member.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-users',
                        'sort' => 0,
                        'item' => 'member_all',
                        'parents' => ['Member'],
                        'child' => [
                            'member_excel_demo' => [
                                'name' => '下载导入会员模板',
                                'url' => 'member.member.memberExcelDemo',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_excel_demo',
                                'parents' => ['Member'],
                            ],

                            'member_excel' => [
                                'name' => '上传导入会员',
                                'url' => 'member.member.member-excel',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_excel',
                                'parents' => ['Member'],
                            ],

                            'member_see' => [
                                'name' => '浏览列表',
                                'url' => 'member.member.show',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_see',
                                'parents' => ['Member', 'member_all'],
                            ],
                            'member_merge' => [
                                'name' => '会员合并',
                                'url' => 'member.member.memberMerge',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_merge',
                                'parents' => ['Member', 'member_all'],
                            ],
                            'member_tag_del' => [
                                'name' => '删除标签',
                                'url' => 'plugin.member-tags.Backend.controllers.tag.delMemberTags',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_merge',
                                'parents' => ['Member', 'member_all'],
                            ],

                            'member_add' => [
                                'name' => '添加会员',
                                'url' => 'member.member.add-member',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_add',
                                'parents' => ['Member', 'member_all'],
                                'child' => [
                                    'member_add_data' => [
                                        'name' => '添加会员数据',
                                        'url' => 'member.member.add-member-data',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'member_add_data',
                                        'parents' => ['Member', 'member_all','member_add'],
                                    ],
                                ]
                            ],
                            'member_chart_check' => [
                                'name' => '查看会员登录端统计',
                                'url' => 'member.member.memberChart',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_chart_check',
                                'parents' => ['Member', 'member_all'],
                            ],

                            'member_import' => [
                                'name' => '会员excel导入',
                                'url' => 'member.member.import',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_import',
                                'parents' => ['Member', 'member_all'],
                            ],

                            'member_export' => [
                                'name' => '会员导出',
                                'url' => 'member.member.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_export',
                                'parents' => ['Member', 'member_all'],
                            ],

                            'member_tag' => [
                                'name' => '添加标签',
                                'url' => 'plugin.member-tags.Backend.controllers.tag.handleTagsList',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_tag',
                                'parents' => ['Member', 'member_all'],
                            ],
                            'member_tag_choice' => [
                                'name' => '选择标签',
                                'url' => 'plugin.member-tags.Backend.controllers.tag.makeMemberTags',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_tag_choice',
                                'parents' => ['Member', 'member_all'],
                            ],

                            'member_detail' => [
                                'name' => '查看详情',
                                'url' => 'member.member.detail',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_detail',
                                'parents' => ['Member', 'member_all'],
                                'child' => [
                                    'get_member_detail' => [
                                        'name' => '获取会员信息',
                                        'url' => 'member.member.get-member-detail',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'get_member_detail',
                                        'parents' => ['Member', 'member_all', 'member_detail'],
                                    ],
                                    'change_mobile_log' => [
                                        'name' => '修改绑定手机号记录',
                                        'url' => 'member.member.changeMobileLog',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'change_mobile_log',
                                        'parents' => ['Member', 'member_all', 'member_detail'],
                                    ],
                                    'change_mobile' => [
                                        'name' => '修改绑定手机号',
                                        'url' => 'member.member.changeMobile',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'change_mobile',
                                        'parents' => ['Member', 'member_all', 'member_detail'],
                                    ],
                                ]
                            ],
                            'passwordPage' => [
                                'name' => '支付密码',
                                'url' => 'password.page.index',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'passwordPage',
                                'parents' => ['Member', 'member_all'],
                                'child' => [
                                    'passwordUpdate' => [
                                        'name' => '支付密码',
                                        'url' => 'password.update.index',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'passwordUpdate',
                                        'parents' => ['Member', 'member_all', 'passwordPage'],
                                    ],
                                ]
                            ],


                            'member_detail_update' => [
                                'name' => '修改信息',
                                'url' => 'member.member.update',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_detail_update',
                                'parents' => ['Member', 'member_all'],
                            ],

                            'member_change_relation' => [
                                'name' => '修改关系',
                                'url' => 'member.member.change_relation',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_change_relation',
                                'parents' => ['Member', 'member_all'],
                            ],

                            'member_member_agent_old' => [
                                'name' => '推广下线',
                                'url' => 'member.member.agent-old',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_member_agent_old',
                                'parents' => ['Member', 'member_all'],
                                'child' => [
                                    'member_agent_old_show' => [
                                        'name' => '推广下线详情',
                                        'url' => 'member.member.agent-old-show',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'member_agent_old_show',
                                        'parents' => ['Member', 'member_all', 'member_member_agent_old'],
                                    ],
                                    'member_agent_old_export' => [
                                        'name' => '推广下线列表',
                                        'url' => 'member.member.do-export',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'member_agent_old_export',
                                        'parents' => ['Member', 'member_all', 'member_member_agent_old'],
                                    ],
                                ]
                            ],

                            'member_member_agent' => [
                                'name' => '团队下线',
                                'url' => 'member.member.agent',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_member_agent',
                                'parents' => ['Member', 'member_all'],
                                'child' => [
                                    'member_agent_show' => [
                                        'name' => '团队下线详情',
                                        'url' => 'member.member.agent-show',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'member_agent_show',
                                        'parents' => ['Member', 'member_all', 'member_member_agent'],
                                    ],
                                    'member_agent_export' => [
                                        'name' => '团队下线导出',
                                        'url' => 'member.member.agent-export',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'member_agent_export',
                                        'parents' => ['Member', 'member_all', 'member_member_agent'],
                                    ],
                                    'member_agent_level' => [
                                        'name' => '团队下线层级',
                                        'url' => 'member.member.getLevels',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'member_agent_level',
                                        'parents' => ['Member', 'member_all', 'member_member_agent'],
                                    ],
                                ]
                            ],

                            'member_member_agent_parent' => [
                                'name' => '推广上线',
                                'url' => 'member.member.agent-parent',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_member_agent_parent',
                                'parents' => ['Member', 'member_all'],
                                'child' => [
                                    'member_agent_parent_show' => [
                                        'name' => '推广上线详情',
                                        'url' => 'member.member.agent-parent-show',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'member_agent_parent_show',
                                        'parents' => ['Member', 'member_all', 'member_member_agent_parent'],
                                    ],
                                    'member_agent_parent_export' => [
                                        'name' => '推广上线导出',
                                        'url' => 'member.member.first-agent-export',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'member_agent_parent_export',
                                        'parents' => ['Member', 'member_all', 'member_member_agent_parent'],
                                    ],
                                ]
                            ],

                            'member_member_black' => [
                                'name' => '加入黑名单',
                                'url' => 'member.member.black',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_member_black',
                                'parents' => ['Member', 'member_all'],
                            ],

                            'member_member_delete' => [
                                'name' => '删除会员',
                                'url' => 'member.member.delete',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_member_delete',
                                'parents' => ['Member', 'member_all'],
                            ],
                            'member_income' => [
                                'name' => '收入详情',
                                'url' => 'member.member-income.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_income',
                                'parents' => ['Member', 'member_all'],
                                'child' => [
                                    'income_records_show' => [
                                        'name' => '收入详情列表',
                                        'url' => 'member.member-income.show',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'income_records_show',
                                        'parents' => ['Member', 'member_all'],
                                    ],
                                    'income_records_export' => [
                                        'name' => '导出 EXCEL',
                                        'url' => 'member.member-income..export',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'income_records_export',
                                        'parents' => ['finance', 'balance', 'income_records'],
                                    ],
                                ]
                            ],
                            'member_bank_card' => [
                                'name' => '银行卡管理',
                                'url' => 'member.bank-card.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_bank_card',
                                'parents' => ['Member', 'member_all'],
                                'child' => [
                                    'member_bank_card_show' => [
                                        'name' => '银行卡管理操作',
                                        'url' => 'member.bank-card.edit',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'member_bank_card_show',
                                        'parents' => ['Member', 'member_all', 'member_bank_card'],
                                    ],
                                ]
                            ],
                            'member_member_address' => [
                                'name' => '收货地址',
                                'url' => 'member.member-address.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_member_address',
                                'parents' => ['Member', 'member_all'],
                                'child' => [
                                    'member_member_address_show' => [
                                        'name' => '收货地址详情',
                                        'url' => 'member.member-address.show',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'member_member_address_show',
                                        'parents' => ['Member', 'member_all', 'member_member_address'],
                                    ],
                                ]
                            ],
                        ],
                    ],

                    'member_level' => [
                        'name' => '会员等级',
                        'url' => 'member.member-level.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-sort-amount-asc',
                        'sort' => 0,
                        'item' => 'member_level',
                        'parents' => ['Member'],
                        'child' => [
                            'member_member_level_show' => [
                                'name' => '会员等级数据加载',
                                'url' => 'member.member-level.show',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_member_level_show',
                                'parents' => ['Member', 'member_level'],
                            ],
                            'member_member_level_form' => [
                                'name' => '操作页面加载',
                                'url' => 'member.member-level.form',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_member_level_form',
                                'parents' => ['Member', 'member_level'],
                            ],
                            'member_member_level_search_goods' => [
                                'name' => '搜索商品',
                                'url' => 'member.member-level.search-goods',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_member_level_search_goods',
                                'parents' => ['Member', 'member_level'],
                            ],
                            'member_member_level_store' => [
                                'name' => '添加会员等级数据保存',
                                'url' => 'member.member-level.store',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-plus',
                                'sort' => 0,
                                'item' => 'member_member_level_store',
                                'parents' => ['Member', 'member_level', 'member_member_level_form'],
                            ],
                            'member_member_level_update' => [
                                'name' => '编辑会员等级',
                                'url' => 'member.member-level.update',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-edit',
                                'sort' => 0,
                                'item' => 'member_member_level_update',
                                'parents' => ['Member', 'member_level', 'member_member_level_form'],
                            ],
                            'member_member_level_destroy' => [
                                'name' => '删除会员等级',
                                'url' => 'member.member-level.destroy',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-remove',
                                'sort' => 0,
                                'item' => 'member_member_level_destroy',
                                'parents' => ['Member', 'member_level'],
                            ],
                        ],
                    ],

                    'member_group' => [
                        'name' => '会员分组',
                        'url' => 'member.member-group.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-sort-alpha-asc',
                        'sort' => 0,
                        'item' => 'member_group',
                        'parents' => ['Member'],
                        'child' => [
                            'member_member_group_show' => [
                                'name' => '查看会员分组信息',
                                'url' => 'member.member-group.show',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_member_group_show',
                                'parents' => ['Member', 'member_group'],
                            ],
                            'member_member_group_form' => [
                                'name' => '会员分组操作',
                                'url' => 'member.member-group.form',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_member_group_form',
                                'parents' => ['Member', 'member_group'],
                            ],
                            'member_member_group_store' => [
                                'name' => '添加会员分组',
                                'url' => 'member.member-group.store',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-plus',
                                'sort' => 0,
                                'item' => 'member_member_group_store',
                                'parents' => ['Member', 'member_group'],
                            ],

                            'member_member_group_update' => [
                                'name' => '修改会员分组',
                                'url' => 'member.member-group.update',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-pencil-square-o',
                                'sort' => 0,
                                'item' => 'member_member_group_update',
                                'parents' => ['Member', 'member_group'],
                            ],

                            'member_member_group_destroy' => [
                                'name' => '删除会员分组',
                                'url' => 'member.member-group.destroy',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-remove',
                                'sort' => 0,
                                'item' => 'member_member_group_destroy',
                                'parents' => ['Member', 'member_group'],
                            ],

                        ],

                    ],


                    'user_relation' => [
                        'name' => '关系设置',
                        'url' => 'member.member-relation.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa fa-code-fork',
                        'sort' => 0,
                        'item' => 'user_relation',
                        'parents' => ['Member'],
                        'child' => [

                            'user_permission_show' => [
                                'name' => '关系设置数据详情',
                                'url' => 'member.member-relation.show',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'user_permission_show',
                                'parents' => ['Member', 'user_relation'],
                            ],
                            'user_relation_search_goods' => [
                                'name' => '搜索商品',
                                'url' => 'member.member-relation.query',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'user_relation_search_goods',
                                'parents' => ['Member', 'user_relation'],
                            ],

                            'user_relation_see' => [
                                'name' => '查看修改',
                                'url' => 'member.member-relation.save',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'user_relation_see',
                                'parents' => ['Member', 'user_relation'],
                            ],
                        ],
                    ],

                    'member_agent_apply' => [
                        'name' => '资格申请',
                        'url' => 'member.member-relation.apply',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'iconfont icon-fontclass-mendianshenqing2',
                        'sort' => 0,
                        'item' => 'member_agent_apply',
                        'parents' => ['Member', 'member_relation'],
                        'child' => [

                            'agent_apply_show' => [
                                'name' => '申请列表',
                                'url' => 'member.member-relation.apply-show',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sliders',
                                'sort' => 0,
                                'item' => 'agent_apply_show',
                                'parents' => ['Member', 'member_relation', 'agent_apply'],
                            ],
                            'agent_apply_chkApply' => [
                                'name' => '资格审核',
                                'url' => 'member.member-relation.chk-apply',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sliders',
                                'sort' => 0,
                                'item' => 'agent_apply_chkApplye',
                                'parents' => ['Member', 'member_relation', 'agent_apply'],
                            ],

                            'agent_apply_export' => [
                                'name' => '导出申请',
                                'url' => 'member.member-relation.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sliders',
                                'sort' => 0,
                                'item' => 'agent_apply_export',
                                'parents' => ['Member', 'member_relation', 'agent_apply'],
                            ]
                        ],
                    ],
                    'relation_base' => [
                        'name' => '会员设置',
                        'url' => 'member.member-relation.base',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa fa-user',
                        'sort' => 0,
                        'item' => 'relation_base',
                        'parents' => ['Member'],
                        'child' => [

                            'relation_base_data' => [
                                'name' => '查看修改',
                                'url' => 'member.member-relation.relation-base',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-circle-o',
                                'sort' => 0,
                                'item' => 'relation_base_data',
                                'parents' => ['Member', 'relation_base'],
                            ],
                            'relation_base_save' => [
                                'name' => '查看修改',
                                'url' => 'member.member.member_record',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-circle-o',
                                'sort' => 0,
                                'item' => 'relation_base',
                                'parents' => ['Member', 'relation_base'],
                            ],
                        ],
                    ],
                    'popularize_page_show' => [
                        'name' => '推广中心设置',
                        'url' => 'member.popularize-page-show.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'top_show' => 1,               //顶部导航是否显示
                        'left_first_show' => 1,           //左侧一级导航是否显示
                        'left_second_show' => 1,
                        'icon' => 'fa fa-gear',
                        'sort' => 0,
                        'item' => 'popularize_page_show',
                        'parents' => ['Member'],
                        'child' => [
                            'popularize_wechat_set' => [
                                'name' => '微信公众号',
                                'url' => 'member.popularize-page-show.wechatSet',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'popularize_wechat_set',
                                'parents' => ['Member', 'popularize_page_show'],
                            ],
                            'popularize_mini_set' => [
                                'name' => '微信小程序',
                                'url' => 'member.popularize-page-show.miniSet',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'popularize_mini_set',
                                'parents' => ['Member', 'popularize_page_show'],
                            ],
                            'popularize_wap_set' => [
                                'name' => 'wap',
                                'url' => 'member.popularize-page-show.wapSet',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'popularize_wap_set',
                                'parents' => ['Member', 'popularize_page_show'],
                            ],
                            'popularize_app_set' => [
                                'name' => 'APP',
                                'url' => 'member.popularize-page-show.appSet',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'popularize_app_set',
                                'parents' => ['Member', 'popularize_page_show'],
                            ],
                            'popularize_alipay_set' => [
                                'name' => '支付宝',
                                'url' => 'member.popularize-page-show.alipaySet',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'popularize_alipay_set',
                                'parents' => ['Member', 'popularize_page_show'],
                            ],
                        ]
                    ],
                    'relation_export' => [
                        'name' => '关系链升级',
                        'url' => 'member.member.exportRelation',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => 'fa fa-circle-o',
                        'sort' => 0,
                        'item' => 'relation_base',
                        'parents' => ['Member'],
                        'child' => [
                        ],
                    ],
                    'member_invited' => [
                        'name' => '会员邀请码',
                        'url' => 'member.member_invited.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa fa-code',
                        'sort' => 0,
                        'left_first_show' => 1,
                        'left_second_show' => 1,
                        'item' => 'member_invited',
                        'parents' => ['Member'],
                        'child' => [
                            'member_invited_list' => [
                                'name' => '查看',
                                'url' => 'member.member_invited.show',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-circle-o',
                                'sort' => 0,
                                'item' => 'member_invited_list',
                                'parents' => ['Member', 'member_invited'],
                            ],
                            'member_invited_export' => [
                                'name' => '导出',
                                'url' => 'member.member_invited.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-circle-o',
                                'sort' => 0,
                                'item' => 'member_invited_export',
                                'parents' => ['Member', 'member_invited'],
                            ],
                        ],
                    ],
                    'member_update_record' => [
                        'name' => '关系链修改记录',
                        'url' => 'member.member.record-list',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa fa-link',
                        'sort' => 0,
                        'left_first_show' => 1,
                        'left_second_show' => 1,
                        'item' => 'member_merge_log',
                        'parents' => ['Member'],
                        'child' => [
                            'member_update_record_data' => [
                                'name' => '关系链修改记录详情',
                                'url' => 'member.member.record-datas',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-circle-o',
                                'sort' => 0,
                                'item' => 'member_update_record_data',
                                'parents' => ['Member', 'member_update_record'],
                            ],
                        ],
                    ],
                    'member_merge_log' => [
                        'name' => '会员合并记录',
                        'url' => 'member.merge-log.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa fa-bar-chart-o',
                        'sort' => 0,
                        'left_first_show' => 1,
                        'left_second_show' => 1,
                        'item' => 'member_merge_log',
                        'parents' => ['Member'],
                        'child' => [
                            'member_merge_log_auth_merge' => [
                                'name' => '自动合并',
                                'url' => 'member.merge-log.auth-merge',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-circle-o',
                                'sort' => 0,
                                'item' => 'member_merge_log_auth_merge',
                                'parents' => ['Member', 'member_merge_log'],
                            ],
                            'member_merge_log_bind_tel' => [
                                'name' => '绑定手机',
                                'url' => 'member.merge-log.bind-tel',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-circle-o',
                                'sort' => 0,
                                'item' => 'member_merge_log_bind_tel',
                                'parents' => ['Member', 'member_merge_log'],
                            ],
                            'member_merge_log_click_merge' => [
                                'name' => '点击合并',
                                'url' => 'member.merge-log.click-merge',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-circle-o',
                                'sort' => 0,
                                'item' => 'member_merge_log_click_merge',
                                'parents' => ['Member', 'member_merge_log'],
                            ],
                        ],
                    ],
                    'member_cancel_manage' => [
                        'name' => '账号注销管理',
                        'url' => 'member.member-cancel.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa fa-clipboard',
                        'sort' => 0,
                        'left_first_show' => 1,
                        'left_second_show' => 1,
                        'item' => 'member_cancel_manage',
                        'parents' => ['Member'],
                        'child' => [
                            'member_cancel_verify' => [
                                'name' => '账号注销申请审核',
                                'url' => 'member.member-cancel.verify',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-circle-o',
                                'sort' => 0,
                                'item' => 'member_cancel_verify',
                                'parents' => ['Member', 'member_cancel_manage'],
                            ],
                            'member_cancel_verify_search' => [
                                'name' => '审核搜索',
                                'url' => 'member.member-cancel.search',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-circle-o',
                                'sort' => 0,
                                'item' => 'member_cancel_verify_search',
                                'parents' => ['Member', 'member_cancel_manage'],
                            ],
                            'member_cancel_verify_pass' => [
                                'name' => '通过审核',
                                'url' => 'member.member-cancel.pass',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-circle-o',
                                'sort' => 0,
                                'item' => 'member_cancel_verify_pass',
                                'parents' => ['Member', 'member_cancel_manage'],
                            ],
                            'member_cancel_verify_reject' => [
                                'name' => '驳回审核',
                                'url' => 'member.member-cancel.reject',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-circle-o',
                                'sort' => 0,
                                'item' => 'member_cancel_verify_reject',
                                'parents' => ['Member', 'member_cancel_manage'],
                            ],
                        ],
                    ]
                ],
            ],

            'Order' => [
                'name' => '订单',
                'url' => 'order.order-list.index',
                'url_params' => '',
                'permit' => 1,
                'menu' => 1,
                'top_show' => 0,
                'left_first_show' => 1,
                'left_second_show' => 1,
                'icon' => 'fa-shopping-cart',
                'sort' => '4',
                'item' => 'Order',
                'parents' => [],
                'child' => [
                    'order_vue_operation' => [
                        'name' => '订单操作',
                        'url' => 'order.order-list.index',
                        'permit' => 1,
                        'menu' => 0,
                        'item' => 'order_vue_operation',
                        'parents' => ['Order'],
                        'child' => [
                            'order_list_get_list' => [
                                'name' => '获取订单数据',
                                'url' => 'order.order-list.get-list',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'item' => 'order_list_get_list',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'order_list_common_part' => [
                                'name' => '获取订单公共参数',
                                'url' => 'order.order-list.common-part',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'item' => 'order_list_common_part',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'order_detail_vue-index' => [
                                'name' => '查看详情',
                                'url' => 'order.detail.vue-index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'item' => 'order_detail_vue-index',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'order_detail_get-data' => [
                                'name' => '获取订单详情数据',
                                'url' => 'order.detail.get-data',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'item' => 'order_detail_get-data',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'order_vue_operation_pay' => [
                                'name' => '确认付款',
                                'url' => 'order.vue-operation.pay',
                                'permit' => 1,
                                'menu' => 0,
                                'item' => 'order_vue_operation_pay',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'order_vue_operation_send' => [
                                'name' => '确认发货',
                                'url' => 'order.vue-operation.send',
                                'permit' => 1,
                                'menu' => 0,
                                'item' => 'order_vue_operation_send',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            /*
                             * 修复2020 12 .25 客服提出的多包裹发货没有权限
                             */
                            'order_vue_operation_separate_send' => [
                                'name' => 'vue多包裹发货',
                                'url' => 'order.vue-operation.separate-send',
                                'permit' => 1,
                                'menu' => 0,
                                'item' => 'order_vue_operation_send',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],


                            'order_vue_operation_cancel_send' => [
                                'name' => '取消发货',
                                'url' => 'order.vue-operation.cancel-send',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'item' => 'order_vue_operation_cancel_send',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'order_vue_operation_receive' => [
                                'name' => '确认收货',
                                'url' => 'order.vue-operation.receive',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'item' => 'order_vue_operation_receive',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],

                            'order_vue_operation_close' => [
                                'name' => '关闭订单',
                                'url' => 'order.vue-operation.close',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'item' => 'order_vue_operation_close',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'order_vue_operation_manualrefund' => [
                                'name' => '退款并关闭订单',
                                'url' => 'order.vue-operation.manualRefund',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'item' => 'order_vue_operation_manualrefund',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'order_vue_operation_remark' => [
                                'name' => '订单备注',
                                'url' => 'order.vue-operation.remarks',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'item' => 'order_vue_operation_remark',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'order_vue_operation_revoice' => [
                                'name' => '上传发票',
                                'url' => 'order.vue-operation.invoice',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'item' => 'order_vue_operation_revoice',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'order_list_export' => [
                                'name' => '订单导出',
                                'url' => 'order.order-list.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'item' => 'order_list_export',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'vue_order_pay_list' => [
                                'name' => '查看订单支付记录',
                                'url' => 'order.orderPay.vue',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-file-text',
                                'sort' => 1,
                                'item' => 'vue_order_pay_list',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'order_detail_refund_express' => [
                                'name' => '订单退款物流',
                                'url' => 'order.detail.refund-express',
                                'permit' => 0,
                                'menu' => 0,
                                'item' => 'order_detail_refund_express',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'refund_detail_logistics' => [
                                'name' => '订单售后物流查看',
                                'url' => 'refund.detail.express',
                                'permit' => 0,
                                'menu' => 0,
                                'item' => 'refund_detail_logistics',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'refund_detail_returnLogistics' => [
                                'name' => '订单售后用户发货物流',
                                'url' => 'refund.detail.return-logistics',
                                'permit' => 0,
                                'menu' => 0,
                                'item' => 'refund_detail_returnLogistics',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                            'refund_detail_resendLogistics' => [
                                'name' => '订单售后商家发货物流',
                                'url' => 'refund.detail.resend-logistics',
                                'permit' => 0,
                                'menu' => 0,
                                'item' => 'refund_detail_resendLogistics',
                                'parents' => ['Order', 'order_vue_operation'],
                            ],
                        ],
                    ],
                    'order_list' => [
                        'name' => '全部订单',
                        'url' => 'order.order-list.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-clipboard',
                        'sort' => 0,
                        'item' => 'order_list',
                        'parents' => ['Order'],
                        'child' => [
                            'order_list_see' => [
                                'name' => '旧订单接口',
                                'url' => 'order.list.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 1,
                                'item' => 'order_list_see',
                                'parents' => ['Order', 'order_list'],
                                'child' => [],
                            ],
                            'shop_order_list' => [
                                'name'       => '平台订单列表',
                                'url'        => 'order.shop-order-list.index',
                                'permit'     => 1,
                                'sort'       => 1,
                                'item'       => 'shop_order_list',
                                'parents'    => ['Order'],
                                'child'      => [
                                    'shop_order_get_list' => [
                                        'name'       => '平台订单列表数据',
                                        'url'        => 'order.shop-order-list.get-list',
                                        'permit'     => 0,
                                        'item'       => 'shop_order_get_list',
                                        'parents'    => ['Order', 'shop_order_list'],
                                    ],
                                    'shop_order_export' => [
                                        'name'       => '平台订单列表导出',
                                        'url'        => 'order.shop-order-list.export',
                                        'permit'     => 0,
                                        'item'       => 'shop_order_export',
                                        'parents'    => ['Order','shop_order_list'],
                                    ],
                                ],
                            ],

                            //订单操作所有订单的共同操作
                            'order_handel' => [
                                'name' => '旧订单操作',
                                'url' => 'order.handel',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 1,
                                'item' => 'order_handel',
                                'parents' => ['Order', 'order_list'],
                                'child' => [
                                    'order_list_index' => [
                                        'name' => '查看详情',
                                        'url' => 'order.detail.index',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-file-text',
                                        'sort' => 1,
                                        'item' => 'order_list_index',
                                        'parents' => ['Order', 'order_list', 'order_handel'],
                                    ],
                                    'order_detail_express' => [
                                        'name' => '订单物流',
                                        'url' => 'order.detail.express',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'icon' => 'fa-file-text',
                                        'sort' => 1,
                                        'item' => 'order_detail_express',
                                        'parents' => ['Order', 'order_list', 'order_handel'],
                                    ],
                                    'order_pay_list' => [
                                        'name' => '查看订单支付记录',
                                        'url' => 'order.orderPay.index',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-file-text',
                                        'sort' => 1,
                                        'item' => 'order_pay_index',
                                        'parents' => ['Order', 'order_detail'],
                                    ],
                                    'order_pay_detail' => [
                                        'name' => '查看订单支付详情',
                                        'url' => 'orderPay.detail.index',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-file-text',
                                        'sort' => 1,
                                        'item' => 'order_pay_detail',
                                        'parents' => ['Order', 'order_detail'],
                                    ],
                                    'order_fix_payfail' => [
                                        'name' => '修复支付状态',
                                        'url' => 'order.fix.pay-fail',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-file-text',
                                        'sort' => 1,
                                        'item' => 'order_fix_payfail',
                                        'parents' => ['Order', 'order_detail'],
                                    ],
                                    'orderpay_fix_refund' => [
                                        'name' => '原路退款',
                                        'url' => 'orderPay.fix.refund',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-file-text',
                                        'sort' => 1,
                                        'item' => 'orderpay_fix_refund',
                                        'parents' => ['Order', 'order_detail'],
                                    ],
                                    'change_order_price_index' => [
                                        'name' => '修改价格跳转路由',
                                        'url' => 'order.change-order-price.index',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 1,
                                        'item' => 'change_order_price_index',
                                        'parents' => ['Order', 'order_list'],
                                    ],
                                    'change_order_price_index_api' => [
                                        'name' => '显示订单改价',
                                        'url' => 'order.change-order-price.index-api',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-file-text',
                                        'sort' => 1,
                                        'item' => 'change_order_price_store',
                                        'parents' => ['Order', 'order_list'],
                                    ],
                                    'change_order_price_store_api' => [
                                        'name' => '订单改价路由',
                                        'url' => 'order.change-order-price.store-api',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-file-text',
                                        'sort' => 1,
                                        'item' => 'change_order_price_store',
                                        'parents' => ['Order', 'order_list'],
                                    ],

                                    'change_order_price_store' => [
                                        'name' => '订单改价',
                                        'url' => 'order.change-order-price.store',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-file-text',
                                        'sort' => 1,
                                        'item' => 'change_order_price_store',
                                        'parents' => ['Order', 'order_list'],
                                    ],
                                    'order_operation_pay' => [
                                        'name' => '确认付款',
                                        'url' => 'order.operation.pay',
                                        'url_params' => 'order.operation.send',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 1,
                                        'item' => 'order_operation_pay',
                                        'parents' => ['Order', 'order_list', 'order_handel'],
                                    ],
                                    'order_operation_send' => [
                                        'name' => '确认发货',
                                        'url' => 'order.operation.send',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-file-text',
                                        'sort' => 1,
                                        'item' => 'order_operation_send',
                                        'parents' => ['Order', 'order_list', 'order_handel'],
                                    ],
                                    'order_multiple_packages_order_goods_get_order_goods' => [
                                        'name' => '获取订单商品',
                                        'url' => 'order.multiple-packages-order-goods.get-order-goods',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-file-text',
                                        'sort' => 1,
                                        'item' => 'order_multiple_packages_order_goods_get_order_goods',
                                        'parents' => ['Order', 'order_list', 'order_handel'],
                                    ],
                                    'order_operation_add_order_express' => [
                                        'name' => '继续发货',
                                        'url' => 'order.operation.add-order-express',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-file-text',
                                        'sort' => 1,
                                        'item' => 'order_operation_add_order_express',
                                        'parents' => ['Order', 'order_list', 'order_handel'],
                                    ],
                                    'order_waybill' => [
                                        'name' => '自动填充运单号',
                                        'url' => 'order.waybill.waybill',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-file-text',
                                        'sort' => 1,
                                        'item' => 'order_waybill',
                                        'parents' => ['Order', 'order_list', 'order_handel'],
                                    ],
                                    'order_operation_cancel_send' => [
                                        'name' => '取消发货',
                                        'url' => 'order.operation.cancel-send',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 1,
                                        'item' => 'order_operation_cancel_send',
                                        'parents' => ['Order', 'order_list', 'order_handel'],
                                    ],
                                    'order_operation_receive' => [
                                        'name' => '确认收货',
                                        'url' => 'order.operation.receive',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 1,
                                        'item' => 'order_operation_receive',
                                        'parents' => ['Order', 'order_list', 'order_handel'],
                                    ],


                                    'order_operation_close' => [
                                        'name' => '关闭订单',
                                        'url' => 'order.operation.close',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 1,
                                        'item' => 'order_operation_close',
                                        'parents' => ['Order', 'order_list'],
                                    ],

                                    'order_operation_manualrefund' => [
                                        'name' => '退款并关闭订单',
                                        'url' => 'order.operation.manualRefund',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 1,
                                        'item' => 'order_operation_manualrefund',
                                        'parents' => ['Order', 'order_list'],
                                    ],

                                    'order_operation_remark' => [
                                        'name' => '订单备注',
                                        // 'url'               => 'order.remark.index',
                                        'url' => 'order.operation.remarks',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 1,
                                        'item' => 'order_operation_remark',
                                        'parents' => ['Order', 'order_list', 'order_handel'],
                                    ],

                                    'order_operation_revoice' => [
                                        'name' => '上传发票',
                                        'url' => 'order.operation.invoice',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 1,
                                        'item' => 'order_operation_revoice',
                                        'parents' => ['Order', 'order_list', 'order_handel'],
                                    ],
                                ],
                            ],
                        ],

                    ],

                    'order_list_waitPay' => [
                        'name' => '待支付订单',
                        'url' => 'order.order-list.waitPay',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-credit-card',
                        'sort' => 1,
                        'item' => 'order_list_waitPay',
                        'parents' => ['Order'],
                        'child' => [
                            'order_list_waitPay_see' => [
                                'name' => '旧订单接口',
                                'url' => 'order.list.waitPay',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-circle-o',
                                'sort' => 1,
                                'item' => 'order_list_waitPay',
                                'parents' => ['Order', 'order_list_waitPay'],
                                'child' => [],
                            ],
                        ],
                    ],

                    'order_list_waitSend' => [
                        'name' => '待发货订单',
                        'url' => 'order.order-list.waitSend',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-truck',
                        'sort' => '2',
                        'item' => 'order_list_waitSend',
                        'parents' => ['Order'],
                        'child' => [
                            'order_list_waitSend_see' => [
                                'name' => '旧订单接口',
                                'url' => 'order.list.waitSend',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 1,
                                'item' => 'order_list_waitSend_see',
                                'parents' => ['Order', 'order_list_waitSend'],
                                'child' => [],
                            ],
                        ],
                    ],
                    'order_list_waitReceive' => [
                        'name' => '待收货订单',
                        'url' => 'order.order-list.waitReceive',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-dropbox',
                        'sort' => '3',
                        'item' => 'order_list_waitReceive',
                        'parents' => ['Order'],
                        'child' => [
                            'order_list_waitReceive_see' => [
                                'name' => '旧订单接口',
                                'url' => 'order.list.waitReceive',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 1,
                                'item' => 'order_list_waitReceive_see',
                                'parents' => ['Order', 'order_list_waitReceive'],
                                'child' => [],
                            ],
                        ],
                    ],
                    'order_list_completed' => [
                        'name' => '已完成订单',
                        'url' => 'order.order-list.completed',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-check-square-o',
                        'sort' => '5',
                        'item' => 'order_list_completed',
                        'parents' => ['Order'],
                        'child' => [
                            'order_list_completed_see' => [
                                'name' => '旧订单接口',
                                'url' => 'order.list.completed',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 1,
                                'item' => 'order_list_completed_see',
                                'parents' => ['Order', 'order_list_completed'],
                                'child' => [],

                            ],
                        ],
                    ],
                    'order_list_cancelled' => [
                        'name' => '已关闭订单',
                        'url' => 'order.order-list.cancelled',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bitbucket',
                        'sort' => '5',
                        'item' => 'order_list_cancelled',
                        'parents' => ['Order'],
                        'child' => [
                            'order_list_cancelled_see' => [
                                'name' => '旧订单接口',
                                'url' => 'order.list.completed',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 1,
                                'item' => 'order_list_cancelled_see',
                                'parents' => ['Order', 'order_list_cancelled'],
                                'child' => [],
                            ],
                        ],
                    ],
                    'order_list_pay_fail' => [
                        'name' => '支付异常订单',
                        'url' => 'order.failedList.pay-fail',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bitbucket',
                        'sort' => '5',
                        'item' => 'order_list_pay_fail',
                        'parents' => ['Order'],
                        'child' => [
                            'order_list_cancelled_see' => [
                                'name' => '旧订单接口',
                                'url' => 'order.failedList.pay-fail',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 1,
                                'item' => 'order_list_pay_fail_see',
                                'parents' => ['Order', 'order_list_pay_fail'],
                                'child' => [],
                            ],
                            'order_list_pay_fail_get_list' => [
                                'name' => '获取支付异常订单数据',
                                'url' => 'order.failed-list.get-list',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'item' => 'order_list_pay_fail_get_list',
                                'parents' => ['Order', 'order_list_pay_fail'],
                            ],
                            'order_list_pay_fail_export' => [
                                'name' => '支付异常订单数据导出',
                                'url' => 'order.failed-list.export',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'item' => 'order_list_pay_fail_export',
                                'parents' => ['Order', 'order_list_pay_fail'],
                            ],
                        ],
                    ],
                    'order_list_callback_fail' => [
                        'name' => '支付回调异常订单',
                        'url' => 'order.failedList.callback-fail',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bitbucket',
                        'sort' => '5',
                        'item' => 'order_list_callback_fail',
                        'parents' => ['Order'],
                        'child' => [
                            'order_list_cancelled_see' => [
                                'name' => '旧订单接口',
                                'url' => 'order.failedList.callback-fail',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 1,
                                'item' => 'order_list_callback_fail_see',
                                'parents' => ['Order', 'order_list_callback_fail'],
                                'child' => [],
                            ],
                        ],
                    ],
                    'refund_list_refund' => [
                        'name' => '退换货订单',
                        'url' => 'refund.list.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-refresh',
                        'sort' => '6',
                        'item' => 'refund_list_refund',
                        'parents' => ['Order'],
                        'child' => [
                            'refund_list_refund_get_list' => [
                                'name' => '获取退换货订单数据',
                                'url' => 'refund.list.get-list',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'item' => 'refund_list_refund_get_list',
                                'parents' => ['Order', 'refund_list_refund'],
                            ],
                            'refund_list_refund_export' => [
                                'name' => '退换货订单数据导出',
                                'url' => 'refund.list.export',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'item' => 'refund_list_refund_export',
                                'parents' => ['Order', 'refund_list_refund'],
                            ],
                            'vue_refund_order_handel' => [
                                'name' => '退换货操作',
                                'url' => 'order.handel',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'sort' => 1,
                                'item' => 'vue_refund_order_handel',
                                'parents' => ['Order', 'refund_list_refund'],
                                'child' => [
                                    'refund_vue_operation_reject' => [
                                        'name' => '驳回申请',
                                        'url' => 'refund.vue-operation.reject',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'sort' => '4',
                                        'item' => 'refund_vue_operation_reject',
                                        'parents' => ['Order', 'refund_list_refund'],
                                    ],
//                                    'refund_pay_vue'                      => [
//                                        'name'       => '同意退款',
//                                        'url'        => 'refund.pay.vue',
//                                        'url_params' => '',
//                                        'permit'     => 1,
//                                        'menu'       => 0,
//                                        'icon'       => '',
//                                        'sort'       => '4',
//                                        'item'       => 'refund_pay_vue',
//                                        'parents'    => ['Order', 'refund_list_refund'],
//                                        'child'      => []
//                                    ],
                                    'refund_vue_operation_consensus' => [
                                        'name' => '手动退款',
                                        'url' => 'refund.vue-operation.consensus',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'sort' => '4',
                                        'item' => 'refund_vue_operation_consensus',
                                        'parents' => ['Order', 'refund_list_refund'],
                                    ],
                                    'refund_vue_operation_pass' => [
                                        'name' => '通过申请(需要客户寄回商品)',
                                        'url' => 'refund.vue-operation.pass',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'sort' => '4',
                                        'item' => 'refund_vue_operation_pass',
                                        'parents' => ['Order', 'refund_list_refund'],
                                    ],
                                    'refund_vue_operation_resend' => [
                                        'name' => '商家重新发货',
                                        'url' => 'refund.vue-operation.resend',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'sort' => '4',
                                        'item' => 'refund_vue_operation_resend',
                                        'parents' => ['Order', 'refund_list_refund'],
                                    ],
                                    'refund_vue_operation_close' => [
                                        'name' => '关闭申请(换货完成)',
                                        'url' => 'refund.vue-operation.close',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'sort' => '4',
                                        'item' => 'refund_vue_operation_close',
                                        'parents' => ['Order', 'refund_list_refund'],
                                    ],
                                    'refund_vue_operation_change_price' => [
                                        'name' => '退款金额改价',
                                        'url' => 'refund.vue-operation.change-price',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'sort' => '4',
                                        'item' => 'refund_vue_operation_change_price',
                                        'parents' => ['Order', 'refund_list_refund'],
                                    ],
                                ],
                            ],
                            'refund_order_handel' => [
                                'name' => '退换货操作',
                                'url' => 'order.handel',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 1,
                                'item' => 'refund_order_handel',
                                'parents' => ['Order', 'refund_list_refund'],
                                'child' => [
                                    'refund_operation_reject' => [
                                        'name' => '驳回申请',
                                        'url' => 'refund.operation.reject',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => '4',
                                        'item' => 'refund_operation_reject',
                                        'parents' => ['Order', 'refund_list_refund'],
                                        'child' => []
                                    ],
                                    'refund_pay_index' => [
                                        'name' => '同意退款',
                                        'url' => 'refund.pay.index',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => '4',
                                        'item' => 'refund_pay_index',
                                        'parents' => ['Order', 'refund_list_refund'],
                                        'child' => []
                                    ],
                                    'refund_operation_consensus' => [
                                        'name' => '手动退款',
                                        'url' => 'refund.operation.consensus',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => '4',
                                        'item' => 'refund_operation_consensus',
                                        'parents' => ['Order', 'refund_list_refund'],
                                        'child' => []
                                    ],
                                    'refund_operation_pass' => [
                                        'name' => '通过申请(需要客户寄回商品)',
                                        'url' => 'refund.operation.pass',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-circle-o',
                                        'sort' => '4',
                                        'item' => 'refund_operation_pass',
                                        'parents' => ['Order', 'refund_list_refund'],
                                        'child' => []
                                    ],
                                    'refund_operation_receive_return_goods' => [
                                        'name' => '商家确认收货',
                                        'url' => 'refund.operation.receiveReturnGoods',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-circle-o',
                                        'sort' => '4',
                                        'item' => 'refund_operation_receive_return_goods',
                                        'parents' => ['Order', 'refund_list_refund'],
                                        'child' => []
                                    ],
                                    'refund_operation_resend' => [
                                        'name' => '商家重新发货',
                                        'url' => 'refund.operation.resend',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-circle-o',
                                        'sort' => '4',
                                        'item' => 'refund_operation_resend',
                                        'parents' => ['Order', 'refund_list_refund'],
                                        'child' => []
                                    ],
                                    'refund_operation_close' => [
                                        'name' => '关闭申请(换货完成)',
                                        'url' => 'refund.operation.close',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => '4',
                                        'item' => 'refund_operation_close',
                                        'parents' => ['Order', 'refund_list_refund'],
                                        'child' => []
                                    ],

                                ],
                            ],
                            'refund_list_refund_all' => [
                                'name' => '全部',
                                'url' => 'refund.list.refund',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-file',
                                'sort' => 1,
                                'item' => 'refund_list_refund_all',
                                'parents' => ['Order', 'refund_list_refund'],
                                'child' => []
                            ],
                            'refund_list_refundMoney' => [
                                'name' => '仅退款',
                                'url' => 'refund.list.refundMoney',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-money',
                                'sort' => '2',
                                'item' => 'refund_list_refundMoney',
                                'parents' => ['Order', 'refund_list_refund'],
                                'child' => []
                            ],
                            'refund_list_returnGoods' => [
                                'name' => '退货退款',
                                'url' => 'refund.list.returnGoods',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-location-arrow',
                                'sort' => '3',
                                'item' => 'refund_list_returnGoods',
                                'parents' => ['Order', 'refund_list_refund'],
                                'child' => []
                            ],
                            'refund_list_exchangeGoods' => [
                                'name' => '换货',
                                'url' => 'refund.list.exchangeGoods',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-codepen',
                                'sort' => '4',
                                'item' => 'refund_list_exchangeGoods',
                                'parents' => ['Order', 'refund_list_refund'],
                                'child' => []
                            ],
                        ],
                    ],

                    'refund_list_refunded' => [
                        'name' => '已退款',
                        'url' => 'refund.list.refunded',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-share-square-o',
                        'sort' => '7',
                        'item' => 'refund_list_refunded',
                        'parents' => ['Order'],
                        'child' => [

                            'refund_list_refunded_see' => [
                                'name' => '浏览',
                                'url' => 'refund.list.refunded',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 1,
                                'item' => 'refund_list_refunded_see',
                                'parents' => ['Order', 'refund_list_refunded'],
                                'child' => [],
                            ],
                        ],
                    ],

                    'order_batch_send' => [
                        'name' => '批量发货',
                        'url' => 'order.batch-send.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-send',
                        'sort' => '8',
                        'item' => 'order_batch_send',
                        'parents' => ['Order'],
                        'child' => [

                            'order_batch_send_get_example' => [
                                'name' => '下载模版',
                                'url' => 'order.batch-send.get-example',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'item' => 'order_batch_send_get_example',
                                'parents' => ['Order', 'order_batch_send'],
                                'child' => [],
                            ],

                            'order_batch_send_get_express' => [
                                'name' => '地址获取',
                                'url' => 'order.batch-send.get-express',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'item' => 'order_batch_send_get_express',
                                'parents' => ['Order', 'order_batch_send'],
                            ],
                        ],
                    ],
                    'order_expediting_delivery' => [
                        'name' => '催发货订单',
                        'url' => 'order.order-list.expediting-send',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-truck',
                        'sort' => '8',
                        'item' => 'order_expediting_delivery',
                        'parents' => ['Order'],
                        'child' => [
                            'order_expediting_delivery_see' => [
                                'name' => '旧订单接口',
                                'url' => 'order.list.expediting-send',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 1,
                                'item' => 'order_expediting_delivery_see',
                                'parents' => ['Order', 'order_expediting_delivery'],
                                'child' => [],
                            ],
                        ],
                    ],
                    'order_address_update' => [
                        'name' => '订单地址修改',
                        'url' => 'order.address-update.index',
                        'permit' => 0,
                        'menu' => 0,
                        'item' => 'order_address_update',
                        'parents' => ['Order'],
                        'child' => [
                            'order_address_update_confirm' => [
                                'name' => '订单地址修改提交',
                                'url' => 'order.address-update.update',
                                'permit' => 0,
                                'menu' => 0,
                                'item' => 'order_address_update_confirm',
                                'parents' => ['Order', 'order_address_update'],
                            ],
                        ],
                    ],
                ],
            ],

            'plugins' => [
                'name' => '应用',
                'url' => 'plugins.get-plugin-list',
                'urlParams' => '',
                'permit' => 1,
                'menu' => 1,
                'icon' => 'fa-cubes',
                'top_show' => 0,
                'left_first_show' => 1,
                'left_second_show' => 0,
                'parents' => [],
                'item' => 'plugins',
                'child' => [

                    'plugins_list' => [
                        'name' => '应用列表',
                        'url' => 'plugins.get-plugin-list',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => '',
                        'sort' => '4',
                        'item' => 'plugins_list',
                        'parents' => ['plugins'],
                        'child' => []
                    ],
                    'getPluginList' => [
                        'name' => '应用列表22',
                        'url' => 'plugins.getPluginList',
                        'url_params' => '',
                        'permit' => 0,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => '4',
                        'item' => 'getPluginList',
                        'parents' => ['plugins'],
                        'child' => []
                    ],

                    'set_top_show' => [
                        'name' => '置顶',
                        'url' => 'plugins.setTopShow',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => '4',
                        'item' => 'set_top_show',
                        'parents' => ['plugins'],
                        'child' => []
                    ],

                ],
            ],

            'finance' => [
                'name' => '财务',
                'url' => 'finance.balance-set.see',
                'url_params' => '',
                'permit' => 1,
                'menu' => 1,
                'top_show' => 0,
                'left_first_show' => 1,
                'left_second_show' => 1,
                'icon' => 'fa-rmb',
                'parent_id' => 0,
                'sort' => '5',
                'item' => 'finance',
                'parents' => [],
                'child' => [

                    'finance_balance_set' => [
                        'name' => '余额设置',
                        'url' => 'finance.balance-set.see',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-gear',
                        'sort' => 0,
                        'item' => 'finance_balance_set',
                        'parents' => ['finance'],
                        'child' => [

                            'finance_balance_set_see' => [
                                'name' => '查看设置',
                                'url' => 'finance.balance-set.see',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-gear',
                                'item' => 'finance_balance_set_see',
                                'parents' => ['finance', 'finance_balance_set'],
                            ],

                            'finance_balance_set_store' => [
                                'name' => '修改设置',
                                'url' => 'finance.balance-set.store',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-gear',
                                'item' => 'finance_balance_set_see',
                                'parents' => ['finance', 'finance_balance_set'],
                            ],
                        ],
                    ],

                    'finance_balance_member' => [
                        'name' => '会员余额',
                        'url' => 'balance.member',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-book',
                        'sort' => 0,
                        'item' => 'finance_balance_member',
                        'parents' => ['finance', 'balance'],
                        'child' => [

                            'finance_balance_member_see' => [
                                'name' => '浏览记录',
                                'url' => 'balance.member.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'finance_balance_member_see',
                                'parents' => ['finance', 'balance', 'finance_balance_member'],
                            ],
                            'finance_balance_member_export' => [
                                'name' => '导出记录',
                                'url' => 'balance.member-export.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'finance_balance_member_export',
                                'parents' => ['finance', 'balance', 'finance_balance_member'],
                            ],

                            'finance_balance_member_recharge' => [
                                'name' => '余额充值',
                                'url' => 'balance.recharge.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'finance_balance_member_recharge',
                                'parents' => ['finance', 'balance', 'finance_balance_member'],
                            ],
                        ],
                    ],

                    'finance_balance_rechargeRecord' => [
                        'name' => '充值记录',
                        'url' => 'finance.balance-recharge-records',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-download',
                        'sort' => 0,
                        'item' => 'finance_balance_rechargeRecord',
                        'parents' => ['finance', 'balance'],
                        'child' => [
                            'balanceRechargeRecordsSee' => [
                                'name' => '浏览记录',
                                'url' => 'finance.balance-recharge-records.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'balanceRechargeRecordsSee',
                                'parents' => ['finance', 'balance', 'finance_balance_rechargeRecord'],
                            ],
                            'balanceRechargeRecordsExport' => [
                                'name' => '导出记录',
                                'url' => 'finance.balance-recharge-records.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'balanceRechargeRecordsExport',
                                'parents' => ['finance', 'balance', 'finance_balance_rechargeRecord'],
                            ]
                        ]
                    ],

                    //茶余饭后独立开发充值满额统计
                    /*'finance_balance_recharge_statistics' => [
                'name'              => '满额统计',
                'url'               => 'finance.balanceRechargeStatistics.index',
                'url_params'        => '',
                'permit'            => 1,
                'menu'              => 1,
                'icon'              => 'fa-download',
                'sort'              => 0,
                'item'              => 'finance_balance_recharge_statistics',
                'parents'           => ['finance', 'balance'],
            ],*/


                    'finance_balance_tansferRecord' => [
                        'name' => '转让记录',
                        'url' => 'finance.balance.transferRecord',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-external-link',
                        'sort' => 0,
                        'item' => 'finance_balance_tansferRecord',
                        'parents' => ['finance', 'balance'],
                    ],

                    'finance_balance_records' => [
                        'name' => '余额明细',
                        'url' => 'finance.balance-records.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-file-text-o',
                        'sort' => 0,
                        'item' => 'finance_balance_balanceDetail',
                        'parents' => ['finance', 'balance'],
                        'child' => [

                            'finance_balance_records_see' => [
                                'name' => '浏览记录',
                                'url' => 'finance.balance-records.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'finance_balance_records_see',
                                'parents' => ['finance', 'balance', 'finance_balance_records'],
                            ],

                            'finance_balance_records_export' => [
                                'name' => '导出 EXCEL',
                                'url' => 'finance.balance-records.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'finance_balance_records_export',
                                'parents' => ['finance', 'balance', 'finance_balance_records'],
                            ],

                            'finance_balance_records_detail' => [
                                'name' => '明细详情',
                                'url' => 'finance.balance.lookBalanceDetail',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'finance_balance_records_detail',
                                'parents' => ['finance', 'balance', 'finance_balance_records'],
                            ],
                        ],


                    ],

                    'income_records' => [
                        'name' => '收入明细',
                        'url' => 'income.income-records.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-file-text-o',
                        'sort' => 0,
                        'item' => 'income_records',
                        'parents' => ['finance'],
                        'child' => [
                            'income_records_export' => [
                                'name' => '导出excel',
                                'url' => 'income.income-records.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '0',
                                'item' => 'income_records_export',
                                'parents' => ['finance', 'income_records'],
                            ],
                        ]
                    ],

                    'withdraw_set' => [
                        'name' => '提现设置',
                        'url' => 'finance.withdraw-set.see',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-gear',
                        'sort' => 0,
                        'item' => 'withdraw_set',
                        'parents' => ['finance', 'withdraw'],
                        'child' => [
                            'withdraw_set_see' => [
                                'name' => '编辑保存',
                                'url' => 'finance.withdraw-set.see',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => '0',
                                'item' => 'withdraw_set_see',
                                'parents' => ['finance', 'withdraw', 'withdraw_set'],
                            ],
                        ],
                    ],

                    'withdraw_statistics' => [
                        'name' => '提现统计',
                        'url' => 'finance.withdraw-statistics.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-line-chart',
                        'sort' => 0,
                        'item' => 'withdraw_statistics',
                        'parents' => ['finance', 'withdraw'],
                    ],

                    'withdrawRecords' => [
                        'name' => '提现记录',
                        'url' => 'withdraw.records',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-pencil',
                        'sort' => 0,
                        'item' => 'withdrawRecords',
                        'parents' => ['finance'],
                        'child' => [

                            'withdrawRecordsIndex' => [
                                'name' => '全部记录',
                                'url' => 'withdraw.records.index',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecordsIndex',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                            'withdrawRecordsInitial' => [
                                'name' => '待审核',
                                'url' => 'withdraw.records.initial',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecordsInitial',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                            'withdrawRecordsAudit' => [
                                'name' => '待打款',
                                'url' => 'withdraw.records.audit',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecordsAudit',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                            'withdrawRecordsPaying' => [
                                'name' => '打款中',
                                'url' => 'withdraw.records.paying',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecordsPaying',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                            'withdrawRecordsPayed' => [
                                'name' => '已打款',
                                'url' => 'withdraw.records.payed',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecordsPayed',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                            'withdrawRecordsRebut' => [
                                'name' => '已驳回',
                                'url' => 'withdraw.records.rebut',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecordsRebut',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                            'withdrawRecordsInvalid' => [
                                'name' => '无效提现',
                                'url' => 'withdraw.records.invalid',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecordsInvalid',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],

                            'withdrawRecords_balance_detail' => [
                                'name' => '余额提现详情',
                                'url' => 'finance.balance-withdraw.detail',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecords_balance_detail',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                            'withdrawRecords_prepayment_detail' => [
                                'name' => '预付款提现详情',
                                'url' => 'finance.prepayment-withdraw.detail',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecords_balance_detail',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                            'withdrawRecords_balance_examine' => [
                                'name' => '余额审核打款',
                                'url' => 'finance.balance-withdraw.examine',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecords_balance_examine',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],

                            'withdrawRecordsDetail' => [
                                'name' => '收入提现详情',
                                'url' => 'withdraw.detail.index',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecordsDetail',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],

                            'withdrawRecordsAudited' => [
                                'name' => '收入提现审核',
                                'url' => 'withdraw.audit.index',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecordsAudited',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                            'withdrawRecordsAuditedRebut' => [
                                'name' => '收入提现审核后驳回',
                                'url' => 'withdraw.audited-rebut.index',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecordsAudited',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                            'withdrawRecordsPay' => [
                                'name' => '收入提现打款',
                                'url' => 'withdraw.pay.index',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecordsPay',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                            'withdrawRecordsAgainPay' => [
                                'name' => '收入提现重新打款',
                                'url' => 'withdraw.again-pay.index',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecordsAgainPay',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                            'withdrawRecordsConfirmPay' => [
                                'name' => '收入提现确认打款/线下打款',
                                'url' => 'withdraw.confirm-pay.index',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecordsAgainPay',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                            'finance.withdraw.sendCode' => [
                                'name' => '二次校验发送验证码',
                                'url' => 'finance.withdraw.sendCode',
                                'url_params' => "",
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecords_examine',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                            'finance.withdraw.checkVerifyCode' => [
                                'name' => '二次校验校验验证码',
                                'url' => 'finance.withdraw.checkVerifyCode',
                                'url_params' => "",
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'withdrawRecords_examine',
                                'parents' => ['finance', 'withdrawRecords'],
                            ],
                        ],
                    ],
                    'finance_point' => [
                        'name' => '积分管理',
                        'url' => 'finance.point-member.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-database',
                        'sort' => 0,
                        'item' => 'finance_point',
                        'parents' => ['finance'],
                        'child' => [
                            'point_set' => [
                                'name' => '基础设置',
                                'url' => 'finance.point-set.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-gear',
                                'sort' => 0,
                                'item' => 'point_set',
                                'parents' => ['finance', 'finance_point'],
                            ],
                            'point_member' => [
                                'name' => '会员积分',
                                'url' => 'finance.point-member.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-database',
                                'sort' => 0,
                                'item' => 'point_member',
                                'parents' => ['finance', 'finance_point'],
                            ],
                            'point_member_export' => [
                                'name' => '会员积分导出',
                                'url' => 'point.member-export.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-database',
                                'sort' => 0,
                                'item' => 'point_member_export',
                                'parents' => ['finance', 'finance_point'],
                            ],
                            'point_recharge' => [
                                'name' => '积分充值',
                                'url' => 'point.recharge.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'point_recharge',
                                'parents' => ['finance', 'finance_point'],
                            ],
                            'point_recharge_records' => [
                                'name' => '充值记录',
                                'url' => 'point.recharge-records.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-file-text-o',
                                'sort' => 0,
                                'item' => 'point_recharge_records',
                                'parents' => ['finance', 'finance_point'],
                            ],
                            'pointRechargeRecordsExport' => [
                                'name' => '充值记录导出',
                                'url' => 'point.recharge-export.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-file-text-o',
                                'sort' => 0,
                                'item' => 'point_recharge_records',
                                'parents' => ['finance', 'finance_point'],
                            ],
                            'point_log' => [
                                'name' => '积分明细',
                                'url' => 'point.records.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-file-text-o',
                                'sort' => 0,
                                'item' => 'point_log',
                                'parents' => ['finance', 'finance_point'],
                            ],
                            'point_export' => [
                                'name' => '明细导出',
                                'url' => 'point.export.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-file-text-o',
                                'sort' => 0,
                                'item' => 'point_export',
                                'parents' => ['finance', 'finance_point'],
                            ],

                            'point_queue' => [
                                'name' => '积分队列',
                                'url' => 'point.queue.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-file-text-o',
                                'sort' => 0,
                                'item' => 'point_queue',
                                'parents' => ['finance', 'finance_point'],
                            ],

                            'point_queue_log' => [
                                'name' => '队列明细',
                                'url' => 'point.queue-log.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-file-text-o',
                                'sort' => 0,
                                'item' => 'point_queue_log',
                                'parents' => ['finance', 'finance_point'],
                            ],
                            'point_balance_see' => [
                                'name' => '转入余额设置',
                                'url' => 'point.to-balance.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'point_balance_see',
                                'parents' => ['finance', 'finance_point'],
                            ],
                            'point_balance_update' => [
                                'name' => '修改转入余额',
                                'url' => 'point.to-balance.update',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'point_balance_update',
                                'parents' => ['finance', 'finance_point'],
                            ],

                            'point_love_see' => [
                                'name' => '查看转出设置',
                                'url' => 'finance.point-love.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'point_love_see',
                                'parents' => ['finance', 'finance_point'],
                            ],

                            'point_love_update' => [
                                'name' => '修改转出设置',
                                'url' => 'finance.point-love.update',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'point_love_update',
                                'parents' => ['finance', 'finance_point'],
                            ],
                        ],
                    ],
                    'remittance_audit' => [
                        'name' => '转账审核',
                        'url' => 'finance.remittance-audit.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-file-text-o',
                        'sort' => 0,
                        'item' => 'remittance_audit',
                        'parents' => ['finance'],
                        'bubble' => RemittanceAuditFlow::getCount(),
                        'child' => [
                            'remittance_audit_ajax' => [
                                'name' => '转账审核全部',
                                'url' => 'finance.remittance-audit.ajax',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-gear',
                                'sort' => 0,
                                'item' => 'remittance_audit_ajax',
                                'parents' => ['finance', 'remittance_audit'],
                            ],
                            'remittance_audit_detail' => [
                                'name' => '转账审核详情',
                                'url' => 'remittanceAudit.detail.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-gear',
                                'sort' => 0,
                                'item' => 'remittance_audit_detail',
                                'parents' => ['finance', 'remittance_audit'],
                            ],
                            'remittance_audit_pass' => [
                                'name' => '转账审核通过',
                                'url' => 'remittanceAudit.operation.pass',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-gear',
                                'sort' => 0,
                                'item' => 'remittance_audit_pass',
                                'parents' => ['finance', 'remittance_audit'],
                            ],
                            'remittance_audit_reject' => [
                                'name' => '转账审核拒绝',
                                'url' => 'remittanceAudit.operation.reject',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-gear',
                                'sort' => 0,
                                'item' => 'remittance_audit_reject',
                                'parents' => ['finance', 'remittance_audit'],
                            ],
                        ]
                    ],
                    'profit_advertisement' => [
                        'name' => '收益广告',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-image',
                        'sort' => 0,
                        'item' => 'profit_advertisement',
                        'url' => 'finance.advertisement.index',
                        'url_params' => '',
                        'parents' => ['finance', 'profit_advertisement'],
                        'child' => [
                            'profit_advertisement_advertisement_add' => [
                                'name' => '添加广告',
                                'permit' => 1,
                                'menu' => '',
                                'icon' => '',
                                'url' => 'finance.advertisement.add',
                                'url_params' => '',
                                'parents' => ['finance', 'profit_advertisement'],
                                'child' => []
                            ],
                            'profit_advertisement_advertisement_edit' => [
                                'name' => '编辑广告',
                                'permit' => 1,
                                'menu' => '',
                                'icon' => '',
                                'url' => 'finance.advertisement.edit',
                                'url_params' => '',
                                'parents' => ['finance', 'profit_advertisement'],
                                'child' => []
                            ],
                            'profit_advertisement_advertisement_del' => [
                                'name' => '删除广告',
                                'permit' => 1,
                                'menu' => '',
                                'icon' => '',
                                'url' => 'finance.advertisement.del',
                                'url_params' => '',
                                'parents' => ['finance', 'profit_advertisement'],
                                'child' => []
                            ],
                            'profit_advertisement_advertisement_change' => [
                                'name' => '切换状态',
                                'permit' => 1,
                                'menu' => '',
                                'icon' => '',
                                'url' => 'finance.advertisement.setStatus',
                                'url_params' => '',
                                'parents' => ['finance', 'profit_advertisement'],
                                'child' => []
                            ],
                        ]
                    ],

                    'excelRecharge' => [
                        'name' => '批量充值',
                        'url' => 'excelRecharge.page.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-line-chart',
                        'sort' => 0,
                        'item' => 'excelRecharge',
                        'parents' => ['finance'],
                        'child' => [
                            'excelRechargeExample' => [
                                'name' => '下载模版',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'url' => 'excelRecharge.example.index',
                                'url_params' => '',
                                'item' => 'excelRechargeExample',
                                'parents' => ['finance', 'excelRecharge']
                            ],
                            'excelRechargeConfirm' => [
                                'name' => '确认充值',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'url' => 'excelRecharge.confirm.index',
                                'url_params' => '',
                                'item' => 'excelRechargeConfirm',
                                'parents' => ['finance', 'excelRecharge']
                            ],
                            'excelRechargeRecords' => [
                                'name' => '充值记录',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'url' => 'excelRecharge.records.index',
                                'url_params' => '',
                                'item' => 'excelRechargeRecords',
                                'parents' => ['finance', 'excelRecharge']
                            ],
                            'excelRechargeDetail' => [
                                'name' => '详情记录',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'url' => 'excelRecharge.detail.index',
                                'url_params' => '',
                                'item' => 'excelRechargeDetail',
                                'parents' => ['finance', 'excelRecharge']
                            ],
                        ]
                    ],

                    'balanceRechargeCheck' => [
                        'name' => '审核列表',
                        'url' => 'finance.balance-recharge-check.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa fa-dashboard',
                        'sort' => 0,
                        'item' => 'balanceRechargeCheck',
                        'parents' => ['finance'],
                        'child' => [
                            'balanceRechargeCheckList' => [
                                'name' => '余额充值审核列表',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'url' => 'finance.balance-recharge-check.get-list',
                                'url_params' => '',
                                'item' => 'balanceRechargeCheckList',
                                'parents' => ['finance', 'balanceRechargeCheck']
                            ],
                            'balanceRechargeCheckUpdate' => [
                                'name' => '余额充值审核',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'url' => 'finance.balance-recharge-check.check',
                                'url_params' => '',
                                'item' => 'balanceRechargeCheckUpdate',
                                'parents' => ['finance', 'balanceRechargeCheck']
                            ],
                            'balanceRechargeCheckUpload' => [
                                'name' => '附件上传',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'url' => 'finance.balance-recharge-check.upload-file',
                                'url_params' => '',
                                'item' => 'balanceRechargeCheckUpload',
                                'parents' => ['finance', 'balanceRechargeCheck']
                            ],
                        ]
                    ],
                    'balanceRechargePaySet' => [
                        'name' => '指定支付',
                        'url' => 'finance.balance-recharge-set.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa fa-minus-square-o',
                        'sort' => 0,
                        'item' => 'balanceRechargePaySet',
                        'parents' => ['finance'],
                        'child' => [
                            'balanceRechargePaySetStore' => [
                                'name' => '指定支付设置保存',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'url' => 'finance.balance-recharge-set.store',
                                'url_params' => '',
                                'item' => 'balanceRechargePaySetStore',
                                'parents' => ['finance', 'balanceRechargePaySet']
                            ],
                        ]
                    ],


                ],

            ],

            'charts' => [
                'name' => '统计',
                'url' => 'charts.member.count.index',
                'url_params' => '',
                'permit' => 1,
                'menu' => 1,
                'icon' => 'fa-bar-chart-o',
                'sort' => 3,
                'top_show' => 0,               //顶部导航是否显示
                'left_first_show' => 1,           //左侧一级导航是否显示
                'left_second_show' => 1,           //左侧二级导航是否显示
                'item' => 'system',
                'parents' => [],
                'child' => [

                    'member_count_charts' => [
                        'name' => '会员数据统计',
                        'url' => 'charts.member.count.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'member_count_charts',
                        'parents' => ['charts'],

                    ],

                    'member_offline_charts' => [
                        'name' => '会员关系统计',
                        'url' => 'charts.member.offline-count.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'member_offline_charts',
                        'parents' => ['charts'],
                        'child' => [
                            'member_offline_count_charts' => [
                                'name' => '下线人数排行',
                                'url' => 'charts.member.offline-count.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_offline_count_charts',
                                'parents' => ['charts', 'member_offline_charts'],

                            ],

                            'member_offline_order_charts' => [
                                'name' => '下线订单排行',
                                'url' => 'charts.member.offline-order.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_offline_order_charts',
                                'parents' => ['charts', 'member_offline_charts'],

                            ],

                            'member_offline_team_order_charts' => [
                                'name' => '团队支付订单排行',
                                'url' => 'charts.member.offline-team-order.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_offline_team_order_charts',
                                'parents' => ['charts', 'member_offline_charts'],
                            ],

                            'member_offline_commission_order_charts' => [
                                'name' => '分销订单排行',
                                'url' => 'charts.member.offline-commission-order.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_offline_commission_order_charts',
                                'parents' => ['charts', 'member_offline_charts'],
                            ],
                        ]

                    ],

                    /* 'goods_sales_count_charts'     => [
                 'name'          => '商品数据统计',
                 'url'           => 'charts.goods.sales-count.index',
                 'url_params'    => '',
                 'permit'        => 1,
                 'menu'          => 1,
                 'icon'          => 'fa-bar-chart-o',
                 'sort'          => 0,
                 'item'          => 'goods_charts',
                 'parents'       => ['charts'],
             ],

             'order_total_charts'=> [
                 'name'          => '订单数据统计',
                 'url'           => 'charts.order.today-trends.index',
                 'url_params'    => '',
                 'permit'        => 1,
                 'menu'          => 1,
                 'icon'          => 'fa-bar-chart-o',
                 'sort'          => 0,
                 'item'          => 'order_total_charts',
                 'parents'       => ['charts'],
                 'child'         => [

                     'today_order_total_charts'     => [
                         'name'          => '今日订单统计',
                         'url'           => 'charts.order.today-trends.index',
                         'url_params'    => '',
                         'permit'        => 1,
                         'menu'          => 1,
                         'icon'          => '',
                         'sort'          => 0,
                         'item'          => 'today_order_total_charts',
                         'parents'       => ['charts','order_total_charts'],

                     ],

                     'all_order_total_charts'     => [
                         'name'          => '全部订单统计',
                         'url'           => 'charts.order.order-trends.index',
                         'url_params'    => '',
                         'permit'        => 1,
                         'menu'          => 1,
                         'icon'          => '',
                         'sort'          => 0,
                         'item'          => 'all_order_total_charts',
                         'parents'       => ['charts','order_total_charts'],

                     ],


                 ]
             ],*/

                    'order_ranking_charts' => [
                        'name' => '会员订单排行',
                        'url' => 'charts.order.order-ranking.count',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'order_ranking_charts',
                        'parents' => ['charts'],
                        'child' => [

                            'order_count_ranking_charts' => [
                                'name' => '订单数量排行',
                                'url' => 'charts.order.order-ranking.count',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'order_count_ranking_charts',
                                'parents' => ['charts', 'order_ranking_charts'],

                            ],

                            'order_money_ranking_charts' => [
                                'name' => '订单金额排行',
                                'url' => 'charts.order.order-ranking.money',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'order_money_ranking_charts',
                                'parents' => ['charts', 'order_ranking_charts'],

                            ],

                        ]
                    ],

                    'member_phone_attribution' => [
                        'name' => '手机归属地统计',
                        'url' => 'charts.phone.phone-attribution.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,//0-关
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'order_ranking_charts',
                        'parents' => ['charts'],
                        'child' => []
                    ],

                    'order_dividend_charts' => [
                        'name' => '订单分润',
                        'url' => 'charts.order.order-dividend.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'order_ranking_charts',
                        'parents' => ['charts'],
                        'child' => [

                            'order_dividend_charts_export' => [
                                'name' => '订单分润导出',
                                'url' => 'charts.order.order-dividend.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'order_dividend_charts_export',
                                'parents' => ['charts', 'order_dividend_charts'],
                            ],
                        ]
                    ],

                    'transaction_amount_charts' => [
                        'name' => '交易额统计',
                        'url' => 'charts.order.transaction-amount.count',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'order_ranking_charts',
                        'parents' => ['charts'],
                        'child' => [
                            'transaction_amount_charts_export' => [
                                'name' => '交易额统计导出',
                                'url' => 'charts.order.transaction-amount.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'transaction_amount_charts_export',
                                'parents' => ['charts', 'transaction_amount_charts'],
                            ],
                        ]
                    ],

                    'merchant_income_charts' => [
                        'name' => '商家收入统计',
                        'url' => 'charts.merchant.supplier-income.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'order_ranking_charts',
                        'parents' => ['charts'],
                        'child' => [
                            'supplier_charts' => [
                                'name' => '供应商收入排行',
                                'url' => 'charts.merchant.supplier-income.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_offline_count_charts',
                                'parents' => ['charts', 'merchant_income_charts'],
                                'child' => [
                                    'supplier_charts_export' => [
                                        'name' => '供应商收入排行导出',
                                        'url' => 'charts.merchant.supplier-income.export',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'supplier_charts_export',
                                        'parents' => ['charts', 'merchant_income_charts', 'supplier_charts'],
                                    ],
                                ]
                            ],
                            'store_charts' => [
                                'name' => '门店收入排行',
                                'url' => 'charts.merchant.store-income.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_offline_order_charts',
                                'parents' => ['charts', 'merchant_income_charts'],
                                'child' => [
                                    'store_charts_export' => [
                                        'name' => '门店收入排行导出',
                                        'url' => 'charts.merchant.store-income.export',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'store_charts_export',
                                        'parents' => ['charts', 'merchant_income_charts', 'store_charts'],
                                    ],
                                ]
                            ],
                            'cashier_charts' => [
                                'name' => '收银台收入排行',
                                'url' => 'charts.merchant.cashier-income.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_offline_order_charts',
                                'parents' => ['charts', 'merchant_income_charts'],
                                'child' => [
                                    'cashier_charts_export' => [
                                        'name' => '收银台收入排行导出',
                                        'url' => 'charts.merchant.cashier-income.export',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'cashier_charts_export',
                                        'parents' => ['charts', 'merchant_income_charts', 'cashier_charts'],
                                    ],
                                ]
                            ],
                        ]
                    ],
                    'shop_income_list' => [
                        'name' => '平台收益列表',
                        'url' => 'charts.income.shop-income-list.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'order_ranking_charts',
                        'parents' => ['charts'],
                        'child' => [
                            'shop_income_export' => [
                                'name' => '平台收益列表导出',
                                'url' => 'charts.income.shop-income-list.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'shop_income_export',
                                'parents' => ['charts', 'shop_income_list'],
                            ],
                        ]
                    ],
                    'shop_income_charts' => [
                        'name' => '平台收益统计',
                        'url' => 'charts.income.shop-income-statistics.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'order_ranking_charts',
                        'parents' => ['charts'],
                        'child' => [
                            'shop_income_charts_export' => [
                                'name' => '平台收益统计导出',
                                'url' => 'charts.income.shop-income-statistics.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'shop_income_charts_export',
                                'parents' => ['charts', 'shop_income_charts'],
                            ],
                        ]
                    ],
                    'member_income_charts' => [
                        'name' => '会员收入统计',
                        'url' => 'charts.income.member-income.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'order_ranking_charts',
                        'parents' => ['charts'],
                        'child' => [
                            'member_income_charts_detail' => [
                                'name' => '会员收入详情',
                                'url' => 'charts.income.member-income.detail',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bar-chart-o',
                                'sort' => 0,
                                'item' => 'order_ranking_charts',
                                'parents' => ['charts', 'member_income_charts'],
                            ],
                            'member_income_charts_back' => [
                                'name' => '返回按钮',
                                'url' => 'charts.income.member_income.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bar-chart-o',
                                'sort' => 0,
                                'item' => 'member_income_charts_back',
                                'parents' => ['charts', 'member_income_charts'],
                            ],
                            'member_income_charts_back' => [
                                'name' => '返回按钮',
                                'url' => 'charts.income.member_income.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bar-chart-o',
                                'sort' => 0,
                                'item' => 'member_income_charts_back',
                                'parents' => ['charts', 'member_income_charts'],
                            ],
                            'member_income_charts_export' => [
                                'name' => '会员收入导出',
                                'url' => 'charts.income.member-income.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bar-chart-o',
                                'sort' => 0,
                                'item' => 'member_income_charts_export',
                                'parents' => ['charts', 'member_income_charts'],
                            ],
                        ]
                    ],
                    'poundage_income_charts' => [
                        'name' => '手续费/劳务税汇总',
                        'url' => 'charts.income.poundage.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'order_ranking_charts',
                        'parents' => ['charts'],
                        'child' => [
                            'poundage_income_charts' => [
                                'name' => '手续费明细',
                                'url' => 'charts.income.poundage.detail',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bar-chart-o',
                                'sort' => 0,
                                'item' => 'order_ranking_charts',
                                'parents' => ['charts', 'poundage_income_charts'],
                            ],
                            'poundage_income_export' => [
                                'name' => '导出',
                                'url' => 'charts.income.poundage.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-bar-chart-o',
                                'sort' => 0,
                                'item' => 'poundage_income_export',
                                'parents' => ['charts', 'poundage_income_charts'],
                            ]
                        ],
                    ],
                    'point_charts' => [
                        'name' => '积分数据统计',
                        'url' => 'charts.finance.point.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'order_ranking_charts',
                        'parents' => ['charts'],
                        'child' => [
                            'point_charts_export' => [
                                'name' => '积分数据统计导出',
                                'url' => 'charts.finance.point.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-bar-chart-o',
                                'sort' => 0,
                                'item' => 'point_charts_export',
                                'parents' => ['charts', 'point_charts'],
                            ]
                        ]
                    ],
                    'money_charts' => [
                        'name' => '余额数据统计',
                        'url' => 'charts.finance.balance.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'order_ranking_charts',
                        'parents' => ['charts'],
                        'child' => [
                            'money_charts_export' => [
                                'name' => '余额数据统计导出',
                                'url' => 'charts.finance.balance.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-bar-chart-o',
                                'sort' => 0,
                                'item' => 'money_charts_export',
                                'parents' => ['charts', 'money_charts'],
                            ]
                        ]
                    ],
                    'coupon_charts' => [
                        'name' => '赠送优惠券统计',
                        'url' => 'charts.finance.coupon.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'order_ranking_charts',
                        'parents' => ['charts'],
                        'child' => [
                            'coupon_charts_export' => [
                                'name' => '赠送优惠券统计导出',
                                'url' => 'charts.finance.coupon.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-bar-chart-o',
                                'sort' => 0,
                                'item' => 'coupon_charts_export',
                                'parents' => ['charts', 'coupon_charts'],
                            ]
                        ]
                    ],
                    'goods_charts' => [
                        'name' => '商品销售统计',
                        'url' => 'charts.goods.sales-volume-count.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'order_ranking_charts',
                        'parents' => ['charts'],
                        'child' => [
                            'goods_volume_charts' => [
                                'name' => '商品销量排行',
                                'url' => 'charts.goods.sales-volume-count.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_offline_count_charts',
                                'parents' => ['charts', 'goods_charts'],

                            ],
                            'goods_sales_charts' => [
                                'name' => '商品销售额排行',
                                'url' => 'charts.goods.sales-volume-count.sales-price',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'member_offline_order_charts',
                                'parents' => ['charts', 'goods_charts'],

                            ]
                        ]
                    ],
                    'team_charts' => [
                        'name' => '会员一二级团队统计',
                        'url' => 'charts.team.list.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bar-chart-o',
                        'sort' => 0,
                        'item' => 'order_ranking_charts',
                        'parents' => ['charts'],
                        'child' => [
                            'team_charts_export' => [
                                'name' => '会员一二级团队统计导出',
                                'url' => 'charts.team.list.export',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => '',
                                'sort' => 0,
                                'item' => 'team_charts_export',
                                'parents' => ['charts', 'team_charts'],

                            ]
                        ]
                    ],
                ],
            ],

            'system' => [
                'name' => '系统',
                'url' => 'setting.shop.entry',
                'url_params' => '',
                'permit' => 1,
                'menu' => 1,
                'icon' => 'fa-cogs',
                'sort' => 6,
                'top_show' => 0,               //顶部导航是否显示
                'left_first_show' => 1,               //左侧导航是否显示
                'left_second_show' => 1,
                'item' => 'system',
                'parents' => [],
                'child' => [

                    'shop' => [
                        'name' => '商城入口',
                        'url' => 'setting.shop.entry',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-hand-o-right',
                        'sort' => 0,
                        'item' => 'shop',
                        'parents' => ['system'],
                    ],

                    'member.member.updateWechatOpenData' => [
                        'name' => '微信开放平台数据同步',
                        'url' => 'member.member.updateWechatOpenData',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => 'fa-hand-o-right',
                        'sort' => 0,
                        'item' => 'shop',
                        'parents' => ['system'],
                    ],

                    'Setting' => [
                        'name' => '商城设置',
                        'url' => 'setting.shop.index',
                        'url_params' => '',
                        'permit' => 1,
                        'top_show' => 1,               //顶部导航是否显示
                        'left_first_show' => 1,           //左侧一级导航是否显示
                        'left_second_show' => 1,           //左侧二级导航是否显示
                        'menu' => 1,
                        'icon' => 'fa-cog',
                        'sort' => 0,
                        'item' => 'Setting',
                        'parents' => ['system'],
                        'child' => [

                            'setting_shop1' => [
                                'name' => '商城设置',
                                'url' => 'setting.shop.index',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 1,
                                'icon' => 'fa-sliders',
                                'sort' => 0,
                                'item' => 'setting_shop',
                                'parents' => ['system', 'Setting'],
                            ],
                            'setting_shop2' => [
                                'name' => '商城设置',
                                'url' => 'setting.shop.shop-set-info',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-sliders',
                                'sort' => 0,
                                'item' => 'setting_shop',
                                'parents' => ['system', 'Setting'],
                            ],

                            'setting_member' => [
                                'name' => '会员设置',
                                'url' => 'setting.shop.member',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '3',
                                'item' => 'setting_member',
                                'parents' => ['system', 'Setting'],
                                'child' => [
                                    'check_invite_code' => [
                                        'name' => '会员设置数据保存',
                                        'url' => 'setting.shop.checkInviteCode',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 1,
                                        'icon' => 'fa-sliders',
                                        'sort' => 0,
                                        'item' => 'check_invite_code',
                                        'parents' => ['system', 'Setting', 'setting_member'],
                                    ],
                                ]
                            ],
                            'setting_shop_form' => [
                                'name' => '会员资料设置',
                                'url' => 'setting.form.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bell-o',
                                'sort' => '7',
                                'item' => 'setting_shop_form',
                                'parents' => ['system', 'Setting'],
                            ],

                            'setting_register' => [
                                'name' => '注册设置',
                                'url' => 'setting.shop.register',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '3',
                                'item' => 'setting_register',
                                'parents' => ['system', 'Setting', 'setting_shop'],
                            ],

                            'setting_order' => [
                                'name' => '订单设置',
                                'url' => 'setting.shop.order',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '3',
                                'item' => 'setting_order',
                                'parents' => ['system', 'Setting'],
                            ],

                            'express_info' => [
                                'name' => '物流查询',
                                'url' => 'setting.shop.express-info',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bell-o',
                                'sort' => '7',
                                'item' => 'setting_shop_protocol',
                                'parents' => ['system', 'Setting'],
                            ],
                            'setting_sms' => [
                                'name' => '短信',
                                'url' => 'setting.shop.sms',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '6',
                                'item' => 'setting_sms',
                                'parents' => ['system', 'Setting', 'setting_shop'],
                            ],
                            'setting_contact' => [
                                'name' => '联系方式',
                                'url' => 'setting.shop.contact',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '5',
                                'item' => 'setting_contact',
                                'parents' => ['system', 'Setting', 'setting_shop'],
                            ],

                            'setting_category' => [
                                'name' => '分类',
                                'url' => 'setting.shop.category',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '4',
                                'item' => 'setting_category',
                                'parents' => ['system', 'Setting', 'setting_shop'],
                            ],

                            'setting_coupon' => [
                                'name' => '优惠券',
                                'url' => 'setting.coupon.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '6',
                                'item' => 'setting_coupon',
                                'parents' => ['system', 'Setting', 'setting_shop'],
                            ],

                            'setting_shop_share' => [
                                'name' => '分享',
                                'url' => 'setting.shop.share',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-link',
                                'sort' => '5',
                                'item' => 'setting_shop_share',
                                'parents' => ['system', 'Setting'],
                            ],

                            'setting_shop_slide' => [
                                'name' => '幻灯片',
                                'url' => 'setting.slide.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bell-o',
                                'sort' => '6',
                                'item' => 'setting_shop_slide',
                                'parents' => ['system', 'Setting'],
                                'child' => [

                                    'setting_shop_slide_index' => [
                                        'name' => '浏览列表',
                                        'url' => 'setting.slide.index',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'item' => 'setting_shop_slide_index',
                                        'parents' => ['system', 'Setting', 'setting_shop_slide'],
                                    ],

                                    'setting_shop_slide_add' => [
                                        'name' => '添加幻灯片',
                                        'url' => 'setting.slide.create',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'item' => 'setting_shop_slide_add',
                                        'parents' => ['system', 'Setting', 'setting_shop_slide'],
                                    ],

                                    'setting_shop_slide_edit' => [
                                        'name' => '修改幻灯片',
                                        'url' => 'setting.slide.edit',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'item' => 'setting_shop_slide_edit',
                                        'parents' => ['system', 'Setting', 'setting_shop_slide'],
                                    ],

                                    'setting_shop_slide_deleted' => [
                                        'name' => '删除幻灯片',
                                        'url' => 'setting.slide.deleted',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'item' => 'setting_shop_slide_deleted',
                                        'parents' => ['system', 'Setting', 'setting_shop_slide'],
                                    ],
                                ]
                            ],
                            'setting_shop_adv' => [
                                'name' => '广告位',
                                'url' => 'setting.shop-advs.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bell-o',
                                'sort' => '7',
                                'item' => 'setting_shop_adv',
                                'parents' => ['system', 'Setting'],
                            ],
                            'passwordSetting' => [
                                'name' => '资产密码',
                                'url' => 'password.setting.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bell-o',
                                'sort' => '7',
                                'item' => 'passwordSetting',
                                'parents' => ['system', 'Setting'],
                            ],
                            'password.setting.verifyWithdrawCode' => [
                                'name' => '校验提现手机号验证码',
                                'url' => 'password.setting.verifyWithdrawCode',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-bell-o',
                                'sort' => '7',
                                'item' => 'passwordSetting',
                                'parents' => ['system', 'Setting'],
                            ],
                            'password.setting.sendVerifyCode' => [
                                'name' => '发送提现手机号验证码',
                                'url' => 'password.setting.sendVerifyCode',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-bell-o',
                                'sort' => '7',
                                'item' => 'passwordSetting',
                                'parents' => ['system', 'Setting'],
                            ],
                            'map_setting' => [
                                'name' => '地图设置',
                                'url' => 'map.setting.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bell-o',
                                'sort' => '7',
                                'item' => 'map_setting',
                                'parents' => ['system', 'Setting'],
                            ],
                            'outside_app_setting' => [
                                'name' => 'API应用',
                                'url' => 'setting.outside-app.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bell-o',
                                'sort' => '3',
                                'item' => 'outside_app_setting',
                                'parents' => ['system', 'Setting'],
                                'child' => [
                                    'outside_app_setting_create' => [
                                        'name' => '开启API应用',
                                        'url' => 'setting.outside-app.create-app',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'item' => 'outside_app_setting_create',
                                        'parents' => ['system', 'Setting', 'outside_app_setting'],
                                    ],
                                    'outside_app_setting_store' => [
                                        'name' => 'API应用设置保存',
                                        'url' => 'setting.outside-app.store',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'item' => 'outside_app_setting_store',
                                        'parents' => ['system', 'Setting', 'outside_app_setting'],
                                    ],

                                    'outside_app_setting_updateSecret' => [
                                        'name' => 'API应用更新密钥',
                                        'url' => 'setting.outside-app.update-secret',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'item' => 'outside_app_setting_updateSecret',
                                        'parents' => ['system', 'Setting', 'outside_app_setting'],
                                    ],
                                ],
                            ],
                            'setting_email' => [
                                'name' => '邮箱配置',
                                'url' => 'setting.shop.email',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => '',
                                'sort' => '6',
                                'item' => 'setting_email',
                                'parents' => ['system', 'Setting', 'setting_shop'],
                            ]
                        ],
                    ],
                    'setting_shop_trade' => [
                        'name' => '交易设置',
                        'url' => 'setting.shop.trade',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-compress',
                        'sort' => '7',
                        'item' => 'setting_shop_trade',
                        'parents' => ['system'],

                    ],


                    'setting_shop_pay' => [
                        'name' => '支付方式',
                        'url' => 'setting.shop.pay',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-credit-card',
                        'sort' => '3',
                        'item' => 'setting_shop_pay',
                        'parents' => ['system'],
                    ],
                    'setting_wechat_notice' => [
                        'name' => '微信模板消息',
                        'url' => 'setting.shop.notice',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-wechat',
                        'sort' => '6',
                        'item' => 'setting_wechat_notice',
                        'parents' => ['system'],
                        'child' => [
                            'setting_shop_notice' => [
                                'name' => '公众号消息提醒 ',
                                'url' => 'setting.shop.notice',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bell-o',
                                'sort' => '6',
                                'item' => 'setting_shop_notice',
                                'parents' => ['system', 'setting_wechat_notice'],
                                'child' => [

                                    'setting_shop_default_notice_open' => [
                                        'name' => '默认消息模版开启',
                                        'url' => 'setting.default-notice.index',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'item' => 'setting_shop_default_notice_open',
                                        'parents' => ['system', 'setting_shop_notice'],
                                    ],
                                    'setting_shop_default_notice_closed' => [
                                        'name' => '默认消息模版取消',
                                        'url' => 'setting.default-notice.cancel',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'item' => 'setting_shop_default_notice_closed',
                                        'parents' => ['system', 'setting_shop_notice'],
                                    ],
                                ]
                            ],

                            'setting_wechat_notice' => [
                                'name' => '公众号模板管理',
                                'url' => 'setting.wechat-notice.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-wechat',
                                'sort' => '6',
                                'item' => 'setting_wechat_notice',
                                'parents' => ['system', 'setting_wechat_notice'],
                                'child' => [

                                    'setting_wechat_notice_see' => [
                                        'name' => '查看',
                                        'url' => 'setting.wechat-notice.see',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'item' => 'setting_wechat_notice_see',
                                        'parents' => ['system', 'setting_wechat_notice'],
                                    ],

                                    'setting_wechat_notice_del' => [
                                        'name' => '删除',
                                        'url' => 'setting.wechat-notice.del',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'item' => 'setting_wechat_notice_del',
                                        'parents' => ['system', 'setting_wechat_notice'],
                                    ],

                                    'setting_wechat_notice_add' => [
                                        'name' => '添加模版',
                                        'url' => 'setting.wechat-notice.addTmp',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'item' => 'setting_wechat_notice_add',
                                        'parents' => ['system', 'setting_wechat_notice'],
                                    ],
                                ]
                            ],
                            'setting_diy_temp' => [
                                'name' => '自定义模板管理',
                                'url' => 'setting.diy-temp.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bars',
                                'sort' => '6',
                                'item' => 'setting_diy_temp',
                                'parents' => ['system', 'setting_wechat_notice'],
                                'child' => [

                                    'setting_diy_temp_choose' => [
                                        'name' => '选择模版（白名单）',
                                        'url' => 'setting.wechat-notice.returnJson',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'item' => 'setting_diy_temp_choose',
                                        'parents' => ['system', 'setting_diy_temp'],
                                    ],


                                    'setting_diy_temp_choose2' => [
                                        'name' => '选择模版（白名单）',
                                        'url' => 'setting.diy-temp.tpl',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'item' => 'setting_diy_temp_choose2',
                                        'parents' => ['system', 'setting_diy_temp'],
                                    ],

                                    'setting_diy_temp_add' => [
                                        'name' => '添加模版',
                                        'url' => 'setting.diy-temp.add',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'item' => 'setting_diy_temp_add',
                                        'parents' => ['system', 'setting_diy_temp', 'setting_wechat_notice'],
                                    ],
                                    'setting_diy_temp_edit' => [
                                        'name' => '修改模版',
                                        'url' => 'setting.diy-temp.edit',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'item' => 'setting_diy_temp_edit',
                                        'parents' => ['system', 'setting_diy_temp', 'setting_wechat_notice'],
                                    ],
                                    'setting_diy_temp_delete' => [
                                        'name' => '删除模版',
                                        'url' => 'setting.diy-temp.del',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'item' => 'setting_diy_temp_delete',
                                        'parents' => ['system', 'setting_diy_temp'],
                                    ],
                                ]
                            ],
                            'setting_small_program' => [
                                'name' => '小程序消息模板',
                                'url' => 'setting.small-program.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-bars',
                                'sort' => '6',
                                'item' => 'setting_small_program',
                                'parents' => ['system', 'setting_wechat_notice'],
                                'child' => [
                                    'setting_small_program_choose' => [
                                        'name' => '选择模版（白名单）',
                                        'url' => 'setting.wechat-notice.returnJson',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'item' => 'setting_small_program_choose',
                                        'parents' => ['system', 'setting_small_program'],
                                    ],
                                    'setting_small_program_synchronization' => [
                                        'name' => '小程序消息模板同步',
                                        'url' => 'setting.small-program.synchronization',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'item' => 'setting_small_program_choose',
                                        'parents' => ['system', 'setting_small_program'],
                                    ],


                                    'setting_small_program_choose2' => [
                                        'name' => '选择模版（白名单）',
                                        'url' => 'setting.small-program.tpl',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'item' => 'setting_small_program_choose2',
                                        'parents' => ['system', 'setting_small_program'],
                                    ],

                                    'setting_small_program_index' => [
                                        'name' => '浏览列表',
                                        'url' => 'setting.small-program.index',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'item' => 'setting_small_program_index',
                                        'parents' => ['system', 'setting_small_program'],
                                    ],
                                    'setting_small_program_see' => [
                                        'name' => '小程序通知模板详情',
                                        'url' => 'setting.small-program.see',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'item' => 'setting_small_program_index',
                                        'parents' => ['system', 'setting_small_program', 'setting_wechat_notice'],
                                    ],
//                    'setting_small_program_add'  => [
//                        'name'              => '添加模版',
//                        'url'               => 'setting.small-program.add',
//                        'url_params'        => '',
//                        'permit'            => 1,
//                        'menu'              => 0,
//                        'item'              => 'setting_small_program_add',
//                        'parents'           => ['system','setting_small_program'],
//                    ],
                                    'setting_small_program_get_template_key' => [
                                        'name' => '选择模板',
                                        'url' => 'setting.small-program.get-template-key',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'item' => 'setting_small_program_get_template_key',
                                        'parents' => ['system', 'setting_small_program'],
                                    ],
                                    'setting_small_program_edit' => [
                                        'name' => '修改模版',
                                        'url' => 'setting.small-program.edit',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'item' => 'setting_small_program_edit',
                                        'parents' => ['system', 'setting_small_program'],
                                    ],
                                    'setting_small_program_delete' => [
                                        'name' => '删除模版',
                                        'url' => 'setting.small-program.del',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'item' => 'setting_diy_temp_delete',
                                        'parents' => ['system', 'setting_small_program'],
                                    ],
                                ]
                            ],
                            'setting_small_program_notice' => [
                                'name' => '小程序消息设置',
                                'url' => 'setting.small-program.notice',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'item' => 'setting_small_program_notice',
                                'parents' => ['system', 'setting_wechat_notice'],
                            ],

                        ],


                    ],


                    'setting_shop_lang' => [
                        'name' => '语言设置',
                        'url' => 'setting.lang.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-language',
                        'sort' => '6',
                        'item' => 'setting_shop_lang',
                        'parents' => ['system'],
                    ],

                    'permission' => [
                        'name' => '权限管理',
                        'url' => 'user.role.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-gamepad',
                        'sort' => '6',
                        'item' => 'permission',
                        'parents' => ['system'],
                        'child' => [
                            'role' => [
                                'name' => '角色管理',
                                'url' => 'user.role.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-gamepad',
                                'sort' => 0,
                                'item' => 'role',
                                'parents' => ['system', 'permission'],
                                'child' => [

                                    'role_store' => [
                                        'name' => '添加角色',
                                        'url' => 'user.role.store',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'role_store',
                                        'parents' => ['system', 'role', 'permission'],
                                    ],

                                    'role_update' => [
                                        'name' => '修改角色',
                                        'url' => 'user.role.update',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'role_update',
                                        'parents' => ['system', 'role', 'permission'],
                                    ],

                                    'role_destroy' => [
                                        'name' => '删除角色',
                                        'url' => 'user.role.destory',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'role_destroy',
                                        'parents' => ['system', 'role', 'permission'],
                                    ],
                                    'role_switch' => [
                                        'name' => '禁用开启角色',
                                        'url' => 'user.role.switchRole',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'role_switch',
                                        'parents' => ['system', 'role', 'permission'],
                                    ],
                                    'get_role' => [
                                        'name' => '获取角色包含的权限(白名单)',
                                        'url' => 'role.permission.index',
                                        'url_params' => '',
                                        'permit' => 0,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'get_role',
                                        'parents' => ['system', 'role', 'permission'],
                                    ],
                                ],
                            ],
                            'user' => [
                                'name' => '操作员',
                                'url' => 'user.user.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-list-ul',
                                'sort' => 0,
                                'item' => 'user',
                                'parents' => ['system', 'permission'],
                                'child' => [

                                    'user_see' => [
                                        'name' => '浏览操作员',
                                        'url' => 'user.user.index1',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'user_see',
                                        'parents' => ['system', 'user', 'permission'],
                                    ],


                                    'user_store' => [
                                        'name' => '添加操作员',
                                        'url' => 'user.user.store',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'user_store',
                                        'parents' => ['system', 'user', 'permission'],
                                    ],

                                    'user_update' => [
                                        'name' => '修改操作员',
                                        'url' => 'user.user.update',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => '',
                                        'sort' => 0,
                                        'item' => 'user_update',
                                        'parents' => ['system', 'user', 'permission'],
                                    ],

                                    'user_destroy' => [
                                        'name' => '删除操作员',
                                        'url' => 'user.user.destroy',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-remove',
                                        'sort' => 0,
                                        'item' => 'user_destroy',
                                        'parents' => ['system', 'user', 'permission'],
                                    ],
                                    'user_switch' => [
                                        'name' => '禁用开启操作员',
                                        'url' => 'user.user.switchUser',
                                        'url_params' => '',
                                        'permit' => 1,
                                        'menu' => 0,
                                        'icon' => 'fa-remove',
                                        'sort' => 0,
                                        'item' => 'user_switch',
                                        'parents' => ['system', 'user', 'permission'],
                                    ],
                                    'user_reset_password' => [
                                        'name'       => '修改用户密码',
                                        'url'        => 'user.user.resetPassword',
                                        'url_params' => '',
                                        'permit'     => 0,
                                        'menu'       => 0,
                                        'icon'       => 'fa-remove',
                                        'sort'       => 0,
                                        'item'       => 'user_switch',
                                        'parents'    => ['system', 'user', 'permission'],
                                    ],
                                    'user_get_admin_user_info' => [
                                        'name'       => '获取当前用户信息',
                                        'url'        => 'user.user.getAdminUserInfo',
                                        'url_params' => '',
                                        'permit'     => 0,
                                        'menu'       => 0,
                                        'icon'       => 'fa-remove',
                                        'sort'       => 0,
                                        'item'       => 'user_switch',
                                        'parents'    => ['system', 'user', 'permission'],
                                    ],
                                ],
                            ],
                            'operation_log' => [
                                'name' => '操作日志',
                                'url' => 'setting.operation-log.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-list-ul',
                                'sort' => '6',
                                'item' => 'operation_log',
                                'parents' => ['system', 'permission'],
                            ],
                            'admin_login_log' => [
                                'name' => '登录日志',
                                'url' => 'user.admin-log.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 1,
                                'icon' => 'fa-list-ul',
                                'sort' => '6',
                                'item' => 'admin_login_log',
                                'parents' => ['system', 'permission'],
                            ],
                        ],
                    ],
                    'media' => [
                        'name' => '多媒体管理',
                        'url' => 'setting.media.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-list-ul',
                        'sort' => '5',
                        'item' => 'media',
                        'parents' => ['system'],
                        'child' => [
                            'media_index' => [
                                'name' => '多媒体管理页面',
                                'url' => 'setting.media.index',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => '6',
                                'item' => 'media-index',
                                'parents' => ['system', 'media'],
                            ], 'media_tags' => [
                                'name' => '资源标签列表',
                                'url' => 'setting.media.tags',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => '6',
                                'item' => 'media-tags',
                                'parents' => ['system', 'media'],
                            ], 'media_source' => [
                                'name' => '资源列表',
                                'url' => 'setting.media.source',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => '6',
                                'item' => 'media-source',
                                'parents' => ['system', 'media'],
                            ], 'media_addTag' => [
                                'name' => '添加标签',
                                'url' => 'setting.media.addTag',
                                'url_params' => '',
                                'permit' => 0,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => '6',
                                'item' => 'media-addTag',
                                'parents' => ['system', 'media'],
                            ], 'media_batchDelete' => [
                                'name' => '批量删除',
                                'url' => 'setting.media.batchDelete',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => '6',
                                'item' => 'media-batchDelete',
                                'parents' => ['system', 'media'],
                            ], 'media_batchMove' => [
                                'name' => '批量移动',
                                'url' => 'setting.media.batchMove',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => '6',
                                'item' => 'media-batchMove',
                                'parents' => ['system', 'media'],
                            ], 'media_rename' => [
                                'name' => '资源重命名',
                                'url' => 'setting.media.rename',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => '6',
                                'item' => 'media-rename',
                                'parents' => ['system', 'media'],
                            ], 'media_renameTag' => [
                                'name' => '标签重命名',
                                'url' => 'setting.media.renameTag',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => '6',
                                'item' => 'media-renameTag',
                                'parents' => ['system', 'media'],
                            ], 'media_deleteTag' => [
                                'name' => '删除标签',
                                'url' => 'setting.media.deleteTag',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => '6',
                                'item' => 'media-deleteTag',
                                'parents' => ['system', 'media'],
                            ],
                        ]
                    ],

                ],
            ],
            'system_msg' => [
                'name' => '消息',
                'url' => 'sysMsg.system-msg.index',
                'url_params' => '',
                'permit' => 1,
                'menu' => 1,
                'icon' => 'fa-comment',
                'sort' => 1000,
                'top_show' => 0,               //顶部导航是否显示
                'left_first_show' => 1,               //左侧导航是否显示
                'left_second_show' => 0,
                'item' => 'system_msg',
                'parents' => [],
                'child' => [
                    'getTpl' => [
                        'name' => '初始化模版(必选)',
                        'url' => 'sysMsg.system-msg.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'item' => 'getTpl',
                        'parents' => ['system_msg'],
                    ],
                    'allMsg' => [
                        'name' => '全部消息',
                        'url' => 'sysMsg.system-msg.all-message',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => '',
                        'sort' => 1,
                        'item' => 'allMsg',
                        'parents' => ['system_msg'],
                    ],
                    'sysMsg' => [
                        'name' => '系统通知',
                        'url' => 'sysMsg.system-msg.sys-message',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => '',
                        'sort' => 2,
                        'item' => 'sysMsg',
                        'parents' => ['system_msg'],
                    ],
                    'sysMsgDetail' => [
                        'name' => '紧急通知详情',
                        'url' => 'sysMsg.system-msg.read-system-message',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 2,
                        'item' => 'sysMsgDetail',
                        'parents' => ['system_msg'],
                    ],
                    'orderMsg' => [
                        'name' => '订单通知',
                        'url' => 'sysMsg.system-msg.order-message',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => '',
                        'sort' => 3,
                        'item' => 'orderMsg',
                        'parents' => ['system_msg'],
                    ],
                    'withdrawalMsg' => [
                        'name' => '提现通知',
                        'url' => 'sysMsg.system-msg.withdrawal-message',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => '',
                        'sort' => 4,
                        'item' => 'withdrawalMsg',
                        'parents' => ['system_msg'],
                    ],
                    'applyMsg' => [
                        'name' => '申请通知',
                        'url' => 'sysMsg.system-msg.apply-message',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => '',
                        'sort' => 5,
                        'item' => 'applyMsg',
                        'parents' => ['system_msg'],
                    ],
                    'stockMsg' => [
                        'name' => '商品库存',
                        'url' => 'sysMsg.system-msg.stock-message',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => '',
                        'sort' => 6,
                        'item' => 'stockMsg',
                        'parents' => ['system_msg'],
                    ],
                    'couponMsg' => [
                        'name' => '优惠券',
                        'url' => 'sysMsg.system-msg.coupon-message',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => '',
                        'sort' => 7,
                        'item' => 'couponMsg',
                        'parents' => ['system_msg'],
                    ],
                    'refundMsg' => [
                        'name' => '退款通知',
                        'url' => 'sysMsg.system-msg.refund-message',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => '',
                        'sort' => 8,
                        'item' => 'refundMsg',
                        'parents' => ['system_msg'],
                    ],
                    'sysMsgRead' => [
                        'name' => '查看详情/已读',
                        'url' => 'sysMsg.system-msg.read-log',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => 2,
                        'item' => 'sysMsgRead',
                        'parents' => ['system_msg'],
                    ],
                ]
            ],
        ];
    }

    private function founderMenu()
    {
        $is_platfrom = config('app.APP_Framework', false) == 'platform' ? 1 : 0;
        return [
            'founder_plugins' => [
                'name' => '插件管理',
                'url' => 'plugins.get-plugin-data',
                'url_params' => '',
                'permit' => 1,
                'menu' => 1,
                'icon' => 'fa-puzzle-piece',
                'sort' => '0',
                'item' => 'plugins',
                'parents' => ['system'],
                'child' => [
                    'plugins_enable' => [
                        'name' => '启用插件',
                        'url' => 'plugins.enable',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => '1',
                        'item' => 'plugins_enable',
                        'parents' => ['system', 'plugins'],
                    ],

                    'plugins_disable' => [
                        'name' => '禁用插件',
                        'url' => 'plugins.disable',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => '2',
                        'item' => 'plugins_disable',
                        'parents' => ['system', 'plugins'],
                    ],

                    'plugins_manage' => [
                        'name' => '插件安装',
                        'url' => 'plugins.manage',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => '3',
                        'item' => 'plugins_manage',
                        'parents' => ['system', 'plugins'],
                    ],

                    'plugins_delete' => [
                        'name' => '插件卸载',
                        'url' => 'plugins.delete',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => '4',
                        'item' => 'plugins_delete',
                        'parents' => ['system', 'plugins'],
                    ],

                    'plugins_update' => [
                        'name' => '插件升级',
                        'url' => 'plugins.update',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => '5',
                        'item' => 'plugins_update',
                        'parents' => ['system', 'plugins'],
                    ],
                ],
            ],

            'site_setting' => [
                'name' => '系统工具',
                'url' => 'supervisord.supervisord.index',
                'url_params' => '',
                'permit' => 1,
                'menu' => 1,
                'icon' => 'fa-cog',
                'sort' => '5',
                'item' => 'site_setting_index',
                'parents' => ['system'],
                'child' => [
                    'supervisord_supervisord_index' => [
                        'name' => '终端管理',
                        'url' => 'supervisord.supervisord.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => '',
                        'sort' => '2',
                        'item' => 'supervisord_supervisord_index',
                        'parents' => ['system', 'site_setting', 'system_site_setting_index_index'],
                        'child' => []
                    ],
                    'supervisord_supervisord_store' => [
                        'name' => '服务器Ip设置',
                        'url' => 'supervisord.supervisord.store',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => '',
                        'sort' => '2',
                        'item' => 'supervisord_supervisord_store',
                        'parents' => ['system', 'site_setting', 'system_site_setting_index_index'],
                        'child' => []
                    ],
                    'site_setting.index' => [
                        'name' => 'HTTPS设置',
                        'url' => 'siteSetting.index.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => '',
                        'sort' => '1',
                        'item' => 'site_setting_index_index',
                        'parents' => ['system', 'site_setting', 'system_site_setting_index_index'],
                        'child' => []
                    ],
                    'site_setting.queue' => [
                        'name' => '队列设置',
                        'url' => 'siteSetting.index.queue',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bell-o',
                        'sort' => '7',
                        'item' => 'setting_shop_queue',
                        'parents' => ['system', 'site_setting', 'system_site_setting_queue_index'],
                    ],
                    'site_setting.physics_path' => [
                        'name' => '物理路径修改',
                        'url' => 'siteSetting.index.physics-path',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bell-o',
                        'sort' => '7',
                        'item' => 'setting_shop_physics_path',
                        'parents' => ['system', 'site_setting', 'system_site_setting_physics_path_index'],
                    ],
                    'site_setting.redis_config' => [
                        'name' => 'Redis设置',
                        'url' => 'siteSetting.index.redis-config',
                        'url_params' => '',
                        'permit' => 1,
//                        'menu'       => $is_platfrom,
                        'menu' => 1,
                        'icon' => '',
                        'sort' => '1',
                        'item' => 'setting_shop_redis_config',
                        'parents' => ['system', 'site_setting'],
                    ],
                    'site_setting.mongoDB_config' => [
                        'name' => 'MongoDB设置',
                        'url' => 'siteSetting.index.mongoDB-config',
                        'url_params' => '',
                        'permit' => 1,
//                        'menu'       => $is_platfrom,
                        'menu' => 1,
                        'icon' => '',
                        'sort' => '1',
                        'item' => 'setting_shop_mongoDB_config',
                        'parents' => ['system', 'site_setting'],
                    ],
                    'site_setting.store' => [
                        'name' => 'http设置',
                        'url' => 'site_setting.store.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => '',
                        'sort' => '2',
                        'item' => 'site_setting_store_index',
                        'parents' => ['system', 'site_setting', 'system_site_setting_store_index'],
                        'child' => []
                    ],
                    'cache_setting' => [
                        'name' => '缓存设置',
                        'url' => 'setting.cache.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-cog',
                        'sort' => '5',
                        'item' => 'setting_cache_index',
                        'parents' => ['system', 'site_setting', 'system_site_setting_store_index'],
                    ],
                    'setting_shop_log' => [
                        'name' => '失败队列重试',
                        'url' => 'setting.cron_log.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bell-o',
                        'sort' => '6',
                        'item' => 'setting_shop_lang',
                        'parents' => ['system', 'site_setting'],
                    ],
                    'sql_manage' => [
                        'name' => '数据库管理',
                        'url' => 'setting.sql-manage.index',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-bell-o',
                        'sort' => '',
                        'item' => '',
                        'parents' => ['system', 'site_setting'],
                        'child' => [
                            'sql_manage_download' => [
                                'name' => '数据库下载安装',
                                'url' => 'setting.sql-manage.download',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => '',
                                'item' => 'sql_manage',
                                'parents' => ['system', 'site_setting'],
                            ],
                            'sql_manage_uninstall' => [
                                'name' => '数据库卸载',
                                'url' => 'setting.sql-manage.uninstall',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => '',
                                'item' => 'sql_manage',
                                'parents' => ['system', 'site_setting'],
                            ]
                        ]
                    ],
                    'trojan' => [
                        'name' => '检查木马文件',
                        'url' => 'setting.trojan.check',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 1,
                        'icon' => 'fa-cog',
                        'sort' => '5',
                        'item' => 'trojan_check',
                        'parents' => ['system', 'site_setting'],
                        'child' => [
                            'work_order_store_page' => [
                                'name' => '删除木马文件',
                                'url' => 'setting.trojan.del',
                                'url_params' => '',
                                'permit' => 1,
                                'menu' => 0,
                                'icon' => 'fa-list-ul',
                                'sort' => '6',
                                'item' => 'trojan_del',
                                'parents' => ['system', 'site_setting'],
                            ]
                        ]
                    ],


                ]
            ],
            'work_order' => [
                'name' => '工单管理',
                'url' => 'setting.work-order.index',
                'url_params' => '',
                'permit' => 1,
                'menu' => 1,
                'icon' => 'fa-list-ul',
                'sort' => '5',
                'item' => 'log-viewer',
                'parents' => ['system'],
                'child' => [
                    'work_order_store_page' => [
                        'name' => '工单提交页面',
                        'url' => 'setting.work-order.store-page',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => 'fa-list-ul',
                        'sort' => '6',
                        'item' => 'operation_log',
                        'parents' => ['system'],
                    ],

                    'work_order_details' => [
                        'name' => '工单详情页面',
                        'url' => 'setting.work-order.details',
                        'url_params' => '',
                        'permit' => 1,
                        'menu' => 0,
                        'icon' => 'fa-list-ul',
                        'sort' => '6',
                        'item' => 'operation_log',
                        'parents' => ['system'],
                    ],
                ]
            ],
            'setting_shop_update' => [
                'name' => '系统更新',
                'url' => 'update.index',
                'url_params' => '',
                'permit' => 1,
                'menu' => 1,
                'icon' => 'fa-paper-plane',
                'sort' => '6',
                'item' => 'setting_shop_update',
                'parents' => ['system'],

            ],

//    'log_viewer'      => [
//        'name'              => '系统日志',
//        'url'               => 'developer.log-viewer',
//        'url_params'        => '',
//        'permit'            => 1,
//        'menu'              => 1,
//        'icon'              => 'fa-history',
//        'sort'              => '5',
//        'item'              => 'log-viewer',
//        'parents'           => ['system'],
//    ],
            /*    'shop_upgrade'      => [
                    'name'              => '系统升级',
                    'url'               => 'update.index',
                    'url_params'        => '',
                    'permit'            => 1,
                    'menu'              => 1,
                    'icon'              => 'fa-history',
                    'sort'              => '5',
                    'item'              => 'shop_upgrade',
                    'parents'           => ['system'],
                ]*/
        ];

    }

    public function clearMainMenu()
    {
        $this->mainMenu();
        $this->mainMenu = [];
    }

    public function setMainMenu($key, $value)
    {
        $mainMenu = $this->mainMenu();
        array_set($mainMenu, $key, $value);
        $this->mainMenu = $mainMenu;
        return $mainMenu;
    }

    private function _getItems()
    {
        $menuListCacheKey = "menuList_" . \YunShop::app()->uid;

        if (!$this->items) {
            if (!Cache::has($menuListCacheKey)) {
                $pluginMenu = $this->getPluginMenus() ?: [];

                //菜单生成
                $menuList = array_merge($this->mainMenu(), $pluginMenu);

                if (PermissionService::isFounder()) {
                    //创始人私有菜单
                    $menuList['system']['child'] = array_merge($menuList['system']['child'], $this->founderMenu());
                    $install_plugins = [
                        'install_plugins' => [
                            'name' => '安装应用',
                            'url' => 'plugins.jump',
                            'urlParams' => '',
                            'permit' => 1,
                            'menu' => 1,
                            'icon' => 'fa-puzzle-piece',
                            'sort' => 2,
                            'top_show' => 0,
                            'left_first_show' => 1,
                            'left_second_show' => 0,
                            'parents' => [],
                            'item' => 'install_plugins',
                            'child' => [
                            ],
                        ],
                    ];
                    $menuList = array_merge($menuList,$install_plugins);
                }
                $this->items = $menuList;
                /**
                 * 这里可能会递归调用
                 */
                $this->items = static::validateMenuPermit($this->items);
                $this->unSetRepeat($this->items);//只保留一个相同的商城功能
                Cache::put($menuListCacheKey, $this->items, 3600);
            }
            $this->items = Cache::get($menuListCacheKey);
        }


        return $this->items;
    }

    private function _getCurrentItems()
    {
        $item = \app\common\models\Menu::getCurrentItemByRoute(request()->input('route'), $this->getItems());

        $result = array_merge(\app\common\models\Menu::getCurrentMenuParents($item, $this->getItems()), [$item]);


        // //检测权限
        // if (!PermissionService::can($item)) {
        //     $exception = new ShopException('Sorry,您没有操作无权限，请联系管理员!');
        //     $exception->setRedirect(yzWebUrl('index.index'));
        //     throw $exception;
        // }


        return $result;
    }

    public function getCurrentItems()
    {
        if (!isset($this->currentItems)) {
            $this->currentItems = $this->_getCurrentItems();
        }
        return $this->currentItems;
    }

    public function getItems()
    {
        if (!isset($this->items)) {
            $this->items = $this->_getItems();
        }
        return $this->items;
    }

    private function unSetRepeat(&$items)
    {
        //开启了插件的统计去除商城的原统计
        if (app('plugins')->isEnabled('shop-statistics')) {
            unset($items['charts']);
        }
    }
}
#!/usr/bin/env bash
set -e

svc=mysqld
systemctl status mysqld >/dev/null 2>&1 || svc=mariadb
systemctl start "$svc" || true

PASS='COmlq37xcliqpobe'

# Try to set root password from temporary password if present (MySQL 5.7+)
if [ -f /var/log/mysqld.log ] && grep -q 'temporary password' /var/log/mysqld.log; then
  TMP=$(grep -m1 'temporary password' /var/log/mysqld.log | awk '{print $NF}')
  mysql --connect-expired-password -uroot -p"$TMP" -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '$PASS';" || true
fi

# If root has no password yet, set it
if mysql -uroot -e "SELECT 1;" >/dev/null 2>&1; then
  mysql -uroot -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '$PASS';" || true
fi

# Create DB and grant access for root@127.0.0.1
mysql -uroot -p"$PASS" -e "CREATE DATABASE IF NOT EXISTS yz CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -uroot -p"$PASS" -e "GRANT ALL PRIVILEGES ON yz.* TO 'root'@'127.0.0.1' IDENTIFIED BY '$PASS'; FLUSH PRIVILEGES;"

# Import backup
mysql -uroot -p"$PASS" yz < /root/database_backup.sql

echo "DB_IMPORT_OK"


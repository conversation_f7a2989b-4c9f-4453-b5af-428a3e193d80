<?php

namespace Php<PERSON>ars<PERSON>\Parser;

use <PERSON>p<PERSON><PERSON><PERSON>\Error;
use Php<PERSON><PERSON><PERSON>\Node;
use Php<PERSON>arser\Node\Expr;
use PhpParser\Node\Name;
use PhpParser\Node\Scalar;
use Php<PERSON>arser\Node\Stmt;

/* This is an automatically GENERATED file, which should not be manually edited.
 * Instead edit one of the following:
 *  * the grammar files grammar/php5.y or grammar/php7.y
 *  * the skeleton file grammar/parser.template
 *  * the preprocessing script grammar/rebuildParsers.php
 */
class Php7 extends \PhpParser\ParserAbstract
{
    protected $tokenToSymbolMapSize = 396;
    protected $actionTableSize = 1196;
    protected $gotoTableSize = 545;

    protected $invalidSymbol = 168;
    protected $errorSymbol = 1;
    protected $defaultAction = -32766;
    protected $unexpectedTokenRule = 32767;

    protected $YY2TBLSTATE = 420;
    protected $numNonLeafStates = 710;

    protected $symbolToName = array(
        "EOF",
        "error",
        "T_THROW",
        "T_INCLUDE",
        "T_INCLUDE_ONCE",
        "T_EVAL",
        "T_REQUIRE",
        "T_REQUIRE_ONCE",
        "','",
        "T_LOGICAL_OR",
        "T_LOGICAL_XOR",
        "T_LOGICAL_AND",
        "T_PRINT",
        "T_YIELD",
        "T_DOUBLE_ARROW",
        "T_YIELD_FROM",
        "'='",
        "T_PLUS_EQUAL",
        "T_MINUS_EQUAL",
        "T_MUL_EQUAL",
        "T_DIV_EQUAL",
        "T_CONCAT_EQUAL",
        "T_MOD_EQUAL",
        "T_AND_EQUAL",
        "T_OR_EQUAL",
        "T_XOR_EQUAL",
        "T_SL_EQUAL",
        "T_SR_EQUAL",
        "T_POW_EQUAL",
        "T_COALESCE_EQUAL",
        "'?'",
        "':'",
        "T_COALESCE",
        "T_BOOLEAN_OR",
        "T_BOOLEAN_AND",
        "'|'",
        "'^'",
        "T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG",
        "T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG",
        "T_IS_EQUAL",
        "T_IS_NOT_EQUAL",
        "T_IS_IDENTICAL",
        "T_IS_NOT_IDENTICAL",
        "T_SPACESHIP",
        "'<'",
        "T_IS_SMALLER_OR_EQUAL",
        "'>'",
        "T_IS_GREATER_OR_EQUAL",
        "T_SL",
        "T_SR",
        "'+'",
        "'-'",
        "'.'",
        "'*'",
        "'/'",
        "'%'",
        "'!'",
        "T_INSTANCEOF",
        "'~'",
        "T_INC",
        "T_DEC",
        "T_INT_CAST",
        "T_DOUBLE_CAST",
        "T_STRING_CAST",
        "T_ARRAY_CAST",
        "T_OBJECT_CAST",
        "T_BOOL_CAST",
        "T_UNSET_CAST",
        "'@'",
        "T_POW",
        "'['",
        "T_NEW",
        "T_CLONE",
        "T_EXIT",
        "T_IF",
        "T_ELSEIF",
        "T_ELSE",
        "T_ENDIF",
        "T_LNUMBER",
        "T_DNUMBER",
        "T_STRING",
        "T_STRING_VARNAME",
        "T_VARIABLE",
        "T_NUM_STRING",
        "T_INLINE_HTML",
        "T_ENCAPSED_AND_WHITESPACE",
        "T_CONSTANT_ENCAPSED_STRING",
        "T_ECHO",
        "T_DO",
        "T_WHILE",
        "T_ENDWHILE",
        "T_FOR",
        "T_ENDFOR",
        "T_FOREACH",
        "T_ENDFOREACH",
        "T_DECLARE",
        "T_ENDDECLARE",
        "T_AS",
        "T_SWITCH",
        "T_MATCH",
        "T_ENDSWITCH",
        "T_CASE",
        "T_DEFAULT",
        "T_BREAK",
        "T_CONTINUE",
        "T_GOTO",
        "T_FUNCTION",
        "T_FN",
        "T_CONST",
        "T_RETURN",
        "T_TRY",
        "T_CATCH",
        "T_FINALLY",
        "T_USE",
        "T_INSTEADOF",
        "T_GLOBAL",
        "T_STATIC",
        "T_ABSTRACT",
        "T_FINAL",
        "T_PRIVATE",
        "T_PROTECTED",
        "T_PUBLIC",
        "T_READONLY",
        "T_VAR",
        "T_UNSET",
        "T_ISSET",
        "T_EMPTY",
        "T_HALT_COMPILER",
        "T_CLASS",
        "T_TRAIT",
        "T_INTERFACE",
        "T_ENUM",
        "T_EXTENDS",
        "T_IMPLEMENTS",
        "T_OBJECT_OPERATOR",
        "T_NULLSAFE_OBJECT_OPERATOR",
        "T_LIST",
        "T_ARRAY",
        "T_CALLABLE",
        "T_CLASS_C",
        "T_TRAIT_C",
        "T_METHOD_C",
        "T_FUNC_C",
        "T_LINE",
        "T_FILE",
        "T_START_HEREDOC",
        "T_END_HEREDOC",
        "T_DOLLAR_OPEN_CURLY_BRACES",
        "T_CURLY_OPEN",
        "T_PAAMAYIM_NEKUDOTAYIM",
        "T_NAMESPACE",
        "T_NS_C",
        "T_DIR",
        "T_NS_SEPARATOR",
        "T_ELLIPSIS",
        "T_NAME_FULLY_QUALIFIED",
        "T_NAME_QUALIFIED",
        "T_NAME_RELATIVE",
        "T_ATTRIBUTE",
        "';'",
        "']'",
        "'{'",
        "'}'",
        "'('",
        "')'",
        "'`'",
        "'\"'",
        "'$'"
    );

    protected $tokenToSymbol = array(
            0,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,   56,  166,  168,  167,   55,  168,  168,
          163,  164,   53,   50,    8,   51,   52,   54,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,   31,  159,
           44,   16,   46,   30,   68,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,   70,  168,  160,   36,  168,  165,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  161,   35,  162,   58,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,    1,    2,    3,    4,
            5,    6,    7,    9,   10,   11,   12,   13,   14,   15,
           17,   18,   19,   20,   21,   22,   23,   24,   25,   26,
           27,   28,   29,   32,   33,   34,   37,   38,   39,   40,
           41,   42,   43,   45,   47,   48,   49,   57,   59,   60,
           61,   62,   63,   64,   65,   66,   67,   69,   71,   72,
           73,   74,   75,   76,   77,   78,   79,   80,   81,   82,
           83,   84,   85,   86,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  124,  125,  126,  127,  128,  129,  130,  131,  132,
          133,  134,  135,  136,  137,  138,  139,  140,  141,  142,
          143,  144,  145,  146,  147,  148,  149,  150,  151,  152,
          153,  154,  155,  156,  157,  158
    );

    protected $action = array(
          132,  133,  134,  569,  135,  136,    0,  722,  723,  724,
          137,   37,  834,  911,  835,  469,-32766,-32766,-32766,-32767,
        -32767,-32767,-32767,  101,  102,  103,  104,  105, 1068, 1069,
         1070, 1067, 1066, 1065, 1071,  716,  715,-32766,-32766,-32766,
        -32766,-32766,-32766,-32766,-32766,-32766,-32767,-32767,-32767,-32767,
        -32767,  545,  546,-32766,-32766,  725,-32766,-32766,-32766,  998,
          999,  806,  922,  447,  448,  449,  370,  371,    2,  267,
          138,  396,  729,  730,  731,  732,  414,-32766,  420,-32766,
        -32766,-32766,-32766,-32766,  990,  733,  734,  735,  736,  737,
          738,  739,  740,  741,  742,  743,  763,  570,  764,  765,
          766,  767,  755,  756,  336,  337,  758,  759,  744,  745,
          746,  748,  749,  750,  346,  790,  791,  792,  793,  794,
          795,  751,  752,  571,  572,  784,  775,  773,  774,  787,
          770,  771,  283,  420,  573,  574,  769,  575,  576,  577,
          578,  579,  580,  598, -575,  470,   14,  798,  772,  581,
          582, -575,  139,-32766,-32766,-32766,  132,  133,  134,  569,
          135,  136, 1017,  722,  723,  724,  137,   37, 1060,-32766,
        -32766,-32766, 1303,  696,-32766, 1304,-32766,-32766,-32766,-32766,
        -32766,-32766,-32766, 1068, 1069, 1070, 1067, 1066, 1065, 1071,
        -32766,  716,  715,  372,  371, 1258,-32766,-32766,-32766, -572,
          106,  107,  108,  414,  270,  891, -572,  240, 1193, 1192,
         1194,  725,-32766,-32766,-32766, 1046,  109,-32766,-32766,-32766,
        -32766,  986,  985,  984,  987,  267,  138,  396,  729,  730,
          731,  732,   12,-32766,  420,-32766,-32766,-32766,-32766,  998,
          999,  733,  734,  735,  736,  737,  738,  739,  740,  741,
          742,  743,  763,  570,  764,  765,  766,  767,  755,  756,
          336,  337,  758,  759,  744,  745,  746,  748,  749,  750,
          346,  790,  791,  792,  793,  794,  795,  751,  752,  571,
          572,  784,  775,  773,  774,  787,  770,  771,  881,  321,
          573,  574,  769,  575,  576,  577,  578,  579,  580,-32766,
           82,   83,   84, -575,  772,  581,  582, -575,  148,  747,
          717,  718,  719,  720,  721, 1278,  722,  723,  724,  760,
          761,   36, 1277,   85,   86,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  996,  270,  150,
        -32766,-32766,-32766,  455,  456,   81,   34, -264, -572, 1016,
          109,  320, -572,  893,  725,  682,  803,  128,  998,  999,
          592,-32766, 1044,-32766,-32766,-32766,  809,  151,  726,  727,
          728,  729,  730,  731,  732,  -88, 1198,  796,  278, -526,
          283,-32766,-32766,-32766,  733,  734,  735,  736,  737,  738,
          739,  740,  741,  742,  743,  763,  786,  764,  765,  766,
          767,  755,  756,  757,  785,  758,  759,  744,  745,  746,
          748,  749,  750,  789,  790,  791,  792,  793,  794,  795,
          751,  752,  753,  754,  784,  775,  773,  774,  787,  770,
          771,  144,  804,  762,  768,  769,  776,  777,  779,  778,
          780,  781, -314, -526, -526, -193, -192,  772,  783,  782,
           49,   50,   51,  500,   52,   53,  239,  807, -526,  -86,
           54,   55, -111,   56,  996,  253,-32766, -111,  800, -111,
         -526,  541, -532, -352,  300, -352,  304, -111, -111, -111,
         -111, -111, -111, -111, -111,  998,  999,  998,  999,  153,
        -32766,-32766,-32766, 1191,  807,  126,  306, 1293,   57,   58,
          103,  104,  105, -111,   59, 1218,   60,  246,  247,   61,
           62,   63,   64,   65,   66,   67,   68, -525,   27,  268,
           69,  436,  501, -328,  808,  -86, 1224, 1225,  502, 1189,
          807, 1198, 1230,  293, 1222,   41,   24,  503,   74,  504,
          953,  505,  320,  506,  802,  154,  507,  508,  279,  684,
          280,   43,   44,  437,  367,  366,  891,   45,  509,   35,
          249,  -16, -566,  358,  332,  318, -566, 1198, 1193, 1192,
         1194, -527,  510,  511,  512,  333, -524, 1274,   48,  716,
          715, -525, -525,  334,  513,  514,  807, 1212, 1213, 1214,
         1215, 1209, 1210,  292,  360,  284, -525,  285, -314, 1216,
         1211, -193, -192, 1193, 1192, 1194,  293,  891, -525,  364,
         -531,   70,  807,  316,  317,  320,   31,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
         -153, -153, -153,  638,   25, -527, -527,  687,  379,  881,
         -524, -524,  296,  297,  891, -153,  432, -153,  807, -153,
         -527, -153,  716,  715,  433, -524,  798,  363, -111, 1105,
         1107,  365, -527,  434,  891,  140,  435, -524,  954,  127,
         -524,  320, -111, -111,  688,  813,  381, -529,   11,  834,
          155,  835,  867, -111, -111, -111, -111,   47,  293,-32766,
          881,  654,  655,   74,  689, 1191, 1045,  320,  708,  149,
          399,  157,-32766,-32766,-32766,   32,-32766,  -79,-32766,  123,
        -32766,  716,  715,-32766,  893,  891,  682, -153,-32766,-32766,
        -32766,  716,  715,  891,-32766,-32766,  124,  881,  129,   74,
        -32766,  411,  130,  320, -524, -524,  143,  141,  -75,-32766,
          158, -529, -529,  320,   27,  691,  159,  881,  160, -524,
          161,  294,  295,  698,  368,  369,  807,  -73,-32766,  -72,
         1222, -524,  373,  374, 1191,  893,  -71,  682, -529,   73,
          -70,-32766,-32766,-32766,  -69,-32766,  -68,-32766,  125,-32766,
          630,  631,-32766,  -67,  -66,  -47,  -51,-32766,-32766,-32766,
          -18,  147,  271,-32766,-32766,  277,  697,  700,  881,-32766,
          411,  890,  893,  146,  682,  282,  881,  907,-32766,  281,
          513,  514,  286, 1212, 1213, 1214, 1215, 1209, 1210,  326,
          131,  145,  939,  287,  682, 1216, 1211,  109,  270,-32766,
          798,  807,-32766,  662,  639, 1191,  657,   72,  675, 1075,
          317,  320,-32766,-32766,-32766, 1305,-32766,  301,-32766,  628,
        -32766,  431,  543,-32766,-32766,  923,  555,  924,-32766,-32766,
        -32766, 1229,  549,-32766,-32766,-32766,   -4,  891, -490, 1191,
        -32766,  411,  644,  893,  299,  682,-32766,-32766,-32766,-32766,
        -32766,  893,-32766,  682,-32766,   13, 1231,-32766,  452,  480,
          645,  909,-32766,-32766,-32766,-32766,  658, -480,-32766,-32766,
            0, 1191,    0,    0,-32766,  411,    0,  298,-32766,-32766,
        -32766,  305,-32766,-32766,-32766,    0,-32766,    0,  806,-32766,
            0,    0,    0,  475,-32766,-32766,-32766,-32766,    0,    7,
        -32766,-32766,   16, 1191,  561,  596,-32766,  411, 1219,  891,
        -32766,-32766,-32766,  362,-32766,-32766,-32766,  818,-32766, -267,
          881,-32766,   39,  293,    0,    0,-32766,-32766,-32766,   40,
          705,  706,-32766,-32766,  872,  963,  940,  947,-32766,  411,
          937,  948,  365,  870,  427,  891,  935,-32766, 1049,  291,
         1244, 1052, 1053, -111, -111, 1050, 1051, 1057, -560, 1262,
         1296,  633,    0,  826, -111, -111, -111, -111,   33,  315,
        -32766,  361,  683,  686,  690,  692, 1191,  693,  694,  695,
          699,  685,  320,-32766,-32766,-32766,    9,-32766,  702,-32766,
          868,-32766,  881, 1300,-32766,  893, 1302,  682,   -4,-32766,
        -32766,-32766,  829,  828,  837,-32766,-32766,  916, -242, -242,
         -242,-32766,  411,  955,  365,   27,  836, 1301,  915,  917,
        -32766,  914, 1177,  900,  910, -111, -111,  807,  881,  898,
          945, 1222,  946, 1299, 1256,  867, -111, -111, -111, -111,
         1245, 1263, 1269, 1272, -241, -241, -241, -558, -532, -531,
          365, -530,    1,   28,   29,   38,   42,   46,   71,    0,
           75, -111, -111,   76,   77,   78,   79,  893,   80,  682,
         -242,  867, -111, -111, -111, -111,  142,  152,  156,  245,
          322,  347,  514,  348, 1212, 1213, 1214, 1215, 1209, 1210,
          349,  350,  351,  352,  353,  354, 1216, 1211,  355,  356,
          357,  359,  428,  893, -265,  682, -241, -264,   72,    0,
           18,  317,  320,   19,   20,   21,   23,  398,  471,  472,
          479,  482,  483,  484,  485,  489,  490,  491,  498,  669,
         1202, 1145, 1220, 1019, 1018, 1181, -269, -103,   17,   22,
           26,  290,  397,  589,  593,  620,  674, 1149, 1197, 1146,
         1275,    0, -494, 1162,    0, 1223
    );

    protected $actionCheck = array(
            2,    3,    4,    5,    6,    7,    0,    9,   10,   11,
           12,   13,  106,    1,  108,   31,    9,   10,   11,   44,
           45,   46,   47,   48,   49,   50,   51,   52,  116,  117,
          118,  119,  120,  121,  122,   37,   38,   30,  116,   32,
           33,   34,   35,   36,   37,   38,   39,   40,   41,   42,
           43,  117,  118,    9,   10,   57,    9,   10,   11,  137,
          138,  155,  128,  129,  130,  131,  106,  107,    8,   71,
           72,   73,   74,   75,   76,   77,  116,   30,   80,   32,
           33,   34,   35,   36,    1,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  109,  110,  111,
          112,  113,  114,  115,  116,  117,  118,  119,  120,  121,
          122,  123,  124,  125,  126,  127,  128,  129,  130,  131,
          132,  133,   30,   80,  136,  137,  138,  139,  140,  141,
          142,  143,  144,   51,    1,  161,  101,   80,  150,  151,
          152,    8,  154,    9,   10,   11,    2,    3,    4,    5,
            6,    7,  164,    9,   10,   11,   12,   13,  123,    9,
           10,   11,   80,  161,   30,   83,   32,   33,   34,   35,
           36,   37,   38,  116,  117,  118,  119,  120,  121,  122,
           30,   37,   38,  106,  107,    1,    9,   10,   11,    1,
           53,   54,   55,  116,   57,    1,    8,   14,  155,  156,
          157,   57,    9,   10,   11,  162,   69,   30,  116,   32,
           33,  119,  120,  121,  122,   71,   72,   73,   74,   75,
           76,   77,    8,   30,   80,   32,   33,   34,   35,  137,
          138,   87,   88,   89,   90,   91,   92,   93,   94,   95,
           96,   97,   98,   99,  100,  101,  102,  103,  104,  105,
          106,  107,  108,  109,  110,  111,  112,  113,  114,  115,
          116,  117,  118,  119,  120,  121,  122,  123,  124,  125,
          126,  127,  128,  129,  130,  131,  132,  133,   84,   70,
          136,  137,  138,  139,  140,  141,  142,  143,  144,    9,
            9,   10,   11,  160,  150,  151,  152,  164,  154,    2,
            3,    4,    5,    6,    7,    1,    9,   10,   11,   12,
           13,   30,    8,   32,   33,   34,   35,   36,   37,   38,
           39,   40,   41,   42,   43,   44,   45,   46,   47,   48,
           49,   50,   51,   52,   53,   54,   55,  116,   57,   14,
            9,   10,   11,  134,  135,  161,    8,  164,  160,    1,
           69,  167,  164,  159,   57,  161,   80,    8,  137,  138,
            1,   30,    1,   32,   33,   34,    1,   14,   71,   72,
           73,   74,   75,   76,   77,   31,    1,   80,   30,   70,
           30,    9,   10,   11,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  124,  125,  126,  127,  128,  129,  130,  131,  132,
          133,    8,  156,  136,  137,  138,  139,  140,  141,  142,
          143,  144,    8,  134,  135,    8,    8,  150,  151,  152,
            2,    3,    4,    5,    6,    7,   97,   82,  149,   31,
           12,   13,  101,   15,  116,    8,  116,  106,   80,  108,
          161,   85,  163,  106,  113,  108,    8,  116,  117,  118,
          119,  120,  121,  122,  123,  137,  138,  137,  138,   14,
            9,   10,   11,   80,   82,   14,    8,   85,   50,   51,
           50,   51,   52,  128,   56,    1,   58,   59,   60,   61,
           62,   63,   64,   65,   66,   67,   68,   70,   70,   71,
           72,   73,   74,  162,  159,   97,   78,   79,   80,  116,
           82,    1,  146,  158,   86,   87,   88,   89,  163,   91,
           31,   93,  167,   95,  156,   14,   98,   99,   35,  161,
           37,  103,  104,  105,  106,  107,    1,  109,  110,  147,
          148,   31,  160,  115,  116,    8,  164,    1,  155,  156,
          157,   70,  124,  125,  126,    8,   70,    1,   70,   37,
           38,  134,  135,    8,  136,  137,   82,  139,  140,  141,
          142,  143,  144,  145,    8,   35,  149,   37,  164,  151,
          152,  164,  164,  155,  156,  157,  158,    1,  161,    8,
          163,  163,   82,  165,  166,  167,   16,   17,   18,   19,
           20,   21,   22,   23,   24,   25,   26,   27,   28,   29,
           75,   76,   77,   75,   76,  134,  135,   31,    8,   84,
          134,  135,  134,  135,    1,   90,    8,   92,   82,   94,
          149,   96,   37,   38,    8,  149,   80,  149,  128,   59,
           60,  106,  161,    8,    1,  161,    8,  161,  159,  161,
           70,  167,  117,  118,   31,    8,  106,   70,  108,  106,
           14,  108,  127,  128,  129,  130,  131,   70,  158,   74,
           84,   75,   76,  163,   31,   80,  159,  167,  161,  101,
          102,   14,   87,   88,   89,   14,   91,   31,   93,   16,
           95,   37,   38,   98,  159,    1,  161,  162,  103,  104,
          105,   37,   38,    1,  109,  110,   16,   84,   16,  163,
          115,  116,   16,  167,  134,  135,   16,  161,   31,  124,
           16,  134,  135,  167,   70,   31,   16,   84,   16,  149,
           16,  134,  135,   31,  106,  107,   82,   31,   74,   31,
           86,  161,  106,  107,   80,  159,   31,  161,  161,  154,
           31,   87,   88,   89,   31,   91,   31,   93,  161,   95,
          111,  112,   98,   31,   31,   31,   31,  103,  104,  105,
           31,   31,   31,  109,  110,   31,   31,   31,   84,  115,
          116,   31,  159,   31,  161,   37,   84,   38,  124,   35,
          136,  137,   35,  139,  140,  141,  142,  143,  144,   35,
           31,   70,  159,   37,  161,  151,  152,   69,   57,   74,
           80,   82,   85,   77,   90,   80,   94,  163,   92,   82,
          166,  167,   87,   88,   89,   83,   91,  114,   93,  113,
           95,  128,   85,   98,  116,  128,  153,  128,  103,  104,
          105,  146,   89,   74,  109,  110,    0,    1,  149,   80,
          115,  116,   96,  159,  133,  161,   87,   88,   89,  124,
           91,  159,   93,  161,   95,   97,  146,   98,   97,   97,
          100,  154,  103,  104,  105,   74,  100,  149,  109,  110,
           -1,   80,   -1,   -1,  115,  116,   -1,  132,   87,   88,
           89,  132,   91,  124,   93,   -1,   95,   -1,  155,   98,
           -1,   -1,   -1,  102,  103,  104,  105,   74,   -1,  149,
          109,  110,  149,   80,   81,  153,  115,  116,  160,    1,
           87,   88,   89,  149,   91,  124,   93,  160,   95,  164,
           84,   98,  159,  158,   -1,   -1,  103,  104,  105,  159,
          159,  159,  109,  110,  159,  159,  159,  159,  115,  116,
          159,  159,  106,  159,  108,    1,  159,  124,  159,  113,
          160,  159,  159,  117,  118,  159,  159,  159,  163,  160,
          160,  160,   -1,  127,  128,  129,  130,  131,  161,  161,
           74,  161,  161,  161,  161,  161,   80,  161,  161,  161,
          161,  161,  167,   87,   88,   89,  150,   91,  162,   93,
          162,   95,   84,  162,   98,  159,  162,  161,  162,  103,
          104,  105,  162,  162,  162,  109,  110,  162,  100,  101,
          102,  115,  116,  162,  106,   70,  162,  162,  162,  162,
          124,  162,  162,  162,  162,  117,  118,   82,   84,  162,
          162,   86,  162,  162,  162,  127,  128,  129,  130,  131,
          162,  162,  162,  162,  100,  101,  102,  163,  163,  163,
          106,  163,  163,  163,  163,  163,  163,  163,  163,   -1,
          163,  117,  118,  163,  163,  163,  163,  159,  163,  161,
          162,  127,  128,  129,  130,  131,  163,  163,  163,  163,
          163,  163,  137,  163,  139,  140,  141,  142,  143,  144,
          163,  163,  163,  163,  163,  163,  151,  152,  163,  163,
          163,  163,  163,  159,  164,  161,  162,  164,  163,   -1,
          164,  166,  167,  164,  164,  164,  164,  164,  164,  164,
          164,  164,  164,  164,  164,  164,  164,  164,  164,  164,
          164,  164,  164,  164,  164,  164,  164,  164,  164,  164,
          164,  164,  164,  164,  164,  164,  164,  164,  164,  164,
          164,   -1,  165,  165,   -1,  166
    );

    protected $actionBase = array(
            0,   -2,  154,  565,  876,  948,  984,  514,   53,  398,
          837,  307,  307,   67,  307,  307,  307,  653,  724,  724,
          732,  724,  616,  673,  204,  204,  204,  625,  625,  625,
          625,  694,  694,  831,  831,  863,  799,  765,  936,  936,
          936,  936,  936,  936,  936,  936,  936,  936,  936,  936,
          936,  936,  936,  936,  936,  936,  936,  936,  936,  936,
          936,  936,  936,  936,  936,  936,  936,  936,  936,  936,
          936,  936,  936,  936,  936,  936,  936,  936,  936,  936,
          936,  936,  936,  936,  936,  936,  936,  936,  936,  936,
          936,  936,  936,  936,  936,  936,  936,  936,  936,  936,
          936,  936,  936,  936,  936,  936,  936,  936,  936,  936,
          936,  936,  936,  936,  936,  936,  936,  936,  936,  936,
          936,  936,  936,  936,  936,  936,  936,  936,  936,  936,
          936,  936,  936,  936,  936,  936,  936,  936,  936,  936,
          936,  936,  936,  936,  936,  936,  936,  936,  936,  936,
          936,  936,  936,  936,  936,  936,  936,  936,  936,  936,
          936,  936,  375,  519,  369,  701, 1017, 1023, 1019, 1024,
         1015, 1014, 1018, 1020, 1025,  911,  912,  782,  918,  919,
          920,  921, 1021,  841, 1016, 1022,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  290,  491,   44,  382,  382,  382,  382,  382,
          382,  382,  382,  382,  382,  382,  382,  382,  382,  382,
          382,  382,  382,  382,  382,  160,  160,  160,  187,  684,
          684,  341,  203,  610,   47,  985,  985,  985,  985,  985,
          985,  985,  985,  985,  985,  144,  144,    7,    7,    7,
            7,    7,  371,  -25,  -25,  -25,  -25,  540,  385,  102,
          576,  358,   45,  377,  460,  460,  360,  231,  231,  231,
          231,  231,  231,  -78,  -78,  -78,  -78,  -78,  -66,  319,
          457,  -94,  396,  423,  586,  586,  586,  586,  423,  423,
          423,  423,  750, 1029,  423,  423,  423,  511,  516,  516,
          518,  147,  147,  147,  516,  583,  777,  422,  583,  422,
          194,   92,  748,  -40,   87,  412,  748,  617,  627,  198,
          143,  773,  658,  773, 1013,  757,  764,  717,  838,  860,
         1026,  800,  908,  806,  910,  219,  686, 1012, 1012, 1012,
         1012, 1012, 1012, 1012, 1012, 1012, 1012, 1012,  855,  552,
         1013,  286,  855,  855,  855,  552,  552,  552,  552,  552,
          552,  552,  552,  552,  552,  679,  286,  568,  626,  286,
          794,  552,  375,  758,  375,  375,  375,  375,  958,  375,
          375,  375,  375,  375,  375,  970,  769,  -16,  375,  519,
           12,   12,  547,   83,   12,   12,   12,   12,  375,  375,
          375,  658,  781,  713,  666,  792,  448,  781,  781,  781,
          438,  444,  193,  447,  570,  523,  580,  760,  760,  767,
          929,  929,  760,  759,  760,  767,  934,  760,  929,  805,
          359,  648,  577,  611,  656,  929,  478,  760,  760,  760,
          760,  665,  760,  467,  433,  760,  760,  785,  774,  789,
           60,  929,  929,  929,  789,  596,  751,  751,  751,  811,
          812,  746,  771,  567,  498,  677,  348,  779,  771,  771,
          760,  640,  746,  771,  746,  771,  747,  771,  771,  771,
          746,  771,  759,  585,  771,  734,  668,  224,  771,    6,
          935,  937,  354,  940,  932,  941,  979,  942,  943,  851,
          956,  933,  945,  931,  930,  780,  703,  720,  790,  729,
          928,  768,  768,  768,  925,  768,  768,  768,  768,  768,
          768,  768,  768,  703,  788,  804,  733,  783,  960,  722,
          726,  725,  868, 1027, 1028,  737,  739,  958, 1006,  953,
          803,  730,  992,  967,  866,  848,  968,  969,  993, 1007,
         1008,  871,  761,  874,  880,  797,  971,  852,  768,  935,
          943,  933,  945,  931,  930,  763,  762,  753,  755,  749,
          745,  736,  738,  770, 1009,  924,  835,  830,  970,  926,
          703,  839,  986,  847,  994,  995,  850,  801,  772,  840,
          881,  972,  975,  976,  853, 1010,  810,  989,  795,  996,
          802,  882,  997,  998,  999, 1000,  885,  854,  856,  857,
          815,  754,  980,  786,  891,  335,  787,  796,  978,  363,
          957,  858,  894,  895, 1001, 1002, 1003,  896,  954,  816,
          990,  752,  991,  983,  817,  818,  485,  784,  778,  541,
          676,  897,  899,  900,  955,  775,  766,  821,  822, 1011,
          901,  697,  824,  740,  902, 1005,  742,  744,  756,  859,
          793,  743,  798,  977,  776,  827,  907,  829,  832,  833,
         1004,  836,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,  458,  458,  458,  458,  458,  458,  307,  307,  307,
          307,    0,    0,  307,    0,    0,    0,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  423,
          423,  291,  291,    0,  291,  423,  423,  423,  423,  423,
          423,  423,  423,  423,  423,  291,  291,  291,  291,  291,
          291,  291,  805,  147,  147,  147,  147,  423,  423,  423,
          423,  423,  -88,  -88,  147,  147,  423,  423,  423,  423,
          423,  423,  423,  423,  423,  423,  423,  423,    0,    0,
            0,  286,  422,    0,  759,  759,  759,  759,    0,    0,
            0,    0,  422,  422,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,  286,  422,    0,  286,    0,
          759,  759,  423,  805,  805,  314,  423,    0,    0,    0,
            0,  286,  759,  286,  552,  422,  552,  552,   12,  375,
          314,  608,  608,  608,  608,    0,  658,  805,  805,  805,
          805,  805,  805,  805,  805,  805,  805,  805,  759,    0,
          805,    0,  759,  759,  759,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
          759,    0,    0,  929,    0,    0,    0,    0,  760,    0,
            0,    0,    0,    0,    0,  760,  934,    0,    0,    0,
            0,    0,    0,  759,    0,    0,    0,    0,    0,    0,
            0,    0,  768,  801,    0,  801,    0,  768,  768,  768
    );

    protected $actionDefault = array(
            3,32767,  103,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,  101,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  578,  578,  578,
          578,32767,32767,  246,  103,32767,32767,  454,  372,  372,
          372,32767,32767,  522,  522,  522,  522,  522,  522,32767,
        32767,32767,32767,32767,32767,  454,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  101,32767,
        32767,32767,   37,    7,    8,   10,   11,   50,   17,  310,
        32767,32767,32767,32767,  103,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,  571,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  458,  437,  438,  440,
          441,  371,  523,  577,  313,  574,  370,  146,  325,  315,
          234,  316,  250,  459,  251,  460,  463,  464,  211,  279,
          367,  150,  401,  455,  403,  453,  457,  402,  377,  382,
          383,  384,  385,  386,  387,  388,  389,  390,  391,  392,
          393,  394,  375,  376,  456,  434,  433,  432,  399,32767,
        32767,  400,  404,  374,  407,32767,32767,32767,32767,32767,
        32767,32767,32767,  103,32767,  405,  406,  423,  424,  421,
          422,  425,32767,  426,  427,  428,  429,32767,32767,  302,
        32767,32767,  351,  349,  414,  415,  302,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  516,
          431,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  103,32767,  101,  518,  396,  398,
          486,  409,  410,  408,  378,32767,  493,32767,  103,  495,
        32767,32767,32767,  112,32767,32767,32767,  517,32767,  524,
          524,32767,  479,  101,  194,32767,  194,  194,32767,32767,
        32767,32767,32767,32767,32767,  585,  479,  111,  111,  111,
          111,  111,  111,  111,  111,  111,  111,  111,32767,  194,
          111,32767,32767,32767,  101,  194,  194,  194,  194,  194,
          194,  194,  194,  194,  194,  189,32767,  260,  262,  103,
          539,  194,32767,  498,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  491,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,  479,  419,  139,32767,  139,  524,  411,  412,  413,
          481,  524,  524,  524,  298,  281,32767,32767,32767,32767,
          496,  496,  101,  101,  101,  101,  491,32767,32767,  112,
          100,  100,  100,  100,  100,  104,  102,32767,32767,32767,
        32767,  100,32767,  102,  102,32767,32767,  217,  208,  215,
          102,32767,  543,  544,  215,  102,  219,  219,  219,  239,
          239,  470,  304,  102,  100,  102,  102,  196,  304,  304,
        32767,  102,  470,  304,  470,  304,  198,  304,  304,  304,
          470,  304,32767,  102,  304,  210,  100,  100,  304,32767,
        32767,32767,  481,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  511,32767,  528,
          541,  417,  418,  420,  526,  442,  443,  444,  445,  446,
          447,  448,  450,  573,32767,  485,32767,32767,32767,32767,
          324,  583,32767,  583,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,  584,32767,  524,32767,32767,32767,32767,  416,    9,
           76,   43,   44,   52,   58,  502,  503,  504,  505,  499,
          500,  506,  501,32767,32767,  507,  549,32767,32767,  525,
          576,32767,32767,32767,32767,32767,32767,  139,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  511,32767,
          137,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,  524,32767,32767,32767,  300,  301,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  524,32767,32767,32767,  283,  284,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  278,32767,32767,  366,32767,32767,32767,
        32767,  345,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,  152,  152,    3,    3,  327,  152,  152,  152,
          327,  152,  327,  327,  327,  152,  152,  152,  152,  152,
          152,  272,  184,  254,  257,  239,  239,  152,  337,  152
    );

    protected $goto = array(
          194,  194,  670,  422,  643,  463, 1264, 1265, 1022,  416,
          308,  309,  329,  563,  314,  421,  330,  423,  622,  801,
          678,  637,  586,  651,  652,  653,  165,  165,  165,  165,
          218,  195,  191,  191,  175,  177,  213,  191,  191,  191,
          191,  191,  192,  192,  192,  192,  192,  192,  186,  187,
          188,  189,  190,  215,  213,  216,  521,  522,  412,  523,
          525,  526,  527,  528,  529,  530,  531,  532, 1091,  166,
          167,  168,  193,  169,  170,  171,  164,  172,  173,  174,
          176,  212,  214,  217,  235,  238,  241,  242,  244,  255,
          256,  257,  258,  259,  260,  261,  263,  264,  265,  266,
          274,  275,  311,  312,  313,  417,  418,  419,  568,  219,
          220,  221,  222,  223,  224,  225,  226,  227,  228,  229,
          230,  231,  232,  233,  178,  234,  179,  196,  197,  198,
          236,  186,  187,  188,  189,  190,  215, 1091,  199,  180,
          181,  182,  200,  196,  183,  237,  201,  199,  163,  202,
          203,  184,  204,  205,  206,  185,  207,  208,  209,  210,
          211,  323,  323,  323,  323,  827,  608,  608,  824,  547,
          538,  342, 1221, 1221, 1221, 1221, 1221, 1221, 1221, 1221,
         1221, 1221, 1239, 1239,  288,  288,  288,  288, 1239, 1239,
         1239, 1239, 1239, 1239, 1239, 1239, 1239, 1239,  388,  538,
          547,  556,  557,  395,  566,  588,  602,  603,  832,  825,
          880,  875,  876,  889,   15,  833,  877,  830,  878,  879,
          831,  799,  251,  251,  883,  919,  992, 1000, 1004, 1001,
         1005, 1237, 1237,  938, 1043, 1039, 1040, 1237, 1237, 1237,
         1237, 1237, 1237, 1237, 1237, 1237, 1237,  858,  248,  248,
          248,  248,  250,  252,  533,  533,  533,  533,  487,  590,
          488, 1190, 1190,  997, 1190,  997,  494, 1290, 1290,  560,
          997,  997,  997,  997,  997,  997,  997,  997,  997,  997,
          997,  997, 1261, 1261, 1290, 1261,  340, 1190,  930,  402,
          677, 1279, 1190, 1190, 1190, 1190,  959,  345, 1190, 1190,
         1190, 1271, 1271, 1271, 1271,  606,  640,  345,  345, 1273,
         1273, 1273, 1273,  820,  820,  805,  896,  884,  840,  885,
          897,  345,  345,    5,  345,    6, 1306,  384,  535,  535,
          559,  535,  415,  852,  597, 1257,  839,  540,  524,  524,
          345, 1289, 1289,  642,  524,  524,  524,  524,  524,  524,
          524,  524,  524,  524,  445,  805, 1140,  805, 1289,  932,
          932,  932,  932, 1063, 1064,  445,  926,  933,  386,  390,
          548,  587,  591, 1030, 1292,  331,  554, 1259, 1259, 1030,
          704,  621,  623,  823,  641, 1250,  319,  303,  660,  664,
          973,  668,  676,  969,  429,  553,  962,  936,  936,  934,
          936,  703,  601,  537,  971,  966,  343,  344,  663,  817,
          595,  609,  612,  613,  614,  615,  634,  635,  636,  680,
          439, 1186,  845,  454,  454,  439,  439, 1266, 1267,  820,
          901, 1079,  454,  394,  539,  551, 1183,  605,  540,  539,
          842,  551,  978,  272,  387,  618,  619,  981,  536,  536,
          844,  707,  646,  957,  567,  457,  458,  459,  838,  850,
          254,  254, 1297, 1298,  400,  401,  976,  976,  464,  649,
         1182,  650, 1028,  404,  405,  406, 1187,  661,  424, 1032,
          407,  564,  600,  815,  338,  424,  854,  848,  853,  841,
         1027, 1031, 1009, 1002, 1006, 1003, 1007, 1185,  941, 1188,
         1247, 1248,  943,    0, 1074,  439,  439,  439,  439,  439,
          439,  439,  439,  439,  439,  439,    0,  468,  439,  585,
         1056,  931,  681,  667,  667,    0,  495,  673, 1054, 1171,
          912,    0,    0, 1172, 1175,  913, 1176,    0,    0,    0,
            0,    0,    0, 1072,  857
    );

    protected $gotoCheck = array(
           42,   42,   72,   65,   65,  166,  166,  166,  119,   65,
           65,   65,   65,   65,   65,   65,   65,   65,   65,    7,
            9,   84,  122,   84,   84,   84,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   23,   23,   23,   23,   15,  104,  104,   26,   75,
           75,   93,  104,  104,  104,  104,  104,  104,  104,  104,
          104,  104,  160,  160,   24,   24,   24,   24,  160,  160,
          160,  160,  160,  160,  160,  160,  160,  160,   75,   75,
           75,   75,   75,   75,   75,   75,   75,   75,   15,   27,
           15,   15,   15,   15,   75,   15,   15,   15,   15,   15,
           15,    6,    5,    5,   15,   87,   87,   87,   87,   87,
           87,  161,  161,   49,   15,   15,   15,  161,  161,  161,
          161,  161,  161,  161,  161,  161,  161,   45,    5,    5,
            5,    5,    5,    5,  103,  103,  103,  103,  147,  103,
          147,   72,   72,   72,   72,   72,  147,  173,  173,  162,
           72,   72,   72,   72,   72,   72,   72,   72,   72,   72,
           72,   72,  122,  122,  173,  122,  169,   72,   89,   89,
           89,  171,   72,   72,   72,   72,   99,   14,   72,   72,
           72,    9,    9,    9,    9,   55,   55,   14,   14,  122,
          122,  122,  122,   22,   22,   12,   72,   64,   35,   64,
           72,   14,   14,   46,   14,   46,   14,   61,   19,   19,
          100,   19,   13,   35,   13,  122,   35,   14,  163,  163,
           14,  172,  172,   63,  163,  163,  163,  163,  163,  163,
          163,  163,  163,  163,   19,   12,  143,   12,  172,   19,
           19,   19,   19,  136,  136,   19,   19,   19,   58,   58,
           58,   58,   58,  122,  172,   29,   48,  122,  122,  122,
           48,   48,   48,   25,   48,   14,  159,  159,   48,   48,
           48,   48,   48,   48,  109,    9,   25,   25,   25,   25,
           25,   25,    9,   25,   25,   25,   93,   93,   14,   18,
           79,   79,   79,   79,   79,   79,   79,   79,   79,   79,
           23,   20,   39,  141,  141,   23,   23,  168,  168,   22,
           17,   17,  141,   28,    9,    9,  152,   17,   14,    9,
           37,    9,   17,   24,    9,   83,   83,  106,   24,   24,
           17,   95,   17,   17,    9,    9,    9,    9,   17,    9,
            5,    5,    9,    9,   80,   80,  103,  103,  149,   80,
           17,   80,  121,   80,   80,   80,   20,   80,  113,  124,
           80,    2,    2,   20,   80,  113,   41,    9,   16,   16,
           16,   16,  113,  113,  113,  113,  113,   14,   16,   20,
           20,   20,   92,   -1,  139,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   -1,   82,   23,    8,
            8,   16,    8,    8,    8,   -1,    8,    8,    8,   78,
           78,   -1,   -1,   78,   78,   78,   78,   -1,   -1,   -1,
           -1,   -1,   -1,   16,   16
    );

    protected $gotoBase = array(
            0,    0, -203,    0,    0,  221,  208,   10,  512,    7,
            0,    0,   24,    1,    5, -174,   47,  -23,  105,   61,
           38,    0,  -10,  158,  181,  379,  164,  205,  102,   84,
            0,    0,    0,    0,    0,  -43,    0,  107,    0,  104,
            0,   54,   -1,    0,    0,  235, -384,    0, -307,  210,
            0,    0,    0,    0,    0,  266,    0,    0,  324,    0,
            0,  286,    0,  103,  298, -236,    0,    0,    0,    0,
            0,    0,   -6,    0,    0, -167,    0,    0,  129,   62,
          -14,    0,   53,  -22, -669,    0,    0,  -52,    0,  -11,
            0,    0,   68, -299,    0,   52,    0,    0,    0,  262,
          288,    0,    0,  227,  -73,    0,   87,    0,    0,  118,
            0,    0,    0,  209,    0,    0,    0,    0,    0,    6,
            0,  108,   15,    0,   46,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,   91,    0,    0,   69,
            0,  390,    0,   86,    0,    0,    0, -224,    0,   37,
            0,    0,   77,    0,    0,    0,    0,    0,    0,   70,
          -57,   -8,  241,   99,    0,    0, -290,    0,   65,  257,
            0,  261,   39,  -35,    0,    0
    );

    protected $gotoDefault = array(
        -32768,  499,  711,    4,  712,  905,  788,  797,  583,  515,
          679,  339,  610,  413, 1255,  882, 1078,  565,  816, 1199,
         1207,  446,  819,  324,  701,  864,  865,  866,  391,  376,
          382,  389,  632,  611,  481,  851,  442,  843,  473,  846,
          441,  855,  162,  410,  497,  859,    3,  861,  542,  892,
          377,  869,  378,  656,  871,  550,  873,  874,  385,  392,
          393, 1083,  558,  607,  886,  243,  552,  887,  375,  888,
          895,  380,  383,  665,  453,  492,  486,  403, 1058,  594,
          629,  450,  467,  617,  616,  604,  466,  425,  408,  928,
          474,  451,  942,  341,  950,  709, 1090,  624,  476,  958,
          625,  965,  968,  516,  517,  465,  980,  269,  983,  477,
         1015,  647,  648,  995,  626,  627, 1013,  460,  584, 1021,
          443, 1029, 1243,  444, 1033,  262, 1036,  276,  409,  426,
         1041, 1042,    8, 1048,  671,  672,   10,  273,  496, 1073,
          666,  440, 1089,  430, 1159, 1161,  544,  478, 1179, 1178,
          659,  493, 1184, 1246,  438,  518,  461,  310,  519,  302,
          327,  307,  534,  289,  328,  520,  462, 1252, 1260,  325,
           30, 1280, 1291,  335,  562,  599
    );

    protected $ruleToNonTerminal = array(
            0,    1,    3,    3,    2,    5,    5,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    7,    7,
            7,    7,    7,    7,    7,    7,    8,    8,    9,   10,
           11,   11,   11,   12,   12,   13,   13,   14,   15,   15,
           16,   16,   17,   17,   18,   18,   21,   21,   22,   23,
           23,   24,   24,    4,    4,    4,    4,    4,    4,    4,
            4,    4,    4,    4,   29,   29,   30,   30,   32,   34,
           34,   28,   36,   36,   33,   38,   38,   35,   35,   37,
           37,   39,   39,   31,   40,   40,   41,   43,   44,   44,
           45,   46,   46,   48,   47,   47,   47,   47,   49,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   25,   25,   68,   68,   71,   71,   70,   69,
           69,   62,   74,   74,   75,   75,   76,   76,   77,   77,
           78,   78,   26,   26,   27,   27,   27,   27,   86,   86,
           88,   88,   81,   81,   81,   82,   82,   85,   85,   83,
           83,   89,   90,   90,   56,   56,   64,   64,   67,   67,
           67,   66,   91,   91,   92,   57,   57,   57,   57,   93,
           93,   94,   94,   95,   95,   96,   97,   97,   98,   98,
           99,   99,   54,   54,   50,   50,  101,   52,   52,  102,
           51,   51,   53,   53,   63,   63,   63,   63,   79,   79,
          105,  105,  107,  107,  108,  108,  108,  108,  106,  106,
          106,  110,  110,  110,  110,   87,   87,  113,  113,  113,
          111,  111,  114,  114,  112,  112,  115,  115,  116,  116,
          116,  116,  109,  109,   80,   80,   80,   20,   20,   20,
          118,  117,  117,  119,  119,  119,  119,   59,  120,  120,
          121,   60,  123,  123,  124,  124,  125,  125,   84,  126,
          126,  126,  126,  126,  126,  131,  131,  132,  132,  133,
          133,  133,  133,  133,  134,  135,  135,  130,  130,  127,
          127,  129,  129,  137,  137,  136,  136,  136,  136,  136,
          136,  136,  128,  138,  138,  140,  139,  139,   61,  100,
          141,  141,   55,   55,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,  148,  142,  142,
          147,  147,  150,  151,  151,  152,  153,  153,  153,   19,
           19,   72,   72,   72,   72,  143,  143,  143,  143,  155,
          155,  144,  144,  146,  146,  146,  149,  149,  160,  160,
          160,  160,  160,  160,  160,  160,  160,  161,  161,  104,
          163,  163,  163,  163,  145,  145,  145,  145,  145,  145,
          145,  145,   58,   58,  158,  158,  158,  158,  164,  164,
          154,  154,  154,  165,  165,  165,  165,  165,  165,   73,
           73,   65,   65,   65,   65,  122,  122,  122,  122,  168,
          167,  157,  157,  157,  157,  157,  157,  157,  156,  156,
          156,  166,  166,  166,  166,  103,  162,  170,  170,  169,
          169,  171,  171,  171,  171,  171,  171,  171,  171,  159,
          159,  159,  159,  173,  174,  172,  172,  172,  172,  172,
          172,  172,  172,  175,  175,  175,  175
    );

    protected $ruleToLength = array(
            1,    1,    2,    0,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            0,    1,    0,    1,    1,    2,    1,    3,    4,    1,
            2,    0,    1,    1,    1,    1,    1,    3,    5,    4,
            3,    4,    2,    3,    1,    1,    7,    6,    2,    3,
            1,    2,    3,    1,    2,    3,    1,    1,    3,    1,
            3,    1,    2,    2,    3,    1,    3,    2,    3,    1,
            3,    2,    0,    1,    1,    1,    1,    1,    3,    7,
           10,    5,    7,    9,    5,    3,    3,    3,    3,    3,
            3,    1,    2,    5,    7,    9,    6,    5,    6,    3,
            2,    1,    1,    1,    0,    2,    1,    3,    8,    0,
            4,    2,    1,    3,    0,    1,    0,    1,    0,    1,
            3,    1,    8,    9,    8,    7,    6,    8,    0,    2,
            0,    2,    1,    2,    2,    0,    2,    0,    2,    0,
            2,    2,    1,    3,    1,    4,    1,    4,    1,    1,
            4,    2,    1,    3,    3,    3,    4,    4,    5,    0,
            2,    4,    3,    1,    1,    7,    0,    2,    1,    3,
            3,    4,    1,    4,    0,    2,    5,    0,    2,    6,
            0,    2,    0,    3,    1,    2,    1,    1,    2,    0,
            1,    3,    0,    2,    1,    1,    1,    1,    6,    8,
            6,    1,    2,    1,    1,    1,    1,    1,    1,    1,
            3,    3,    3,    3,    3,    3,    3,    3,    1,    2,
            1,    1,    0,    1,    0,    2,    2,    2,    4,    3,
            1,    1,    3,    1,    2,    2,    3,    2,    3,    1,
            1,    2,    3,    1,    1,    3,    2,    0,    1,    5,
            5,   10,    3,    5,    1,    1,    3,    0,    2,    4,
            5,    4,    4,    4,    3,    1,    1,    1,    1,    1,
            1,    0,    1,    1,    2,    1,    1,    1,    1,    1,
            1,    1,    2,    1,    3,    1,    1,    3,    2,    2,
            3,    1,    0,    1,    1,    3,    3,    3,    4,    1,
            1,    2,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    2,    2,    2,    2,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    2,    2,    2,
            2,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    5,    4,    3,    4,    4,    2,    2,    4,
            2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
            2,    1,    3,    2,    1,    2,    4,    2,    2,    8,
            9,    8,    9,    9,   10,    9,   10,    8,    3,    2,
            0,    4,    2,    1,    3,    2,    2,    2,    4,    1,
            1,    1,    1,    1,    1,    1,    1,    3,    1,    1,
            1,    0,    3,    0,    1,    1,    0,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    3,    3,    3,
            4,    1,    1,    3,    1,    1,    1,    1,    1,    3,
            2,    3,    0,    1,    1,    3,    1,    1,    1,    1,
            1,    3,    1,    1,    4,    4,    1,    4,    4,    0,
            1,    1,    1,    3,    3,    1,    4,    2,    2,    1,
            3,    1,    4,    4,    3,    3,    3,    3,    1,    3,
            1,    1,    3,    1,    1,    4,    1,    1,    1,    3,
            1,    1,    2,    1,    3,    4,    3,    2,    0,    2,
            2,    1,    2,    1,    1,    1,    4,    3,    3,    3,
            3,    6,    3,    1,    1,    2,    1
    );

    protected function initReduceCallbacks() {
        $this->reduceCallbacks = [
            0 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            1 => function ($stackPos) {
                 $this->semValue = $this->handleNamespaces($this->semStack[$stackPos-(1-1)]);
            },
            2 => function ($stackPos) {
                 if (is_array($this->semStack[$stackPos-(2-2)])) { $this->semValue = array_merge($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]); } else { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; };
            },
            3 => function ($stackPos) {
                 $this->semValue = array();
            },
            4 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            5 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            6 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            7 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            8 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            9 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            10 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            11 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            12 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            13 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            14 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            15 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            16 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            17 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            18 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            19 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            20 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            21 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            22 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            23 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            24 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            25 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            26 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            27 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            28 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            29 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            30 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            31 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            32 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            33 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            34 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            35 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            36 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            37 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            38 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            39 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            40 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            41 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            42 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            43 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            44 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            45 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            46 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            47 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            48 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            49 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            50 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            51 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            52 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            53 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            54 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            55 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            56 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            57 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            58 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            59 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            60 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            61 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            62 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            63 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            64 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            65 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            66 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            67 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            68 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            69 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            70 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            71 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            72 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            73 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            74 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            75 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            76 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            77 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            78 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            79 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            80 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            81 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            82 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            83 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            84 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            85 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            86 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            87 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            88 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            89 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            90 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            91 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            92 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            93 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            94 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            95 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            96 => function ($stackPos) {
                 $this->semValue = new Name(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            97 => function ($stackPos) {
                 $this->semValue = new Expr\Variable(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            98 => function ($stackPos) {
                 /* nothing */
            },
            99 => function ($stackPos) {
                 /* nothing */
            },
            100 => function ($stackPos) {
                 /* nothing */
            },
            101 => function ($stackPos) {
                 $this->emitError(new Error('A trailing comma is not allowed here', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes));
            },
            102 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            103 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            104 => function ($stackPos) {
                 $this->semValue = new Node\Attribute($this->semStack[$stackPos-(1-1)], [], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            105 => function ($stackPos) {
                 $this->semValue = new Node\Attribute($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            106 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            107 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            108 => function ($stackPos) {
                 $this->semValue = new Node\AttributeGroup($this->semStack[$stackPos-(4-2)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            109 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            110 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            111 => function ($stackPos) {
                 $this->semValue = [];
            },
            112 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            113 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            114 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            115 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            116 => function ($stackPos) {
                 $this->semValue = new Stmt\HaltCompiler($this->lexer->handleHaltCompiler(), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            117 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_($this->semStack[$stackPos-(3-2)], null, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_SEMICOLON);
            $this->checkNamespace($this->semValue);
            },
            118 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_($this->semStack[$stackPos-(5-2)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $this->checkNamespace($this->semValue);
            },
            119 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_(null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $this->checkNamespace($this->semValue);
            },
            120 => function ($stackPos) {
                 $this->semValue = new Stmt\Use_($this->semStack[$stackPos-(3-2)], Stmt\Use_::TYPE_NORMAL, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            121 => function ($stackPos) {
                 $this->semValue = new Stmt\Use_($this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-2)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            122 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            123 => function ($stackPos) {
                 $this->semValue = new Stmt\Const_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            124 => function ($stackPos) {
                 $this->semValue = Stmt\Use_::TYPE_FUNCTION;
            },
            125 => function ($stackPos) {
                 $this->semValue = Stmt\Use_::TYPE_CONSTANT;
            },
            126 => function ($stackPos) {
                 $this->semValue = new Stmt\GroupUse($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-6)], $this->semStack[$stackPos-(7-2)], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            127 => function ($stackPos) {
                 $this->semValue = new Stmt\GroupUse($this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-5)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            128 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            129 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            130 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            131 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            132 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            133 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            134 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            135 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            136 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            137 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(1-1));
            },
            138 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(3-3));
            },
            139 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(1-1));
            },
            140 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(3-3));
            },
            141 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $this->semValue->type = Stmt\Use_::TYPE_NORMAL;
            },
            142 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)]; $this->semValue->type = $this->semStack[$stackPos-(2-1)];
            },
            143 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            144 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            145 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            146 => function ($stackPos) {
                 $this->semValue = new Node\Const_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            147 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            148 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            149 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            150 => function ($stackPos) {
                 $this->semValue = new Node\Const_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            151 => function ($stackPos) {
                 if (is_array($this->semStack[$stackPos-(2-2)])) { $this->semValue = array_merge($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]); } else { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; };
            },
            152 => function ($stackPos) {
                 $this->semValue = array();
            },
            153 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            154 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            155 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            156 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            157 => function ($stackPos) {
                 throw new Error('__HALT_COMPILER() can only be used from the outermost scope', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            158 => function ($stackPos) {

        if ($this->semStack[$stackPos-(3-2)]) {
            $this->semValue = $this->semStack[$stackPos-(3-2)]; $attrs = $this->startAttributeStack[$stackPos-(3-1)]; $stmts = $this->semValue; if (!empty($attrs['comments'])) {$stmts[0]->setAttribute('comments', array_merge($attrs['comments'], $stmts[0]->getAttribute('comments', []))); };
        } else {
            $startAttributes = $this->startAttributeStack[$stackPos-(3-1)]; if (isset($startAttributes['comments'])) { $this->semValue = new Stmt\Nop($startAttributes + $this->endAttributes); } else { $this->semValue = null; };
            if (null === $this->semValue) { $this->semValue = array(); }
        }

            },
            159 => function ($stackPos) {
                 $this->semValue = new Stmt\If_($this->semStack[$stackPos-(7-3)], ['stmts' => is_array($this->semStack[$stackPos-(7-5)]) ? $this->semStack[$stackPos-(7-5)] : array($this->semStack[$stackPos-(7-5)]), 'elseifs' => $this->semStack[$stackPos-(7-6)], 'else' => $this->semStack[$stackPos-(7-7)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            160 => function ($stackPos) {
                 $this->semValue = new Stmt\If_($this->semStack[$stackPos-(10-3)], ['stmts' => $this->semStack[$stackPos-(10-6)], 'elseifs' => $this->semStack[$stackPos-(10-7)], 'else' => $this->semStack[$stackPos-(10-8)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            },
            161 => function ($stackPos) {
                 $this->semValue = new Stmt\While_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            162 => function ($stackPos) {
                 $this->semValue = new Stmt\Do_($this->semStack[$stackPos-(7-5)], is_array($this->semStack[$stackPos-(7-2)]) ? $this->semStack[$stackPos-(7-2)] : array($this->semStack[$stackPos-(7-2)]), $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            163 => function ($stackPos) {
                 $this->semValue = new Stmt\For_(['init' => $this->semStack[$stackPos-(9-3)], 'cond' => $this->semStack[$stackPos-(9-5)], 'loop' => $this->semStack[$stackPos-(9-7)], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            164 => function ($stackPos) {
                 $this->semValue = new Stmt\Switch_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            165 => function ($stackPos) {
                 $this->semValue = new Stmt\Break_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            166 => function ($stackPos) {
                 $this->semValue = new Stmt\Continue_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            167 => function ($stackPos) {
                 $this->semValue = new Stmt\Return_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            168 => function ($stackPos) {
                 $this->semValue = new Stmt\Global_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            169 => function ($stackPos) {
                 $this->semValue = new Stmt\Static_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            170 => function ($stackPos) {
                 $this->semValue = new Stmt\Echo_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            171 => function ($stackPos) {
                 $this->semValue = new Stmt\InlineHTML($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            172 => function ($stackPos) {

        $e = $this->semStack[$stackPos-(2-1)];
        if ($e instanceof Expr\Throw_) {
            // For backwards-compatibility reasons, convert throw in statement position into
            // Stmt\Throw_ rather than Stmt\Expression(Expr\Throw_).
            $this->semValue = new Stmt\Throw_($e->expr, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
        } else {
            $this->semValue = new Stmt\Expression($e, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
        }

            },
            173 => function ($stackPos) {
                 $this->semValue = new Stmt\Unset_($this->semStack[$stackPos-(5-3)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            174 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-5)][0], ['keyVar' => null, 'byRef' => $this->semStack[$stackPos-(7-5)][1], 'stmts' => $this->semStack[$stackPos-(7-7)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            175 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(9-3)], $this->semStack[$stackPos-(9-7)][0], ['keyVar' => $this->semStack[$stackPos-(9-5)], 'byRef' => $this->semStack[$stackPos-(9-7)][1], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            176 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(6-3)], new Expr\Error($this->startAttributeStack[$stackPos-(6-4)] + $this->endAttributeStack[$stackPos-(6-4)]), ['stmts' => $this->semStack[$stackPos-(6-6)]], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            177 => function ($stackPos) {
                 $this->semValue = new Stmt\Declare_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            178 => function ($stackPos) {
                 $this->semValue = new Stmt\TryCatch($this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-5)], $this->semStack[$stackPos-(6-6)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes); $this->checkTryCatch($this->semValue);
            },
            179 => function ($stackPos) {
                 $this->semValue = new Stmt\Goto_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            180 => function ($stackPos) {
                 $this->semValue = new Stmt\Label($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            181 => function ($stackPos) {
                 $this->semValue = array(); /* means: no statement */
            },
            182 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            183 => function ($stackPos) {
                 $startAttributes = $this->startAttributeStack[$stackPos-(1-1)]; if (isset($startAttributes['comments'])) { $this->semValue = new Stmt\Nop($startAttributes + $this->endAttributes); } else { $this->semValue = null; };
            if ($this->semValue === null) $this->semValue = array(); /* means: no statement */
            },
            184 => function ($stackPos) {
                 $this->semValue = array();
            },
            185 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            186 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            187 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            188 => function ($stackPos) {
                 $this->semValue = new Stmt\Catch_($this->semStack[$stackPos-(8-3)], $this->semStack[$stackPos-(8-4)], $this->semStack[$stackPos-(8-7)], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            189 => function ($stackPos) {
                 $this->semValue = null;
            },
            190 => function ($stackPos) {
                 $this->semValue = new Stmt\Finally_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            191 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            192 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            193 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            194 => function ($stackPos) {
                 $this->semValue = false;
            },
            195 => function ($stackPos) {
                 $this->semValue = true;
            },
            196 => function ($stackPos) {
                 $this->semValue = false;
            },
            197 => function ($stackPos) {
                 $this->semValue = true;
            },
            198 => function ($stackPos) {
                 $this->semValue = false;
            },
            199 => function ($stackPos) {
                 $this->semValue = true;
            },
            200 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            201 => function ($stackPos) {
                 $this->semValue = [];
            },
            202 => function ($stackPos) {
                 $this->semValue = new Stmt\Function_($this->semStack[$stackPos-(8-3)], ['byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-5)], 'returnType' => $this->semStack[$stackPos-(8-7)], 'stmts' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            203 => function ($stackPos) {
                 $this->semValue = new Stmt\Function_($this->semStack[$stackPos-(9-4)], ['byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-6)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            204 => function ($stackPos) {
                 $this->semValue = new Stmt\Class_($this->semStack[$stackPos-(8-3)], ['type' => $this->semStack[$stackPos-(8-2)], 'extends' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            $this->checkClass($this->semValue, $stackPos-(8-3));
            },
            205 => function ($stackPos) {
                 $this->semValue = new Stmt\Interface_($this->semStack[$stackPos-(7-3)], ['extends' => $this->semStack[$stackPos-(7-4)], 'stmts' => $this->semStack[$stackPos-(7-6)], 'attrGroups' => $this->semStack[$stackPos-(7-1)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            $this->checkInterface($this->semValue, $stackPos-(7-3));
            },
            206 => function ($stackPos) {
                 $this->semValue = new Stmt\Trait_($this->semStack[$stackPos-(6-3)], ['stmts' => $this->semStack[$stackPos-(6-5)], 'attrGroups' => $this->semStack[$stackPos-(6-1)]], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            207 => function ($stackPos) {
                 $this->semValue = new Stmt\Enum_($this->semStack[$stackPos-(8-3)], ['scalarType' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            $this->checkEnum($this->semValue, $stackPos-(8-3));
            },
            208 => function ($stackPos) {
                 $this->semValue = null;
            },
            209 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            210 => function ($stackPos) {
                 $this->semValue = null;
            },
            211 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            212 => function ($stackPos) {
                 $this->semValue = 0;
            },
            213 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_ABSTRACT;
            },
            214 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_FINAL;
            },
            215 => function ($stackPos) {
                 $this->semValue = null;
            },
            216 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            217 => function ($stackPos) {
                 $this->semValue = array();
            },
            218 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            219 => function ($stackPos) {
                 $this->semValue = array();
            },
            220 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            221 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            222 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            223 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            224 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            225 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            226 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            227 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            228 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            229 => function ($stackPos) {
                 $this->semValue = null;
            },
            230 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            231 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            232 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            233 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            234 => function ($stackPos) {
                 $this->semValue = new Stmt\DeclareDeclare($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            235 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            236 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-3)];
            },
            237 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            238 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(5-3)];
            },
            239 => function ($stackPos) {
                 $this->semValue = array();
            },
            240 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            241 => function ($stackPos) {
                 $this->semValue = new Stmt\Case_($this->semStack[$stackPos-(4-2)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            242 => function ($stackPos) {
                 $this->semValue = new Stmt\Case_(null, $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            243 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            244 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            245 => function ($stackPos) {
                 $this->semValue = new Expr\Match_($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-6)], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            246 => function ($stackPos) {
                 $this->semValue = [];
            },
            247 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            248 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            249 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            250 => function ($stackPos) {
                 $this->semValue = new Node\MatchArm($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            251 => function ($stackPos) {
                 $this->semValue = new Node\MatchArm(null, $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            252 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            253 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            254 => function ($stackPos) {
                 $this->semValue = array();
            },
            255 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            256 => function ($stackPos) {
                 $this->semValue = new Stmt\ElseIf_($this->semStack[$stackPos-(5-3)], is_array($this->semStack[$stackPos-(5-5)]) ? $this->semStack[$stackPos-(5-5)] : array($this->semStack[$stackPos-(5-5)]), $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            257 => function ($stackPos) {
                 $this->semValue = array();
            },
            258 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            259 => function ($stackPos) {
                 $this->semValue = new Stmt\ElseIf_($this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-6)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            260 => function ($stackPos) {
                 $this->semValue = null;
            },
            261 => function ($stackPos) {
                 $this->semValue = new Stmt\Else_(is_array($this->semStack[$stackPos-(2-2)]) ? $this->semStack[$stackPos-(2-2)] : array($this->semStack[$stackPos-(2-2)]), $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            262 => function ($stackPos) {
                 $this->semValue = null;
            },
            263 => function ($stackPos) {
                 $this->semValue = new Stmt\Else_($this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            264 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            265 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(2-2)], true);
            },
            266 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            267 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            268 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            269 => function ($stackPos) {
                 $this->semValue = array();
            },
            270 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            271 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            272 => function ($stackPos) {
                 $this->semValue = 0;
            },
            273 => function ($stackPos) {
                 $this->checkModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            274 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PUBLIC;
            },
            275 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PROTECTED;
            },
            276 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PRIVATE;
            },
            277 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_READONLY;
            },
            278 => function ($stackPos) {
                 $this->semValue = new Node\Param($this->semStack[$stackPos-(6-6)], null, $this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-4)], $this->semStack[$stackPos-(6-5)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes, $this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-1)]);
            $this->checkParam($this->semValue);
            },
            279 => function ($stackPos) {
                 $this->semValue = new Node\Param($this->semStack[$stackPos-(8-6)], $this->semStack[$stackPos-(8-8)], $this->semStack[$stackPos-(8-3)], $this->semStack[$stackPos-(8-4)], $this->semStack[$stackPos-(8-5)], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes, $this->semStack[$stackPos-(8-2)], $this->semStack[$stackPos-(8-1)]);
            $this->checkParam($this->semValue);
            },
            280 => function ($stackPos) {
                 $this->semValue = new Node\Param(new Expr\Error($this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes), null, $this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-4)], $this->semStack[$stackPos-(6-5)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes, $this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-1)]);
            },
            281 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            282 => function ($stackPos) {
                 $this->semValue = new Node\NullableType($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            283 => function ($stackPos) {
                 $this->semValue = new Node\UnionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            284 => function ($stackPos) {
                 $this->semValue = new Node\IntersectionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            285 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            286 => function ($stackPos) {
                 $this->semValue = new Node\Name('static', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            287 => function ($stackPos) {
                 $this->semValue = $this->handleBuiltinTypes($this->semStack[$stackPos-(1-1)]);
            },
            288 => function ($stackPos) {
                 $this->semValue = new Node\Identifier('array', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            289 => function ($stackPos) {
                 $this->semValue = new Node\Identifier('callable', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            290 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            291 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            292 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            293 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            294 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            295 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            296 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            297 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            298 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            299 => function ($stackPos) {
                 $this->semValue = new Node\NullableType($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            300 => function ($stackPos) {
                 $this->semValue = new Node\UnionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            301 => function ($stackPos) {
                 $this->semValue = new Node\IntersectionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            302 => function ($stackPos) {
                 $this->semValue = null;
            },
            303 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            304 => function ($stackPos) {
                 $this->semValue = null;
            },
            305 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            306 => function ($stackPos) {
                 $this->semValue = null;
            },
            307 => function ($stackPos) {
                 $this->semValue = array();
            },
            308 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            309 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-2)]);
            },
            310 => function ($stackPos) {
                 $this->semValue = new Node\VariadicPlaceholder($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            311 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            312 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            313 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(1-1)], false, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            314 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(2-2)], true, false, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            315 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(2-2)], false, true, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            316 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(3-3)], false, false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes, $this->semStack[$stackPos-(3-1)]);
            },
            317 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            318 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            319 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            320 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            321 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            322 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            323 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            324 => function ($stackPos) {
                 $this->semValue = new Stmt\StaticVar($this->semStack[$stackPos-(1-1)], null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            325 => function ($stackPos) {
                 $this->semValue = new Stmt\StaticVar($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            326 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(2-2)] !== null) { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; }
            },
            327 => function ($stackPos) {
                 $this->semValue = array();
            },
            328 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            329 => function ($stackPos) {
                 $this->semValue = new Stmt\Property($this->semStack[$stackPos-(5-2)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes, $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-1)]);
            $this->checkProperty($this->semValue, $stackPos-(5-2));
            },
            330 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassConst($this->semStack[$stackPos-(5-4)], $this->semStack[$stackPos-(5-2)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes, $this->semStack[$stackPos-(5-1)]);
            $this->checkClassConst($this->semValue, $stackPos-(5-2));
            },
            331 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassMethod($this->semStack[$stackPos-(10-5)], ['type' => $this->semStack[$stackPos-(10-2)], 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-7)], 'returnType' => $this->semStack[$stackPos-(10-9)], 'stmts' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            $this->checkClassMethod($this->semValue, $stackPos-(10-2));
            },
            332 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUse($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            333 => function ($stackPos) {
                 $this->semValue = new Stmt\EnumCase($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-4)], $this->semStack[$stackPos-(5-1)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            334 => function ($stackPos) {
                 $this->semValue = null; /* will be skipped */
            },
            335 => function ($stackPos) {
                 $this->semValue = array();
            },
            336 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            337 => function ($stackPos) {
                 $this->semValue = array();
            },
            338 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            339 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Precedence($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            340 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(5-1)][0], $this->semStack[$stackPos-(5-1)][1], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            341 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], $this->semStack[$stackPos-(4-3)], null, $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            342 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            343 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            344 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            345 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            346 => function ($stackPos) {
                 $this->semValue = array(null, $this->semStack[$stackPos-(1-1)]);
            },
            347 => function ($stackPos) {
                 $this->semValue = null;
            },
            348 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            349 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            350 => function ($stackPos) {
                 $this->semValue = 0;
            },
            351 => function ($stackPos) {
                 $this->semValue = 0;
            },
            352 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            353 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            354 => function ($stackPos) {
                 $this->checkModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            355 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PUBLIC;
            },
            356 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PROTECTED;
            },
            357 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PRIVATE;
            },
            358 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_STATIC;
            },
            359 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_ABSTRACT;
            },
            360 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_FINAL;
            },
            361 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_READONLY;
            },
            362 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            363 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            364 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            365 => function ($stackPos) {
                 $this->semValue = new Node\VarLikeIdentifier(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            366 => function ($stackPos) {
                 $this->semValue = new Stmt\PropertyProperty($this->semStack[$stackPos-(1-1)], null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            367 => function ($stackPos) {
                 $this->semValue = new Stmt\PropertyProperty($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            368 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            369 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            370 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            371 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            372 => function ($stackPos) {
                 $this->semValue = array();
            },
            373 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            374 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            375 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            376 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            377 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            378 => function ($stackPos) {
                 $this->semValue = new Expr\AssignRef($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            379 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            380 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            381 => function ($stackPos) {
                 $this->semValue = new Expr\Clone_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            382 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            383 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            384 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            385 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            386 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            387 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            388 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            389 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            390 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            391 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            392 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            393 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            394 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Coalesce($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            395 => function ($stackPos) {
                 $this->semValue = new Expr\PostInc($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            396 => function ($stackPos) {
                 $this->semValue = new Expr\PreInc($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            397 => function ($stackPos) {
                 $this->semValue = new Expr\PostDec($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            398 => function ($stackPos) {
                 $this->semValue = new Expr\PreDec($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            399 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            400 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            401 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            402 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            403 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            404 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            405 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            406 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            407 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            408 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            409 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            410 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            411 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            412 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            413 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            414 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            415 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            416 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            417 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryPlus($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            418 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryMinus($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            419 => function ($stackPos) {
                 $this->semValue = new Expr\BooleanNot($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            420 => function ($stackPos) {
                 $this->semValue = new Expr\BitwiseNot($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            421 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Identical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            422 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotIdentical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            423 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Equal($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            424 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            425 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Spaceship($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            426 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Smaller($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            427 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\SmallerOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            428 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Greater($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            429 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\GreaterOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            430 => function ($stackPos) {
                 $this->semValue = new Expr\Instanceof_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            431 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            432 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(5-1)], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            433 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(4-1)], null, $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            434 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Coalesce($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            435 => function ($stackPos) {
                 $this->semValue = new Expr\Isset_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            436 => function ($stackPos) {
                 $this->semValue = new Expr\Empty_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            437 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            438 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE_ONCE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            439 => function ($stackPos) {
                 $this->semValue = new Expr\Eval_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            440 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            441 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE_ONCE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            442 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Int_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            443 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes;
            $attrs['kind'] = $this->getFloatCastKind($this->semStack[$stackPos-(2-1)]);
            $this->semValue = new Expr\Cast\Double($this->semStack[$stackPos-(2-2)], $attrs);
            },
            444 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\String_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            445 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Array_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            446 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Object_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            447 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Bool_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            448 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Unset_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            449 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes;
            $attrs['kind'] = strtolower($this->semStack[$stackPos-(2-1)]) === 'exit' ? Expr\Exit_::KIND_EXIT : Expr\Exit_::KIND_DIE;
            $this->semValue = new Expr\Exit_($this->semStack[$stackPos-(2-2)], $attrs);
            },
            450 => function ($stackPos) {
                 $this->semValue = new Expr\ErrorSuppress($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            451 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            452 => function ($stackPos) {
                 $this->semValue = new Expr\ShellExec($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            453 => function ($stackPos) {
                 $this->semValue = new Expr\Print_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            454 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_(null, null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            455 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_($this->semStack[$stackPos-(2-2)], null, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            456 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_($this->semStack[$stackPos-(4-4)], $this->semStack[$stackPos-(4-2)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            457 => function ($stackPos) {
                 $this->semValue = new Expr\YieldFrom($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            458 => function ($stackPos) {
                 $this->semValue = new Expr\Throw_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            459 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-4)], 'returnType' => $this->semStack[$stackPos-(8-6)], 'expr' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            460 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'returnType' => $this->semStack[$stackPos-(9-7)], 'expr' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            461 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-4)], 'uses' => $this->semStack[$stackPos-(8-6)], 'returnType' => $this->semStack[$stackPos-(8-7)], 'stmts' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            462 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'uses' => $this->semStack[$stackPos-(9-7)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            463 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'returnType' => $this->semStack[$stackPos-(9-7)], 'expr' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            464 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-6)], 'returnType' => $this->semStack[$stackPos-(10-8)], 'expr' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            },
            465 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'uses' => $this->semStack[$stackPos-(9-7)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            466 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-6)], 'uses' => $this->semStack[$stackPos-(10-8)], 'returnType' => $this->semStack[$stackPos-(10-9)], 'stmts' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            },
            467 => function ($stackPos) {
                 $this->semValue = array(new Stmt\Class_(null, ['type' => 0, 'extends' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes), $this->semStack[$stackPos-(8-3)]);
            $this->checkClass($this->semValue[0], -1);
            },
            468 => function ($stackPos) {
                 $this->semValue = new Expr\New_($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            469 => function ($stackPos) {
                 list($class, $ctorArgs) = $this->semStack[$stackPos-(2-2)]; $this->semValue = new Expr\New_($class, $ctorArgs, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            470 => function ($stackPos) {
                 $this->semValue = array();
            },
            471 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-3)];
            },
            472 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            473 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            474 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            475 => function ($stackPos) {
                 $this->semValue = new Expr\ClosureUse($this->semStack[$stackPos-(2-2)], $this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            476 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            477 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            478 => function ($stackPos) {
                 $this->semValue = new Expr\StaticCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            479 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            480 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            481 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            482 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            483 => function ($stackPos) {
                 $this->semValue = new Name\FullyQualified(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            484 => function ($stackPos) {
                 $this->semValue = new Name\Relative(substr($this->semStack[$stackPos-(1-1)], 10), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            485 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            486 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            487 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            488 => function ($stackPos) {
                 $this->semValue = new Expr\Error($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->errorState = 2;
            },
            489 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            490 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            491 => function ($stackPos) {
                 $this->semValue = null;
            },
            492 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            493 => function ($stackPos) {
                 $this->semValue = array();
            },
            494 => function ($stackPos) {
                 $this->semValue = array(new Scalar\EncapsedStringPart(Scalar\String_::parseEscapeSequences($this->semStack[$stackPos-(1-1)], '`'), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes));
            },
            495 => function ($stackPos) {
                 foreach ($this->semStack[$stackPos-(1-1)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', true); } }; $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            496 => function ($stackPos) {
                 $this->semValue = array();
            },
            497 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            498 => function ($stackPos) {
                 $this->semValue = new Expr\ConstFetch($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            499 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Line($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            500 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\File($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            501 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Dir($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            502 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Class_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            503 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Trait_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            504 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Method($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            505 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Function_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            506 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Namespace_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            507 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            508 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(3-1)], new Expr\Error($this->startAttributeStack[$stackPos-(3-3)] + $this->endAttributeStack[$stackPos-(3-3)]), $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->errorState = 2;
            },
            509 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = Expr\Array_::KIND_SHORT;
            $this->semValue = new Expr\Array_($this->semStack[$stackPos-(3-2)], $attrs);
            },
            510 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes; $attrs['kind'] = Expr\Array_::KIND_LONG;
            $this->semValue = new Expr\Array_($this->semStack[$stackPos-(4-3)], $attrs);
            },
            511 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            512 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes; $attrs['kind'] = ($this->semStack[$stackPos-(1-1)][0] === "'" || ($this->semStack[$stackPos-(1-1)][1] === "'" && ($this->semStack[$stackPos-(1-1)][0] === 'b' || $this->semStack[$stackPos-(1-1)][0] === 'B')) ? Scalar\String_::KIND_SINGLE_QUOTED : Scalar\String_::KIND_DOUBLE_QUOTED);
            $this->semValue = new Scalar\String_(Scalar\String_::parse($this->semStack[$stackPos-(1-1)]), $attrs);
            },
            513 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = Scalar\String_::KIND_DOUBLE_QUOTED;
            foreach ($this->semStack[$stackPos-(3-2)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '"', true); } }; $this->semValue = new Scalar\Encapsed($this->semStack[$stackPos-(3-2)], $attrs);
            },
            514 => function ($stackPos) {
                 $this->semValue = $this->parseLNumber($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            515 => function ($stackPos) {
                 $this->semValue = new Scalar\DNumber(Scalar\DNumber::parse($this->semStack[$stackPos-(1-1)]), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            516 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            517 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            518 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            519 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(3-3)] + $this->endAttributeStack[$stackPos-(3-3)], true);
            },
            520 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(2-1)], '', $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(2-2)] + $this->endAttributeStack[$stackPos-(2-2)], true);
            },
            521 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(3-3)] + $this->endAttributeStack[$stackPos-(3-3)], true);
            },
            522 => function ($stackPos) {
                 $this->semValue = null;
            },
            523 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            524 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            525 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            526 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            527 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            528 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            529 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            530 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            531 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            532 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            533 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            534 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            535 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            536 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            537 => function ($stackPos) {
                 $this->semValue = new Expr\MethodCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            538 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafeMethodCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            539 => function ($stackPos) {
                 $this->semValue = null;
            },
            540 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            541 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            542 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            543 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            544 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            545 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            546 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            547 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            548 => function ($stackPos) {
                 $this->semValue = new Expr\Variable(new Expr\Error($this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes), $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes); $this->errorState = 2;
            },
            549 => function ($stackPos) {
                 $var = $this->semStack[$stackPos-(1-1)]->name; $this->semValue = \is_string($var) ? new Node\VarLikeIdentifier($var, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes) : $var;
            },
            550 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            551 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            552 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            553 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            554 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            555 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            556 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            557 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            558 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            559 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            560 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            561 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            562 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            563 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            564 => function ($stackPos) {
                 $this->semValue = new Expr\Error($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->errorState = 2;
            },
            565 => function ($stackPos) {
                 $this->semValue = new Expr\List_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            566 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $end = count($this->semValue)-1; if ($this->semValue[$end] === null) array_pop($this->semValue);
            },
            567 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            568 => function ($stackPos) {
                 /* do nothing -- prevent default action of $$=$this->semStack[$1]. See $551. */
            },
            569 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            570 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            571 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            572 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(2-2)], null, true, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            573 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            574 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(3-3)], $this->semStack[$stackPos-(3-1)], false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            575 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(4-4)], $this->semStack[$stackPos-(4-1)], true, $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            576 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(3-3)], $this->semStack[$stackPos-(3-1)], false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            577 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(2-2)], null, false, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes, true, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            578 => function ($stackPos) {
                 $this->semValue = null;
            },
            579 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            580 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            581 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            582 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]);
            },
            583 => function ($stackPos) {
                 $this->semValue = new Scalar\EncapsedStringPart($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            584 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            585 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            586 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            587 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            588 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            589 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            590 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            591 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-4)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            592 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            593 => function ($stackPos) {
                 $this->semValue = new Scalar\String_($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            594 => function ($stackPos) {
                 $this->semValue = $this->parseNumString($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            595 => function ($stackPos) {
                 $this->semValue = $this->parseNumString('-' . $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            596 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
        ];
    }
}

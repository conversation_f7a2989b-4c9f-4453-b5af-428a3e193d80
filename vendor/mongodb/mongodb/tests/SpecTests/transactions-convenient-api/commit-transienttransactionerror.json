{"runOn": [{"minServerVersion": "4.0", "topology": ["replicaset"]}, {"minServerVersion": "4.1.8", "topology": ["sharded"]}], "database_name": "withTransaction-tests", "collection_name": "test", "data": [], "tests": [{"description": "transaction is retried after commitTransaction TransientTransactionError (LockTimeout)", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 2}, "data": {"failCommands": ["commitTransaction"], "errorCode": 24, "closeConnection": false}}, "operations": [{"name": "withTransaction", "object": "session0", "arguments": {"callback": {"operations": [{"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 1}}, "result": {"insertedId": 1}}]}}}], "expectations": [{"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 1}], "ordered": true, "lsid": "session0", "txnNumber": {"$numberLong": "1"}, "startTransaction": true, "autocommit": false, "readConcern": null, "writeConcern": null}, "command_name": "insert", "database_name": "withTransaction-tests"}}, {"command_started_event": {"command": {"commitTransaction": 1, "lsid": "session0", "txnNumber": {"$numberLong": "1"}, "autocommit": false, "readConcern": null, "startTransaction": null, "writeConcern": null}, "command_name": "commitTransaction", "database_name": "admin"}}, {"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 1}], "ordered": true, "lsid": "session0", "readConcern": {"afterClusterTime": 42}, "txnNumber": {"$numberLong": "2"}, "startTransaction": true, "autocommit": false, "writeConcern": null}, "command_name": "insert", "database_name": "withTransaction-tests"}}, {"command_started_event": {"command": {"commitTransaction": 1, "lsid": "session0", "txnNumber": {"$numberLong": "2"}, "autocommit": false, "readConcern": null, "startTransaction": null, "writeConcern": null}, "command_name": "commitTransaction", "database_name": "admin"}}, {"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 1}], "ordered": true, "lsid": "session0", "readConcern": {"afterClusterTime": 42}, "txnNumber": {"$numberLong": "3"}, "startTransaction": true, "autocommit": false, "writeConcern": null}, "command_name": "insert", "database_name": "withTransaction-tests"}}, {"command_started_event": {"command": {"commitTransaction": 1, "lsid": "session0", "txnNumber": {"$numberLong": "3"}, "autocommit": false, "readConcern": null, "startTransaction": null, "writeConcern": null}, "command_name": "commitTransaction", "database_name": "admin"}}], "outcome": {"collection": {"data": [{"_id": 1}]}}}, {"description": "transaction is retried after commitTransaction TransientTransactionError (WriteConflict)", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 2}, "data": {"failCommands": ["commitTransaction"], "errorCode": 112, "closeConnection": false}}, "operations": [{"name": "withTransaction", "object": "session0", "arguments": {"callback": {"operations": [{"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 1}}, "result": {"insertedId": 1}}]}}}], "expectations": [{"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 1}], "ordered": true, "lsid": "session0", "txnNumber": {"$numberLong": "1"}, "startTransaction": true, "autocommit": false, "readConcern": null, "writeConcern": null}, "command_name": "insert", "database_name": "withTransaction-tests"}}, {"command_started_event": {"command": {"commitTransaction": 1, "lsid": "session0", "txnNumber": {"$numberLong": "1"}, "autocommit": false, "readConcern": null, "startTransaction": null, "writeConcern": null}, "command_name": "commitTransaction", "database_name": "admin"}}, {"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 1}], "ordered": true, "lsid": "session0", "readConcern": {"afterClusterTime": 42}, "txnNumber": {"$numberLong": "2"}, "startTransaction": true, "autocommit": false, "writeConcern": null}, "command_name": "insert", "database_name": "withTransaction-tests"}}, {"command_started_event": {"command": {"commitTransaction": 1, "lsid": "session0", "txnNumber": {"$numberLong": "2"}, "autocommit": false, "readConcern": null, "startTransaction": null, "writeConcern": null}, "command_name": "commitTransaction", "database_name": "admin"}}, {"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 1}], "ordered": true, "lsid": "session0", "readConcern": {"afterClusterTime": 42}, "txnNumber": {"$numberLong": "3"}, "startTransaction": true, "autocommit": false, "writeConcern": null}, "command_name": "insert", "database_name": "withTransaction-tests"}}, {"command_started_event": {"command": {"commitTransaction": 1, "lsid": "session0", "txnNumber": {"$numberLong": "3"}, "autocommit": false, "readConcern": null, "startTransaction": null, "writeConcern": null}, "command_name": "commitTransaction", "database_name": "admin"}}], "outcome": {"collection": {"data": [{"_id": 1}]}}}, {"description": "transaction is retried after commitTransaction TransientTransactionError (SnapshotUnavailable)", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 2}, "data": {"failCommands": ["commitTransaction"], "errorCode": 246, "closeConnection": false}}, "operations": [{"name": "withTransaction", "object": "session0", "arguments": {"callback": {"operations": [{"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 1}}, "result": {"insertedId": 1}}]}}}], "expectations": [{"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 1}], "ordered": true, "lsid": "session0", "txnNumber": {"$numberLong": "1"}, "startTransaction": true, "autocommit": false, "readConcern": null, "writeConcern": null}, "command_name": "insert", "database_name": "withTransaction-tests"}}, {"command_started_event": {"command": {"commitTransaction": 1, "lsid": "session0", "txnNumber": {"$numberLong": "1"}, "autocommit": false, "readConcern": null, "startTransaction": null, "writeConcern": null}, "command_name": "commitTransaction", "database_name": "admin"}}, {"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 1}], "ordered": true, "lsid": "session0", "readConcern": {"afterClusterTime": 42}, "txnNumber": {"$numberLong": "2"}, "startTransaction": true, "autocommit": false, "writeConcern": null}, "command_name": "insert", "database_name": "withTransaction-tests"}}, {"command_started_event": {"command": {"commitTransaction": 1, "lsid": "session0", "txnNumber": {"$numberLong": "2"}, "autocommit": false, "readConcern": null, "startTransaction": null, "writeConcern": null}, "command_name": "commitTransaction", "database_name": "admin"}}, {"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 1}], "ordered": true, "lsid": "session0", "readConcern": {"afterClusterTime": 42}, "txnNumber": {"$numberLong": "3"}, "startTransaction": true, "autocommit": false, "writeConcern": null}, "command_name": "insert", "database_name": "withTransaction-tests"}}, {"command_started_event": {"command": {"commitTransaction": 1, "lsid": "session0", "txnNumber": {"$numberLong": "3"}, "autocommit": false, "readConcern": null, "startTransaction": null, "writeConcern": null}, "command_name": "commitTransaction", "database_name": "admin"}}], "outcome": {"collection": {"data": [{"_id": 1}]}}}, {"description": "transaction is retried after commitTransaction TransientTransactionError (NoSuchTransaction)", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 2}, "data": {"failCommands": ["commitTransaction"], "errorCode": 251, "closeConnection": false}}, "operations": [{"name": "withTransaction", "object": "session0", "arguments": {"callback": {"operations": [{"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 1}}, "result": {"insertedId": 1}}]}}}], "expectations": [{"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 1}], "ordered": true, "lsid": "session0", "txnNumber": {"$numberLong": "1"}, "startTransaction": true, "autocommit": false, "readConcern": null, "writeConcern": null}, "command_name": "insert", "database_name": "withTransaction-tests"}}, {"command_started_event": {"command": {"commitTransaction": 1, "lsid": "session0", "txnNumber": {"$numberLong": "1"}, "autocommit": false, "readConcern": null, "startTransaction": null, "writeConcern": null}, "command_name": "commitTransaction", "database_name": "admin"}}, {"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 1}], "ordered": true, "lsid": "session0", "readConcern": {"afterClusterTime": 42}, "txnNumber": {"$numberLong": "2"}, "startTransaction": true, "autocommit": false, "writeConcern": null}, "command_name": "insert", "database_name": "withTransaction-tests"}}, {"command_started_event": {"command": {"commitTransaction": 1, "lsid": "session0", "txnNumber": {"$numberLong": "2"}, "autocommit": false, "readConcern": null, "startTransaction": null, "writeConcern": null}, "command_name": "commitTransaction", "database_name": "admin"}}, {"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 1}], "ordered": true, "lsid": "session0", "readConcern": {"afterClusterTime": 42}, "txnNumber": {"$numberLong": "3"}, "startTransaction": true, "autocommit": false, "writeConcern": null}, "command_name": "insert", "database_name": "withTransaction-tests"}}, {"command_started_event": {"command": {"commitTransaction": 1, "lsid": "session0", "txnNumber": {"$numberLong": "3"}, "autocommit": false, "readConcern": null, "startTransaction": null, "writeConcern": null}, "command_name": "commitTransaction", "database_name": "admin"}}], "outcome": {"collection": {"data": [{"_id": 1}]}}}]}
{"runOn": [{"minServerVersion": "4.1.8", "topology": ["sharded"]}], "database_name": "transaction-tests", "collection_name": "test", "data": [{"_id": 1}, {"_id": 2}], "tests": [{"description": "remain pinned after non-transient Interrupted error on insertOne", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["insert"], "errorCode": 11601}}}}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 4}}, "result": {"errorLabelsOmit": ["TransientTransactionError", "UnknownTransactionCommitResult"], "errorCodeName": "Interrupted"}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "commitTransaction", "object": "session0"}], "expectations": [{"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 3}], "ordered": true, "readConcern": null, "lsid": "session0", "txnNumber": {"$numberLong": "1"}, "startTransaction": true, "autocommit": false, "writeConcern": null}, "command_name": "insert", "database_name": "transaction-tests"}}, {"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 4}], "ordered": true, "readConcern": null, "lsid": "session0", "txnNumber": {"$numberLong": "1"}, "startTransaction": null, "autocommit": false, "writeConcern": null}, "command_name": "insert", "database_name": "transaction-tests"}}, {"command_started_event": {"command": {"commitTransaction": 1, "lsid": "session0", "txnNumber": {"$numberLong": "1"}, "startTransaction": null, "autocommit": false, "writeConcern": null, "recoveryToken": 42}, "command_name": "commitTransaction", "database_name": "admin"}}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}, {"_id": 3}]}}}, {"description": "unpin after transient error within a transaction", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["insert"], "closeConnection": true}}}}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 4}}, "result": {"errorLabelsContain": ["TransientTransactionError"], "errorLabelsOmit": ["UnknownTransactionCommitResult"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "expectations": [{"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 3}], "ordered": true, "readConcern": null, "lsid": "session0", "txnNumber": {"$numberLong": "1"}, "startTransaction": true, "autocommit": false, "writeConcern": null}, "command_name": "insert", "database_name": "transaction-tests"}}, {"command_started_event": {"command": {"insert": "test", "documents": [{"_id": 4}], "ordered": true, "readConcern": null, "lsid": "session0", "txnNumber": {"$numberLong": "1"}, "startTransaction": null, "autocommit": false, "writeConcern": null}, "command_name": "insert", "database_name": "transaction-tests"}}, {"command_started_event": {"command": {"abortTransaction": 1, "lsid": "session0", "txnNumber": {"$numberLong": "1"}, "startTransaction": null, "autocommit": false, "writeConcern": null, "recoveryToken": 42}, "command_name": "abortTransaction", "database_name": "admin"}}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on insertOne insert", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["insert"], "errorCode": 11601}}}}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 4}}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on insert<PERSON>any insert", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["insert"], "errorCode": 11601}}}}, {"name": "insertMany", "object": "collection", "arguments": {"session": "session0", "documents": [{"_id": 4}, {"_id": 5}]}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on updateOne update", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["update"], "errorCode": 11601}}}}, {"name": "updateOne", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}, "update": {"$inc": {"x": 1}}}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on replaceOne update", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["update"], "errorCode": 11601}}}}, {"name": "replaceOne", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}, "replacement": {"y": 1}}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on updateMany update", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["update"], "errorCode": 11601}}}}, {"name": "updateMany", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": {"$gte": 1}}, "update": {"$set": {"z": 1}}}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on deleteOne delete", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["delete"], "errorCode": 11601}}}}, {"name": "deleteOne", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on deleteMany delete", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["delete"], "errorCode": 11601}}}}, {"name": "deleteMany", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": {"$gte": 1}}}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on findOneAndDelete findAndModify", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["findAndModify"], "errorCode": 11601}}}}, {"name": "findOneAndDelete", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on findOneAndUpdate findAndModify", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["findAndModify"], "errorCode": 11601}}}}, {"name": "findOneAndUpdate", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}, "update": {"$inc": {"x": 1}}, "returnDocument": "Before"}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on findOneAndReplace findAndModify", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["findAndModify"], "errorCode": 11601}}}}, {"name": "findOneAndReplace", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}, "replacement": {"y": 1}, "returnDocument": "Before"}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on bulkWrite insert", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["insert"], "errorCode": 11601}}}}, {"name": "bulkWrite", "object": "collection", "arguments": {"session": "session0", "requests": [{"name": "insertOne", "arguments": {"document": {"_id": 1}}}]}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on bulkWrite update", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["update"], "errorCode": 11601}}}}, {"name": "bulkWrite", "object": "collection", "arguments": {"session": "session0", "requests": [{"name": "updateOne", "arguments": {"filter": {"_id": 1}, "update": {"$set": {"x": 1}}}}]}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on bulkWrite delete", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["delete"], "errorCode": 11601}}}}, {"name": "bulkWrite", "object": "collection", "arguments": {"session": "session0", "requests": [{"name": "deleteOne", "arguments": {"filter": {"_id": 1}}}]}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on find find", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["find"], "errorCode": 11601}}}}, {"name": "find", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on countDocuments aggregate", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["aggregate"], "errorCode": 11601}}}}, {"name": "countDocuments", "object": "collection", "arguments": {"session": "session0", "filter": {}}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on aggregate aggregate", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["aggregate"], "errorCode": 11601}}}}, {"name": "aggregate", "object": "collection", "arguments": {"session": "session0", "pipeline": []}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on distinct distinct", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["distinct"], "errorCode": 11601}}}}, {"name": "distinct", "object": "collection", "arguments": {"session": "session0", "fieldName": "_id"}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "remain pinned after non-transient Interrupted error on runCommand insert", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["insert"], "errorCode": 11601}}}}, {"name": "runCommand", "object": "database", "command_name": "insert", "arguments": {"session": "session0", "command": {"insert": "test", "documents": [{"_id": 1}]}}, "result": {"errorLabelsOmit": ["TransientTransactionError"]}}, {"name": "assertSessionPinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on insertOne insert", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["insert"], "closeConnection": true}}}}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 4}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on insertOne insert", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["insert"], "errorCode": 91}}}}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 4}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on insertMany insert", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["insert"], "closeConnection": true}}}}, {"name": "insertMany", "object": "collection", "arguments": {"session": "session0", "documents": [{"_id": 4}, {"_id": 5}]}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on insertMany insert", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["insert"], "errorCode": 91}}}}, {"name": "insertMany", "object": "collection", "arguments": {"session": "session0", "documents": [{"_id": 4}, {"_id": 5}]}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on updateOne update", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["update"], "closeConnection": true}}}}, {"name": "updateOne", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}, "update": {"$inc": {"x": 1}}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on updateOne update", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["update"], "errorCode": 91}}}}, {"name": "updateOne", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}, "update": {"$inc": {"x": 1}}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on replaceOne update", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["update"], "closeConnection": true}}}}, {"name": "replaceOne", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}, "replacement": {"y": 1}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on replaceOne update", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["update"], "errorCode": 91}}}}, {"name": "replaceOne", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}, "replacement": {"y": 1}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on updateMany update", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["update"], "closeConnection": true}}}}, {"name": "updateMany", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": {"$gte": 1}}, "update": {"$set": {"z": 1}}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on updateMany update", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["update"], "errorCode": 91}}}}, {"name": "updateMany", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": {"$gte": 1}}, "update": {"$set": {"z": 1}}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on deleteOne delete", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["delete"], "closeConnection": true}}}}, {"name": "deleteOne", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on deleteOne delete", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["delete"], "errorCode": 91}}}}, {"name": "deleteOne", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on deleteMany delete", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["delete"], "closeConnection": true}}}}, {"name": "deleteMany", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": {"$gte": 1}}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on deleteMany delete", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["delete"], "errorCode": 91}}}}, {"name": "deleteMany", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": {"$gte": 1}}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on findOneAndDelete findAndModify", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["findAndModify"], "closeConnection": true}}}}, {"name": "findOneAndDelete", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on findOneAndDelete findAndModify", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["findAndModify"], "errorCode": 91}}}}, {"name": "findOneAndDelete", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on findOneAndUpdate findAndModify", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["findAndModify"], "closeConnection": true}}}}, {"name": "findOneAndUpdate", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}, "update": {"$inc": {"x": 1}}, "returnDocument": "Before"}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on findOneAndUpdate findAndModify", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["findAndModify"], "errorCode": 91}}}}, {"name": "findOneAndUpdate", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}, "update": {"$inc": {"x": 1}}, "returnDocument": "Before"}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on findOneAndReplace findAndModify", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["findAndModify"], "closeConnection": true}}}}, {"name": "findOneAndReplace", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}, "replacement": {"y": 1}, "returnDocument": "Before"}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on findOneAndReplace findAndModify", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["findAndModify"], "errorCode": 91}}}}, {"name": "findOneAndReplace", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}, "replacement": {"y": 1}, "returnDocument": "Before"}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on bulkWrite insert", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["insert"], "closeConnection": true}}}}, {"name": "bulkWrite", "object": "collection", "arguments": {"session": "session0", "requests": [{"name": "insertOne", "arguments": {"document": {"_id": 1}}}]}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on bulkWrite insert", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["insert"], "errorCode": 91}}}}, {"name": "bulkWrite", "object": "collection", "arguments": {"session": "session0", "requests": [{"name": "insertOne", "arguments": {"document": {"_id": 1}}}]}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on bulkWrite update", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["update"], "closeConnection": true}}}}, {"name": "bulkWrite", "object": "collection", "arguments": {"session": "session0", "requests": [{"name": "updateOne", "arguments": {"filter": {"_id": 1}, "update": {"$set": {"x": 1}}}}]}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on bulkWrite update", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["update"], "errorCode": 91}}}}, {"name": "bulkWrite", "object": "collection", "arguments": {"session": "session0", "requests": [{"name": "updateOne", "arguments": {"filter": {"_id": 1}, "update": {"$set": {"x": 1}}}}]}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on bulkWrite delete", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["delete"], "closeConnection": true}}}}, {"name": "bulkWrite", "object": "collection", "arguments": {"session": "session0", "requests": [{"name": "deleteOne", "arguments": {"filter": {"_id": 1}}}]}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on bulkWrite delete", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["delete"], "errorCode": 91}}}}, {"name": "bulkWrite", "object": "collection", "arguments": {"session": "session0", "requests": [{"name": "deleteOne", "arguments": {"filter": {"_id": 1}}}]}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on find find", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["find"], "closeConnection": true}}}}, {"name": "find", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on find find", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["find"], "errorCode": 91}}}}, {"name": "find", "object": "collection", "arguments": {"session": "session0", "filter": {"_id": 1}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on countDocuments aggregate", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["aggregate"], "closeConnection": true}}}}, {"name": "countDocuments", "object": "collection", "arguments": {"session": "session0", "filter": {}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on countDocuments aggregate", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["aggregate"], "errorCode": 91}}}}, {"name": "countDocuments", "object": "collection", "arguments": {"session": "session0", "filter": {}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on aggregate aggregate", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["aggregate"], "closeConnection": true}}}}, {"name": "aggregate", "object": "collection", "arguments": {"session": "session0", "pipeline": []}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on aggregate aggregate", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["aggregate"], "errorCode": 91}}}}, {"name": "aggregate", "object": "collection", "arguments": {"session": "session0", "pipeline": []}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on distinct distinct", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["distinct"], "closeConnection": true}}}}, {"name": "distinct", "object": "collection", "arguments": {"session": "session0", "fieldName": "_id"}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on distinct distinct", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["distinct"], "errorCode": 91}}}}, {"name": "distinct", "object": "collection", "arguments": {"session": "session0", "fieldName": "_id"}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient connection error on runCommand insert", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["insert"], "closeConnection": true}}}}, {"name": "runCommand", "object": "database", "command_name": "insert", "arguments": {"session": "session0", "command": {"insert": "test", "documents": [{"_id": 1}]}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}, {"description": "unpin after transient ShutdownInProgress error on runCommand insert", "useMultipleMongoses": true, "operations": [{"name": "startTransaction", "object": "session0"}, {"name": "insertOne", "object": "collection", "arguments": {"session": "session0", "document": {"_id": 3}}, "result": {"insertedId": 3}}, {"name": "targetedFailPoint", "object": "testRunner", "arguments": {"session": "session0", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["insert"], "errorCode": 91}}}}, {"name": "runCommand", "object": "database", "command_name": "insert", "arguments": {"session": "session0", "command": {"insert": "test", "documents": [{"_id": 1}]}}, "result": {"errorLabelsContain": ["TransientTransactionError"]}}, {"name": "assertSessionUnpinned", "object": "testRunner", "arguments": {"session": "session0"}}, {"name": "abortTransaction", "object": "session0"}], "outcome": {"collection": {"data": [{"_id": 1}, {"_id": 2}]}}}]}
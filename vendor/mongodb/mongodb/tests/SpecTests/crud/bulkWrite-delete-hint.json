{"runOn": [{"minServerVersion": "4.3.4"}], "data": [{"_id": 1, "x": 11}, {"_id": 2, "x": 22}, {"_id": 3, "x": 33}, {"_id": 4, "x": 44}], "collection_name": "BulkWrite_delete_hint", "tests": [{"description": "BulkWrite deleteOne with hints", "operations": [{"name": "bulkWrite", "arguments": {"requests": [{"name": "deleteOne", "arguments": {"filter": {"_id": 1}, "hint": "_id_"}}, {"name": "deleteOne", "arguments": {"filter": {"_id": 2}, "hint": {"_id": 1}}}], "options": {"ordered": true}}, "result": {"deletedCount": 2, "insertedCount": 0, "insertedIds": {}, "matchedCount": 0, "modifiedCount": 0, "upsertedCount": 0, "upsertedIds": {}}}], "expectations": [{"command_started_event": {"command": {"delete": "BulkWrite_delete_hint", "deletes": [{"q": {"_id": 1}, "hint": "_id_", "limit": 1}, {"q": {"_id": 2}, "hint": {"_id": 1}, "limit": 1}], "ordered": true}}}], "outcome": {"collection": {"data": [{"_id": 3, "x": 33}, {"_id": 4, "x": 44}]}}}, {"description": "BulkWrite deleteMany with hints", "operations": [{"name": "bulkWrite", "arguments": {"requests": [{"name": "deleteMany", "arguments": {"filter": {"_id": {"$lt": 3}}, "hint": "_id_"}}, {"name": "deleteMany", "arguments": {"filter": {"_id": {"$gte": 4}}, "hint": {"_id": 1}}}], "options": {"ordered": true}}, "result": {"deletedCount": 3, "insertedCount": 0, "insertedIds": {}, "matchedCount": 0, "modifiedCount": 0, "upsertedCount": 0, "upsertedIds": {}}}], "expectations": [{"command_started_event": {"command": {"delete": "BulkWrite_delete_hint", "deletes": [{"q": {"_id": {"$lt": 3}}, "hint": "_id_", "limit": 0}, {"q": {"_id": {"$gte": 4}}, "hint": {"_id": 1}, "limit": 0}], "ordered": true}}}], "outcome": {"collection": {"data": [{"_id": 3, "x": 33}]}}}]}
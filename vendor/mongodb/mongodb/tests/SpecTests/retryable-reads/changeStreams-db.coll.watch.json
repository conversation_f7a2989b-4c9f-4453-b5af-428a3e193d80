{"runOn": [{"minServerVersion": "4.0", "topology": ["replicaset"]}, {"minServerVersion": "4.1.7", "topology": ["sharded"]}], "database_name": "retryable-reads-tests", "collection_name": "coll", "data": [{"_id": 1, "x": 11}], "tests": [{"description": "db.coll.watch succeeds on first attempt", "operations": [{"name": "watch", "object": "collection"}], "expectations": [{"command_started_event": {"command": {"aggregate": "coll", "cursor": {}, "pipeline": [{"$changeStream": {}}]}, "database_name": "retryable-reads-tests"}}]}, {"description": "db.coll.watch succeeds on second attempt", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["aggregate"], "closeConnection": true}}, "operations": [{"name": "watch", "object": "collection"}], "expectations": [{"command_started_event": {"command": {"aggregate": "coll", "cursor": {}, "pipeline": [{"$changeStream": {}}]}, "database_name": "retryable-reads-tests"}}, {"command_started_event": {"command": {"aggregate": "coll", "cursor": {}, "pipeline": [{"$changeStream": {}}]}, "database_name": "retryable-reads-tests"}}]}, {"description": "db.coll.watch fails on first attempt", "clientOptions": {"retryReads": false}, "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 1}, "data": {"failCommands": ["aggregate"], "closeConnection": true}}, "operations": [{"name": "watch", "object": "collection", "error": true}], "expectations": [{"command_started_event": {"command": {"aggregate": "coll", "cursor": {}, "pipeline": [{"$changeStream": {}}]}, "database_name": "retryable-reads-tests"}}]}, {"description": "db.coll.watch fails on second attempt", "failPoint": {"configureFailPoint": "failCommand", "mode": {"times": 2}, "data": {"failCommands": ["aggregate"], "closeConnection": true}}, "operations": [{"name": "watch", "object": "collection", "error": true}], "expectations": [{"command_started_event": {"command": {"aggregate": "coll", "cursor": {}, "pipeline": [{"$changeStream": {}}]}, "database_name": "retryable-reads-tests"}}, {"command_started_event": {"command": {"aggregate": "coll", "cursor": {}, "pipeline": [{"$changeStream": {}}]}, "database_name": "retryable-reads-tests"}}]}]}
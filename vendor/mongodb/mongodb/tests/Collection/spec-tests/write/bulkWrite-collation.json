{"data": [{"_id": 1, "x": 11}, {"_id": 2, "x": "ping"}, {"_id": 3, "x": "pINg"}, {"_id": 4, "x": "pong"}, {"_id": 5, "x": "pONg"}], "minServerVersion": "3.4", "tests": [{"description": "BulkWrite with delete operations and collation", "operation": {"name": "bulkWrite", "arguments": {"requests": [{"name": "deleteOne", "arguments": {"filter": {"x": "PING"}, "collation": {"locale": "en_US", "strength": 2}}}, {"name": "deleteOne", "arguments": {"filter": {"x": "PING"}, "collation": {"locale": "en_US", "strength": 2}}}, {"name": "deleteMany", "arguments": {"filter": {"x": "PONG"}, "collation": {"locale": "en_US", "strength": 2}}}], "options": {"ordered": true}}}, "outcome": {"result": {"deletedCount": 4, "insertedCount": 0, "insertedIds": {}, "matchedCount": 0, "modifiedCount": 0, "upsertedCount": 0, "upsertedIds": {}}, "collection": {"data": [{"_id": 1, "x": 11}]}}}, {"description": "BulkWrite with update operations and collation", "operation": {"name": "bulkWrite", "arguments": {"requests": [{"name": "updateMany", "arguments": {"filter": {"x": "ping"}, "update": {"$set": {"x": "PONG"}}, "collation": {"locale": "en_US", "strength": 3}}}, {"name": "updateOne", "arguments": {"filter": {"x": "ping"}, "update": {"$set": {"x": "PONG"}}, "collation": {"locale": "en_US", "strength": 2}}}, {"name": "replaceOne", "arguments": {"filter": {"x": "ping"}, "replacement": {"_id": 6, "x": "ping"}, "upsert": true, "collation": {"locale": "en_US", "strength": 3}}}, {"name": "updateMany", "arguments": {"filter": {"x": "pong"}, "update": {"$set": {"x": "PONG"}}, "collation": {"locale": "en_US", "strength": 2}}}], "options": {"ordered": true}}}, "outcome": {"result": {"deletedCount": 0, "insertedCount": 0, "insertedIds": {}, "matchedCount": 6, "modifiedCount": 4, "upsertedCount": 1, "upsertedIds": {"2": 6}}, "collection": {"data": [{"_id": 1, "x": 11}, {"_id": 2, "x": "PONG"}, {"_id": 3, "x": "PONG"}, {"_id": 4, "x": "PONG"}, {"_id": 5, "x": "PONG"}, {"_id": 6, "x": "ping"}]}}}]}
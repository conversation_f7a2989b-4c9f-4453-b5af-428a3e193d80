<?php return array(
    'root' => array(
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => '1bb89f45bf63bcb4004fcd4131674e9d3c7030e2',
        'name' => 'yun_shop/yun_shop',
        'dev' => false,
    ),
    'versions' => array(
        'adbario/php-dot-notation' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../adbario/php-dot-notation',
            'aliases' => array(),
            'reference' => 'eee4fc81296531e6aafba4c2bbccfc5adab1676e',
            'dev_requirement' => false,
        ),
        'alibabacloud/tea' => array(
            'pretty_version' => '3.1.23',
            'version' => '3.1.23.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../alibabacloud/tea',
            'aliases' => array(),
            'reference' => '61fce993274edf6e7131af07256ed7723d97a85f',
            'dev_requirement' => false,
        ),
        'alibabacloud/tea-fileform' => array(
            'pretty_version' => '0.3.4',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../alibabacloud/tea-fileform',
            'aliases' => array(),
            'reference' => '4bf0c75a045c8115aa8cb1a394bd08d8bb833181',
            'dev_requirement' => false,
        ),
        'alipaysdk/easysdk' => array(
            'pretty_version' => '2.2.1',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../alipaysdk/easysdk',
            'aliases' => array(),
            'reference' => '066388d02c6f55fe0919d75b386456d80801fec2',
            'dev_requirement' => false,
        ),
        'bacon/bacon-qr-code' => array(
            'pretty_version' => '1.0.3',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../bacon/bacon-qr-code',
            'aliases' => array(),
            'reference' => '5a91b62b9d37cee635bbf8d553f4546057250bee',
            'dev_requirement' => false,
        ),
        'composer/package-versions-deprecated' => array(
            'pretty_version' => '*********',
            'version' => '*********',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/./package-versions-deprecated',
            'aliases' => array(),
            'reference' => 'b4f54f74ef3453349c24a845d22392cd31e65f1d',
            'dev_requirement' => false,
        ),
        'danielstjules/stringy' => array(
            'pretty_version' => '3.1.0',
            'version' => '3.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../danielstjules/stringy',
            'aliases' => array(),
            'reference' => 'df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e',
            'dev_requirement' => false,
        ),
        'doctrine/annotations' => array(
            'pretty_version' => '1.13.2',
            'version' => '1.13.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/annotations',
            'aliases' => array(),
            'reference' => '5b668aef16090008790395c02c893b1ba13f7e08',
            'dev_requirement' => false,
        ),
        'doctrine/cache' => array(
            'pretty_version' => '1.12.1',
            'version' => '1.12.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/cache',
            'aliases' => array(),
            'reference' => '4cf401d14df219fa6f38b671f5493449151c9ad8',
            'dev_requirement' => false,
        ),
        'doctrine/collections' => array(
            'pretty_version' => '1.6.8',
            'version' => '1.6.8.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/collections',
            'aliases' => array(),
            'reference' => '1958a744696c6bb3bb0d28db2611dc11610e78af',
            'dev_requirement' => false,
        ),
        'doctrine/common' => array(
            'pretty_version' => 'v2.7.3',
            'version' => '2.7.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/common',
            'aliases' => array(),
            'reference' => '4acb8f89626baafede6ee5475bc5844096eba8a9',
            'dev_requirement' => false,
        ),
        'doctrine/dbal' => array(
            'pretty_version' => 'v2.5.12',
            'version' => '2.5.12.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/dbal',
            'aliases' => array(),
            'reference' => '7b9e911f9d8b30d43b96853dab26898c710d8f44',
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '1.4.4',
            'version' => '1.4.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'reference' => '4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9',
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'reference' => 'c268e882d4dbdd85e36e4ad69e02dc284f89d229',
            'dev_requirement' => false,
        ),
        'dragonmantank/cron-expression' => array(
            'pretty_version' => 'v2.3.1',
            'version' => '2.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dragonmantank/cron-expression',
            'aliases' => array(),
            'reference' => '65b2d8ee1f10915efb3b55597da3404f096acba2',
            'dev_requirement' => false,
        ),
        'easywechat-composer/easywechat-composer' => array(
            'pretty_version' => '1.4.1',
            'version' => '1.4.1.0',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../easywechat-composer/easywechat-composer',
            'aliases' => array(),
            'reference' => '3fc6a7ab6d3853c0f4e2922539b56cc37ef361cd',
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '2.1.25',
            'version' => '2.1.25.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'reference' => '0dbf5d78455d4d6a41d186da50adc1122ec066f4',
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.14.0',
            'version' => '4.14.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'reference' => '12ab42bd6e742c70c0a52f7b82477fcd44e64b75',
            'dev_requirement' => false,
        ),
        'fguillot/json-rpc' => array(
            'pretty_version' => 'v1.2.8',
            'version' => '1.2.8.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fguillot/json-rpc',
            'aliases' => array(),
            'reference' => 'f1eef90bf0bb3f7779c9c8113311811ef449ece8',
            'dev_requirement' => false,
        ),
        'gregwar/captcha' => array(
            'pretty_version' => 'v1.1.9',
            'version' => '1.1.9.0',
            'type' => 'captcha',
            'install_path' => __DIR__ . '/../gregwar/captcha',
            'aliases' => array(),
            'reference' => '4bb668e6b40e3205a020ca5ee4ca8cff8b8780c5',
            'dev_requirement' => false,
        ),
        'guzzlehttp/command' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/command',
            'aliases' => array(),
            'reference' => '2aaa2521a8f8269d6f5dfc13fe2af12c76921034',
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '6.5.5',
            'version' => '6.5.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'reference' => '9d4290de1cfd701f38099ef7e183b64b4b7b0c5e',
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle-services' => array(
            'pretty_version' => '1.1.3',
            'version' => '1.1.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle-services',
            'aliases' => array(),
            'reference' => '9e3abf20161cbf662d616cbb995f2811771759f7',
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '1.5.1',
            'version' => '1.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'reference' => 'fe752aedc9fd8fcca3fe7ad05d419d32998a06da',
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '1.8.3',
            'version' => '1.8.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'reference' => '1afdd860a2566ed3c2b0b4a3de6e23434a79ec85',
            'dev_requirement' => false,
        ),
        'hao-li/laravel-amount' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hao-li/laravel-amount',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'reference' => 'f1f9fcd0e669221288e821357878b4cc4441cffc',
            'dev_requirement' => false,
        ),
        'illuminate/auth' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/broadcasting' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/bus' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/cache' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/config' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/console' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/container' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/contracts' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/cookie' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/database' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/encryption' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/events' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/filesystem' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/hashing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/http' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/log' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/mail' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/notifications' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/pagination' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/pipeline' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/queue' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/redis' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/routing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/session' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/support' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/translation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/validation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'illuminate/view' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v6.19.1',
            ),
        ),
        'intervention/image' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/image',
            'aliases' => array(),
            'reference' => '744ebba495319501b873a4e48787759c72e3fb8c',
            'dev_requirement' => false,
        ),
        'iscms/alisms-for-laravel' => array(
            'pretty_version' => '0.0.3',
            'version' => '0.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../iscms/alisms-for-laravel',
            'aliases' => array(),
            'reference' => 'c713ea802993fb6dd5a0852e660cf7451ef98d39',
            'dev_requirement' => false,
        ),
        'ixudra/curl' => array(
            'pretty_version' => '6.22.0',
            'version' => '6.22.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ixudra/curl',
            'aliases' => array(),
            'reference' => '23cf5977a03a3ebcc41e979ef9e6ba87ae508f0e',
            'dev_requirement' => false,
        ),
        'james-heinrich/getid3' => array(
            'pretty_version' => 'v1.9.21',
            'version' => '1.9.21.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../james-heinrich/getid3',
            'aliases' => array(),
            'reference' => '36f5dabb1325415a4b07a401113f8db2eb81eca1',
            'dev_requirement' => false,
        ),
        'jean85/pretty-package-versions' => array(
            'pretty_version' => '1.6.0',
            'version' => '1.6.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jean85/pretty-package-versions',
            'aliases' => array(),
            'reference' => '1e0104b46f045868f11942aea058cd7186d6c303',
            'dev_requirement' => false,
        ),
        'jenssegers/mongodb' => array(
            'pretty_version' => 'v3.6.8',
            'version' => '3.6.8.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jenssegers/mongodb',
            'aliases' => array(),
            'reference' => '07c03110ed208720028f87c83e8c1fafca5d82d9',
            'dev_requirement' => false,
        ),
        'jeremeamia/superclosure' => array(
            'pretty_version' => '2.4.0',
            'version' => '2.4.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jeremeamia/superclosure',
            'aliases' => array(),
            'reference' => '5707d5821b30b9a07acfb4d76949784aaa0e9ce9',
            'dev_requirement' => false,
        ),
        'laracasts/flash' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laracasts/flash',
            'aliases' => array(),
            'reference' => 'f64a8d97f901fdb3ce8eb9b28c5308a726455467',
            'dev_requirement' => false,
        ),
        'laravel/framework' => array(
            'pretty_version' => 'v6.19.1',
            'version' => '6.19.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/framework',
            'aliases' => array(),
            'reference' => '7eb0b25daf58ec9374c59bd700949b3ea78d0276',
            'dev_requirement' => false,
        ),
        'laravel/helpers' => array(
            'pretty_version' => 'v1.5.0',
            'version' => '1.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/helpers',
            'aliases' => array(),
            'reference' => 'c28b0ccd799d58564c41a62395ac9511a1e72931',
            'dev_requirement' => false,
        ),
        'laravelcollective/html' => array(
            'pretty_version' => 'v6.3.0',
            'version' => '6.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravelcollective/html',
            'aliases' => array(),
            'reference' => '78c3cb516ac9e6d3d76cad9191f81d217302dea6',
            'dev_requirement' => false,
        ),
        'league/commonmark' => array(
            'pretty_version' => '1.6.7',
            'version' => '1.6.7.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/commonmark',
            'aliases' => array(),
            'reference' => '2b8185c13bc9578367a5bf901881d1c1b5bbd09b',
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '1.1.9',
            'version' => '1.1.9.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'reference' => '094defdb4a7001845300334e7c1ee2335925ef99',
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.10.0',
            'version' => '1.10.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'reference' => '3e4a35d756eedc67096f30240a68a3149120dae7',
            'dev_requirement' => false,
        ),
        'liebig/cron' => array(
            'pretty_version' => '1.3',
            'version' => '1.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../liebig/cron',
            'aliases' => array(),
            'reference' => '2a9806b40a063c873f067c00b77cb33fe117e705',
            'dev_requirement' => false,
        ),
        'maatwebsite/excel' => array(
            'pretty_version' => '3.1.37',
            'version' => '********',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maatwebsite/excel',
            'aliases' => array(),
            'reference' => '49ccd4142d3d7bce492d6bfb9dd9a27b12935408',
            'dev_requirement' => false,
        ),
        'maennchen/zipstream-php' => array(
            'pretty_version' => '2.1.0',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maennchen/zipstream-php',
            'aliases' => array(),
            'reference' => 'c4c5803cc1f93df3d2448478ef79394a5981cc58',
            'dev_requirement' => false,
        ),
        'markbaker/complex' => array(
            'pretty_version' => '3.0.1',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/complex',
            'aliases' => array(),
            'reference' => 'ab8bc271e404909db09ff2d5ffa1e538085c0f22',
            'dev_requirement' => false,
        ),
        'markbaker/matrix' => array(
            'pretty_version' => '3.0.0',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/matrix',
            'aliases' => array(),
            'reference' => 'c66aefcafb4f6c269510e9ac46b82619a904c576',
            'dev_requirement' => false,
        ),
        'mews/captcha' => array(
            'pretty_version' => '3.2.7',
            'version' => '3.2.7.0',
            'type' => 'package',
            'install_path' => __DIR__ . '/../mews/captcha',
            'aliases' => array(),
            'reference' => 'c4dec4963ea19a89aaf679fac1921323dd7decd8',
            'dev_requirement' => false,
        ),
        'mongodb/mongodb' => array(
            'pretty_version' => '1.8.0',
            'version' => '1.8.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mongodb/mongodb',
            'aliases' => array(),
            'reference' => '953dbc19443aa9314c44b7217a16873347e6840d',
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '1.27.0',
            'version' => '1.27.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'reference' => '52ebd235c1f7e0d5e1b16464b695a28335f8e44a',
            'dev_requirement' => false,
        ),
        'mtdowling/jmespath.php' => array(
            'pretty_version' => '2.6.1',
            'version' => '2.6.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mtdowling/jmespath.php',
            'aliases' => array(),
            'reference' => '9b87907a81b87bc76d19a7fb2d61e61486ee9edb',
            'dev_requirement' => false,
        ),
        'myclabs/php-enum' => array(
            'pretty_version' => '1.7.7',
            'version' => '1.7.7.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/php-enum',
            'aliases' => array(),
            'reference' => 'd178027d1e679832db9f38248fcc7200647dc2b7',
            'dev_requirement' => false,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '2.57.0',
            'version' => '2.57.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'reference' => '4a54375c21eea4811dbd1149fe6b246517554e78',
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v4.13.2',
            'version' => '4.13.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'reference' => '210577fe3cf7badcc5814d99455df46564f3c077',
            'dev_requirement' => false,
        ),
        'ocramius/package-versions' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '1.11.99',
            ),
        ),
        'opis/closure' => array(
            'pretty_version' => '3.6.3',
            'version' => '3.6.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../opis/closure',
            'aliases' => array(),
            'reference' => '3d81e4309d2a927abbe66df935f4bb60082805ad',
            'dev_requirement' => false,
        ),
        'orangehill/iseed' => array(
            'pretty_version' => 'v2.6.4',
            'version' => '2.6.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../orangehill/iseed',
            'aliases' => array(),
            'reference' => '03c73d3f829a74065e14a757f0d749ad4928a15f',
            'dev_requirement' => false,
        ),
        'overtrue/laravel-pinyin' => array(
            'pretty_version' => '3.0.5',
            'version' => '3.0.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../overtrue/laravel-pinyin',
            'aliases' => array(),
            'reference' => '4ca98a67cc2cd53ce98ee43dddbc5f5093cdbacc',
            'dev_requirement' => false,
        ),
        'overtrue/laravel-wechat' => array(
            'pretty_version' => '5.1.0',
            'version' => '5.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../overtrue/laravel-wechat',
            'aliases' => array(),
            'reference' => '1bc59aa52cf6bae2f4f388e9f20f7893305f2fe8',
            'dev_requirement' => false,
        ),
        'overtrue/pinyin' => array(
            'pretty_version' => '3.0.6',
            'version' => '3.0.6.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../overtrue/pinyin',
            'aliases' => array(),
            'reference' => '3b781d267197b74752daa32814d3a2cf5d140779',
            'dev_requirement' => false,
        ),
        'overtrue/socialite' => array(
            'pretty_version' => '2.0.24',
            'version' => '2.0.24.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../overtrue/socialite',
            'aliases' => array(),
            'reference' => 'ee7e7b000ec7d64f2b8aba1f6a2eec5cdf3f8bec',
            'dev_requirement' => false,
        ),
        'overtrue/wechat' => array(
            'pretty_version' => '4.5.0',
            'version' => '4.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../overtrue/wechat',
            'aliases' => array(),
            'reference' => '04a940f97d6812a67bb8d5f2dbaebf9ad78ae776',
            'dev_requirement' => false,
        ),
        'owen-oj/laravel-getid3' => array(
            'pretty_version' => 'v1.3',
            'version' => '1.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../owen-oj/laravel-getid3',
            'aliases' => array(),
            'reference' => '4c118fe8f7bc54104a634102a65bdc18c0ceac69',
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'dev_requirement' => false,
        ),
        'paypal/rest-api-sdk-php' => array(
            'pretty_version' => '1.13.0',
            'version' => '1.13.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paypal/rest-api-sdk-php',
            'aliases' => array(),
            'reference' => '192e217beed14c8e75624e821fdc8ec51e2a21f5',
            'dev_requirement' => false,
        ),
        'phpoffice/phpspreadsheet' => array(
            'pretty_version' => '1.19.0',
            'version' => '1.19.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpspreadsheet',
            'aliases' => array(),
            'reference' => 'a9ab55bfae02eecffb3df669a2e19ba0e2f04bbf',
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.8.1',
            'version' => '1.8.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'reference' => 'eab7a0df01fe2344d172bff4cd6dbd3f8b84ad15',
            'dev_requirement' => false,
        ),
        'phpxmlrpc/phpxmlrpc' => array(
            'pretty_version' => '4.0.0',
            'version' => '4.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpxmlrpc/phpxmlrpc',
            'aliases' => array(),
            'reference' => '679eacd661962f353a809ab66e83a86233f28bcc',
            'dev_requirement' => false,
        ),
        'pimple/pimple' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pimple/pimple',
            'aliases' => array(),
            'reference' => 'a94b3a4db7fb774b3d78dad2315ddc07629e1bed',
            'dev_requirement' => false,
        ),
        'predis/predis' => array(
            'pretty_version' => 'v1.1.10',
            'version' => '1.1.10.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../predis/predis',
            'aliases' => array(),
            'reference' => 'a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e',
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'reference' => 'd11b50ad223250cf17b86e38383413f5a6764bf8',
            'dev_requirement' => false,
        ),
        'psr/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '1.1.1',
            'version' => '1.1.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'reference' => '8622567409010282b7aeebe4bb841fe98b58dcaf',
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'reference' => '2dfb5f6c5eff0e91e20e913f8c5452ed95b86621',
            'dev_requirement' => false,
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'reference' => '12ac7fcd07e5b077433f5f2bee95b3a771bf61be',
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'reference' => 'f6561bf28d520154e4b0ec72be95418abe6d9363',
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0',
                1 => '1.0|2.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'reference' => '408d5eafb83c57f6365a3ca330ff23aa4a5fa39b',
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'qcloud/cos-sdk-v5' => array(
            'pretty_version' => 'v2.5.1',
            'version' => '2.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../qcloud/cos-sdk-v5',
            'aliases' => array(),
            'reference' => '85d5cb660574a3de6f4a83b76e8b0506e03b9e9a',
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '3.9.6',
            'version' => '3.9.6.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'reference' => 'ffa80ab953edd85d5b6c004f96181a538aad35a3',
            'dev_requirement' => false,
        ),
        'rap2hpoutre/laravel-log-viewer' => array(
            'pretty_version' => 'v1.7.0',
            'version' => '1.7.0.0',
            'type' => 'laravel-package',
            'install_path' => __DIR__ . '/../rap2hpoutre/laravel-log-viewer',
            'aliases' => array(),
            'reference' => '27392d29234b6ff38a456454558f4bcc40cc837a',
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.9.6',
            ),
        ),
        'simplesoftwareio/simple-qrcode' => array(
            'pretty_version' => '1.5.1',
            'version' => '1.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../simplesoftwareio/simple-qrcode',
            'aliases' => array(),
            'reference' => '0d8fbf73f7adc166ec5aabbf898b7327f6c69600',
            'dev_requirement' => false,
        ),
        'songshenzong/support' => array(
            'pretty_version' => '2.0.6',
            'version' => '2.0.6.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../songshenzong/support',
            'aliases' => array(),
            'reference' => 'b334d8abc99e8a85538a556e10c670c18b71c230',
            'dev_requirement' => false,
        ),
        'supervisorphp/supervisor' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../supervisorphp/supervisor',
            'aliases' => array(),
            'reference' => '41b52b9e74daffd58c4993b21d59c29515a3493e',
            'dev_requirement' => false,
        ),
        'swiftmailer/swiftmailer' => array(
            'pretty_version' => 'v6.3.0',
            'version' => '6.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../swiftmailer/swiftmailer',
            'aliases' => array(),
            'reference' => '8a5d5072dca8f48460fce2f4131fcc495eec654c',
            'dev_requirement' => false,
        ),
        'symfony/cache' => array(
            'pretty_version' => 'v5.1.10',
            'version' => '5.1.10.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache',
            'aliases' => array(),
            'reference' => 'f4faa7bfe3ca46891febf603291274047d3f62fe',
            'dev_requirement' => false,
        ),
        'symfony/cache-contracts' => array(
            'pretty_version' => 'v2.5.1',
            'version' => '2.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache-contracts',
            'aliases' => array(),
            'reference' => '64be4a7acb83b6f2bf6de9a02cee6dad41277ebc',
            'dev_requirement' => false,
        ),
        'symfony/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'symfony/console' => array(
            'pretty_version' => 'v4.4.40',
            'version' => '4.4.40.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'reference' => 'bdcc66f3140421038f495e5b50e3ca6ffa14c773',
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v5.4.3',
            'version' => '5.4.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'reference' => 'b0a190285cd95cb019237851205b8140ef6e368e',
            'dev_requirement' => false,
        ),
        'symfony/debug' => array(
            'pretty_version' => 'v4.4.37',
            'version' => '4.4.37.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/debug',
            'aliases' => array(),
            'reference' => '5de6c6e7f52b364840e53851c126be4d71e60470',
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v2.5.1',
            'version' => '2.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'reference' => 'e8b495ea28c1d97b5e0c121748d6f9b53d075c66',
            'dev_requirement' => false,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v4.4.40',
            'version' => '4.4.40.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'reference' => '2d0c9c229d995bef5e87fe4e83b717541832b448',
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v4.4.37',
            'version' => '4.4.37.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'reference' => '3ccfcfb96ecce1217d7b0875a0736976bc6e63dc',
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v1.1.12',
            'version' => '1.1.12.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'reference' => '1d5cd762abaa6b2a4169d3e77610193a7157129e',
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1',
            ),
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v4.4.37',
            'version' => '4.4.37.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'reference' => 'b17d76d7ed179f017aad646e858c90a2771af15d',
            'dev_requirement' => false,
        ),
        'symfony/http-client-contracts' => array(
            'pretty_version' => 'v2.5.1',
            'version' => '2.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-client-contracts',
            'aliases' => array(),
            'reference' => '1a4f708e4e87f335d1b1be6148060739152f0bd5',
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v4.4.39',
            'version' => '4.4.39.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'reference' => '60e8e42a4579551e5ec887d04380e2ab9e4cc314',
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v4.4.40',
            'version' => '4.4.40.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'reference' => '330a859a7ec9d7e7d82f2569b1c0700a26ffb1e3',
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v5.4.7',
            'version' => '5.4.7.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'reference' => '92d27a34dea2e199fa9b687e3fff3a7d169b7b1c',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'reference' => '30885182c981ab175d4d034db0f6f469898070ab',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-iconv' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-iconv',
            'aliases' => array(),
            'reference' => 'f1aed619e28cb077fc83fac8c4c0383578356e40',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'reference' => '749045c69efb97c70d25d7463abba812e91f3a44',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'reference' => '8590a5f561694770bdcd3f9b5c69dde6945028e8',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'reference' => '0abb51d2f102e00a4eefcf46ba7fec406d245825',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php56' => array(
            'pretty_version' => 'v1.20.0',
            'version' => '1.20.0.0',
            'type' => 'metapackage',
            'install_path' => NULL,
            'aliases' => array(),
            'reference' => '54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php72' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php72',
            'aliases' => array(),
            'reference' => '9a142215a36a3888e30d0a9eeea9766764e96976',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php73' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php73',
            'aliases' => array(),
            'reference' => 'cc5db0e22b3cb4111010e48785a97f670b350ca5',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.25.0',
            'version' => '1.25.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'reference' => '4407588e0d3f1f52efb65fbe92babe41f37fe50c',
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v4.4.40',
            'version' => '4.4.40.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'reference' => '54e9d763759268e07eb13b921d8631fc2816206f',
            'dev_requirement' => false,
        ),
        'symfony/psr-http-message-bridge' => array(
            'pretty_version' => 'v2.1.2',
            'version' => '2.1.2.0',
            'type' => 'symfony-bridge',
            'install_path' => __DIR__ . '/../symfony/psr-http-message-bridge',
            'aliases' => array(),
            'reference' => '22b37c8a3f6b5d94e9cdbd88e1270d96e2f97b34',
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v4.4.37',
            'version' => '4.4.37.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'reference' => '324f7f73b89cd30012575119430ccfb1dfbc24be',
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v2.5.1',
            'version' => '2.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'reference' => '24d9dc654b83e91aa59f9d167b131bc3b5bea24c',
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v4.4.37',
            'version' => '4.4.37.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'reference' => '4ce00d6875230b839f5feef82e51971f6c886e00',
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v2.5.1',
            'version' => '2.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'reference' => '1211df0afa701e45a04253110e959d4af4ef0f07',
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0',
            ),
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v4.4.39',
            'version' => '4.4.39.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'reference' => '35237c5e5dcb6593a46a860ba5b29c1d4683d80e',
            'dev_requirement' => false,
        ),
        'symfony/var-exporter' => array(
            'pretty_version' => 'v5.4.7',
            'version' => '5.4.7.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-exporter',
            'aliases' => array(),
            'reference' => '7eacaa588c9b27f2738575adb4a8457a80d9c807',
            'dev_requirement' => false,
        ),
        'tencentcloud/common' => array(
            'pretty_version' => '3.0.611',
            'version' => '3.0.611.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tencentcloud/common',
            'aliases' => array(),
            'reference' => '0ae91359567f7321e93b4bc234a21617f615227f',
            'dev_requirement' => false,
        ),
        'tencentcloud/live' => array(
            'pretty_version' => '3.0.611',
            'version' => '3.0.611.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tencentcloud/live',
            'aliases' => array(),
            'reference' => 'ae6abc4e33e026402542c5705d1c8e8f4cdb41c5',
            'dev_requirement' => false,
        ),
        'tijsverkoyen/css-to-inline-styles' => array(
            'pretty_version' => '2.2.4',
            'version' => '2.2.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tijsverkoyen/css-to-inline-styles',
            'aliases' => array(),
            'reference' => 'da444caae6aca7a19c0c140f68c6182e337d5b1c',
            'dev_requirement' => false,
        ),
        'toplan/laravel-sms' => array(
            'pretty_version' => '2.6.5',
            'version' => '2.6.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../toplan/laravel-sms',
            'aliases' => array(),
            'reference' => '9c8427493c768c07a15fb993436267b81f31bae5',
            'dev_requirement' => false,
        ),
        'toplan/phpsms' => array(
            'pretty_version' => '1.8.3',
            'version' => '1.8.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../toplan/phpsms',
            'aliases' => array(),
            'reference' => '863918ed0970d335471fb48c09f9f446a4daddbd',
            'dev_requirement' => false,
        ),
        'toplan/task-balancer' => array(
            'pretty_version' => '0.5.0',
            'version' => '0.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../toplan/task-balancer',
            'aliases' => array(),
            'reference' => '6ba16aafbf6888de0f0c53ea93dfff844b6ef27f',
            'dev_requirement' => false,
        ),
        'vierbergenlars/php-semver' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vierbergenlars/php-semver',
            'aliases' => array(),
            'reference' => 'be22b86be4c1133acc42fd1685276792024af5f9',
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v3.6.10',
            'version' => '3.6.10.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'reference' => '5b547cdb25825f10251370f57ba5d9d924e6f68e',
            'dev_requirement' => false,
        ),
        'watson/bootstrap-form' => array(
            'pretty_version' => '1.4.0',
            'version' => '1.4.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../watson/bootstrap-form',
            'aliases' => array(),
            'reference' => '6f52624175a8cb7891bb5d511af822529ebae9fb',
            'dev_requirement' => false,
        ),
        'wechatpay/wechatpay-guzzle-middleware' => array(
            'pretty_version' => '0.1.1',
            'version' => '0.1.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wechatpay/wechatpay-guzzle-middleware',
            'aliases' => array(),
            'reference' => '118602bf759e8b1891719f856b0c189bf1ef63de',
            'dev_requirement' => false,
        ),
        'workerman/workerman' => array(
            'pretty_version' => 'v4.0.33',
            'version' => '4.0.33.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../workerman/workerman',
            'aliases' => array(),
            'reference' => '7e7a95d38abf2a878438070aa8934029059d6866',
            'dev_requirement' => false,
        ),
        'xin/container' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../xin/container',
            'aliases' => array(),
            'reference' => '97bb67f87dd851545938a1f2fe0ffbd379e3ff81',
            'dev_requirement' => false,
        ),
        'xin/helper' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../xin/helper',
            'aliases' => array(),
            'reference' => '02a58132dae2aea2d1c0b8e66f55125969224747',
            'dev_requirement' => false,
        ),
        'yun_shop/yun_shop' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => '1bb89f45bf63bcb4004fcd4131674e9d3c7030e2',
            'dev_requirement' => false,
        ),
        'zgldh/laravel-upload-manager' => array(
            'pretty_version' => 'v0.6.0',
            'version' => '0.6.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../zgldh/laravel-upload-manager',
            'aliases' => array(),
            'reference' => 'd7d4d49e0f3249390487ebf05a9c37bbb1ee52dd',
            'dev_requirement' => false,
        ),
    ),
);

{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "4664cfb0da026216f99b63e24b8f4e3a", "packages": [{"name": "doctrine/inflector", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "9cf661f4eb38f7c881cac67c75ea9b00bf97b210"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/9cf661f4eb38f7c881cac67c75ea9b00bf97b210", "reference": "9cf661f4eb38f7c881cac67c75ea9b00bf97b210", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^7.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2020-05-29T15:13:26+00:00"}, {"name": "doctrine/lexer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/e864bbf5904cb8f5bb334f99209b48018522f042", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2020-05-25T17:44:05+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/65b2d8ee1f10915efb3b55597da3404f096acba2", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2", "shasum": ""}, "require": {"php": "^7.0|^8.0"}, "require-dev": {"phpunit/phpunit": "^6.4|^7.0|^8.0|^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v2.3.1"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2020-10-13T00:52:37+00:00"}, {"name": "egulias/email-validator", "version": "2.1.25", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/0dbf5d78455d4d6a41d186da50adc1122ec066f4", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.25"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2020-12-29T14:50:06+00:00"}, {"name": "james-hein<PERSON>/getid3", "version": "v1.9.20", "source": {"type": "git", "url": "https://github.com/JamesHeinrich/getID3.git", "reference": "3c15e353b9bb1252201c73394bb8390b573a751d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JamesHeinrich/getID3/zipball/3c15e353b9bb1252201c73394bb8390b573a751d", "reference": "3c15e353b9bb1252201c73394bb8390b573a751d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"jakub-onderka/php-parallel-lint": "^0.9 || ^1.0"}, "suggest": {"ext-SimpleXML": "SimpleXML extension is required to analyze RIFF/WAV/BWF audio files (also requires `ext-libxml`).", "ext-com_dotnet": "COM extension is required when loading files larger than 2GB on Windows.", "ext-ctype": "ctype extension is required when loading files larger than 2GB on 32-bit PHP (also on 64-bit PHP on Windows) or executing `getid3_lib::CopyTagsToComments`.", "ext-dba": "DBA extension is required to use the DBA database as a cache storage.", "ext-exif": "EXIF extension is required for graphic modules.", "ext-iconv": "iconv extension is required to work with different character sets (when `ext-mbstring` is not available).", "ext-json": "JSON extension is required to analyze Apple Quicktime videos.", "ext-libxml": "libxml extension is required to analyze RIFF/WAV/BWF audio files.", "ext-mbstring": "mbstring extension is required to work with different character sets.", "ext-mysql": "MySQL extension is required to use the MySQL database as a cache storage (deprecated in PHP 5.5, removed in PHP >= 7.0, use `ext-mysqli` instead).", "ext-mysqli": "MySQLi extension is required to use the MySQL database as a cache storage.", "ext-rar": "RAR extension is required for RAR archive module.", "ext-sqlite3": "SQLite3 extension is required to use the SQLite3 database as a cache storage.", "ext-xml": "XML extension is required for graphic modules to analyze the XML metadata.", "ext-zlib": "Zlib extension is required for archive modules and compressed metadata."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"classmap": ["getid3/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-1.0-or-later", "LGPL-3.0-only", "MPL-2.0"], "description": "PHP script that extracts useful information from popular multimedia file formats", "homepage": "https://www.getid3.org/", "keywords": ["codecs", "php", "tags"], "support": {"issues": "https://github.com/JamesHeinrich/getID3/issues", "source": "https://github.com/JamesHeinrich/getID3/tree/master"}, "time": "2020-06-30T18:43:34+00:00"}, {"name": "laravel/framework", "version": "v6.20.13", "source": {"type": "git", "url": "https://github.com/laravel/framework.git", "reference": "12ed06da14e3fe4257cb1ece91c2d3439cc2d34b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/framework/zipball/12ed06da14e3fe4257cb1ece91c2d3439cc2d34b", "reference": "12ed06da14e3fe4257cb1ece91c2d3439cc2d34b", "shasum": ""}, "require": {"doctrine/inflector": "^1.4|^2.0", "dragonmantank/cron-expression": "^2.3.1", "egulias/email-validator": "^2.1.10", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "league/commonmark": "^1.3", "league/flysystem": "^1.1", "monolog/monolog": "^1.12|^2.0", "nesbot/carbon": "^2.31", "opis/closure": "^3.6", "php": "^7.2.5|^8.0", "psr/container": "^1.0", "psr/simple-cache": "^1.0", "ramsey/uuid": "^3.7", "swiftmailer/swiftmailer": "^6.0", "symfony/console": "^4.3.4", "symfony/debug": "^4.3.4", "symfony/finder": "^4.3.4", "symfony/http-foundation": "^4.3.4", "symfony/http-kernel": "^4.3.4", "symfony/polyfill-php73": "^1.17", "symfony/process": "^4.3.4", "symfony/routing": "^4.3.4", "symfony/var-dumper": "^4.3.4", "tijsverkoyen/css-to-inline-styles": "^2.2.1", "vlucas/phpdotenv": "^3.3"}, "conflict": {"tightenco/collect": "<5.5.33"}, "replace": {"illuminate/auth": "self.version", "illuminate/broadcasting": "self.version", "illuminate/bus": "self.version", "illuminate/cache": "self.version", "illuminate/config": "self.version", "illuminate/console": "self.version", "illuminate/container": "self.version", "illuminate/contracts": "self.version", "illuminate/cookie": "self.version", "illuminate/database": "self.version", "illuminate/encryption": "self.version", "illuminate/events": "self.version", "illuminate/filesystem": "self.version", "illuminate/hashing": "self.version", "illuminate/http": "self.version", "illuminate/log": "self.version", "illuminate/mail": "self.version", "illuminate/notifications": "self.version", "illuminate/pagination": "self.version", "illuminate/pipeline": "self.version", "illuminate/queue": "self.version", "illuminate/redis": "self.version", "illuminate/routing": "self.version", "illuminate/session": "self.version", "illuminate/support": "self.version", "illuminate/translation": "self.version", "illuminate/validation": "self.version", "illuminate/view": "self.version"}, "require-dev": {"aws/aws-sdk-php": "^3.155", "doctrine/dbal": "^2.6", "filp/whoops": "^2.8", "guzzlehttp/guzzle": "^6.3.1|^7.0.1", "league/flysystem-cached-adapter": "^1.0", "mockery/mockery": "~1.3.3|^1.4.2", "moontoast/math": "^1.1", "orchestra/testbench-core": "^4.8", "pda/pheanstalk": "^4.0", "phpunit/phpunit": "^7.5.15|^8.4|^9.3.3", "predis/predis": "^1.1.1", "symfony/cache": "^4.3.4"}, "suggest": {"aws/aws-sdk-php": "Required to use the SQS queue driver, DynamoDb failed job storage and SES mail driver (^3.155).", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.6).", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().", "ext-memcached": "Required to use the memcache cache driver.", "ext-pcntl": "Required to use all features of the queue worker.", "ext-posix": "Required to use all features of the queue worker.", "ext-redis": "Required to use the Redis cache and queue drivers (^4.0|^5.0).", "fakerphp/faker": "Required to use the eloquent factory builder (^1.9.1).", "filp/whoops": "Required for friendly error pages in development (^2.8).", "guzzlehttp/guzzle": "Required to use the Mailgun mail driver and the ping methods on schedules (^6.3.1|^7.0.1).", "laravel/tinker": "Required to use the tinker console command (^2.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^1.0).", "league/flysystem-cached-adapter": "Required to use the Flysystem cache (^1.0).", "league/flysystem-sftp": "Required to use the Flysystem SFTP driver (^1.0).", "moontoast/math": "Required to use ordered UUIDs (^1.1).", "nyholm/psr7": "Required to use PSR-7 bridging features (^1.2).", "pda/pheanstalk": "Required to use the beanstalk queue driver (^4.0).", "predis/predis": "Required to use the predis connector (^1.1.2).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^4.0).", "symfony/cache": "Required to PSR-6 cache bridge (^4.3.4).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^1.2).", "wildbit/swiftmailer-postmark": "Required to use Postmark mail driver (^3.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "autoload": {"files": ["src/Illuminate/Foundation/helpers.php", "src/Illuminate/Support/helpers.php"], "psr-4": {"Illuminate\\": "src/Illuminate/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Framework.", "homepage": "https://laravel.com", "keywords": ["framework", "laravel"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2021-01-19T13:41:17+00:00"}, {"name": "league/commonmark", "version": "1.5.7", "source": {"type": "git", "url": "https://github.com/thephpleague/commonmark.git", "reference": "11df9b36fd4f1d2b727a73bf14931d81373b9a54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/commonmark/zipball/11df9b36fd4f1d2b727a73bf14931d81373b9a54", "reference": "11df9b36fd4f1d2b727a73bf14931d81373b9a54", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^8.0"}, "conflict": {"scrutinizer/ocular": "1.7.*"}, "require-dev": {"cebe/markdown": "~1.0", "commonmark/commonmark.js": "0.29.2", "erusev/parsedown": "~1.0", "ext-json": "*", "github/gfm": "0.29.0", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.4", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.2", "scrutinizer/ocular": "^1.5", "symfony/finder": "^4.2"}, "bin": ["bin/commonmark"], "type": "library", "autoload": {"psr-4": {"League\\CommonMark\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and Github-Flavored Markdown (GFM)", "homepage": "https://commonmark.thephpleague.com", "keywords": ["commonmark", "flavored", "gfm", "github", "github-flavored", "markdown", "md", "parser"], "support": {"docs": "https://commonmark.thephpleague.com/", "issues": "https://github.com/thephpleague/commonmark/issues", "rss": "https://github.com/thephpleague/commonmark/releases.atom", "source": "https://github.com/thephpleague/commonmark"}, "funding": [{"url": "https://enjoy.gitstore.app/repositories/thephpleague/commonmark", "type": "custom"}, {"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://www.patreon.com/colinodell", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "time": "2020-10-31T13:49:32+00:00"}, {"name": "league/flysystem", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "9be3b16c877d477357c015cec057548cf9b2a14a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/9be3b16c877d477357c015cec057548cf9b2a14a", "reference": "9be3b16c877d477357c015cec057548cf9b2a14a", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-fileinfo": "Required for MimeType", "ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.x"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2020-08-23T07:39:11+00:00"}, {"name": "league/mime-type-detection", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "3b9dff8aaf7323590c1d2e443db701eb1f9aa0d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/3b9dff8aaf7323590c1d2e443db701eb1f9aa0d3", "reference": "3b9dff8aaf7323590c1d2e443db701eb1f9aa0d3", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.18", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.7.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2021-01-18T20:58:21+00:00"}, {"name": "monolog/monolog", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "1cb1cde8e8dd0f70cc0fe51354a59acad9302084"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/1cb1cde8e8dd0f70cc0fe51354a59acad9302084", "reference": "1cb1cde8e8dd0f70cc0fe51354a59acad9302084", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "graylog2/gelf-php": "^1.4.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90 <7.0.1", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.2.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2020-12-14T13:15:25+00:00"}, {"name": "nesbot/carbon", "version": "2.43.0", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "d32c57d8389113742f4a88725a170236470012e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/d32c57d8389113742f4a88725a170236470012e2", "reference": "d32c57d8389113742f4a88725a170236470012e2", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1.8 || ^8.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^3.4 || ^4.0 || ^5.0"}, "require-dev": {"doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.54", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev", "dev-3.x": "3.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}, {"name": "kylekatarnls", "homepage": "http://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2020-12-17T20:55:32+00:00"}, {"name": "opis/closure", "version": "3.6.1", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "943b5d70cc5ae7483f6aff6ff43d7e34592ca0f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/943b5d70cc5ae7483f6aff6ff43d7e34592ca0f5", "reference": "943b5d70cc5ae7483f6aff6ff43d7e34592ca0f5", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6.x-dev"}}, "autoload": {"psr-4": {"Opis\\Closure\\": "src/"}, "files": ["functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "support": {"issues": "https://github.com/opis/closure/issues", "source": "https://github.com/opis/closure/tree/3.6.1"}, "time": "2020-11-07T02:01:34+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.99", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "shasum": ""}, "require": {"php": "^7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2018-07-02T15:55:56+00:00"}, {"name": "phpoption/phpoption", "version": "1.7.5", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "994ecccd8f3283ecf5ac33254543eb0ac946d525"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/994ecccd8f3283ecf5ac33254543eb0ac946d525", "reference": "994ecccd8f3283ecf5ac33254543eb0ac946d525", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^4.8.35 || ^5.7.27 || ^6.5.6 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.7.5"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2020-07-20T17:29:33+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/master"}, "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/log", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.3"}, "time": "2020-03-23T09:12:05+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "ramsey/uuid", "version": "3.9.3", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "7e1633a6964b48589b142d60542f9ed31bd37a92"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/7e1633a6964b48589b142d60542f9ed31bd37a92", "reference": "7e1633a6964b48589b142d60542f9ed31bd37a92", "shasum": ""}, "require": {"ext-json": "*", "paragonie/random_compat": "^1 | ^2 | 9.99.99", "php": "^5.4 | ^7 | ^8", "symfony/polyfill-ctype": "^1.8"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"codeception/aspect-mock": "^1 | ^2", "doctrine/annotations": "^1.2", "goaop/framework": "1.0.0-alpha.2 | ^1 | ^2.1", "jakub-onderka/php-parallel-lint": "^1", "mockery/mockery": "^0.9.11 | ^1", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock-phpunit": "^0.3 | ^1.1", "phpunit/phpunit": "^4.8 | ^5.4 | ^6.5", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-ctype": "Provides support for PHP Ctype functions", "ext-libsodium": "Provides the PECL libsodium extension for use with the SodiumRandomGenerator", "ext-openssl": "Provides the OpenSSL extension for use with the OpenSslGenerator", "ext-uuid": "Provides the PECL UUID extension for use with the PeclUuidTimeGenerator and PeclUuidRandomGenerator", "moontoast/math": "Provides support for converting UUID to 128-bit integer (in string form).", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-console": "A console application for generating UUIDs with ramsey/uuid", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Ramsey\\Uuid\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Formerly rhumsaa/uuid. A PHP 5.4+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "homepage": "https://github.com/ramsey/uuid", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "rss": "https://github.com/ramsey/uuid/releases.atom", "source": "https://github.com/ramsey/uuid", "wiki": "https://github.com/ramsey/uuid/wiki"}, "time": "2020-02-21T04:36:14+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.2.5", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "698a6a9f54d7eb321274de3ad19863802c879fb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/698a6a9f54d7eb321274de3ad19863802c879fb7", "reference": "698a6a9f54d7eb321274de3ad19863802c879fb7", "shasum": ""}, "require": {"egulias/email-validator": "^2.0", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.0"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v6.2.5"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "time": "2021-01-12T09:35:59+00:00"}, {"name": "symfony/console", "version": "v4.4.18", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "12e071278e396cc3e1c149857337e9e192deca0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/12e071278e396cc3e1c149857337e9e192deca0b", "reference": "12e071278e396cc3e1c149857337e9e192deca0b", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3|>=5", "symfony/lock": "<4.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^4.3", "symfony/lock": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^4.3|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/console/tree/v4.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-18T07:41:31+00:00"}, {"name": "symfony/css-selector", "version": "v5.2.1", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "f789e7ead4c79e04ca9a6d6162fc629c89bd8054"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/f789e7ead4c79e04ca9a6d6162fc629c89bd8054", "reference": "f789e7ead4c79e04ca9a6d6162fc629c89bd8054", "shasum": ""}, "require": {"php": ">=7.2.5"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony CssSelector Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.2.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-08T17:02:38+00:00"}, {"name": "symfony/debug", "version": "v4.4.18", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "5dfc7825f3bfe9bb74b23d8b8ce0e0894e32b544"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/5dfc7825f3bfe9bb74b23d8b8ce0e0894e32b544", "reference": "5dfc7825f3bfe9bb74b23d8b8ce0e0894e32b544", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "~1.0", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/http-kernel": "<3.4"}, "require-dev": {"symfony/http-kernel": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/debug/tree/v4.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-10T16:34:26+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "5fa56b4074d1ae755beb55617ddafe6f5d78f665"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/5fa56b4074d1ae755beb55617ddafe6f5d78f665", "reference": "5fa56b4074d1ae755beb55617ddafe6f5d78f665", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/master"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-07T11:33:47+00:00"}, {"name": "symfony/error-handler", "version": "v4.4.18", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "ef2f7ddd3b9177bbf8ff2ecd8d0e970ed48da0c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/ef2f7ddd3b9177bbf8ff2ecd8d0e970ed48da0c3", "reference": "ef2f7ddd3b9177bbf8ff2ecd8d0e970ed48da0c3", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "~1.0", "symfony/debug": "^4.4.5", "symfony/polyfill-php80": "^1.15", "symfony/var-dumper": "^4.4|^5.0"}, "require-dev": {"symfony/http-kernel": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ErrorHandler Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v4.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-09T11:15:38+00:00"}, {"name": "symfony/event-dispatcher", "version": "v4.4.18", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "5d4c874b0eb1c32d40328a09dbc37307a5a910b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/5d4c874b0eb1c32d40328a09dbc37307a5a910b0", "reference": "5d4c874b0eb1c32d40328a09dbc37307a5a910b0", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/event-dispatcher-contracts": "^1.1"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "~3.4|~4.4", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-18T07:41:31+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.1.9", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "84e23fdcd2517bf37aecbd16967e83f0caee25a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/84e23fdcd2517bf37aecbd16967e83f0caee25a7", "reference": "84e23fdcd2517bf37aecbd16967e83f0caee25a7", "shasum": ""}, "require": {"php": ">=7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.9"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-07-06T13:19:58+00:00"}, {"name": "symfony/finder", "version": "v4.4.18", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "ebd0965f2dc2d4e0f11487c16fbb041e50b5c09b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/ebd0965f2dc2d4e0f11487c16fbb041e50b5c09b", "reference": "ebd0965f2dc2d4e0f11487c16fbb041e50b5c09b", "shasum": ""}, "require": {"php": ">=7.1.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v4.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-08T16:59:59+00:00"}, {"name": "symfony/http-client-contracts", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "41db680a15018f9c1d4b23516059633ce280ca33"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/41db680a15018f9c1d4b23516059633ce280ca33", "reference": "41db680a15018f9c1d4b23516059633ce280ca33", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-version": "2.3", "branch-alias": {"dev-main": "2.3-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-14T17:08:19+00:00"}, {"name": "symfony/http-foundation", "version": "v4.4.18", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "5ebda66b51612516bf338d5f87da2f37ff74cf34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/5ebda66b51612516bf338d5f87da2f37ff74cf34", "reference": "5ebda66b51612516bf338d5f87da2f37ff74cf34", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.15"}, "require-dev": {"predis/predis": "~1.0", "symfony/expression-language": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v4.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-18T07:41:31+00:00"}, {"name": "symfony/http-kernel", "version": "v4.4.18", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "eaff9a43e74513508867ecfa66ef94fbb96ab128"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/eaff9a43e74513508867ecfa66ef94fbb96ab128", "reference": "eaff9a43e74513508867ecfa66ef94fbb96ab128", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "~1.0", "symfony/error-handler": "^4.4", "symfony/event-dispatcher": "^4.4", "symfony/http-client-contracts": "^1.1|^2", "symfony/http-foundation": "^4.4|^5.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/browser-kit": "<4.3", "symfony/config": "<3.4", "symfony/console": ">=5", "symfony/dependency-injection": "<4.3", "symfony/translation": "<4.2", "twig/twig": "<1.34|<2.4,>=2"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "~1.0", "symfony/browser-kit": "^4.3|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^1.34|^2.4|^3.0"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpKernel Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v4.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-18T13:32:33+00:00"}, {"name": "symfony/mime", "version": "v5.2.1", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "de97005aef7426ba008c46ba840fc301df577ada"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/de97005aef7426ba008c46ba840fc301df577ada", "reference": "de97005aef7426ba008c46ba840fc301df577ada", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/property-access": "^4.4|^5.1", "symfony/property-info": "^4.4|^5.1", "symfony/serializer": "^5.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A library to manipulate MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v5.2.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-09T18:54:12+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.22.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "c6c942b1ac76c82448322025e084cadc56048b4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/c6c942b1ac76c82448322025e084cadc56048b4e", "reference": "c6c942b1ac76c82448322025e084cadc56048b4e", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.22.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.22.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "b34bfb8c4c22650ac080d2662ae3502e5f2f4ae6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/b34bfb8c4c22650ac080d2662ae3502e5f2f4ae6", "reference": "b34bfb8c4c22650ac080d2662ae3502e5f2f4ae6", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.22.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.22.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "0eb8293dbbcd6ef6bf81404c9ce7d95bcdf34f44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/0eb8293dbbcd6ef6bf81404c9ce7d95bcdf34f44", "reference": "0eb8293dbbcd6ef6bf81404c9ce7d95bcdf34f44", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.22.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.22.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "6e971c891537eb617a00bb07a43d182a6915faba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/6e971c891537eb617a00bb07a43d182a6915faba", "reference": "6e971c891537eb617a00bb07a43d182a6915faba", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.22.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T17:09:11+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.22.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "f377a3dd1fde44d37b9831d68dc8dea3ffd28e13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/f377a3dd1fde44d37b9831d68dc8dea3ffd28e13", "reference": "f377a3dd1fde44d37b9831d68dc8dea3ffd28e13", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.22.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.22.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "cc6e6f9b39fe8075b3dabfbaf5b5f645ae1340c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/cc6e6f9b39fe8075b3dabfbaf5b5f645ae1340c9", "reference": "cc6e6f9b39fe8075b3dabfbaf5b5f645ae1340c9", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.22.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.22.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "a678b42e92f86eca04b7fa4c0f6f19d097fb69e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/a678b42e92f86eca04b7fa4c0f6f19d097fb69e2", "reference": "a678b42e92f86eca04b7fa4c0f6f19d097fb69e2", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.22.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.22.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "dc3063ba22c2a1fd2f45ed856374d79114998f91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/dc3063ba22c2a1fd2f45ed856374d79114998f91", "reference": "dc3063ba22c2a1fd2f45ed856374d79114998f91", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.22.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/process", "version": "v4.4.18", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "075316ff72233ce3d04a9743414292e834f2cb4a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/075316ff72233ce3d04a9743414292e834f2cb4a", "reference": "075316ff72233ce3d04a9743414292e834f2cb4a", "shasum": ""}, "require": {"php": ">=7.1.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v4.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-08T16:59:59+00:00"}, {"name": "symfony/routing", "version": "v4.4.18", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "80b042c20b035818daec844723e23b9825134ba0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/80b042c20b035818daec844723e23b9825134ba0", "reference": "80b042c20b035818daec844723e23b9825134ba0", "shasum": ""}, "require": {"php": ">=7.1.3"}, "conflict": {"symfony/config": "<4.2", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.2", "psr/log": "~1.0", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Routing Component", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v4.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-08T16:59:59+00:00"}, {"name": "symfony/service-contracts", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "d15da7ba4957ffb8f1747218be9e1a121fd298a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/d15da7ba4957ffb8f1747218be9e1a121fd298a1", "reference": "d15da7ba4957ffb8f1747218be9e1a121fd298a1", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.0"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/master"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-07T11:33:47+00:00"}, {"name": "symfony/translation", "version": "v4.4.18", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "c1001b7d75b3136648f94b245588209d881c6939"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/c1001b7d75b3136648f94b245588209d881c6939", "reference": "c1001b7d75b3136648f94b245588209d881c6939", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v4.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-08T16:59:59+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "e2eaa60b558f26a4b0354e1bbb25636efaaad105"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/e2eaa60b558f26a4b0354e1bbb25636efaaad105", "reference": "e2eaa60b558f26a4b0354e1bbb25636efaaad105", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-28T13:05:58+00:00"}, {"name": "symfony/var-dumper", "version": "v4.4.18", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "4f31364bbc8177f2a6dbc125ac3851634ebe2a03"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/4f31364bbc8177f2a6dbc125ac3851634ebe2a03", "reference": "4f31364bbc8177f2a6dbc125ac3851634ebe2a03", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5", "symfony/polyfill-php80": "^1.15"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^3.4|^4.0|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^1.34|^2.4|^3.0"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v4.4.18"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-08T16:59:59+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "2.2.3", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "b43b05cf43c1b6d849478965062b6ef73e223bb5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/b43b05cf43c1b6d849478965062b6ef73e223bb5", "reference": "b43b05cf43c1b6d849478965062b6ef73e223bb5", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/2.2.3"}, "time": "2020-07-13T06:12:54+00:00"}, {"name": "vlucas/phpdotenv", "version": "v3.6.8", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "5e679f7616db829358341e2d5cccbd18773bdab8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/5e679f7616db829358341e2d5cccbd18773bdab8", "reference": "5e679f7616db829358341e2d5cccbd18773bdab8", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "phpoption/phpoption": "^1.5.2", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gjcampbell.co.uk/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://vancelucas.com/"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v3.6.8"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2021-01-20T14:39:46+00:00"}], "packages-dev": [{"name": "doctrine/instantiator", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/d56bf6102915de5702778fe20f2de3b2fe570b5b", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13 || 1.0.0-alpha2", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.4.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2020-11-10T18:47:58+00:00"}, {"name": "fakerphp/faker", "version": "v1.13.0", "source": {"type": "git", "url": "https://github.com/FakerPHP/Faker.git", "reference": "ab3f5364d01f2c2c16113442fb987d26e4004913"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FakerPHP/Faker/zipball/ab3f5364d01f2c2c16113442fb987d26e4004913", "reference": "ab3f5364d01f2c2c16113442fb987d26e4004913", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"fzaninotto/faker": "*"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "ext-intl": "*", "phpunit/phpunit": "^7.5.20 || ^8.5.8 || ^9.4.2"}, "type": "library", "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.13.0"}, "time": "2020-12-18T16:50:48+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "mockery/mockery", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "20cab678faed06fac225193be281ea0fddb43b93"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/20cab678faed06fac225193be281ea0fddb43b93", "reference": "20cab678faed06fac225193be281ea0fddb43b93", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": "^7.3 || ^8.0"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-0": {"Mockery": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/master"}, "time": "2020-08-11T18:10:13+00:00"}, {"name": "myclabs/deep-copy", "version": "1.10.2", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/776f831124e9c62e1a2c601ecc52e776d8bb7220", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.10.2"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2020-11-13T09:40:50+00:00"}, {"name": "orchestra/testbench", "version": "v4.14.0", "source": {"type": "git", "url": "https://github.com/orchestral/testbench.git", "reference": "36823e88681ffcea17917005030781024ede8920"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/orchestral/testbench/zipball/36823e88681ffcea17917005030781024ede8920", "reference": "36823e88681ffcea17917005030781024ede8920", "shasum": ""}, "require": {"laravel/framework": "^6.20.12", "mockery/mockery": "^1.3.3 || ^1.4.2", "orchestra/testbench-core": "^4.13", "php": ">=7.2 || >=8.0", "phpunit/phpunit": "^8.3 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/crynobone"}], "description": "Laravel Testing Helper for Packages Development", "homepage": "http://orchestraplatform.com/docs/latest/components/testbench/", "keywords": ["BDD", "TDD", "laravel", "orchestra-platform", "orchestral", "testing"], "support": {"issues": "https://github.com/orchestral/testbench/issues", "source": "https://github.com/orchestral/testbench/tree/v4.14.0"}, "funding": [{"url": "https://paypal.me/crynobone", "type": "custom"}, {"url": "https://liberapay.com/crynobone", "type": "liberapay"}], "time": "2021-01-17T08:11:39+00:00"}, {"name": "orchestra/testbench-core", "version": "v4.13.0", "source": {"type": "git", "url": "https://github.com/orchestral/testbench-core.git", "reference": "01e093a4295b5ae48933d60f781c6a2c160a071c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/orchestral/testbench-core/zipball/01e093a4295b5ae48933d60f781c6a2c160a071c", "reference": "01e093a4295b5ae48933d60f781c6a2c160a071c", "shasum": ""}, "require": {"fakerphp/faker": "^1.9.1", "php": ">=7.2 || >=8.0"}, "require-dev": {"laravel/framework": "^6.20.12", "laravel/laravel": "6.x-dev", "mockery/mockery": "^1.3.3 || ^1.4.2", "phpunit/phpunit": "^8.3 || ^9.0"}, "suggest": {"laravel/framework": "Required for testing (^6.20.12).", "mockery/mockery": "Allow using <PERSON><PERSON><PERSON> for testing (^1.3.2).", "orchestra/testbench-browser-kit": "Allow using legacy Laravel BrowserKit for testing (^4.0).", "orchestra/testbench-dusk": "Allow using <PERSON>vel <PERSON> for testing (^4.0).", "phpunit/phpunit": "Allow using PHPUnit for testing (^8.3)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"psr-4": {"Orchestra\\Testbench\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/crynobone"}], "description": "Testing Helper for Laravel Development", "homepage": "http://orchestraplatform.com/docs/latest/components/testbench/", "keywords": ["BDD", "TDD", "laravel", "orchestra-platform", "orchestral", "testing"], "support": {"issues": "https://github.com/orchestral/testbench/issues", "source": "https://github.com/orchestral/testbench-core"}, "funding": [{"url": "https://paypal.me/crynobone", "type": "custom"}, {"url": "https://liberapay.com/crynobone", "type": "liberapay"}], "time": "2021-01-17T07:53:53+00:00"}, {"name": "phar-io/manifest", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "85265efd3af7ba3ca4b2a2c34dbfc5788dd29133"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/85265efd3af7ba3ca4b2a2c34dbfc5788dd29133", "reference": "85265efd3af7ba3ca4b2a2c34dbfc5788dd29133", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/master"}, "time": "2020-06-27T14:33:11+00:00"}, {"name": "phar-io/version", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "e4782611070e50613683d2b9a57730e9a3ba5451"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/e4782611070e50613683d2b9a57730e9a3ba5451", "reference": "e4782611070e50613683d2b9a57730e9a3ba5451", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.0.4"}, "time": "2020-12-13T23:18:30+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.2.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "069a785b2141f5bcf49f3e353548dc1cce6df556"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/069a785b2141f5bcf49f3e353548dc1cce6df556", "reference": "069a785b2141f5bcf49f3e353548dc1cce6df556", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/master"}, "time": "2020-09-03T19:13:55+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0", "reference": "6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.4.0"}, "time": "2020-09-17T18:55:26+00:00"}, {"name": "phpspec/prophecy", "version": "1.12.2", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "245710e971a030f42e08f4912863805570f23d39"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/245710e971a030f42e08f4912863805570f23d39", "reference": "245710e971a030f42e08f4912863805570f23d39", "shasum": ""}, "require": {"doctrine/instantiator": "^1.2", "php": "^7.2 || ~8.0, <8.1", "phpdocumentor/reflection-docblock": "^5.2", "sebastian/comparator": "^3.0 || ^4.0", "sebastian/recursion-context": "^3.0 || ^4.0"}, "require-dev": {"phpspec/phpspec": "^6.0", "phpunit/phpunit": "^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/1.12.2"}, "time": "2020-12-19T10:15:11+00:00"}, {"name": "phpunit/php-code-coverage", "version": "7.0.14", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "bb7c9a210c72e4709cdde67f8b7362f672f2225c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/bb7c9a210c72e4709cdde67f8b7362f672f2225c", "reference": "bb7c9a210c72e4709cdde67f8b7362f672f2225c", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": ">=7.2", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.1.1 || ^4.0", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.2.2", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1.3"}, "require-dev": {"phpunit/phpunit": "^8.2.2"}, "suggest": {"ext-xdebug": "^2.7.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/7.0.14"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-12-02T13:39:03+00:00"}, {"name": "phpunit/php-file-iterator", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "4b49fb70f067272b659ef0174ff9ca40fdaa6357"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/4b49fb70f067272b659ef0174ff9ca40fdaa6357", "reference": "4b49fb70f067272b659ef0174ff9ca40fdaa6357", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:25:21+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/1.2.1"}, "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "2454ae1765516d20c4ffe103d85a58a9a3bd5662"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/2454ae1765516d20c4ffe103d85a58a9a3bd5662", "reference": "2454ae1765516d20c4ffe103d85a58a9a3bd5662", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/2.1.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:20:02+00:00"}, {"name": "phpunit/php-token-stream", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "a853a0e183b9db7eed023d7933a858fa1c8d25a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/a853a0e183b9db7eed023d7933a858fa1c8d25a3", "reference": "a853a0e183b9db7eed023d7933a858fa1c8d25a3", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-token-stream/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-token-stream/tree/master"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "abandoned": true, "time": "2020-08-04T08:28:15+00:00"}, {"name": "phpunit/phpunit", "version": "8.5.14", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "c25f79895d27b6ecd5abfa63de1606b786a461a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/c25f79895d27b6ecd5abfa63de1606b786a461a3", "reference": "c25f79895d27b6ecd5abfa63de1606b786a461a3", "shasum": ""}, "require": {"doctrine/instantiator": "^1.3.1", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.10.0", "phar-io/manifest": "^2.0.1", "phar-io/version": "^3.0.2", "php": ">=7.2", "phpspec/prophecy": "^1.10.3", "phpunit/php-code-coverage": "^7.0.12", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1.2", "sebastian/comparator": "^3.0.2", "sebastian/diff": "^3.0.2", "sebastian/environment": "^4.2.3", "sebastian/exporter": "^3.1.2", "sebastian/global-state": "^3.0.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^2.0.1", "sebastian/type": "^1.1.3", "sebastian/version": "^2.0.1"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*", "phpunit/php-invoker": "^2.0.0"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "8.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/8.5.14"}, "funding": [{"url": "https://phpunit.de/donate.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-01-17T07:37:30+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/1de8cd5c010cb153fcd68b8d0f64606f523f7619", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/1.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:15:22+00:00"}, {"name": "sebastian/comparator", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "1071dfcef776a57013124ff35e1fc41ccd294758"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/1071dfcef776a57013124ff35e1fc41ccd294758", "reference": "1071dfcef776a57013124ff35e1fc41ccd294758", "shasum": ""}, "require": {"php": ">=7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:04:30+00:00"}, {"name": "sebastian/diff", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "14f72dd46eaf2f2293cbe79c93cc0bc43161a211"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/14f72dd46eaf2f2293cbe79c93cc0bc43161a211", "reference": "14f72dd46eaf2f2293cbe79c93cc0bc43161a211", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:59:04+00:00"}, {"name": "sebastian/environment", "version": "4.2.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "d47bbbad83711771f167c72d4e3f25f7fcc1f8b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastianbergmann/environment/zipball/d47bbbad83711771f167c72d4e3f25f7fcc1f8b0", "reference": "d47bbbad83711771f167c72d4e3f25f7fcc1f8b0", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/4.2.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:53:42+00:00"}, {"name": "sebastian/exporter", "version": "3.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "6b853149eab67d4da22291d36f5b0631c0fd856e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/6b853149eab67d4da22291d36f5b0631c0fd856e", "reference": "6b853149eab67d4da22291d36f5b0631c0fd856e", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/3.1.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:47:53+00:00"}, {"name": "sebastian/global-state", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "474fb9edb7ab891665d3bfc6317f42a0a150454b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/474fb9edb7ab891665d3bfc6317f42a0a150454b", "reference": "474fb9edb7ab891665d3bfc6317f42a0a150454b", "shasum": ""}, "require": {"php": ">=7.2", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^8.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:43:24+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/object-enumerator/zipball/e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/3.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:40:27+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/1.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:37:18+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/367dcba38d6e1977be014dc4b22f47a484dac7fb", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:34:24+00:00"}, {"name": "sebastian/resource-operations", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "31d35ca87926450c44eae7e2611d45a7a65ea8b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/31d35ca87926450c44eae7e2611d45a7a65ea8b3", "reference": "31d35ca87926450c44eae7e2611d45a7a65ea8b3", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"issues": "https://github.com/sebastian<PERSON>mann/resource-operations/issues", "source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:30:19+00:00"}, {"name": "sebastian/type", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "0150cfbc4495ed2df3872fb31b26781e4e077eb4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/0150cfbc4495ed2df3872fb31b26781e4e077eb4", "reference": "0150cfbc4495ed2df3872fb31b26781e4e077eb4", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/1.1.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:25:11+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/master"}, "time": "2016-10-03T07:35:21+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "75a63c33a8577608444246075ea0af0d052e452a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/75a63c33a8577608444246075ea0af0d052e452a", "reference": "75a63c33a8577608444246075ea0af0d052e452a", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/master"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2020-07-12T23:59:07+00:00"}, {"name": "webmozart/assert", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/webmozart/assert.git", "reference": "bafc69caeb4d49c39fd0779086c03a3738cbb389"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/assert/zipball/bafc69caeb4d49c39fd0779086c03a3738cbb389", "reference": "bafc69caeb4d49c39fd0779086c03a3738cbb389", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<3.9.1"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^7.5.13"}, "type": "library", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozart/assert/issues", "source": "https://github.com/webmozart/assert/tree/master"}, "time": "2020-07-08T17:02:28+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.0.0"}
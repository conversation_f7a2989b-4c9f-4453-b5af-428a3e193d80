[2025-08-21 15:53:09] production.ERROR: Predis\Connection\ConnectionException: Connection refused [tcp://127.0.0.1:6379] in /data/wwwroot/www.zhongnanhui.vip/vendor/predis/predis/src/Connection/AbstractConnection.php:155
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/predis/predis/src/Connection/StreamConnection.php(128): Predis\Connection\AbstractConnection->onConnectionError('Connection refu...', 111)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/predis/predis/src/Connection/StreamConnection.php(178): Predis\Connection\StreamConnection->createStreamSocket(Object(Predis\Connection\Parameters), 'tcp://127.0.0.1...', 4)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/predis/predis/src/Connection/StreamConnection.php(100): Predis\Connection\StreamConnection->tcpStreamInitializer(Object(Predis\Connection\Parameters))
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/predis/predis/src/Connection/AbstractConnection.php(81): Predis\Connection\StreamConnection->createResource()
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/predis/predis/src/Connection/StreamConnection.php(258): Predis\Connection\AbstractConnection->connect()
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/predis/predis/src/Connection/AbstractConnection.php(180): Predis\Connection\StreamConnection->connect()
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/predis/predis/src/Connection/StreamConnection.php(288): Predis\Connection\AbstractConnection->getResource()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/predis/predis/src/Connection/StreamConnection.php(394): Predis\Connection\StreamConnection->write('*2\r\n$3\r\nGET\r\n$2...')
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/predis/predis/src/Connection/AbstractConnection.php(110): Predis\Connection\StreamConnection->writeRequest(Object(Predis\Command\StringGet))
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/predis/predis/src/Client.php(331): Predis\Connection\AbstractConnection->executeCommand(Object(Predis\Command\StringGet))
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/predis/predis/src/Client.php(314): Predis\Client->executeCommand(Object(Predis\Command\StringGet))
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Redis/Connections/Connection.php(116): Predis\Client->__call('get', Array)
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Redis/Connections/Connection.php(220): Illuminate\Redis\Connections\Connection->command('get', Array)
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/RedisStore.php(54): Illuminate\Redis\Connections\Connection->__call('get', Array)
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(97): Illuminate\Cache\RedisStore->get('account_app_')
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(374): Illuminate\Cache\Repository->get('account_app_')
#16 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#18 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#19 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#20 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#21 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#22 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#28 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#29 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#30 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#31 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#36 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#37 {main}  
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    [2025-08-21 15:54:39] production.ERROR: PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:43
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php(43): PDO->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#6 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#16 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#24 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#31 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#32 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#39 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#40 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#45 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#46 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#47 {main}

Next Doctrine\DBAL\Driver\PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:47
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#16 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#27 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#31 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#39 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#40 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#45 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#46 {main}

Next Illuminate\Database\QueryException: SQLSTATE[HY000] [2002] Connection refused (SQL: select * from `ims_yz_uniacid_app` where `uniacid` is null limit 1) in /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php:669
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#4 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#12 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#16 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#17 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#18 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#19 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#20 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#28 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#29 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#30 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#31 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#34 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#35 {main}  
[2025-08-21 15:54:40] production.ERROR: PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:43
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php(43): PDO->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#6 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#16 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#24 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#31 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#32 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#39 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#40 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#45 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#46 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#47 {main}

Next Doctrine\DBAL\Driver\PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:47
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#16 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#27 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#31 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#39 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#40 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#45 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#46 {main}

Next Illuminate\Database\QueryException: SQLSTATE[HY000] [2002] Connection refused (SQL: select * from `ims_yz_uniacid_app` where `uniacid` is null limit 1) in /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php:669
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#4 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#12 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#16 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#17 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#18 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#19 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#20 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#28 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#29 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#30 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#31 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#34 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#35 {main}  
[2025-08-21 15:54:41] production.ERROR: PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:43
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php(43): PDO->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#6 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#16 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#24 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#31 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#32 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#39 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#40 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#45 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#46 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#47 {main}

Next Doctrine\DBAL\Driver\PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:47
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#16 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#27 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#31 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#39 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#40 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#45 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#46 {main}

Next Illuminate\Database\QueryException: SQLSTATE[HY000] [2002] Connection refused (SQL: select * from `ims_yz_uniacid_app` where `uniacid` is null limit 1) in /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php:669
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#4 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#12 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#16 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#17 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#18 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#19 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#20 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#28 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#29 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#30 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#31 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#34 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#35 {main}  
[2025-08-21 15:54:42] production.ERROR: PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:43
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php(43): PDO->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#6 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#16 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#24 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#31 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#32 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#39 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#40 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#45 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#46 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#47 {main}

Next Doctrine\DBAL\Driver\PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:47
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#16 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#27 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#31 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#39 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#40 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#45 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#46 {main}

Next Illuminate\Database\QueryException: SQLSTATE[HY000] [2002] Connection refused (SQL: select * from `ims_yz_uniacid_app` where `uniacid` is null limit 1) in /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php:669
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#4 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#12 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#16 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#17 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#18 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#19 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#20 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#28 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#29 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#30 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#31 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#34 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#35 {main}  
[2025-08-21 15:54:42] production.ERROR: PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:43
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php(43): PDO->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#6 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#16 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#24 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#31 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#32 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#39 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#40 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#45 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#46 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#47 {main}

Next Doctrine\DBAL\Driver\PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:47
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#16 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#27 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#31 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#39 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#40 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#45 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#46 {main}

Next Illuminate\Database\QueryException: SQLSTATE[HY000] [2002] Connection refused (SQL: select * from `ims_yz_uniacid_app` where `uniacid` is null limit 1) in /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php:669
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#4 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#12 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#16 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#17 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#18 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#19 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#20 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#28 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#29 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#30 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#31 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#34 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#35 {main}  
[2025-08-21 15:54:43] production.ERROR: PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:43
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php(43): PDO->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#6 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#16 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#24 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#31 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#32 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#39 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#40 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#45 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#46 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#47 {main}

Next Doctrine\DBAL\Driver\PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:47
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#16 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#27 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#31 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#39 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#40 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#45 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#46 {main}

Next Illuminate\Database\QueryException: SQLSTATE[HY000] [2002] Connection refused (SQL: select * from `ims_yz_uniacid_app` where `uniacid` is null limit 1) in /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php:669
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#4 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#12 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#16 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#17 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#18 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#19 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#20 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#28 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#29 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#30 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#31 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#34 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#35 {main}  
[2025-08-21 15:54:44] production.ERROR: PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:43
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php(43): PDO->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#6 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#16 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#24 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#31 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#32 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#39 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#40 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#45 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#46 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#47 {main}

Next Doctrine\DBAL\Driver\PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:47
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#16 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#27 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#31 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#39 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#40 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#45 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#46 {main}

Next Illuminate\Database\QueryException: SQLSTATE[HY000] [2002] Connection refused (SQL: select * from `ims_yz_uniacid_app` where `uniacid` is null limit 1) in /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php:669
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#4 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#12 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#16 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#17 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#18 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#19 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#20 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#28 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#29 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#30 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#31 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#34 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#35 {main}  
[2025-08-21 15:54:44] production.ERROR: PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:43
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php(43): PDO->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#6 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#16 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#24 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#31 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#32 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#39 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#40 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#45 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#46 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#47 {main}

Next Doctrine\DBAL\Driver\PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:47
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#16 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#27 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#31 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#39 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#40 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#45 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#46 {main}

Next Illuminate\Database\QueryException: SQLSTATE[HY000] [2002] Connection refused (SQL: select * from `ims_yz_uniacid_app` where `uniacid` is null limit 1) in /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php:669
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#4 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#12 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#16 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#17 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#18 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#19 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#20 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#28 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#29 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#30 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#31 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#34 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#35 {main}  
[2025-08-21 15:54:45] production.ERROR: PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:43
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php(43): PDO->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#6 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#16 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#24 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#31 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#32 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#39 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#40 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#45 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#46 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#47 {main}

Next Doctrine\DBAL\Driver\PDOException: SQLSTATE[HY000] [2002] Connection refused in /data/wwwroot/www.zhongnanhui.vip/vendor/doctrine/dbal/lib/Doctrine/DBAL/Driver/PDOConnection.php:47
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(67): Doctrine\DBAL\Driver\PDOConnection->__construct('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(100): Illuminate\Database\Connectors\Connector->createPdoConnection('mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(50): Illuminate\Database\Connectors\Connector->tryAgainIfCausedByLostConnection(Object(Doctrine\DBAL\Driver\PDOException), 'mysql:host=127....', 'root', 'COmlq37xcliqpob...', Array)
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\Database\Connectors\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(182): Illuminate\Database\Connectors\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\Database\Connectors\ConnectionFactory->Illuminate\Database\Connectors\{closure}()
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(924): call_user_func(Object(Closure))
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(959): Illuminate\Database\Connection->getPdo()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\Database\Connection->getReadPdo()
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(330): Illuminate\Database\Connection->getPdoForSelect(true)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(662): Illuminate\Database\Connection->Illuminate\Database\{closure}('select * from `...', Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#16 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#17 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#18 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#19 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#20 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#23 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#27 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#28 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#29 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#30 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#31 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#34 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#35 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#36 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#37 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#38 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#39 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#40 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#41 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#42 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#43 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#44 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#45 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#46 {main}

Next Illuminate\Database\QueryException: SQLSTATE[HY000] [2002] Connection refused (SQL: select * from `ims_yz_uniacid_app` where `uniacid` is null limit 1) in /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php:669
Stack trace:
#0 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(743): Illuminate\Database\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(723): Illuminate\Database\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#2 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(632): Illuminate\Database\Connection->handleQueryException(Object(Illuminate\Database\QueryException), 'select * from `...', Array, Object(Closure))
#3 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Connection.php(338): Illuminate\Database\Connection->run('select * from `...', Array, Object(Closure))
#4 /data/wwwroot/www.zhongnanhui.vip/app/framework/Database/MySqlConnection.php(27): Illuminate\Database\Connection->select('select * from `...', Array, true)
#5 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2149): app\framework\Database\MySqlConnection->select('select * from `...', Array, true)
#6 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2137): Illuminate\Database\Query\Builder->runSelect()
#7 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2609): Illuminate\Database\Query\Builder->Illuminate\Database\Query\{closure}()
#8 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2138): Illuminate\Database\Query\Builder->onceWithColumns(Array, Object(Closure))
#9 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(545): Illuminate\Database\Query\Builder->get(Array)
#10 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(529): Illuminate\Database\Eloquent\Builder->getModels(Array)
#11 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(143): Illuminate\Database\Eloquent\Builder->get(Array)
#12 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(33): Illuminate\Database\Eloquent\Builder->first()
#13 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(383): app\common\models\AccountWechats::app\common\models\{closure}()
#14 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(378): Illuminate\Cache\Repository->remember('account_app_', 3600, Object(Closure))
#15 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(261): Illuminate\Cache\CacheManager->__call('remember', Array)
#16 /data/wwwroot/www.zhongnanhui.vip/app/common/models/AccountWechats.php(34): Illuminate\Support\Facades\Facade::__callStatic('remember', Array)
#17 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(497): app\common\models\AccountWechats::getAccountByUniacid(NULL)
#18 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(492): YunApp->getW()
#19 /data/wwwroot/www.zhongnanhui.vip/app/yunshop.php(231): YunApp->__construct()
#20 /data/wwwroot/www.zhongnanhui.vip/app/common/providers/YunShopServiceProvider.php(27): YunShop::app()
#21 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): app\common\providers\YunShopServiceProvider->boot()
#22 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Util.php(37): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#23 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#24 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\Container\BoundMethod::callBoundMethod(Object(app\framework\Foundation\Application), Array, Object(Closure))
#25 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Container/Container.php(590): Illuminate\Container\BoundMethod::call(Object(app\framework\Foundation\Application), Array, Array, NULL)
#26 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\Container\Container->call(Array)
#27 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(839): Illuminate\Foundation\Application->bootProvider(Object(app\common\providers\YunShopServiceProvider))
#28 [internal function]: Illuminate\Foundation\Application->Illuminate\Foundation\{closure}(Object(app\common\providers\YunShopServiceProvider), 27)
#29 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(840): array_walk(Array, Object(Closure))
#30 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\Foundation\Application->boot()
#31 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(219): Illuminate\Foundation\Bootstrap\BootProviders->bootstrap(Object(app\framework\Foundation\Application))
#32 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(320): Illuminate\Foundation\Application->bootstrapWith(Array)
#33 /data/wwwroot/www.zhongnanhui.vip/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(129): Illuminate\Foundation\Console\Kernel->bootstrap()
#34 /data/wwwroot/www.zhongnanhui.vip/artisan(39): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#35 {main}  

/*
录音
https://github.com/xiangyuecn/Recorder
src: engine/beta-amr.js,engine/beta-amr-engine.js,engine/wav.js
*/
!function(){"use strict";Recorder.prototype.enc_amr={stable:!1,testmsg:"采样率比特率设置无效，只提供8000hz，AMR12.2(12.8kbps)"},Recorder.amr2wav=function(e,n,r){var t=new FileReader;t.onload=function(){var e=new Uint8Array(t.result);Recorder.AMR.decode(e,function(e){Recorder({type:"wav"}).mock(e,8e3).stop(function(e,r){n(e,r)},r)},r)},t.readAsArrayBuffer(e)},Recorder.prototype.amr=function(e,n,r){var t=this.set;e.length;t.bitRate=12.8;var i=t.sampleRate;if(8e3!=i)return console.log("amr mock start"),t.sampleRate=8e3,void this.mock(e,i).stop(function(e,r){console.log("amr mock end"),n(e)},r);Recorder.AMR.encode(e,function(e){n(new Blob([e.buffer],{type:"audio/amr"}))})}}(),function(){"use strict";var AMR={decode:function(t,i,e){var o=this;if(String.fromCharCode.apply(null,t.subarray(0,this.AMR_HEADER.length))===this.AMR_HEADER){var a=this.Decoder_Interface_init(),s=new Int16Array(Math.floor(t.length/6*this.PCM_BUFFER_COUNT)),r=Module._malloc(this.AMR_BUFFER_COUNT),l=new Uint8Array(Module.HEAPU8.buffer,r,this.AMR_BUFFER_COUNT);r=Module._malloc(2*this.PCM_BUFFER_COUNT);var f=new Int16Array(Module.HEAPU8.buffer,r,this.PCM_BUFFER_COUNT),u=6,c=0,d=function(){for(var e=0;u+1<t.length&&c+1<s.length;){var r=o.SIZES[t[u]>>3&15];if(u+r+1>t.length)break;if(l.set(t.subarray(u,u+r+1)),o.Decoder_Interface_Decode(a,l.byteOffset,f.byteOffset,0),c+o.PCM_BUFFER_COUNT>s.length){var n=new Int16Array(2*s.length);n.set(s.subarray(0,c)),s=n}if(s.set(f,c),c+=o.PCM_BUFFER_COUNT,u+=r+1,2e4<(e+=r+1))return void setTimeout(d)}Module._free(l.byteOffset),Module._free(f.byteOffset),o.Decoder_Interface_exit(a),i(s.subarray(0,c))};d()}else e("非AMR音频数据")},encode:function(n,t){var i=this,o=this.Encoder_Interface_init(),e=Module._malloc(2*this.PCM_BUFFER_COUNT),a=new Int16Array(Module.HEAPU8.buffer,e,this.PCM_BUFFER_COUNT);e=Module._malloc(this.AMR_BUFFER_COUNT);var s=new Uint8Array(Module.HEAPU8.buffer,e,this.AMR_BUFFER_COUNT),l=this.SIZES[7]+1,f=new Uint8Array(Math.ceil(n.length/this.PCM_BUFFER_COUNT*l)+this.AMR_HEADER.length);f.set(new TextEncoder("utf-8").encode(this.AMR_HEADER));var u=0,c=this.AMR_HEADER.length,d=function(){for(var e=0;u+i.PCM_BUFFER_COUNT<n.length&&c+l<f.length;){a.set(n.subarray(u,u+i.PCM_BUFFER_COUNT));var r=i.Encoder_Interface_Encode(o,7,a.byteOffset,s.byteOffset,0);if(r!=l){console.error([r,l]);break}if(f.set(s.subarray(0,r),c),u+=i.PCM_BUFFER_COUNT,c+=r,4e4<(e+=i.PCM_BUFFER_COUNT))return void setTimeout(d)}Module._free(a.byteOffset),Module._free(s.byteOffset),i.Encoder_Interface_exit(o),t(f.subarray(0,c))};d()},Decoder_Interface_init:function(){return console.warn("Decoder_Interface_init not initialized."),0},Decoder_Interface_exit:function(e){console.warn("Decoder_Interface_exit not initialized.")},Decoder_Interface_Decode:function(e,r,n,t){console.warn("Decoder_Interface_Decode not initialized.")},Encoder_Interface_init:function(e){return console.warn("Encoder_Interface_init not initialized."),0},Encoder_Interface_exit:function(e){console.warn("Encoder_Interface_exit not initialized.")},Encoder_Interface_Encode:function(e,r,n,t,i){console.warn("Encoder_Interface_Encode not initialized.")},Mode:{MR475:0,MR515:1,MR59:2,MR67:3,MR74:4,MR795:5,MR102:6,MR122:7,MRDTX:8},SIZES:[12,13,15,17,19,20,26,31,5,6,5,5,0,0,0,0],AMR_BUFFER_COUNT:32,PCM_BUFFER_COUNT:160,AMR_HEADER:"#!AMR\n",WAV_HEADER_SIZE:44},Module={canvas:{},print:function(e){console.log(e)},_main:function(){return AMR.Decoder_Interface_init=Module._Decoder_Interface_init,AMR.Decoder_Interface_exit=Module._Decoder_Interface_exit,AMR.Decoder_Interface_Decode=Module._Decoder_Interface_Decode,AMR.Encoder_Interface_init=Module._Encoder_Interface_init,AMR.Encoder_Interface_exit=Module._Encoder_Interface_exit,AMR.Encoder_Interface_Encode=Module._Encoder_Interface_Encode,0}},Module;Module||(Module=(void 0!==Module?Module:null)||{});var moduleOverrides={};for(var key in Module)Module.hasOwnProperty(key)&&(moduleOverrides[key]=Module[key]);var ENVIRONMENT_IS_WEB="object"==typeof window,ENVIRONMENT_IS_WORKER="function"==typeof importScripts,ENVIRONMENT_IS_NODE=!1,ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;if(ENVIRONMENT_IS_SHELL)Module.print||(Module.print=print),"undefined"!=typeof printErr&&(Module.printErr=printErr),"undefined"!=typeof read?Module.read=read:Module.read=function(){throw"no read() available (jsc?)"},Module.readBinary=function(e){if("function"==typeof readbuffer)return new Uint8Array(readbuffer(e));var r=read(e,"binary");return assert("object"==typeof r),r},"undefined"!=typeof scriptArgs?Module.arguments=scriptArgs:void 0!==arguments&&(Module.arguments=arguments);else{if(!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_WORKER)throw"Unknown runtime environment. Where are we?";if(Module.read=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.send(null),r.responseText},void 0!==arguments&&(Module.arguments=arguments),"undefined"!=typeof console)Module.print||(Module.print=function(e){console.log(e)}),Module.printErr||(Module.printErr=function(e){console.log(e)});else{var TRY_USE_DUMP=!1;Module.print||(Module.print=TRY_USE_DUMP&&"undefined"!=typeof dump?function(e){dump(e)}:function(e){})}ENVIRONMENT_IS_WORKER&&(Module.load=importScripts),void 0===Module.setWindowTitle&&(Module.setWindowTitle=function(e){document.title=e})}function globalEval(e){eval.call(null,e)}for(var key in!Module.load&&Module.read&&(Module.load=function(e){globalEval(Module.read(e))}),Module.print||(Module.print=function(){}),Module.printErr||(Module.printErr=Module.print),Module.arguments||(Module.arguments=[]),Module.thisProgram||(Module.thisProgram="./this.program"),Module.print=Module.print,Module.printErr=Module.printErr,Module.preRun=[],Module.postRun=[],moduleOverrides)moduleOverrides.hasOwnProperty(key)&&(Module[key]=moduleOverrides[key]);var Runtime={setTempRet0:function(e){tempRet0=e},getTempRet0:function(){return tempRet0},stackSave:function(){return STACKTOP},stackRestore:function(e){STACKTOP=e},getNativeTypeSize:function(e){switch(e){case"i1":case"i8":return 1;case"i16":return 2;case"i32":return 4;case"i64":return 8;case"float":return 4;case"double":return 8;default:if("*"===e[e.length-1])return Runtime.QUANTUM_SIZE;if("i"===e[0]){var r=parseInt(e.substr(1));return assert(r%8==0),r/8}return 0}},getNativeFieldSize:function(e){return Math.max(Runtime.getNativeTypeSize(e),Runtime.QUANTUM_SIZE)},STACK_ALIGN:16,prepVararg:function(e,r){return"double"===r||"i64"===r?7&e&&(assert(4==(7&e)),e+=4):assert(0==(3&e)),e},getAlignSize:function(e,r,n){return n||"i64"!=e&&"double"!=e?e?Math.min(r||(e?Runtime.getNativeFieldSize(e):0),Runtime.QUANTUM_SIZE):Math.min(r,8):8},dynCall:function(e,r,n){return n&&n.length?(n.splice||(n=Array.prototype.slice.call(n)),n.splice(0,0,r),Module["dynCall_"+e].apply(null,n)):Module["dynCall_"+e].call(null,r)},functionPointers:[],addFunction:function(e){for(var r=0;r<Runtime.functionPointers.length;r++)if(!Runtime.functionPointers[r])return Runtime.functionPointers[r]=e,2*(1+r);throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS."},removeFunction:function(e){Runtime.functionPointers[(e-2)/2]=null},warnOnce:function(e){Runtime.warnOnce.shown||(Runtime.warnOnce.shown={}),Runtime.warnOnce.shown[e]||(Runtime.warnOnce.shown[e]=1,Module.printErr(e))},funcWrappers:{},getFuncWrapper:function(e,r){assert(r),Runtime.funcWrappers[r]||(Runtime.funcWrappers[r]={});var n=Runtime.funcWrappers[r];return n[e]||(n[e]=function(){return Runtime.dynCall(r,e,arguments)}),n[e]},getCompilerSetting:function(e){throw"You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work"},stackAlloc:function(e){var r=STACKTOP;return STACKTOP=(STACKTOP=STACKTOP+e|0)+15&-16,r},staticAlloc:function(e){var r=STATICTOP;return STATICTOP=(STATICTOP=STATICTOP+e|0)+15&-16,r},dynamicAlloc:function(e){var r=DYNAMICTOP;if(TOTAL_MEMORY<=(DYNAMICTOP=(DYNAMICTOP=DYNAMICTOP+e|0)+15&-16)&&!enlargeMemory())return DYNAMICTOP=r,0;return r},alignMemory:function(e,r){return e=Math.ceil(e/(r||16))*(r||16)},makeBigInt:function(e,r,n){return n?+(e>>>0)+4294967296*+(r>>>0):+(e>>>0)+4294967296*+(0|r)},GLOBAL_BASE:8,QUANTUM_SIZE:4,__dummy__:0};Module.Runtime=Runtime;var __THREW__=0,ABORT=!1,EXITSTATUS=0,undef=0,tempValue,tempInt,tempBigInt,tempInt2,tempBigInt2,tempPair,tempBigIntI,tempBigIntR,tempBigIntS,tempBigIntP,tempBigIntD,tempDouble,tempFloat,tempI64,tempI64b,tempRet0,tempRet1,tempRet2,tempRet3,tempRet4,tempRet5,tempRet6,tempRet7,tempRet8,tempRet9;function assert(e,r){e||abort("Assertion failed: "+r)}var globalScope=this,cwrap,ccall;function getCFunc(ident){var func=Module["_"+ident];if(!func)try{func=eval("_"+ident)}catch(e){}return assert(func,"Cannot call unknown function "+ident+" (perhaps LLVM optimizations or closure removed it?)"),func}function setValue(e,r,n,t){switch("*"===(n=n||"i8").charAt(n.length-1)&&(n="i32"),n){case"i1":case"i8":HEAP8[e>>0]=r;break;case"i16":HEAP16[e>>1]=r;break;case"i32":HEAP32[e>>2]=r;break;case"i64":tempI64=[r>>>0,(tempDouble=r,1<=+Math_abs(tempDouble)?0<tempDouble?(0|Math_min(+Math_floor(tempDouble/4294967296),4294967295))>>>0:~~+Math_ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[e>>2]=tempI64[0],HEAP32[e+4>>2]=tempI64[1];break;case"float":HEAPF32[e>>2]=r;break;case"double":HEAPF64[e>>3]=r;break;default:abort("invalid type for setValue: "+n)}}function getValue(e,r,n){switch("*"===(r=r||"i8").charAt(r.length-1)&&(r="i32"),r){case"i1":case"i8":return HEAP8[e>>0];case"i16":return HEAP16[e>>1];case"i32":case"i64":return HEAP32[e>>2];case"float":return HEAPF32[e>>2];case"double":return HEAPF64[e>>3];default:abort("invalid type for setValue: "+r)}return null}!function(){var JSfuncs={stackSave:function(){Runtime.stackSave()},stackRestore:function(){Runtime.stackRestore()},arrayToC:function(e){var r=Runtime.stackAlloc(e.length);return writeArrayToMemory(e,r),r},stringToC:function(e){var r=0;return null!=e&&0!==e&&writeStringToMemory(e,r=Runtime.stackAlloc(1+(e.length<<2))),r}},toC={string:JSfuncs.stringToC,array:JSfuncs.arrayToC};ccall=function(e,r,n,t,i){var o=getCFunc(e),a=[],s=0;if(t)for(var l=0;l<t.length;l++){var f=toC[n[l]];f?(0===s&&(s=Runtime.stackSave()),a[l]=f(t[l])):a[l]=t[l]}var u=o.apply(null,a);if("string"===r&&(u=Pointer_stringify(u)),0!==s){if(i&&i.async)return void EmterpreterAsync.asyncFinalizers.push(function(){Runtime.stackRestore(s)});Runtime.stackRestore(s)}return u};var sourceRegex=/^function\s*\(([^)]*)\)\s*{\s*([^*]*?)[\s;]*(?:return\s*(.*?)[;\s]*)?}$/;function parseJSFunc(e){var r=e.toString().match(sourceRegex).slice(1);return{arguments:r[0],body:r[1],returnValue:r[2]}}var JSsource={};for(var fun in JSfuncs)JSfuncs.hasOwnProperty(fun)&&(JSsource[fun]=parseJSFunc(JSfuncs[fun]));cwrap=function cwrap(ident,returnType,argTypes){argTypes=argTypes||[];var cfunc=getCFunc(ident),numericArgs=argTypes.every(function(e){return"number"===e}),numericRet="string"!==returnType;if(numericRet&&numericArgs)return cfunc;var argNames=argTypes.map(function(e,r){return"$"+r}),funcstr="(function("+argNames.join(",")+") {",nargs=argTypes.length;if(!numericArgs){funcstr+="var stack = "+JSsource.stackSave.body+";";for(var i=0;i<nargs;i++){var arg=argNames[i],type=argTypes[i];if("number"!==type){var convertCode=JSsource[type+"ToC"];funcstr+="var "+convertCode.arguments+" = "+arg+";",funcstr+=convertCode.body+";",funcstr+=arg+"="+convertCode.returnValue+";"}}}var cfuncname=parseJSFunc(function(){return cfunc}).returnValue;if(funcstr+="var ret = "+cfuncname+"("+argNames.join(",")+");",!numericRet){var strgfy=parseJSFunc(function(){return Pointer_stringify}).returnValue;funcstr+="ret = "+strgfy+"(ret);"}return numericArgs||(funcstr+=JSsource.stackRestore.body.replace("()","(stack)")+";"),funcstr+="return ret})",eval(funcstr)}}(),Module.ccall=ccall,Module.cwrap=cwrap,Module.setValue=setValue,Module.getValue=getValue;var ALLOC_NORMAL=0,ALLOC_STACK=1,ALLOC_STATIC=2,ALLOC_DYNAMIC=3,ALLOC_NONE=4;function allocate(e,r,n,t){var i,o;"number"==typeof e?(i=!0,o=e):(i=!1,o=e.length);var a,s="string"==typeof r?r:null;if(a=n==ALLOC_NONE?t:[_malloc,Runtime.stackAlloc,Runtime.staticAlloc,Runtime.dynamicAlloc][void 0===n?ALLOC_STATIC:n](Math.max(o,s?1:r.length)),i){var l;t=a;for(assert(0==(3&a)),l=a+(-4&o);t<l;t+=4)HEAP32[t>>2]=0;for(l=a+o;t<l;)HEAP8[t++>>0]=0;return a}if("i8"===s)return e.subarray||e.slice?HEAPU8.set(e,a):HEAPU8.set(new Uint8Array(e),a),a;for(var f,u,c,d=0;d<o;){var h=e[d];"function"==typeof h&&(h=Runtime.getFunctionIndex(h)),0!==(f=s||r[d])?("i64"==f&&(f="i32"),setValue(a+d,h,f),c!==f&&(u=Runtime.getNativeTypeSize(f),c=f),d+=u):d++}return a}function getMemory(e){return staticSealed?void 0!==_sbrk&&!_sbrk.called||!runtimeInitialized?Runtime.dynamicAlloc(e):_malloc(e):Runtime.staticAlloc(e)}function Pointer_stringify(e,r){if(0===r||!e)return"";for(var n,t=0,i=0;t|=n=HEAPU8[e+i>>0],(0!=n||r)&&(i++,!r||i!=r););r||(r=i);var o="";if(t<128){for(var a;0<r;)a=String.fromCharCode.apply(String,HEAPU8.subarray(e,e+Math.min(r,1024))),o=o?o+a:a,e+=1024,r-=1024;return o}return Module.UTF8ToString(e)}function AsciiToString(e){for(var r="";;){var n=HEAP8[e++>>0];if(!n)return r;r+=String.fromCharCode(n)}}function stringToAscii(e,r){return writeAsciiToMemory(e,r,!1)}function UTF8ArrayToString(e,r){for(var n,t,i,o,a,s="";;){if(!(n=e[r++]))return s;if(128&n)if(t=63&e[r++],192!=(224&n))if(i=63&e[r++],224==(240&n)?n=(15&n)<<12|t<<6|i:(o=63&e[r++],240==(248&n)?n=(7&n)<<18|t<<12|i<<6|o:(a=63&e[r++],n=248==(252&n)?(3&n)<<24|t<<18|i<<12|o<<6|a:(1&n)<<30|t<<24|i<<18|o<<12|a<<6|63&e[r++])),n<65536)s+=String.fromCharCode(n);else{var l=n-65536;s+=String.fromCharCode(55296|l>>10,56320|1023&l)}else s+=String.fromCharCode((31&n)<<6|t);else s+=String.fromCharCode(n)}}function UTF8ToString(e){return UTF8ArrayToString(HEAPU8,e)}function stringToUTF8Array(e,r,n,t){if(!(0<t))return 0;for(var i=n,o=n+t-1,a=0;a<e.length;++a){var s=e.charCodeAt(a);if(55296<=s&&s<=57343&&(s=65536+((1023&s)<<10)|1023&e.charCodeAt(++a)),s<=127){if(o<=n)break;r[n++]=s}else if(s<=2047){if(o<=n+1)break;r[n++]=192|s>>6,r[n++]=128|63&s}else if(s<=65535){if(o<=n+2)break;r[n++]=224|s>>12,r[n++]=128|s>>6&63,r[n++]=128|63&s}else if(s<=2097151){if(o<=n+3)break;r[n++]=240|s>>18,r[n++]=128|s>>12&63,r[n++]=128|s>>6&63,r[n++]=128|63&s}else if(s<=67108863){if(o<=n+4)break;r[n++]=248|s>>24,r[n++]=128|s>>18&63,r[n++]=128|s>>12&63,r[n++]=128|s>>6&63,r[n++]=128|63&s}else{if(o<=n+5)break;r[n++]=252|s>>30,r[n++]=128|s>>24&63,r[n++]=128|s>>18&63,r[n++]=128|s>>12&63,r[n++]=128|s>>6&63,r[n++]=128|63&s}}return r[n]=0,n-i}function stringToUTF8(e,r,n){return stringToUTF8Array(e,HEAPU8,r,n)}function lengthBytesUTF8(e){for(var r=0,n=0;n<e.length;++n){var t=e.charCodeAt(n);55296<=t&&t<=57343&&(t=65536+((1023&t)<<10)|1023&e.charCodeAt(++n)),t<=127?++r:r+=t<=2047?2:t<=65535?3:t<=2097151?4:t<=67108863?5:6}return r}function UTF16ToString(e){for(var r=0,n="";;){var t=HEAP16[e+2*r>>1];if(0==t)return n;++r,n+=String.fromCharCode(t)}}function stringToUTF16(e,r,n){if(void 0===n&&(n=2147483647),n<2)return 0;for(var t=r,i=(n-=2)<2*e.length?n/2:e.length,o=0;o<i;++o){var a=e.charCodeAt(o);HEAP16[r>>1]=a,r+=2}return HEAP16[r>>1]=0,r-t}function lengthBytesUTF16(e){return 2*e.length}function UTF32ToString(e){for(var r=0,n="";;){var t=HEAP32[e+4*r>>2];if(0==t)return n;if(++r,65536<=t){var i=t-65536;n+=String.fromCharCode(55296|i>>10,56320|1023&i)}else n+=String.fromCharCode(t)}}function stringToUTF32(e,r,n){if(void 0===n&&(n=2147483647),n<4)return 0;for(var t=r,i=t+n-4,o=0;o<e.length;++o){var a=e.charCodeAt(o);if(55296<=a&&a<=57343)a=65536+((1023&a)<<10)|1023&e.charCodeAt(++o);if(HEAP32[r>>2]=a,i<(r+=4)+4)break}return HEAP32[r>>2]=0,r-t}function lengthBytesUTF32(e){for(var r=0,n=0;n<e.length;++n){var t=e.charCodeAt(n);55296<=t&&t<=57343&&++n,r+=4}return r}function demangle(c){var e=!!Module.___cxa_demangle;if(e)try{var r=_malloc(c.length);writeStringToMemory(c.substr(1),r);var n=_malloc(4),t=Module.___cxa_demangle(r,0,0,n);if(0===getValue(n,"i32")&&t)return Pointer_stringify(t)}catch(e){}finally{r&&_free(r),n&&_free(n),t&&_free(t)}var d=3,h={v:"void",b:"bool",c:"char",s:"short",i:"int",l:"long",f:"float",d:"double",w:"wchar_t",a:"signed char",h:"unsigned char",t:"unsigned short",j:"unsigned int",m:"unsigned long",x:"long long",y:"unsigned long long",z:"..."},w=[],m=!0;var i=c;try{if("Object._main"==c||"_main"==c)return"main()";if("number"==typeof c&&(c=Pointer_stringify(c)),"_"!==c[0])return c;if("_"!==c[1])return c;if("Z"!==c[2])return c;switch(c[3]){case"n":return"operator new()";case"d":return"operator delete()"}i=function e(r,n,t){n=n||1/0;var i,o="",a=[];if("N"===c[d]){if(i=function(){"K"===c[++d]&&d++;for(var e=[];"E"!==c[d];)if("S"!==c[d])if("C"!==c[d]){var r=parseInt(c.substr(d)),n=r.toString().length;if(!r||!n){d--;break}var t=c.substr(d+n,r);e.push(t),w.push(t),d+=n+r}else e.push(e[e.length-1]),d+=2;else{d++;var i=c.indexOf("_",d),o=c.substring(d,i)||0;e.push(w[o]||"?"),d=i+1}return d++,e}().join("::"),0==--n)return r?[i]:i}else if(("K"===c[d]||m&&"L"===c[d])&&d++,u=parseInt(c.substr(d))){var s=u.toString().length;i=c.substr(d+s,u),d+=s+u}if(m=!1,"I"===c[d]){d++;var l=e(!0);o+=e(!0,1,!0)[0]+" "+i+"<"+l.join(", ")+">"}else o=i;e:for(;d<c.length&&0<n--;){var f=c[d++];if(f in h)a.push(h[f]);else switch(f){case"P":a.push(e(!0,1,!0)[0]+"*");break;case"R":a.push(e(!0,1,!0)[0]+"&");break;case"L":d++;var u=c.indexOf("E",d)-d;a.push(c.substr(d,u)),d+=u+2;break;case"A":if(u=parseInt(c.substr(d)),d+=u.toString().length,"_"!==c[d])throw"?";d++,a.push(e(!0,1,!0)[0]+" ["+u+"]");break;case"E":break e;default:o+="?"+f;break e}}return t||1!==a.length||"void"!==a[0]||(a=[]),r?(o&&a.push(o+"?"),a):o+"("+a.join(", ")+")"}()}catch(e){i+="?"}return 0<=i.indexOf("?")&&!e&&Runtime.warnOnce("warning: a problem occurred in builtin C++ name demangling; build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"),i}function demangleAll(e){return e.replace(/__Z[\w\d_]+/g,function(e){var r=demangle(e);return e===r?e:e+" ["+r+"]"})}function jsStackTrace(){var r=new Error;if(!r.stack){try{throw new Error(0)}catch(e){r=e}if(!r.stack)return"(no stack trace available)"}return r.stack.toString()}function stackTrace(){return demangleAll(jsStackTrace())}Module.ALLOC_NORMAL=ALLOC_NORMAL,Module.ALLOC_STACK=ALLOC_STACK,Module.ALLOC_STATIC=ALLOC_STATIC,Module.ALLOC_DYNAMIC=ALLOC_DYNAMIC,Module.ALLOC_NONE=ALLOC_NONE,Module.allocate=allocate,Module.getMemory=getMemory,Module.Pointer_stringify=Pointer_stringify,Module.AsciiToString=AsciiToString,Module.stringToAscii=stringToAscii,Module.UTF8ArrayToString=UTF8ArrayToString,Module.UTF8ToString=UTF8ToString,Module.stringToUTF8Array=stringToUTF8Array,Module.stringToUTF8=stringToUTF8,Module.lengthBytesUTF8=lengthBytesUTF8,Module.UTF16ToString=UTF16ToString,Module.stringToUTF16=stringToUTF16,Module.lengthBytesUTF16=lengthBytesUTF16,Module.UTF32ToString=UTF32ToString,Module.stringToUTF32=stringToUTF32,Module.lengthBytesUTF32=lengthBytesUTF32,Module.stackTrace=stackTrace;var PAGE_SIZE=4096,HEAP,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function alignMemoryPage(e){return 0<e%4096&&(e+=4096-e%4096),e}var STATIC_BASE=0,STATICTOP=0,staticSealed=!1,STACK_BASE=0,STACKTOP=0,STACK_MAX=0,DYNAMIC_BASE=0,DYNAMICTOP=0;function enlargeMemory(){abort("Cannot enlarge memory arrays. Either (1) compile with -s TOTAL_MEMORY=X with X higher than the current value "+TOTAL_MEMORY+", (2) compile with ALLOW_MEMORY_GROWTH which adjusts the size at runtime but prevents some optimizations, or (3) set Module.TOTAL_MEMORY before the program runs.")}for(var TOTAL_STACK=Module.TOTAL_STACK||65536,TOTAL_MEMORY=Module.TOTAL_MEMORY||524288,totalMemory=65536,buffer;totalMemory<TOTAL_MEMORY||totalMemory<2*TOTAL_STACK;)totalMemory<16777216?totalMemory*=2:totalMemory+=16777216;function callRuntimeCallbacks(e){for(;0<e.length;){var r=e.shift();if("function"!=typeof r){var n=r.func;"number"==typeof n?void 0===r.arg?Runtime.dynCall("v",n):Runtime.dynCall("vi",n,[r.arg]):n(void 0===r.arg?null:r.arg)}else r()}}totalMemory!==TOTAL_MEMORY&&(Module.printErr("increasing TOTAL_MEMORY to "+totalMemory+" to be compliant with the asm.js spec (and given that TOTAL_STACK="+TOTAL_STACK+")"),TOTAL_MEMORY=totalMemory),assert("undefined"!=typeof Int32Array&&"undefined"!=typeof Float64Array&&!!new Int32Array(1).subarray&&!!new Int32Array(1).set,"JS engine does not provide full typed array support"),buffer=new ArrayBuffer(TOTAL_MEMORY),HEAP8=new Int8Array(buffer),HEAP16=new Int16Array(buffer),HEAP32=new Int32Array(buffer),HEAPU8=new Uint8Array(buffer),HEAPU16=new Uint16Array(buffer),HEAPU32=new Uint32Array(buffer),HEAPF32=new Float32Array(buffer),HEAPF64=new Float64Array(buffer),HEAP32[0]=255,assert(255===HEAPU8[0]&&0===HEAPU8[3],"Typed arrays 2 must be run on a little-endian system"),Module.HEAP=HEAP,Module.buffer=buffer,Module.HEAP8=HEAP8,Module.HEAP16=HEAP16,Module.HEAP32=HEAP32,Module.HEAPU8=HEAPU8,Module.HEAPU16=HEAPU16,Module.HEAPU32=HEAPU32,Module.HEAPF32=HEAPF32,Module.HEAPF64=HEAPF64;var __ATPRERUN__=[],__ATINIT__=[],__ATMAIN__=[],__ATEXIT__=[],__ATPOSTRUN__=[],runtimeInitialized=!1,runtimeExited=!1;function preRun(){if(Module.preRun)for("function"==typeof Module.preRun&&(Module.preRun=[Module.preRun]);Module.preRun.length;)addOnPreRun(Module.preRun.shift());callRuntimeCallbacks(__ATPRERUN__)}function ensureInitRuntime(){runtimeInitialized||(runtimeInitialized=!0,callRuntimeCallbacks(__ATINIT__))}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function exitRuntime(){callRuntimeCallbacks(__ATEXIT__),runtimeExited=!0}function postRun(){if(Module.postRun)for("function"==typeof Module.postRun&&(Module.postRun=[Module.postRun]);Module.postRun.length;)addOnPostRun(Module.postRun.shift());callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(e){__ATPRERUN__.unshift(e)}function addOnInit(e){__ATINIT__.unshift(e)}function addOnPreMain(e){__ATMAIN__.unshift(e)}function addOnExit(e){__ATEXIT__.unshift(e)}function addOnPostRun(e){__ATPOSTRUN__.unshift(e)}function intArrayFromString(e,r,n){var t=0<n?n:lengthBytesUTF8(e)+1,i=new Array(t),o=stringToUTF8Array(e,i,0,i.length);return r&&(i.length=o),i}function intArrayToString(e){for(var r=[],n=0;n<e.length;n++){var t=e[n];255<t&&(t&=255),r.push(String.fromCharCode(t))}return r.join("")}function writeStringToMemory(e,r,n){for(var t=intArrayFromString(e,n),i=0;i<t.length;){var o=t[i];HEAP8[r+i>>0]=o,i+=1}}function writeArrayToMemory(e,r){for(var n=0;n<e.length;n++)HEAP8[r++>>0]=e[n]}function writeAsciiToMemory(e,r,n){for(var t=0;t<e.length;++t)HEAP8[r++>>0]=e.charCodeAt(t);n||(HEAP8[r>>0]=0)}function unSign(e,r,n){return 0<=e?e:r<=32?2*Math.abs(1<<r-1)+e:Math.pow(2,r)+e}function reSign(e,r,n){if(e<=0)return e;var t=r<=32?Math.abs(1<<r-1):Math.pow(2,r-1);return t<=e&&(r<=32||t<e)&&(e=-2*t+e),e}Module.addOnPreRun=addOnPreRun,Module.addOnInit=addOnInit,Module.addOnPreMain=addOnPreMain,Module.addOnExit=addOnExit,Module.addOnPostRun=addOnPostRun,Module.intArrayFromString=intArrayFromString,Module.intArrayToString=intArrayToString,Module.writeStringToMemory=writeStringToMemory,Module.writeArrayToMemory=writeArrayToMemory,Module.writeAsciiToMemory=writeAsciiToMemory,Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(e,r){var n=65535&e,t=65535&r;return n*t+((e>>>16)*t+n*(r>>>16)<<16)|0}),Math.imul=Math.imul,Math.clz32||(Math.clz32=function(e){e>>>=0;for(var r=0;r<32;r++)if(e&1<<31-r)return r;return 32}),Math.clz32=Math.clz32;var Math_abs=Math.abs,Math_cos=Math.cos,Math_sin=Math.sin,Math_tan=Math.tan,Math_acos=Math.acos,Math_asin=Math.asin,Math_atan=Math.atan,Math_atan2=Math.atan2,Math_exp=Math.exp,Math_log=Math.log,Math_sqrt=Math.sqrt,Math_ceil=Math.ceil,Math_floor=Math.floor,Math_pow=Math.pow,Math_imul=Math.imul,Math_fround=Math.fround,Math_min=Math.min,Math_clz32=Math.clz32,runDependencies=0,runDependencyWatcher=null,dependenciesFulfilled=null;function getUniqueRunDependency(e){return e}function addRunDependency(e){runDependencies++,Module.monitorRunDependencies&&Module.monitorRunDependencies(runDependencies)}function removeRunDependency(e){if(runDependencies--,Module.monitorRunDependencies&&Module.monitorRunDependencies(runDependencies),0==runDependencies&&(null!==runDependencyWatcher&&(clearInterval(runDependencyWatcher),runDependencyWatcher=null),dependenciesFulfilled)){var r=dependenciesFulfilled;dependenciesFulfilled=null,r()}}Module.addRunDependency=addRunDependency,Module.removeRunDependency=removeRunDependency,Module.preloadedImages={},Module.preloadedAudios={};var memoryInitializer=null,ASM_CONSTS=[];STATIC_BASE=8,STATICTOP=STATIC_BASE+31776,__ATINIT__.push(),allocate([154,14,0,0,188,14,0,0,226,14,0,0,8,15,0,0,46,15,0,0,84,15,0,0,130,15,0,0,208,15,0,0,66,16,0,0,108,16,0,0,42,17,0,0,248,17,0,0,228,18,0,0,240,19,0,0,24,21,0,0,86,22,0,0,238,23,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,13,0,15,0,17,0,19,0,20,0,26,0,31,0,5,0,6,0,5,0,5,0,0,0,0,0,0,0,0,0,1,252,146,252,36,253,182,253,72,254,218,254,108,255,0,0,0,0,32,78,32,78,32,78,32,78,32,78,80,70,0,64,0,32,0,0,0,0,255,127,112,125,112,125,112,125,112,125,112,125,153,89,255,127,112,125,112,125,102,102,102,38,153,25,153,25,154,89,185,62,232,43,188,30,132,21,16,15,139,10,97,7,42,5,157,3,0,96,0,72,0,54,128,40,96,30,200,22,22,17,209,12,157,9,54,7,102,70,184,38,75,21,182,11,113,6,139,3,243,1,18,1,151,0,83,0,154,89,185,62,232,43,188,30,132,21,16,15,139,10,97,7,42,5,157,3,44,3,128,0,30,2,140,0,57,11,111,4,218,8,74,13,19,8,51,2,133,49,135,2,36,16,6,7,225,21,165,20,9,30,118,1,151,14,185,1,160,42,78,10,31,46,190,9,10,80,29,3,98,20,163,2,68,26,162,32,162,20,160,6,208,5,172,1,250,22,196,1,212,20,232,15,255,13,244,4,165,9,133,3,22,62,237,3,134,58,199,12,91,40,250,18,51,14,229,7,36,10,67,3,72,48,28,19,174,47,168,6,120,52,68,6,158,35,37,9,128,15,2,6,103,21,208,38,211,14,161,1,79,5,158,1,56,14,33,6,59,31,213,13,141,44,133,2,104,33,123,2,216,15,97,5,224,64,236,23,156,44,188,2,215,7,95,2,127,48,42,6,111,43,46,18,112,53,172,6,214,46,205,4,60,31,129,28,175,51,83,22,124,9,135,4,25,8,149,7,74,24,233,23,218,13,12,7,221,34,10,7,231,33,44,6,111,54,248,13,1,52,93,24,254,23,106,4,106,23,198,6,61,55,54,18,7,44,249,12,194,47,15,6,107,54,199,11,217,19,224,40,228,36,50,26,153,6,171,2,156,5,26,5,44,28,93,15,242,15,153,10,113,30,192,2,222,58,34,3,155,24,92,20,241,16,237,20,20,26,29,2,174,23,114,2,83,53,116,14,234,44,104,9,28,63,204,2,145,47,239,2,129,31,225,44,170,24,208,8,114,17,240,1,125,28,11,2,229,39,249,14,202,32,221,11,211,32,198,3,148,55,88,7,255,33,33,21,11,64,255,18,252,28,187,7,201,23,206,4,155,36,46,17,222,56,35,13,247,52,57,11,107,51,185,5,158,21,142,6,82,51,179,57,170,28,88,2,38,5,36,2,156,16,211,13,60,39,60,9,91,41,110,2,32,51,157,2,46,55,198,13,175,19,56,38,234,59,107,2,43,12,78,2,58,64,197,11,182,60,72,16,177,60,75,6,45,60,204,4,151,62,83,36,110,29,112,19,198,7,189,4,183,44,133,4,224,48,143,21,3,37,84,10,36,30,242,7,224,51,191,8,139,62,229,19,130,31,105,26,99,39,133,5,138,19,43,9,235,48,87,23,22,59,83,11,88,71,241,8,211,61,223,9,137,63,14,40,59,57,55,44,5,7,81,1,43,12,141,1,182,13,112,11,240,17,110,10,95,29,116,2,151,44,144,2,58,23,131,9,144,25,199,28,46,32,61,3,160,15,95,3,48,39,188,9,185,62,223,13,28,71,30,4,215,23,174,5,252,22,220,30,64,73,140,13,72,7,32,2,238,35,171,2,103,45,64,16,242,17,108,6,86,12,133,4,81,62,0,10,61,48,149,14,12,68,140,20,218,23,212,7,101,11,206,6,83,64,137,20,147,65,144,6,53,67,223,6,165,18,159,12,218,28,147,23,6,56,28,39,195,15,186,1,98,16,202,1,254,35,194,8,3,29,121,16,60,50,33,3,178,43,57,3,104,49,36,8,156,50,154,25,33,37,228,3,229,25,217,3,41,41,198,9,185,59,142,19,58,49,7,8,124,60,117,6,66,63,9,27,151,55,158,22,66,10,60,3,239,21,150,6,95,53,146,22,84,14,18,6,49,44,73,10,42,38,179,5,179,54,125,18,25,62,147,24,134,24,78,7,230,30,237,8,82,66,219,17,192,64,9,15,144,59,7,9,151,62,172,12,123,56,144,69,71,46,203,10,189,7,127,5,120,5,108,3,239,16,219,13,39,17,114,16,29,21,168,2,53,68,13,3,101,25,254,19,155,31,253,29,187,28,26,3,141,32,158,4,193,58,88,12,80,58,223,11,197,79,112,3,209,56,84,3,49,48,116,57,248,26,128,7,129,16,165,3,26,32,63,4,163,41,244,15,98,39,181,17,175,10,72,3,177,80,57,4,71,65,78,23,1,62,226,17,119,42,14,10,189,14,142,4,183,56,204,15,219,80,67,10,115,59,174,10,170,59,138,8,113,24,154,12,69,51,24,76,28,28,162,3,158,9,82,6,163,17,20,12,28,54,181,16,220,40,65,3,187,67,42,3,251,65,241,8,186,60,25,32,35,53,148,6,125,12,42,7,76,62,4,11,196,61,207,20,110,66,134,9,148,65,46,5,55,61,220,31,206,45,108,33,178,14,5,8,91,37,37,5,249,52,134,26,195,47,144,7,244,31,222,13,231,51,242,6,171,63,199,25,163,63,78,30,73,33,247,9,57,28,85,10,93,71,65,29,245,65,200,8,218,69,68,11,113,67,0,13,201,36,194,78,34,43,128,32,6,5,108,2,151,5,71,2,105,23,241,8,138,15,42,14,24,20,240,2,97,52,62,3,177,21,44,11,244,45,20,23,241,41,48,2,70,21,52,2,9,52,192,11,170,46,99,14,175,77,30,3,97,38,216,2,95,53,44,34,223,28,237,11,211,9,10,3,162,23,65,3,69,25,210,19,113,32,159,9,253,23,73,7,204,59,238,4,72,56,195,17,95,53,163,17,65,12,167,11,175,9,235,4,240,58,39,18,22,60,47,10,156,56,88,9,174,48,233,9,115,29,133,11,109,50,28,47,92,21,172,2,69,12,210,2,217,19,250,4,188,49,104,16,198,59,169,2,139,30,80,2,134,25,229,7,94,64,33,34,52,52,114,3,21,21,131,3,64,57,130,8,149,57,131,16,190,55,18,5,105,54,237,7,117,60,58,29,199,61,220,17,217,9,221,7,198,19,12,7,39,20,182,25,218,27,13,14,168,42,75,6,209,45,172,6,7,66,127,13,140,63,240,25,90,36,239,3,153,36,58,8,238,74,173,19,153,48,173,16,47,62,52,5,253,59,184,13,122,46,61,55,229,62,198,26,218,7,225,2,195,14,93,3,190,44,64,11,236,13,212,13,97,35,217,4,103,48,128,3,98,33,21,18,41,45,144,22,193,31,77,2,26,32,76,2,40,73,171,14,173,50,77,12,113,61,246,2,250,64,242,2,118,59,130,43,255,61,160,8,65,18,98,2,234,39,166,2,153,59,50,16,97,22,255,12,185,32,134,6,150,77,17,9,90,60,135,21,230,54,105,21,96,22,72,11,156,29,66,5,48,56,205,20,108,63,110,15,14,59,160,14,202,59,155,5,5,57,230,15,13,48,80,61,193,29,163,6,122,8,116,3,107,17,215,17,174,70,234,12,198,49,47,3,78,58,139,3,168,58,185,16,158,60,176,32,74,70,63,4,54,9,97,3,153,63,203,14,63,61,244,17,228,63,254,5,200,64,162,8,193,65,225,37,57,62,161,17,205,12,61,4,171,37,139,8,197,46,180,23,239,35,110,17,251,34,93,6,49,40,246,11,97,64,35,20,106,60,154,27,110,53,239,9,153,20,229,8,106,65,69,24,15,65,80,13,80,79,35,13,0,73,193,7,92,55,67,50,50,59,87,61,121,17,252,3,145,6,118,3,215,16,205,16,248,34,73,14,5,23,123,4,127,45,172,5,14,62,179,8,230,17,244,25,17,27,181,4,76,24,31,3,127,48,81,13,96,62,37,15,147,77,61,8,217,37,93,8,150,57,126,34,144,56,39,10,25,7,214,4,91,30,45,3,135,74,58,17,178,21,16,8,103,14,28,11,27,68,208,8,57,65,134,17,71,63,12,21,92,31,203,10,77,13,71,8,18,68,101,21,130,53,226,10,167,77,160,10,138,35,40,15,252,70,225,18,184,67,175,47,252,19,228,3,71,19,220,3,160,38,9,12,126,23,251,20,9,62,131,6,213,32,159,4,239,58,62,9,65,77,90,27,187,46,26,6,111,28,104,4,219,65,252,5,146,61,5,21,116,57,17,8,137,78,107,8,6,67,53,32,247,69,174,24,91,21,224,5,4,16,14,10,13,68,154,26,41,22,72,11,252,64,54,13,15,35,39,7,191,78,129,18,94,76,126,28,2,26,221,10,208,44,249,12,197,75,190,19,190,73,114,18,55,64,69,9,206,79,34,17,89,44,158,103,73,45,252,11,50,11,30,6,244,19,46,4,142,37,51,19,75,19,208,13,117,29,110,3,237,80,83,3,26,27,43,17,159,65,53,30,153,39,251,3,117,38,196,3,134,60,115,15,99,60,102,13,175,73,214,3,152,78,195,3,236,65,87,50,254,55,104,16,199,25,196,4,6,36,46,3,46,66,14,20,29,22,34,19,112,21,6,7,34,79,122,15,109,66,34,24,9,70,41,23,149,36,92,13,50,29,179,7,81,76,57,20,59,74,190,11,70,64,204,14,198,62,63,9,216,33,183,10,229,36,246,102,104,42,7,5,227,13,241,3,230,21,38,14,253,75,136,21,165,48,29,3,154,80,143,3,67,60,250,11,141,66,35,40,195,73,73,10,73,15,244,4,63,76,43,13,132,70,110,20,91,75,142,6,52,76,100,12,152,70,2,42,241,64,189,26,62,12,250,8,117,42,133,9,220,60,1,27,53,49,53,13,108,43,225,12,122,65,120,9,165,73,59,26,19,67,159,38,199,49,45,10,233,34,68,12,89,74,84,30,171,71,40,15,251,79,98,14,146,76,52,13,244,50,173,75,30,41,84,90,1,0,3,0,0,0,1,0,2,0,4,0,82,120,26,113,81,106,240,99,241,93,78,88,2,83,7,78,89,73,242,68,51,115,174,103,80,93,251,83,149,75,6,68,56,61,25,55,150,49,161,44,205,76,21,46,166,27,151,16,244,9,249,5,149,3,38,2,74,1,198,0,249,79,26,80,59,80,92,80,125,80,164,80,197,80,236,80,13,81,52,81,85,81,124,81,157,81,196,81,236,81,19,82,58,82,97,82,137,82,176,82,215,82,255,82,38,83,84,83,123,83,169,83,208,83,254,83,38,84,84,84,129,84,175,84,221,84,11,85,57,85,103,85,149,85,201,85,247,85,43,86,89,86,142,86,194,86,247,86,43,87,95,87,148,87,200,87,3,88,56,88,115,88,174,88,233,88,36,89,95,89,154,89,219,89,22,90,88,90,153,90,212,90,28,91,94,91,159,91,231,91,48,92,113,92,192,92,8,93,80,93,159,93,237,93,60,94,138,94,224,94,46,95,131,95,217,95,52,96,138,96,229,96,72,97,163,97,6,98,104,98,209,98,51,99,156,99,11,100,123,100,234,100,96,101,214,101,76,102,201,102,76,103,207,103,82,104,220,104,108,105,252,105,147,106,48,107,205,107,113,108,27,109,204,109,125,110,59,111,249,111,197,112,150,113,111,114,84,115,64,116,50,117,50,118,63,119,88,120,225,122,255,127,255,127,255,127,255,127,255,127,255,127,255,127,225,122,88,120,63,119,50,118,50,117,64,116,84,115,111,114,150,113,197,112,249,111,59,111,125,110,204,109,27,109,113,108,205,107,48,107,147,106,252,105,108,105,220,104,82,104,207,103,76,103,201,102,76,102,214,101,96,101,234,100,123,100,11,100,156,99,51,99,209,98,104,98,6,98,163,97,72,97,229,96,138,96,52,96,217,95,131,95,46,95,224,94,138,94,60,94,237,93,159,93,80,93,8,93,192,92,113,92,48,92,231,91,159,91,94,91,28,91,212,90,153,90,88,90,22,90,219,89,154,89,95,89,36,89,233,88,174,88,115,88,56,88,3,88,200,87,148,87,95,87,43,87,247,86,194,86,142,86,89,86,43,86,247,85,201,85,149,85,103,85,57,85,11,85,221,84,175,84,129,84,84,84,38,84,254,83,208,83,169,83,123,83,84,83,38,83,255,82,215,82,176,82,137,82,97,82,58,82,19,82,236,81,196,81,157,81,124,81,85,81,52,81,13,81,236,80,197,80,164,80,125,80,92,80,59,80,26,80,249,79,210,79,177,79,145,79,112,79,13,0,14,0,16,0,18,0,20,0,21,0,27,0,32,0,6,0,7,0,6,0,6,0,0,0,0,0,0,0,1,0,13,0,14,0,16,0,18,0,19,0,21,0,26,0,31,0,6,0,6,0,6,0,6,0,0,0,0,0,0,0,1,0,79,115,156,110,74,97,126,77,72,54,9,31,195,10,153,251,125,242,48,239,127,240,173,244,231,249,176,254,22,2,202,3,255,3,55,3,4,2,220,0,0,0,125,255,62,255,41,255,0,0,216,127,107,127,182,126,187,125,123,124,248,122,53,121,53,119,250,116,137,114,128,46,128,67,0,120,0,101,128,94,64,113,64,95,192,28,64,76,192,57,84,0,1,0,254,255,2,0,5,0,10,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,5,0,10,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,10,0,19,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,94,0,0,0,253,255,3,0,3,0,6,0,5,0,9,0,18,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,17,0,19,0,19,0,19,0,19,0,23,0,39,0,57,0,5,0,8,0,8,0,7,0,8,0,7,0,2,0,8,0,4,0,7,0,2,0,4,0,7,0,2,0,8,0,4,0,7,0,2,0,8,0,8,0,7,0,8,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,8,0,9,0,9,0,8,0,9,0,2,0,6,0,4,0,9,0,2,0,6,0,8,0,9,0,2,0,6,0,4,0,9,0,2,0,6,0,8,0,9,0,9,0,8,0,11,0,3,0,7,0,4,0,11,0,3,0,7,0,8,0,11,0,3,0,7,0,4,0,11,0,3,0,7,0,8,0,9,0,9,0,8,0,13,0,4,0,7,0,5,0,13,0,4,0,7,0,8,0,13,0,4,0,7,0,5,0,13,0,4,0,7,0,9,0,9,0,9,0,8,0,13,0,4,0,4,0,5,0,6,0,13,0,4,0,4,0,5,0,8,0,13,0,4,0,4,0,5,0,6,0,13,0,4,0,4,0,5,0,8,0,9,0,9,0,8,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,5,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,8,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,5,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,7,0,8,0,9,0,8,0,6,0,9,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,6,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,9,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,6,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,3,0,8,0,9,0,9,0,6,0,95,0,103,0,118,0,134,0,148,0,159,0,204,0,244,0,39,0,43,0,38,0,37,0,0,0,0,0,0,0,0,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,15,0,23,0,24,0,25,0,26,0,27,0,28,0,48,0,49,0,61,0,62,0,82,0,83,0,47,0,46,0,45,0,44,0,81,0,80,0,79,0,78,0,17,0,18,0,20,0,22,0,77,0,76,0,75,0,74,0,29,0,30,0,43,0,42,0,41,0,40,0,38,0,39,0,16,0,19,0,21,0,50,0,51,0,59,0,60,0,63,0,64,0,72,0,73,0,84,0,85,0,93,0,94,0,32,0,33,0,35,0,36,0,53,0,54,0,56,0,57,0,66,0,67,0,69,0,70,0,87,0,88,0,90,0,91,0,34,0,55,0,68,0,89,0,37,0,58,0,71,0,92,0,31,0,52,0,65,0,86,0,7,0,6,0,5,0,4,0,3,0,2,0,1,0,0,0,15,0,14,0,13,0,12,0,11,0,10,0,9,0,8,0,23,0,24,0,25,0,26,0,27,0,46,0,65,0,84,0,45,0,44,0,43,0,64,0,63,0,62,0,83,0,82,0,81,0,102,0,101,0,100,0,42,0,61,0,80,0,99,0,28,0,47,0,66,0,85,0,18,0,41,0,60,0,79,0,98,0,29,0,48,0,67,0,17,0,20,0,22,0,40,0,59,0,78,0,97,0,21,0,30,0,49,0,68,0,86,0,19,0,16,0,87,0,39,0,38,0,58,0,57,0,77,0,35,0,54,0,73,0,92,0,76,0,96,0,95,0,36,0,55,0,74,0,93,0,32,0,51,0,33,0,52,0,70,0,71,0,89,0,90,0,31,0,50,0,69,0,88,0,37,0,56,0,75,0,94,0,34,0,53,0,72,0,91,0,0,0,1,0,4,0,5,0,3,0,6,0,7,0,2,0,13,0,15,0,8,0,9,0,11,0,12,0,14,0,10,0,16,0,28,0,74,0,29,0,75,0,27,0,73,0,26,0,72,0,30,0,76,0,51,0,97,0,50,0,71,0,96,0,117,0,31,0,77,0,52,0,98,0,49,0,70,0,95,0,116,0,53,0,99,0,32,0,78,0,33,0,79,0,48,0,69,0,94,0,115,0,47,0,68,0,93,0,114,0,46,0,67,0,92,0,113,0,19,0,21,0,23,0,22,0,18,0,17,0,20,0,24,0,111,0,43,0,89,0,110,0,64,0,65,0,44,0,90,0,25,0,45,0,66,0,91,0,112,0,54,0,100,0,40,0,61,0,86,0,107,0,39,0,60,0,85,0,106,0,36,0,57,0,82,0,103,0,35,0,56,0,81,0,102,0,34,0,55,0,80,0,101,0,42,0,63,0,88,0,109,0,41,0,62,0,87,0,108,0,38,0,59,0,84,0,105,0,37,0,58,0,83,0,104,0,0,0,1,0,4,0,3,0,5,0,6,0,13,0,7,0,2,0,8,0,9,0,11,0,15,0,12,0,14,0,10,0,28,0,82,0,29,0,83,0,27,0,81,0,26,0,80,0,30,0,84,0,16,0,55,0,109,0,56,0,110,0,31,0,85,0,57,0,111,0,48,0,73,0,102,0,127,0,32,0,86,0,51,0,76,0,105,0,130,0,52,0,77,0,106,0,131,0,58,0,112,0,33,0,87,0,19,0,23,0,53,0,78,0,107,0,132,0,21,0,22,0,18,0,17,0,20,0,24,0,25,0,50,0,75,0,104,0,129,0,47,0,72,0,101,0,126,0,54,0,79,0,108,0,133,0,46,0,71,0,100,0,125,0,128,0,103,0,74,0,49,0,45,0,70,0,99,0,124,0,42,0,67,0,96,0,121,0,39,0,64,0,93,0,118,0,38,0,63,0,92,0,117,0,35,0,60,0,89,0,114,0,34,0,59,0,88,0,113,0,44,0,69,0,98,0,123,0,43,0,68,0,97,0,122,0,41,0,66,0,95,0,120,0,40,0,65,0,94,0,119,0,37,0,62,0,91,0,116,0,36,0,61,0,90,0,115,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,15,0,16,0,26,0,87,0,27,0,88,0,28,0,89,0,29,0,90,0,30,0,91,0,51,0,80,0,112,0,141,0,52,0,81,0,113,0,142,0,54,0,83,0,115,0,144,0,55,0,84,0,116,0,145,0,58,0,119,0,59,0,120,0,21,0,22,0,23,0,17,0,18,0,19,0,31,0,60,0,92,0,121,0,56,0,85,0,117,0,146,0,20,0,24,0,25,0,50,0,79,0,111,0,140,0,57,0,86,0,118,0,147,0,49,0,78,0,110,0,139,0,48,0,77,0,53,0,82,0,114,0,143,0,109,0,138,0,47,0,76,0,108,0,137,0,32,0,33,0,61,0,62,0,93,0,94,0,122,0,123,0,41,0,42,0,43,0,44,0,45,0,46,0,70,0,71,0,72,0,73,0,74,0,75,0,102,0,103,0,104,0,105,0,106,0,107,0,131,0,132,0,133,0,134,0,135,0,136,0,34,0,63,0,95,0,124,0,35,0,64,0,96,0,125,0,36,0,65,0,97,0,126,0,37,0,66,0,98,0,127,0,38,0,67,0,99,0,128,0,39,0,68,0,100,0,129,0,40,0,69,0,101,0,130,0,8,0,7,0,6,0,5,0,4,0,3,0,2,0,14,0,16,0,9,0,10,0,12,0,13,0,15,0,11,0,17,0,20,0,22,0,24,0,23,0,19,0,18,0,21,0,56,0,88,0,122,0,154,0,57,0,89,0,123,0,155,0,58,0,90,0,124,0,156,0,52,0,84,0,118,0,150,0,53,0,85,0,119,0,151,0,27,0,93,0,28,0,94,0,29,0,95,0,30,0,96,0,31,0,97,0,61,0,127,0,62,0,128,0,63,0,129,0,59,0,91,0,125,0,157,0,32,0,98,0,64,0,130,0,1,0,0,0,25,0,26,0,33,0,99,0,34,0,100,0,65,0,131,0,66,0,132,0,54,0,86,0,120,0,152,0,60,0,92,0,126,0,158,0,55,0,87,0,121,0,153,0,117,0,116,0,115,0,46,0,78,0,112,0,144,0,43,0,75,0,109,0,141,0,40,0,72,0,106,0,138,0,36,0,68,0,102,0,134,0,114,0,149,0,148,0,147,0,146,0,83,0,82,0,81,0,80,0,51,0,50,0,49,0,48,0,47,0,45,0,44,0,42,0,39,0,35,0,79,0,77,0,76,0,74,0,71,0,67,0,113,0,111,0,110,0,108,0,105,0,101,0,145,0,143,0,142,0,140,0,137,0,133,0,41,0,73,0,107,0,139,0,37,0,69,0,103,0,135,0,38,0,70,0,104,0,136,0,7,0,6,0,5,0,4,0,3,0,2,0,1,0,0,0,16,0,15,0,14,0,13,0,12,0,11,0,10,0,9,0,8,0,26,0,27,0,28,0,29,0,30,0,31,0,115,0,116,0,117,0,118,0,119,0,120,0,72,0,73,0,161,0,162,0,65,0,68,0,69,0,108,0,111,0,112,0,154,0,157,0,158,0,197,0,200,0,201,0,32,0,33,0,121,0,122,0,74,0,75,0,163,0,164,0,66,0,109,0,155,0,198,0,19,0,23,0,21,0,22,0,18,0,17,0,20,0,24,0,25,0,37,0,36,0,35,0,34,0,80,0,79,0,78,0,77,0,126,0,125,0,124,0,123,0,169,0,168,0,167,0,166,0,70,0,67,0,71,0,113,0,110,0,114,0,159,0,156,0,160,0,202,0,199,0,203,0,76,0,165,0,81,0,82,0,92,0,91,0,93,0,83,0,95,0,85,0,84,0,94,0,101,0,102,0,96,0,104,0,86,0,103,0,87,0,97,0,127,0,128,0,138,0,137,0,139,0,129,0,141,0,131,0,130,0,140,0,147,0,148,0,142,0,150,0,132,0,149,0,133,0,143,0,170,0,171,0,181,0,180,0,182,0,172,0,184,0,174,0,173,0,183,0,190,0,191,0,185,0,193,0,175,0,192,0,176,0,186,0,38,0,39,0,49,0,48,0,50,0,40,0,52,0,42,0,41,0,51,0,58,0,59,0,53,0,61,0,43,0,60,0,44,0,54,0,194,0,179,0,189,0,196,0,177,0,195,0,178,0,187,0,188,0,151,0,136,0,146,0,153,0,134,0,152,0,135,0,144,0,145,0,105,0,90,0,100,0,107,0,88,0,106,0,89,0,98,0,99,0,62,0,47,0,57,0,64,0,45,0,63,0,46,0,55,0,56,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,23,0,15,0,16,0,17,0,18,0,19,0,20,0,21,0,22,0,24,0,25,0,26,0,27,0,28,0,38,0,141,0,39,0,142,0,40,0,143,0,41,0,144,0,42,0,145,0,43,0,146,0,44,0,147,0,45,0,148,0,46,0,149,0,47,0,97,0,150,0,200,0,48,0,98,0,151,0,201,0,49,0,99,0,152,0,202,0,86,0,136,0,189,0,239,0,87,0,137,0,190,0,240,0,88,0,138,0,191,0,241,0,91,0,194,0,92,0,195,0,93,0,196,0,94,0,197,0,95,0,198,0,29,0,30,0,31,0,32,0,33,0,34,0,35,0,50,0,100,0,153,0,203,0,89,0,139,0,192,0,242,0,51,0,101,0,154,0,204,0,55,0,105,0,158,0,208,0,90,0,140,0,193,0,243,0,59,0,109,0,162,0,212,0,63,0,113,0,166,0,216,0,67,0,117,0,170,0,220,0,36,0,37,0,54,0,53,0,52,0,58,0,57,0,56,0,62,0,61,0,60,0,66,0,65,0,64,0,70,0,69,0,68,0,104,0,103,0,102,0,108,0,107,0,106,0,112,0,111,0,110,0,116,0,115,0,114,0,120,0,119,0,118,0,157,0,156,0,155,0,161,0,160,0,159,0,165,0,164,0,163,0,169,0,168,0,167,0,173,0,172,0,171,0,207,0,206,0,205,0,211,0,210,0,209,0,215,0,214,0,213,0,219,0,218,0,217,0,223,0,222,0,221,0,73,0,72,0,71,0,76,0,75,0,74,0,79,0,78,0,77,0,82,0,81,0,80,0,85,0,84,0,83,0,123,0,122,0,121,0,126,0,125,0,124,0,129,0,128,0,127,0,132,0,131,0,130,0,135,0,134,0,133,0,176,0,175,0,174,0,179,0,178,0,177,0,182,0,181,0,180,0,185,0,184,0,183,0,188,0,187,0,186,0,226,0,225,0,224,0,229,0,228,0,227,0,232,0,231,0,230,0,235,0,234,0,233,0,238,0,237,0,236,0,96,0,199,0,0,0,2,0,0,0,3,0,0,0,2,0,0,0,3,0,1,0,3,0,2,0,4,0,1,0,4,0,1,0,4,0,0,0,205,12,156,25,0,32,102,38,205,44,0,48,51,51,102,54,154,57,205,60,0,64,51,67,102,70,154,73,205,76,159,0,64,241,53,167,206,0,190,242,52,176,12,1,67,244,88,185,93,1,201,245,133,194,163,1,215,246,223,200,226,1,166,247,189,205,42,2,116,248,147,210,125,2,66,249,109,215,221,2,18,250,77,220,74,3,222,250,30,225,201,3,174,251,0,230,90,4,124,252,216,234,1,5,74,253,179,239,193,5,25,254,141,244,158,6,231,254,104,249,156,7,181,255,67,254,193,8,133,0,33,3,17,10,83,1,252,7,147,11,33,2,213,12,80,13,240,2,178,17,79,15,190,3,140,22,155,17,141,4,104,27,63,20,91,5,67,32,72,23,41,6,29,37,199,26,248,6,249,41,203,30,199,7,212,46,105,35,149,8,175,51,185,40,100,9,138,56,222,48,113,10,224,62,135,63,244,11,253,71,150,82,120,13,27,81,93,107,252,14,57,90,93,107,252,14,57,90,0,0,1,0,3,0,2,0,6,0,4,0,5,0,7,0,0,0,1,0,3,0,2,0,5,0,6,0,4,0,7,0,248,127,211,127,76,127,108,126,51,125,163,123,188,121,127,119,239,116,12,114,217,110,89,107,141,103,121,99,31,95,130,90,166,85,141,80,60,75,182,69,0,64,28,58,15,52,223,45,141,39,32,33,156,26,6,20,97,13,178,6,0,0,78,249,159,242,250,235,100,229,224,222,115,216,33,210,241,203,228,197,0,192,74,186,196,180,115,175,90,170,126,165,225,160,135,156,115,152,167,148,39,145,244,141,17,139,129,136,68,134,93,132,205,130,148,129,180,128,45,128,8,128,255,127,46,124,174,120,118,117,125,114,186,111,41,109,194,106,131,104,102,102,105,100,137,98,194,96,19,95,122,93,245,91,130,90,33,89,207,87,139,86,85,85,44,84,15,83,252,81,244,80,246,79,1,79,20,78,48,77,83,76,126,75,175,74,231,73,37,73,104,72,178,71,0,71,84,70,173,69,10,69,107,68,209,67,59,67,168,66,25,66,142,65,6,65,130,64,0,64,0,0,175,5,50,11,140,16,192,21,207,26,188,31,136,36,53,41,196,45,55,50,143,54,206,58,245,62,4,67,252,70,223,74,174,78,105,82,17,86,167,89,44,93,159,96,3,100,87,103,155,106,209,109,250,112,20,116,33,119,34,122,23,125,255,127,255,127,217,127,98,127,157,126,138,125,42,124,125,122,133,120,66,118,182,115,227,112,202,109,110,106,208,102,242,98,215,94,130,90,246,85,52,81,64,76,29,71,206,65,87,60,186,54,252,48,31,43,40,37,26,31,249,24,200,18,140,12,72,6,0,0,184,249,116,243,56,237,7,231,230,224,216,218,225,212,4,207,70,201,169,195,50,190,227,184,192,179,204,174,10,170,126,165,41,161,14,157,48,153,146,149,54,146,29,143,74,140,190,137,123,135,131,133,214,131,118,130,99,129,158,128,39,128,0,128,249,150,148,221,53,235,27,241,93,244,116,246,223,247,237,248,184,249,86,250,214,250,61,251,148,251,221,251,26,252,78,252,123,252,163,252,197,252,227,252,252,252,18,253,38,253,55,253,69,253,81,253,91,253,100,253,106,253,111,253,114,253,116,253,116,253,114,253,111,253,106,253,100,253,91,253,81,253,69,253,55,253,38,253,18,253,252,252,227,252,197,252,163,252,123,252,78,252,26,252,221,251,148,251,61,251,214,250,86,250,184,249,237,248,223,247,116,246,93,244,27,241,53,235,148,221,249,150,48,117,144,101,8,82,152,58,64,31,0,0,192,224,104,197,248,173,112,154,153,104,33,3,201,9,85,253,154,250,70,2,92,2,6,251,183,13,250,232,182,17,13,254,108,248,195,11,62,236,238,21,58,248,219,251,77,250,90,17,68,253,41,235,1,18,196,1,179,253,232,242,137,11,243,4,68,251,226,245,195,6,86,14,133,238,49,252,39,17,23,246,181,3,173,250,45,252,102,22,66,118,247,14,60,240,156,11,232,251,22,252,173,9,29,244,255,10,73,247,217,6,181,249,178,6,17,249,7,6,16,252,173,1,87,255,216,1,16,251,128,8,110,245,219,9,171,249,88,1,58,3,7,250,188,6,135,249,165,6,241,247,84,10,12,244,81,11,70,248,45,2,12,3,167,250,74,3,143,2,98,57,254,44,244,4,55,245,217,233,90,29,221,255,9,245,32,244,215,18,136,11,24,223,201,14,175,5,131,8,67,222,115,31,201,247,82,250,9,3,84,4,175,246,206,8,149,254,94,253,201,247,158,23,207,233,48,4,51,12,62,236,192,20,231,246,112,241,12,27,207,240,163,2,17,249,29,0,161,39,66,118,247,14,60,240,156,11,232,251,22,252,173,9,29,244,255,10,73,247,217,6,181,249,178,6,17,249,7,6,16,252,173,1,87,255,216,1,16,251,128,8,110,245,219,9,171,249,88,1,58,3,7,250,188,6,135,249,165,6,241,247,84,10,12,244,81,11,70,248,45,2,12,3,167,250,74,3,143,2,0,64,103,65,213,66,76,68,203,69,82,71,226,72,122,74,28,76,199,77,123,79,56,81,255,82,209,84,172,86,146,88,130,90,126,92,132,94,150,96,180,98,221,100,18,103,84,105,162,107,254,109,102,112,221,114,96,117,242,119,147,122,66,125,255,127,3,115,186,110,119,98,225,79,109,57,245,33,71,12,184,250,206,238,23,233,38,233,191,237,33,245,96,253,187,4,232,9,58,12,175,11,211,8,146,4,0,0,23,252,140,249,180,248,126,249,133,251,48,254,218,0,244,2,36,4,75,4,136,3,38,2,135,0,11,255,254,253,134,253,166,253,61,254,25,255,0,0,191,0,52,1,84,1,40,1,198,0,78,0,220,255,136,255,93,255,91,255,124,255,177,255,237,255,34,0,73,0,91,0,89,0,70,0,38,0,0,0,254,254,194,254,73,254,134,253,112,253,251,252,57,253,10,254,244,254,63,255,254,255,125,0,122,0,217,255,247,255,105,0,129,0,27,1,116,1,63,2,235,254,188,254,59,255,25,254,67,254,150,254,220,254,229,255,177,0,31,2,86,1,5,2,4,2,130,0,27,0,152,255,136,255,116,255,182,255,200,255,204,253,81,252,16,250,59,252,210,252,242,253,190,254,254,255,159,0,145,2,200,254,228,254,126,254,171,253,19,254,242,253,94,254,27,255,105,0,193,1,211,253,154,252,205,251,105,252,74,252,16,253,59,253,196,254,62,0,230,1,198,254,65,255,53,255,182,254,96,255,153,255,205,255,131,0,82,1,3,2,10,6,224,8,194,14,112,21,60,27,190,32,63,39,221,43,222,49,146,53,84,37,17,42,27,49,236,51,45,56,131,45,92,41,39,38,145,33,84,25,6,0,82,0,125,255,154,0,200,255,33,253,183,0,191,255,247,254,9,0,46,255,151,254,113,0,206,2,25,7,242,3,190,4,37,6,89,3,53,5,228,8,59,3,32,6,141,7,205,2,197,7,158,8,70,3,148,4,31,7,209,2,232,3,106,8,30,1,220,1,229,5,9,255,237,253,230,0,147,0,174,255,57,2,26,0,79,255,80,252,229,255,239,254,180,2,92,255,248,254,73,255,224,0,22,3,15,4,131,3,178,3,89,2,229,1,3,3,126,4,12,2,165,2,135,3,116,255,119,1,10,3,154,1,164,2,173,1,45,1,18,2,241,3,207,2,134,2,38,0,226,0,111,1,40,0,145,0,211,255,7,254,34,1,121,0,135,255,46,1,127,0,166,0,132,255,129,254,68,252,154,254,57,254,47,252,203,2,110,3,126,3,210,3,155,3,211,0,221,1,16,1,64,0,188,0,178,255,17,0,113,255,191,255,38,0,131,2,74,2,109,2,122,255,86,254,117,253,91,1,33,2,4,11,164,4,166,10,138,9,142,0,176,255,199,6,27,1,130,0,205,1,250,254,113,254,135,251,101,254,155,0,174,1,73,1,119,1,11,3,53,0,30,255,117,255,127,255,20,255,146,6,29,1,232,2,47,5,226,2,185,2,128,6,56,1,153,1,10,1,69,1,208,2,135,0,1,0,221,0,197,1,8,0,203,0,145,0,43,1,128,2,248,2,29,0,212,1,126,2,103,0,173,1,123,1,164,1,186,3,164,3,46,5,186,4,234,4,192,2,244,3,128,4,90,255,68,254,246,254,196,254,126,255,136,254,191,0,127,4,112,7,16,255,225,253,20,251,144,255,12,1,183,4,70,0,38,4,47,6,22,1,80,5,38,6,254,254,240,254,0,253,19,0,51,2,192,8,253,255,247,254,135,0,217,254,177,253,124,254,140,0,98,1,50,255,252,254,8,254,229,252,79,254,50,253,217,250,109,0,75,1,194,3,83,254,169,255,140,2,216,254,170,1,251,3,17,255,7,3,83,3,233,1,54,5,49,4,178,254,180,254,25,0,31,2,182,4,15,7,70,1,61,0,215,2,66,2,81,3,125,5,48,255,235,254,73,1,104,255,64,0,157,2,78,254,90,253,41,253,58,254,185,255,251,0,93,2,224,1,254,0,30,254,11,0,228,3,223,254,139,1,230,1,210,2,25,4,160,5,226,255,196,254,238,252,150,255,141,255,149,253,93,3,194,5,132,5,31,4,86,5,160,4,44,3,213,4,157,3,42,0,5,255,192,253,86,1,141,0,58,254,88,255,176,255,79,5,170,254,112,253,29,249,100,0,53,3,213,2,222,3,235,2,32,3,76,1,184,1,56,2,151,2,123,1,84,3,112,0,165,0,143,254,85,2,142,3,26,1,248,255,66,3,1,5,160,254,60,2,183,2,206,1,198,8,14,7,89,1,190,0,94,5,160,1,147,3,118,8,168,0,174,255,24,1,252,253,66,254,72,3,47,0,21,2,44,0,150,254,57,253,137,251,22,0,193,0,192,5,171,255,233,0,21,7,194,255,67,2,224,5,38,2,176,3,213,6,211,2,138,2,124,4,204,3,116,3,115,5,87,254,131,2,0,0,232,3,184,3,74,4,249,0,166,5,160,2,178,254,169,255,124,8,214,253,90,7,112,10,140,0,34,7,61,7,152,3,213,6,30,10,52,4,141,7,246,7,119,255,69,254,237,249,245,4,150,4,212,1,19,254,134,255,241,5,61,254,9,4,190,4,226,1,159,6,94,4,47,3,137,2,128,1,66,254,76,253,107,0,193,254,163,253,138,255,49,255,7,254,13,2,44,254,244,255,176,10,75,0,142,7,25,5,112,3,54,9,219,8,5,5,39,6,212,7,208,255,208,254,94,251,77,254,51,254,5,255,146,254,108,254,221,253,223,254,163,253,171,253,230,253,214,252,91,255,136,255,3,0,100,1,127,2,217,4,222,5,96,0,177,0,238,2,77,254,183,253,106,251,156,254,109,0,177,255,27,254,32,1,213,7,9,0,92,4,219,2,112,3,86,8,178,3,247,254,49,6,41,4,133,4,186,4,75,3,14,254,100,253,175,1,118,1,65,1,27,255,160,5,53,8,101,5,193,1,205,1,131,4,151,255,39,0,128,254,249,254,111,1,182,0,141,254,108,253,5,3,68,255,127,4,203,3,53,5,96,6,155,5,6,3,243,4,197,4,30,254,192,252,47,250,19,255,46,255,92,3,122,3,79,6,40,4,216,1,38,4,168,4,185,0,53,4,221,3,200,253,32,252,88,249,63,254,122,252,5,248,114,255,135,254,54,254,46,255,214,253,251,251,245,255,109,4,217,8,183,254,93,253,131,252,6,255,145,2,163,4,7,2,230,5,243,6,8,2,27,2,123,5,15,2,141,5,22,5,205,253,153,252,32,251,109,255,49,254,111,3,180,255,30,9,24,11,51,2,13,10,81,9,120,2,134,7,104,11,207,2,231,7,48,7,223,253,45,253,84,4,129,0,131,255,116,3,137,5,96,6,157,3,162,255,30,6,215,6,171,254,253,5,15,6,79,2,139,1,238,254,180,255,213,3,15,11,153,0,169,11,52,7,8,4,5,10,189,10,228,5,16,11,87,7,23,3,175,4,26,2,66,255,59,254,209,5,234,254,220,253,134,4,11,255,149,7,252,7,0,4,24,6,114,6,0,2,253,0,210,1,194,255,189,254,127,4,39,254,136,254,251,1,79,254,100,5,114,8,131,3,151,7,165,5,134,0,192,2,184,1,204,1,13,2,228,255,62,254,23,1,58,5,0,0,203,3,252,0,67,254,141,253,33,252,164,254,166,253,112,250,142,1,200,2,120,6,149,255,58,1,78,255,93,0,178,8,190,8,6,2,81,3,144,2,50,254,57,253,65,254,174,0,222,255,167,4,137,255,42,0,237,3,140,254,18,1,246,2,12,4,48,9,46,7,163,2,188,6,218,5,174,1,6,5,85,8,127,255,73,254,0,0,139,254,32,3,96,8,6,0,51,6,174,9,222,1,84,2,80,8,84,254,32,253,225,5,129,1,178,0,212,3,139,0,193,1,201,4,242,253,182,252,42,252,145,0,18,6,218,4,111,2,168,5,144,2,93,1,248,3,202,5,31,0,232,254,159,1,196,254,212,2,105,6,104,1,34,4,44,2,76,254,154,254,177,4,157,254,99,4,147,7,145,1,48,6,200,8,241,253,12,252,99,1,233,0,238,0,185,8,218,253,127,252,129,253,147,254,11,254,165,7,133,1,68,7,85,6,162,0,108,4,240,4,19,255,150,4,110,5,128,253,101,254,116,0,28,255,158,6,250,8,103,6,138,8,219,8,50,2,249,4,98,10,67,1,82,1,238,6,66,2,83,4,84,3,22,0,82,2,166,3,113,255,206,2,190,1,50,0,71,0,247,255,174,254,70,253,129,250,102,0,118,255,204,252,202,254,43,254,133,251,158,1,67,0,245,254,36,4,46,3,161,5,12,6,80,5,248,4,218,6,103,7,125,6,227,7,85,8,28,7,16,7,14,9,53,7,132,2,163,255,198,1,90,3,73,1,120,255,233,1,254,254,128,255,58,255,23,253,215,255,204,255,247,254,39,252,90,1,137,0,223,1,51,249,20,253,84,253,117,251,67,249,145,254,129,252,135,251,240,252,24,254,78,252,56,252,171,255,122,254,43,253,215,0,172,254,85,255,252,3,148,3,177,7,52,2,179,0,234,2,150,2,209,3,198,6,119,3,110,2,146,3,171,3,88,3,141,4,53,1,176,2,35,3,149,3,161,0,58,2,118,0,236,255,229,254,208,252,214,255,204,0,52,251,187,254,50,254,61,252,54,255,113,255,36,252,28,254,151,254,66,253,46,252,35,254,210,254,234,252,92,251,156,255,238,252,192,251,226,251,77,252,108,249,54,255,181,252,242,252,241,251,158,250,123,252,144,253,146,255,171,255,100,1,213,0,246,255,19,254,108,1,6,3,169,1,54,3,223,1,173,255,45,2,8,2,32,252,232,249,196,253,165,253,27,253,230,255,10,254,130,253,121,252,209,0,50,1,147,0,196,254,175,253,172,253,171,255,45,255,31,255,106,252,239,253,117,0,233,0,73,254,30,253,77,4,239,2,121,2,177,5,180,6,231,5,229,6,177,5,142,3,98,4,132,4,81,3,74,5,100,3,214,1,153,252,130,251,252,248,153,252,163,252,32,252,138,255,155,0,212,0,229,251,175,252,162,253,163,251,199,248,66,245,5,252,109,250,179,248,114,1,72,255,98,254,191,3,237,1,104,0,190,3,15,4,31,2,154,0,141,2,201,0,225,4,251,1,150,0,151,2,247,1,230,0,111,2,9,3,163,2,147,2,88,0,146,255,75,3,244,0,224,0,126,1,29,2,46,1,212,2,177,1,154,2,142,4,222,2,85,1,118,255,20,0,115,254,97,251,88,254,210,255,191,254,160,254,132,255,53,5,253,3,56,4,6,1,110,1,211,2,154,3,27,1,217,253,31,0,132,253,157,253,79,253,71,253,97,254,72,252,245,252,55,255,207,250,170,253,153,254,71,252,251,250,166,0,237,1,49,1,221,0,78,3,191,2],"i8",ALLOC_NONE,Runtime.GLOBAL_BASE),allocate([98,2,72,3,168,3,6,3,45,253,212,250,19,251,155,254,255,251,148,250,184,251,160,250,147,254,120,250,167,248,160,253,250,248,65,249,94,253,223,253,107,251,65,253,166,2,18,3,148,0,133,255,184,2,8,5,132,2,94,1,246,255,158,1,102,2,15,0,137,0,88,1,45,255,210,252,24,250,205,252,121,254,94,252,180,253,47,0,177,253,126,252,115,252,183,251,93,255,8,251,113,251,99,255,72,250,11,250,123,254,6,251,92,251,144,253,159,2,213,0,198,1,124,0,238,254,243,253,39,253,16,254,104,255,192,250,122,0,135,0,167,244,179,253,118,254,64,249,185,1,206,255,196,5,136,3,19,3,60,1,236,0,72,254,165,254,217,0,157,1,113,252,107,252,121,0,57,254,92,252,202,0,164,255,47,254,137,254,232,1,134,1,218,1,108,3,217,2,60,1,233,248,224,250,99,253,87,0,194,3,176,1,51,2,7,255,222,251,250,0,29,1,81,4,117,4,171,1,184,2,242,251,128,249,210,249,76,252,90,1,160,0,203,254,240,254,166,252,158,2,112,2,226,4,80,252,104,254,102,253,162,253,192,254,128,254,20,254,230,0,65,0,78,1,206,255,240,255,240,255,78,253,139,250,255,6,180,6,119,5,174,9,15,8,124,5,221,4,191,5,146,5,130,254,243,251,254,255,173,0,114,254,121,4,211,5,232,7,9,7,4,3,250,4,226,5,149,5,199,6,209,7,55,4,194,4,249,4,126,251,197,248,207,250,216,252,147,251,184,251,61,254,247,251,70,249,65,0,66,2,172,255,60,250,126,246,14,249,3,253,170,250,18,254,38,255,174,253,93,252,81,1,20,255,50,2,53,9,102,10,146,7,209,5,252,4,106,3,189,0,102,1,118,1,17,250,23,247,214,246,57,252,9,251,209,247,140,253,92,251,250,249,125,6,19,4,34,2,53,2,37,4,220,2,192,255,188,252,78,254,76,254,160,255,203,0,54,4,192,4,100,6,139,3,254,5,218,3,70,1,197,3,77,3,142,0,172,255,197,0,214,1,75,9,34,6,109,4,214,1,190,4,139,1,96,5,176,4,101,4,18,4,92,1,225,253,46,251,136,254,41,255,75,255,225,1,101,248,171,249,46,255,18,253,95,251,134,1,29,0,113,254,27,0,52,3,212,4,243,2,183,2,211,3,153,1,82,255,173,4,11,4,144,3,76,5,54,7,32,252,99,250,228,1,51,250,92,249,208,0,100,254,180,4,152,5,241,254,128,3,120,4,96,254,241,6,154,5,96,249,172,245,52,255,3,249,241,249,9,4,136,249,233,249,23,5,27,251,203,249,57,4,99,253,185,251,190,255,86,253,64,1,167,254,147,2,49,1,45,4,244,250,220,252,237,255,157,249,245,250,29,0,109,249,15,254,71,0,225,254,249,255,156,255,18,254,62,252,19,255,84,3,89,7,204,6,63,251,149,250,227,0,108,253,46,1,117,1,96,0,63,4,233,4,206,251,123,249,160,0,229,1,28,8,6,7,90,252,36,255,40,2,172,253,156,253,237,0,80,1,184,6,111,3,131,2,117,2,178,1,243,4,10,2,97,6,15,0,244,0,71,254,195,5,205,2,184,0,27,7,54,6,173,6,220,3,5,1,169,3,45,8,41,9,240,5,91,8,66,7,70,6,191,253,189,253,77,251,68,252,135,0,24,254,48,254,51,0,174,254,139,253,164,254,45,253,122,4,25,8,162,5,144,8,186,5,143,3,92,250,220,249,26,247,120,5,198,2,17,5,55,5,121,2,160,3,154,5,146,8,34,10,118,9,156,8,89,7,214,3,194,8,62,7,124,1,24,3,121,4,193,255,229,253,158,1,4,255,60,252,198,254,19,251,85,253,244,252,193,252,242,253,19,252,126,249,145,251,88,254,181,249,60,254,213,254,244,4,24,4,130,2,123,4,85,3,88,3,93,253,176,254,139,0,220,8,63,5,138,5,29,0,0,3,29,3,56,251,167,1,52,2,218,250,198,251,245,0,234,250,212,252,61,2,238,250,175,249,134,2,56,252,66,3,211,2,225,3,116,6,235,7,65,255,207,252,176,1,150,2,60,0,198,0,114,2,229,3,50,5,112,6,171,7,9,5,195,249,163,255,211,255,192,251,37,0,172,255,117,6,47,10,33,9,41,4,248,7,73,9,115,4,22,9,70,8,91,3,101,1,230,5,152,2,203,4,75,4,223,1,80,5,144,3,105,7,218,6,227,7,144,4,117,7,248,6,143,1,34,0,0,1,175,253,208,254,227,251,35,2,158,6,127,5,135,2,157,255,171,254,212,5,111,6,166,4,38,0,124,253,44,255,139,1,78,3,222,0,64,253,3,253,52,253,44,253,84,248,12,245,106,255,35,1,174,255,209,4,179,5,239,3,116,255,101,255,153,0,183,1,41,1,32,6,7,250,102,254,132,253,0,6,199,1,19,255,208,250,117,255,252,254,19,2,42,2,100,3,13,1,240,4,94,2,23,255,115,3,207,1,230,2,88,2,136,255,183,255,165,1,212,0,73,254,198,255,36,3,250,250,39,251,216,2,38,1,22,254,50,0,177,253,119,252,26,251,42,0,81,253,147,0,231,255,17,1,84,2,201,254,189,4,89,2,14,253,81,3,72,2,173,1,95,2,75,2,166,253,90,255,205,1,228,252,201,252,9,3,100,5,142,3,219,6,119,0,137,5,204,3,37,255,144,252,196,249,231,251,14,252,182,1,55,253,157,250,78,0,0,0,65,254,101,251,144,251,217,250,219,249,200,8,231,6,29,5,178,3,47,6,152,5,126,4,226,1,180,1,43,254,172,251,106,2,65,254,58,252,64,4,28,251,21,250,142,255,176,251,40,248,189,253,210,0,101,2,241,1,73,248,99,250,130,2,11,251,168,252,243,3,146,249,95,251,39,4,237,249,96,253,180,4,100,249,166,251,111,2,45,252,210,250,3,251,27,2,109,255,126,3,182,250,127,252,78,254,120,3,219,1,172,1,153,0,128,254,82,1,44,250,1,254,103,1,50,252,165,251,42,254,105,0,218,253,165,2,87,252,135,251,109,3,124,1,252,254,210,0,149,6,156,3,232,4,239,6,166,4,71,4,139,5,119,2,21,2,115,2,43,1,165,254,101,254,234,253,135,2,118,253,29,0,173,253,134,254,169,250,27,6,122,5,97,4,185,5,65,4,130,5,136,2,208,247,190,251,250,255,55,1,62,255,155,252,129,253,193,252,160,1,118,251,56,251,69,5,33,251,83,252,21,7,111,247,61,248,197,1,149,253,169,250,68,252,186,249,76,248,29,250,105,251,223,251,176,251,135,254,89,2,201,0,84,7,57,3,118,1,82,254,213,250,29,0,139,250,31,251,205,250,17,252,32,250,192,3,135,250,39,248,197,0,157,250,99,248,20,255,203,251,123,0,166,1,103,2,245,4,34,2,206,254,246,5,136,3,170,4,252,6,153,4,142,253,140,252,10,250,199,0,254,2,224,5,215,251,94,3,197,0,246,251,19,249,137,252,224,252,145,0,87,2,146,251,249,253,114,2,75,251,122,248,244,1,114,252,239,251,141,250,60,250,225,249,55,252,245,253,74,3,34,0,2,7,134,2,94,3,73,251,160,248,22,252,178,255,247,255,96,253,20,4,247,2,80,0,168,253,115,4,251,3,57,0,208,7,142,5,191,252,134,5,97,4,78,251,94,6,236,4,51,254,140,5,220,4,1,6,207,3,253,0,229,254,68,1,153,254,87,2,61,255,106,0,76,2,62,0,181,253,11,253,133,2,205,0,51,0,177,4,246,2,71,251,161,2,122,254,144,253,45,6,173,3,105,255,255,3,223,2,4,11,21,5,178,2,210,254,12,2,157,255,124,252,204,249,91,251,60,4,251,0,238,0,222,7,0,7,242,3,221,4,97,6,205,6,53,251,252,249,72,251,147,253,200,1,147,255,40,0,191,255,20,3,219,252,69,253,186,250,185,253,136,3,64,3,223,252,20,2,82,2,180,7,128,5,71,5,103,251,168,248,190,247,251,252,56,2,180,3,9,252,55,4,236,4,169,251,226,1,126,255,242,6,20,4,12,3,45,250,245,0,144,3,196,254,139,251,107,252,232,253,94,250,214,246,239,252,246,249,60,248,45,248,1,1,141,3,199,248,135,253,71,251,254,249,130,248,226,251,70,6,191,8,40,6,201,253,36,250,248,249,1,251,195,0,89,5,207,252,37,1,195,4,243,253,118,2,173,4,94,249,135,246,208,248,209,254,219,2,235,2,111,251,5,255,13,1,74,252,181,255,148,6,98,251,59,254,237,3,193,249,73,2,122,1,229,247,197,253,85,254,239,253,121,251,109,251,229,254,51,255,204,253,228,252,222,4,205,2,229,8,159,3,27,2,58,254,47,2,184,1,51,253,180,5,79,6,250,251,28,4,74,6,111,251,118,255,79,3,226,0,39,0,156,253,29,251,150,255,39,253,117,253,200,3,22,5,54,253,132,253,191,6,97,1,45,4,154,1,226,252,100,255,75,4,194,253,150,3,190,1,226,250,244,3,210,1,128,5,55,6,253,2,149,5,100,5,221,6,157,7,164,7,74,9,42,6,255,7,100,8,148,3,98,0,249,255,101,7,138,5,93,8,92,1,125,5,43,6,152,0,110,4,9,7,245,254,154,0,115,5,114,251,213,1,30,4,138,251,107,254,207,251,195,250,40,247,211,249,148,254,101,3,170,6,118,251,37,2,14,6,55,251,116,248,126,249,51,250,71,248,249,247,65,249,118,252,158,255,151,248,233,0,212,5,124,3,108,0,181,254,64,249,110,251,92,249,220,251,188,7,254,6,210,251,51,249,139,248,245,255,3,6,37,5,192,249,94,0,241,1,165,1,187,1,59,255,214,249,163,254,30,252,169,253,229,253,116,4,59,252,117,250,127,255,195,250,175,0,65,254,137,254,31,5,7,8,141,254,118,253,205,254,207,251,93,2,109,1,247,247,143,255,174,1,140,2,146,3,199,3,12,252,206,249,237,246,225,5,224,4,47,2,6,1,26,254,111,254,65,249,62,5,10,6,50,0,56,0,176,1,182,254,119,0,164,253,19,250,200,251,214,252,178,3,103,4,31,4,136,250,89,249,80,249,10,251,64,253,219,250,39,3,29,7,119,4,200,10,70,6,123,8,96,4,153,1,106,255,109,255,148,1,191,3,135,9,119,7,141,8,118,252,115,255,158,252,120,252,114,255,54,254,211,253,60,253,113,249,194,252,105,250,209,249,206,248,190,250,194,251,188,249,240,254,147,3,84,251,4,3,32,4,130,253,46,251,151,248,12,254,175,255,202,252,247,250,179,249,33,253,139,255,17,3,168,0,190,251,109,4,154,3,184,251,22,253,104,5,31,1,221,253,217,251,160,250,103,247,76,251,128,247,222,249,35,249,25,250,63,247,253,252,55,249,75,4,62,3,204,249,212,2,219,4,250,249,181,2,37,3,102,249,16,255,129,6,92,249,252,255,100,253,101,8,48,3,18,4,206,252,207,248,22,0,4,253,5,254,193,1,129,251,151,253,33,1,181,252,196,249,16,255,242,1,22,255,111,253,16,253,224,1,142,6,193,254,31,254,193,0,213,252,171,0,137,255,176,247,54,255,176,252,181,6,116,4,164,6,67,0,239,255,66,0,244,255,102,249,187,253,152,255,240,254,204,251,94,251,203,248,136,254,140,251,98,252,92,254,198,255,253,254,112,253,146,251,215,253,252,6,203,4,199,1,129,0,206,1,185,1,16,255,240,253,72,3,2,2,130,0,181,255,90,4,111,2,153,0,216,0,44,4,52,2,250,255,236,254,95,4,215,2,190,0,188,255,192,2,50,1,119,0,248,254,73,1,61,0,156,255,156,0,108,1,123,0,183,0,48,255,85,255,133,255,220,0,191,255,206,254,194,255,146,1,17,0,108,253,86,252,246,254,0,0,129,1,235,0,20,1,29,1,64,1,12,1,176,254,56,255,44,253,17,0,172,255,125,1,224,253,173,1,238,1,7,2,139,255,32,1,48,1,73,1,131,2,157,0,189,2,252,1,176,4,113,2,28,3,96,2,230,3,165,1,236,1,120,2,180,4,12,3,190,1,132,0,233,4,76,3,35,2,193,1,61,3,146,2,29,2,214,1,108,4,234,4,150,3,127,2,35,2,51,0,167,1,23,1,9,0,136,1,83,0,94,0,30,2,31,2,229,0,109,255,58,255,129,0,194,0,71,255,161,252,215,250,210,254,30,0,171,253,139,253,237,255,114,0,124,252,199,251,210,1,97,1,53,250,219,249,15,0,113,255,84,249,245,247,17,253,196,0,172,248,237,247,126,253,254,254,225,246,66,250,62,254,204,253,184,253,70,255,152,252,98,254,243,248,36,252,155,251,226,250,42,253,151,251,28,0,169,0,241,251,160,252,50,253,10,255,228,1,36,0,23,255,207,255,9,1,67,0,33,1,211,1,178,0,31,2,42,3,28,2,84,0,26,1,160,2,191,2,49,252,247,252,129,0,31,1,86,252,29,255,187,3,83,2,175,249,223,254,68,3,137,2,201,248,41,255,82,4,206,2,14,248,195,251,138,2,184,1,203,247,239,253,139,3,63,2,37,248,176,254,158,2,204,0,171,246,76,253,104,1,137,0,148,247,100,247,247,255,24,1,246,254,119,0,39,0,193,0,78,0,197,255,136,255,226,0,49,252,166,252,243,252,185,251,149,253,99,254,61,254,182,252,64,251,215,250,211,252,141,252,160,250,177,249,118,254,84,254,31,253,167,251,219,253,234,252,144,252,49,252,57,252,126,253,39,252,138,252,7,251,175,250,39,254,220,252,135,250,129,250,160,0,247,254,105,252,237,254,8,255,6,255,50,253,132,254,97,0,153,255,137,254,27,255,97,254,63,255,121,255,213,253,116,2,105,1,119,0,216,0,67,2,108,1,135,1,209,0,122,2,10,2,102,255,108,255,14,2,133,1,170,0,33,0,105,0,11,1,64,0,124,1,33,250,24,252,226,255,143,254,210,251,58,0,135,2,223,0,16,250,221,254,109,2,51,1,5,250,156,0,250,2,148,1,19,248,141,0,222,2,243,1,199,248,118,253,50,1,0,2,69,255,152,255,197,255,182,1,134,0,26,255,156,0,70,255,195,255,252,254,240,255,10,0,199,253,253,255,91,254,215,254,67,249,247,253,166,254,178,0,174,250,197,255,212,255,157,0,158,247,51,254,42,254,163,254,134,247,255,255,143,254,135,255,213,249,139,254,124,252,9,252,163,251,177,253,155,253,240,252,207,253,122,0,181,255,63,254,252,255,85,255,133,255,140,254,192,0,168,0,180,255,124,255,252,0,149,255,84,1,210,0,136,1,253,1,16,1,181,0,147,255,145,0,218,0,119,0,96,254,249,254,229,1,9,1,75,255,248,255,226,254,226,0,12,255,38,255,69,0,222,254,98,255,191,0,255,255,192,255,176,253,166,255,213,0,160,255,255,0,179,1,178,0,176,255,143,254,238,255,223,255,176,255,214,255,159,1,140,0,34,255,119,4,139,2,137,2,73,1,255,2,44,2,249,0,235,0,180,3,157,1,186,1,23,1,141,0,83,1,100,1,45,2,42,254,86,255,99,0,237,0,199,253,224,252,96,1,53,2,26,1,217,1,214,1,76,1,57,255,78,253,252,250,107,252,63,255,86,254,224,252,158,251,230,255,141,254,22,254,63,255,125,2,83,2,7,2,74,1,152,1,141,255,79,0,12,0,221,1,87,0,153,255,136,254,102,253,165,254,235,254,221,254,2,254,31,254,169,0,41,1,195,252,30,253,51,255,85,255,192,254,228,253,72,1,27,1,165,252,66,252,186,1,254,255,44,2,174,2,130,0,56,0,103,5,244,3,243,2,171,1,100,2,229,2,116,2,41,2,173,254,228,252,134,0,21,1,135,253,195,251,254,255,10,255,144,252,245,251,185,249,216,251,30,252,38,254,142,251,24,254,98,254,229,252,73,0,50,255,248,255,117,255,183,1,204,0,80,255,190,253,23,0,131,0,243,254,11,253,65,255,245,0,147,255,174,254,112,0,60,1,120,0,106,254,138,255,99,2,76,255,70,255,123,253,115,0,83,255,34,0,250,253,23,254,105,255,61,0,185,253,180,252,220,0,118,255,87,253,4,252,135,1,239,255,170,253,191,254,157,0,217,254,129,0,155,0,98,252,149,252,37,252,29,1,241,0,173,255,131,255,131,255,108,2,85,2,176,1,92,0,137,1,78,0,153,1,61,0,119,254,29,253,99,254,20,253,83,0,54,0,105,1,27,0,196,251,130,0,175,254,74,253,227,249,41,1,62,1,237,255,175,248,36,0,51,0,195,254,237,246,10,255,231,0,172,255,254,246,241,252,40,0,77,255,71,247,94,252,38,254,50,254,14,253,170,255,224,254,142,253,149,246,57,254,193,255,171,0,181,251,186,251,230,255,113,255,87,251,57,254,106,254,131,254,163,253,46,255,160,255,205,255,188,253,36,254,236,254,241,255,85,251,134,253,77,251,143,252,134,254,35,255,99,253,72,252,82,2,178,0,109,254,92,253,251,2,71,1,89,2,34,1,172,0,44,1,203,0,157,0,200,255,176,254,100,1,24,0,28,255,216,254,253,254,227,255,70,255,7,1,160,1,14,0,159,254,117,1,244,255,40,255,1,1,96,0,174,0,57,0,10,250,152,253,70,252,13,254,15,254,104,255,179,254,125,0,105,0,200,0,179,0,159,255,181,254,32,255,253,2,185,2,248,2,0,1,45,1,59,0,199,1,171,255,204,0,32,1,254,253,240,0,251,0,147,255,0,1,161,1,222,255,99,254,101,0,174,1,128,1,156,0,225,255,246,255,206,0,170,1,77,2,145,0,143,0,71,0,40,3,138,3,77,1,93,1,218,3,170,3,77,2,75,1,20,5,56,3,187,0,253,1,38,4,141,2,123,1,210,1,182,5,169,3,145,1,18,1,19,3,93,3,9,1,2,0,97,2,41,2,28,0,49,1,158,3,84,1,106,0,130,1,241,0,245,254,109,255,225,0,78,255,234,253,91,1,246,1,125,253,131,254,141,1,30,0,117,253,35,253,77,254,142,1,105,254,42,253,28,254,8,255,235,252,110,252,74,254,36,254,14,254,122,254,75,0,217,254,60,252,178,253,162,253,150,0,135,255,207,255,101,255,178,255,167,3,38,2,133,1,38,0,191,254,127,0,168,1,59,1,227,254,143,255,27,1,3,1,146,2,203,0,66,1,230,1,135,3,249,1,236,2,161,1,99,2,167,1,43,2,0,2,239,0,173,255,190,253,237,255,173,254,37,253,93,1,13,0,90,252,137,250,142,255,152,254,107,0,180,2,182,0,90,0,37,251,254,249,241,249,43,253,200,253,121,252,173,250,243,253,251,253,171,252,163,252,20,252,88,255,78,253,189,252,63,0,119,255,212,253,221,253,144,0,226,254,207,252,229,1,63,1,109,255,104,254,14,2,246,0,165,254,78,254,41,1,228,255,222,254,41,254,170,251,251,250,52,254,153,254,36,252,230,252,67,5,19,5,178,2,11,2,192,4,44,4,70,4,245,2,57,3,116,4,240,2,238,1,228,4,85,5,171,4,130,3,9,2,29,4,20,2,176,1,178,254,40,255,199,254,249,254,96,255,52,0,40,254,101,255,127,0,136,0,132,254,44,0,83,3,154,1,94,255,23,254,123,0,1,255,228,252,101,253,66,4,149,3,21,3,237,1,117,5,173,4,46,2,202,0,205,255,138,255,170,254,67,253,83,0,108,0,214,255,71,254,61,0,95,0,31,1,0,1,229,255,89,0,12,2,19,2,95,1,227,0,80,2,33,2,185,2,155,0,92,255,51,1,126,2,18,1,23,254,206,255,242,2,240,0,90,255,132,255,140,255,189,253,68,251,193,255,190,0,217,254,240,251,240,250,147,0,136,254,79,255,143,255,73,3,217,4,27,4,156,2,2,0,37,1,39,2,48,1,184,251,71,252,8,255,120,1,18,253,59,252,87,0,4,2,237,254,252,253,177,2,135,1,133,254,125,253,108,3,82,2,122,254,11,252,123,253,61,2,149,255,200,253,79,253,198,252,255,251,229,255,184,254,53,255,93,3,237,2,36,2,233,0,132,249,237,251,195,1,108,0,108,253,148,253,174,1,236,0,21,0,116,254,122,251,137,253,92,5,18,5,199,3,65,2,101,4,101,4,77,2,198,1,189,254,159,252,45,254,153,0,44,254,69,253,220,252,3,254,120,254,50,253,52,255,221,255,165,253,187,251,201,253,94,255,7,254,20,252,154,255,94,1,219,0,224,0,167,1,252,0,139,1,79,2,96,2,107,1,22,253,160,255,117,1,172,0,171,0,39,1,202,2,83,1,233,0,77,0,107,0,21,1,157,0,153,0,13,254,156,254,11,6,49,4,64,2,238,1,220,254,173,254,8,254,176,253,121,252,184,255,149,253,31,254,198,249,163,251,201,253,2,255,231,252,5,254,204,253,221,254,20,254,236,253,246,1,48,2,130,254,171,1,88,2,230,0,29,255,221,1,251,0,75,0,29,1,74,3,45,3,220,1,226,250,203,250,186,0,121,1,181,253,107,252,131,2,125,1,94,251,215,253,155,1,82,0,153,251,204,252,82,255,228,253,164,253,119,0,31,2,205,0,132,254,145,2,141,3,55,2,112,0,214,254,138,254,114,0,167,252,5,255,56,0,159,0,145,1,89,1,222,255,116,255,145,255,161,253,41,0,102,2,99,1,142,255,179,255,218,1,66,2,56,0,170,5,156,3,74,4,140,5,229,2,144,1,246,0,22,0,76,2,57,1,135,255,71,1,63,3,216,1,142,251,160,253,88,3,40,2,39,251,208,251,126,2,88,2,154,254,254,0,179,254,209,254,122,253,227,2,102,1,74,0,202,4,135,6,197,4,81,3,193,8,88,6,215,3,124,2,49,7,197,5,237,2,128,1,94,1,7,1,87,0,128,0,146,248,83,252,112,255,192,255,58,249,1,255,32,1,225,255,172,245,42,251,110,1,235,0,149,249,188,251,192,250,208,254,227,253,205,251,164,251,123,0,102,251,4,255,208,252,76,255,8,252,21,2,53,2,233,0,25,254,82,254,68,255,78,1,99,3,212,4,22,2,171,0,202,249,185,249,123,2,118,2,108,247,54,1,156,3,156,1,202,246,184,254,188,3,17,2,177,245,135,254,118,2,22,1,214,245,61,1,31,3,43,1,154,246,133,0,84,1,31,0,148,247,68,250,131,0,125,0,96,251,22,254,117,255,46,0,24,253,191,1,123,3,52,2,67,0,61,254,134,2,92,2,215,253,83,254,148,252,140,1,162,0,190,255,25,5,147,3,223,1,67,2,64,4,26,3,194,1,22,1,54,2,68,1,223,251,102,255,148,0,79,255,15,246,168,0,46,4,80,2,209,246,214,255,51,3,89,1,216,246,61,253,209,2,250,0,129,247,39,250,203,254,122,0,178,255,183,255,120,0,173,0,252,255,6,1,249,254,251,254,81,254,192,255,107,254,36,253,207,245,116,0,173,255,63,255,11,250,80,252,35,254,43,253,4,254,51,1,170,0,172,0,64,3,161,1,64,3,174,2,31,255,177,0,126,3,50,3,30,254,123,254,255,4,15,4,129,254,201,0,162,254,40,0,218,2,123,2,226,0,14,2,247,1,206,1,82,1,142,1,23,2,202,2,40,0,230,254,202,5,191,5,61,4,219,2,25,6,48,4,141,3,181,2,139,5,2,5,121,3,111,3,129,4,216,2,162,4,72,3,30,255,106,4,181,3,177,2,18,254,38,252,236,249,128,255,200,253,47,253,55,253,230,255,61,1,12,2,70,0,135,0,107,254,159,252,26,249,116,253,82,255,223,252,117,3,5,3,103,255,165,255,75,4,239,2,6,254,131,251,85,3,134,2,241,0,14,3,7,2,27,2,61,7,164,6,77,4,172,2,31,251,50,250,48,254,188,0,131,252,127,250,224,250,171,254,121,255,182,1,81,255,18,0,87,4,208,3,63,1,208,0,106,250,24,249,83,0,202,1,238,253,24,252,51,1,129,0,184,252,241,255,227,255,156,254,113,252,100,252,133,251,14,255,137,255,240,253,127,0,123,255,7,253,3,253,190,0,173,255,197,254,127,3,10,2,231,0,34,255,102,0,193,255,84,254,60,1,187,2,123,1,70,0,25,0,204,2,58,1,148,255,251,1,106,3,54,2,238,0,108,0,173,3,7,2,195,0,169,1,196,255,85,254,1,1,139,0,153,255,138,253,190,1,78,1,114,1,156,1,48,0,84,255,78,253,229,254,45,2,187,0,226,254,158,0,227,1,140,0,14,1,168,254,137,253,156,3,67,2,140,255,132,0,142,0,210,1,188,255,192,255,230,0,111,255,210,254,226,253,221,252,112,252,250,3,225,2,251,252,247,3,118,2,41,1,220,245,95,0,189,1,80,1,182,247,235,1,254,1,191,0,27,251,161,0,254,255,188,254,86,250,135,253,56,253,151,255,182,252,2,255,101,254,100,0,128,253,222,254,242,3,251,2,118,253,57,1,145,4,218,2,140,0,249,1,6,4,254,2,4,3,31,1,43,4,55,3,239,1,237,2,49,1,67,1,92,255,206,1,78,0,143,1,170,254,150,252,69,0,85,2,240,255,108,2,109,2,81,1,118,255,68,254,247,254,218,0,84,0,62,254,185,3,154,2,34,255,221,252,29,2,92,2,103,252,160,250,244,0,116,0,183,252,45,253,118,2,76,2,140,0,151,2,38,1,112,1,167,3,22,4,113,3,247,2,210,6,184,5,148,3,116,2,180,1,195,3,25,1,1,0,137,255,74,0,30,2,213,0,1,0,201,253,45,1,241,0,4,1,179,1,222,0,140,1,168,3,189,3,84,4,191,2,254,1,250,1,40,3,222,1,89,2,182,2,192,3,108,2,204,3,229,2,212,3,88,2,66,3,205,2,255,2,172,2,131,2,204,3,167,3,126,2,245,1,149,2,208,2,83,3,151,255,136,253,209,254,139,255,83,254,130,0,21,3,186,1,246,253,68,255,192,2,117,1,9,253,42,0,46,3,11,2,237,253,143,251,117,1,66,2,86,253,77,251,57,254,29,1,117,251,215,249,182,251,44,0,81,0,174,255,200,2,107,1,221,1,246,0,186,3,110,2,68,6,86,6,253,4,123,3,129,5,91,3,156,3,124,3,6,3,17,4,179,3,118,4,40,0,222,253,181,255,32,1,152,253,150,255,71,253,230,255,87,255,96,255,133,252,29,253,233,254,128,254,251,251,162,254,245,6,28,5,22,4,48,3,44,6,253,5,192,5,154,4,225,5,52,4,192,4,131,3,122,3,136,3,52,2,142,2,152,3,180,2,253,3,88,3,19,254,132,0,177,0,249,1,71,0,195,0,228,255,97,0,200,1,95,1,92,255,88,0,183,1,22,1,216,255,94,1,115,5,181,3,234,0,161,255,219,252,40,254,38,0,93,255,111,1,158,255,233,1,11,2,1,4,154,4,188,4,138,3,63,1,34,5,46,3,205,1,133,255,225,253,220,252,191,1,20,253,188,254,127,252,153,251,31,253,11,254,235,252,55,253,203,2,9,3,215,4,154,3,157,7,147,7,88,5,97,3,218,2,112,3,246,2,132,1,153,252,198,1,17,0,5,255,131,254,214,252,209,249,239,0,247,253,58,252,232,252,3,1,134,252,178,250,254,252,183,255,166,0,93,1,44,255,67,1,184,252,211,254,217,1,179,1,89,253,48,254,216,2,95,1,100,255,57,255,155,2,176,1,29,0,4,255,159,1,224,1,37,253,133,254,145,0,47,2,240,253,137,253,122,251,97,255,189,1,17,1,123,0,127,2,117,1,130,255,32,3,56,2,84,0,94,255,208,2,200,2,194,252,232,253,71,255,222,0,152,1,196,1,245,1,3,3,127,252,181,250,189,255,186,1,232,252,130,250,54,2,90,2,167,0,186,254,253,1,74,1,161,255,142,253,38,253,168,254,132,6,193,4,11,3,199,1,36,5,60,3,72,2,207,2,148,1,225,255,245,3,21,3,89,0,107,0,123,3,37,2,103,3,45,6,149,3,159,2,98,3,199,5,9,5,86,3,135,1,44,4,98,4,44,3,78,0,206,253,89,1,51,2,173,1,153,255,161,1,19,3,134,255,75,254,155,1,20,3,111,252,95,254,90,2,242,2,30,255,240,255,151,0,248,2,68,253,118,0,152,255,242,255,152,251,48,0,28,1,137,1,122,254,93,254,129,253,140,255,114,252,50,1,60,1,243,255,183,4,216,3,53,3,157,2,85,251,75,253,140,0,43,255,140,252,96,254,57,255,210,253,152,253,245,0,108,254,104,253,6,1,56,0,151,253,44,253,171,255,21,254,192,254,112,253,198,253,193,252,127,255,240,253,30,250,193,255,145,254,127,254,154,254,191,254,4,0,51,0,146,254,42,255,63,1,255,1,146,0,159,2,239,255,221,254,146,255,208,1,117,255,16,254,54,255,220,0,200,254,137,253,108,253,183,255,113,253,204,252,106,253,115,253,248,250,167,252,82,254,71,252,65,252,248,254,207,255,44,254,184,255,131,254,162,254,205,253,63,255,105,254,55,0,104,254,221,252,11,0,203,254,137,2,188,0,58,255,0,254,205,1,177,255,54,254,218,250,249,254,122,255,245,253,135,249,77,254,17,254,3,253,57,0,165,254,98,254,178,1,139,251,14,255,104,253,167,252,34,0,188,255,61,253,174,254,163,1,163,0,226,255,250,254,57,254,235,252,106,250,47,253,238,3,152,2,13,1,25,0,107,2,4,1,183,0,96,0,56,252,178,250,124,254,135,0,75,253,67,3,200,1,154,0,81,4,191,2,57,2,107,1,89,6,46,5,217,3,236,2,36,255,219,0,76,0,48,255,81,250,130,249,49,0,149,0,60,252,84,255,16,253,176,254,113,2,209,0,6,255,190,255,7,252,186,252,254,255,61,1,136,247,51,250,118,255,123,0,172,248,205,247,247,253,85,0,57,252,146,254,73,253,143,252,103,252,13,252,5,253,75,252,132,255,0,255,160,254,108,253,178,0,207,1,98,1,48,1,48,249,177,253,230,254,79,0,55,247,175,0,99,3,243,1,118,255,76,255,75,255,235,255,13,247,39,251,52,254,248,253,253,252,195,1,246,255,204,254,15,1,191,255,4,0,214,0,233,254,77,254,213,255,164,254,98,253,35,0,191,255,45,255,38,3,23,2,85,0,41,1,57,0,239,0,210,2,237,1,225,0,149,2,72,3,35,2,228,253,136,254,14,0,93,1,213,1,209,2,75,1,162,0,224,253,16,253,194,255,246,255,142,1,168,255,212,2,189,2,237,255,235,253,162,255,89,2,136,0,185,255,87,253,21,253,90,255,168,254,5,1,206,255,161,0,204,255,229,1,81,1,117,249,50,0,190,0,163,255,22,247,25,255,62,255,174,255,161,255,173,253,102,255,128,0,126,3,245,1,76,2,201,1,167,254,206,0,122,0,110,0,137,253,29,255,199,253,3,0,152,1,239,0,141,1,226,0,59,255,254,255,128,0,235,1,1,5,136,3,36,1,215,0,26,2,50,1,3,1,253,1,91,253,233,251,13,0,65,1,89,253,180,253,154,254,44,255,210,253,243,0,134,2,223,1,230,1,86,1,122,2,20,2,107,0,34,3,75,1,136,0,144,255,114,254,249,251,226,254,186,254,63,253,32,1,16,1,19,5,120,4,154,4,92,3,89,254,121,0,127,254,108,255,217,254,210,254,190,252,205,252,16,0,232,255,55,255,36,254,43,2,91,0,11,255,38,1,218,255,133,254,62,252,59,251,89,251,18,250,239,254,117,254,122,254,11,252,123,253,61,2,205,248,250,251,249,1,212,1,232,2,179,3,97,2,237,1,79,253,108,251,140,253,121,255,254,251,195,0,155,1,196,0,46,6,123,4,63,2,81,1,41,251,247,252,120,253,114,255,83,2,57,3,199,3,223,2,74,251,54,252,175,255,170,254,23,253,13,0,184,255,119,1,198,1,19,0,127,5,153,3,145,249,84,255,93,3,50,2,160,3,1,6,39,4,228,2,88,246,72,252,8,1,82,0,10,254,59,252,202,250,123,0,99,3,212,4,22,2,171,0,240,246,52,254,12,3,107,1,90,251,151,253,252,0,195,255,82,255,34,0,243,3,20,3,227,246,247,0,167,1,153,0,240,255,157,254,6,1,193,1,216,249,207,251,224,253,141,254,153,253,207,254,27,4,37,3,175,2,16,2,6,0,74,255,167,3,107,3,234,3,41,3,199,0,1,1,126,0,76,0,184,253,142,251,87,2,44,2,175,251,145,250,201,249,249,253,47,252,211,250,108,0,91,1,46,253,49,252,109,1,101,0,111,255,169,2,249,0,103,255,0,0,178,254,198,253,159,0,156,1,29,1,176,254,151,253,71,252,58,252,119,3,177,2,29,251,84,0,71,255,114,254,176,253,177,1,20,4,141,2,85,0,73,1,216,255,105,1,79,254,63,253,210,1,62,2,102,255,142,2,80,2,34,1,89,255,72,0,93,1,175,0,162,2,41,1,209,3,208,2,211,4,180,4,245,2,232,1,112,254,243,254,26,2,116,1,186,250,149,250,86,251,165,255,238,4,108,3,7,3,188,2,169,253,218,255,82,254,46,253,184,7,94,6,223,3,96,2,111,0,20,1,30,255,160,255,77,252,124,254,245,255,249,255,209,254,237,253,185,252,82,1,198,6,174,6,125,5,245,3,252,253,169,252,123,253,210,0,80,253,96,254,1,2,230,0,202,252,131,253,134,251,192,254,72,252,110,253,74,253,183,0,142,255,145,253,50,3,162,2,65,255,52,255,219,2,123,2,51,0,197,4,115,3,64,2,70,252,81,254,58,3,86,2,170,254,13,253,124,252,105,254,154,251,158,254,50,255,0,254,221,253,214,252,155,254,148,253,66,0,3,2,183,255,102,254,152,252,79,252,92,250,53,251,191,0,239,255,224,253,25,255,252,249,224,253,123,252,138,252,134,252,242,249,19,246,205,252,54,252,175,0,198,252,46,251,6,253,169,253,234,255,122,2,213,252,37,252,122,252,189,254,203,0,26,0,129,254,21,255,243,252,113,254,238,4,138,3,92,252,137,250,156,250,144,253,93,0,87,0,98,254,229,253,77,253,37,0,121,2,254,1,125,254,36,254,206,250,143,1,66,0,7,1,105,254,207,255,177,254,95,254,17,4,73,7,245,252,191,251,96,250,22,253,166,252,64,3,187,253,9,253,141,254,95,253,6,254,40,8,208,253,134,253,101,251,15,1,241,0,14,0,74,254,12,255,115,254,207,1,178,4,23,4,162,253,227,252,98,250,205,255,189,254,225,1,32,255,184,253,241,253,238,1,113,3,170,2,79,254,206,254,22,252,42,2,147,2,222,0,171,0,96,255,159,254,169,2,6,7,29,6,172,252,99,251,97,249,176,254,102,253,114,0,187,253,12,253,24,253,61,255,119,1,241,1,47,254,220,252,182,251,154,0,26,1,125,255,206,255,65,255,49,253,67,1,220,2,6,6,46,253,205,252,132,250,105,0,6,255,185,0,78,255,10,254,26,253,65,1,254,1,87,4,189,254,201,253,58,252,127,0,228,1,82,1,96,255,52,0,174,254,220,2,87,5,18,6,142,253,222,252,96,249,226,254,182,253,164,2,73,253,169,254,142,254,22,254,39,1,101,7,138,253,194,253,10,252,176,255,133,2,187,255,250,255,194,254,148,254,14,3,170,5,14,4,199,254,35,253,141,250,120,0,60,0,221,1,248,254,183,253,133,255,199,2,221,4,121,2,165,255,157,254,8,252,3,3,246,2,5,1,253,0,81,0,38,254,162,3,167,8,184,6,216,252,181,251,123,248,208,253,242,252,169,0,220,252,206,251,68,255,142,253,201,255,125,5,74,253,52,253,86,251,108,253,98,1,73,1,254,253,201,255,225,253,110,1,9,4,158,4,110,253,65,252,179,250,201,255,72,255,93,0,163,253,226,254,106,253,148,1,193,1,59,3,226,254,162,254,17,251,116,2,50,1,227,0,240,255,147,0,145,253,186,0,155,3,98,8,94,253,134,252,186,249,69,254,28,255,83,1,143,254,234,252,103,254,231,0,86,0,189,5,64,254,187,253,219,251,82,2,194,1,79,255,132,255,86,255,65,254,159,2,135,4,124,5,36,254,101,253,25,250,179,255,118,255,204,2,79,255,140,254,131,254,195,1,166,3,147,3,6,255,80,254,202,252,16,1,60,3,190,1,26,0,19,0,225,255,186,2,156,6,120,8,122,253,47,252,124,248,77,255,39,254,12,1,133,254,23,253,77,253,11,0,127,0,9,4,24,254,107,252,199,252,61,0,67,1,135,0,147,0,111,255,82,253,173,2,18,3,146,6,6,254,176,252,239,250,35,0,90,0,222,0,233,255,166,254,98,253,199,1,79,2,7,5,53,255,175,253,194,251,140,2,96,1,181,1,39,0,63,0,55,254,73,3,241,4,57,8,248,253,142,252,208,249,184,254,57,253,141,5,172,253,170,254,186,255,209,0,173,0,136,7,89,254,170,253,103,252,165,1,93,2,218,255,254,255,11,255,129,255,128,3,177,7,111,4,133,254,250,253,213,249,173,0,118,0,241,2,201,255,131,254,204,255,217,3,253,3,241,2,254,255,221,254,133,252,241,2,224,3,167,1,8,1,131,0,60,255,127,3,226,8,239,9,133,253,192,251,61,246,239,253,42,252,14,2,4,253,194,252,220,253,76,254,60,1,87,2,93,253,84,252,22,253,199,255,236,0,245,255,55,255,175,255,226,252,16,0,77,3,22,6,31,253,39,252,68,251,44,254,17,0,34,1,233,254,184,253,68,253,183,0,54,3,193,2,247,254,20,254,93,251,165,1,152,0,212,1,122,254,166,0,244,254,39,0,14,6,76,7,133,253,58,252,221,249,59,254,20,254,142,3,228,254,253,251,181,255,75,255,123,255,60,7,67,254,144,253,106,251,164,1,111,1,207,255,123,254,44,255,87,255,195,2,49,4,184,4,229,253,58,253,87,250,83,0,93,255,228,1,20,255,225,253,157,254,82,1,151,4,46,3,10,255,203,254,66,252,94,2,248,2,60,0,166,0,248,255,93,255,206,254,57,7,3,10,21,253,255,251,9,249,93,254,66,254,209,0,50,253,202,253,234,253,6,254,181,2,89,3,49,254,71,253,198,251,69,1,175,1,50,255,241,255,248,255,5,253,33,2,151,3,238,5,157,253,241,252,223,250,0,1,201,255,208,0,91,255,164,254,106,253,65,1,168,2,162,3,186,254,83,254,73,252,228,1,190,1,58,2,59,255,72,0,183,255,141,3,175,5,205,6,205,253,31,253,74,248,132,255,96,254,206,2,34,254,108,254,198,254,240,255,190,1,100,6,217,253,231,253,18,253,198,255,126,2,214,0,55,0,71,255,241,254,124,4,21,5,188,4,29,254,97,253,16,251,117,0,29,1,31,2,52,255,121,254,145,255,1,2,2,6,86,3,142,255,66,255,46,252,109,3,83,2,208,1,4,1,4,1,201,254,236,2,235,8,168,8,251,253,79,252,133,247,186,254,60,253,122,1,212,252,77,253,24,255,208,253,175,2,129,5,36,253,78,253,188,252,153,254,133,2,130,1,247,254,62,0,90,253,145,0,108,6,184,4,213,253,36,252,47,251,178,255,14,0,114,0,185,254,154,254,23,254,136,1,165,2,185,2,55,255,20,255,140,251,181,2,193,1,178,0,13,255,0,1,79,254,99,2,105,5,152,9,156,253,123,252,72,250,205,254,239,255,243,1,197,254,101,253,2,255,0,1,172,1,183,5,26,254,90,254,224,251,143,2,114,1,18,0,154,255,71,255,236,254,243,2,42,6,55,5,24,254,165,253,118,250,182,0,163,255,102,3,183,255,54,254,164,254,67,3,94,3,189,3,230,254,179,254,22,253,35,2,71,3,172,1,17,1,167,255,13,0,172,3,172,6,16,10,94,254,196,251,34,249,212,255,154,254,3,1,15,254,125,253,208,253,99,0,45,2,193,3,91,254,2,253,107,252,39,1,70,1,184,0,175,0,15,0,142,253,20,2,110,3,189,7,69,254,0,253,5,251,221,0,156,0,12,1,39,0,149,254,7,254,183,2,4,3,116,4,94,255,53,254,112,252,197,2,188,1,146,2,25,0,47,1,200,254,244,4,130,5,179,6,215,254,2,253,212,248,249,254,148,255,46,4,106,254,243,255,127,255,57,0,182,1,174,10,138,254,25,254,189,252,48,1,184,2,164,0,104,0,21,255,5,0,75,6,108,7,119,5,27,255,186,253,211,250,149,1,192,0,49,3,169,255,74,254,111,0,4,4,175,4,225,3,68,0,81,255,90,252,9,4,93,4,195,1,222,1,200,0,8,255,79,8,136,10,250,7,189,252,213,250,173,247,225,252,76,253,210,1,212,252,248,251,43,254,146,253,32,1,152,3,67,253,183,252,210,251,101,254,0,2,8,0,122,254,165,255,24,253,226,255,19,4,137,4,202,252,132,251,124,251,218,254,210,255,110,0,101,254,138,254,90,253,214,0,19,2,156,2,106,254,92,254,86,251,231,1,232,0,47,1,194,254,91,0,40,254,123,0,208,4,141,9,46,253,72,252,41,250,30,253,93,253,52,5,225,253,162,253,45,255,161,255,158,255,228,5,219,253,254,253,87,251,217,1,211,0,73,0,224,254,144,255,123,254,25,2,52,5,234,4,201,253,13,253,247,249,71,0,229,254,120,2,86,255,31,254,19,254,169,2,234,3,49,3,156,254,181,254,147,252,163,1,194,2,90,1,241,0,222,255,186,254,121,1,158,7,91,7,41,253,205,251,167,249,23,255,225,253,116,0,244,253,218,252,183,253,183,255,222,1,217,2,224,254,99,252,137,251,173,0,191,1,204,255,68,0,27,255,162,253,193,1,17,2,5,7,177,253,149,252,173,250,183,0,112,255,68,1,153,255,60,254,102,253,111,2,232,1,152,4,18,255,1,254,20,252,70,1,40,2,202,1,136,0,108,0,193,254,114,2,63,5,91,7,22,254,122,253,62,249,70,255,63,254,216,3,30,253,180,255,86,255,218,253,243,2,0,10,16,254,2,254,77,252,210,0,182,2,204,255,84,0,190,254,57,255,66,4,89,6,200,4,136,254,165,253,140,250,87,1,74,0,120,2,81,255,10,254,224,255,204,3,52,5,222,2,52,0,217,254,167,251,41,4,150,3,160,0,137,1,107,0,115,254,190,4,89,10,205,6,136,253,79,251,157,248,49,253,235,254,97,1,117,253,144,252,134,255,45,255,209,0,58,5,206,253,54,253,221,251,48,255,132,1,159,0,192,254,195,255,217,253,37,1,68,4,163,5,120,253,159,252,27,251,207,255,113,255,49,1,111,254,29,255,183,253,49,2,20,2,159,3,139,255,69,254,92,251,251,1,180,1,36,1,177,255,233,0,54,254,159,2,1,4,92,9,135,253,182,252,11,250,204,254,226,254,128,2,139,254,147,253,105,254,162,1,253,0,25,5,197,254,187,253,143,251,60,2,173,2,231,254,61,0,188,255,141,254,223,3,77,4,218,5,19,254,85,253,174,250,209,255,164,0,192,2,0,255,198,254,244,254,119,2,181,3,28,4,138,255,164,254,191,252,68,0,156,4,56,2,152,0,117,0,34,0,89,4,110,7,191,8,167,253,65,252,86,249,113,255,23,254,224,1,180,254,113,253,194,253,54,0,97,1,168,4,50,254,116,253,228,252,150,0,37,2,112,0,195,0,145,255,253,253,167,2,84,4,111,6,210,253,19,253,63,251,247,255,16,1,85,1,203,255,247,254,233,253,233,1,75,3,18,5,136,255,30,254,248,251,120,2,31,2,152,1,179,0,50,1,242,253,100,4,184,5,196,8,95,254,238,252,230,249,32,255,128,254,84,5,135,254,53,254,231,255,129,1,233,1,126,8,180,254,117,253,195,252,32,2,41,2,61,0,22,0,143,255,167,255,104,4,189,6,244,5,40,255,139,254,139,249,161,0,60,1,140,3,91,255,34,255,189,255,82,5,151,4,21,3,73,0,4,255,1,253,226,2,164,3,104,2,106,1,246,0,130,255,19,3,94,10,211,11,77,253,174,251,114,247,203,253,180,253,12,2,178,253,45,252,22,254,249,254,141,1,214,3,191,253,187,252,79,252,234,255,179,1,207,255,66,255,138,255,139,253,168,255,216,4,233,5,132,253,229,251,5,252,221,254,189,0,3,1,255,254,42,254,139,253,145,0,177,3,126,3,186,254,148,254,186,251,31,2,4,1,118,2,54,255,189,0,47,255,101,1,99,5,43,8,199,253,205,251,87,250,54,253,17,255,151,3,92,254,63,253,172,255,147,255,142,255,103,9,99,254,239,253,103,251,226,1,112,1,131,0,70,255,184,255,125,255,93,3,231,4,196,4,157,253,110,253,195,250,227,0,135,255,119,2,80,255,23,254,38,255,233,2,151,4,189,3,191,254,108,255,88,252,159,2,198,3,216,0,84,1,253,255,113,255,213,1,56,7,133,9,39,253,63,252,109,249,43,255,2,255,65,1,1,254,74,254,247,253,130,255,213,2,135,3,172,254,83,253,248,251,60,1,224,1,20,0,23,0,167,255,217,253,97,1,27,4,253,6,224,253,11,253,172,250,42,1,231,255,180,1,156,255,120,254,249,253,211,1,242,2,54,4,46,255,114,254,202,251,108,2,146,2,118,2],"i8",ALLOC_NONE,Runtime.GLOBAL_BASE+10240),allocate([33,0,147,0,78,255,153,3,151,6,129,7,187,254,240,253,70,248,2,0,227,254,142,3,141,254,22,254,26,255,0,0,85,2,218,7,16,254,117,254,190,252,37,0,177,3,245,0,181,0,96,255,112,255,201,5,93,5,77,5,157,254,167,253,10,251,42,1,66,1,160,2,63,255,176,254,77,0,65,4,253,5,154,3,177,0,217,255,155,251,228,3,13,3,24,2,200,1,110,1,80,254,135,5,136,9,231,8,46,254,10,253,235,246,209,254,3,254,131,1,41,253,211,253,66,0,111,255,131,2,224,4,224,253,92,253,108,252,31,255,94,3,76,2,104,255,40,0,235,253,167,1,143,5,22,6,196,253,181,252,135,251,128,255,85,0,205,1,18,255,255,254,184,253,93,2,236,2,93,3,24,0,54,255,127,250,29,3,231,1,47,1,75,255,108,1,74,255,104,2,98,5,126,11,18,254,172,252,95,250,220,254,61,0,44,3,172,255,45,253,74,255,43,2,20,2,226,5,147,254,19,254,223,251,54,3,76,2,11,0,242,255,238,255,26,255,233,3,121,5,171,5,38,254,199,253,244,250,46,1,62,0,38,4,186,255,136,254,34,255,214,3,206,3,125,4,60,255,22,255,229,252,223,1,74,4,243,1,106,1,58,0,70,0,123,4,21,8,41,11,25,254,146,252,224,248,73,0,224,254,92,1,154,254,12,254,4,254,199,0,209,2,218,4,178,255,71,253,229,252,105,1,24,2,196,0,118,1,110,0,33,253,79,3,27,4,104,7,146,254,55,253,98,251,59,1,64,1,173,1,72,0,41,255,62,254,247,2,118,3,83,5,226,255,84,254,190,252,93,3,115,2,28,3,118,0,212,1,233,254,75,5,91,7,101,7,68,255,126,253,180,249,63,0,81,255,174,4,94,254,45,255,51,0,158,1,75,2,41,10,22,255,211,253,166,252,168,1,121,3,222,0,136,0,155,255,83,0,133,5,230,8,103,5,172,255,67,254,147,250,158,1,57,1,21,4,29,0,169,254,65,0,16,6,111,6,212,3,183,0,165,255,195,252,249,4,133,5,104,1,41,2,16,1,149,255,51,6,77,12,43,10,104,5,29,8,92,13,244,19,86,26,186,31,135,38,84,43,170,49,133,53,61,254,215,251,239,253,231,250,62,254,12,253,15,254,161,252,128,254,149,253,99,254,99,253,195,254,230,253,181,254,212,253,98,254,4,254,88,254,134,254,238,254,188,254,78,254,154,253,30,255,12,254,24,255,254,253,249,254,135,254,214,254,102,254,105,255,58,253,82,255,206,252,107,255,100,254,100,255,83,254,224,254,50,254,70,255,53,255,86,255,210,254,65,255,191,254,125,255,109,255,215,254,117,254,28,255,42,255,11,255,64,255,189,255,196,254,185,255,185,254,152,255,51,255,162,255,73,255,113,255,218,255,63,255,161,255,16,0,180,255,132,255,8,255,23,0,19,255,24,0,12,255,18,0,120,255,44,0,145,255,223,255,232,255,231,255,0,0,149,0,19,0,23,0,113,255,158,0,87,255,174,0,75,255,133,0,201,255,165,0,230,255,111,0,84,0,98,0,75,0,87,0,183,0,141,255,245,255,248,255,130,0,11,0,170,0,254,0,77,0,205,0,17,0,183,0,112,0,6,1,194,0,202,0,31,1,95,0,189,0,214,255,151,255,234,0,179,0,39,0,186,0,163,0,89,1,76,1,199,0,43,1,161,0,202,255,29,1,178,255,25,1,123,255,141,0,74,255,111,0,249,0,85,1,15,1,108,1,93,0,147,1,75,0,135,1,92,0,254,1,118,255,220,0,71,255,227,255,222,255,105,1,141,255,64,1,3,0,42,2,99,0,30,1,218,0,79,2,11,255,150,1,244,254,197,1,0,0,68,2,25,0,94,2,19,1,20,2,148,0,194,1,183,255,227,2,227,254,6,2,224,254,94,0,53,255,162,2,116,255,182,255,205,0,202,2,142,255,43,1,176,0,155,3,182,0,45,2,240,0,193,2,240,255,1,2,229,1,81,2,37,1,128,1,195,1,105,2,218,255,50,0,51,2,17,2,47,1,209,0,203,1,107,1,177,1,196,1,194,1,198,1,111,1,94,2,221,1,229,2,176,1,97,1,112,1,11,1,105,1,204,2,17,1,71,2,197,1,166,0,254,1,172,0,201,0,117,2,18,1,191,0,56,2,127,2,46,1,42,1,122,2,131,1,131,2,94,1,75,2,48,2,100,2,53,2,88,2,20,3,231,1,160,2,0,2,247,3,65,1,77,1,101,1,86,3,131,255,157,1,218,1,200,2,17,0,105,255,52,2,29,1,14,1,15,255,203,3,121,3,233,1,220,0,254,1,128,3,37,2,156,3,71,1,57,3,34,1,143,3,28,2,84,4,158,0,37,3,199,0,189,3,255,1,218,2,100,0,106,3,13,0,23,3,179,1,120,2,164,2,204,3,249,0,132,3,211,1,194,4,13,3,50,4,73,2,17,3,233,255,157,2,11,1,19,4,107,2,60,4,103,2,121,4,110,2,137,3,148,3,25,4,80,0,75,1,72,2,51,4,89,0,127,2,220,3,193,3,2,3,208,2,30,3,187,2,236,1,191,1,131,3,115,2,15,1,164,4,213,2,53,5,87,0,91,2,64,3,67,6,104,2,103,4,122,3,225,5,232,3,132,4,98,3,241,3,227,3,59,3,125,4,90,3,49,3,170,5,5,3,40,5,244,1,109,5,56,1,129,4,236,255,60,4,64,0,3,5,2,0,148,4,143,1,77,7,2,2,170,6,246,1,100,6,118,3,242,5,160,1,88,2,107,4,70,5,251,4,110,5,121,3,3,7,146,3,230,6,227,0,159,4,226,4,34,7,249,1,62,7,151,3,49,9,57,255,175,1,152,0,199,6,43,255,228,255,136,1,54,5,103,255,204,255,210,3,127,4,189,254,112,254,45,3,167,6,120,255,84,0,169,5,223,7,181,254,113,255,119,255,168,4,0,255,22,2,99,255,7,4,205,254,73,254,30,2,219,2,183,254,92,254,159,255,104,2,150,254,88,255,190,254,110,1,9,255,146,255,45,255,89,0,60,255,203,254,20,0,59,0,148,254,49,254,226,254,89,0,176,254,175,0,80,254,141,0,133,254,66,255,78,254,60,255,177,255,150,0,234,254,29,255,232,254,166,0,213,253,90,254,101,255,29,2,146,254,54,0,227,255,173,255,211,254,250,252,186,0,116,2,115,254,248,254,242,0,37,1,59,255,183,253,124,0,154,1,53,0,123,255,10,0,84,1,198,253,215,251,65,0,66,254,68,0,19,254,127,1,169,3,155,254,57,253,153,254,6,255,91,253,212,251,36,1,230,255,107,1,6,0,95,2,33,5,129,255,246,255,233,5,94,7,201,2,204,3,189,5,133,8,163,5,224,7,161,249,192,249,252,248,14,247,253,251,22,249,180,251,23,248,3,251,148,250,169,250,2,250,77,252,75,250,52,252,12,250,25,252,58,251,4,252,108,251,209,252,37,252,32,252,165,250,64,251,18,252,247,250,186,251,24,253,12,251,13,253,243,250,162,252,101,252,119,252,40,252,90,253,229,251,83,253,230,251,193,251,39,252,218,251,89,253,35,252,127,253,153,251,48,252,6,253,114,253,134,252,218,252,191,252,189,251,62,253,139,253,147,253,218,252,128,253,212,252,249,252,134,253,245,252,225,253,28,252,203,253,205,251,188,253,222,253,157,253,196,253,149,253,8,253,222,254,145,252,242,253,201,252,50,254,229,252,3,255,215,253,97,254,179,253,73,254,235,253,172,254,76,253,89,252,7,254,252,252,66,253,149,251,249,254,206,254,53,252,29,254,67,254,182,255,213,253,220,253,154,253,127,255,75,253,22,255,116,254,10,255,37,254,6,255,247,254,108,254,136,254,254,253,95,254,2,254,212,254,199,254,178,254,104,253,49,254,210,252,126,254,64,253,175,254,153,253,22,255,55,255,23,255,17,255,89,255,201,253,53,255,149,253,109,255,97,254,141,255,160,254,90,255,18,253,85,255,7,253,242,254,145,252,248,254,121,252,145,254,24,253,43,0,37,254,14,0,115,253,43,0,98,253,11,0,64,254,197,255,247,253,130,255,137,255,101,255,155,253,214,255,161,252,229,255,93,252,136,0,29,254,183,0,44,254,55,0,214,254,55,0,208,254,57,1,159,253,57,1,48,253,66,1,89,255,100,0,227,253,253,255,137,255,145,255,69,255,233,0,20,255,4,1,22,255,26,0,91,255,134,0,211,255,216,255,219,253,104,1,53,255,122,1,124,254,194,1,129,254,19,1,20,0,182,0,153,255,246,0,145,255,175,1,37,0,206,1,110,255,231,1,99,255,228,254,197,255,247,1,72,255,24,0,53,0,253,255,54,0,122,0,3,1,77,1,66,0,228,1,104,0,180,1,68,0,195,0,116,0,190,0,206,0,13,1,247,255,226,1,96,1,126,1,29,1,143,1,21,1,196,1,0,1,69,0,186,0,13,0,41,1,243,255,3,1,161,255,30,0,56,0,138,1,196,0,169,1,205,0,200,1,25,1,65,2,15,0,191,0,119,1,34,1,151,1,64,2,200,255,227,0,32,2,149,1,0,0,37,2,164,255,16,2,27,255,95,1,11,255,82,1,150,254,179,1,167,0,15,2,181,255,46,1,91,0,56,3,129,0,87,2,240,1,167,2,186,0,237,2,153,0,225,2,231,254,88,2,164,254,103,2,20,255,1,3,41,0,113,3,38,0,122,3,36,255,73,3,155,254,115,3,119,254,135,3,134,253,218,1,68,254,82,3,81,255,166,2,19,254,242,0,249,253,17,3,54,253,70,2,227,253,110,1,225,253,178,1,171,253,244,1,3,253,222,0,66,253,149,3,25,253,194,3,155,252,245,1,125,252,36,2,133,254,200,0,77,254,157,0,205,252,214,0,163,252,157,0,154,253,40,0,136,253,94,0,141,252,202,255,27,253,4,2,11,254,42,1,154,253,85,255,154,252,95,255,159,252,233,255,206,252,93,0,9,252,245,254,106,253,153,254,219,253,2,0,70,254,135,255,135,254,0,0,29,255,33,0,98,254,130,255,127,255,212,0,90,252,34,0,198,251,230,254,161,251,244,254,58,253,199,252,92,254,65,255,204,251,96,252,107,252,163,255,140,253,154,254,97,0,7,0,50,255,119,254,155,255,24,0,53,255,38,0,88,255,83,0,169,253,89,254,233,254,170,1,68,253,118,0,181,255,206,0,43,252,95,253,88,253,161,1,145,254,37,0,233,254,218,1,127,255,194,254,63,1,40,1,142,253,217,255,87,1,90,2,72,253,217,255,209,254,172,3,104,0,233,0,132,254,137,0,220,255,13,1,181,255,42,255,120,0,43,0,239,253,35,254,203,1,164,0,54,255,27,255,207,255,89,255,97,2,24,3,98,0,36,255,147,3,148,0,37,1,27,1,101,3,91,0,63,2,138,1,70,1,178,255,205,2,67,0,109,1,189,254,104,2,220,255,219,2,27,0,107,2,238,0,120,2,17,1,192,1,99,0,33,3,220,1,101,3,17,1,173,2,64,0,21,3,72,0,253,3,217,0,25,3,203,1,222,2,104,1,134,2,224,1,104,1,66,1,173,1,208,1,126,2,174,1,244,2,107,1,232,3,148,1,171,2,16,2,90,2,103,2,143,2,157,1,178,3,175,2,169,3,90,2,136,3,92,2,43,2,225,2,18,3,150,2,211,1,142,2,106,1,77,2,161,3,198,2,242,1,222,1,159,1,164,1,181,2,115,3,45,3,171,2,13,3,157,3,145,3,171,3,214,2,220,2,235,1,85,3,19,2,180,3,222,2,195,3,59,1,40,3,249,2,243,2,120,4,248,2,143,2,52,4,58,3,33,4,67,4,70,3,235,3,40,3,23,4,109,4,147,2,77,4,224,3,26,4,50,4,51,4,203,3,182,2,202,4,30,4,59,2,73,3,116,3,124,5,99,5,72,4,56,4,93,3,207,4,223,2,4,5,248,2,248,4,223,3,87,5,29,4,233,4,188,2,26,4,22,2,220,3,197,1,240,4,87,2,116,4,167,2,85,6,47,3,104,5,9,2,37,5,137,1,28,6,37,3,168,5,174,2,44,4,136,2,107,3,51,1,59,4,105,1,23,4,61,1,137,5,196,3,163,2,59,2,128,4,79,0,90,4,209,255,250,5,55,1,185,6,58,1,142,4,177,2,2,2,162,255,93,1,26,1,132,5,72,1,1,4,231,1,191,255,57,0,37,3,202,3,36,0,62,0,1,3,249,254,23,3,166,254,125,2,187,2,119,255,108,2,22,2,29,2,33,253,194,0,199,2,44,1,244,254,161,252,158,3,1,3,60,253,84,254,250,1,174,0,132,252,138,253,179,1,35,2,101,250,254,254,109,2,215,1,6,252,168,250,119,254,9,2,104,252,82,253,231,255,20,0,42,252,124,251,84,1,9,0,234,249,145,251,160,254,48,0,213,249,110,254,137,252,6,0,124,251,136,252,220,253,160,254,149,249,112,251,97,255,98,2,24,248,61,252,31,255,193,0,136,249,88,248,11,255,19,254,60,252,112,249,88,252,133,253,237,250,48,249,148,250,164,253,252,249,189,252,139,250,121,255,204,249,222,254,122,249,56,253,37,248,160,249,129,249,229,255,46,247,213,252,123,251,184,0,15,251,189,0,169,250,74,2,37,248,201,0,234,252,200,2,70,251,3,0,247,251,40,3,29,251,62,3,145,255,123,2,156,249,191,1,49,254,75,252,67,254,96,252,8,254,118,251,11,254,69,251,144,0,161,254,140,254,228,251,229,254,221,251,233,254,157,251,193,253,98,250,181,253,178,249,89,252,40,252,229,0,178,2,103,252,49,253,109,254,82,5,83,253,47,254,106,3,141,1,3,254,210,255,61,1,54,5,27,254,200,1,45,3,183,1,101,254,83,1,130,3,43,4,87,254,46,0,161,5,241,1,115,252,224,252,185,5,22,4,2,255,191,254,150,5,141,4,68,0,94,1,10,4,154,2,114,1,11,0,31,5,22,3,143,0,232,0,17,4,26,6,142,255,151,2,80,6,54,4,198,1,67,2,251,4,16,4,180,255,141,3,240,2,43,4,153,0,0,2,92,1,190,4,102,2,129,1,51,7,40,3,13,1,10,4,203,0,62,4,140,2,249,3,247,6,106,4,173,1,47,5,131,1,104,5,207,255,159,4,184,255,191,4,96,254,233,3,32,2,213,6,160,254,199,4,10,254,175,4,179,253,57,2,29,255,94,6,114,255,42,6,26,255,179,6,54,253,8,5,186,252,118,5,107,4,77,5,48,255,208,4,181,1,197,3,95,252,50,3,43,3,130,5,91,3,227,5,164,0,188,4,107,5,1,7,228,1,82,7,200,1,15,8,228,3,146,4,46,5,122,5,36,5,80,5,111,4,238,4,210,4,82,6,81,5,232,6,141,5,203,4,48,6,67,5,86,3,160,2,149,6,30,6,115,4,246,4,224,7,33,7,237,6,45,6,252,5,180,5,207,5,178,3,123,6,253,3,208,6,188,4,112,5,209,3,236,6,137,4,34,7,140,4,182,6,149,5,181,7,55,6,161,4,96,3,84,8,37,4,7,7,46,3,46,7,245,2,56,8,35,5,6,8,234,4,65,8,147,3,27,9,162,3,187,5,123,4,30,10,159,5,197,8,208,6,42,8,84,6,54,9,174,5,106,10,226,5,84,7,45,7,22,8,183,7,203,6,41,6,170,2,9,5,48,6,253,7,174,5,50,8,194,9,212,7,151,10,18,8,214,2,52,6,196,10,32,9,228,0,79,3,152,9,123,6,36,0,45,1,150,7,165,7,66,254,160,255,106,8,116,5,253,5,77,4,14,0,96,2,101,252,36,253,103,5,190,7,65,5,184,3,88,253,65,1,1,5,244,4,198,249,109,1,173,3,178,3,55,249,202,252,70,9,227,10,29,7,228,10,236,248,29,247,169,248,23,246,152,249,200,248,97,249,44,248,60,251,136,248,59,251,198,247,233,249,204,249,219,249,236,249,85,251,177,249,56,251,65,249,177,250,129,251,176,249,100,248,6,251,145,250,231,250,133,250,185,249,101,251,116,249,225,250,93,250,58,250,169,250,126,252,24,251,221,251,205,250,146,251,42,252,147,251,131,251,32,250,200,251,228,250,4,252,97,251,44,252,50,250,57,252,41,250,36,252,102,252,233,251,203,251,186,252,101,251,166,252,58,251,149,251,239,251,216,251,1,253,152,252,123,251,67,253,144,252,62,253,118,252,250,252,8,252,190,253,200,251,223,252,58,250,177,253,169,251,176,253,134,251,55,253,148,250,128,253,160,250,171,253,221,251,96,254,121,252,82,253,192,252,107,253,60,253,68,254,156,252,22,254,103,252,138,254,248,252,149,253,110,251,183,253,219,253,255,252,229,252,77,254,109,253,238,253,27,253,14,254,187,252,155,254,171,253,233,254,153,252,13,255,137,252,230,254,103,253,232,254,101,253,91,255,208,253,118,254,121,252,150,254,102,254,64,254,185,253,103,254,194,253,199,254,155,254,131,253,220,253,198,253,76,254,128,252,8,254,130,254,11,253,198,255,31,254,91,255,150,253,65,255,138,254,22,255,130,254,34,255,85,253,231,255,32,254,94,254,153,254,38,253,159,254,188,254,99,255,80,254,190,254,118,254,209,254,228,254,152,255,167,253,223,254,212,253,60,255,180,253,106,255,109,253,160,253,39,254,232,255,188,255,64,254,38,254,248,255,6,254,211,255,20,253,72,255,180,252,4,255,123,252,165,255,184,253,159,255,116,253,138,0,4,253,125,255,90,253,244,255,98,253,165,0,253,254,253,255,184,252,149,255,115,252,37,0,32,252,44,0,170,252,97,254,185,252,13,0,23,252,241,254,254,251,203,254,226,252,34,254,192,252,24,254,81,252,168,0,168,251,125,254,95,251,155,255,97,251,216,255,83,252,196,254,250,251,254,252,236,251,143,253,199,251,230,253,56,251,213,254,224,250,76,254,83,251,105,253,113,251,95,255,64,251,78,253,43,251,193,252,104,250,48,253,133,250,19,254,126,252,28,253,102,252,223,252,178,251,110,254,213,249,60,252,219,251,130,253,11,251,98,250,37,250,90,252,34,250,129,252,194,249,204,253,69,249,51,253,162,253,171,253,114,251,195,251,167,250,44,254,102,248,43,250,210,248,71,252,116,248,93,252,37,250,68,255,157,249,91,254,79,250,174,254,88,250,234,255,106,248,90,254,42,248,7,255,16,254,142,255,138,248,13,253,247,250,174,0,85,250,147,255,30,254,255,254,59,251,4,254,175,249,151,0,98,249,208,0,114,253,107,0,141,249,29,0,139,251,23,1,65,251,50,1,52,251,6,254,38,253,81,255,44,251,155,255,55,252,39,2,154,252,22,1,201,252,59,1,205,253,120,1,229,251,228,0,5,254,24,1,169,253,25,1,10,253,253,0,207,254,123,1,13,253,122,255,157,253,148,2,200,252,24,2,207,252,134,2,99,254,49,0,171,254,177,0,59,254,14,2,30,254,77,2,185,255,83,1,111,253,8,1,12,255,39,1,19,255,59,1,125,254,57,2,6,254,247,255,135,254,14,0,96,255,149,2,40,255,40,0,204,254,210,255,95,0,214,0,14,255,167,0,170,255,192,0,200,255,27,0,180,255,31,0,36,0,53,1,150,255,74,255,143,255,74,0,71,254,234,255,23,0,139,0,81,0,245,255,44,0,15,0,169,255,119,255,138,255,49,255,98,255,198,255,16,1,164,255,100,255,71,254,8,0,120,255,128,0,35,255,101,0,38,255,40,0,59,255,180,255,56,254,9,0,67,254,33,0,89,254,226,0,60,0,73,0,34,255,156,0,113,254,24,1,194,254,245,0,171,254,166,0,13,254,83,1,66,255,71,1,37,255,69,1,119,255,167,255,172,253,100,0,141,253,144,0,91,253,231,1,28,0,252,0,121,254,214,0,215,255,26,1,228,255,99,0,226,254,75,1,49,0,203,1,124,254,53,2,143,254,180,1,28,0,80,1,247,255,141,1,89,255,106,2,34,0,84,2,239,255,49,2,116,255,43,1,79,0,10,2,125,0,203,0,2,0,244,0,32,1,255,0,211,0,175,0,82,0,84,2,187,0,5,2,108,0,125,1,255,0,109,1,41,1,241,1,96,1,71,1,174,255,25,0,210,0,115,1,245,0,5,1,3,0,33,2,193,1,140,0,38,1,44,0,39,1,212,0,91,1,244,0,238,1,75,1,16,2,201,0,51,1,93,1,155,1,101,2,28,1,102,2,157,1,208,1,66,1,112,2,141,1,97,0,200,0,96,255,128,1,149,0,106,1,239,1,13,2,13,1,73,2,33,0,235,1,135,255,177,1,171,1,99,2,242,1,4,2,171,0,187,1,241,1,154,2,184,1,19,1,54,2,63,2,146,0,127,2,155,0,158,2,223,255,173,0,212,0,184,2,90,255,89,2,65,255,183,2,23,254,247,1,175,0,230,2,214,0,220,1,116,1,59,4,66,2,18,2,74,2,9,3,169,1,106,3,59,1,73,3,118,1,80,3,91,255,53,2,35,0,223,3,217,255,38,4,73,1,200,2,18,3,72,3,133,2,27,3,149,2,164,2,59,2,150,3,120,2,55,4,161,2,49,3,62,1,132,1,106,3,244,3,52,2,80,3,112,3,108,2,45,2,223,1,159,2,197,1,180,2,212,1,72,3,130,2,76,3,133,2,250,1,172,1,129,3,55,2,69,3,131,1,194,3,243,1,179,2,49,2,171,3,158,3,15,3,40,1,22,3,12,1,4,4,18,2,106,3,73,1,36,2,143,0,163,2,35,1,247,1,66,0,17,4,103,1,18,3,97,0,37,3,33,0,69,3,214,1,255,1,49,0,68,4,71,1,150,4,67,1,3,0,242,0,104,3,218,1,177,2,173,1,49,5,166,2,18,4,108,2,85,4,152,2,65,1,193,0,121,3,182,3,129,4,106,3,125,3,123,2,109,3,94,3,180,3,145,3,13,5,153,2,40,5,127,2,229,3,25,3,122,5,6,4,152,4,244,3,86,4,191,3,130,5,157,3,123,5,147,3,31,2,94,3,92,4,198,4,67,3,166,4,67,3,166,4,191,3,124,4,123,4,96,5,20,5,169,4,135,5,207,4,55,5,61,5,234,2,68,4,175,6,3,5,109,5,49,4,54,5,30,6,129,4,195,5,109,6,113,4,33,7,196,4,32,4,102,5,241,5,194,6,96,6,9,6,84,6,6,6,87,3,60,6,97,3,131,6,181,2,117,3,180,6,239,5,143,4,16,5,161,8,224,6,160,7,213,5,228,7,202,5,254,5,74,7,158,6,216,7,30,6,236,2,225,6,57,3,38,1,112,5,60,4,10,8,109,2,35,5,109,1,7,5,198,0,4,4,232,1,128,5,249,0,147,1,246,3,25,6,68,1,107,1,109,6,20,4,193,0,111,1,242,7,67,7,5,255,67,2,238,2,226,3,13,255,30,0,45,5,111,3,228,255,87,255,112,2,149,3,59,254,159,0,186,0,90,5,154,253,6,0,25,2,136,1,162,255,221,254,13,3,229,0,128,255,214,254,245,0,235,1,67,253,120,253,204,3,21,3,11,254,128,253,178,0,255,0,147,254,122,254,1,255,61,1,66,252,218,254,65,255,228,0,249,252,65,254,157,0,19,255,111,253,48,253,105,254,92,0,139,255,157,253,78,1,26,255,89,253,196,251,112,255,195,254,123,252,163,252,30,253,152,254,171,255,41,253,166,255,237,252,100,0,234,255,121,254,249,254,200,255,183,255,175,254,14,253,5,0,67,255,62,253,144,253,89,0,168,254,121,255,167,251,159,254,19,255,84,253,145,251,237,254,178,251,243,254,77,251,152,0,145,0,46,253,48,251,49,0,80,0,32,251,248,252,8,255,135,1,36,253,221,253,213,1,218,0,1,255,160,252,69,0,110,1,90,255,27,254,80,253,191,0,68,251,84,251,86,255,87,255,228,250,161,249,65,1,214,1,117,250,37,251,192,255,16,1,175,250,8,255,236,1,53,2,47,253,159,253,195,0,229,1,195,253,123,255,171,1,202,0,85,255,138,255,199,0,63,2,2,0,225,255,182,2,243,2,170,250,217,255,40,2,45,2,23,254,15,1,168,2,25,2,13,0,59,254,87,3,186,3,123,255,204,255,175,255,226,2,111,251,125,2,31,4,35,4,161,255,164,2,235,4,57,4,233,1,49,1,63,254,186,3,234,253,228,3,55,252,98,3,222,251,35,4,242,250,106,2,120,250,105,2,54,254,86,5,97,255,29,7,250,252,240,253,242,255,86,4,78,251,123,252,252,252,177,1,24,251,25,251,13,252,210,254,166,253,183,253,9,253,174,249,8,253,243,249,184,252,127,248,208,252,229,253,23,249,69,247,29,255,220,255,14,248,217,248,197,247,154,251,89,246,232,248,66,250,252,0,115,245,97,254,197,253,45,254,229,5,18,6,132,8,183,7,22,9,228,7,191,248,111,249,191,248,37,249,248,247,130,251,170,247,138,249,173,249,181,251,88,249,149,251,191,250,184,249,177,250,154,249,198,250,243,250,211,250,15,251,128,249,143,249,49,250,173,252,190,250,216,248,123,250,116,247,254,250,87,253,7,249,143,249,58,252,198,251,97,251,116,249,226,251,207,251,138,251,122,251,73,251,24,253,6,251,27,252,90,252,153,250,97,252,120,250,14,252,231,250,241,252,69,252,231,251,124,252,31,252,207,252,31,253,201,252,52,252,91,251,30,253,186,251,30,253,126,251,240,252,223,252,214,252,238,252,132,252,248,253,24,252,206,252,124,253,59,252,191,253,142,252,227,253,74,253,97,253,107,252,173,253,126,253,122,253,153,253,68,252,147,253,99,252,253,253,41,253,29,254,209,252,27,254,184,252,190,253,72,254,55,253,190,253,187,254,111,253,98,253,126,254,198,253,71,254,102,253,254,253,237,252,120,254,239,253,246,253,59,254,25,254,89,254,152,253,183,253,151,253,99,255,106,253,244,254,88,253,164,254,190,254,189,254,136,253,68,254,208,254,82,254,180,254,54,254,235,254,44,254,109,253,231,252,193,254,132,253,29,255,214,253,139,254,165,254,178,254,46,255,56,254,64,255,238,253,14,255,40,255,58,255,146,254,142,254,174,254,95,255,103,254,20,253,149,255,132,254,218,254,125,253,33,255,103,253,22,255,27,253,115,255,16,254,126,255,2,254,117,255,185,254,84,255,207,254,206,254,188,253,92,255,249,254,250,254,84,255,189,255,110,254,31,0,146,254,246,255,76,254,170,255,241,253,71,0,135,254,234,255,159,253,244,255,90,253,189,255,193,254,63,0,65,255,35,0,75,255,217,255,14,255,126,0,89,255,116,255,224,253,155,0,215,254,174,0,215,254,38,0,248,255,117,0,132,254,197,0,60,254,240,0,246,253,223,0,153,255,110,0,69,255,87,0,101,255,169,0,209,255,157,0,26,0,173,255,156,255,128,0,80,0,209,0,194,255,6,0,7,0,22,0,5,0,62,1,236,255,248,0,211,255,56,255,193,255,156,0,187,255,250,0,73,255,113,1,130,255,143,255,180,255,114,255,134,255,192,255,2,255,225,255,35,0,79,255,185,255,249,255,171,0,93,0,27,0,108,0,212,0,182,254,47,255,133,255,186,255,233,254,95,0,160,255,20,0,68,255,195,255,198,254,87,0,212,254,178,255,158,254,122,255,11,0,122,0,116,255,122,0,237,254,152,0,219,254,140,0,174,255,138,0,191,254,145,255,32,254,100,255,153,254,76,0,2,255,216,255,133,253,160,255,246,253,79,0,5,254,8,0,244,254,47,1,229,253,68,0,66,254,61,0,246,253,50,1,111,0,189,0,77,254,122,0,133,254,166,0,197,253,114,254,136,253,182,255,21,253,161,255,57,254,194,0,72,252,83,0,226,252,192,0,13,253,192,0,243,252,94,255,149,253,234,0,105,253,215,254,24,254,147,255,60,252,124,255,186,252,188,255,181,252,58,0,168,251,170,255,219,252,213,254,80,252,3,255,246,252,206,255,59,252,219,253,160,254,158,255,32,252,169,254,163,251,197,254,163,251,205,254,125,251,138,254,131,253,26,255,114,251,213,255,237,250,156,255,99,252,119,254,6,251,168,253,79,253,126,255,57,250,200,254,215,250,2,255,72,250,70,254,244,250,155,253,19,251,9,254,35,250,144,254,214,250,26,0,104,250,190,255,49,249,95,255,148,249,45,254,32,249,220,253,143,250,200,253,236,249,153,252,41,250,246,251,149,250,197,253,131,248,240,253,9,249,133,255,151,248,25,255,250,247,189,254,252,247,118,252,72,248,201,253,131,248,148,253,1,248,35,252,203,251,142,254,17,248,64,253,205,246,19,253,76,245,191,251,139,248,159,0,36,248,248,0,142,253,133,255,221,246,62,252,99,253,104,254,157,250,106,251,60,254,148,254,236,251,33,253,124,255,183,0,172,249,16,253,221,253,205,254,247,252,19,251,158,255,41,0,144,252,189,251,255,254,97,0,190,249,215,248,31,0,230,255,124,253,207,253,76,255,222,253,127,254,185,251,102,254,222,252,98,254,197,252,55,254,54,252,22,254,171,251,41,255,108,252,112,255,87,252,19,254,11,251,251,253,29,250,181,0,101,0,180,254,135,252,188,252,87,252,209,253,83,254,139,253,221,253,73,255,175,254,223,253,174,255,6,255,226,254,5,0,124,255,164,254,4,255,219,254,40,254,98,255,100,0,227,255,197,0,20,255,88,254,163,252,43,255,116,255,249,255,85,254,69,254,187,0,159,255,84,253,32,253,219,254,2,1,144,254,104,255,106,255,136,1,159,253,175,0,114,255,43,1,118,255,152,0,137,255,73,1,26,254,204,255,37,1,198,0,73,255,117,0,175,0,75,1,198,255,238,254,231,0,44,1,224,254,74,1,207,254,116,1,145,255,153,1,247,255,167,1,83,0,0,1,67,0,111,1,237,255,248,0,91,0,113,0,221,255,150,1,65,255,154,0,238,0,40,1,5,0,197,0,141,0,221,0,57,1,198,0,211,0,165,1,244,0,78,1,88,0,170,1,13,255,198,1,202,0,40,2,251,255,147,1,35,1,185,0,219,0,45,1,251,0,138,0,128,0,69,0,197,0,32,1,116,255,195,255,188,0,105,1,197,0,86,2,186,1,17,1,34,1,143,0,216,1,226,1,157,0,114,1,159,1,65,1,116,1,129,1,146,1,40,2,155,0,24,0,38,2,7,1,245,255,21,0,104,1,227,0,147,0,2,255,168,1,97,0,110,1,243,255,119,1,141,0,193,1,232,0,140,1,251,1,218,1,16,1,189,2,68,1,106,1,209,255,75,2,148,0,31,2,69,0,144,1,205,255,49,2,59,0,220,0,246,255,96,1,147,0,206,0,211,0,141,2,185,0,51,2,41,1,53,2,28,1,82,2,121,0,254,2,192,0,142,1,118,0,130,2,178,1,233,0,8,1,225,1,211,1,129,0,91,255,187,2,239,0,90,0,26,0,86,1,218,1,201,255,27,0,132,1,94,0,84,255,0,0,213,2,123,1,196,255,81,1,114,1,209,1,95,0,63,1,38,3,83,2,78,0,4,1,241,1,83,3,210,0,48,2,202,1,62,2,48,254,202,0,241,1,113,2,54,255,152,0,48,0,200,2,236,255,54,2,100,0,203,2,199,1,212,1,155,1,93,2,63,1,134,2,195,0,103,2,145,1,26,2,168,2,227,2,201,0,155,2,178,1,186,3,198,1,169,1,134,2,235,1,94,2,169,2,160,1,252,1,241,1,54,3,170,1,47,3,148,2,135,2,116,2,204,2,185,2,210,1,106,2,201,1,173,2,204,1,109,1,53,1,209,2,55,2,68,3,89,2,97,2,44,1,57,3,203,1,175,3,175,2,169,2,21,2,147,3,86,2,79,2,243,0,108,3,195,1,106,3,164,1,18,3,61,1,220,2,220,0,154,3,61,1,84,4,111,1,19,2,210,1,4,4,137,2,29,4,103,2,10,4,41,2,61,3,90,2,253,3,31,3,159,3,35,3,110,3,251,2,31,3,240,1,93,5,5,3,73,2,2,3,35,3,162,3,75,4,25,3,198,4,94,3,185,4,127,3,1,4,215,2,4,3,77,3,148,4,91,4,99,3,253,3,62,3,245,3,73,3,142,3,250,1,191,2,215,4,53,4,108,2,51,3,172,4,59,4,131,4,57,4,118,4,139,3,11,6,97,4,29,5,136,2,63,5,100,2,204,5,220,3,199,5,169,3,217,3,48,5,187,3,61,5,173,1,142,3,73,3,58,5,52,2,155,4,156,1,132,4,147,5,40,5,154,5,50,5,128,2,248,2,190,6,130,5,190,0,43,2,49,4,237,3,170,1,1,1,71,3,212,3,235,0,231,0,240,5,143,4,109,0,37,1,246,3,33,6,49,1,142,0,124,4,27,2,221,254,148,255,189,4,204,3,22,0,40,255,155,2,60,3,30,254,182,1,197,1,151,5,187,253,90,254,21,3,131,1,154,254,58,254,174,0,12,3,220,255,140,254,134,1,122,255,139,253,160,0,206,254,239,2,22,251,181,254,177,0,10,2,8,255,62,2,5,255,127,2,237,253,151,1,172,253,138,1,93,254,21,3,151,253,33,3,38,252,143,1,167,252,215,2,249,255,6,2,65,253,54,1,137,251,232,255,22,252,31,1,64,252,107,1,237,250,56,1,2,250,245,0,235,249,49,1,28,0,153,0,165,252,81,255,223,255,76,1,138,250,102,255,212,0,154,1,175,253,59,255,188,251,64,253,120,252,191,255,26,1,111,1,106,252,82,253,89,1,93,0,254,254,155,254,184,2,132,2,75,253,228,255,192,1,237,1,239,254,193,0,15,2,34,2,13,255,255,253,128,1,120,255,17,1,159,254,0,2,114,255,25,2,58,255,173,3,238,2,83,0,248,0,66,2,93,3,200,255,80,2,74,3,44,0,124,3,24,0,33,0,122,3,240,255,214,3,63,3,118,5,255,5,106,7,180,6,96,5,156,7,185,5,22,252,95,252,184,251,77,251,127,253,93,252,164,253,63,252,245,252,95,253,189,252,236,252,96,254,104,253,54,254,2,253,116,253,247,253,106,253,17,254,1,252,3,254,1,252,84,254,68,254,216,253,144,254,63,254,33,254,45,255,226,251,121,252,196,254,7,255,199,253,177,253,199,253,237,254,227,253,65,255,52,253,68,255,182,252,248,254,179,254,8,255,194,254,28,255,237,254,1,0,201,253,28,255,141,255,35,255,18,255,138,254,59,255,5,254,34,255,189,253,254,254,80,254,195,255,12,255,167,254,2,0,174,254,39,0,41,255,87,255,198,255,0,0,200,255,250,255,53,255,125,255,1,0,70,255,251,255,45,255,6,0,132,254,11,0,94,254,140,255,131,0,122,255,113,0,89,0,252,255,71,0,254,255,237,255,64,255,6,1,24,0,189,0,151,0,123,255,147,255,186,0,103,255,166,0,37,255,37,0,139,0,193,0,171,0,81,1,124,0,158,0,195,255,141,0,226,0,243,255,190,0,231,0,34,0,98,1,109,0,60,1,201,0,244,0,164,0,74,1,171,255,134,1,172,255,254,0,71,1,1,1,79,1,235,1,147,0,220,1,105,0,54,0,77,0,181,1,114,1,165,1,58,1,193,1,86,1,73,1,126,0,161,2,36,1,59,2,132,1,243,0,193,0,141,2,64,1,109,2,24,1,194,0,124,1,5,2,69,2,45,0,67,1,111,0,166,1,233,1,139,1,222,2,22,2,110,2,34,2,230,1,246,1,62,1,60,2,189,0,38,2,129,1,166,1,99,255,153,0,131,255,126,1,59,255,130,1,249,254,78,1,228,0,185,2,68,255,1,0,51,0,41,1,5,254,213,0,136,254,141,1,232,255,255,0,221,253,89,0,10,254,162,255,131,1,179,0,148,253,68,0,84,253,112,0,126,253,162,254,252,254,172,0,74,254,188,254,8,1,136,2,60,252,252,255,159,251,7,0,122,255,134,0,147,251,206,254,143,0,96,0,92,254,15,254,59,251,162,254,9,250,83,253,95,255,72,0,105,3,179,2,220,2,27,1,153,3,97,1,78,1,219,1,71,4,53,3,96,3,12,2,75,3,241,1,202,2,199,2,20,3,238,2,52,4,202,2,180,4,241,2,65,2,150,2,124,245,170,192,38,3,44,7,95,251,33,228,37,12,28,4,40,248,202,208,85,16,107,5,192,249,99,218,69,9,145,5,232,249,78,219,176,12,193,7,210,251,214,230,35,7,16,9,184,252,64,236,173,3,242,12,199,254,163,248,47,9,161,11,41,254,234,244,32,14,116,9,247,252,183,237,123,13,24,12,98,254,70,246,139,11,205,16,72,0,178,1,56,7,148,17,139,0,68,3,44,15,40,21,157,1,180,9,163,4,42,28,67,3,166,19,11,12,40,35,139,4,90,27,216,28,115,3,37,247,177,202,74,23,226,5,58,250,60,221,35,20,86,8,61,252,88,233,8,31,217,7,228,251,65,231,107,25,202,8,139,252,49,235,246,29,192,10,180,253,47,242,64,23,200,11,60,254,92,245,34,19,180,14,131,255,17,253,77,27,4,14,60,255,103,251,238,31,138,15,213,255,252,254,176,23,52,17,107,0,133,2,29,30,223,19,64,1,136,7,147,21,133,23,57,2,98,13,89,30,214,27,50,3,62,19,172,23,2,31,209,3,253,22,218,21,223,44,243,5,212,35,85,41,76,5,159,249,153,217,89,35,61,6,145,250,68,223,66,38,243,7,247,251,180,231,242,34,111,9,244,252,164,237,56,40,24,10,87,253,253,239,191,36,174,10,171,253,245,241,252,33,146,12,156,254,160,247,29,38,67,13,235,254,123,249,193,39,52,15,181,255,58,254,210,35,176,17,148,0,123,3,168,39,140,19,40,1,245,6,154,35,103,22,241,1,177,11,4,41,122,24,116,2,198,14,126,39,207,29,151,3,158,21,140,34,23,34,93,4,72,26,252,34,208,48,112,6,193,38,124,50,208,3,185,247,47,206,171,44,219,6,28,251,141,226,106,47,24,9,189,252,96,236,124,44,64,9,214,252,248,236,204,41,248,11,83,254,236,245,44,48,45,11,238,253,136,243,202,45,255,12,205,254,200,248,6,44,116,14,106,255,120,252,109,42,61,17,110,0,151,2,50,47,181,17,150,0,134,3,19,44,85,20,98,1,84,8,184,46,161,24,125,2,253,14,159,43,110,29,132,3,44,21,96,47,137,32,25,4,168,24,217,42,25,42,149,5,156,33,60,40,224,67,87,8,53,50,75,54,145,6,220,250,15,225,36,49,253,7,254,251,221,231,209,51,135,9,2,253,254,237,209,54,173,11,47,254,14,245,140,52,26,12,99,254,78,246,108,48,74,14,89,255,18,252,198,52,196,14,137,255,55,253,80,50,176,16,62,0,118,1,221,52,253,18,253,0,243,5,123,49,81,21,168,1,248,9,30,54,218,23,78,2,223,13,231,50,83,25,166,2,244,15,245,52,41,30,169,3,7,22,157,50,95,36,189,4,136,28,146,53,31,45,252,5,5,36,47,49,102,59,146,7,147,45,9,59,4,6,91,250,4,222,224,58,29,9,192,252,113,236,191,56,207,9,45,253,0,239,100,57,127,12,147,254,107,247,22,60,232,13,49,255,33,251,53,55,120,15,206,255,212,254,254,58,140,16,50,0,42,1,252,55,216,18,242,0,174,5,254,57,75,21,166,1,238,9,202,59,195,23,72,2,190,13,249,55,232,26,0,3,15,18,212,58,9,30,162,3,226,21,70,56,210,36,207,4,245,28,27,60,13,38,0,5,26,30,232,57,191,55,52,7,94,43,32,53,107,97,109,10,195,62,12,64,177,7,198,251,139,230,177,65,16,11,223,253,45,243,97,61,27,11,229,253,80,243,232,62,8,13,209,254,223,248,0,64,123,15,207,255,218,254,44,66,227,17,165,0,224,3,95,61,247,17,171,0,6,4,94,63,72,21,165,1,233,9,192,65,238,24,143,2,105,15,129,61,229,27,53,3,80,19,198,63,45,29,120,3,223,20,227,64,176,33,76,4,222,25,132,66,178,40,99,5,111,32,33,62,41,46,29,6,207,36,238,65,98,57,95,7,96,44,131,64,134,81,102,9,147,56,222,70,35,8,25,252,131,232,201,75,106,12,137,254,47,247,100,68,98,13,248,254,203,249,86,78,187,15,231,255,105,255,149,70,153,16,54,0,70,1,8,74,202,19,58,1,98,7,47,69,26,21,153,1,157,9,123,77,48,24,98,2,92,14,30,70,102,27,27,3,176,18,70,83,197,30,198,3,184,22,246,69,73,36,186,4,115,28,200,74,74,36,186,4,116,28,37,80,117,44,230,5,129,35,155,70,149,56,74,7,226,43,31,78,218,69,129,8,52,51,154,73,252,127,0,12,62,72,61,42,81,112,63,11,181,67,0,80,225,10,198,253,153,242,153,73,194,25,191,2,139,16,81,24,245,28,108,3,156,20,51,67,204,40,103,5,133,32,122,84,245,4,61,249,74,215,143,82,71,17,113,0,171,2,40,44,20,6,106,250,95,222,61,74,20,50,150,6,164,39,215,67,194,9,37,253,210,238,194,69,225,18,244,0,192,5,10,39,194,9,37,253,210,238,122,68,184,30,196,3,170,22,174,55,92,7,133,251,5,229,20,62,81,12,125,254,233,246,61,26,10,7,67,251,121,227,10,71,225,78,53,9,109,55,102,70,215,11,67,254,138,245,71,65,225,22,16,2,109,12,143,34,174,15,226,255,76,255,20,62,10,35,134,4,60,27,102,70,112,5,198,249,129,218,71,65,0,16,0,0,0,0,0,32,143,2,108,245,79,192,133,59,102,54,16,7,132,42,174,55,40,12,106,254,116,246,10,55,61,18,193,0,141,4,30,21,143,10,154,253,143,241,122,52,153,25,182,2,84,16,163,48,133,3,67,247,100,203,163,48,102,10,131,253,7,241,184,14,143,2,108,245,79,192,153,57,215,91,22,10,183,60,225,74,153,9,13,253,62,238,184,78,215,19,62,1,121,7,225,26,0,16,0,0,0,0,0,80,112,33,65,4,156,25,204,76,225,2,26,246,105,196,61,74,163,16,58,0,91,1,184,30,40,8,29,252,151,232,204,44,0,48,87,6,43,38,20,62,194,5,26,250,126,220,112,61,20,18,180,0,62,4,215,35,153,5,240,249,131,219,184,62,92,27,25,3,164,18,235,57,225,2,26,246,105,196,225,58,204,8,140,252,55,235,215,19,204,4,12,249,38,214,215,51,174,67,83,8,27,50,163,64,30,9,193,252,118,236,225,58,184,22,6,2,46,12,92,15,102,14,100,255,86,252,174,55,153,33,72,4,198,25,235,65,10,3,106,246,74,198,225,58,225,14,149,255,122,253,174,23,102,2,12,245,17,190,122,36,40,36,180,4,83,28,215,51,225,6,33,251,172,226,215,51,194,13,33,255,193,250,153,9,174,7,196,251,127,230,204,44,153,21,187,1,108,10,245,40,225,2,26,246,105,196,112,45,122,12,145,254,92,247,194,5,10,3,106,246,74,198,0,64,248,65,226,67,190,69,142,71,82,73,12,75,188,76,98,78,0,80,150,81,35,83,170,84,42,86,163,87,22,89,130,90,234,91,76,93,168,94,0,96,83,97,161,98,236,99,49,101,115,102,177,103,235,104,34,106,85,107,132,108,177,109,218,110,0,112,35,113,67,114,97,115,123,116,147,117,169,118,188,119,204,120,218,121,230,122,239,123,247,124,252,125,255,126,255,127,255,127,61,10,63,10,69,10,78,10,91,10,108,10,129,10,153,10,181,10,212,10,248,10,31,11,74,11,120,11,170,11,224,11,25,12,86,12,151,12,219,12,35,13,110,13,189,13,15,14,101,14,190,14,27,15,123,15,223,15,70,16,176,16,30,17,143,17,3,18,123,18,245,18,115,19,244,19,120,20,0,21,138,21,23,22,168,22,59,23,209,23,106,24,6,25,165,25,70,26,234,26,145,27,59,28,231,28,149,29,70,30,250,30,176,31,104,32,35,33,224,33,159,34,97,35,36,36,234,36,178,37,124,38,71,39,21,40,228,40,181,41,136,42,93,43,51,44,11,45,228,45,191,46,155,47,121,48,88,49,56,50,26,51,252,51,224,52,196,53,170,54,145,55,120,56,96,57,73,58,51,59,29,60,8,61,243,61,223,62,203,63,184,64,165,65,146,66,127,67,108,68,90,69,71,70,52,71,33,72,14,73,251,73,231,74,211,75,191,76,170,77,149,78,126,79,104,80,80,81,56,82,31,83,5,84,234,84,207,85,178,86,148,87,116,88,84,89,50,90,15,91,235,91,197,92,157,93,117,94,74,95,30,96,240,96,192,97,143,98,91,99,38,100,239,100,181,101,122,102,60,103,253,103,187,104,119,105,48,106,232,106,156,107,79,108,255,108,172,109,87,110,255,110,165,111,71,112,231,112,133,113,31,114,183,114,75,115,221,115,108,116,248,116,129,117,6,118,137,118,8,119,133,119,254,119,116,120,230,120,86,121,194,121,42,122,144,122,242,122,80,123,171,123,3,124,87,124,167,124,244,124,62,125,132,125,198,125,5,126,64,126,120,126,172,126,220,126,9,127,49,127,87,127,120,127,150,127,176,127,199,127,217,127,232,127,243,127,251,127,255,127,255,127,229,127,153,127,25,127,103,126,129,125],"i8",ALLOC_NONE,Runtime.GLOBAL_BASE+20480),allocate([106,124,33,123,167,121,252,119,34,118,24,116,223,113,122,111,231,108,41,106,65,103,47,100,245,96,149,93,15,90,101,86,153,82,171,78,158,74,116,70,45,66,204,61,82,57,193,52,27,48,98,43,151,38,189,33,213,28,226,23,230,18,226,13,216,8,203,3,61,10,64,10,73,10,88,10,108,10,135,10,167,10,205,10,249,10,43,11,99,11,160,11,227,11,44,12,122,12,207,12,40,13,136,13,237,13,87,14,199,14,60,15,183,15,55,16,189,16,71,17,215,17,108,18,6,19,165,19,73,20,242,20,159,21,82,22,9,23,196,23,133,24,73,25,18,26,224,26,177,27,135,28,97,29,62,30,32,31,5,32,238,32,219,33,203,34,191,35,182,36,176,37,174,38,174,39,177,40,184,41,193,42,204,43,218,44,235,45,254,46,19,48,42,49,67,50,94,51,123,52,154,53,186,54,219,55,254,56,34,58,71,59,109,60,148,61,188,62,228,63,13,65,54,66,96,67,138,68,180,69,221,70,7,72,48,73,89,74,130,75,169,76,208,77,246,78,27,80,63,81,98,82,132,83,164,84,194,85,223,86,250,87,19,89,43,90,64,91,83,92,99,93,113,94,125,95,134,96,140,97,143,98,144,99,141,100,135,101,126,102,114,103,98,104,79,105,56,106,30,107,255,107,221,108,183,109,140,110,94,111,43,112,244,112,185,113,121,114,53,115,236,115,158,116,76,117,245,117,153,118,55,119,209,119,102,120,246,120,129,121,6,122,134,122,1,123,118,123,230,123,81,124,182,124,21,125,111,125,195,125,17,126,90,126,157,126,219,126,18,127,68,127,112,127,150,127,183,127,209,127,230,127,244,127,253,127,255,127,255,127,244,127,208,127,149,127,66,127,215,126,85,126,188,125,12,125,69,124,104,123,117,122,108,121,78,120,28,119,213,117,122,116,13,115,140,113,250,111,87,110,162,108,222,106,11,105,40,103,57,101,60,99,51,97,30,95,255,92,215,90,165,88,108,86,44,84,229,81,154,79,74,77,247,74,161,72,74,70,243,67,156,65,71,63,244,60,164,58,88,56,18,54,209,51,152,49,103,47,62,45,31,43,11,41,2,39,5,37,21,35,51,33,95,31,155,29,231,27,67,26,177,24,49,23,195,21,105,20,34,19,239,17,209,16,201,15,214,14,249,13,50,13,130,12,232,11,102,11,252,10,169,10,109,10,73,10,61,10,61,10,63,10,67,10,74,10,84,10,96,10,111,10,129,10,150,10,174,10,200,10,229,10,5,11,39,11,77,11,117,11,159,11,205,11,253,11,48,12,101,12,157,12,216,12,22,13,86,13,153,13,222,13,38,14,113,14,190,14,13,15,96,15,181,15,12,16,102,16,194,16,33,17,130,17,230,17,76,18,180,18,31,19,140,19,252,19,110,20,226,20,88,21,209,21,76,22,201,22,72,23,202,23,77,24,211,24,91,25,229,25,113,26,254,26,142,27,32,28,180,28,74,29,225,29,123,30,22,31,179,31,82,32,242,32,149,33,57,34,222,34,133,35,46,36,216,36,132,37,50,38,224,38,145,39,66,40,245,40,169,41,95,42,22,43,206,43,135,44,66,45,253,45,186,46,120,47,54,48,246,48,183,49,120,50,59,51,254,51,194,52,135,53,77,54,19,55,218,55,161,56,106,57,50,58,252,58,197,59,144,60,90,61,37,62,240,62,188,63,136,64,84,65,32,66,236,66,185,67,133,68,82,69,30,70,235,70,183,71,132,72,80,73,28,74,231,74,179,75,126,76,73,77,19,78,221,78,166,79,111,80,56,81,0,82,199,82,142,83,84,84,25,85,221,85,161,86,100,87,38,88,231,88,167,89,103,90,37,91,226,91,158,92,89,93,19,94,204,94,131,95,57,96,238,96,162,97,84,98,5,99,181,99,99,100,15,101,186,101,100,102,12,103,178,103,87,104,250,104,155,105,59,106,217,106,117,107,16,108,168,108,63,109,211,109,102,110,247,110,134,111,19,112,158,112,39,113,174,113,50,114,181,114,53,115,179,115,47,116,169,116,33,117,150,117,9,118,122,118,232,118,84,119,190,119,37,120,138,120,236,120,76,121,170,121,5,122,94,122,180,122,7,123,88,123,167,123,242,123,60,124,130,124,198,124,8,125,71,125,131,125,188,125,243,125,39,126,89,126,136,126,180,126,221,126,4,127,40,127,73,127,103,127,131,127,156,127,178,127,197,127,214,127,228,127,239,127,247,127,253,127,255,127,255,127,97,125,160,117,15,105,48,88,181,67,116,44,98,19,68,101,99,111,100,101,114,0,101,110,99,111,100,101,114,0],"i8",ALLOC_NONE,Runtime.GLOBAL_BASE+30720);var tempDoublePtr=Runtime.alignMemory(allocate(12,"i8",ALLOC_STATIC),8);function copyTempFloat(e){HEAP8[tempDoublePtr]=HEAP8[e],HEAP8[tempDoublePtr+1]=HEAP8[e+1],HEAP8[tempDoublePtr+2]=HEAP8[e+2],HEAP8[tempDoublePtr+3]=HEAP8[e+3]}function copyTempDouble(e){HEAP8[tempDoublePtr]=HEAP8[e],HEAP8[tempDoublePtr+1]=HEAP8[e+1],HEAP8[tempDoublePtr+2]=HEAP8[e+2],HEAP8[tempDoublePtr+3]=HEAP8[e+3],HEAP8[tempDoublePtr+4]=HEAP8[e+4],HEAP8[tempDoublePtr+5]=HEAP8[e+5],HEAP8[tempDoublePtr+6]=HEAP8[e+6],HEAP8[tempDoublePtr+7]=HEAP8[e+7]}function _sbrk(e){var r=_sbrk;r.called||(DYNAMICTOP=alignMemoryPage(DYNAMICTOP),r.called=!0,assert(Runtime.dynamicAlloc),r.alloc=Runtime.dynamicAlloc,Runtime.dynamicAlloc=function(){abort("cannot dynamically allocate, sbrk now has control")});var n=DYNAMICTOP;if(0!=e&&!r.alloc(e))return-1>>>0;return n}function ___setErrNo(e){return Module.___errno_location&&(HEAP32[Module.___errno_location()>>2]=e),e}assert(tempDoublePtr%8==0);var ERRNO_CODES={EPERM:1,ENOENT:2,ESRCH:3,EINTR:4,EIO:5,ENXIO:6,E2BIG:7,ENOEXEC:8,EBADF:9,ECHILD:10,EAGAIN:11,EWOULDBLOCK:11,ENOMEM:12,EACCES:13,EFAULT:14,ENOTBLK:15,EBUSY:16,EEXIST:17,EXDEV:18,ENODEV:19,ENOTDIR:20,EISDIR:21,EINVAL:22,ENFILE:23,EMFILE:24,ENOTTY:25,ETXTBSY:26,EFBIG:27,ENOSPC:28,ESPIPE:29,EROFS:30,EMLINK:31,EPIPE:32,EDOM:33,ERANGE:34,ENOMSG:42,EIDRM:43,ECHRNG:44,EL2NSYNC:45,EL3HLT:46,EL3RST:47,ELNRNG:48,EUNATCH:49,ENOCSI:50,EL2HLT:51,EDEADLK:35,ENOLCK:37,EBADE:52,EBADR:53,EXFULL:54,ENOANO:55,EBADRQC:56,EBADSLT:57,EDEADLOCK:35,EBFONT:59,ENOSTR:60,ENODATA:61,ETIME:62,ENOSR:63,ENONET:64,ENOPKG:65,EREMOTE:66,ENOLINK:67,EADV:68,ESRMNT:69,ECOMM:70,EPROTO:71,EMULTIHOP:72,EDOTDOT:73,EBADMSG:74,ENOTUNIQ:76,EBADFD:77,EREMCHG:78,ELIBACC:79,ELIBBAD:80,ELIBSCN:81,ELIBMAX:82,ELIBEXEC:83,ENOSYS:38,ENOTEMPTY:39,ENAMETOOLONG:36,ELOOP:40,EOPNOTSUPP:95,EPFNOSUPPORT:96,ECONNRESET:104,ENOBUFS:105,EAFNOSUPPORT:97,EPROTOTYPE:91,ENOTSOCK:88,ENOPROTOOPT:92,ESHUTDOWN:108,ECONNREFUSED:111,EADDRINUSE:98,ECONNABORTED:103,ENETUNREACH:101,ENETDOWN:100,ETIMEDOUT:110,EHOSTDOWN:112,EHOSTUNREACH:113,EINPROGRESS:115,EALREADY:114,EDESTADDRREQ:89,EMSGSIZE:90,EPROTONOSUPPORT:93,ESOCKTNOSUPPORT:94,EADDRNOTAVAIL:99,ENETRESET:102,EISCONN:106,ENOTCONN:107,ETOOMANYREFS:109,EUSERS:87,EDQUOT:122,ESTALE:116,ENOTSUP:95,ENOMEDIUM:123,EILSEQ:84,EOVERFLOW:75,ECANCELED:125,ENOTRECOVERABLE:131,EOWNERDEAD:130,ESTRPIPE:86};function _sysconf(e){switch(e){case 30:return PAGE_SIZE;case 85:return totalMemory/PAGE_SIZE;case 132:case 133:case 12:case 137:case 138:case 15:case 235:case 16:case 17:case 18:case 19:case 20:case 149:case 13:case 10:case 236:case 153:case 9:case 21:case 22:case 159:case 154:case 14:case 77:case 78:case 139:case 80:case 81:case 82:case 68:case 67:case 164:case 11:case 29:case 47:case 48:case 95:case 52:case 51:case 46:return 200809;case 79:return 0;case 27:case 246:case 127:case 128:case 23:case 24:case 160:case 161:case 181:case 182:case 242:case 183:case 184:case 243:case 244:case 245:case 165:case 178:case 179:case 49:case 50:case 168:case 169:case 175:case 170:case 171:case 172:case 97:case 76:case 32:case 173:case 35:return-1;case 176:case 177:case 7:case 155:case 8:case 157:case 125:case 126:case 92:case 93:case 129:case 130:case 131:case 94:case 91:return 1;case 74:case 60:case 69:case 70:case 4:return 1024;case 31:case 42:case 72:return 32;case 87:case 26:case 33:return 2147483647;case 34:case 1:return 47839;case 38:case 36:return 99;case 43:case 37:return 2048;case 0:return 2097152;case 3:return 65536;case 28:return 32768;case 44:return 32767;case 75:return 16384;case 39:return 1e3;case 89:return 700;case 71:return 256;case 40:return 255;case 2:return 100;case 180:return 64;case 25:return 20;case 5:return 16;case 6:return 6;case 73:return 4;case 84:return"object"==typeof navigator&&navigator.hardwareConcurrency||1}return ___setErrNo(ERRNO_CODES.EINVAL),-1}function _emscripten_memcpy_big(e,r,n){return HEAPU8.set(HEAPU8.subarray(r,r+n),e),e}function _abort(){Module.abort()}Module._memcpy=_memcpy,Module._memmove=_memmove,Module._memset=_memset;var ERRNO_MESSAGES={0:"Success",1:"Not super-user",2:"No such file or directory",3:"No such process",4:"Interrupted system call",5:"I/O error",6:"No such device or address",7:"Arg list too long",8:"Exec format error",9:"Bad file number",10:"No children",11:"No more processes",12:"Not enough core",13:"Permission denied",14:"Bad address",15:"Block device required",16:"Mount device busy",17:"File exists",18:"Cross-device link",19:"No such device",20:"Not a directory",21:"Is a directory",22:"Invalid argument",23:"Too many open files in system",24:"Too many open files",25:"Not a typewriter",26:"Text file busy",27:"File too large",28:"No space left on device",29:"Illegal seek",30:"Read only file system",31:"Too many links",32:"Broken pipe",33:"Math arg out of domain of func",34:"Math result not representable",35:"File locking deadlock error",36:"File or path name too long",37:"No record locks available",38:"Function not implemented",39:"Directory not empty",40:"Too many symbolic links",42:"No message of desired type",43:"Identifier removed",44:"Channel number out of range",45:"Level 2 not synchronized",46:"Level 3 halted",47:"Level 3 reset",48:"Link number out of range",49:"Protocol driver not attached",50:"No CSI structure available",51:"Level 2 halted",52:"Invalid exchange",53:"Invalid request descriptor",54:"Exchange full",55:"No anode",56:"Invalid request code",57:"Invalid slot",59:"Bad font file fmt",60:"Device not a stream",61:"No data (for no delay io)",62:"Timer expired",63:"Out of streams resources",64:"Machine is not on the network",65:"Package not installed",66:"The object is remote",67:"The link has been severed",68:"Advertise error",69:"Srmount error",70:"Communication error on send",71:"Protocol error",72:"Multihop attempted",73:"Cross mount point (not really error)",74:"Trying to read unreadable message",75:"Value too large for defined data type",76:"Given log. name not unique",77:"f.d. invalid for this operation",78:"Remote address changed",79:"Can   access a needed shared lib",80:"Accessing a corrupted shared lib",81:".lib section in a.out corrupted",82:"Attempting to link in too many libs",83:"Attempting to exec a shared library",84:"Illegal byte sequence",86:"Streams pipe error",87:"Too many users",88:"Socket operation on non-socket",89:"Destination address required",90:"Message too long",91:"Protocol wrong type for socket",92:"Protocol not available",93:"Unknown protocol",94:"Socket type not supported",95:"Not supported",96:"Protocol family not supported",97:"Address family not supported by protocol family",98:"Address already in use",99:"Address not available",100:"Network interface is not configured",101:"Network is unreachable",102:"Connection reset by network",103:"Connection aborted",104:"Connection reset by peer",105:"No buffer space available",106:"Socket is already connected",107:"Socket is not connected",108:"Can't send after socket shutdown",109:"Too many references",110:"Connection timed out",111:"Connection refused",112:"Host is down",113:"Host is unreachable",114:"Socket already connected",115:"Connection already in progress",116:"Stale file handle",122:"Quota exceeded",123:"No medium (in tape drive)",125:"Operation canceled",130:"Previous owner died",131:"State not recoverable"},TTY={ttys:[],init:function(){},shutdown:function(){},register:function(e,r){TTY.ttys[e]={input:[],output:[],ops:r},FS.registerDevice(e,TTY.stream_ops)},stream_ops:{open:function(e){var r=TTY.ttys[e.node.rdev];if(!r)throw new FS.ErrnoError(ERRNO_CODES.ENODEV);e.tty=r,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,r,n,t,i){if(!e.tty||!e.tty.ops.get_char)throw new FS.ErrnoError(ERRNO_CODES.ENXIO);for(var o=0,a=0;a<t;a++){var s;try{s=e.tty.ops.get_char(e.tty)}catch(e){throw new FS.ErrnoError(ERRNO_CODES.EIO)}if(void 0===s&&0===o)throw new FS.ErrnoError(ERRNO_CODES.EAGAIN);if(null==s)break;o++,r[n+a]=s}return o&&(e.node.timestamp=Date.now()),o},write:function(e,r,n,t,i){if(!e.tty||!e.tty.ops.put_char)throw new FS.ErrnoError(ERRNO_CODES.ENXIO);for(var o=0;o<t;o++)try{e.tty.ops.put_char(e.tty,r[n+o])}catch(e){throw new FS.ErrnoError(ERRNO_CODES.EIO)}return t&&(e.node.timestamp=Date.now()),o}},default_tty_ops:{get_char:function(e){if(!e.input.length){var r=null;if("undefined"!=typeof window&&"function"==typeof window.prompt?null!==(r=window.prompt("Input: "))&&(r+="\n"):"function"==typeof readline&&null!==(r=readline())&&(r+="\n"),!r)return null;e.input=intArrayFromString(r,!0)}return e.input.shift()},put_char:function(e,r){null===r||10===r?(Module.print(UTF8ArrayToString(e.output,0)),e.output=[]):0!=r&&e.output.push(r)},flush:function(e){e.output&&0<e.output.length&&(Module.print(UTF8ArrayToString(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,r){null===r||10===r?(Module.printErr(UTF8ArrayToString(e.output,0)),e.output=[]):0!=r&&e.output.push(r)},flush:function(e){e.output&&0<e.output.length&&(Module.printErr(UTF8ArrayToString(e.output,0)),e.output=[])}}},MEMFS={ops_table:null,mount:function(e){return MEMFS.createNode(null,"/",16895,0)},createNode:function(e,r,n,t){if(FS.isBlkdev(n)||FS.isFIFO(n))throw new FS.ErrnoError(ERRNO_CODES.EPERM);MEMFS.ops_table||(MEMFS.ops_table={dir:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,lookup:MEMFS.node_ops.lookup,mknod:MEMFS.node_ops.mknod,rename:MEMFS.node_ops.rename,unlink:MEMFS.node_ops.unlink,rmdir:MEMFS.node_ops.rmdir,readdir:MEMFS.node_ops.readdir,symlink:MEMFS.node_ops.symlink},stream:{llseek:MEMFS.stream_ops.llseek}},file:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:{llseek:MEMFS.stream_ops.llseek,read:MEMFS.stream_ops.read,write:MEMFS.stream_ops.write,allocate:MEMFS.stream_ops.allocate,mmap:MEMFS.stream_ops.mmap,msync:MEMFS.stream_ops.msync}},link:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,readlink:MEMFS.node_ops.readlink},stream:{}},chrdev:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:FS.chrdev_stream_ops}});var i=FS.createNode(e,r,n,t);return FS.isDir(i.mode)?(i.node_ops=MEMFS.ops_table.dir.node,i.stream_ops=MEMFS.ops_table.dir.stream,i.contents={}):FS.isFile(i.mode)?(i.node_ops=MEMFS.ops_table.file.node,i.stream_ops=MEMFS.ops_table.file.stream,i.usedBytes=0,i.contents=null):FS.isLink(i.mode)?(i.node_ops=MEMFS.ops_table.link.node,i.stream_ops=MEMFS.ops_table.link.stream):FS.isChrdev(i.mode)&&(i.node_ops=MEMFS.ops_table.chrdev.node,i.stream_ops=MEMFS.ops_table.chrdev.stream),i.timestamp=Date.now(),e&&(e.contents[r]=i),i},getFileDataAsRegularArray:function(e){if(e.contents&&e.contents.subarray){for(var r=[],n=0;n<e.usedBytes;++n)r.push(e.contents[n]);return r}return e.contents},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array},expandFileStorage:function(e,r){if(e.contents&&e.contents.subarray&&r>e.contents.length&&(e.contents=MEMFS.getFileDataAsRegularArray(e),e.usedBytes=e.contents.length),!e.contents||e.contents.subarray){var n=e.contents?e.contents.buffer.byteLength:0;if(r<=n)return;r=Math.max(r,n*(n<1048576?2:1.125)|0),0!=n&&(r=Math.max(r,256));var t=e.contents;return e.contents=new Uint8Array(r),void(0<e.usedBytes&&e.contents.set(t.subarray(0,e.usedBytes),0))}for(!e.contents&&0<r&&(e.contents=[]);e.contents.length<r;)e.contents.push(0)},resizeFileStorage:function(e,r){if(e.usedBytes!=r){if(0==r)return e.contents=null,void(e.usedBytes=0);if(!e.contents||e.contents.subarray){var n=e.contents;return e.contents=new Uint8Array(new ArrayBuffer(r)),n&&e.contents.set(n.subarray(0,Math.min(r,e.usedBytes))),void(e.usedBytes=r)}if(e.contents||(e.contents=[]),e.contents.length>r)e.contents.length=r;else for(;e.contents.length<r;)e.contents.push(0);e.usedBytes=r}},node_ops:{getattr:function(e){var r={};return r.dev=FS.isChrdev(e.mode)?e.id:1,r.ino=e.id,r.mode=e.mode,r.nlink=1,r.uid=0,r.gid=0,r.rdev=e.rdev,FS.isDir(e.mode)?r.size=4096:FS.isFile(e.mode)?r.size=e.usedBytes:FS.isLink(e.mode)?r.size=e.link.length:r.size=0,r.atime=new Date(e.timestamp),r.mtime=new Date(e.timestamp),r.ctime=new Date(e.timestamp),r.blksize=4096,r.blocks=Math.ceil(r.size/r.blksize),r},setattr:function(e,r){void 0!==r.mode&&(e.mode=r.mode),void 0!==r.timestamp&&(e.timestamp=r.timestamp),void 0!==r.size&&MEMFS.resizeFileStorage(e,r.size)},lookup:function(e,r){throw FS.genericErrors[ERRNO_CODES.ENOENT]},mknod:function(e,r,n,t){return MEMFS.createNode(e,r,n,t)},rename:function(e,r,n){if(FS.isDir(e.mode)){var t;try{t=FS.lookupNode(r,n)}catch(e){}if(t)for(var i in t.contents)throw new FS.ErrnoError(ERRNO_CODES.ENOTEMPTY)}delete e.parent.contents[e.name],e.name=n,(r.contents[n]=e).parent=r},unlink:function(e,r){delete e.contents[r]},rmdir:function(e,r){var n=FS.lookupNode(e,r);for(var t in n.contents)throw new FS.ErrnoError(ERRNO_CODES.ENOTEMPTY);delete e.contents[r]},readdir:function(e){var r=[".",".."];for(var n in e.contents)e.contents.hasOwnProperty(n)&&r.push(n);return r},symlink:function(e,r,n){var t=MEMFS.createNode(e,r,41471,0);return t.link=n,t},readlink:function(e){if(!FS.isLink(e.mode))throw new FS.ErrnoError(ERRNO_CODES.EINVAL);return e.link}},stream_ops:{read:function(e,r,n,t,i){var o=e.node.contents;if(i>=e.node.usedBytes)return 0;var a=Math.min(e.node.usedBytes-i,t);if(assert(0<=a),8<a&&o.subarray)r.set(o.subarray(i,i+a),n);else for(var s=0;s<a;s++)r[n+s]=o[i+s];return a},write:function(e,r,n,t,i,o){if(!t)return 0;var a=e.node;if(a.timestamp=Date.now(),r.subarray&&(!a.contents||a.contents.subarray)){if(o)return a.contents=r.subarray(n,n+t),a.usedBytes=t;if(0===a.usedBytes&&0===i)return a.contents=new Uint8Array(r.subarray(n,n+t)),a.usedBytes=t;if(i+t<=a.usedBytes)return a.contents.set(r.subarray(n,n+t),i),t}if(MEMFS.expandFileStorage(a,i+t),a.contents.subarray&&r.subarray)a.contents.set(r.subarray(n,n+t),i);else for(var s=0;s<t;s++)a.contents[i+s]=r[n+s];return a.usedBytes=Math.max(a.usedBytes,i+t),t},llseek:function(e,r,n){var t=r;if(1===n?t+=e.position:2===n&&FS.isFile(e.node.mode)&&(t+=e.node.usedBytes),t<0)throw new FS.ErrnoError(ERRNO_CODES.EINVAL);return t},allocate:function(e,r,n){MEMFS.expandFileStorage(e.node,r+n),e.node.usedBytes=Math.max(e.node.usedBytes,r+n)},mmap:function(e,r,n,t,i,o,a){if(!FS.isFile(e.node.mode))throw new FS.ErrnoError(ERRNO_CODES.ENODEV);var s,l,f=e.node.contents;if(2&a||f.buffer!==r&&f.buffer!==r.buffer){if((0<i||i+t<e.node.usedBytes)&&(f=f.subarray?f.subarray(i,i+t):Array.prototype.slice.call(f,i,i+t)),l=!0,!(s=_malloc(t)))throw new FS.ErrnoError(ERRNO_CODES.ENOMEM);r.set(f,s)}else l=!1,s=f.byteOffset;return{ptr:s,allocated:l}},msync:function(e,r,n,t,i){if(!FS.isFile(e.node.mode))throw new FS.ErrnoError(ERRNO_CODES.ENODEV);if(2&i)return 0;MEMFS.stream_ops.write(e,r,0,t,n,!1);return 0}}},IDBFS={dbs:{},indexedDB:function(){if("undefined"!=typeof indexedDB)return indexedDB;var e=null;return"object"==typeof window&&(e=window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB),assert(e,"IDBFS used, but indexedDB not supported"),e},DB_VERSION:21,DB_STORE_NAME:"FILE_DATA",mount:function(e){return MEMFS.mount.apply(null,arguments)},syncfs:function(r,o,a){IDBFS.getLocalSet(r,function(e,i){if(e)return a(e);IDBFS.getRemoteSet(r,function(e,r){if(e)return a(e);var n=o?r:i,t=o?i:r;IDBFS.reconcile(n,t,a)})})},getDB:function(e,r){var n,t=IDBFS.dbs[e];if(t)return r(null,t);try{n=IDBFS.indexedDB().open(e,IDBFS.DB_VERSION)}catch(e){return r(e)}n.onupgradeneeded=function(e){var r,n=e.target.result,t=e.target.transaction;(r=n.objectStoreNames.contains(IDBFS.DB_STORE_NAME)?t.objectStore(IDBFS.DB_STORE_NAME):n.createObjectStore(IDBFS.DB_STORE_NAME)).indexNames.contains("timestamp")||r.createIndex("timestamp","timestamp",{unique:!1})},n.onsuccess=function(){t=n.result,IDBFS.dbs[e]=t,r(null,t)},n.onerror=function(e){r(this.error),e.preventDefault()}},getLocalSet:function(e,r){var n={};function t(e){return"."!==e&&".."!==e}function i(r){return function(e){return PATH.join2(r,e)}}for(var o=FS.readdir(e.mountpoint).filter(t).map(i(e.mountpoint));o.length;){var a,s=o.pop();try{a=FS.stat(s)}catch(e){return r(e)}FS.isDir(a.mode)&&o.push.apply(o,FS.readdir(s).filter(t).map(i(s))),n[s]={timestamp:a.mtime}}return r(null,{type:"local",entries:n})},getRemoteSet:function(e,t){var i={};IDBFS.getDB(e.mountpoint,function(e,n){if(e)return t(e);var r=n.transaction([IDBFS.DB_STORE_NAME],"readonly");r.onerror=function(e){t(this.error),e.preventDefault()},r.objectStore(IDBFS.DB_STORE_NAME).index("timestamp").openKeyCursor().onsuccess=function(e){var r=e.target.result;if(!r)return t(null,{type:"remote",db:n,entries:i});i[r.primaryKey]={timestamp:r.key},r.continue()}})},loadLocalEntry:function(e,r){var n,t;try{t=FS.lookupPath(e).node,n=FS.stat(e)}catch(e){return r(e)}return FS.isDir(n.mode)?r(null,{timestamp:n.mtime,mode:n.mode}):FS.isFile(n.mode)?(t.contents=MEMFS.getFileDataAsTypedArray(t),r(null,{timestamp:n.mtime,mode:n.mode,contents:t.contents})):r(new Error("node type not supported"))},storeLocalEntry:function(e,r,n){try{if(FS.isDir(r.mode))FS.mkdir(e,r.mode);else{if(!FS.isFile(r.mode))return n(new Error("node type not supported"));FS.writeFile(e,r.contents,{encoding:"binary",canOwn:!0})}FS.chmod(e,r.mode),FS.utime(e,r.timestamp,r.timestamp)}catch(e){return n(e)}n(null)},removeLocalEntry:function(e,r){try{FS.lookupPath(e);var n=FS.stat(e);FS.isDir(n.mode)?FS.rmdir(e):FS.isFile(n.mode)&&FS.unlink(e)}catch(e){return r(e)}r(null)},loadRemoteEntry:function(e,r,n){var t=e.get(r);t.onsuccess=function(e){n(null,e.target.result)},t.onerror=function(e){n(this.error),e.preventDefault()}},storeRemoteEntry:function(e,r,n,t){var i=e.put(n,r);i.onsuccess=function(){t(null)},i.onerror=function(e){t(this.error),e.preventDefault()}},removeRemoteEntry:function(e,r,n){var t=e.delete(r);t.onsuccess=function(){n(null)},t.onerror=function(e){n(this.error),e.preventDefault()}},reconcile:function(t,i,r){var o=0,a=[];Object.keys(t.entries).forEach(function(e){var r=t.entries[e],n=i.entries[e];(!n||r.timestamp>n.timestamp)&&(a.push(e),o++)});var n=[];if(Object.keys(i.entries).forEach(function(e){i.entries[e];t.entries[e]||(n.push(e),o++)}),!o)return r(null);var s=0,e=("remote"===t.type?t.db:i.db).transaction([IDBFS.DB_STORE_NAME],"readwrite"),l=e.objectStore(IDBFS.DB_STORE_NAME);function f(e){return e?f.errored?void 0:(f.errored=!0,r(e)):++s>=o?r(null):void 0}e.onerror=function(e){f(this.error),e.preventDefault()},a.sort().forEach(function(n){"local"===i.type?IDBFS.loadRemoteEntry(l,n,function(e,r){if(e)return f(e);IDBFS.storeLocalEntry(n,r,f)}):IDBFS.loadLocalEntry(n,function(e,r){if(e)return f(e);IDBFS.storeRemoteEntry(l,n,r,f)})}),n.sort().reverse().forEach(function(e){"local"===i.type?IDBFS.removeLocalEntry(e,f):IDBFS.removeRemoteEntry(l,e,f)})}},WORKERFS={DIR_MODE:16895,FILE_MODE:33279,reader:null,mount:function(e){assert(ENVIRONMENT_IS_WORKER),WORKERFS.reader||(WORKERFS.reader=new FileReaderSync);var o=WORKERFS.createNode(null,"/",WORKERFS.DIR_MODE,0),a={};function t(e){for(var r=e.split("/"),n=o,t=0;t<r.length-1;t++){var i=r.slice(0,t+1).join("/");a[i]||(a[i]=WORKERFS.createNode(n,i,WORKERFS.DIR_MODE,0)),n=a[i]}return n}function i(e){var r=e.split("/");return r[r.length-1]}return Array.prototype.forEach.call(e.opts.files||[],function(e){WORKERFS.createNode(t(e.name),i(e.name),WORKERFS.FILE_MODE,0,e,e.lastModifiedDate)}),(e.opts.blobs||[]).forEach(function(e){WORKERFS.createNode(t(e.name),i(e.name),WORKERFS.FILE_MODE,0,e.data)}),(e.opts.packages||[]).forEach(function(n){n.metadata.files.forEach(function(e){var r=e.filename.substr(1);WORKERFS.createNode(t(r),i(r),WORKERFS.FILE_MODE,0,n.blob.slice(e.start,e.end))})}),o},createNode:function(e,r,n,t,i,o){var a=FS.createNode(e,r,n);return a.mode=n,a.node_ops=WORKERFS.node_ops,a.stream_ops=WORKERFS.stream_ops,a.timestamp=(o||new Date).getTime(),assert(WORKERFS.FILE_MODE!==WORKERFS.DIR_MODE),n===WORKERFS.FILE_MODE?(a.size=i.size,a.contents=i):(a.size=4096,a.contents={}),e&&(e.contents[r]=a),a},node_ops:{getattr:function(e){return{dev:1,ino:void 0,mode:e.mode,nlink:1,uid:0,gid:0,rdev:void 0,size:e.size,atime:new Date(e.timestamp),mtime:new Date(e.timestamp),ctime:new Date(e.timestamp),blksize:4096,blocks:Math.ceil(e.size/4096)}},setattr:function(e,r){void 0!==r.mode&&(e.mode=r.mode),void 0!==r.timestamp&&(e.timestamp=r.timestamp)},lookup:function(e,r){throw new FS.ErrnoError(ERRNO_CODES.ENOENT)},mknod:function(e,r,n,t){throw new FS.ErrnoError(ERRNO_CODES.EPERM)},rename:function(e,r,n){throw new FS.ErrnoError(ERRNO_CODES.EPERM)},unlink:function(e,r){throw new FS.ErrnoError(ERRNO_CODES.EPERM)},rmdir:function(e,r){throw new FS.ErrnoError(ERRNO_CODES.EPERM)},readdir:function(e){throw new FS.ErrnoError(ERRNO_CODES.EPERM)},symlink:function(e,r,n){throw new FS.ErrnoError(ERRNO_CODES.EPERM)},readlink:function(e){throw new FS.ErrnoError(ERRNO_CODES.EPERM)}},stream_ops:{read:function(e,r,n,t,i){if(i>=e.node.size)return 0;var o=e.node.contents.slice(i,i+t),a=WORKERFS.reader.readAsArrayBuffer(o);return r.set(new Uint8Array(a),n),o.size},write:function(e,r,n,t,i){throw new FS.ErrnoError(ERRNO_CODES.EIO)},llseek:function(e,r,n){var t=r;if(1===n?t+=e.position:2===n&&FS.isFile(e.node.mode)&&(t+=e.node.size),t<0)throw new FS.ErrnoError(ERRNO_CODES.EINVAL);return t}}},_stdin=allocate(1,"i32*",ALLOC_STATIC),_stdout=allocate(1,"i32*",ALLOC_STATIC),_stderr=allocate(1,"i32*",ALLOC_STATIC),FS={root:null,mounts:[],devices:[null],streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,trackingDelegate:{},tracking:{openFlags:{READ:1,WRITE:2}},ErrnoError:null,genericErrors:{},filesystems:null,handleFSError:function(e){if(!(e instanceof FS.ErrnoError))throw e+" : "+stackTrace();return ___setErrNo(e.errno)},lookupPath:function(e,r){if(r=r||{},!(e=PATH.resolve(FS.cwd(),e)))return{path:"",node:null};var n={follow_mount:!0,recurse_count:0};for(var t in n)void 0===r[t]&&(r[t]=n[t]);if(8<r.recurse_count)throw new FS.ErrnoError(ERRNO_CODES.ELOOP);for(var i=PATH.normalizeArray(e.split("/").filter(function(e){return!!e}),!1),o=FS.root,a="/",s=0;s<i.length;s++){var l=s===i.length-1;if(l&&r.parent)break;if(o=FS.lookupNode(o,i[s]),a=PATH.join2(a,i[s]),FS.isMountpoint(o)&&(!l||l&&r.follow_mount)&&(o=o.mounted.root),!l||r.follow)for(var f=0;FS.isLink(o.mode);){var u=FS.readlink(a);if(a=PATH.resolve(PATH.dirname(a),u),o=FS.lookupPath(a,{recurse_count:r.recurse_count}).node,40<f++)throw new FS.ErrnoError(ERRNO_CODES.ELOOP)}}return{path:a,node:o}},getPath:function(e){for(var r;;){if(FS.isRoot(e)){var n=e.mount.mountpoint;return r?"/"!==n[n.length-1]?n+"/"+r:n+r:n}r=r?e.name+"/"+r:e.name,e=e.parent}},hashName:function(e,r){for(var n=0,t=0;t<r.length;t++)n=(n<<5)-n+r.charCodeAt(t)|0;return(e+n>>>0)%FS.nameTable.length},hashAddNode:function(e){var r=FS.hashName(e.parent.id,e.name);e.name_next=FS.nameTable[r],FS.nameTable[r]=e},hashRemoveNode:function(e){var r=FS.hashName(e.parent.id,e.name);if(FS.nameTable[r]===e)FS.nameTable[r]=e.name_next;else for(var n=FS.nameTable[r];n;){if(n.name_next===e){n.name_next=e.name_next;break}n=n.name_next}},lookupNode:function(e,r){var n=FS.mayLookup(e);if(n)throw new FS.ErrnoError(n,e);for(var t=FS.hashName(e.id,r),i=FS.nameTable[t];i;i=i.name_next){var o=i.name;if(i.parent.id===e.id&&o===r)return i}return FS.lookup(e,r)},createNode:function(e,r,n,t){if(!FS.FSNode){FS.FSNode=function(e,r,n,t){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=FS.nextInode++,this.name=r,this.mode=n,this.node_ops={},this.stream_ops={},this.rdev=t},FS.FSNode.prototype={};Object.defineProperties(FS.FSNode.prototype,{read:{get:function(){return 365==(365&this.mode)},set:function(e){e?this.mode|=365:this.mode&=-366}},write:{get:function(){return 146==(146&this.mode)},set:function(e){e?this.mode|=146:this.mode&=-147}},isFolder:{get:function(){return FS.isDir(this.mode)}},isDevice:{get:function(){return FS.isChrdev(this.mode)}}})}var i=new FS.FSNode(e,r,n,t);return FS.hashAddNode(i),i},destroyNode:function(e){FS.hashRemoveNode(e)},isRoot:function(e){return e===e.parent},isMountpoint:function(e){return!!e.mounted},isFile:function(e){return 32768==(61440&e)},isDir:function(e){return 16384==(61440&e)},isLink:function(e){return 40960==(61440&e)},isChrdev:function(e){return 8192==(61440&e)},isBlkdev:function(e){return 24576==(61440&e)},isFIFO:function(e){return 4096==(61440&e)},isSocket:function(e){return 49152==(49152&e)},flagModes:{r:0,rs:1052672,"r+":2,w:577,wx:705,xw:705,"w+":578,"wx+":706,"xw+":706,a:1089,ax:1217,xa:1217,"a+":1090,"ax+":1218,"xa+":1218},modeStringToFlags:function(e){var r=FS.flagModes[e];if(void 0===r)throw new Error("Unknown file open mode: "+e);return r},flagsToPermissionString:function(e){var r=["r","w","rw"][3&e];return 512&e&&(r+="w"),r},nodePermissions:function(e,r){return FS.ignorePermissions?0:(-1===r.indexOf("r")||292&e.mode)&&(-1===r.indexOf("w")||146&e.mode)&&(-1===r.indexOf("x")||73&e.mode)?0:ERRNO_CODES.EACCES},mayLookup:function(e){var r=FS.nodePermissions(e,"x");return r||(e.node_ops.lookup?0:ERRNO_CODES.EACCES)},mayCreate:function(e,r){try{FS.lookupNode(e,r);return ERRNO_CODES.EEXIST}catch(e){}return FS.nodePermissions(e,"wx")},mayDelete:function(e,r,n){var t;try{t=FS.lookupNode(e,r)}catch(e){return e.errno}var i=FS.nodePermissions(e,"wx");if(i)return i;if(n){if(!FS.isDir(t.mode))return ERRNO_CODES.ENOTDIR;if(FS.isRoot(t)||FS.getPath(t)===FS.cwd())return ERRNO_CODES.EBUSY}else if(FS.isDir(t.mode))return ERRNO_CODES.EISDIR;return 0},mayOpen:function(e,r){return e?FS.isLink(e.mode)?ERRNO_CODES.ELOOP:FS.isDir(e.mode)&&(0!=(2097155&r)||512&r)?ERRNO_CODES.EISDIR:FS.nodePermissions(e,FS.flagsToPermissionString(r)):ERRNO_CODES.ENOENT},MAX_OPEN_FDS:4096,nextfd:function(e,r){e=e||0,r=r||FS.MAX_OPEN_FDS;for(var n=e;n<=r;n++)if(!FS.streams[n])return n;throw new FS.ErrnoError(ERRNO_CODES.EMFILE)},getStream:function(e){return FS.streams[e]},createStream:function(e,r,n){FS.FSStream||(FS.FSStream=function(){},FS.FSStream.prototype={},Object.defineProperties(FS.FSStream.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}}}));var t=new FS.FSStream;for(var i in e)t[i]=e[i];e=t;var o=FS.nextfd(r,n);return e.fd=o,FS.streams[o]=e},closeStream:function(e){FS.streams[e]=null},chrdev_stream_ops:{open:function(e){var r=FS.getDevice(e.node.rdev);e.stream_ops=r.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:function(){throw new FS.ErrnoError(ERRNO_CODES.ESPIPE)}},major:function(e){return e>>8},minor:function(e){return 255&e},makedev:function(e,r){return e<<8|r},registerDevice:function(e,r){FS.devices[e]={stream_ops:r}},getDevice:function(e){return FS.devices[e]},getMounts:function(e){for(var r=[],n=[e];n.length;){var t=n.pop();r.push(t),n.push.apply(n,t.mounts)}return r},syncfs:function(r,n){"function"==typeof r&&(n=r,r=!1);var t=FS.getMounts(FS.root.mount),i=0;function o(e){if(e)return o.errored?void 0:(o.errored=!0,n(e));++i>=t.length&&n(null)}t.forEach(function(e){if(!e.type.syncfs)return o(null);e.type.syncfs(e,r,o)})},mount:function(e,r,n){var t,i="/"===n,o=!n;if(i&&FS.root)throw new FS.ErrnoError(ERRNO_CODES.EBUSY);if(!i&&!o){var a=FS.lookupPath(n,{follow_mount:!1});if(n=a.path,t=a.node,FS.isMountpoint(t))throw new FS.ErrnoError(ERRNO_CODES.EBUSY);if(!FS.isDir(t.mode))throw new FS.ErrnoError(ERRNO_CODES.ENOTDIR)}var s={type:e,opts:r,mountpoint:n,mounts:[]},l=e.mount(s);return(l.mount=s).root=l,i?FS.root=l:t&&(t.mounted=s,t.mount&&t.mount.mounts.push(s)),l},unmount:function(e){var r=FS.lookupPath(e,{follow_mount:!1});if(!FS.isMountpoint(r.node))throw new FS.ErrnoError(ERRNO_CODES.EINVAL);var n=r.node,t=n.mounted,i=FS.getMounts(t);Object.keys(FS.nameTable).forEach(function(e){for(var r=FS.nameTable[e];r;){var n=r.name_next;-1!==i.indexOf(r.mount)&&FS.destroyNode(r),r=n}}),n.mounted=null;var o=n.mount.mounts.indexOf(t);assert(-1!==o),n.mount.mounts.splice(o,1)},lookup:function(e,r){return e.node_ops.lookup(e,r)},mknod:function(e,r,n){var t=FS.lookupPath(e,{parent:!0}).node,i=PATH.basename(e);if(!i||"."===i||".."===i)throw new FS.ErrnoError(ERRNO_CODES.EINVAL);var o=FS.mayCreate(t,i);if(o)throw new FS.ErrnoError(o);if(!t.node_ops.mknod)throw new FS.ErrnoError(ERRNO_CODES.EPERM);return t.node_ops.mknod(t,i,r,n)},create:function(e,r){return r=void 0!==r?r:438,r&=4095,r|=32768,FS.mknod(e,r,0)},mkdir:function(e,r){return r=void 0!==r?r:511,r&=1023,r|=16384,FS.mknod(e,r,0)},mkdev:function(e,r,n){return void 0===n&&(n=r,r=438),r|=8192,FS.mknod(e,r,n)},symlink:function(e,r){if(!PATH.resolve(e))throw new FS.ErrnoError(ERRNO_CODES.ENOENT);var n=FS.lookupPath(r,{parent:!0}).node;if(!n)throw new FS.ErrnoError(ERRNO_CODES.ENOENT);var t=PATH.basename(r),i=FS.mayCreate(n,t);if(i)throw new FS.ErrnoError(i);if(!n.node_ops.symlink)throw new FS.ErrnoError(ERRNO_CODES.EPERM);return n.node_ops.symlink(n,t,e)},rename:function(r,n){var e,t,i=PATH.dirname(r),o=PATH.dirname(n),a=PATH.basename(r),s=PATH.basename(n);try{e=FS.lookupPath(r,{parent:!0}).node,t=FS.lookupPath(n,{parent:!0}).node}catch(e){throw new FS.ErrnoError(ERRNO_CODES.EBUSY)}if(!e||!t)throw new FS.ErrnoError(ERRNO_CODES.ENOENT);if(e.mount!==t.mount)throw new FS.ErrnoError(ERRNO_CODES.EXDEV);var l,f=FS.lookupNode(e,a),u=PATH.relative(r,o);if("."!==u.charAt(0))throw new FS.ErrnoError(ERRNO_CODES.EINVAL);if("."!==(u=PATH.relative(n,i)).charAt(0))throw new FS.ErrnoError(ERRNO_CODES.ENOTEMPTY);try{l=FS.lookupNode(t,s)}catch(e){}if(f!==l){var c=FS.isDir(f.mode),d=FS.mayDelete(e,a,c);if(d)throw new FS.ErrnoError(d);if(d=l?FS.mayDelete(t,s,c):FS.mayCreate(t,s))throw new FS.ErrnoError(d);if(!e.node_ops.rename)throw new FS.ErrnoError(ERRNO_CODES.EPERM);if(FS.isMountpoint(f)||l&&FS.isMountpoint(l))throw new FS.ErrnoError(ERRNO_CODES.EBUSY);if(t!==e&&(d=FS.nodePermissions(e,"w")))throw new FS.ErrnoError(d);try{FS.trackingDelegate.willMovePath&&FS.trackingDelegate.willMovePath(r,n)}catch(e){console.log("FS.trackingDelegate['willMovePath']('"+r+"', '"+n+"') threw an exception: "+e.message)}FS.hashRemoveNode(f);try{e.node_ops.rename(f,t,s)}catch(e){throw e}finally{FS.hashAddNode(f)}try{FS.trackingDelegate.onMovePath&&FS.trackingDelegate.onMovePath(r,n)}catch(e){console.log("FS.trackingDelegate['onMovePath']('"+r+"', '"+n+"') threw an exception: "+e.message)}}},rmdir:function(r){var e=FS.lookupPath(r,{parent:!0}).node,n=PATH.basename(r),t=FS.lookupNode(e,n),i=FS.mayDelete(e,n,!0);if(i)throw new FS.ErrnoError(i);if(!e.node_ops.rmdir)throw new FS.ErrnoError(ERRNO_CODES.EPERM);if(FS.isMountpoint(t))throw new FS.ErrnoError(ERRNO_CODES.EBUSY);try{FS.trackingDelegate.willDeletePath&&FS.trackingDelegate.willDeletePath(r)}catch(e){console.log("FS.trackingDelegate['willDeletePath']('"+r+"') threw an exception: "+e.message)}e.node_ops.rmdir(e,n),FS.destroyNode(t);try{FS.trackingDelegate.onDeletePath&&FS.trackingDelegate.onDeletePath(r)}catch(e){console.log("FS.trackingDelegate['onDeletePath']('"+r+"') threw an exception: "+e.message)}},readdir:function(e){var r=FS.lookupPath(e,{follow:!0}).node;if(!r.node_ops.readdir)throw new FS.ErrnoError(ERRNO_CODES.ENOTDIR);return r.node_ops.readdir(r)},unlink:function(r){var e=FS.lookupPath(r,{parent:!0}).node,n=PATH.basename(r),t=FS.lookupNode(e,n),i=FS.mayDelete(e,n,!1);if(i)throw i===ERRNO_CODES.EISDIR&&(i=ERRNO_CODES.EPERM),new FS.ErrnoError(i);if(!e.node_ops.unlink)throw new FS.ErrnoError(ERRNO_CODES.EPERM);if(FS.isMountpoint(t))throw new FS.ErrnoError(ERRNO_CODES.EBUSY);try{FS.trackingDelegate.willDeletePath&&FS.trackingDelegate.willDeletePath(r)}catch(e){console.log("FS.trackingDelegate['willDeletePath']('"+r+"') threw an exception: "+e.message)}e.node_ops.unlink(e,n),FS.destroyNode(t);try{FS.trackingDelegate.onDeletePath&&FS.trackingDelegate.onDeletePath(r)}catch(e){console.log("FS.trackingDelegate['onDeletePath']('"+r+"') threw an exception: "+e.message)}},readlink:function(e){var r=FS.lookupPath(e).node;if(!r)throw new FS.ErrnoError(ERRNO_CODES.ENOENT);if(!r.node_ops.readlink)throw new FS.ErrnoError(ERRNO_CODES.EINVAL);return PATH.resolve(FS.getPath(r.parent),r.node_ops.readlink(r))},stat:function(e,r){var n=FS.lookupPath(e,{follow:!r}).node;if(!n)throw new FS.ErrnoError(ERRNO_CODES.ENOENT);if(!n.node_ops.getattr)throw new FS.ErrnoError(ERRNO_CODES.EPERM);return n.node_ops.getattr(n)},lstat:function(e){return FS.stat(e,!0)},chmod:function(e,r,n){var t;"string"==typeof e?t=FS.lookupPath(e,{follow:!n}).node:t=e;if(!t.node_ops.setattr)throw new FS.ErrnoError(ERRNO_CODES.EPERM);t.node_ops.setattr(t,{mode:4095&r|-4096&t.mode,timestamp:Date.now()})},lchmod:function(e,r){FS.chmod(e,r,!0)},fchmod:function(e,r){var n=FS.getStream(e);if(!n)throw new FS.ErrnoError(ERRNO_CODES.EBADF);FS.chmod(n.node,r)},chown:function(e,r,n,t){var i;"string"==typeof e?i=FS.lookupPath(e,{follow:!t}).node:i=e;if(!i.node_ops.setattr)throw new FS.ErrnoError(ERRNO_CODES.EPERM);i.node_ops.setattr(i,{timestamp:Date.now()})},lchown:function(e,r,n){FS.chown(e,r,n,!0)},fchown:function(e,r,n){var t=FS.getStream(e);if(!t)throw new FS.ErrnoError(ERRNO_CODES.EBADF);FS.chown(t.node,r,n)},truncate:function(e,r){if(r<0)throw new FS.ErrnoError(ERRNO_CODES.EINVAL);var n;"string"==typeof e?n=FS.lookupPath(e,{follow:!0}).node:n=e;if(!n.node_ops.setattr)throw new FS.ErrnoError(ERRNO_CODES.EPERM);if(FS.isDir(n.mode))throw new FS.ErrnoError(ERRNO_CODES.EISDIR);if(!FS.isFile(n.mode))throw new FS.ErrnoError(ERRNO_CODES.EINVAL);var t=FS.nodePermissions(n,"w");if(t)throw new FS.ErrnoError(t);n.node_ops.setattr(n,{size:r,timestamp:Date.now()})},ftruncate:function(e,r){var n=FS.getStream(e);if(!n)throw new FS.ErrnoError(ERRNO_CODES.EBADF);if(0==(2097155&n.flags))throw new FS.ErrnoError(ERRNO_CODES.EINVAL);FS.truncate(n.node,r)},utime:function(e,r,n){var t=FS.lookupPath(e,{follow:!0}).node;t.node_ops.setattr(t,{timestamp:Math.max(r,n)})},open:function(r,e,n,t,i){if(""===r)throw new FS.ErrnoError(ERRNO_CODES.ENOENT);var o;if(n=void 0===n?438:n,n=64&(e="string"==typeof e?FS.modeStringToFlags(e):e)?4095&n|32768:0,"object"==typeof r)o=r;else{r=PATH.normalize(r);try{o=FS.lookupPath(r,{follow:!(131072&e)}).node}catch(e){}}var a=!1;if(64&e)if(o){if(128&e)throw new FS.ErrnoError(ERRNO_CODES.EEXIST)}else o=FS.mknod(r,n,0),a=!0;if(!o)throw new FS.ErrnoError(ERRNO_CODES.ENOENT);if(FS.isChrdev(o.mode)&&(e&=-513),65536&e&&!FS.isDir(o.mode))throw new FS.ErrnoError(ERRNO_CODES.ENOTDIR);if(!a){var s=FS.mayOpen(o,e);if(s)throw new FS.ErrnoError(s)}512&e&&FS.truncate(o,0),e&=-641;var l=FS.createStream({node:o,path:FS.getPath(o),flags:e,seekable:!0,position:0,stream_ops:o.stream_ops,ungotten:[],error:!1},t,i);l.stream_ops.open&&l.stream_ops.open(l),!Module.logReadFiles||1&e||(FS.readFiles||(FS.readFiles={}),r in FS.readFiles||(FS.readFiles[r]=1,Module.printErr("read file: "+r)));try{if(FS.trackingDelegate.onOpenFile){var f=0;1!=(2097155&e)&&(f|=FS.tracking.openFlags.READ),0!=(2097155&e)&&(f|=FS.tracking.openFlags.WRITE),FS.trackingDelegate.onOpenFile(r,f)}}catch(e){console.log("FS.trackingDelegate['onOpenFile']('"+r+"', flags) threw an exception: "+e.message)}return l},close:function(e){e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{FS.closeStream(e.fd)}},llseek:function(e,r,n){if(!e.seekable||!e.stream_ops.llseek)throw new FS.ErrnoError(ERRNO_CODES.ESPIPE);return e.position=e.stream_ops.llseek(e,r,n),e.ungotten=[],e.position},read:function(e,r,n,t,i){if(t<0||i<0)throw new FS.ErrnoError(ERRNO_CODES.EINVAL);if(1==(2097155&e.flags))throw new FS.ErrnoError(ERRNO_CODES.EBADF);if(FS.isDir(e.node.mode))throw new FS.ErrnoError(ERRNO_CODES.EISDIR);if(!e.stream_ops.read)throw new FS.ErrnoError(ERRNO_CODES.EINVAL);var o=!0;if(void 0===i)i=e.position,o=!1;else if(!e.seekable)throw new FS.ErrnoError(ERRNO_CODES.ESPIPE);var a=e.stream_ops.read(e,r,n,t,i);return o||(e.position+=a),a},write:function(e,r,n,t,i,o){if(t<0||i<0)throw new FS.ErrnoError(ERRNO_CODES.EINVAL);if(0==(2097155&e.flags))throw new FS.ErrnoError(ERRNO_CODES.EBADF);if(FS.isDir(e.node.mode))throw new FS.ErrnoError(ERRNO_CODES.EISDIR);if(!e.stream_ops.write)throw new FS.ErrnoError(ERRNO_CODES.EINVAL);1024&e.flags&&FS.llseek(e,0,2);var a=!0;if(void 0===i)i=e.position,a=!1;else if(!e.seekable)throw new FS.ErrnoError(ERRNO_CODES.ESPIPE);var s=e.stream_ops.write(e,r,n,t,i,o);a||(e.position+=s);try{e.path&&FS.trackingDelegate.onWriteToFile&&FS.trackingDelegate.onWriteToFile(e.path)}catch(e){console.log("FS.trackingDelegate['onWriteToFile']('"+path+"') threw an exception: "+e.message)}return s},allocate:function(e,r,n){if(r<0||n<=0)throw new FS.ErrnoError(ERRNO_CODES.EINVAL);if(0==(2097155&e.flags))throw new FS.ErrnoError(ERRNO_CODES.EBADF);if(!FS.isFile(e.node.mode)&&!FS.isDir(node.mode))throw new FS.ErrnoError(ERRNO_CODES.ENODEV);if(!e.stream_ops.allocate)throw new FS.ErrnoError(ERRNO_CODES.EOPNOTSUPP);e.stream_ops.allocate(e,r,n)},mmap:function(e,r,n,t,i,o,a){if(1==(2097155&e.flags))throw new FS.ErrnoError(ERRNO_CODES.EACCES);if(!e.stream_ops.mmap)throw new FS.ErrnoError(ERRNO_CODES.ENODEV);return e.stream_ops.mmap(e,r,n,t,i,o,a)},msync:function(e,r,n,t,i){return e&&e.stream_ops.msync?e.stream_ops.msync(e,r,n,t,i):0},munmap:function(e){return 0},ioctl:function(e,r,n){if(!e.stream_ops.ioctl)throw new FS.ErrnoError(ERRNO_CODES.ENOTTY);return e.stream_ops.ioctl(e,r,n)},readFile:function(e,r){if((r=r||{}).flags=r.flags||"r",r.encoding=r.encoding||"binary","utf8"!==r.encoding&&"binary"!==r.encoding)throw new Error('Invalid encoding type "'+r.encoding+'"');var n,t=FS.open(e,r.flags),i=FS.stat(e).size,o=new Uint8Array(i);return FS.read(t,o,0,i,0),"utf8"===r.encoding?n=UTF8ArrayToString(o,0):"binary"===r.encoding&&(n=o),FS.close(t),n},writeFile:function(e,r,n){if((n=n||{}).flags=n.flags||"w",n.encoding=n.encoding||"utf8","utf8"!==n.encoding&&"binary"!==n.encoding)throw new Error('Invalid encoding type "'+n.encoding+'"');var t=FS.open(e,n.flags,n.mode);if("utf8"===n.encoding){var i=new Uint8Array(lengthBytesUTF8(r)+1),o=stringToUTF8Array(r,i,0,i.length);FS.write(t,i,0,o,0,n.canOwn)}else"binary"===n.encoding&&FS.write(t,r,0,r.length,0,n.canOwn);FS.close(t)},cwd:function(){return FS.currentPath},chdir:function(e){var r=FS.lookupPath(e,{follow:!0});if(!FS.isDir(r.node.mode))throw new FS.ErrnoError(ERRNO_CODES.ENOTDIR);var n=FS.nodePermissions(r.node,"x");if(n)throw new FS.ErrnoError(n);FS.currentPath=r.path},createDefaultDirectories:function(){FS.mkdir("/tmp"),FS.mkdir("/home"),FS.mkdir("/home/<USER>")},createDefaultDevices:function(){var e;if(FS.mkdir("/dev"),FS.registerDevice(FS.makedev(1,3),{read:function(){return 0},write:function(e,r,n,t,i){return t}}),FS.mkdev("/dev/null",FS.makedev(1,3)),TTY.register(FS.makedev(5,0),TTY.default_tty_ops),TTY.register(FS.makedev(6,0),TTY.default_tty1_ops),FS.mkdev("/dev/tty",FS.makedev(5,0)),FS.mkdev("/dev/tty1",FS.makedev(6,0)),"undefined"!=typeof crypto){var r=new Uint8Array(1);e=function(){return crypto.getRandomValues(r),r[0]}}else e=function(){return 256*Math.random()|0};FS.createDevice("/dev","random",e),FS.createDevice("/dev","urandom",e),FS.mkdir("/dev/shm"),FS.mkdir("/dev/shm/tmp")},createSpecialDirectories:function(){FS.mkdir("/proc"),FS.mkdir("/proc/self"),FS.mkdir("/proc/self/fd"),FS.mount({mount:function(){var e=FS.createNode("/proc/self","fd",16895,73);return e.node_ops={lookup:function(e,r){var n=+r,t=FS.getStream(n);if(!t)throw new FS.ErrnoError(ERRNO_CODES.EBADF);var i={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:function(){return t.path}}};return i.parent=i}},e}},{},"/proc/self/fd")},createStandardStreams:function(){Module.stdin?FS.createDevice("/dev","stdin",Module.stdin):FS.symlink("/dev/tty","/dev/stdin"),Module.stdout?FS.createDevice("/dev","stdout",null,Module.stdout):FS.symlink("/dev/tty","/dev/stdout"),Module.stderr?FS.createDevice("/dev","stderr",null,Module.stderr):FS.symlink("/dev/tty1","/dev/stderr");var e=FS.open("/dev/stdin","r");assert(0===e.fd,"invalid handle for stdin ("+e.fd+")");var r=FS.open("/dev/stdout","w");assert(1===r.fd,"invalid handle for stdout ("+r.fd+")");var n=FS.open("/dev/stderr","w");assert(2===n.fd,"invalid handle for stderr ("+n.fd+")")},ensureErrnoError:function(){FS.ErrnoError||(FS.ErrnoError=function(e,r){this.node=r,this.setErrno=function(e){for(var r in this.errno=e,ERRNO_CODES)if(ERRNO_CODES[r]===e){this.code=r;break}},this.setErrno(e),this.message=ERRNO_MESSAGES[e]},FS.ErrnoError.prototype=new Error,FS.ErrnoError.prototype.constructor=FS.ErrnoError,[ERRNO_CODES.ENOENT].forEach(function(e){FS.genericErrors[e]=new FS.ErrnoError(e),FS.genericErrors[e].stack="<generic error, no stack>"}))},staticInit:function(){FS.ensureErrnoError(),FS.nameTable=new Array(4096),FS.mount(MEMFS,{},"/"),FS.createDefaultDirectories(),FS.createDefaultDevices(),FS.createSpecialDirectories(),FS.filesystems={MEMFS:MEMFS,IDBFS:IDBFS,NODEFS:{},WORKERFS:WORKERFS}},init:function(e,r,n){assert(!FS.init.initialized,"FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)"),FS.init.initialized=!0,FS.ensureErrnoError(),Module.stdin=e||Module.stdin,Module.stdout=r||Module.stdout,Module.stderr=n||Module.stderr,FS.createStandardStreams()},quit:function(){FS.init.initialized=!1;var e=Module._fflush;e&&e(0);for(var r=0;r<FS.streams.length;r++){var n=FS.streams[r];n&&FS.close(n)}},getMode:function(e,r){var n=0;return e&&(n|=365),r&&(n|=146),n},joinPath:function(e,r){var n=PATH.join.apply(null,e);return r&&"/"==n[0]&&(n=n.substr(1)),n},absolutePath:function(e,r){return PATH.resolve(r,e)},standardizePath:function(e){return PATH.normalize(e)},findObject:function(e,r){var n=FS.analyzePath(e,r);return n.exists?n.object:(___setErrNo(n.error),null)},analyzePath:function(e,r){try{e=(t=FS.lookupPath(e,{follow:!r})).path}catch(e){}var n={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var t=FS.lookupPath(e,{parent:!0});n.parentExists=!0,n.parentPath=t.path,n.parentObject=t.node,n.name=PATH.basename(e),t=FS.lookupPath(e,{follow:!r}),n.exists=!0,n.path=t.path,n.object=t.node,n.name=t.node.name,n.isRoot="/"===t.path}catch(e){n.error=e.errno}return n},createFolder:function(e,r,n,t){var i=PATH.join2("string"==typeof e?e:FS.getPath(e),r),o=FS.getMode(n,t);return FS.mkdir(i,o)},createPath:function(e,r,n,t){e="string"==typeof e?e:FS.getPath(e);for(var i=r.split("/").reverse();i.length;){var o=i.pop();if(o){var a=PATH.join2(e,o);try{FS.mkdir(a)}catch(e){}e=a}}return a},createFile:function(e,r,n,t,i){var o=PATH.join2("string"==typeof e?e:FS.getPath(e),r),a=FS.getMode(t,i);return FS.create(o,a)},createDataFile:function(e,r,n,t,i,o){var a=r?PATH.join2("string"==typeof e?e:FS.getPath(e),r):e,s=FS.getMode(t,i),l=FS.create(a,s);if(n){if("string"==typeof n){for(var f=new Array(n.length),u=0,c=n.length;u<c;++u)f[u]=n.charCodeAt(u);n=f}FS.chmod(l,146|s);var d=FS.open(l,"w");FS.write(d,n,0,n.length,0,o),FS.close(d),FS.chmod(l,s)}return l},createDevice:function(e,r,l,a){var n=PATH.join2("string"==typeof e?e:FS.getPath(e),r),t=FS.getMode(!!l,!!a);FS.createDevice.major||(FS.createDevice.major=64);var i=FS.makedev(FS.createDevice.major++,0);return FS.registerDevice(i,{open:function(e){e.seekable=!1},close:function(e){a&&a.buffer&&a.buffer.length&&a(10)},read:function(e,r,n,t,i){for(var o=0,a=0;a<t;a++){var s;try{s=l()}catch(e){throw new FS.ErrnoError(ERRNO_CODES.EIO)}if(void 0===s&&0===o)throw new FS.ErrnoError(ERRNO_CODES.EAGAIN);if(null==s)break;o++,r[n+a]=s}return o&&(e.node.timestamp=Date.now()),o},write:function(e,r,n,t,i){for(var o=0;o<t;o++)try{a(r[n+o])}catch(e){throw new FS.ErrnoError(ERRNO_CODES.EIO)}return t&&(e.node.timestamp=Date.now()),o}}),FS.mkdev(n,t,i)},createLink:function(e,r,n,t,i){var o=PATH.join2("string"==typeof e?e:FS.getPath(e),r);return FS.symlink(n,o)},forceLoadFile:function(e){if(e.isDevice||e.isFolder||e.link||e.contents)return!0;var r=!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!Module.read)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=intArrayFromString(Module.read(e.url),!0),e.usedBytes=e.contents.length}catch(e){r=!1}return r||___setErrNo(ERRNO_CODES.EIO),r},createLazyFile:function(e,r,a,n,t){function i(){this.lengthKnown=!1,this.chunks=[]}if(i.prototype.get=function(e){if(!(e>this.length-1||e<0)){var r=e%this.chunkSize,n=e/this.chunkSize|0;return this.getter(n)[r]}},i.prototype.setDataGetter=function(e){this.getter=e},i.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",a,!1),e.send(null),!(200<=e.status&&e.status<300||304===e.status))throw new Error("Couldn't load "+a+". Status: "+e.status);var r,t=Number(e.getResponseHeader("Content-length")),n=(r=e.getResponseHeader("Accept-Ranges"))&&"bytes"===r,i=1048576;n||(i=t);var o=this;o.setDataGetter(function(e){var r=e*i,n=(e+1)*i-1;if(n=Math.min(n,t-1),void 0===o.chunks[e]&&(o.chunks[e]=function(e,r){if(r<e)throw new Error("invalid range ("+e+", "+r+") or no bytes requested!");if(t-1<r)throw new Error("only "+t+" bytes available! programmer error!");var n=new XMLHttpRequest;if(n.open("GET",a,!1),t!==i&&n.setRequestHeader("Range","bytes="+e+"-"+r),"undefined"!=typeof Uint8Array&&(n.responseType="arraybuffer"),n.overrideMimeType&&n.overrideMimeType("text/plain; charset=x-user-defined"),n.send(null),!(200<=n.status&&n.status<300||304===n.status))throw new Error("Couldn't load "+a+". Status: "+n.status);return void 0!==n.response?new Uint8Array(n.response||[]):intArrayFromString(n.responseText||"",!0)}(r,n)),void 0===o.chunks[e])throw new Error("doXHR failed!");return o.chunks[e]}),this._length=t,this._chunkSize=i,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!ENVIRONMENT_IS_WORKER)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var o=new i;Object.defineProperty(o,"length",{get:function(){return this.lengthKnown||this.cacheLength(),this._length}}),Object.defineProperty(o,"chunkSize",{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}});var s={isDevice:!1,contents:o}}else s={isDevice:!1,url:a};var l=FS.createFile(e,r,s,n,t);s.contents?l.contents=s.contents:s.url&&(l.contents=null,l.url=s.url),Object.defineProperty(l,"usedBytes",{get:function(){return this.contents.length}});var f={};return Object.keys(l.stream_ops).forEach(function(e){var r=l.stream_ops[e];f[e]=function(){if(!FS.forceLoadFile(l))throw new FS.ErrnoError(ERRNO_CODES.EIO);return r.apply(null,arguments)}}),f.read=function(e,r,n,t,i){if(!FS.forceLoadFile(l))throw new FS.ErrnoError(ERRNO_CODES.EIO);var o=e.node.contents;if(i>=o.length)return 0;var a=Math.min(o.length-i,t);if(assert(0<=a),o.slice)for(var s=0;s<a;s++)r[n+s]=o[i+s];else for(s=0;s<a;s++)r[n+s]=o.get(i+s);return a},l.stream_ops=f,l},createPreloadedFile:function(i,o,e,a,s,l,f,u,c,d){Browser.init();var h=o?PATH.resolve(PATH.join2(i,o)):i,w=getUniqueRunDependency("cp "+h);function r(r){function n(e){d&&d(),u||FS.createDataFile(i,o,e,a,s,c),l&&l(),removeRunDependency(w)}var t=!1;Module.preloadPlugins.forEach(function(e){t||e.canHandle(h)&&(e.handle(r,h,n,function(){f&&f(),removeRunDependency(w)}),t=!0)}),t||n(r)}addRunDependency(w),"string"==typeof e?Browser.asyncLoad(e,function(e){r(e)},f):r(e)},indexedDB:function(){return window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB},DB_NAME:function(){return"EM_FS_"+window.location.pathname},DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:function(r,s,l){s=s||function(){},l=l||function(){};var e=FS.indexedDB();try{var f=e.open(FS.DB_NAME(),FS.DB_VERSION)}catch(e){return l(e)}f.onupgradeneeded=function(){console.log("creating db"),f.result.createObjectStore(FS.DB_STORE_NAME)},f.onsuccess=function(){var e=f.result.transaction([FS.DB_STORE_NAME],"readwrite"),n=e.objectStore(FS.DB_STORE_NAME),t=0,i=0,o=r.length;function a(){0==i?s():l()}r.forEach(function(e){var r=n.put(FS.analyzePath(e).object.contents,e);r.onsuccess=function(){++t+i==o&&a()},r.onerror=function(){t+ ++i==o&&a()}}),e.onerror=l},f.onerror=l},loadFilesFromDB:function(s,l,f){l=l||function(){},f=f||function(){};var e=FS.indexedDB();try{var u=e.open(FS.DB_NAME(),FS.DB_VERSION)}catch(e){return f(e)}u.onupgradeneeded=f,u.onsuccess=function(){var e=u.result;try{var r=e.transaction([FS.DB_STORE_NAME],"readonly")}catch(e){return void f(e)}var n=r.objectStore(FS.DB_STORE_NAME),t=0,i=0,o=s.length;function a(){0==i?l():f()}s.forEach(function(e){var r=n.get(e);r.onsuccess=function(){FS.analyzePath(e).exists&&FS.unlink(e),FS.createDataFile(PATH.dirname(e),PATH.basename(e),r.result,!0,!0,!0),++t+i==o&&a()},r.onerror=function(){t+ ++i==o&&a()}}),r.onerror=f},u.onerror=f}},PATH={splitPath:function(e){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1)},normalizeArray:function(e,r){for(var n=0,t=e.length-1;0<=t;t--){var i=e[t];"."===i?e.splice(t,1):".."===i?(e.splice(t,1),n++):n&&(e.splice(t,1),n--)}if(r)for(;n--;n)e.unshift("..");return e},normalize:function(e){var r="/"===e.charAt(0),n="/"===e.substr(-1);return(e=PATH.normalizeArray(e.split("/").filter(function(e){return!!e}),!r).join("/"))||r||(e="."),e&&n&&(e+="/"),(r?"/":"")+e},dirname:function(e){var r=PATH.splitPath(e),n=r[0],t=r[1];return n||t?(t&&(t=t.substr(0,t.length-1)),n+t):"."},basename:function(e){if("/"===e)return"/";var r=e.lastIndexOf("/");return-1===r?e:e.substr(r+1)},extname:function(e){return PATH.splitPath(e)[3]},join:function(){var e=Array.prototype.slice.call(arguments,0);return PATH.normalize(e.join("/"))},join2:function(e,r){return PATH.normalize(e+"/"+r)},resolve:function(){for(var e="",r=!1,n=arguments.length-1;-1<=n&&!r;n--){var t=0<=n?arguments[n]:FS.cwd();if("string"!=typeof t)throw new TypeError("Arguments to path.resolve must be strings");if(!t)return"";e=t+"/"+e,r="/"===t.charAt(0)}return(r?"/":"")+(e=PATH.normalizeArray(e.split("/").filter(function(e){return!!e}),!r).join("/"))||"."},relative:function(e,r){function n(e){for(var r=0;r<e.length&&""===e[r];r++);for(var n=e.length-1;0<=n&&""===e[n];n--);return n<r?[]:e.slice(r,n-r+1)}e=PATH.resolve(e).substr(1),r=PATH.resolve(r).substr(1);for(var t=n(e.split("/")),i=n(r.split("/")),o=Math.min(t.length,i.length),a=o,s=0;s<o;s++)if(t[s]!==i[s]){a=s;break}var l=[];for(s=a;s<t.length;s++)l.push("..");return(l=l.concat(i.slice(a))).join("/")}};function _emscripten_set_main_loop_timing(e,r){if(Browser.mainLoop.timingMode=e,Browser.mainLoop.timingValue=r,!Browser.mainLoop.func)return 1;if(0==e)Browser.mainLoop.scheduler=function(){setTimeout(Browser.mainLoop.runner,r)},Browser.mainLoop.method="timeout";else if(1==e)Browser.mainLoop.scheduler=function(){Browser.requestAnimationFrame(Browser.mainLoop.runner)},Browser.mainLoop.method="rAF";else if(2==e){if(!window.setImmediate){var n=[];window.addEventListener("message",function(e){e.source===window&&"__emcc"===e.data&&(e.stopPropagation(),n.shift()())},!0),window.setImmediate=function(e){n.push(e),window.postMessage("__emcc","*")}}Browser.mainLoop.scheduler=function(){window.setImmediate(Browser.mainLoop.runner)},Browser.mainLoop.method="immediate"}return 0}function _emscripten_set_main_loop(i,e,r,o,n){Module.noExitRuntime=!0,assert(!Browser.mainLoop.func,"emscripten_set_main_loop: there can only be one main loop function at once: call emscripten_cancel_main_loop to cancel the previous one before setting a new one with different parameters."),Browser.mainLoop.func=i,Browser.mainLoop.arg=o;var a=Browser.mainLoop.currentlyRunningMainloop;if(Browser.mainLoop.runner=function(){if(!ABORT){if(0<Browser.mainLoop.queue.length){var e=Date.now(),r=Browser.mainLoop.queue.shift();if(r.func(r.arg),Browser.mainLoop.remainingBlockers){var n=Browser.mainLoop.remainingBlockers,t=n%1==0?n-1:Math.floor(n);r.counted?Browser.mainLoop.remainingBlockers=t:(t+=.5,Browser.mainLoop.remainingBlockers=(8*n+t)/9)}return console.log('main loop blocker "'+r.name+'" took '+(Date.now()-e)+" ms"),Browser.mainLoop.updateStatus(),void setTimeout(Browser.mainLoop.runner,0)}a<Browser.mainLoop.currentlyRunningMainloop||(Browser.mainLoop.currentFrameNumber=Browser.mainLoop.currentFrameNumber+1|0,1==Browser.mainLoop.timingMode&&1<Browser.mainLoop.timingValue&&Browser.mainLoop.currentFrameNumber%Browser.mainLoop.timingValue!=0?Browser.mainLoop.scheduler():("timeout"===Browser.mainLoop.method&&Module.ctx&&(Module.printErr("Looks like you are rendering without using requestAnimationFrame for the main loop. You should use 0 for the frame rate in emscripten_set_main_loop in order to use requestAnimationFrame, as that can greatly improve your frame rates!"),Browser.mainLoop.method=""),Browser.mainLoop.runIter(function(){void 0!==o?Runtime.dynCall("vi",i,[o]):Runtime.dynCall("v",i)}),a<Browser.mainLoop.currentlyRunningMainloop||("object"==typeof SDL&&SDL.audio&&SDL.audio.queueNewAudioData&&SDL.audio.queueNewAudioData(),Browser.mainLoop.scheduler())))}},n||(e&&0<e?_emscripten_set_main_loop_timing(0,1e3/e):_emscripten_set_main_loop_timing(1,1),Browser.mainLoop.scheduler()),r)throw"SimulateInfiniteLoop"}var Browser={mainLoop:{scheduler:null,method:"",currentlyRunningMainloop:0,func:null,arg:0,timingMode:0,timingValue:0,currentFrameNumber:0,queue:[],pause:function(){Browser.mainLoop.scheduler=null,Browser.mainLoop.currentlyRunningMainloop++},resume:function(){Browser.mainLoop.currentlyRunningMainloop++;var e=Browser.mainLoop.timingMode,r=Browser.mainLoop.timingValue,n=Browser.mainLoop.func;Browser.mainLoop.func=null,_emscripten_set_main_loop(n,0,!1,Browser.mainLoop.arg,!0),_emscripten_set_main_loop_timing(e,r),Browser.mainLoop.scheduler()},updateStatus:function(){if(Module.setStatus){var e=Module.statusMessage||"Please wait...",r=Browser.mainLoop.remainingBlockers,n=Browser.mainLoop.expectedBlockers;r?r<n?Module.setStatus(e+" ("+(n-r)+"/"+n+")"):Module.setStatus(e):Module.setStatus("")}},runIter:function(e){if(!ABORT){if(Module.preMainLoop)if(!1===Module.preMainLoop())return;try{e()}catch(e){if(e instanceof ExitStatus)return;throw e&&"object"==typeof e&&e.stack&&Module.printErr("exception thrown: "+[e,e.stack]),e}Module.postMainLoop&&Module.postMainLoop()}}},isFullScreen:!1,pointerLock:!1,moduleContextCreatedCallbacks:[],workers:[],init:function(){if(Module.preloadPlugins||(Module.preloadPlugins=[]),!Browser.initted){Browser.initted=!0;try{new Blob,Browser.hasBlobConstructor=!0}catch(e){Browser.hasBlobConstructor=!1,console.log("warning: no blob constructor, cannot create blobs with mimetypes")}Browser.BlobBuilder="undefined"!=typeof MozBlobBuilder?MozBlobBuilder:"undefined"!=typeof WebKitBlobBuilder?WebKitBlobBuilder:Browser.hasBlobConstructor?null:console.log("warning: no BlobBuilder"),Browser.URLObject="undefined"!=typeof window?window.URL?window.URL:window.webkitURL:void 0,Module.noImageDecoding||void 0!==Browser.URLObject||(console.log("warning: Browser does not support creating object URLs. Built-in browser image decoding will not be available."),Module.noImageDecoding=!0);var e={canHandle:function(e){return!Module.noImageDecoding&&/\.(jpg|jpeg|png|bmp)$/i.test(e)},handle:function(r,n,t,i){var e=null;if(Browser.hasBlobConstructor)try{(e=new Blob([r],{type:Browser.getMimetype(n)})).size!==r.length&&(e=new Blob([new Uint8Array(r).buffer],{type:Browser.getMimetype(n)}))}catch(e){Runtime.warnOnce("Blob constructor present but fails: "+e+"; falling back to blob builder")}if(!e){var o=new Browser.BlobBuilder;o.append(new Uint8Array(r).buffer),e=o.getBlob()}var a=Browser.URLObject.createObjectURL(e),s=new Image;s.onload=function(){assert(s.complete,"Image "+n+" could not be decoded");var e=document.createElement("canvas");e.width=s.width,e.height=s.height,e.getContext("2d").drawImage(s,0,0),Module.preloadedImages[n]=e,Browser.URLObject.revokeObjectURL(a),t&&t(r)},s.onerror=function(e){console.log("Image "+a+" could not be decoded"),i&&i()},s.src=a}};Module.preloadPlugins.push(e);var r={canHandle:function(e){return!Module.noAudioDecoding&&e.substr(-4)in{".ogg":1,".wav":1,".mp3":1}},handle:function(r,n,t,e){var i=!1;function o(e){i||(i=!0,Module.preloadedAudios[n]=e,t&&t(r))}function a(){i||(i=!0,Module.preloadedAudios[n]=new Audio,e&&e())}if(!Browser.hasBlobConstructor)return a();try{var s=new Blob([r],{type:Browser.getMimetype(n)})}catch(e){return a()}var l=Browser.URLObject.createObjectURL(s),f=new Audio;f.addEventListener("canplaythrough",function(){o(f)},!1),f.onerror=function(e){i||(console.log("warning: browser could not fully decode audio "+n+", trying slower base64 approach"),f.src="data:audio/x-"+n.substr(-3)+";base64,"+function(e){for(var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n="",t=0,i=0,o=0;o<e.length;o++)for(t=t<<8|e[o],i+=8;6<=i;){var a=t>>i-6&63;i-=6,n+=r[a]}return 2==i?(n+=r[(3&t)<<4],n+="=="):4==i&&(n+=r[(15&t)<<2],n+="="),n}(r),o(f))},f.src=l,Browser.safeSetTimeout(function(){o(f)},1e4)}};Module.preloadPlugins.push(r);var n=Module.canvas;n&&(n.requestPointerLock=n.requestPointerLock||n.mozRequestPointerLock||n.webkitRequestPointerLock||n.msRequestPointerLock||function(){},n.exitPointerLock=document.exitPointerLock||document.mozExitPointerLock||document.webkitExitPointerLock||document.msExitPointerLock||function(){},n.exitPointerLock=n.exitPointerLock.bind(document),document.addEventListener("pointerlockchange",t,!1),document.addEventListener("mozpointerlockchange",t,!1),document.addEventListener("webkitpointerlockchange",t,!1),document.addEventListener("mspointerlockchange",t,!1),Module.elementPointerLock&&n.addEventListener("click",function(e){!Browser.pointerLock&&n.requestPointerLock&&(n.requestPointerLock(),e.preventDefault())},!1))}function t(){Browser.pointerLock=document.pointerLockElement===n||document.mozPointerLockElement===n||document.webkitPointerLockElement===n||document.msPointerLockElement===n}},createContext:function(e,r,n,t){if(r&&Module.ctx&&e==Module.canvas)return Module.ctx;var i,o;if(r){var a={antialias:!1,alpha:!1};if(t)for(var s in t)a[s]=t[s];(o=GL.createContext(e,a))&&(i=GL.getContext(o).GLctx),e.style.backgroundColor="black"}else i=e.getContext("2d");return i?(n&&(r||assert("undefined"==typeof GLctx,"cannot set in module if GLctx is used, but we are a non-GL context that would replace it"),Module.ctx=i,r&&GL.makeContextCurrent(o),Module.useWebGL=r,Browser.moduleContextCreatedCallbacks.forEach(function(e){e()}),Browser.init()),i):null},destroyContext:function(e,r,n){},fullScreenHandlersInstalled:!1,lockPointer:void 0,resizeCanvas:void 0,requestFullScreen:function(e,r,n){Browser.lockPointer=e,Browser.resizeCanvas=r,Browser.vrDevice=n,void 0===Browser.lockPointer&&(Browser.lockPointer=!0),void 0===Browser.resizeCanvas&&(Browser.resizeCanvas=!1),void 0===Browser.vrDevice&&(Browser.vrDevice=null);var t=Module.canvas;function i(){Browser.isFullScreen=!1;var e=t.parentNode;(document.webkitFullScreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.mozFullscreenElement||document.fullScreenElement||document.fullscreenElement||document.msFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement)===e?(t.cancelFullScreen=document.cancelFullScreen||document.mozCancelFullScreen||document.webkitCancelFullScreen||document.msExitFullscreen||document.exitFullscreen||function(){},t.cancelFullScreen=t.cancelFullScreen.bind(document),Browser.lockPointer&&t.requestPointerLock(),Browser.isFullScreen=!0,Browser.resizeCanvas&&Browser.setFullScreenCanvasSize()):(e.parentNode.insertBefore(t,e),e.parentNode.removeChild(e),Browser.resizeCanvas&&Browser.setWindowedCanvasSize()),Module.onFullScreen&&Module.onFullScreen(Browser.isFullScreen),Browser.updateCanvasDimensions(t)}Browser.fullScreenHandlersInstalled||(Browser.fullScreenHandlersInstalled=!0,document.addEventListener("fullscreenchange",i,!1),document.addEventListener("mozfullscreenchange",i,!1),document.addEventListener("webkitfullscreenchange",i,!1),document.addEventListener("MSFullscreenChange",i,!1));var o=document.createElement("div");t.parentNode.insertBefore(o,t),o.appendChild(t),o.requestFullScreen=o.requestFullScreen||o.mozRequestFullScreen||o.msRequestFullscreen||(o.webkitRequestFullScreen?function(){o.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT)}:null),n?o.requestFullScreen({vrDisplay:n}):o.requestFullScreen()},nextRAF:0,fakeRequestAnimationFrame:function(e){var r=Date.now();if(0===Browser.nextRAF)Browser.nextRAF=r+1e3/60;else for(;r+2>=Browser.nextRAF;)Browser.nextRAF+=1e3/60;var n=Math.max(Browser.nextRAF-r,0);setTimeout(e,n)},requestAnimationFrame:function(e){"undefined"==typeof window?Browser.fakeRequestAnimationFrame(e):(window.requestAnimationFrame||(window.requestAnimationFrame=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame||Browser.fakeRequestAnimationFrame),window.requestAnimationFrame(e))},safeCallback:function(e){return function(){if(!ABORT)return e.apply(null,arguments)}},allowAsyncCallbacks:!0,queuedAsyncCallbacks:[],pauseAsyncCallbacks:function(){Browser.allowAsyncCallbacks=!1},resumeAsyncCallbacks:function(){if(Browser.allowAsyncCallbacks=!0,0<Browser.queuedAsyncCallbacks.length){var e=Browser.queuedAsyncCallbacks;Browser.queuedAsyncCallbacks=[],e.forEach(function(e){e()})}},safeRequestAnimationFrame:function(e){return Browser.requestAnimationFrame(function(){ABORT||(Browser.allowAsyncCallbacks?e():Browser.queuedAsyncCallbacks.push(e))})},safeSetTimeout:function(e,r){return Module.noExitRuntime=!0,setTimeout(function(){ABORT||(Browser.allowAsyncCallbacks?e():Browser.queuedAsyncCallbacks.push(e))},r)},safeSetInterval:function(e,r){return Module.noExitRuntime=!0,setInterval(function(){ABORT||Browser.allowAsyncCallbacks&&e()},r)},getMimetype:function(e){return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",bmp:"image/bmp",ogg:"audio/ogg",wav:"audio/wav",mp3:"audio/mpeg"}[e.substr(e.lastIndexOf(".")+1)]},getUserMedia:function(e){window.getUserMedia||(window.getUserMedia=navigator.getUserMedia||navigator.mozGetUserMedia),window.getUserMedia(e)},getMovementX:function(e){return e.movementX||e.mozMovementX||e.webkitMovementX||0},getMovementY:function(e){return e.movementY||e.mozMovementY||e.webkitMovementY||0},getMouseWheelDelta:function(e){var r=0;switch(e.type){case"DOMMouseScroll":r=e.detail;break;case"mousewheel":r=e.wheelDelta;break;case"wheel":r=e.deltaY;break;default:throw"unrecognized mouse wheel event: "+e.type}return r},mouseX:0,mouseY:0,mouseMovementX:0,mouseMovementY:0,touches:{},lastTouches:{},calculateMouseEvent:function(e){if(Browser.pointerLock)"mousemove"!=e.type&&"mozMovementX"in e?Browser.mouseMovementX=Browser.mouseMovementY=0:(Browser.mouseMovementX=Browser.getMovementX(e),Browser.mouseMovementY=Browser.getMovementY(e)),"undefined"!=typeof SDL?(Browser.mouseX=SDL.mouseX+Browser.mouseMovementX,Browser.mouseY=SDL.mouseY+Browser.mouseMovementY):(Browser.mouseX+=Browser.mouseMovementX,Browser.mouseY+=Browser.mouseMovementY);else{var r=Module.canvas.getBoundingClientRect(),n=Module.canvas.width,t=Module.canvas.height,i=void 0!==window.scrollX?window.scrollX:window.pageXOffset,o=void 0!==window.scrollY?window.scrollY:window.pageYOffset;if("touchstart"===e.type||"touchend"===e.type||"touchmove"===e.type){var a=e.touch;if(void 0===a)return;var s=a.pageX-(i+r.left),l=a.pageY-(o+r.top),f={x:s*=n/r.width,y:l*=t/r.height};if("touchstart"===e.type)Browser.lastTouches[a.identifier]=f,Browser.touches[a.identifier]=f;else if("touchend"===e.type||"touchmove"===e.type){var u=Browser.touches[a.identifier];u||(u=f),Browser.lastTouches[a.identifier]=u,Browser.touches[a.identifier]=f}return}var c=e.pageX-(i+r.left),d=e.pageY-(o+r.top);c*=n/r.width,d*=t/r.height,Browser.mouseMovementX=c-Browser.mouseX,Browser.mouseMovementY=d-Browser.mouseY,Browser.mouseX=c,Browser.mouseY=d}},xhrLoad:function(e,r,n){var t=new XMLHttpRequest;t.open("GET",e,!0),t.responseType="arraybuffer",t.onload=function(){200==t.status||0==t.status&&t.response?r(t.response):n()},t.onerror=n,t.send(null)},asyncLoad:function(r,n,t,i){Browser.xhrLoad(r,function(e){assert(e,'Loading data file "'+r+'" failed (no arrayBuffer).'),n(new Uint8Array(e)),i||removeRunDependency("al "+r)},function(e){if(!t)throw'Loading data file "'+r+'" failed.';t()}),i||addRunDependency("al "+r)},resizeListeners:[],updateResizeListeners:function(){var r=Module.canvas;Browser.resizeListeners.forEach(function(e){e(r.width,r.height)})},setCanvasSize:function(e,r,n){var t=Module.canvas;Browser.updateCanvasDimensions(t,e,r),n||Browser.updateResizeListeners()},windowedWidth:0,windowedHeight:0,setFullScreenCanvasSize:function(){if("undefined"!=typeof SDL){var e=HEAPU32[SDL.screen+0*Runtime.QUANTUM_SIZE>>2];e|=8388608,HEAP32[SDL.screen+0*Runtime.QUANTUM_SIZE>>2]=e}Browser.updateResizeListeners()},setWindowedCanvasSize:function(){if("undefined"!=typeof SDL){var e=HEAPU32[SDL.screen+0*Runtime.QUANTUM_SIZE>>2];e&=-8388609,HEAP32[SDL.screen+0*Runtime.QUANTUM_SIZE>>2]=e}Browser.updateResizeListeners()},updateCanvasDimensions:function(e,r,n){r&&n?(e.widthNative=r,e.heightNative=n):(r=e.widthNative,n=e.heightNative);var t=r,i=n;if(Module.forcedAspectRatio&&0<Module.forcedAspectRatio&&(t/i<Module.forcedAspectRatio?t=Math.round(i*Module.forcedAspectRatio):i=Math.round(t/Module.forcedAspectRatio)),(document.webkitFullScreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.mozFullscreenElement||document.fullScreenElement||document.fullscreenElement||document.msFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement)===e.parentNode&&"undefined"!=typeof screen){var o=Math.min(screen.width/t,screen.height/i);t=Math.round(t*o),i=Math.round(i*o)}Browser.resizeCanvas?(e.width!=t&&(e.width=t),e.height!=i&&(e.height=i),void 0!==e.style&&(e.style.removeProperty("width"),e.style.removeProperty("height"))):(e.width!=r&&(e.width=r),e.height!=n&&(e.height=n),void 0!==e.style&&(t!=r||i!=n?(e.style.setProperty("width",t+"px","important"),e.style.setProperty("height",i+"px","important")):(e.style.removeProperty("width"),e.style.removeProperty("height"))))},wgetRequests:{},nextWgetRequestHandle:0,getNextWgetRequestHandle:function(){var e=Browser.nextWgetRequestHandle;return Browser.nextWgetRequestHandle++,e}};function _time(e){var r=Date.now()/1e3|0;return e&&(HEAP32[e>>2]=r),r}function _pthread_self(){return 0}Module.requestFullScreen=function(e,r,n){Browser.requestFullScreen(e,r,n)},Module.requestAnimationFrame=function(e){Browser.requestAnimationFrame(e)},Module.setCanvasSize=function(e,r,n){Browser.setCanvasSize(e,r,n)},Module.pauseMainLoop=function(){Browser.mainLoop.pause()},Module.resumeMainLoop=function(){Browser.mainLoop.resume()},Module.getUserMedia=function(){Browser.getUserMedia()},Module.createContext=function(e,r,n,t){return Browser.createContext(e,r,n,t)},FS.staticInit(),__ATINIT__.unshift(function(){Module.noFSInit||FS.init.initialized||FS.init()}),__ATMAIN__.push(function(){FS.ignorePermissions=!1}),__ATEXIT__.push(function(){FS.quit()}),Module.FS_createFolder=FS.createFolder,Module.FS_createPath=FS.createPath,Module.FS_createDataFile=FS.createDataFile,Module.FS_createPreloadedFile=FS.createPreloadedFile,Module.FS_createLazyFile=FS.createLazyFile,Module.FS_createLink=FS.createLink,Module.FS_createDevice=FS.createDevice,Module.FS_unlink=FS.unlink,__ATINIT__.unshift(function(){TTY.init()}),__ATEXIT__.push(function(){TTY.shutdown()}),STACK_BASE=STACKTOP=Runtime.alignMemory(STATICTOP),staticSealed=!0,STACK_MAX=STACK_BASE+TOTAL_STACK,DYNAMIC_BASE=DYNAMICTOP=Runtime.alignMemory(STACK_MAX),assert(DYNAMIC_BASE<TOTAL_MEMORY,"TOTAL_MEMORY not big enough for stack"),Module.asmGlobalArg={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array,NaN:NaN,Infinity:1/0},Module.asmLibraryArg={abort:abort,assert:assert,_sysconf:_sysconf,_pthread_self:_pthread_self,_abort:_abort,___setErrNo:___setErrNo,_sbrk:_sbrk,_time:_time,_emscripten_set_main_loop_timing:_emscripten_set_main_loop_timing,_emscripten_memcpy_big:_emscripten_memcpy_big,_emscripten_set_main_loop:_emscripten_set_main_loop,STACKTOP:STACKTOP,STACK_MAX:STACK_MAX,tempDoublePtr:tempDoublePtr,ABORT:ABORT};var asm=function(e,r,n){"use asm";var Ke=new e.Int8Array(n);var Ve=new e.Int16Array(n);var We=new e.Int32Array(n);var l=new e.Uint8Array(n);var Xe=new e.Uint16Array(n);var t=new e.Uint32Array(n);var i=new e.Float32Array(n);var o=new e.Float64Array(n);var Ge=r.STACKTOP|0;var a=r.STACK_MAX|0;var s=r.tempDoublePtr|0;var f=r.ABORT|0;var u=0;var c=0;var d=0;var h=0;var w=e.NaN,m=e.Infinity;var E=0,p=0,S=0,b=0,v=0.0,_=0,k=0,F=0,M=0.0;var g=0;var R=0;var A=0;var y=0;var O=0;var T=0;var D=0;var N=0;var P=0;var C=0;var I=e.Math.floor;var B=e.Math.abs;var L=e.Math.sqrt;var U=e.Math.pow;var x=e.Math.cos;var H=e.Math.sin;var z=e.Math.tan;var Y=e.Math.acos;var j=e.Math.asin;var q=e.Math.atan;var K=e.Math.atan2;var V=e.Math.exp;var W=e.Math.log;var X=e.Math.ceil;var Ze=e.Math.imul;var G=e.Math.min;var Z=e.Math.clz32;var J=r.abort;var Q=r.assert;var $=r._sysconf;var ee=r._pthread_self;var re=r._abort;var ne=r.___setErrNo;var te=r._sbrk;var ie=r._time;var oe=r._emscripten_set_main_loop_timing;var ae=r._emscripten_memcpy_big;var se=r._emscripten_set_main_loop;var le=0.0;function fe(e){e=e|0;var r=0;r=Ge;Ge=Ge+e|0;Ge=Ge+15&-16;return r|0}function ue(){return Ge|0}function ce(e){e=e|0;Ge=e}function de(e,r){e=e|0;r=r|0;Ge=e;a=r}function he(e,r){e=e|0;r=r|0;if(!u){u=e;c=r}}function we(e){e=e|0;Ke[s>>0]=Ke[e>>0];Ke[s+1>>0]=Ke[e+1>>0];Ke[s+2>>0]=Ke[e+2>>0];Ke[s+3>>0]=Ke[e+3>>0]}function me(e){e=e|0;Ke[s>>0]=Ke[e>>0];Ke[s+1>>0]=Ke[e+1>>0];Ke[s+2>>0]=Ke[e+2>>0];Ke[s+3>>0]=Ke[e+3>>0];Ke[s+4>>0]=Ke[e+4>>0];Ke[s+5>>0]=Ke[e+5>>0];Ke[s+6>>0]=Ke[e+6>>0];Ke[s+7>>0]=Ke[e+7>>0]}function Ee(e){e=e|0;g=e}function pe(){return g|0}function Se(){var e=0,r=0;r=Ge;Ge=Ge+16|0;e=r;We[e>>2]=0;Nr(e,31756)|0;Ge=r;return We[e>>2]|0}function be(e){e=e|0;var r=0,n=0;r=Ge;Ge=Ge+16|0;n=r;We[n>>2]=e;Pr(n);Ge=r;return}function ve(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;Re(e,(t|0)==0?(l[r>>0]|0)>>>3&15:15,r+1|0,n,2)|0;return}function _e(e){e=e|0;var r=0;r=xi(8)|0;Br(r,r+4|0,e)|0;return r|0}function ke(e){e=e|0;Lr(e,e+4|0);Hi(e);return}function Fe(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0;i=Ge;Ge=Ge+16|0;o=i;We[o>>2]=r;n=(Ur(We[e>>2]|0,We[e+4>>2]|0,r,n,t,o,3)|0)<<16>>16;Ke[t>>0]=l[t>>0]|0|4;Ge=i;return n|0}function Me(e){e=e|0;if(!e)e=-1;else{Ve[e>>1]=4096;e=0}return e|0}function ge(e,r,n,t,i,o){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;var a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0;u=We[o>>2]|0;w=i<<16>>16>0;if(w){a=0;s=0;do{f=Ve[n+(a<<1)>>1]|0;f=Ze(f,f)|0;if((f|0)!=1073741824){l=(f<<1)+s|0;if((f^s|0)>0&(l^s|0)<0){We[o>>2]=1;s=(s>>>31)+2147483647|0}else s=l}else{We[o>>2]=1;s=2147483647}a=a+1|0}while((a&65535)<<16>>16!=i<<16>>16);if((s|0)==2147483647){We[o>>2]=u;f=0;l=0;do{s=Ve[n+(f<<1)>>1]>>2;s=Ze(s,s)|0;if((s|0)!=1073741824){a=(s<<1)+l|0;if((s^l|0)>0&(a^l|0)<0){We[o>>2]=1;l=(l>>>31)+2147483647|0}else l=a}else{We[o>>2]=1;l=2147483647}f=f+1|0}while((f&65535)<<16>>16!=i<<16>>16)}else h=8}else{s=0;h=8}if((h|0)==8)l=s>>4;if(!l){Ve[e>>1]=0;return}d=((bi(l)|0)&65535)+65535|0;s=d<<16>>16;if((d&65535)<<16>>16>0){a=l<<s;if((a>>s|0)==(l|0))l=a;else l=l>>31^2147483647}else{s=0-s<<16;if((s|0)<2031616)l=l>>(s>>16);else l=0}c=Ni(l,o)|0;a=We[o>>2]|0;if(w){s=0;l=0;do{u=Ve[r+(s<<1)>>1]|0;u=Ze(u,u)|0;if((u|0)!=1073741824){f=(u<<1)+l|0;if((u^l|0)>0&(f^l|0)<0){We[o>>2]=1;l=(l>>>31)+2147483647|0}else l=f}else{We[o>>2]=1;l=2147483647}s=s+1|0}while((s&65535)<<16>>16!=i<<16>>16);if((l|0)==2147483647){We[o>>2]=a;u=0;l=0;do{f=Ve[r+(u<<1)>>1]>>2;f=Ze(f,f)|0;if((f|0)!=1073741824){s=(f<<1)+l|0;if((f^l|0)>0&(s^l|0)<0){We[o>>2]=1;l=(l>>>31)+2147483647|0}else l=s}else{We[o>>2]=1;l=2147483647}u=u+1|0}while((u&65535)<<16>>16!=i<<16>>16)}else h=29}else{l=0;h=29}if((h|0)==29)l=l>>4;if(!l)f=0;else{s=(bi(l)|0)<<16>>16;a=d-s|0;f=a&65535;l=(Gt(c,Ni(l<<s,o)|0)|0)<<16>>16;s=l<<7;a=a<<16>>16;if(f<<16>>16>0)a=f<<16>>16<31?s>>a:0;else{h=0-a<<16>>16;a=s<<h;a=(a>>h|0)==(s|0)?a:l>>24^2147483647}f=(Ze(((ai(a,o)|0)<<9)+32768>>16,32767-(t&65535)<<16>>16)|0)>>>15<<16>>16}a=Ve[e>>1]|0;if(w){l=t<<16>>16;s=0;while(1){t=((Ze(a<<16>>16,l)|0)>>>15&65535)+f|0;a=t&65535;Ve[n>>1]=(Ze(Ve[n>>1]|0,t<<16>>16)|0)>>>12;s=s+1<<16>>16;if(s<<16>>16>=i<<16>>16)break;else n=n+2|0}}Ve[e>>1]=a;return}function Je(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0;a=We[t>>2]|0;i=n<<16>>16>0;if(i){s=0;o=0;do{f=Ve[r+(s<<1)>>1]|0;f=Ze(f,f)|0;if((f|0)!=1073741824){l=(f<<1)+o|0;if((f^o|0)>0&(l^o|0)<0){We[t>>2]=1;o=(o>>>31)+2147483647|0}else o=l}else{We[t>>2]=1;o=2147483647}s=s+1|0}while((s&65535)<<16>>16!=n<<16>>16);if((o|0)==2147483647){We[t>>2]=a;f=0;a=0;do{l=Ve[r+(f<<1)>>1]>>2;l=Ze(l,l)|0;if((l|0)!=1073741824){s=(l<<1)+a|0;if((l^a|0)>0&(s^a|0)<0){We[t>>2]=1;a=(a>>>31)+2147483647|0}else a=s}else{We[t>>2]=1;a=2147483647}f=f+1|0}while((f&65535)<<16>>16!=n<<16>>16)}else d=8}else{o=0;d=8}if((d|0)==8)a=o>>4;if(!a)return;c=((bi(a)|0)&65535)+65535|0;l=c<<16>>16;if((c&65535)<<16>>16>0){s=a<<l;if((s>>l|0)==(a|0))a=s;else a=a>>31^2147483647}else{l=0-l<<16;if((l|0)<2031616)a=a>>(l>>16);else a=0}u=Ni(a,t)|0;a=We[t>>2]|0;if(i){s=0;o=0;do{f=Ve[e+(s<<1)>>1]|0;f=Ze(f,f)|0;if((f|0)!=1073741824){l=(f<<1)+o|0;if((f^o|0)>0&(l^o|0)<0){We[t>>2]=1;o=(o>>>31)+2147483647|0}else o=l}else{We[t>>2]=1;o=2147483647}s=s+1|0}while((s&65535)<<16>>16!=n<<16>>16);if((o|0)==2147483647){We[t>>2]=a;a=0;s=0;do{f=Ve[e+(a<<1)>>1]>>2;f=Ze(f,f)|0;if((f|0)!=1073741824){l=(f<<1)+s|0;if((f^s|0)>0&(l^s|0)<0){We[t>>2]=1;s=(s>>>31)+2147483647|0}else s=l}else{We[t>>2]=1;s=2147483647}a=a+1|0}while((a&65535)<<16>>16!=n<<16>>16)}else d=28}else{o=0;d=28}if((d|0)==28)s=o>>4;if(!s)i=0;else{f=bi(s)|0;l=f<<16>>16;if(f<<16>>16>0){a=s<<l;if((a>>l|0)==(s|0))s=a;else s=s>>31^2147483647}else{l=0-l<<16;if((l|0)<2031616)s=s>>(l>>16);else s=0}a=c-(f&65535)|0;l=a&65535;o=(Gt(u,Ni(s,t)|0)|0)<<16>>16;i=o<<7;a=a<<16>>16;if(l<<16>>16>0)i=l<<16>>16<31?i>>a:0;else{c=0-a<<16>>16;e=i<<c;i=(e>>c|0)==(i|0)?e:o>>24^2147483647}i=ai(i,t)|0;if((i|0)>4194303)i=2147483647;else i=(i|0)<-4194304?-2147483648:i<<9;i=Ni(i,t)|0}o=(n&65535)+65535&65535;if(o<<16>>16<=-1)return;f=i<<16>>16;l=n+-1<<16>>16<<16>>16;while(1){a=r+(l<<1)|0;i=Ze(Ve[a>>1]|0,f)|0;do{if((i|0)!=1073741824){s=i<<1;if((s|0)<=268435455)if((s|0)<-268435456){Ve[a>>1]=-32768;break}else{Ve[a>>1]=i>>>12;break}else d=52}else{We[t>>2]=1;d=52}}while(0);if((d|0)==52){d=0;Ve[a>>1]=32767}o=o+-1<<16>>16;if(o<<16>>16<=-1)break;else l=l+-1|0}return}function Re(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0,a=0,s=0,l=0;l=Ge;Ge=Ge+496|0;s=l;a=(i|0)==2;do{if(!(a&1|(i|0)==4)){if(i){e=-1;Ge=l;return e|0}a=Ve[n>>1]|0;r=n+490|0;i=n+2|0;o=0;while(1){Ve[s+(o<<1)>>1]=Ve[i>>1]|0;o=o+1|0;if((o|0)==244)break;else i=i+2|0}o=a<<16>>16;if(a<<16>>16==7){i=492;r=We[e+1760>>2]|0;break}else{i=492;r=Ve[r>>1]|0;break}}else{o=e+1168|0;if(a){Ir(r,n,s,o);o=604}else{He(r,n,s,o);o=3436}i=Ve[o+(r<<1)>>1]|0;do{if(r>>>0>=8){if((r|0)==8){r=Ve[s+76>>1]<<2|(Ve[s+74>>1]<<1|Ve[s+72>>1]);o=(Ve[s+70>>1]|0)==0?4:5;break}if(r>>>0<15){e=-1;Ge=l;return e|0}else{r=We[e+1760>>2]|0;o=7;break}}else o=0}while(0);if(i<<16>>16==-1){e=-1;Ge=l;return e|0}}}while(0);Cr(e,r,s,o,t);We[e+1760>>2]=r;e=i;Ge=l;return e|0}function Ae(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0;m=Ge;Ge=Ge+48|0;h=m+20|0;w=m;i=h;t=i+20|0;do{Ve[i>>1]=Ve[e>>1]|0;i=i+2|0;e=e+2|0}while((i|0)<(t|0));e=Ve[h+18>>1]|0;d=(e&65535)-((e&65535)>>>15&65535)|0;e:do{if(((d<<16>>31^d)&65535)<<16>>16<=4095){t=9;d=9;while(1){e=e<<16>>16;e=(e<<19>>19|0)==(e|0)?e<<3:e>>>15^32767;c=r+(t<<1)|0;Ve[c>>1]=e;e=e<<16>>16;e=Ze(e,e)|0;if((e|0)==1073741824){We[n>>2]=1;i=2147483647}else i=e<<1;e=2147483647-i|0;if((e&i|0)<0){We[n>>2]=1;e=2147483647}f=bi(e)|0;u=15-(f&65535)&65535;o=f<<16>>16;if(f<<16>>16>0){i=e<<o;if((i>>o|0)!=(e|0))i=e>>31^2147483647}else{i=0-o<<16;if((i|0)<2031616)i=e>>(i>>16);else i=0}i=Gt(16384,Ni(i,n)|0)|0;do{if(d<<16>>16>0){f=t+-1|0;a=i<<16>>16;s=d<<16>>16;l=0;while(1){t=Xe[h+(l<<1)>>1]|0;e=t<<16;o=Ze(Ve[h+(f-l<<1)>>1]|0,Ve[c>>1]|0)|0;if((o|0)==1073741824){We[n>>2]=1;i=2147483647}else i=o<<1;o=e-i|0;if(((o^e)&(i^e)|0)<0){We[n>>2]=1;o=(t>>>15)+2147483647|0}o=Ze((Ni(o,n)|0)<<16>>16,a)|0;if((o|0)==1073741824){We[n>>2]=1;o=2147483647}else o=o<<1;o=ui(o,u,n)|0;i=o-(o>>>31)|0;if((i>>31^i|0)>32767){o=24;break}Ve[w+(l<<1)>>1]=o;l=l+1|0;if((s|0)<=(l|0)){o=26;break}}if((o|0)==24){o=0;i=r;t=i+20|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(t|0));e=10}else if((o|0)==26){o=0;if(d<<16>>16>0)e=d;else{o=28;break}}i=e+-1<<16>>16;qi(h|0,w|0,((i&65535)<<1)+2|0)|0;t=i<<16>>16}else o=28}while(0);if((o|0)==28){e=d+-1<<16>>16;if(e<<16>>16>-1){t=e<<16>>16;i=32767}else break}e=Ve[h+(t<<1)>>1]|0;d=(e&65535)-((e&65535)>>>15&65535)|0;if(((d<<16>>31^d)&65535)<<16>>16>4095)break e;else d=i}Ge=m;return}}while(0);i=r;t=i+20|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(t|0));Ge=m;return}function ye(e,r){e=e|0;r=r|0;var n=0,t=0,i=0,o=0,a=0;if(r<<16>>16<=0){e=0;return e|0}t=We[e>>2]|0;i=0;n=0;do{a=t&1;n=a|n<<1&131070;o=t>>1;t=(a|0)==(t>>>28&1|0)?o:o|1073741824;i=i+1<<16>>16}while(i<<16>>16<r<<16>>16);We[e>>2]=t;a=n&65535;return a|0}function Oe(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0,a=0,s=0,l=0;i=r;t=i+80|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(t|0));t=0;i=We[e>>2]|0;do{l=i&1;s=i>>1;s=(l|0)==(i>>>28&1|0)?s:s|1073741824;o=s&1;a=s>>1;We[e>>2]=(o|0)==(s>>>28&1|0)?a:a|1073741824;o=Wt((Ze(l<<1|o,1310720)|0)>>>17&65535,t,n)|0;l=We[e>>2]|0;a=l&1;s=l>>1;i=(a|0)==(l>>>28&1|0)?s:s|1073741824;We[e>>2]=i;Ve[r+(o<<16>>16<<1)>>1]=((a&65535)<<13&65535)+-4096<<16>>16;t=t+1<<16>>16}while(t<<16>>16<10);return}function Qe(e,r,n,t,i,o){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;var a=0,s=0;a=Ve[e>>1]|0;if((a*31821|0)==1073741824){We[o>>2]=1;s=1073741823}else s=a*63642>>1;a=s+13849|0;if((s|0)>-1&(a^s|0)<0){We[o>>2]=1;a=(s>>>31)+2147483647|0}Ve[e>>1]=a;if(r<<16>>16<=0)return;s=0;a=i+((a&127)<<1)|0;while(1){Ve[t+(s<<1)>>1]=(-65536<<Ve[n+(s<<1)>>1]>>>16^65535)&Xe[a>>1];s=s+1|0;if((s&65535)<<16>>16==r<<16>>16)break;else a=a+2|0}return}function Te(e){e=e|0;var r=0;if(!e){r=-1;return r|0}r=e+122|0;do{Ve[e>>1]=0;e=e+2|0}while((e|0)<(r|0));r=0;return r|0}function $e(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0,a=0,s=0,l=0,f=0,u=0,c=0;s=159;a=0;while(1){f=Ve[n+(s<<1)>>1]|0;f=Ze(f,f)|0;f=(f|0)==1073741824?2147483647:f<<1;o=f+a|0;if((f^a|0)>-1&(o^a|0)<0){We[i>>2]=1;a=(a>>>31)+2147483647|0}else a=o;if((s|0)>0)s=s+-1|0;else{s=a;break}}i=s>>>14&65535;a=32767;o=59;while(1){f=Ve[e+(o<<1)>>1]|0;a=f<<16>>16<a<<16>>16?f:a;if((o|0)>0)o=o+-1|0;else break}f=(s|0)>536870911?32767:i;i=a<<16>>16;o=i<<20>>16;s=a<<16>>16>0?32767:-32768;n=55;a=Ve[e>>1]|0;while(1){l=Ve[e+(n<<1)>>1]|0;a=a<<16>>16<l<<16>>16?l:a;if((n|0)>1)n=n+-1|0;else break}n=Ve[e+80>>1]|0;l=Ve[e+82>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+84>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+86>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+88>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+90>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+92>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+94>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+96>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+98>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+100>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+102>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+104>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+106>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+108>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+110>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+112>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+114>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=Ve[e+116>>1]|0;n=n<<16>>16<l<<16>>16?l:n;l=e+118|0;c=Ve[l>>1]|0;do{if((f+-21&65535)<17557&a<<16>>16>20?(f<<16>>16|0)<(((i<<4|0)==(o|0)?o:s)|0)?1:(n<<16>>16<c<<16>>16?c:n)<<16>>16<1953:0){a=e+120|0;o=Ve[a>>1]|0;if(o<<16>>16>29){Ve[a>>1]=30;n=a;s=1;break}else{s=(o&65535)+1&65535;Ve[a>>1]=s;n=a;s=s<<16>>16>1&1;break}}else u=14}while(0);if((u|0)==14){n=e+120|0;Ve[n>>1]=0;s=0}a=0;do{c=a;a=a+1|0;Ve[e+(c<<1)>>1]=Ve[e+(a<<1)>>1]|0}while((a|0)!=59);Ve[l>>1]=f;a=Ve[n>>1]|0;a=a<<16>>16>15?16383:a<<16>>16>8?15565:13926;o=ri(r+8|0,5)|0;if((Ve[n>>1]|0)>20){if(((ri(r,9)|0)<<16>>16|0)>(a|0))u=20}else if((o<<16>>16|0)>(a|0))u=20;if((u|0)==20){Ve[t>>1]=0;return s|0}o=(Xe[t>>1]|0)+1&65535;if(o<<16>>16>10){Ve[t>>1]=10;return s|0}else{Ve[t>>1]=o;return s|0}return 0}function De(e){e=e|0;var r=0;if(!e){r=-1;return r|0}r=e+18|0;do{Ve[e>>1]=0;e=e+2|0}while((e|0)<(r|0));r=0;return r|0}function er(e,r,n,t,i,o,a,s,l,f,u,c){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;u=u|0;c=c|0;var d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0;_=e+2|0;Ve[e>>1]=Ve[_>>1]|0;k=e+4|0;Ve[_>>1]=Ve[k>>1]|0;F=e+6|0;Ve[k>>1]=Ve[F>>1]|0;M=e+8|0;Ve[F>>1]=Ve[M>>1]|0;g=e+10|0;Ve[M>>1]=Ve[g>>1]|0;R=e+12|0;Ve[g>>1]=Ve[R>>1]|0;Ve[R>>1]=n;E=0;v=0;do{d=i+(v<<1)|0;w=Bi(Ve[d>>1]|0,Ve[t+(v<<1)>>1]|0,c)|0;w=(w&65535)-((w&65535)>>>15&65535)|0;w=w<<16>>31^w;b=((vi(w&65535)|0)&65535)+65535|0;h=b<<16>>16;if((b&65535)<<16>>16<0){m=0-h<<16;if((m|0)<983040)p=w<<16>>16>>(m>>16)&65535;else p=0}else{m=w<<16>>16;w=m<<h;if((w<<16>>16>>h|0)==(m|0))p=w&65535;else p=(m>>>15^32767)&65535}S=vi(Ve[d>>1]|0)|0;w=Ve[d>>1]|0;h=S<<16>>16;if(S<<16>>16<0){m=0-h<<16;if((m|0)<983040)m=w<<16>>16>>(m>>16)&65535;else m=0}else{m=w<<16>>16;w=m<<h;if((w<<16>>16>>h|0)==(m|0))m=w&65535;else m=(m>>>15^32767)&65535}h=Gt(p,m)|0;m=(b&65535)+2-(S&65535)|0;w=m&65535;do{if(m&32768){if(w<<16>>16!=-32768){b=0-m|0;m=b<<16>>16;if((b&65535)<<16>>16<0){m=0-m<<16;if((m|0)>=983040){m=0;break}m=h<<16>>16>>(m>>16)&65535;break}}else m=32767;w=h<<16>>16;h=w<<m;if((h<<16>>16>>m|0)==(w|0))m=h&65535;else m=(w>>>15^32767)&65535}else m=Pi(h,w,c)|0}while(0);E=Wt(E,m,c)|0;v=v+1|0}while((v|0)!=10);m=E&65535;w=E<<16>>16>5325;E=e+14|0;if(w){i=(Xe[E>>1]|0)+1&65535;Ve[E>>1]=i;if(i<<16>>16>10)Ve[e+16>>1]=0}else Ve[E>>1]=0;switch(r|0){case 0:case 1:case 2:case 3:case 6:break;default:{R=e+16|0;c=n;n=Ve[R>>1]|0;n=n&65535;n=n+1|0;n=n&65535;Ve[R>>1]=n;return c|0}}p=(a|o)<<16>>16==0;S=f<<16>>16==0;b=r>>>0<3;E=m+(b&((S|(p&(s<<16>>16==0|l<<16>>16==0)|u<<16>>16<2))^1)?61030:62259)&65535;E=E<<16>>16>0?E:0;if(E<<16>>16<=2048){E=E<<16>>16;if((E<<18>>18|0)==(E|0))l=E<<2;else l=E>>>15^32767}else l=8192;s=e+16|0;u=w|(Ve[s>>1]|0)<40;E=Ve[k>>1]|0;if((E*6554|0)==1073741824){We[c>>2]=1;w=2147483647}else w=E*13108|0;E=Ve[F>>1]|0;m=E*6554|0;if((m|0)!=1073741824){E=(E*13108|0)+w|0;if((m^w|0)>0&(E^w|0)<0){We[c>>2]=1;E=(w>>>31)+2147483647|0}}else{We[c>>2]=1;E=2147483647}m=Ve[M>>1]|0;w=m*6554|0;if((w|0)!=1073741824){m=(m*13108|0)+E|0;if((w^E|0)>0&(m^E|0)<0){We[c>>2]=1;m=(E>>>31)+2147483647|0}}else{We[c>>2]=1;m=2147483647}E=Ve[g>>1]|0;w=E*6554|0;if((w|0)!=1073741824){E=(E*13108|0)+m|0;if((w^m|0)>0&(E^m|0)<0){We[c>>2]=1;w=(m>>>31)+2147483647|0}else w=E}else{We[c>>2]=1;w=2147483647}E=Ve[R>>1]|0;m=E*6554|0;if((m|0)!=1073741824){E=(E*13108|0)+w|0;if((m^w|0)>0&(E^w|0)<0){We[c>>2]=1;E=(w>>>31)+2147483647|0}}else{We[c>>2]=1;E=2147483647}w=Ni(E,c)|0;if(b&((p|S)^1)){E=Ve[e>>1]|0;if((E*4681|0)==1073741824){We[c>>2]=1;w=2147483647}else w=E*9362|0;E=Ve[_>>1]|0;m=E*4681|0;if((m|0)!=1073741824){E=(E*9362|0)+w|0;if((m^w|0)>0&(E^w|0)<0){We[c>>2]=1;w=(w>>>31)+2147483647|0}else w=E}else{We[c>>2]=1;w=2147483647}E=Ve[k>>1]|0;m=E*4681|0;if((m|0)!=1073741824){E=(E*9362|0)+w|0;if((m^w|0)>0&(E^w|0)<0){We[c>>2]=1;w=(w>>>31)+2147483647|0}else w=E}else{We[c>>2]=1;w=2147483647}E=Ve[F>>1]|0;m=E*4681|0;if((m|0)!=1073741824){E=(E*9362|0)+w|0;if((m^w|0)>0&(E^w|0)<0){We[c>>2]=1;E=(w>>>31)+2147483647|0}}else{We[c>>2]=1;E=2147483647}m=Ve[M>>1]|0;w=m*4681|0;if((w|0)!=1073741824){m=(m*9362|0)+E|0;if((w^E|0)>0&(m^E|0)<0){We[c>>2]=1;E=(E>>>31)+2147483647|0}else E=m}else{We[c>>2]=1;E=2147483647}m=Ve[g>>1]|0;w=m*4681|0;if((w|0)!=1073741824){m=(m*9362|0)+E|0;if((w^E|0)>0&(m^E|0)<0){We[c>>2]=1;m=(E>>>31)+2147483647|0}}else{We[c>>2]=1;m=2147483647}w=Ve[R>>1]|0;d=w*4681|0;if((d|0)!=1073741824){h=(w*9362|0)+m|0;if((d^m|0)>0&(h^m|0)<0){We[c>>2]=1;h=(m>>>31)+2147483647|0}}else{We[c>>2]=1;h=2147483647}w=Ni(h,c)|0}E=u?8192:l<<16>>16;d=Ze(E,n<<16>>16)|0;if((d|0)==1073741824){We[c>>2]=1;m=2147483647}else m=d<<1;w=w<<16>>16;h=w<<13;if((h|0)!=1073741824){d=m+(w<<14)|0;if((m^h|0)>0&(d^m|0)<0){We[c>>2]=1;m=(m>>>31)+2147483647|0}else m=d}else{We[c>>2]=1;m=2147483647}d=Ze(w,E)|0;if((d|0)==1073741824){We[c>>2]=1;h=2147483647}else h=d<<1;d=m-h|0;if(((d^m)&(h^m)|0)<0){We[c>>2]=1;d=(m>>>31)+2147483647|0}R=d<<2;n=s;c=Ni((R>>2|0)==(d|0)?R:d>>31^2147483647,c)|0;R=Ve[n>>1]|0;R=R&65535;R=R+1|0;R=R&65535;Ve[n>>1]=R;return c|0}function rr(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0,a=0;t=r;i=t+80|0;do{Ve[t>>1]=0;t=t+2|0}while((t|0)<(i|0));t=0;do{a=Ve[e+(t<<1)>>1]|0;i=((a&8)<<10&65535^8192)+-4096<<16>>16;o=t<<16;a=((Ve[n+((a&7)<<1)>>1]|0)*327680|0)+o>>16;Ve[r+(a<<1)>>1]=i;o=((Ve[n+((Xe[e+(t+5<<1)>>1]&7)<<1)>>1]|0)*327680|0)+o>>16;if((o|0)<(a|0))i=0-(i&65535)&65535;a=r+(o<<1)|0;Ve[a>>1]=(Xe[a>>1]|0)+(i&65535);t=t+1|0}while((t|0)!=5);return}function nr(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0;i=r<<16>>16;t=(i<<1&2|1)+((i>>>1&7)*5|0)|0;r=i>>>4&3;r=((i>>>6&7)*5|0)+((r|0)==3?4:r)|0;i=n;o=i+80|0;do{Ve[i>>1]=0;i=i+2|0}while((i|0)<(o|0));e=e<<16>>16;Ve[n+(t<<1)>>1]=(0-(e&1)&16383)+57344;Ve[n+(r<<1)>>1]=(0-(e>>>1&1)&16383)+57344;return}function tr(e,r,n,t,i,o){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;var a=0,s=0;o=n<<16>>16;s=o>>>3;e=e<<16>>16;e=((e<<17>>17|0)==(e|0)?e<<1:e>>>15^32767)+(s&8)<<16;s=(Xe[t+(e+65536>>16<<1)>>1]|0)+((s&7)*5|0)|0;n=r<<16>>16;a=(0-(n&1)&16383)+57344&65535;e=i+((Xe[t+(e>>16<<1)>>1]|0)+((o&7)*5|0)<<16>>16<<1)|0;r=i;o=r+80|0;do{Ve[r>>1]=0;r=r+2|0}while((r|0)<(o|0));Ve[e>>1]=a;Ve[i+(s<<16>>16<<1)>>1]=(0-(n>>>1&1)&16383)+57344;return}function ir(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0,a=0;r=r<<16>>16;t=(r&7)*5|0;i=(r>>>2&2|1)+((r>>>4&7)*5|0)|0;r=(r>>>6&2)+2+((r>>>8&7)*5|0)|0;o=n;a=o+80|0;do{Ve[o>>1]=0;o=o+2|0}while((o|0)<(a|0));e=e<<16>>16;Ve[n+(t<<1)>>1]=(0-(e&1)&16383)+57344;Ve[n+(i<<1)>>1]=(0-(e>>>1&1)&16383)+57344;Ve[n+(r<<1)>>1]=(0-(e>>>2&1)&16383)+57344;return}function or(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0;r=r<<16>>16;a=Ve[n+((r&7)<<1)>>1]|0;s=Ve[n+((r>>>3&7)<<1)>>1]|0;o=Ve[n+((r>>>6&7)<<1)>>1]|0;n=(r>>>9&1)+3+((Ve[n+((r>>>10&7)<<1)>>1]|0)*5|0)|0;r=t;i=r+80|0;do{Ve[r>>1]=0;r=r+2|0}while((r|0)<(i|0));e=e<<16>>16;Ve[t+(a*327680>>16<<1)>>1]=(0-(e&1)&16383)+57344;Ve[t+((s*327680|0)+65536>>16<<1)>>1]=(0-(e>>>1&1)&16383)+57344;Ve[t+((o*327680|0)+131072>>16<<1)>>1]=(0-(e>>>2&1)&16383)+57344;Ve[t+(n<<16>>16<<1)>>1]=(0-(e>>>3&1)&16383)+57344;return}function ar(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0;d=Ge;Ge=Ge+32|0;c=d+16|0;u=d;o=r;i=o+80|0;do{Ve[o>>1]=0;o=o+2|0}while((o|0)<(i|0));i=Ve[e>>1]|0;Ve[c>>1]=i;Ve[c+2>>1]=Ve[e+2>>1]|0;Ve[c+4>>1]=Ve[e+4>>1]|0;Ve[c+6>>1]=Ve[e+6>>1]|0;l=Ve[e+8>>1]|0;Ne(l>>>3&65535,l&7,0,4,1,u,n);l=Ve[e+10>>1]|0;Ne(l>>>3&65535,l&7,2,6,5,u,n);l=Ve[e+12>>1]|0;t=l>>2;do{if((t*25|0)!=1073741824){o=(Ze(t,1638400)|0)+786432>>21;t=o*6554>>15;if((t|0)>32767){We[n>>2]=1;a=1;s=1;e=163835;f=6;break}e=(t<<16>>16)*5|0;a=t&1;if((e|0)==1073741824){We[n>>2]=1;s=0;e=65535}else{s=0;f=6}}else{We[n>>2]=1;a=0;t=0;s=0;o=0;e=0;f=6}}while(0);if((f|0)==6)e=e&65535;f=o-e|0;a=a<<16>>16==0?f:4-f|0;f=a<<16>>16;Ve[u+6>>1]=Wt(((a<<17>>17|0)==(f|0)?a<<1:f>>>15^32767)&65535,l&1,n)|0;if(s){We[n>>2]=1;t=32767}f=t<<16>>16;Ve[u+14>>1]=((t<<17>>17|0)==(f|0)?t<<1:f>>>15^32767)+(l>>>1&1);t=0;while(1){i=i<<16>>16==0?8191:-8191;f=(Ve[u+(t<<1)>>1]<<2)+t<<16;o=f>>16;if((f|0)<2621440)Ve[r+(o<<1)>>1]=i;a=(Ve[u+(t+4<<1)>>1]<<2)+t<<16;e=a>>16;if((e|0)<(o|0))i=0-(i&65535)&65535;if((a|0)<2621440){f=r+(e<<1)|0;Ve[f>>1]=(Xe[f>>1]|0)+(i&65535)}t=t+1|0;if((t|0)==4)break;i=Ve[c+(t<<1)>>1]|0}Ge=d;return}function Ne(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;var s=0,l=0,f=0,u=0,c=0,d=0,h=0;l=e<<16>>16>124?124:e;e=(l<<16>>16)*1311>>15;h=(e|0)>32767;if(!h){s=e<<16>>16;if((s*25|0)==1073741824){We[a>>2]=1;s=1073741823}else d=4}else{We[a>>2]=1;s=32767;d=4}if((d|0)==4)s=(s*50|0)>>>1;u=(l&65535)-s|0;s=(u<<16>>16)*6554>>15;c=(s|0)>32767;if(!c){l=s<<16>>16;if((l*5|0)==1073741824){We[a>>2]=1;f=1073741823}else d=9}else{We[a>>2]=1;l=32767;d=9}if((d|0)==9)f=(l*10|0)>>>1;u=u-f|0;d=u<<16>>16;l=r<<16>>16;f=l>>2;l=l-(f<<2)|0;Ve[o+(n<<16>>16<<1)>>1]=((u<<17>>17|0)==(d|0)?u<<1:d>>>15^32767)+(l&1);if(c){We[a>>2]=1;s=32767}n=s<<16>>16;Ve[o+(t<<16>>16<<1)>>1]=((s<<17>>17|0)==(n|0)?s<<1:n>>>15^32767)+(l<<16>>17);if(h){We[a>>2]=1;e=32767}t=e<<16>>16;Ve[o+(i<<16>>16<<1)>>1]=Wt(f&65535,((e<<17>>17|0)==(t|0)?e<<1:t>>>15^32767)&65535,a)|0;return}function Pe(e){e=e|0;var r=0,n=0,t=0,i=0;if(!e){i=-1;return i|0}ei(e+1168|0);Ve[e+460>>1]=40;We[e+1164>>2]=0;r=e+646|0;n=e+1216|0;t=e+462|0;i=t+22|0;do{Ve[t>>1]=0;t=t+2|0}while((t|0)<(i|0));Be(r,We[n>>2]|0)|0;xe(e+686|0)|0;Ue(e+700|0)|0;De(e+608|0)|0;ze(e+626|0,We[n>>2]|0)|0;Te(e+484|0)|0;Ye(e+730|0)|0;Le(e+748|0)|0;Zt(e+714|0)|0;sr(e,0)|0;i=0;return i|0}function sr(e,r){e=e|0;r=r|0;var n=0,t=0;if(!e){e=-1;return e|0}We[e+388>>2]=e+308;Vi(e|0,0,308)|0;r=(r|0)!=8;if(r){n=e+412|0;t=n+20|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));Ve[e+392>>1]=3e4;Ve[e+394>>1]=26e3;Ve[e+396>>1]=21e3;Ve[e+398>>1]=15e3;Ve[e+400>>1]=8e3;Ve[e+402>>1]=0;Ve[e+404>>1]=-8e3;Ve[e+406>>1]=-15e3;Ve[e+408>>1]=-21e3;Ve[e+410>>1]=-26e3}Ve[e+432>>1]=0;Ve[e+434>>1]=40;We[e+1164>>2]=0;Ve[e+436>>1]=0;Ve[e+438>>1]=0;Ve[e+440>>1]=0;Ve[e+460>>1]=40;Ve[e+462>>1]=0;Ve[e+464>>1]=0;if(r){n=e+442|0;t=n+18|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));n=e+466|0;t=n+18|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));De(e+608|0)|0;t=e+1216|0;ze(e+626|0,We[t>>2]|0)|0;Be(e+646|0,We[t>>2]|0)|0;xe(e+686|0)|0;Ue(e+700|0)|0;Zt(e+714|0)|0}else{n=e+466|0;t=n+18|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));De(e+608|0)|0;Be(e+646|0,We[e+1216>>2]|0)|0;xe(e+686|0)|0;Ue(e+700|0)|0}Te(e+484|0)|0;Ve[e+606>>1]=21845;Ye(e+730|0)|0;if(!r){e=0;return e|0}Le(e+748|0)|0;e=0;return e|0}function Ce(e,r,n,t,i,o){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;var a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0,C=0,I=0,B=0,L=0,U=0,x=0,H=0,z=0,Y=0,j=0,q=0,K=0,V=0,W=0,X=0,G=0,Z=0,J=0,Q=0,$=0,ee=0,re=0,ne=0,te=0,ie=0,oe=0,ae=0,se=0,le=0,fe=0,ue=0,ce=0,de=0,he=0,we=0,me=0,Ee=0,pe=0,Se=0,be=0,ve=0,_e=0,ke=0,Fe=0,Me=0,ge=0,Re=0,Ae=0,ye=0,Oe=0,Te=0,De=0,Ne=0,Pe=0,Ce=0,Ie=0,Be=0,Le=0,Ue=0,xe=0,He=0,ze=0,Ye=0,je=0,qe=0;qe=Ge;Ge=Ge+336|0;d=qe+236|0;c=qe+216|0;Ye=qe+112|0;ze=qe+12|0;Be=qe+256|0;Ue=qe+136|0;Le=qe+32|0;Ce=qe+8|0;Ie=qe+6|0;He=qe+4|0;xe=qe+2|0;je=qe;Oe=e+1164|0;Te=e+748|0;De=pr(Te,t,Oe)|0;if(De){sr(e,8)|0;mr(Te,e+412|0,e+646|0,e+714|0,e+608|0,De,r,n,e+1168|0,i,o,Oe);je=e+666|0;Ei(je,e+392|0,10,Oe);Mr(e+626|0,je,Oe);je=e+1156|0;We[je>>2]=De;Ge=qe;return}switch(t|0){case 1:{a=1;S=6;break}case 2:case 7:{Qe(e+606|0,Ve[(We[e+1256>>2]|0)+(r<<1)>>1]|0,We[(We[e+1260>>2]|0)+(r<<2)>>2]|0,n,We[e+1276>>2]|0,Oe);S=9;break}case 3:{S=9;break}default:{a=0;S=6}}do{if((S|0)==6){t=e+440|0;if((Ve[t>>1]|0)==6){Ve[t>>1]=5;Ae=0;ye=0;break}else{Ve[t>>1]=0;Ae=0;ye=0;break}}else if((S|0)==9){t=e+440|0;Ae=(Xe[t>>1]|0)+1&65535;Ve[t>>1]=Ae<<16>>16>6?6:Ae;Ae=1;ye=1;a=0}}while(0);Fe=e+1156|0;switch(We[Fe>>2]|0){case 1:{Ve[t>>1]=5;Ve[e+436>>1]=0;break}case 2:{Ve[t>>1]=5;Ve[e+436>>1]=1;break}default:{}}l=e+646|0;Me=e+666|0;s=Ye;f=Me;u=s+20|0;do{Ke[s>>0]=Ke[f>>0]|0;s=s+1|0;f=f+1|0}while((s|0)<(u|0));ge=(r|0)!=7;Re=e+1168|0;if(ge){hr(l,r,ye,n,Re,d,Oe);s=e+392|0;ii(s,d,o,Oe);n=n+6|0}else{wr(l,ye,n,Re,c,d,Oe);s=e+392|0;ni(s,c,d,o,Oe);n=n+10|0}f=d;u=s+20|0;do{Ve[s>>1]=Ve[f>>1]|0;s=s+2|0;f=f+2|0}while((s|0)<(u|0));ke=r>>>0>1;k=r>>>0<4&1;_e=(r|0)==5;ve=_e?10:5;_e=_e?19:9;g=e+434|0;R=143-_e&65535;A=e+460|0;y=e+462|0;O=e+464|0;F=r>>>0>2;T=e+388|0;D=(r|0)==0;N=r>>>0<2;P=e+1244|0;C=e+432|0;I=r>>>0<6;B=e+1168|0;L=(r|0)==6;U=ye<<16>>16==0;x=e+714|0;H=e+686|0;z=e+436|0;Y=e+700|0;j=(r|0)==7;q=e+482|0;K=r>>>0<3;V=e+608|0;W=e+626|0;X=e+438|0;G=r>>>0<7;Z=e+730|0;M=Ae^1;J=a<<16>>16!=0;be=J?ye^1:0;Q=e+442|0;$=e+458|0;ee=e+412|0;re=e+80|0;ne=e+1236|0;te=e+1240|0;ie=e+468|0;oe=e+466|0;ae=e+470|0;se=e+472|0;le=e+474|0;fe=e+476|0;ue=e+478|0;ce=e+480|0;de=e+444|0;he=e+446|0;we=e+448|0;me=e+450|0;Ee=e+452|0;pe=e+454|0;Se=e+456|0;b=0;v=0;h=0;w=0;_=-1;while(1){_=(_<<16>>16)+1|0;u=_&65535;v=1-(v<<16>>16)|0;E=v&65535;c=ke&h<<16>>16==80?0:h;m=n+2|0;d=Ve[n>>1]|0;e:do{if(ge){p=Ve[g>>1]|0;s=(p&65535)-ve&65535;s=s<<16>>16<20?20:s;f=(s&65535)+_e&65535;l=f<<16>>16>143;fr(d,l?R:s,l?143:f,c,p,Ce,Ie,k,Oe);c=Ve[Ce>>1]|0;Ve[A>>1]=c;if(Ae){d=Ve[g>>1]|0;if(d<<16>>16<143){d=(d&65535)+1&65535;Ve[g>>1]=d}Ve[Ce>>1]=d;Ve[Ie>>1]=0;if((Ve[y>>1]|0)!=0?!(F|(Ve[O>>1]|0)<5):0){Ve[Ce>>1]=c;d=c;c=0}else c=0}else{d=c;c=Ve[Ie>>1]|0}ki(We[T>>2]|0,d,c,40,1,Oe);if(N){c=n+6|0;tr(u,Ve[n+4>>1]|0,Ve[m>>1]|0,We[P>>2]|0,Be,Oe);n=Ve[C>>1]|0;p=n<<16>>16;d=p<<1;if((d|0)==(p<<17>>16|0)){f=D;break}f=D;d=n<<16>>16>0?32767:-32768;break}switch(r|0){case 2:{c=n+6|0;nr(Ve[n+4>>1]|0,Ve[m>>1]|0,Be);n=Ve[C>>1]|0;p=n<<16>>16;d=p<<1;if((d|0)==(p<<17>>16|0)){f=D;break e}f=D;d=n<<16>>16>0?32767:-32768;break e}case 3:{c=n+6|0;ir(Ve[n+4>>1]|0,Ve[m>>1]|0,Be);n=Ve[C>>1]|0;p=n<<16>>16;d=p<<1;if((d|0)==(p<<17>>16|0)){f=D;break e}f=D;d=n<<16>>16>0?32767:-32768;break e}default:{if(I){c=n+6|0;or(Ve[n+4>>1]|0,Ve[m>>1]|0,We[B>>2]|0,Be);n=Ve[C>>1]|0;p=n<<16>>16;d=p<<1;if((d|0)==(p<<17>>16|0)){f=D;break e}f=D;d=n<<16>>16>0?32767:-32768;break e}if(!L){f=D;S=44;break e}ar(m,Be,Oe);d=n+16|0;n=Ve[C>>1]|0;p=n<<16>>16;u=p<<1;if((u|0)==(p<<17>>16|0)){c=d;f=D;d=u;break e}c=d;f=D;d=n<<16>>16>0?32767:-32768;break e}}}else{ur(d,18,143,c,Ce,Ie,Oe);if(U?c<<16>>16==0|d<<16>>16<61:0){d=Ve[Ce>>1]|0;c=Ve[Ie>>1]|0}else{Ve[A>>1]=Ve[Ce>>1]|0;d=Ve[g>>1]|0;Ve[Ce>>1]=d;Ve[Ie>>1]=0;c=0}ki(We[T>>2]|0,d,c,40,0,Oe);f=0;S=44}}while(0);if((S|0)==44){S=0;if(Ae)vr(H,Ve[t>>1]|0,He,Oe);else Ve[He>>1]=dr(r,Ve[m>>1]|0,We[te>>2]|0)|0;_r(H,ye,Ve[z>>1]|0,He,Oe);rr(n+4|0,Be,We[B>>2]|0);d=n+24|0;n=Ve[He>>1]|0;p=n<<16>>16;u=p<<1;if((u|0)==(p<<17>>16|0)){c=d;d=u}else{c=d;d=n<<16>>16>0?32767:-32768}}n=Ve[Ce>>1]|0;e:do{if(n<<16>>16<40){s=d<<16>>16;l=n;d=n<<16>>16;while(1){u=Be+(d<<1)|0;n=(Ze(Ve[Be+(d-(l<<16>>16)<<1)>>1]|0,s)|0)>>15;if((n|0)>32767){We[Oe>>2]=1;n=32767}p=n&65535;Ve[je>>1]=p;Ve[u>>1]=Wt(Ve[u>>1]|0,p,Oe)|0;d=d+1|0;if((d&65535)<<16>>16==40)break e;l=Ve[Ce>>1]|0}}}while(0);e:do{if(f){f=(v&65535|0)==0;if(f){n=c;u=w}else{n=c+2|0;u=Ve[c>>1]|0}if(U)lr(x,r,u,Be,E,He,xe,Re,Oe);else{vr(H,Ve[t>>1]|0,He,Oe);Sr(Y,x,Ve[t>>1]|0,xe,Oe)}_r(H,ye,Ve[z>>1]|0,He,Oe);br(Y,ye,Ve[z>>1]|0,xe,Oe);c=Ve[He>>1]|0;d=c<<16>>16>13017?13017:c;if(f)S=80;else p=u}else{n=c+2|0;d=Ve[c>>1]|0;switch(r|0){case 1:case 2:case 3:case 4:case 6:{if(U)lr(x,r,d,Be,E,He,xe,Re,Oe);else{vr(H,Ve[t>>1]|0,He,Oe);Sr(Y,x,Ve[t>>1]|0,xe,Oe)}_r(H,ye,Ve[z>>1]|0,He,Oe);br(Y,ye,Ve[z>>1]|0,xe,Oe);c=Ve[He>>1]|0;d=c<<16>>16>13017?13017:c;if(!L){u=w;S=80;break e}if((Ve[g>>1]|0)<=45){u=w;S=80;break e}u=w;d=d<<16>>16>>>2&65535;S=80;break e}case 5:{if(Ae)vr(H,Ve[t>>1]|0,He,Oe);else Ve[He>>1]=dr(5,d,We[te>>2]|0)|0;_r(H,ye,Ve[z>>1]|0,He,Oe);if(U)cr(x,5,Ve[n>>1]|0,Be,We[ne>>2]|0,xe,Oe);else Sr(Y,x,Ve[t>>1]|0,xe,Oe);br(Y,ye,Ve[z>>1]|0,xe,Oe);d=Ve[He>>1]|0;n=c+4|0;c=d;u=w;d=d<<16>>16>13017?13017:d;S=80;break e}default:{if(U)cr(x,r,d,Be,We[ne>>2]|0,xe,Oe);else Sr(Y,x,Ve[t>>1]|0,xe,Oe);br(Y,ye,Ve[z>>1]|0,xe,Oe);d=Ve[He>>1]|0;c=d;u=w;S=80;break e}}}}while(0);if((S|0)==80){S=0;Ve[C>>1]=c<<16>>16>13017?13017:c;p=u}d=d<<16>>16;d=(d<<17>>17|0)==(d|0)?d<<1:d>>>15^32767;E=(d&65535)<<16>>16>16384;e:do{if(E){m=d<<16>>16;if(j)c=0;else{c=0;while(1){d=(Ze(Ve[(We[T>>2]|0)+(c<<1)>>1]|0,m)|0)>>15;if((d|0)>32767){We[Oe>>2]=1;d=32767}Ve[je>>1]=d;d=Ze(Ve[He>>1]|0,d<<16>>16)|0;if((d|0)==1073741824){We[Oe>>2]=1;d=2147483647}else d=d<<1;Ve[Ue+(c<<1)>>1]=Ni(d,Oe)|0;c=c+1|0;if((c|0)==40)break e}}do{d=(Ze(Ve[(We[T>>2]|0)+(c<<1)>>1]|0,m)|0)>>15;if((d|0)>32767){We[Oe>>2]=1;d=32767}Ve[je>>1]=d;d=Ze(Ve[He>>1]|0,d<<16>>16)|0;if((d|0)!=1073741824){d=d<<1;if((d|0)<0)d=~((d^-2)>>1);else S=88}else{We[Oe>>2]=1;d=2147483647;S=88}if((S|0)==88){S=0;d=d>>1}Ve[Ue+(c<<1)>>1]=Ni(d,Oe)|0;c=c+1|0}while((c|0)!=40)}}while(0);if(U){Ve[oe>>1]=Ve[ie>>1]|0;Ve[ie>>1]=Ve[ae>>1]|0;Ve[ae>>1]=Ve[se>>1]|0;Ve[se>>1]=Ve[le>>1]|0;Ve[le>>1]=Ve[fe>>1]|0;Ve[fe>>1]=Ve[ue>>1]|0;Ve[ue>>1]=Ve[ce>>1]|0;Ve[ce>>1]=Ve[q>>1]|0;Ve[q>>1]=Ve[He>>1]|0}if((Ae|(Ve[z>>1]|0)!=0?K&(Ve[y>>1]|0)!=0:0)?(Ne=Ve[He>>1]|0,Ne<<16>>16>12288):0){S=(((Ne<<16>>16)+118784|0)>>>1)+12288&65535;Ve[He>>1]=S<<16>>16>14745?14745:S}Fr(Ye,Me,h,ze,Oe);d=er(V,r,Ve[xe>>1]|0,ze,W,ye,Ve[z>>1]|0,a,Ve[X>>1]|0,Ve[y>>1]|0,Ve[O>>1]|0,Oe)|0;switch(r|0){case 0:case 1:case 2:case 3:case 6:{u=Ve[He>>1]|0;m=1;break}default:{d=Ve[xe>>1]|0;u=Ve[He>>1]|0;if(G)m=1;else{c=u<<16>>16;if(u<<16>>16<0)c=~((c^-2)>>1);else c=c>>>1;u=c&65535;m=2}}}s=u<<16>>16;h=m&65535;c=We[T>>2]|0;w=0;do{c=c+(w<<1)|0;Ve[Le+(w<<1)>>1]=Ve[c>>1]|0;c=Ze(Ve[c>>1]|0,s)|0;if((c|0)==1073741824){We[Oe>>2]=1;l=2147483647}else l=c<<1;f=Ze(Ve[xe>>1]|0,Ve[Be+(w<<1)>>1]|0)|0;if((f|0)!=1073741824){c=(f<<1)+l|0;if((f^l|0)>0&(c^l|0)<0){We[Oe>>2]=1;c=(l>>>31)+2147483647|0}}else{We[Oe>>2]=1;c=2147483647}S=c<<h;S=Ni((S>>h|0)==(c|0)?S:c>>31^2147483647,Oe)|0;c=We[T>>2]|0;Ve[c+(w<<1)>>1]=S;w=w+1|0}while((w|0)!=40);Rr(Z);if((K?(Ve[O>>1]|0)>3:0)?!((Ve[y>>1]|0)==0|M):0)gr(Z);Ar(Z,r,Le,d,Ve[He>>1]|0,Be,u,m,Re,Oe);d=0;f=0;do{c=Ve[Le+(f<<1)>>1]|0;c=Ze(c,c)|0;if((c|0)!=1073741824){u=(c<<1)+d|0;if((c^d|0)>0&(u^d|0)<0){We[Oe>>2]=1;d=(d>>>31)+2147483647|0}else d=u}else{We[Oe>>2]=1;d=2147483647}f=f+1|0}while((f|0)!=40);if((d|0)<0)d=~((d^-2)>>1);else d=d>>1;d=Ii(d,je,Oe)|0;u=((Ve[je>>1]|0)>>>1)+15|0;c=u&65535;u=u<<16>>16;if(c<<16>>16>0)if(c<<16>>16<31){d=d>>u;S=135}else{d=0;S=137}else{m=0-u<<16>>16;S=d<<m;d=(S>>m|0)==(d|0)?S:d>>31^2147483647;S=135}if((S|0)==135){S=0;if((d|0)<0)d=~((d^-4)>>2);else S=137}if((S|0)==137){S=0;d=d>>>2}d=d&65535;do{if(K?(Pe=Ve[O>>1]|0,Pe<<16>>16>5):0)if(Ve[y>>1]|0)if((Ve[t>>1]|0)<4){if(J){if(!(Ae|(Ve[X>>1]|0)!=0))S=145}else if(!Ae)S=145;if((S|0)==145?(0,(Ve[z>>1]|0)==0):0){S=147;break}kr(Le,d,Q,Pe,Ve[z>>1]|0,be,Oe)|0;S=147}else S=147;else S=151;else S=147}while(0);do{if((S|0)==147){S=0;if(Ve[y>>1]|0){if(!Ae?(Ve[z>>1]|0)==0:0){S=151;break}if((Ve[t>>1]|0)>=4)S=151}else S=151}}while(0);if((S|0)==151){S=0;Ve[Q>>1]=Ve[de>>1]|0;Ve[de>>1]=Ve[he>>1]|0;Ve[he>>1]=Ve[we>>1]|0;Ve[we>>1]=Ve[me>>1]|0;Ve[me>>1]=Ve[Ee>>1]|0;Ve[Ee>>1]=Ve[pe>>1]|0;Ve[pe>>1]=Ve[Se>>1]|0;Ve[Se>>1]=Ve[$>>1]|0;Ve[$>>1]=d}if(E){d=0;do{E=Ue+(d<<1)|0;Ve[E>>1]=Wt(Ve[E>>1]|0,Ve[Le+(d<<1)>>1]|0,Oe)|0;d=d+1|0}while((d|0)!=40);Je(Le,Ue,40,Oe);We[Oe>>2]=0;Li(o,Ue,i+(b<<1)|0,40,ee,0)}else{We[Oe>>2]=0;Li(o,Le,i+(b<<1)|0,40,ee,0)}if(!(We[Oe>>2]|0))Ki(ee|0,i+(b+30<<1)|0,20)|0;else{u=193;while(1){c=e+(u<<1)|0;E=Ve[c>>1]|0;d=E<<16>>16;if(E<<16>>16<0)d=~((d^-4)>>2);else d=d>>>2;Ve[c>>1]=d;if((u|0)>0)u=u+-1|0;else{u=39;break}}while(1){c=Le+(u<<1)|0;E=Ve[c>>1]|0;d=E<<16>>16;if(E<<16>>16<0)d=~((d^-4)>>2);else d=d>>>2;Ve[c>>1]=d;if((u|0)>0)u=u+-1|0;else break}Li(o,Le,i+(b<<1)|0,40,ee,1)}Ki(e|0,re|0,308)|0;Ve[g>>1]=Ve[Ce>>1]|0;d=b+40|0;h=d&65535;if(h<<16>>16>=160)break;else{b=d<<16>>16;o=o+22|0;w=p}}Ve[y>>1]=$e(e+484|0,e+466|0,i,O,Oe)|0;Er(Te,Me,i,Oe);Ve[z>>1]=ye;Ve[X>>1]=a;Mr(e+626|0,Me,Oe);je=Fe;We[je>>2]=De;Ge=qe;return}function lr(e,r,n,t,i,o,a,s,l){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;var f=0,u=0,c=0,d=0,h=0;h=Ge;Ge=Ge+16|0;c=h+2|0;d=h;n=n<<16>>16;n=(n<<18>>18|0)==(n|0)?n<<2:n>>>15^32767;switch(r|0){case 3:case 4:case 6:{u=n<<16>>16;n=We[s+84>>2]|0;Ve[o>>1]=Ve[n+(u<<1)>>1]|0;s=Ve[n+(u+1<<1)>>1]|0;f=Ve[n+(u+3<<1)>>1]|0;o=Ve[n+(u+2<<1)>>1]|0;break}case 0:{s=(n&65535)+(i<<16>>16<<1^2)|0;s=(s&65535)<<16>>16>1022?1022:s<<16>>16;Ve[o>>1]=Ve[782+(s<<1)>>1]|0;o=Ve[782+(s+1<<1)>>1]|0;si(o<<16>>16,d,c,l);Ve[d>>1]=(Xe[d>>1]|0)+65524;s=Ci(Ve[c>>1]|0,5,l)|0;u=Ve[d>>1]|0;u=Wt(s,((u<<26>>26|0)==(u|0)?u<<10:u>>>15^32767)&65535,l)|0;s=Ve[c>>1]|0;n=Ve[d>>1]|0;if((n*24660|0)==1073741824){We[l>>2]=1;i=2147483647}else i=n*49320|0;f=(s<<16>>16)*24660>>15;n=i+(f<<1)|0;if((i^f|0)>0&(n^i|0)<0){We[l>>2]=1;n=(i>>>31)+2147483647|0}f=n<<13;s=o;f=Ni((f>>13|0)==(n|0)?f:n>>31^2147483647,l)|0;o=u;break}default:{u=n<<16>>16;n=We[s+80>>2]|0;Ve[o>>1]=Ve[n+(u<<1)>>1]|0;s=Ve[n+(u+1<<1)>>1]|0;f=Ve[n+(u+3<<1)>>1]|0;o=Ve[n+(u+2<<1)>>1]|0}}Jt(e,r,t,d,c,0,0,l);i=Ze((_i(14,Ve[c>>1]|0,l)|0)<<16>>16,s<<16>>16)|0;if((i|0)==1073741824){We[l>>2]=1;n=2147483647}else n=i<<1;s=10-(Xe[d>>1]|0)|0;i=s&65535;s=s<<16>>16;if(i<<16>>16>0){d=i<<16>>16<31?n>>s:0;d=d>>>16;d=d&65535;Ve[a>>1]=d;Qt(e,o,f);Ge=h;return}else{l=0-s<<16>>16;d=n<<l;d=(d>>l|0)==(n|0)?d:n>>31^2147483647;d=d>>>16;d=d&65535;Ve[a>>1]=d;Qt(e,o,f);Ge=h;return}}function fr(e,r,n,t,i,o,a,s,l){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;if(!(t<<16>>16)){s=e<<16>>16;if(e<<16>>16>=197){Ve[o>>1]=s+65424;Ve[a>>1]=0;return}i=((s<<16)+131072>>16)*10923>>15;if((i|0)>32767){We[l>>2]=1;i=32767}e=(i&65535)+19|0;Ve[o>>1]=e;Ve[a>>1]=s+58-((e*196608|0)>>>16);return}if(!(s<<16>>16)){l=e<<16>>16<<16;e=((l+131072>>16)*21846|0)+-65536>>16;Ve[o>>1]=e+(r&65535);Ve[a>>1]=((l+-131072|0)>>>16)-((e*196608|0)>>>16);return}if((Bi(i,r,l)|0)<<16>>16>5)i=(r&65535)+5&65535;s=n<<16>>16;s=(s-(i&65535)&65535)<<16>>16>4?s+65532&65535:i;i=e<<16>>16;if(e<<16>>16<4){Ve[o>>1]=((((s&65535)<<16)+-327680|0)>>>16)+i;Ve[a>>1]=0;return}i=i<<16;if(e<<16>>16<12){l=(((i+-327680>>16)*10923|0)>>>15<<16)+-65536|0;e=l>>16;Ve[o>>1]=(s&65535)+e;Ve[a>>1]=((i+-589824|0)>>>16)-(l>>>15)-e;return}else{Ve[o>>1]=((i+-786432+((s&65535)<<16)|0)>>>16)+1;Ve[a>>1]=0;return}}function ur(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;if(t<<16>>16){a=(Xe[i>>1]|0)+65531|0;a=(a<<16>>16|0)<(r<<16>>16|0)?r:a&65535;n=n<<16>>16;r=e<<16>>16<<16;e=((r+327680>>16)*10924|0)+-65536>>16;Ve[i>>1]=(((((a&65535)<<16)+589824>>16|0)>(n|0)?n+65527&65535:a)&65535)+e;Ve[o>>1]=((r+-196608|0)>>>16)-((e*393216|0)>>>16);return}t=e<<16>>16;if(e<<16>>16<463){e=((((t<<16)+327680>>16)*10924|0)>>>16)+17|0;Ve[i>>1]=e;Ve[o>>1]=t+105-((e*393216|0)>>>16);return}else{Ve[i>>1]=t+65168;Ve[o>>1]=0;return}}function cr(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;var s=0,l=0,f=0,u=0;u=Ge;Ge=Ge+16|0;l=u+6|0;s=u+4|0;Jt(e,r,t,l,s,u+2|0,u,a);f=(n&31)*3|0;t=i+(f<<1)|0;if(!((Bi(r&65535,7,a)|0)<<16>>16)){l=_i(Ve[l>>1]|0,Ve[s>>1]|0,a)|0;s=l<<16>>16;s=(Ze(((l<<20>>20|0)==(s|0)?l<<4:s>>>15^32767)<<16>>16,Ve[t>>1]|0)|0)>>15;if((s|0)>32767){We[a>>2]=1;s=32767}t=s<<16;n=t>>16;if((s<<17>>17|0)==(n|0))s=t>>15;else s=n>>>15^32767}else{n=_i(14,Ve[s>>1]|0,a)|0;n=Ze(n<<16>>16,Ve[t>>1]|0)|0;if((n|0)==1073741824){We[a>>2]=1;t=2147483647}else t=n<<1;n=Bi(9,Ve[l>>1]|0,a)|0;s=n<<16>>16;if(n<<16>>16>0)s=n<<16>>16<31?t>>s:0;else{a=0-s<<16>>16;s=t<<a;s=(s>>a|0)==(t|0)?s:t>>31^2147483647}s=s>>>16}Ve[o>>1]=s;Qt(e,Ve[i+(f+1<<1)>>1]|0,Ve[i+(f+2<<1)>>1]|0);Ge=u;return}function dr(e,r,n){e=e|0;r=r|0;n=n|0;r=Ve[n+(r<<16>>16<<1)>>1]|0;if((e|0)!=7){e=r;return e|0}e=r&65532;return e|0}function hr(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;var s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0;p=Ge;Ge=Ge+48|0;h=p+20|0;E=p;m=We[i+44>>2]|0;w=We[i+64>>2]|0;s=We[i+4>>2]|0;d=We[i+12>>2]|0;f=We[i+20>>2]|0;l=We[i+56>>2]|0;if(!(n<<16>>16)){u=r>>>0<2;if(u){n=765;c=508;f=We[i+52>>2]|0}else{i=(r|0)==5;n=i?1533:765;c=2044;s=i?l:s}l=Ve[t>>1]|0;n=((l*196608>>16|0)>(n&65535|0)?n:l*3&65535)<<16>>16;l=Ve[s+(n<<1)>>1]|0;Ve[h>>1]=l;Ve[h+2>>1]=Ve[s+(n+1<<1)>>1]|0;Ve[h+4>>1]=Ve[s+(n+2<<1)>>1]|0;n=Ve[t+2>>1]|0;if(u)n=n<<16>>16<<1&65535;u=(n<<16>>16)*196608|0;u=(u|0)>100466688?1533:u>>16;Ve[h+6>>1]=Ve[d+(u<<1)>>1]|0;Ve[h+8>>1]=Ve[d+(u+1<<1)>>1]|0;Ve[h+10>>1]=Ve[d+(u+2<<1)>>1]|0;t=Ve[t+4>>1]|0;t=((t<<18>>16|0)>(c&65535|0)?c:t<<2&65535)<<16>>16;Ve[h+12>>1]=Ve[f+(t<<1)>>1]|0;Ve[h+14>>1]=Ve[f+((t|1)<<1)>>1]|0;Ve[h+16>>1]=Ve[f+((t|2)<<1)>>1]|0;Ve[h+18>>1]=Ve[f+((t|3)<<1)>>1]|0;if((r|0)==8){n=0;while(1){w=e+(n<<1)|0;Ve[E+(n<<1)>>1]=Wt(l,Wt(Ve[m+(n<<1)>>1]|0,Ve[w>>1]|0,a)|0,a)|0;Ve[w>>1]=l;n=n+1|0;if((n|0)==10)break;l=Ve[h+(n<<1)>>1]|0}Ti(E,205,10,a);s=e+20|0;l=E;n=s+20|0;do{Ke[s>>0]=Ke[l>>0]|0;s=s+1|0;l=l+1|0}while((s|0)<(n|0));Ei(E,o,10,a);Ge=p;return}else s=0;do{l=e+(s<<1)|0;n=(Ze(Ve[w+(s<<1)>>1]|0,Ve[l>>1]|0)|0)>>15;if((n|0)>32767){We[a>>2]=1;n=32767}t=Wt(Ve[m+(s<<1)>>1]|0,n&65535,a)|0;r=Ve[h+(s<<1)>>1]|0;Ve[E+(s<<1)>>1]=Wt(r,t,a)|0;Ve[l>>1]=r;s=s+1|0}while((s|0)!=10);Ti(E,205,10,a);s=e+20|0;l=E;n=s+20|0;do{Ke[s>>0]=Ke[l>>0]|0;s=s+1|0;l=l+1|0}while((s|0)<(n|0));Ei(E,o,10,a);Ge=p;return}else{s=0;do{n=(Ve[e+20+(s<<1)>>1]|0)*29491>>15;if((n|0)>32767){We[a>>2]=1;n=32767}l=(Ve[m+(s<<1)>>1]|0)*3277>>15;if((l|0)>32767){We[a>>2]=1;l=32767}Ve[E+(s<<1)>>1]=Wt(l&65535,n&65535,a)|0;s=s+1|0}while((s|0)!=10);if((r|0)==8){s=0;do{w=e+(s<<1)|0;h=Wt(Ve[m+(s<<1)>>1]|0,Ve[w>>1]|0,a)|0;Ve[w>>1]=Bi(Ve[E+(s<<1)>>1]|0,h,a)|0;s=s+1|0}while((s|0)!=10);Ti(E,205,10,a);s=e+20|0;l=E;n=s+20|0;do{Ke[s>>0]=Ke[l>>0]|0;s=s+1|0;l=l+1|0}while((s|0)<(n|0));Ei(E,o,10,a);Ge=p;return}else s=0;do{l=e+(s<<1)|0;n=(Ze(Ve[w+(s<<1)>>1]|0,Ve[l>>1]|0)|0)>>15;if((n|0)>32767){We[a>>2]=1;n=32767}h=Wt(Ve[m+(s<<1)>>1]|0,n&65535,a)|0;Ve[l>>1]=Bi(Ve[E+(s<<1)>>1]|0,h,a)|0;s=s+1|0}while((s|0)!=10);Ti(E,205,10,a);s=e+20|0;l=E;n=s+20|0;do{Ke[s>>0]=Ke[l>>0]|0;s=s+1|0;l=l+1|0}while((s|0)<(n|0));Ei(E,o,10,a);Ge=p;return}}function Ie(e,r,n){e=e|0;r=r|0;n=n|0;Ki(e|0,n+((r<<16>>16)*10<<1)|0,20)|0;return}function wr(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;var s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0;p=Ge;Ge=Ge+80|0;d=p+60|0;h=p+40|0;m=p+20|0;E=p;w=We[t+48>>2]|0;f=We[t+24>>2]|0;u=We[t+28>>2]|0;c=We[t+32>>2]|0;if(r<<16>>16){s=0;do{d=w+(s<<1)|0;n=Wt(((Ve[d>>1]|0)*1639|0)>>>15&65535,((Ve[e+20+(s<<1)>>1]|0)*31128|0)>>>15&65535,a)|0;Ve[m+(s<<1)>>1]=n;Ve[E+(s<<1)>>1]=n;h=e+(s<<1)|0;Ve[h>>1]=Bi(n,Wt(Ve[d>>1]|0,((Ve[h>>1]|0)*21299|0)>>>15&65535,a)|0,a)|0;s=s+1|0}while((s|0)!=10);Ti(m,205,10,a);Ti(E,205,10,a);s=e+20|0;t=E;r=s+20|0;do{Ke[s>>0]=Ke[t>>0]|0;s=s+1|0;t=t+1|0}while((s|0)<(r|0));Ei(m,i,10,a);Ei(E,o,10,a);Ge=p;return}r=We[t+16>>2]|0;t=We[t+8>>2]|0;l=Ve[n>>1]|0;l=((l<<18>>18|0)==(l|0)?l<<2:l>>>15^32767)<<16>>16;Ve[d>>1]=Ve[t+(l<<1)>>1]|0;Ve[d+2>>1]=Ve[t+(l+1<<1)>>1]|0;Ve[h>>1]=Ve[t+(l+2<<1)>>1]|0;Ve[h+2>>1]=Ve[t+(l+3<<1)>>1]|0;l=Ve[n+2>>1]|0;l=((l<<18>>18|0)==(l|0)?l<<2:l>>>15^32767)<<16>>16;Ve[d+4>>1]=Ve[r+(l<<1)>>1]|0;Ve[d+6>>1]=Ve[r+(l+1<<1)>>1]|0;Ve[h+4>>1]=Ve[r+(l+2<<1)>>1]|0;Ve[h+6>>1]=Ve[r+(l+3<<1)>>1]|0;l=Ve[n+4>>1]|0;t=l<<16>>16;if(l<<16>>16<0)r=~((t^-2)>>1);else r=t>>>1;l=r<<16>>16;l=((r<<18>>18|0)==(l|0)?r<<2:l>>>15^32767)<<16>>16;s=f+(l+1<<1)|0;r=Ve[f+(l<<1)>>1]|0;if(!(t&1)){Ve[d+8>>1]=r;Ve[d+10>>1]=Ve[s>>1]|0;Ve[h+8>>1]=Ve[f+(l+2<<1)>>1]|0;Ve[h+10>>1]=Ve[f+(l+3<<1)>>1]|0}else{if(r<<16>>16==-32768)r=32767;else r=0-(r&65535)&65535;Ve[d+8>>1]=r;r=Ve[s>>1]|0;if(r<<16>>16==-32768)r=32767;else r=0-(r&65535)&65535;Ve[d+10>>1]=r;r=Ve[f+(l+2<<1)>>1]|0;if(r<<16>>16==-32768)r=32767;else r=0-(r&65535)&65535;Ve[h+8>>1]=r;r=Ve[f+(l+3<<1)>>1]|0;if(r<<16>>16==-32768)r=32767;else r=0-(r&65535)&65535;Ve[h+10>>1]=r}s=Ve[n+6>>1]|0;s=((s<<18>>18|0)==(s|0)?s<<2:s>>>15^32767)<<16>>16;Ve[d+12>>1]=Ve[u+(s<<1)>>1]|0;Ve[d+14>>1]=Ve[u+(s+1<<1)>>1]|0;Ve[h+12>>1]=Ve[u+(s+2<<1)>>1]|0;Ve[h+14>>1]=Ve[u+(s+3<<1)>>1]|0;s=Ve[n+8>>1]|0;s=((s<<18>>18|0)==(s|0)?s<<2:s>>>15^32767)<<16>>16;Ve[d+16>>1]=Ve[c+(s<<1)>>1]|0;Ve[d+18>>1]=Ve[c+(s+1<<1)>>1]|0;Ve[h+16>>1]=Ve[c+(s+2<<1)>>1]|0;Ve[h+18>>1]=Ve[c+(s+3<<1)>>1]|0;s=0;do{t=e+(s<<1)|0;r=(Ve[t>>1]|0)*21299>>15;if((r|0)>32767){We[a>>2]=1;r=32767}c=Wt(Ve[w+(s<<1)>>1]|0,r&65535,a)|0;Ve[m+(s<<1)>>1]=Wt(Ve[d+(s<<1)>>1]|0,c,a)|0;n=Ve[h+(s<<1)>>1]|0;Ve[E+(s<<1)>>1]=Wt(n,c,a)|0;Ve[t>>1]=n;s=s+1|0}while((s|0)!=10);Ti(m,205,10,a);Ti(E,205,10,a);s=e+20|0;t=E;r=s+20|0;do{Ke[s>>0]=Ke[t>>0]|0;s=s+1|0;t=t+1|0}while((s|0)<(r|0));Ei(m,i,10,a);Ei(E,o,10,a);Ge=p;return}function Be(e,r){e=e|0;r=r|0;var n=0,t=0;if(!e){t=-1;return t|0}n=e;t=n+20|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));Ki(e+20|0,r|0,20)|0;t=0;return t|0}function Le(e){e=e|0;var r=0,n=0,t=0,i=0,o=0;if(!e){o=-1;return o|0}Ve[e>>1]=0;Ve[e+2>>1]=8192;r=e+4|0;Ve[r>>1]=3500;Ve[e+6>>1]=3500;We[e+8>>2]=1887529304;Ve[e+12>>1]=3e4;Ve[e+14>>1]=26e3;Ve[e+16>>1]=21e3;Ve[e+18>>1]=15e3;Ve[e+20>>1]=8e3;Ve[e+22>>1]=0;Ve[e+24>>1]=-8e3;Ve[e+26>>1]=-15e3;Ve[e+28>>1]=-21e3;Ve[e+30>>1]=-26e3;Ve[e+32>>1]=3e4;Ve[e+34>>1]=26e3;Ve[e+36>>1]=21e3;Ve[e+38>>1]=15e3;Ve[e+40>>1]=8e3;Ve[e+42>>1]=0;Ve[e+44>>1]=-8e3;Ve[e+46>>1]=-15e3;Ve[e+48>>1]=-21e3;Ve[e+50>>1]=-26e3;Ve[e+212>>1]=0;Ve[e+374>>1]=0;Ve[e+392>>1]=0;n=e+52|0;Ve[n>>1]=1384;Ve[e+54>>1]=2077;Ve[e+56>>1]=3420;Ve[e+58>>1]=5108;Ve[e+60>>1]=6742;Ve[e+62>>1]=8122;Ve[e+64>>1]=9863;Ve[e+66>>1]=11092;Ve[e+68>>1]=12714;Ve[e+70>>1]=13701;t=e+72|0;i=n;o=t+20|0;do{Ke[t>>0]=Ke[i>>0]|0;t=t+1|0;i=i+1|0}while((t|0)<(o|0));t=e+92|0;i=n;o=t+20|0;do{Ke[t>>0]=Ke[i>>0]|0;t=t+1|0;i=i+1|0}while((t|0)<(o|0));t=e+112|0;i=n;o=t+20|0;do{Ke[t>>0]=Ke[i>>0]|0;t=t+1|0;i=i+1|0}while((t|0)<(o|0));t=e+132|0;i=n;o=t+20|0;do{Ke[t>>0]=Ke[i>>0]|0;t=t+1|0;i=i+1|0}while((t|0)<(o|0));t=e+152|0;i=n;o=t+20|0;do{Ke[t>>0]=Ke[i>>0]|0;t=t+1|0;i=i+1|0}while((t|0)<(o|0));t=e+172|0;i=n;o=t+20|0;do{Ke[t>>0]=Ke[i>>0]|0;t=t+1|0;i=i+1|0}while((t|0)<(o|0));t=e+192|0;i=n;o=t+20|0;do{Ke[t>>0]=Ke[i>>0]|0;t=t+1|0;i=i+1|0}while((t|0)<(o|0));Vi(e+214|0,0,160)|0;Ve[e+376>>1]=3500;Ve[e+378>>1]=3500;o=Ve[r>>1]|0;Ve[e+380>>1]=o;Ve[e+382>>1]=o;Ve[e+384>>1]=o;Ve[e+386>>1]=o;Ve[e+388>>1]=o;Ve[e+390>>1]=o;Ve[e+394>>1]=0;Ve[e+396>>1]=7;Ve[e+398>>1]=32767;Ve[e+400>>1]=0;Ve[e+402>>1]=0;Ve[e+404>>1]=0;We[e+408>>2]=1;Ve[e+412>>1]=0;o=0;return o|0}function mr(e,r,n,t,i,o,a,s,l,f,u,c){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;u=u|0;c=c|0;var d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0,C=0,I=0,B=0,L=0,U=0,x=0,H=0,z=0,Y=0,j=0,q=0,K=0,V=0;V=Ge;Ge=Ge+304|0;L=V+192|0;C=V+168|0;x=V+148|0;j=V+216|0;H=V+146|0;z=V+144|0;I=V+124|0;B=V+104|0;U=V+84|0;Y=V+60|0;N=V+40|0;D=V;K=e+404|0;q=e+400|0;if((Ve[K>>1]|0)!=0?(Ve[q>>1]|0)!=0:0){T=e+394|0;Ve[T>>1]=Ve[636+(a<<1)>>1]|0;v=Ve[e+212>>1]|0;b=v+10|0;Ki(e+52+(((b&65535|0)==80?0:b<<16>>16)<<1)|0,e+52+(v<<1)|0,20)|0;v=Ve[e+392>>1]|0;b=v+1|0;Ve[e+376+(((b&65535|0)==8?0:b<<16>>16)<<1)>>1]=Ve[e+376+(v<<1)>>1]|0;b=e+4|0;Ve[b>>1]=0;v=D+36|0;_=D+32|0;k=D+28|0;F=D+24|0;M=D+20|0;g=D+16|0;R=D+12|0;A=D+8|0;y=D+4|0;O=e+52|0;w=D;P=w+40|0;do{We[w>>2]=0;w=w+4|0}while((w|0)<(P|0));h=0;d=7;while(1){P=Ve[e+376+(d<<1)>>1]|0;S=P<<16>>16;if(P<<16>>16<0)S=~((S^-8)>>3);else S=S>>>3;h=Wt(h,S&65535,c)|0;Ve[b>>1]=h;E=d*10|0;w=9;while(1){m=D+(w<<2)|0;p=We[m>>2]|0;P=Ve[e+52+(w+E<<1)>>1]|0;S=P+p|0;if((P^p|0)>-1&(S^p|0)<0){We[c>>2]=1;S=(p>>>31)+2147483647|0}We[m>>2]=S;if((w|0)>0)w=w+-1|0;else break}if((d|0)<=0)break;else d=d+-1|0}Ve[N+18>>1]=(We[v>>2]|0)>>>3;Ve[N+16>>1]=(We[_>>2]|0)>>>3;Ve[N+14>>1]=(We[k>>2]|0)>>>3;Ve[N+12>>1]=(We[F>>2]|0)>>>3;Ve[N+10>>1]=(We[M>>2]|0)>>>3;Ve[N+8>>1]=(We[g>>2]|0)>>>3;Ve[N+6>>1]=(We[R>>2]|0)>>>3;Ve[N+4>>1]=(We[A>>2]|0)>>>3;Ve[N+2>>1]=(We[y>>2]|0)>>>3;Ve[N>>1]=(We[D>>2]|0)>>>3;Ei(N,e+12|0,10,c);Ve[b>>1]=Bi(Ve[b>>1]|0,Ve[T>>1]|0,c)|0;qi(e+214|0,O|0,160)|0;N=9;while(1){P=Ve[e+214+(N+70<<1)>>1]|0;m=P<<16>>16;D=Ve[e+214+(N+60<<1)>>1]|0;w=(D<<16>>16)+m|0;if((D^P)<<16>>16>-1&(w^m|0)<0){We[c>>2]=1;w=(m>>>31)+2147483647|0}P=Ve[e+214+(N+50<<1)>>1]|0;m=P+w|0;if((P^w|0)>-1&(m^w|0)<0){We[c>>2]=1;m=(w>>>31)+2147483647|0}P=Ve[e+214+(N+40<<1)>>1]|0;w=P+m|0;if((P^m|0)>-1&(w^m|0)<0){We[c>>2]=1;w=(m>>>31)+2147483647|0}P=Ve[e+214+(N+30<<1)>>1]|0;m=P+w|0;if((P^w|0)>-1&(m^w|0)<0){We[c>>2]=1;m=(w>>>31)+2147483647|0}P=Ve[e+214+(N+20<<1)>>1]|0;w=P+m|0;if((P^m|0)>-1&(w^m|0)<0){We[c>>2]=1;w=(m>>>31)+2147483647|0}P=Ve[e+214+(N+10<<1)>>1]|0;m=P+w|0;if((P^w|0)>-1&(m^w|0)<0){We[c>>2]=1;w=(w>>>31)+2147483647|0}else w=m;P=Ve[e+214+(N<<1)>>1]|0;m=P+w|0;if((P^w|0)>-1&(m^w|0)<0){We[c>>2]=1;m=(w>>>31)+2147483647|0}if((m|0)<0)m=~((m^-8)>>3);else m=m>>>3;S=m&65535;E=Ve[654+(N<<1)>>1]|0;p=7;while(1){d=e+214+((p*10|0)+N<<1)|0;m=Bi(Ve[d>>1]|0,S,c)|0;Ve[d>>1]=m;m=(Ze(E,m<<16>>16)|0)>>15;if((m|0)>32767){We[c>>2]=1;m=32767}Ve[d>>1]=m;h=(m&65535)-(m>>>15&1)|0;h=h<<16>>31^h;w=h&65535;if(w<<16>>16>655)w=(((h<<16>>16)+261489|0)>>>2)+655&65535;w=w<<16>>16>1310?1310:w;if(!(m&32768))m=w;else m=0-(w&65535)&65535;Ve[d>>1]=m;if((p|0)>0)p=p+-1|0;else break}if((N|0)>0)N=N+-1|0;else break}}if(Ve[q>>1]|0){S=e+32|0;p=e+12|0;w=S;E=p;P=w+20|0;do{Ke[w>>0]=Ke[E>>0]|0;w=w+1|0;E=E+1|0}while((w|0)<(P|0));E=e+4|0;h=Ve[E>>1]|0;d=e+6|0;Ve[d>>1]=h;do{if(Ve[e+402>>1]|0){w=Ve[e>>1]|0;Ve[e>>1]=0;w=w<<16>>16<32?w:32;P=w<<16>>16;m=P<<10;if((m|0)!=(P<<26>>16|0)){We[c>>2]=1;m=w<<16>>16>0?32767:-32768}if(w<<16>>16>1)m=Gt(1024,m&65535)|0;else m=16384;Ve[e+2>>1]=m;Ie(n,Ve[s>>1]|0,We[l+60>>2]|0);hr(n,8,0,s+2|0,l,p,c);w=n;P=w+20|0;do{Ke[w>>0]=0;w=w+1|0}while((w|0)<(P|0));h=Ve[s+8>>1]|0;h=h<<16>>16==0?-32768:((h+64&65535)>127?h<<16>>16>0?32767:32768:h<<16>>16<<9)+60416&65535;Ve[E>>1]=h;if((Ve[e+412>>1]|0)!=0?(We[e+408>>2]|0)!=0:0)break;w=S;E=p;P=w+20|0;do{Ke[w>>0]=Ke[E>>0]|0;w=w+1|0;E=E+1|0}while((w|0)<(P|0));Ve[d>>1]=h}}while(0);w=h<<16>>16;if(h<<16>>16<0)w=~((w^-2)>>1);else w=w>>>1;w=w+56536|0;m=w<<16;if((m|0)>0)w=0;else w=(m|0)<-946077696?-14436:w&65535;Ve[t>>1]=w;Ve[t+2>>1]=w;Ve[t+4>>1]=w;Ve[t+6>>1]=w;s=((w<<16>>16)*5443|0)>>>15&65535;Ve[t+8>>1]=s;Ve[t+10>>1]=s;Ve[t+12>>1]=s;Ve[t+14>>1]=s}w=((Ve[636+(a<<1)>>1]|0)*104864|0)>>>15<<16;if((w|0)<0)w=~((w>>16^-32)>>5);else w=w>>21;a=e+394|0;Ve[a>>1]=Wt(((Ve[a>>1]|0)*29491|0)>>>15&65535,w&65535,c)|0;t=(Xe[e>>1]<<16)+65536|0;w=t>>16;l=e+2|0;w=(Ze(((t<<10>>26|0)==(w|0)?t>>>6:w>>>15^32767)<<16>>16,Ve[l>>1]|0)|0)>>15;if((w|0)>32767){We[c>>2]=1;w=32767}h=w&65535;if(h<<16>>16<=1024)if(h<<16>>16<-2048)p=-32768;else p=w<<4&65535;else p=16384;s=e+4|0;S=p<<16>>16;m=Ze(Ve[s>>1]|0,S)|0;if((m|0)==1073741824){We[c>>2]=1;N=2147483647}else N=m<<1;m=(Ze(Ve[e+30>>1]|0,S)|0)>>15;if((m|0)>32767){We[c>>2]=1;m=32767}b=m&65535;Ve[L+18>>1]=b;m=(Ze(Ve[e+28>>1]|0,S)|0)>>15;if((m|0)>32767){We[c>>2]=1;m=32767}Ve[L+16>>1]=m;m=(Ze(Ve[e+26>>1]|0,S)|0)>>15;if((m|0)>32767){We[c>>2]=1;m=32767}Ve[L+14>>1]=m;m=(Ze(Ve[e+24>>1]|0,S)|0)>>15;if((m|0)>32767){We[c>>2]=1;m=32767}Ve[L+12>>1]=m;m=(Ze(Ve[e+22>>1]|0,S)|0)>>15;if((m|0)>32767){We[c>>2]=1;m=32767}Ve[L+10>>1]=m;m=(Ze(Ve[e+20>>1]|0,S)|0)>>15;if((m|0)>32767){We[c>>2]=1;m=32767}Ve[L+8>>1]=m;m=(Ze(Ve[e+18>>1]|0,S)|0)>>15;if((m|0)>32767){We[c>>2]=1;m=32767}Ve[L+6>>1]=m;m=(Ze(Ve[e+16>>1]|0,S)|0)>>15;if((m|0)>32767){We[c>>2]=1;m=32767}Ve[L+4>>1]=m;m=(Ze(Ve[e+14>>1]|0,S)|0)>>15;if((m|0)>32767){We[c>>2]=1;m=32767}Ve[L+2>>1]=m;m=(Ze(Ve[e+12>>1]|0,S)|0)>>15;if((m|0)>32767){We[c>>2]=1;m=32767}Ve[L>>1]=m;t=e+6|0;S=16384-(p&65535)<<16>>16;m=Ze(Ve[t>>1]|0,S)|0;if((m|0)!=1073741824){w=(m<<1)+N|0;if((m^N|0)>0&(w^N|0)<0){We[c>>2]=1;D=(N>>>31)+2147483647|0}else D=w}else{We[c>>2]=1;D=2147483647}w=b;E=9;while(1){h=L+(E<<1)|0;m=(Ze(Ve[e+32+(E<<1)>>1]|0,S)|0)>>15;if((m|0)>32767){We[c>>2]=1;m=32767}w=Wt(w,m&65535,c)|0;Ve[h>>1]=w;P=w<<16>>16;m=P<<1;if((m|0)!=(P<<17>>16|0)){We[c>>2]=1;m=w<<16>>16>0?32767:-32768}Ve[h>>1]=m;m=E+-1|0;if((E|0)<=0)break;w=Ve[L+(m<<1)>>1]|0;E=m}N=e+374|0;m=((Xe[N>>1]<<16)+-161021952>>16)*9830>>15;if((m|0)>32767){We[c>>2]=1;m=32767}m=4096-(m&65535)|0;w=m<<16;if((w|0)>268369920)S=32767;else S=(w|0)<0?0:m<<19>>16;T=e+8|0;m=ye(T,3)|0;pi(L,I,10,c);w=B;E=I;P=w+20|0;do{Ve[w>>1]=Ve[E>>1]|0;w=w+2|0;E=E+2|0}while((w|0)<(P|0));w=(m<<16>>16)*10|0;E=9;while(1){h=B+(E<<1)|0;d=Ve[h>>1]|0;m=(Ze(Ve[e+214+(E+w<<1)>>1]|0,S)|0)>>15;if((m|0)>32767){We[c>>2]=1;m=32767}Ve[h>>1]=Wt(d,m&65535,c)|0;if((E|0)>0)E=E+-1|0;else break}Ti(I,205,10,c);Ti(B,205,10,c);w=n+20|0;E=I;P=w+20|0;do{Ke[w>>0]=Ke[E>>0]|0;w=w+1|0;E=E+1|0}while((w|0)<(P|0));Ei(I,L,10,c);Ei(B,U,10,c);ci(L,C,c);ci(U,Y,c);w=u;E=C;P=w+22|0;do{Ke[w>>0]=Ke[E>>0]|0;w=w+1|0;E=E+1|0}while((w|0)<(P|0));w=u+22|0;E=C;P=w+22|0;do{Ke[w>>0]=Ke[E>>0]|0;w=w+1|0;E=E+1|0}while((w|0)<(P|0));w=u+44|0;E=C;P=w+22|0;do{Ke[w>>0]=Ke[E>>0]|0;w=w+1|0;E=E+1|0}while((w|0)<(P|0));w=u+66|0;E=C;P=w+22|0;do{Ke[w>>0]=Ke[E>>0]|0;w=w+1|0;E=E+1|0}while((w|0)<(P|0));Ae(C+2|0,x,c);m=0;w=32767;do{h=Ve[x+(m<<1)>>1]|0;h=Ze(h,h)|0;if(h>>>0<1073741824)h=32767-(h>>>15)|0;else{We[c>>2]=1;h=0}w=(Ze(h<<16>>16,w<<16>>16)|0)>>15;if((w|0)>32767){We[c>>2]=1;w=32767}m=m+1|0}while((m|0)!=10);si(w<<16>>16,H,z,c);w=(Xe[H>>1]<<16)+-983040|0;h=w>>16;h=Pi(Bi(0,Wt(((w<<12>>28|0)==(h|0)?w>>>4:h>>>15^32767)&65535,Pi(Ve[z>>1]|0,3,c)|0,c)|0,c)|0,1,c)|0;w=(Ve[N>>1]|0)*29491>>15;if((w|0)>32767){We[c>>2]=1;w=32767}m=h<<16>>16;h=m*3277>>15;if((h|0)>32767){We[c>>2]=1;h=32767}Ve[N>>1]=Wt(w&65535,h&65535,c)|0;h=D>>10;d=h+262144|0;if((h|0)>-1&(d^h|0)<0){We[c>>2]=1;d=(h>>>31)+2147483647|0}z=m<<4;h=d-z|0;if(((h^d)&(d^z)|0)<0){We[c>>2]=1;d=(d>>>31)+2147483647|0}else d=h;z=Ve[a>>1]<<5;h=z+d|0;if((z^d|0)>-1&(h^d|0)<0){We[c>>2]=1;h=(d>>>31)+2147483647|0}m=(_i(h>>>16&65535,h>>>1&32767,c)|0)<<16>>16;Oe(T,j,c);d=39;while(1){w=j+(d<<1)|0;h=(Ze(Ve[w>>1]|0,m)|0)>>15;if((h|0)>32767){We[c>>2]=1;h=32767}Ve[w>>1]=h;if((d|0)>0)d=d+-1|0;else break}Li(Y,j,f,40,r,1);Oe(T,j,c);d=39;while(1){w=j+(d<<1)|0;h=(Ze(Ve[w>>1]|0,m)|0)>>15;if((h|0)>32767){We[c>>2]=1;h=32767}Ve[w>>1]=h;if((d|0)>0)d=d+-1|0;else break}Li(Y,j,f+80|0,40,r,1);Oe(T,j,c);d=39;while(1){w=j+(d<<1)|0;h=(Ze(Ve[w>>1]|0,m)|0)>>15;if((h|0)>32767){We[c>>2]=1;h=32767}Ve[w>>1]=h;if((d|0)>0)d=d+-1|0;else break}Li(Y,j,f+160|0,40,r,1);Oe(T,j,c);w=39;while(1){d=j+(w<<1)|0;h=(Ze(Ve[d>>1]|0,m)|0)>>15;if((h|0)>32767){We[c>>2]=1;h=32767}Ve[d>>1]=h;if((w|0)>0)w=w+-1|0;else break}Li(Y,j,f+240|0,40,r,1);Ve[i+14>>1]=20;Ve[i+16>>1]=0;if((o|0)==2){h=Ve[e>>1]|0;h=h<<16>>16>32?32:h<<16>>16<1?8:h;f=h<<16>>16;d=f<<10;if((d|0)!=(f<<26>>16|0)){We[c>>2]=1;d=h<<16>>16>0?32767:-32768}Ve[l>>1]=Gt(1024,d&65535)|0;Ve[e>>1]=0;w=e+32|0;E=e+12|0;P=w+20|0;do{Ke[w>>0]=Ke[E>>0]|0;w=w+1|0;E=E+1|0}while((w|0)<(P|0));c=Ve[s>>1]|0;Ve[t>>1]=c;Ve[s>>1]=(c&65535)+65280}if(!(Ve[q>>1]|0)){Ge=V;return}do{if(!(Ve[e+402>>1]|0)){if(Ve[K>>1]|0)break;Ge=V;return}}while(0);Ve[e>>1]=0;Ve[e+412>>1]=1;Ge=V;return}function Er(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0;l=Ge;Ge=Ge+16|0;a=l+2|0;s=l;Ve[s>>1]=0;o=e+212|0;i=(Xe[o>>1]|0)+10|0;i=(i&65535|0)==80?0:i&65535;Ve[o>>1]=i;Ki(e+52+(i<<16>>16<<1)|0,r|0,20)|0;i=0;o=159;while(1){f=Ve[n+(o<<1)>>1]|0;f=Ze(f,f)|0;f=(f|0)==1073741824?2147483647:f<<1;r=f+i|0;if((f^i|0)>-1&(r^i|0)<0){We[t>>2]=1;i=(i>>>31)+2147483647|0}else i=r;if((o|0)>0)o=o+-1|0;else break}si(i,a,s,t);i=Ve[a>>1]|0;f=i<<16>>16;r=f<<10;if((r|0)!=(f<<26>>16|0)){We[t>>2]=1;r=i<<16>>16>0?32767:-32768}Ve[a>>1]=r;f=Ve[s>>1]|0;i=f<<16>>16;if(f<<16>>16<0)i=~((i^-32)>>5);else i=i>>>5;s=e+392|0;f=(Xe[s>>1]|0)+1|0;f=(f&65535|0)==8?0:f&65535;Ve[s>>1]=f;Ve[e+376+(f<<16>>16<<1)>>1]=i+57015+r;Ge=l;return}function pr(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0,a=0,s=0,l=0,f=0,u=0;l=(r|0)==4;f=(r|0)==5;u=(r|0)==6;t=We[e+408>>2]|0;e:do{if((r+-4|0)>>>0<3)s=4;else{if((t+-1|0)>>>0<2)switch(r|0){case 2:case 3:case 7:{s=4;break e}default:{}}Ve[e>>1]=0;a=0}}while(0);if((s|0)==4){e:do{if((t|0)==2){switch(r|0){case 2:case 4:case 6:case 7:break;default:{i=1;break e}}i=2}else i=1}while(0);a=(Xe[e>>1]|0)+1&65535;Ve[e>>1]=a;a=(r|0)!=5&a<<16>>16>50?2:i}o=e+398|0;if(f&(Ve[e+412>>1]|0)==0){Ve[o>>1]=0;i=0}else i=Ve[o>>1]|0;i=Wt(i,1,n)|0;Ve[o>>1]=i;n=e+404|0;Ve[n>>1]=0;e:do{switch(r|0){case 2:case 4:case 5:case 6:case 7:{if(!((r|0)==7&(a|0)==0)){if(i<<16>>16>30){Ve[n>>1]=1;Ve[o>>1]=0;Ve[e+396>>1]=0;break e}i=e+396|0;t=Ve[i>>1]|0;if(!(t<<16>>16)){Ve[o>>1]=0;break e}else{Ve[i>>1]=(t&65535)+65535;break e}}else s=14;break}default:s=14}}while(0);if((s|0)==14)Ve[e+396>>1]=7;if(!a)return a|0;i=e+400|0;Ve[i>>1]=0;t=e+402|0;Ve[t>>1]=0;if(l){Ve[i>>1]=1;return a|0}if(f){Ve[i>>1]=1;Ve[t>>1]=1;return a|0}if(!u)return a|0;Ve[i>>1]=1;Ve[n>>1]=0;return a|0}function Ue(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=1;Ve[e+2>>1]=1;Ve[e+4>>1]=1;Ve[e+6>>1]=1;Ve[e+8>>1]=1;Ve[e+10>>1]=0;Ve[e+12>>1]=1;e=0;return e|0}function Sr(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0,a=0,s=0,l=0;l=Ge;Ge=Ge+16|0;s=l+2|0;a=l;o=ri(e,5)|0;e=e+10|0;if((Bi(o,Ve[e>>1]|0,i)|0)<<16>>16>0)o=Ve[e>>1]|0;o=(Ze(Ve[674+(n<<16>>16<<1)>>1]|0,o<<16>>16)|0)>>15;if((o|0)>32767){We[i>>2]=1;o=32767}Ve[t>>1]=o;$t(r,s,a,i);Qt(r,Ve[s>>1]|0,Ve[a>>1]|0);Ge=l;return}function br(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;if(!(r<<16>>16)){if(n<<16>>16){r=e+12|0;if((Bi(Ve[t>>1]|0,Ve[r>>1]|0,i)|0)<<16>>16>0)Ve[t>>1]=Ve[r>>1]|0}else r=e+12|0;Ve[r>>1]=Ve[t>>1]|0}Ve[e+10>>1]=Ve[t>>1]|0;i=e+2|0;Ve[e>>1]=Ve[i>>1]|0;n=e+4|0;Ve[i>>1]=Ve[n>>1]|0;i=e+6|0;Ve[n>>1]=Ve[i>>1]|0;e=e+8|0;Ve[i>>1]=Ve[e>>1]|0;Ve[e>>1]=Ve[t>>1]|0;return}function vr(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0;i=ri(e,5)|0;e=e+10|0;if((Bi(i,Ve[e>>1]|0,t)|0)<<16>>16>0)i=Ve[e>>1]|0;i=(Ze(Ve[688+(r<<16>>16<<1)>>1]|0,i<<16>>16)|0)>>15;if((i|0)<=32767){t=i;t=t&65535;Ve[n>>1]=t;return}We[t>>2]=1;t=32767;t=t&65535;Ve[n>>1]=t;return}function xe(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=1640;Ve[e+2>>1]=1640;Ve[e+4>>1]=1640;Ve[e+6>>1]=1640;Ve[e+8>>1]=1640;Ve[e+10>>1]=0;Ve[e+12>>1]=16384;e=0;return e|0}function _r(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;if(!(r<<16>>16)){if(n<<16>>16){r=e+12|0;if((Bi(Ve[t>>1]|0,Ve[r>>1]|0,i)|0)<<16>>16>0)Ve[t>>1]=Ve[r>>1]|0}else r=e+12|0;Ve[r>>1]=Ve[t>>1]|0}t=Ve[t>>1]|0;r=e+10|0;Ve[r>>1]=t;if((Bi(t,16384,i)|0)<<16>>16>0){Ve[r>>1]=16384;r=16384}else r=Ve[r>>1]|0;i=e+2|0;Ve[e>>1]=Ve[i>>1]|0;t=e+4|0;Ve[i>>1]=Ve[t>>1]|0;i=e+6|0;Ve[t>>1]=Ve[i>>1]|0;e=e+8|0;Ve[i>>1]=Ve[e>>1]|0;Ve[e>>1]=r;return}function kr(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;var s=0,l=0,f=0;l=ri(n,9)|0;f=Ve[n+16>>1]|0;s=f<<16>>16;n=(s+(Ve[n+14>>1]|0)|0)>>>1;n=(s|0)<(n<<16>>16|0)?f:n&65535;if(!(r<<16>>16>5?l<<16>>16>r<<16>>16:0))return 0;s=n<<16>>16;s=((s<<18>>18|0)==(s|0)?s<<2:s>>>15^32767)&65535;if(!(t<<16>>16>6&i<<16>>16==0))s=Bi(s,n,a)|0;l=l<<16>>16>s<<16>>16?s:l;f=vi(r)|0;s=f<<16>>16;if(f<<16>>16<0){n=0-s<<16;if((n|0)<983040)s=r<<16>>16>>(n>>16)&65535;else s=0}else{n=r<<16>>16;i=n<<s;if((i<<16>>16>>s|0)==(n|0))s=i&65535;else s=(n>>>15^32767)&65535}t=Ze((Gt(16383,s)|0)<<16>>16,l<<16>>16)|0;if((t|0)==1073741824){We[a>>2]=1;i=2147483647}else i=t<<1;t=Bi(20,f,a)|0;s=t<<16>>16;if(t<<16>>16>0)t=t<<16>>16<31?i>>s:0;else{r=0-s<<16>>16;t=i<<r;t=(t>>r|0)==(i|0)?t:i>>31^2147483647}t=(t|0)>32767?32767:t&65535;t=o<<16>>16!=0&t<<16>>16>3072?3072:t<<16>>16;n=0;do{i=e+(n<<1)|0;s=Ze(Ve[i>>1]|0,t)|0;if((s|0)==1073741824){We[a>>2]=1;s=2147483647}else s=s<<1;Ve[i>>1]=s>>>11;n=n+1|0}while((n|0)!=40);return 0}function He(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0;i=We[t+104>>2]|0;o=We[t+96>>2]|0;if(e>>>0>=8){Ve[n>>1]=(l[r>>0]|0)>>>4&1;Ve[n+2>>1]=(l[r>>0]|0)>>>5&1;Ve[n+4>>1]=(l[r>>0]|0)>>>6&1;Ve[n+6>>1]=(l[r>>0]|0)>>>7&255;i=i+(e<<1)|0;if((Ve[i>>1]|0)>1){e=1;t=1;o=4}else return;while(1){a=r+e|0;e=o|1;Ve[n+(o<<16>>16<<1)>>1]=l[a>>0]&1;Ve[n+(e<<16>>16<<1)>>1]=(l[a>>0]|0)>>>1&1;s=o|3;Ve[n+(e+1<<16>>16<<16>>16<<1)>>1]=(l[a>>0]|0)>>>2&1;Ve[n+(s<<16>>16<<1)>>1]=(l[a>>0]|0)>>>3&1;Ve[n+(s+1<<16>>16<<16>>16<<1)>>1]=(l[a>>0]|0)>>>4&1;Ve[n+(s+2<<16>>16<<16>>16<<1)>>1]=(l[a>>0]|0)>>>5&1;Ve[n+(s+3<<16>>16<<16>>16<<1)>>1]=(l[a>>0]|0)>>>6&1;Ve[n+(s+4<<16>>16<<16>>16<<1)>>1]=(l[a>>0]|0)>>>7&255;t=t+1<<16>>16;if(t<<16>>16<(Ve[i>>1]|0)){e=t<<16>>16;o=o+8<<16>>16}else break}return}s=We[(We[t+100>>2]|0)+(e<<2)>>2]|0;Ve[n+(Ve[s>>1]<<1)>>1]=(l[r>>0]|0)>>>4&1;Ve[n+(Ve[s+2>>1]<<1)>>1]=(l[r>>0]|0)>>>5&1;Ve[n+(Ve[s+4>>1]<<1)>>1]=(l[r>>0]|0)>>>6&1;Ve[n+(Ve[s+6>>1]<<1)>>1]=(l[r>>0]|0)>>>7&255;a=i+(e<<1)|0;if((Ve[a>>1]|0)<=1)return;t=o+(e<<1)|0;i=1;e=1;o=4;while(1){i=r+i|0;o=o<<16>>16;if((o|0)<(Ve[t>>1]|0)){Ve[n+(Ve[s+(o<<1)>>1]<<1)>>1]=l[i>>0]&1;o=o+1|0;if((o|0)<(Ve[t>>1]|0)){Ve[n+(Ve[s+(o<<1)>>1]<<1)>>1]=(l[i>>0]|0)>>>1&1;o=o+1|0;if((o|0)<(Ve[t>>1]|0)){Ve[n+(Ve[s+(o<<1)>>1]<<1)>>1]=(l[i>>0]|0)>>>2&1;o=o+1|0;if((o|0)<(Ve[t>>1]|0)){Ve[n+(Ve[s+(o<<1)>>1]<<1)>>1]=(l[i>>0]|0)>>>3&1;o=o+1|0;if((o|0)<(Ve[t>>1]|0)){Ve[n+(Ve[s+(o<<1)>>1]<<1)>>1]=(l[i>>0]|0)>>>4&1;o=o+1|0;if((o|0)<(Ve[t>>1]|0)){Ve[n+(Ve[s+(o<<1)>>1]<<1)>>1]=(l[i>>0]|0)>>>5&1;o=o+1|0;if((o|0)<(Ve[t>>1]|0)){Ve[n+(Ve[s+(o<<1)>>1]<<1)>>1]=(l[i>>0]|0)>>>6&1;o=o+1|0;if((o|0)<(Ve[t>>1]|0)){Ve[n+(Ve[s+(o<<1)>>1]<<1)>>1]=(l[i>>0]|0)>>>7&1;o=o+1|0}}}}}}}}e=e+1<<16>>16;if(e<<16>>16<(Ve[a>>1]|0))i=e<<16>>16;else break}return}function Fr(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0,a=0,s=0,l=0;switch(n<<16>>16){case 0:{l=9;while(1){s=Ve[e+(l<<1)>>1]|0;n=s<<16>>16;if(s<<16>>16<0)n=~((n^-4)>>2);else n=n>>>2;a=Ve[r+(l<<1)>>1]|0;o=a<<16>>16;if(a<<16>>16<0)a=~((o^-4)>>2);else a=o>>>2;Ve[t+(l<<1)>>1]=Wt((s&65535)-n&65535,a&65535,i)|0;if((l|0)>0)l=l+-1|0;else break}return}case 40:{a=9;while(1){i=Ve[e+(a<<1)>>1]|0;n=i<<16>>16;if(i<<16>>16<0)o=~((n^-2)>>1);else o=n>>>1;i=Ve[r+(a<<1)>>1]|0;n=i<<16>>16;if(i<<16>>16<0)n=~((n^-2)>>1);else n=n>>>1;Ve[t+(a<<1)>>1]=n+o;if((a|0)>0)a=a+-1|0;else break}return}case 80:{l=9;while(1){s=Ve[e+(l<<1)>>1]|0;n=s<<16>>16;if(s<<16>>16<0)s=~((n^-4)>>2);else s=n>>>2;n=Ve[r+(l<<1)>>1]|0;o=n<<16>>16;if(n<<16>>16<0)a=~((o^-4)>>2);else a=o>>>2;Ve[t+(l<<1)>>1]=Wt(s&65535,(n&65535)-a&65535,i)|0;if((l|0)>0)l=l+-1|0;else break}return}case 120:{Ve[t+18>>1]=Ve[r+18>>1]|0;Ve[t+16>>1]=Ve[r+16>>1]|0;Ve[t+14>>1]=Ve[r+14>>1]|0;Ve[t+12>>1]=Ve[r+12>>1]|0;Ve[t+10>>1]=Ve[r+10>>1]|0;Ve[t+8>>1]=Ve[r+8>>1]|0;Ve[t+6>>1]=Ve[r+6>>1]|0;Ve[t+4>>1]=Ve[r+4>>1]|0;Ve[t+2>>1]=Ve[r+2>>1]|0;Ve[t>>1]=Ve[r>>1]|0;return}default:return}}function ze(e,r){e=e|0;r=r|0;if(!e){e=-1;return e|0}Ki(e|0,r|0,20)|0;e=0;return e|0}function Mr(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0,a=0,s=0,l=0,f=0;f=0;do{l=e+(f<<1)|0;t=Ve[l>>1]|0;a=t&65535;s=a<<16;t=t<<16>>16;if((t*5243|0)==1073741824){We[n>>2]=1;o=2147483647}else o=t*10486|0;i=s-o|0;if(((i^s)&(o^s)|0)<0){We[n>>2]=1;o=(a>>>15)+2147483647|0}else o=i;t=Ve[r+(f<<1)>>1]|0;i=t*5243|0;if((i|0)!=1073741824){t=(t*10486|0)+o|0;if((i^o|0)>0&(t^o|0)<0){We[n>>2]=1;t=(o>>>31)+2147483647|0}}else{We[n>>2]=1;t=2147483647}Ve[l>>1]=Ni(t,n)|0;f=f+1|0}while((f|0)!=10);return}function Ye(e){e=e|0;var r=0;if(!e){r=-1;return r|0}r=e+18|0;do{Ve[e>>1]=0;e=e+2|0}while((e|0)<(r|0));r=0;return r|0}function gr(e){e=e|0;Ve[e+14>>1]=1;return}function Rr(e){e=e|0;Ve[e+14>>1]=0;return}function Ar(e,r,n,t,i,o,a,s,l,f){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;var u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0;g=Ge;Ge=Ge+160|0;k=g+80|0;F=g;S=We[l+120>>2]|0;b=We[l+124>>2]|0;v=We[l+128>>2]|0;p=We[l+132>>2]|0;c=e+6|0;E=e+8|0;Ve[E>>1]=Ve[c>>1]|0;w=e+4|0;Ve[c>>1]=Ve[w>>1]|0;m=e+2|0;Ve[w>>1]=Ve[m>>1]|0;Ve[m>>1]=Ve[e>>1]|0;Ve[e>>1]=i;l=i<<16>>16<14746?i<<16>>16>9830&1:2;u=e+12|0;i=Ve[u>>1]|0;d=i<<15;do{if((d|0)<=536870911)if((d|0)<-536870912){We[f>>2]=1;i=-2147483648;break}else{i=i<<17;break}else{We[f>>2]=1;i=2147483647}}while(0);_=t<<16>>16;h=e+16|0;if((Ni(i,f)|0)<<16>>16>=t<<16>>16){d=Ve[h>>1]|0;if(d<<16>>16>0){d=(d&65535)+65535&65535;Ve[h>>1]=d}if(!(d<<16>>16)){i=(Ve[e>>1]|0)<9830;i=(Ve[m>>1]|0)<9830?i?2:1:i&1;if((Ve[w>>1]|0)<9830)i=(i&65535)+1&65535;if((Ve[c>>1]|0)<9830)i=(i&65535)+1&65535;if((Ve[E>>1]|0)<9830)i=(i&65535)+1&65535;d=0;l=i<<16>>16>2?0:l}}else{Ve[h>>1]=2;d=2}m=l<<16>>16;E=e+10|0;m=(d<<16>>16==0?(m|0)>((Ve[E>>1]|0)+1|0):0)?m+65535&65535:l;e=(Ve[e+14>>1]|0)==1?0:t<<16>>16<10?2:m<<16>>16<2&d<<16>>16>0?(m&65535)+1&65535:m;Ve[E>>1]=e;Ve[u>>1]=t;switch(r|0){case 4:case 6:case 7:break;default:if(e<<16>>16<2){d=0;l=0;c=o;u=k;while(1){if(!(Ve[c>>1]|0))i=0;else{l=l<<16>>16;Ve[F+(l<<1)>>1]=d;i=Ve[c>>1]|0;l=l+1&65535}Ve[u>>1]=i;Ve[c>>1]=0;d=d+1<<16>>16;if(d<<16>>16>=40){E=l;break}else{c=c+2|0;u=u+2|0}}m=e<<16>>16==0;m=(r|0)==5?m?S:b:m?v:p;if(E<<16>>16>0){w=0;do{h=Ve[F+(w<<1)>>1]|0;l=h<<16>>16;e=Ve[k+(l<<1)>>1]|0;if(h<<16>>16<40){d=e<<16>>16;c=39-h&65535;u=h;l=o+(l<<1)|0;i=m;while(1){r=(Ze(Ve[i>>1]|0,d)|0)>>>15&65535;Ve[l>>1]=Wt(Ve[l>>1]|0,r,f)|0;u=u+1<<16>>16;if(u<<16>>16>=40)break;else{l=l+2|0;i=i+2|0}}if(h<<16>>16>0){l=m+(c+1<<1)|0;M=36}}else{l=m;M=36}if((M|0)==36){M=0;i=e<<16>>16;d=0;c=o;while(1){r=(Ze(Ve[l>>1]|0,i)|0)>>>15&65535;Ve[c>>1]=Wt(Ve[c>>1]|0,r,f)|0;d=d+1<<16>>16;if(d<<16>>16>=h<<16>>16)break;else{c=c+2|0;l=l+2|0}}}w=w+1|0}while((w&65535)<<16>>16!=E<<16>>16)}}}w=a<<16>>16;m=_<<1;i=s<<16>>16;u=0-i<<16;l=u>>16;if(s<<16>>16>0){d=0;c=n;while(1){e=Ze(Ve[n+(d<<1)>>1]|0,w)|0;if((e|0)==1073741824){We[f>>2]=1;u=2147483647}else u=e<<1;s=Ze(m,Ve[o>>1]|0)|0;e=s+u|0;if((s^u|0)>-1&(e^u|0)<0){We[f>>2]=1;e=(u>>>31)+2147483647|0}s=e<<i;Ve[c>>1]=Ni((s>>i|0)==(e|0)?s:e>>31^2147483647,f)|0;d=d+1|0;if((d|0)==40)break;else{o=o+2|0;c=c+2|0}}Ge=g;return}if((u|0)<2031616){d=0;c=n;while(1){e=Ze(Ve[n+(d<<1)>>1]|0,w)|0;if((e|0)==1073741824){We[f>>2]=1;u=2147483647}else u=e<<1;s=Ze(m,Ve[o>>1]|0)|0;e=s+u|0;if((s^u|0)>-1&(e^u|0)<0){We[f>>2]=1;e=(u>>>31)+2147483647|0}Ve[c>>1]=Ni(e>>l,f)|0;d=d+1|0;if((d|0)==40)break;else{o=o+2|0;c=c+2|0}}Ge=g;return}else{c=0;u=n;while(1){e=Ze(Ve[n+(c<<1)>>1]|0,w)|0;if((e|0)==1073741824){We[f>>2]=1;e=2147483647}else e=e<<1;s=Ze(m,Ve[o>>1]|0)|0;if((s^e|0)>-1&(s+e^e|0)<0)We[f>>2]=1;Ve[u>>1]=Ni(0,f)|0;c=c+1|0;if((c|0)==40)break;else{o=o+2|0;u=u+2|0}}Ge=g;return}}function je(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=0;Ve[e+2>>1]=0;Ve[e+4>>1]=0;Ve[e+6>>1]=0;Ve[e+8>>1]=0;Ve[e+10>>1]=0;e=0;return e|0}function qe(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0;if(n<<16>>16<=0)return;i=e+10|0;l=e+8|0;u=e+4|0;c=e+6|0;d=e+2|0;o=Ve[u>>1]|0;a=Ve[c>>1]|0;s=Ve[e>>1]|0;f=Ve[d>>1]|0;h=0;while(1){w=Ve[i>>1]|0;m=Ve[l>>1]|0;Ve[i>>1]=m;E=Ve[r>>1]|0;Ve[l>>1]=E;w=((E<<16>>16)*7699|0)+((Ze(s<<16>>16,-7667)|0)+(((o<<16>>16)*15836|0)+((a<<16>>16)*15836>>15))+((Ze(f<<16>>16,-7667)|0)>>15))+(Ze(m<<16>>16,-15398)|0)+((w<<16>>16)*7699|0)|0;m=w<<3;w=(m>>3|0)==(w|0)?m:w>>31^2147483647;m=w<<1;Ve[r>>1]=Ni((m>>1|0)==(w|0)?m:w>>31^2147483647,t)|0;s=Ve[u>>1]|0;Ve[e>>1]=s;f=Ve[c>>1]|0;Ve[d>>1]=f;o=w>>>16&65535;Ve[u>>1]=o;a=(w>>>1)-(w>>16<<15)&65535;Ve[c>>1]=a;h=h+1<<16>>16;if(h<<16>>16>=n<<16>>16)break;else r=r+2|0}return}function yr(e){e=e|0;if(!e)e=-1;else{Ve[e>>1]=0;e=0}return e|0}function Or(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0,a=0,s=0,l=0;s=t<<16>>16;o=r+(s+-1<<1)|0;s=s+-2|0;l=Ve[o>>1]|0;if(t<<16>>16<2)t=n<<16>>16;else{t=n<<16>>16;a=0;r=r+(s<<1)|0;while(1){n=(Ze(Ve[r>>1]|0,t)|0)>>15;if((n|0)>32767){We[i>>2]=1;n=32767}Ve[o>>1]=Bi(Ve[o>>1]|0,n&65535,i)|0;o=o+-2|0;a=a+1<<16>>16;if((a<<16>>16|0)>(s|0))break;else r=r+-2|0}}t=(Ze(Ve[e>>1]|0,t)|0)>>15;if((t|0)<=32767){s=t;s=s&65535;a=Ve[o>>1]|0;i=Bi(a,s,i)|0;Ve[o>>1]=i;Ve[e>>1]=l;return}We[i>>2]=1;s=32767;s=s&65535;a=Ve[o>>1]|0;i=Bi(a,s,i)|0;Ve[o>>1]=i;Ve[e>>1]=l;return}function Tr(e){e=e|0;var r=0,n=0,t=0;if(!e){t=-1;return t|0}Vi(e+104|0,0,340)|0;r=e+102|0;n=e;t=n+100|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));Me(r)|0;yr(e+100|0)|0;t=0;return t|0}function Dr(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0;S=Ge;Ge=Ge+96|0;w=S+22|0;m=S;E=S+44|0;Ki(e+124|0,n|0,320)|0;u=E+22|0;c=e+100|0;d=e+80|0;h=e+102|0;if((r&-2|0)==6){f=0;while(1){Ui(t,702,w);Ui(t,722,m);l=e+104+(f+10<<1)|0;Di(w,l,e,40);a=E;o=w;r=a+22|0;do{Ve[a>>1]=Ve[o>>1]|0;a=a+2|0;o=o+2|0}while((a|0)<(r|0));a=u;r=a+22|0;do{Ve[a>>1]=0;a=a+2|0}while((a|0)<(r|0));Li(m,E,E,22,u,0);r=0;a=21;do{o=Ve[E+(a<<16>>16<<1)>>1]|0;o=Ze(o,o)|0;if((o|0)==1073741824){p=7;break}s=o<<1;o=s+r|0;if((s^r|0)>-1&(o^r|0)<0){We[i>>2]=1;r=(r>>>31)+2147483647|0}else r=o;a=a+-1<<16>>16}while(a<<16>>16>-1);if((p|0)==7){p=0;We[i>>2]=1}s=r>>>16&65535;o=20;r=0;a=20;while(1){o=Ze(Ve[E+(o+1<<1)>>1]|0,Ve[E+(o<<1)>>1]|0)|0;if((o|0)==1073741824){p=13;break}b=o<<1;o=b+r|0;if((b^r|0)>-1&(o^r|0)<0){We[i>>2]=1;r=(r>>>31)+2147483647|0}else r=o;o=(a&65535)+-1<<16>>16;if(o<<16>>16>-1){o=o<<16>>16;a=a+-1|0}else break}if((p|0)==13){p=0;We[i>>2]=1}r=r>>16;if((r|0)<1)r=0;else r=Gt((r*26214|0)>>>15&65535,s)|0;Or(c,e,r,40,i);r=n+(f<<1)|0;Li(m,e,r,40,d,1);ge(h,l,r,29491,40,i);r=(f<<16)+2621440|0;if((r|0)<10485760){f=r>>16;t=t+22|0}else break}a=e+104|0;o=e+424|0;r=a+20|0;do{Ke[a>>0]=Ke[o>>0]|0;a=a+1|0;o=o+1|0}while((a|0)<(r|0));Ge=S;return}else{f=0;while(1){Ui(t,742,w);Ui(t,762,m);l=e+104+(f+10<<1)|0;Di(w,l,e,40);a=E;o=w;r=a+22|0;do{Ve[a>>1]=Ve[o>>1]|0;a=a+2|0;o=o+2|0}while((a|0)<(r|0));a=u;r=a+22|0;do{Ve[a>>1]=0;a=a+2|0}while((a|0)<(r|0));Li(m,E,E,22,u,0);r=0;a=21;do{o=Ve[E+(a<<16>>16<<1)>>1]|0;o=Ze(o,o)|0;if((o|0)==1073741824){p=22;break}b=o<<1;o=b+r|0;if((b^r|0)>-1&(o^r|0)<0){We[i>>2]=1;r=(r>>>31)+2147483647|0}else r=o;a=a+-1<<16>>16}while(a<<16>>16>-1);if((p|0)==22){p=0;We[i>>2]=1}s=r>>>16&65535;o=20;r=0;a=20;while(1){o=Ze(Ve[E+(o+1<<1)>>1]|0,Ve[E+(o<<1)>>1]|0)|0;if((o|0)==1073741824){p=28;break}b=o<<1;o=b+r|0;if((b^r|0)>-1&(o^r|0)<0){We[i>>2]=1;r=(r>>>31)+2147483647|0}else r=o;o=(a&65535)+-1<<16>>16;if(o<<16>>16>-1){o=o<<16>>16;a=a+-1|0}else break}if((p|0)==28){p=0;We[i>>2]=1}r=r>>16;if((r|0)<1)r=0;else r=Gt((r*26214|0)>>>15&65535,s)|0;Or(c,e,r,40,i);r=n+(f<<1)|0;Li(m,e,r,40,d,1);ge(h,l,r,29491,40,i);r=(f<<16)+2621440|0;if((r|0)<10485760){f=r>>16;t=t+22|0}else break}a=e+104|0;o=e+424|0;r=a+20|0;do{Ke[a>>0]=Ke[o>>0]|0;a=a+1|0;o=o+1|0}while((a|0)<(r|0));Ge=S;return}}function Nr(e,r){e=e|0;r=r|0;var n=0,t=0;if(!e){e=-1;return e|0}We[e>>2]=0;n=xi(1764)|0;if(!n){e=-1;return e|0}if((Pe(n)|0)<<16>>16==0?(t=n+1748|0,(je(t)|0)<<16>>16==0):0){sr(n,0)|0;Tr(n+1304|0)|0;je(t)|0;We[n+1760>>2]=0;We[e>>2]=n;e=0;return e|0}r=We[n>>2]|0;if(!r){e=-1;return e|0}Hi(r);We[n>>2]=0;e=-1;return e|0}function Pr(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Hi(r);We[e>>2]=0;return}function Cr(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0;p=Ge;Ge=Ge+208|0;E=p+88|0;m=p;w=e+1164|0;o=We[e+1256>>2]|0;if((t+-5|0)>>>0<2){h=o+16|0;if((Ve[h>>1]|0)>0){d=We[(We[e+1260>>2]|0)+32>>2]|0;c=0;o=0;while(1){u=d+(c<<1)|0;l=Ve[u>>1]|0;if(l<<16>>16>0){s=n;f=0;a=0;while(1){a=Xe[s>>1]|a<<1&131070;f=f+1<<16>>16;if(f<<16>>16>=l<<16>>16)break;else s=s+2|0}a=a&65535}else a=0;Ve[E+(c<<1)>>1]=a;o=o+1<<16>>16;if(o<<16>>16<(Ve[h>>1]|0)){n=n+(Ve[u>>1]<<1)|0;c=o<<16>>16}else break}}}else{d=o+(r<<1)|0;if((Ve[d>>1]|0)>0){h=We[(We[e+1260>>2]|0)+(r<<2)>>2]|0;u=0;o=0;while(1){c=h+(u<<1)|0;l=Ve[c>>1]|0;if(l<<16>>16>0){s=n;f=0;a=0;while(1){a=Xe[s>>1]|a<<1&131070;f=f+1<<16>>16;if(f<<16>>16>=l<<16>>16)break;else s=s+2|0}a=a&65535}else a=0;Ve[E+(u<<1)>>1]=a;o=o+1<<16>>16;if(o<<16>>16<(Ve[d>>1]|0)){n=n+(Ve[c>>1]<<1)|0;u=o<<16>>16}else break}}}Ce(e,r,E,t,i,m);Dr(e+1304|0,r,i,m,w);qe(e+1748|0,i,160,w);o=0;do{e=i+(o<<1)|0;Ve[e>>1]=Xe[e>>1]&65528;o=o+1|0}while((o|0)!=160);Ge=p;return}function Ir(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0;o=We[t+100>>2]|0;a=(Xe[(We[t+96>>2]|0)+(e<<1)>>1]|0)+65535|0;t=a&65535;i=t<<16>>16>-1;if(e>>>0<8){if(!i)return;o=We[o+(e<<2)>>2]|0;i=a<<16>>16;while(1){Ve[n+(Ve[o+(i<<1)>>1]<<1)>>1]=(l[r+(i>>3)>>0]|0)>>>(i&7^7)&1;t=t+-1<<16>>16;if(t<<16>>16>-1)i=t<<16>>16;else break}return}else{if(!i)return;i=a<<16>>16;while(1){Ve[n+(i<<1)>>1]=(l[r+(i>>3)>>0]|0)>>>(i&7^7)&1;t=t+-1<<16>>16;if(t<<16>>16>-1)i=t<<16>>16;else break}return}}function Br(e,r,n){e=e|0;r=r|0;n=n|0;e=gt(e,n,31764)|0;return((kt(r)|0|e)<<16>>16!=0)<<31>>31|0}function Lr(e,r){e=e|0;r=r|0;Rt(e);Ft(r);return}function Ur(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;var s=0,l=0,f=0,u=0,c=0;c=Ge;Ge=Ge+512|0;s=c+8|0;l=c+4|0;f=c;We[f>>2]=0;u=a<<16>>16==3;if(!((a&65535)<2|u&1)){if(a<<16>>16!=2){i=-1;Ge=c;return i|0}At(e,n,t,s+2|0,f);e=We[f>>2]|0;We[o>>2]=e;Mt(r,e,l);r=We[l>>2]|0;Ve[s>>1]=r;Ve[s+490>>1]=(r|0)==3?-1:n&65535;Ke[i>>0]=r;r=1;do{s=s+1|0;Ke[i+r>>0]=Ke[s>>0]|0;r=r+1|0}while((r|0)!=492);s=492;Ge=c;return s|0}At(e,n,t,s,f);Mt(r,We[f>>2]|0,l);t=We[l>>2]|0;if((t|0)!=3){r=We[f>>2]|0;We[o>>2]=r;if((r|0)==8){switch(t|0){case 1:{Ve[s+70>>1]=0;break}case 2:{f=s+70|0;Ve[f>>1]=Xe[f>>1]|0|1;break}default:{}}Ve[s+72>>1]=n&1;Ve[s+74>>1]=n>>>1&1;Ve[s+76>>1]=n>>>2&1;r=8}}else{We[o>>2]=15;r=15}if(u){Fn(r,s,i,(We[e+4>>2]|0)+2392|0);i=Ve[3404+(We[o>>2]<<16>>16<<1)>>1]|0;Ge=c;return i|0}switch(a<<16>>16){case 0:{kn(r,s,i,(We[e+4>>2]|0)+2392|0);i=Ve[3404+(We[o>>2]<<16>>16<<1)>>1]|0;Ge=c;return i|0}case 1:{_n(r,s,i,(We[e+4>>2]|0)+2392|0);i=Ve[3436+(We[o>>2]<<16>>16<<1)>>1]|0;Ge=c;return i|0}default:{i=-1;Ge=c;return i|0}}return 0}function xr(e,r,n,t,i,o){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;var a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0;k=Ge;Ge=Ge+480|0;_=k;o=240;f=i;l=e;s=_;a=0;while(1){v=((Ze(Ve[f>>1]|0,Ve[l>>1]|0)|0)+16384|0)>>>15;Ve[s>>1]=v;v=v<<16;a=(Ze(v>>15,v>>16)|0)+a|0;if((a|0)<0){u=4;break}o=o+-1|0;if(!((o&65535)<<16>>16)){o=0;break}else{f=f+2|0;l=l+2|0;s=s+2|0}}if((u|0)==4){a=o&65535;s=240-o|0;if(!(a<<16>>16))o=0;else{f=a;l=i+(s<<1)|0;o=e+(s<<1)|0;a=_+(s<<1)|0;while(1){Ve[a>>1]=((Ze(Ve[l>>1]|0,Ve[o>>1]|0)|0)+16384|0)>>>15;f=f+-1<<16>>16;if(!(f<<16>>16)){o=0;break}else{l=l+2|0;o=o+2|0;a=a+2|0}}}do{l=o&65535;o=120;s=_;a=0;while(1){v=(Ve[s>>1]|0)>>>2;S=s+2|0;Ve[s>>1]=v;v=v<<16>>16;v=Ze(v,v)|0;b=(Ve[S>>1]|0)>>>2;Ve[S>>1]=b;b=b<<16>>16;a=((Ze(b,b)|0)+v<<1)+a|0;o=o+-1<<16>>16;if(!(o<<16>>16))break;else s=s+4|0}o=l+4|0}while((a|0)<1)}v=a+1|0;b=(bi(v)|0)<<16>>16;v=v<<b;Ve[n>>1]=v>>>16;Ve[t>>1]=(v>>>1)-(v>>16<<15);v=_+478|0;f=r<<16>>16;if(r<<16>>16<=0){_=b-o|0;_=_&65535;Ge=k;return _|0}m=_+476|0;E=b+1|0;p=239-f|0;S=_+(236-f<<1)|0;r=f;n=n+(f<<1)|0;t=t+(f<<1)|0;while(1){u=Ze((p>>>1)+65535&65535,-2)|0;l=_+(u+236<<1)|0;u=S+(u<<1)|0;i=240-r|0;w=i+-1|0;s=_+(w<<1)|0;e=w>>>1&65535;i=_+(i+-2<<1)|0;f=Ze(Ve[v>>1]|0,Ve[s>>1]|0)|0;if(!(e<<16>>16)){u=i;l=m}else{h=m;d=v;while(1){a=s+-4|0;c=d+-4|0;f=(Ze(Ve[h>>1]|0,Ve[i>>1]|0)|0)+f|0;e=e+-1<<16>>16;f=(Ze(Ve[c>>1]|0,Ve[a>>1]|0)|0)+f|0;if(!(e<<16>>16))break;else{i=s+-6|0;h=d+-6|0;s=a;d=c}}}if(w&1)f=(Ze(Ve[l>>1]|0,Ve[u>>1]|0)|0)+f|0;w=f<<E;Ve[n>>1]=w>>>16;Ve[t>>1]=(w>>>1)-(w>>16<<15);if((r&65535)+-1<<16>>16<<16>>16>0){p=p+1|0;S=S+2|0;r=r+-1|0;n=n+-2|0;t=t+-2|0}else break}_=b-o|0;_=_&65535;Ge=k;return _|0}function Hr(e,r,n,t,i,o,a,s){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;var l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0;A=Ge;Ge=Ge+3440|0;R=A+3420|0;k=A+3400|0;F=A+3224|0;g=A;v=A+3320|0;M=A+3240|0;_=A+24|0;cn(n,e,v,2,s);_t(v,r,M,F,5,k,5,s);fn(n,M,_,s);bt(10,5,5,v,_,k,F,g,s);r=t;s=r+80|0;do{Ve[r>>1]=0;r=r+2|0}while((r|0)<(s|0));Ve[o>>1]=65535;Ve[o+2>>1]=65535;Ve[o+4>>1]=65535;Ve[o+6>>1]=65535;Ve[o+8>>1]=65535;d=0;h=g;w=R;do{e=Ve[h>>1]|0;h=h+2|0;l=(e*6554|0)>>>15;f=l<<16>>16;r=t+(e<<1)|0;s=Ve[r>>1]|0;if((Ve[M+(e<<1)>>1]|0)>0){Ve[r>>1]=s+4096;Ve[w>>1]=8192;u=l}else{Ve[r>>1]=s+61440;Ve[w>>1]=-8192;u=f+8|0}w=w+2|0;c=u&65535;r=e-(l<<2)-f<<16>>16;l=o+(r<<1)|0;s=Ve[l>>1]|0;e=s<<16>>16;do{if(s<<16>>16>=0){f=u<<16>>16;if(!((f^e)&8)){r=o+(r+5<<1)|0;if((e|0)>(f|0)){Ve[r>>1]=s;Ve[l>>1]=c;break}else{Ve[r>>1]=c;break}}else{r=o+(r+5<<1)|0;if((e&7)>>>0>(f&7)>>>0){Ve[r>>1]=c;break}else{Ve[r>>1]=s;Ve[l>>1]=c;break}}}else Ve[l>>1]=c}while(0);d=d+1<<16>>16}while(d<<16>>16<10);w=R+2|0;d=R+4|0;u=R+6|0;f=R+8|0;l=R+10|0;r=R+12|0;s=R+14|0;e=R+16|0;m=R+18|0;E=40;p=n+(0-(Ve[g>>1]|0)<<1)|0;S=n+(0-(Ve[g+2>>1]|0)<<1)|0;b=n+(0-(Ve[g+4>>1]|0)<<1)|0;v=n+(0-(Ve[g+6>>1]|0)<<1)|0;_=n+(0-(Ve[g+8>>1]|0)<<1)|0;k=n+(0-(Ve[g+10>>1]|0)<<1)|0;F=n+(0-(Ve[g+12>>1]|0)<<1)|0;M=n+(0-(Ve[g+14>>1]|0)<<1)|0;t=n+(0-(Ve[g+16>>1]|0)<<1)|0;h=n+(0-(Ve[g+18>>1]|0)<<1)|0;c=i;while(1){P=(Ze(Ve[R>>1]|0,Ve[p>>1]|0)|0)>>7;N=(Ze(Ve[w>>1]|0,Ve[S>>1]|0)|0)>>7;D=(Ze(Ve[d>>1]|0,Ve[b>>1]|0)|0)>>7;T=(Ze(Ve[u>>1]|0,Ve[v>>1]|0)|0)>>7;O=(Ze(Ve[f>>1]|0,Ve[_>>1]|0)|0)>>7;y=(Ze(Ve[l>>1]|0,Ve[k>>1]|0)|0)>>7;g=(Ze(Ve[r>>1]|0,Ve[F>>1]|0)|0)>>7;n=(Ze(Ve[s>>1]|0,Ve[M>>1]|0)|0)>>>7;i=(Ze(Ve[e>>1]|0,Ve[t>>1]|0)|0)>>>7;Ve[c>>1]=(P+128+N+D+T+O+y+g+n+i+((Ze(Ve[m>>1]|0,Ve[h>>1]|0)|0)>>>7)|0)>>>8;E=E+-1<<16>>16;if(!(E<<16>>16))break;else{p=p+2|0;S=S+2|0;b=b+2|0;v=v+2|0;_=_+2|0;k=k+2|0;F=F+2|0;M=M+2|0;t=t+2|0;h=h+2|0;c=c+2|0}}r=0;do{s=o+(r<<1)|0;e=Ve[s>>1]|0;if((r|0)<5)e=(Xe[a+((e&7)<<1)>>1]|e&8)&65535;else e=Ve[a+((e&7)<<1)>>1]|0;Ve[s>>1]=e;r=r+1|0}while((r|0)!=10);Ge=A;return}function zr(e,r,n,t,i,o,a,s){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;var l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0,C=0,I=0,B=0;B=Ge;Ge=Ge+3456|0;D=B+3448|0;O=B+3360|0;A=B+3368|0;d=B+3280|0;T=B+3200|0;y=B;P=(t&65535)<<17;I=n<<16>>16;N=n<<16>>16<40;if(N){t=P>>16;n=I;do{f=(Ze(Ve[r+(n-I<<1)>>1]|0,t)|0)>>15;if((f|0)>32767){We[s>>2]=1;f=32767}R=r+(n<<1)|0;Ve[R>>1]=Wt(Ve[R>>1]|0,f&65535,s)|0;n=n+1|0}while((n&65535)<<16>>16!=40)}cn(r,e,A,1,s);vt(A,T,d,8);fn(r,T,y,s);R=O+2|0;Ve[O>>1]=0;Ve[R>>1]=1;e=1;f=0;c=1;d=0;u=-1;do{M=Ve[2830+(d<<1)>>1]|0;g=M<<16>>16;F=0;do{_=Ve[2834+(F<<1)>>1]|0;k=_<<16>>16;v=e;S=g;p=c;b=M;E=u;while(1){l=Ve[A+(S<<1)>>1]|0;w=Ve[y+(S*80|0)+(S<<1)>>1]|0;n=k;c=1;m=_;e=_;u=-1;while(1){t=Wt(l,Ve[A+(n<<1)>>1]|0,s)|0;t=t<<16>>16;t=(Ze(t,t)|0)>>>15;h=(Ve[y+(S*80|0)+(n<<1)>>1]<<15)+32768+((Ve[y+(n*80|0)+(n<<1)>>1]|0)+w<<14)|0;if(((Ze(t<<16>>16,c<<16>>16)|0)-(Ze(h>>16,u<<16>>16)|0)<<1|0)>0){c=h>>>16&65535;e=m;u=t&65535}h=n+5|0;m=h&65535;if(m<<16>>16>=40)break;else n=h<<16>>16}if(((Ze(u<<16>>16,p<<16>>16)|0)-(Ze(c<<16>>16,E<<16>>16)|0)<<1|0)>0){Ve[O>>1]=b;Ve[R>>1]=e;f=b}else{e=v;c=p;u=E}h=S+5|0;b=h&65535;if(b<<16>>16>=40)break;else{v=e;S=h<<16>>16;p=c;E=u}}F=F+1|0}while((F|0)!=4);d=d+1|0}while((d|0)!=2);w=e;m=f;t=i;n=t+80|0;do{Ve[t>>1]=0;t=t+2|0}while((t|0)<(n|0));c=m;n=0;h=0;t=0;while(1){f=c<<16>>16;l=Ve[T+(f<<1)>>1]|0;e=(f*6554|0)>>>15;c=e<<16;d=c>>15;u=f-(d+(e<<3)<<16>>17)|0;switch(u<<16>>16|0){case 0:{d=c>>10;e=1;break}case 1:{if(!((n&65535)<<16>>16))e=0;else{d=e<<22>>16|16;e=1}break}case 2:{d=e<<22>>16|32;e=1;break}case 3:{d=e<<17>>16|1;e=0;break}case 4:{d=e<<22>>16|48;e=1;break}default:{d=e;e=u&65535}}d=d&65535;u=i+(f<<1)|0;if(l<<16>>16>0){Ve[u>>1]=8191;Ve[D+(n<<1)>>1]=32767;f=e<<16>>16;if(e<<16>>16<0){f=0-f<<16;if((f|0)<983040)f=1>>>(f>>16)&65535;else f=0}else{y=1<<f;f=(y<<16>>16>>f|0)==1?y&65535:32767}t=Wt(t,f,s)|0}else{Ve[u>>1]=-8192;Ve[D+(n<<1)>>1]=-32768}f=Wt(h,d,s)|0;n=n+1|0;if((n|0)==2){h=f;break}c=Ve[O+(n<<1)>>1]|0;h=f}Ve[a>>1]=t;d=D+2|0;c=Ve[D>>1]|0;e=0;u=r+(0-(m<<16>>16)<<1)|0;f=r+(0-(w<<16>>16)<<1)|0;do{t=Ze(Ve[u>>1]|0,c)|0;u=u+2|0;if((t|0)!=1073741824?(C=t<<1,!((t|0)>0&(C|0)<0)):0)l=C;else{We[s>>2]=1;l=2147483647}n=Ze(Ve[d>>1]|0,Ve[f>>1]|0)|0;f=f+2|0;if((n|0)!=1073741824){t=(n<<1)+l|0;if((n^l|0)>0&(t^l|0)<0){We[s>>2]=1;t=(l>>>31)+2147483647|0}}else{We[s>>2]=1;t=2147483647}Ve[o+(e<<1)>>1]=Ni(t,s)|0;e=e+1|0}while((e|0)!=40);if(!N){Ge=B;return h|0}n=P>>16;t=I;do{l=(Ze(Ve[i+(t-I<<1)>>1]|0,n)|0)>>15;if((l|0)>32767){We[s>>2]=1;l=32767}o=i+(t<<1)|0;Ve[o>>1]=Wt(Ve[o>>1]|0,l&65535,s)|0;t=t+1|0}while((t&65535)<<16>>16!=40);Ge=B;return h|0}function Yr(e,r,n,t,i,o,a,s,l,f){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;var u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0;v=Ge;Ge=Ge+3456|0;w=v+3360|0;m=v+3368|0;E=v+3280|0;p=v+3200|0;S=v;b=i<<16>>16;d=b<<1;if((d|0)==(b<<17>>16|0))h=d;else{We[f>>2]=1;h=i<<16>>16>0?32767:-32768}b=t<<16>>16;u=t<<16>>16<40;if(u){i=h<<16>>16;c=b;do{t=n+(c<<1)|0;d=(Ze(Ve[n+(c-b<<1)>>1]|0,i)|0)>>15;if((d|0)>32767){We[f>>2]=1;d=32767}Ve[t>>1]=Wt(Ve[t>>1]|0,d&65535,f)|0;c=c+1|0}while((c&65535)<<16>>16!=40)}cn(n,r,m,1,f);vt(m,p,E,8);fn(n,p,S,f);jr(e,m,S,l,w);d=qr(e,w,p,o,n,a,s,f)|0;if(!u){Ge=v;return d|0}c=h<<16>>16;i=b;do{t=o+(i<<1)|0;u=(Ze(Ve[o+(i-b<<1)>>1]|0,c)|0)>>15;if((u|0)>32767){We[f>>2]=1;u=32767}Ve[t>>1]=Wt(Ve[t>>1]|0,u&65535,f)|0;i=i+1|0}while((i&65535)<<16>>16!=40);Ge=v;return d|0}function jr(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0;_=i+2|0;Ve[i>>1]=0;Ve[_>>1]=1;b=e<<16>>16<<1;o=1;v=0;e=-1;do{S=(v<<3)+b<<16>>16;l=Ve[t+(S<<1)>>1]|0;S=Ve[t+((S|1)<<1)>>1]|0;a=l<<16>>16;e:do{if(l<<16>>16<40){p=S<<16>>16;if(S<<16>>16<40)E=o;else while(1){if((e<<16>>16|0)<(0-(o<<16>>16)|0)){Ve[i>>1]=l;Ve[_>>1]=S;s=1;e=-1}else s=o;o=a+5|0;l=o&65535;if(l<<16>>16>=40){o=s;break e}else{a=o<<16>>16;o=s}}while(1){w=Ve[n+(a*80|0)+(a<<1)>>1]|0;h=Xe[r+(a<<1)>>1]|0;d=p;o=1;m=S;s=S;f=-1;while(1){c=(Xe[r+(d<<1)>>1]|0)+h<<16>>16;c=(Ze(c,c)|0)>>>15;u=(Ve[n+(a*80|0)+(d<<1)>>1]<<15)+32768+((Ve[n+(d*80|0)+(d<<1)>>1]|0)+w<<14)|0;if(((Ze(c<<16>>16,o<<16>>16)|0)-(Ze(u>>16,f<<16>>16)|0)<<1|0)>0){o=u>>>16&65535;s=m;f=c&65535}u=d+5|0;m=u&65535;if(m<<16>>16>=40)break;else d=u<<16>>16}if(((Ze(f<<16>>16,E<<16>>16)|0)-(Ze(o<<16>>16,e<<16>>16)|0)<<1|0)>0){Ve[i>>1]=l;Ve[_>>1]=s;e=f}else o=E;a=a+5|0;l=a&65535;if(l<<16>>16>=40)break;else{a=a<<16>>16;E=o}}}}while(0);v=v+1|0}while((v|0)!=2);return}function qr(e,r,n,t,i,o,a,s){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;var l=0,f=0,u=0,c=0,d=0,h=0;l=t;f=l+80|0;do{Ve[l>>1]=0;l=l+2|0}while((l|0)<(f|0));l=Ve[r>>1]|0;d=(l*6554|0)>>>15;f=d<<16>>16;c=(748250>>>((l+(Ze(f,-5)|0)<<16>>16)+((e<<16>>16)*5|0)|0)&1|0)==0;u=(Ve[n+(l<<1)>>1]|0)>0;h=u?32767:-32768;Ve[t+(l<<1)>>1]=u?8191:-8192;l=r+2|0;e=Ve[l>>1]|0;t=t+(e<<1)|0;if((Ve[n+(e<<1)>>1]|0)>0){Ve[t>>1]=8191;n=32767;t=(u&1|2)&65535}else{Ve[t>>1]=-8192;n=-32768;t=u&1}d=((e*6554|0)>>>15<<3)+(c?d:f+64|0)&65535;Ve[a>>1]=t;c=0;u=i+(0-(Ve[r>>1]|0)<<1)|0;t=i+(0-(Ve[l>>1]|0)<<1)|0;do{l=Ze(h,Ve[u>>1]|0)|0;u=u+2|0;if((l|0)==1073741824){We[s>>2]=1;e=2147483647}else e=l<<1;f=Ze(n,Ve[t>>1]|0)|0;t=t+2|0;if((f|0)!=1073741824){l=(f<<1)+e|0;if((f^e|0)>0&(l^e|0)<0){We[s>>2]=1;l=(e>>>31)+2147483647|0}}else{We[s>>2]=1;l=2147483647}Ve[o+(c<<1)>>1]=Ni(l,s)|0;c=c+1|0}while((c|0)!=40);return d|0}function Kr(e,r,n,t,i,o,a,s){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;var l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0,C=0,I=0,B=0,L=0,U=0,x=0,H=0,z=0,Y=0;Y=Ge;Ge=Ge+3440|0;C=Y+3360|0;I=Y+3280|0;L=Y+3200|0;B=Y;x=(t&65535)<<17;z=n<<16>>16;U=n<<16>>16<40;if(U){n=x>>16;l=z;do{t=(Ze(Ve[r+(l-z<<1)>>1]|0,n)|0)>>15;if((t|0)>32767){We[s>>2]=1;t=32767}P=r+(l<<1)|0;Ve[P>>1]=Wt(Ve[P>>1]|0,t&65535,s)|0;l=l+1|0}while((l&65535)<<16>>16!=40)}cn(r,e,C,1,s);vt(C,L,I,6);fn(r,L,B,s);P=1;f=2;u=1;t=0;l=1;e=-1;c=1;while(1){N=2;w=2;while(1){O=0;T=0;D=c;y=w;while(1){if(T<<16>>16<40){M=D<<16>>16;g=D<<16>>16<40;R=y<<16>>16;A=y<<16>>16<40;k=T<<16>>16;F=T;while(1){if((Ve[I+(k<<1)>>1]|0)>-1){b=Ve[B+(k*80|0)+(k<<1)>>1]|0;if(g){v=Xe[C+(k<<1)>>1]|0;S=M;h=1;_=D;n=D;w=0;d=-1;while(1){E=(Xe[C+(S<<1)>>1]|0)+v|0;p=E<<16>>16;p=(Ze(p,p)|0)>>>15;m=(Ve[B+(k*80|0)+(S<<1)>>1]<<15)+32768+((Ve[B+(S*80|0)+(S<<1)>>1]|0)+b<<14)|0;if(((Ze(p<<16>>16,h<<16>>16)|0)-(Ze(m>>16,d<<16>>16)|0)<<1|0)>0){h=m>>>16&65535;n=_;w=E&65535;d=p&65535}m=S+5|0;_=m&65535;if(_<<16>>16>=40)break;else S=m<<16>>16}}else{h=1;n=D;w=0}if(A){v=w&65535;_=n<<16>>16;S=(h<<16>>16<<14)+32768|0;p=R;w=1;b=y;d=y;h=-1;while(1){E=(Xe[C+(p<<1)>>1]|0)+v<<16>>16;E=(Ze(E,E)|0)>>>15;m=S+(Ve[B+(p*80|0)+(p<<1)>>1]<<12)+((Ve[B+(k*80|0)+(p<<1)>>1]|0)+(Ve[B+(_*80|0)+(p<<1)>>1]|0)<<13)|0;if(((Ze(E<<16>>16,w<<16>>16)|0)-(Ze(m>>16,h<<16>>16)|0)<<1|0)>0){w=m>>>16&65535;d=b;h=E&65535}m=p+5|0;b=m&65535;if(b<<16>>16>=40){S=w;p=h;break}else p=m<<16>>16}}else{S=1;d=y;p=-1}w=Ze(p<<16>>16,l<<16>>16)|0;if((w|0)==1073741824){We[s>>2]=1;m=2147483647}else m=w<<1;w=Ze(S<<16>>16,e<<16>>16)|0;if((w|0)==1073741824){We[s>>2]=1;h=2147483647}else h=w<<1;w=m-h|0;if(((w^m)&(h^m)|0)<0){We[s>>2]=1;w=(m>>>31)+2147483647|0}_=(w|0)>0;f=_?d:f;u=_?n:u;t=_?F:t;l=_?S:l;e=_?p:e}w=k+5|0;F=w&65535;if(F<<16>>16>=40)break;else k=w<<16>>16}}O=O+1<<16>>16;if(O<<16>>16>=3)break;else{A=y;y=D;D=T;T=A}}n=N+2|0;w=n&65535;if(w<<16>>16>=5)break;else N=n&65535}n=P+2|0;c=n&65535;if(c<<16>>16<4)P=n&65535;else{w=f;f=u;break}}n=i;l=n+80|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(l|0));p=t<<16>>16;e=Ve[L+(p<<1)>>1]|0;t=(p*6554|0)>>>15;n=t<<16;l=p-(((n>>16)*327680|0)>>>16)|0;switch(l<<16>>16|0){case 1:{t=n>>12;break}case 2:{t=n>>8;l=2;break}case 3:{t=t<<20>>16|8;l=1;break}case 4:{t=t<<24>>16|128;l=2;break}default:{}}n=i+(p<<1)|0;if(e<<16>>16>0){Ve[n>>1]=8191;_=32767;u=65536<<(l<<16>>16)>>>16&65535}else{Ve[n>>1]=-8192;_=-32768;u=0}m=f<<16>>16;f=Ve[L+(m<<1)>>1]|0;n=(m*6554|0)>>>15;l=n<<16;e=m-(((l>>16)*327680|0)>>>16)|0;switch(e<<16>>16|0){case 1:{n=l>>12;break}case 2:{n=l>>8;e=2;break}case 3:{n=n<<20>>16|8;e=1;break}case 4:{n=n<<24>>16|128;e=2;break}default:{}}l=i+(m<<1)|0;if(f<<16>>16>0){Ve[l>>1]=8191;E=32767;u=(65536<<(e<<16>>16)>>>16)+(u&65535)&65535}else{Ve[l>>1]=-8192;E=-32768}c=n+t|0;h=w<<16>>16;f=Ve[L+(h<<1)>>1]|0;t=(h*6554|0)>>>15;n=t<<16;l=h-(((n>>16)*327680|0)>>>16)|0;switch(l<<16>>16|0){case 1:{n=n>>12;break}case 2:{n=n>>8;l=2;break}case 3:{n=t<<20>>16|8;l=1;break}case 4:{n=t<<24>>16|128;l=2;break}default:n=t}t=i+(h<<1)|0;if(f<<16>>16>0){Ve[t>>1]=8191;w=32767;t=(65536<<(l<<16>>16)>>>16)+(u&65535)&65535}else{Ve[t>>1]=-8192;w=-32768;t=u}d=c+n|0;Ve[a>>1]=t;u=0;c=r+(0-p<<1)|0;e=r+(0-m<<1)|0;f=r+(0-h<<1)|0;do{t=Ze(Ve[c>>1]|0,_)|0;c=c+2|0;if((t|0)!=1073741824?(H=t<<1,!((t|0)>0&(H|0)<0)):0)l=H;else{We[s>>2]=1;l=2147483647}t=Ze(Ve[e>>1]|0,E)|0;e=e+2|0;if((t|0)!=1073741824){n=(t<<1)+l|0;if((t^l|0)>0&(n^l|0)<0){We[s>>2]=1;n=(l>>>31)+2147483647|0}}else{We[s>>2]=1;n=2147483647}l=Ze(Ve[f>>1]|0,w)|0;f=f+2|0;if((l|0)!=1073741824){t=(l<<1)+n|0;if((l^n|0)>0&(t^n|0)<0){We[s>>2]=1;t=(n>>>31)+2147483647|0}}else{We[s>>2]=1;t=2147483647}Ve[o+(u<<1)>>1]=Ni(t,s)|0;u=u+1|0}while((u|0)!=40);t=d&65535;if(!U){Ge=Y;return t|0}l=x>>16;n=z;do{e=(Ze(Ve[i+(n-z<<1)>>1]|0,l)|0)>>15;if((e|0)>32767){We[s>>2]=1;e=32767}o=i+(n<<1)|0;Ve[o>>1]=Wt(Ve[o>>1]|0,e&65535,s)|0;n=n+1|0}while((n&65535)<<16>>16!=40);Ge=Y;return t|0}function Vr(e,r,n,t,i,o,a,s,l){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;var f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0,C=0,I=0,B=0,L=0,U=0,x=0,H=0,z=0,Y=0,j=0,q=0,K=0,V=0,W=0,X=0,G=0,Z=0,J=0,Q=0;Q=Ge;Ge=Ge+3456|0;W=Q+3448|0;K=Q+3360|0;Y=Q+3368|0;j=Q+3280|0;V=Q+3200|0;q=Q;G=(t&65535)<<17;J=n<<16>>16;X=n<<16>>16<40;if(X){n=G>>16;f=J;do{t=(Ze(Ve[r+(f-J<<1)>>1]|0,n)|0)>>15;if((t|0)>32767){We[l>>2]=1;t=32767}z=r+(f<<1)|0;Ve[z>>1]=Wt(Ve[z>>1]|0,t&65535,l)|0;f=f+1|0}while((f&65535)<<16>>16!=40)}cn(r,e,Y,1,l);vt(Y,V,j,4);fn(r,V,q,l);x=K+2|0;Ve[K>>1]=0;H=K+4|0;Ve[x>>1]=1;z=K+6|0;Ve[H>>1]=2;Ve[z>>1]=3;h=3;c=2;u=1;t=0;n=1;f=-1;d=3;do{C=0;I=0;B=d;L=1;U=2;while(1){if(I<<16>>16<40){y=L<<16>>16;O=L<<16>>16<40;T=U<<16>>16;D=U<<16>>16<40;N=B<<16>>16;P=B<<16>>16<40;A=I<<16>>16;R=c;M=u;F=n;g=I;while(1){if((Ve[j+(A<<1)>>1]|0)>-1){m=Ve[q+(A*80|0)+(A<<1)>>1]|0;if(O){w=Xe[Y+(A<<1)>>1]|0;E=y;_=1;c=L;u=L;b=0;v=-1;while(1){S=(Xe[Y+(E<<1)>>1]|0)+w|0;p=S<<16>>16;p=(Ze(p,p)|0)>>>15;k=(Ve[q+(A*80|0)+(E<<1)>>1]<<15)+32768+((Ve[q+(E*80|0)+(E<<1)>>1]|0)+m<<14)|0;if(((Ze(p<<16>>16,_<<16>>16)|0)-(Ze(k>>16,v<<16>>16)|0)<<1|0)>0){_=k>>>16&65535;u=c;b=S&65535;v=p&65535}k=E+5|0;c=k&65535;if(c<<16>>16>=40)break;else E=k<<16>>16}}else{_=1;u=L;b=0}if(D){n=b&65535;e=u<<16>>16;m=(_<<16>>16<<14)+32768|0;E=T;k=1;w=U;c=U;v=0;b=-1;while(1){S=(Xe[Y+(E<<1)>>1]|0)+n|0;p=S<<16>>16;p=(Ze(p,p)|0)>>>15;_=m+(Ve[q+(E*80|0)+(E<<1)>>1]<<12)+((Ve[q+(A*80|0)+(E<<1)>>1]|0)+(Ve[q+(e*80|0)+(E<<1)>>1]|0)<<13)|0;if(((Ze(p<<16>>16,k<<16>>16)|0)-(Ze(_>>16,b<<16>>16)|0)<<1|0)>0){k=_>>>16&65535;c=w;v=S&65535;b=p&65535}_=E+5|0;w=_&65535;if(w<<16>>16>=40)break;else E=_<<16>>16}}else{k=1;c=U;v=0}if(P){m=v&65535;w=c<<16>>16;e=u<<16>>16;p=(k&65535)<<16|32768;S=N;n=1;E=B;_=B;k=-1;while(1){b=(Xe[Y+(S<<1)>>1]|0)+m<<16>>16;b=(Ze(b,b)|0)>>>15;v=(Ve[q+(S*80|0)+(S<<1)>>1]<<12)+p+((Ve[q+(e*80|0)+(S<<1)>>1]|0)+(Ve[q+(w*80|0)+(S<<1)>>1]|0)+(Ve[q+(A*80|0)+(S<<1)>>1]|0)<<13)|0;if(((Ze(b<<16>>16,n<<16>>16)|0)-(Ze(v>>16,k<<16>>16)|0)<<1|0)>0){n=v>>>16&65535;_=E;k=b&65535}v=S+5|0;E=v&65535;if(E<<16>>16>=40)break;else S=v<<16>>16}}else{n=1;_=B;k=-1}if(((Ze(k<<16>>16,F<<16>>16)|0)-(Ze(n<<16>>16,f<<16>>16)|0)<<1|0)>0){Ve[K>>1]=g;Ve[x>>1]=u;Ve[H>>1]=c;Ve[z>>1]=_;h=_;t=g;f=k}else{c=R;u=M;n=F}}else{c=R;u=M;n=F}S=A+5|0;g=S&65535;if(g<<16>>16>=40)break;else{A=S<<16>>16;R=c;M=u;F=n}}}C=C+1<<16>>16;if(C<<16>>16>=4)break;else{N=U;P=B;U=L;L=I;B=N;I=P}}d=d+1<<16>>16}while(d<<16>>16<5);k=h;_=c;v=u;b=t;t=i;n=t+80|0;do{Ve[t>>1]=0;t=t+2|0}while((t|0)<(n|0));e=b;n=0;f=0;t=0;while(1){c=e<<16>>16;d=Ve[V+(c<<1)>>1]|0;e=c*13108>>16;u=c-((e*327680|0)>>>16)|0;e=Ve[s+(e<<1)>>1]|0;switch(u<<16>>16|0){case 1:{h=e<<16>>16<<3&65535;break}case 2:{h=e<<16>>16<<6&65535;break}case 3:{h=e<<16>>16<<10&65535;break}case 4:{h=((e&65535)<<10|512)&65535;u=3;break}default:h=e}e=i+(c<<1)|0;if(d<<16>>16>0){Ve[e>>1]=8191;e=32767;t=(65536<<(u<<16>>16)>>>16)+(t&65535)&65535}else{Ve[e>>1]=-8192;e=-32768}Ve[W+(n<<1)>>1]=e;f=(h&65535)+(f&65535)|0;n=n+1|0;if((n|0)==4){S=f;break}e=Ve[K+(n<<1)>>1]|0}Ve[a>>1]=t;m=W+2|0;E=W+4|0;p=W+6|0;e=Ve[W>>1]|0;w=0;u=r+(0-(b<<16>>16)<<1)|0;c=r+(0-(v<<16>>16)<<1)|0;d=r+(0-(_<<16>>16)<<1)|0;h=r+(0-(k<<16>>16)<<1)|0;do{t=Ze(Ve[u>>1]|0,e)|0;u=u+2|0;if((t|0)!=1073741824?(Z=t<<1,!((t|0)>0&(Z|0)<0)):0)f=Z;else{We[l>>2]=1;f=2147483647}t=Ze(Ve[m>>1]|0,Ve[c>>1]|0)|0;c=c+2|0;if((t|0)!=1073741824){n=(t<<1)+f|0;if((t^f|0)>0&(n^f|0)<0){We[l>>2]=1;n=(f>>>31)+2147483647|0}}else{We[l>>2]=1;n=2147483647}t=Ze(Ve[E>>1]|0,Ve[d>>1]|0)|0;d=d+2|0;if((t|0)!=1073741824){f=(t<<1)+n|0;if((t^n|0)>0&(f^n|0)<0){We[l>>2]=1;f=(n>>>31)+2147483647|0}}else{We[l>>2]=1;f=2147483647}n=Ze(Ve[p>>1]|0,Ve[h>>1]|0)|0;h=h+2|0;if((n|0)!=1073741824){t=(n<<1)+f|0;if((n^f|0)>0&(t^f|0)<0){We[l>>2]=1;t=(f>>>31)+2147483647|0}}else{We[l>>2]=1;t=2147483647}Ve[o+(w<<1)>>1]=Ni(t,l)|0;w=w+1|0}while((w|0)!=40);t=S&65535;if(((J<<16)+-2621440|0)>-1|X^1){Ge=Q;return t|0}f=G>>16;n=J;do{e=(Ze(Ve[i+(n-J<<1)>>1]|0,f)|0)>>15;if((e|0)>32767){We[l>>2]=1;e=32767}o=i+(n<<1)|0;Ve[o>>1]=Wt(Ve[o>>1]|0,e&65535,l)|0;n=n+1|0}while((n&65535)<<16>>16!=40);Ge=Q;return t|0}function Wr(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;var s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0;P=Ge;Ge=Ge+3440|0;m=P+3424|0;y=P+3408|0;O=P+3240|0;E=P+3224|0;R=P+3328|0;w=P+3248|0;A=P+24|0;N=P+16|0;D=P;un(n,e,R,2,4,4,a);_t(R,r,w,O,4,y,4,a);fn(n,w,A,a);bt(8,4,4,R,A,y,O,E,a);r=t;e=r+80|0;do{Ve[r>>1]=0;r=r+2|0}while((r|0)<(e|0));Ve[D>>1]=-1;Ve[N>>1]=-1;M=D+2|0;Ve[M>>1]=-1;g=N+2|0;Ve[g>>1]=-1;R=D+4|0;Ve[R>>1]=-1;A=N+4|0;Ve[A>>1]=-1;O=D+6|0;Ve[O>>1]=-1;y=N+6|0;Ve[y>>1]=-1;d=0;do{u=Ve[E+(d<<1)>>1]|0;r=u>>>2;l=r&65535;e=u&3;f=(Ve[w+(u<<1)>>1]|0)>0;u=t+(u<<1)|0;h=f&1^1;Ve[u>>1]=(Xe[u>>1]|0)+(f?8191:57345);Ve[m+(d<<1)>>1]=f?32767:-32768;f=D+(e<<1)|0;u=Ve[f>>1]|0;do{if(u<<16>>16>=0){c=N+(e<<1)|0;s=(u<<16>>16|0)<=(r<<16>>16|0);r=D+((e|4)<<1)|0;if((h&65535|0)==(Xe[c>>1]&1|0))if(s){Ve[r>>1]=l;break}else{Ve[r>>1]=u;Ve[f>>1]=l;Ve[c>>1]=h;break}else if(s){Ve[r>>1]=u;Ve[f>>1]=l;Ve[c>>1]=h;break}else{Ve[r>>1]=l;break}}else{Ve[f>>1]=l;Ve[N+(e<<1)>>1]=h}}while(0);d=d+1|0}while((d|0)!=8);p=m+2|0;S=m+4|0;b=m+6|0;v=m+8|0;_=m+10|0;k=m+12|0;F=m+14|0;m=Ve[m>>1]|0;d=0;c=n+(0-(Ve[E>>1]|0)<<1)|0;u=n+(0-(Ve[E+2>>1]|0)<<1)|0;f=n+(0-(Ve[E+4>>1]|0)<<1)|0;l=n+(0-(Ve[E+6>>1]|0)<<1)|0;r=n+(0-(Ve[E+8>>1]|0)<<1)|0;e=n+(0-(Ve[E+10>>1]|0)<<1)|0;s=n+(0-(Ve[E+12>>1]|0)<<1)|0;n=n+(0-(Ve[E+14>>1]|0)<<1)|0;do{h=Ze(Ve[c>>1]|0,m)|0;c=c+2|0;if((h|0)!=1073741824?(T=h<<1,!((h|0)>0&(T|0)<0)):0)h=T;else{We[a>>2]=1;h=2147483647}w=Ze(Ve[p>>1]|0,Ve[u>>1]|0)|0;u=u+2|0;if((w|0)!=1073741824){t=(w<<1)+h|0;if((w^h|0)>0&(t^h|0)<0){We[a>>2]=1;h=(h>>>31)+2147483647|0}else h=t}else{We[a>>2]=1;h=2147483647}w=Ze(Ve[S>>1]|0,Ve[f>>1]|0)|0;f=f+2|0;if((w|0)!=1073741824){t=(w<<1)+h|0;if((w^h|0)>0&(t^h|0)<0){We[a>>2]=1;t=(h>>>31)+2147483647|0}}else{We[a>>2]=1;t=2147483647}w=Ze(Ve[b>>1]|0,Ve[l>>1]|0)|0;l=l+2|0;if((w|0)!=1073741824){h=(w<<1)+t|0;if((w^t|0)>0&(h^t|0)<0){We[a>>2]=1;h=(t>>>31)+2147483647|0}}else{We[a>>2]=1;h=2147483647}w=Ze(Ve[v>>1]|0,Ve[r>>1]|0)|0;r=r+2|0;if((w|0)!=1073741824){t=(w<<1)+h|0;if((w^h|0)>0&(t^h|0)<0){We[a>>2]=1;t=(h>>>31)+2147483647|0}}else{We[a>>2]=1;t=2147483647}w=Ze(Ve[_>>1]|0,Ve[e>>1]|0)|0;e=e+2|0;if((w|0)!=1073741824){h=(w<<1)+t|0;if((w^t|0)>0&(h^t|0)<0){We[a>>2]=1;h=(t>>>31)+2147483647|0}}else{We[a>>2]=1;h=2147483647}w=Ze(Ve[k>>1]|0,Ve[s>>1]|0)|0;s=s+2|0;if((w|0)!=1073741824){t=(w<<1)+h|0;if((w^h|0)>0&(t^h|0)<0){We[a>>2]=1;t=(h>>>31)+2147483647|0}}else{We[a>>2]=1;t=2147483647}w=Ze(Ve[F>>1]|0,Ve[n>>1]|0)|0;n=n+2|0;if((w|0)!=1073741824){h=(w<<1)+t|0;if((w^t|0)>0&(h^t|0)<0){We[a>>2]=1;h=(t>>>31)+2147483647|0}}else{We[a>>2]=1;h=2147483647}Ve[i+(d<<1)>>1]=Ni(h,a)|0;d=d+1|0}while((d|0)!=40);Ve[o>>1]=Ve[N>>1]|0;Ve[o+2>>1]=Ve[g>>1]|0;Ve[o+4>>1]=Ve[A>>1]|0;Ve[o+6>>1]=Ve[y>>1]|0;e=Ve[D>>1]|0;r=Ve[D+8>>1]|0;s=Ve[M>>1]|0;Ve[o+8>>1]=r<<1&2|e&1|s<<2&4|(((r>>1)*327680|0)+(e>>>1<<16)+(Ze(s>>1,1638400)|0)|0)>>>13&65528;s=Ve[R>>1]|0;e=Ve[D+12>>1]|0;r=Ve[D+10>>1]|0;Ve[o+10>>1]=e<<1&2|s&1|r<<2&4|(((e>>1)*327680|0)+(s>>>1<<16)+(Ze(r>>1,1638400)|0)|0)>>>13&65528;r=Ve[D+14>>1]|0;s=Ve[O>>1]|0;e=s<<16>>16>>>1;if(!(r&2)){i=e;a=r<<16>>16;N=a>>1;N=N*327680|0;i=i<<16;N=i+N|0;N=N<<5;N=N>>16;N=N|12;N=N*2622|0;N=N>>>16;i=s&65535;i=i&1;a=a<<17;a=a&131072;N=N<<18;a=N|a;a=a>>>16;i=a|i;i=i&65535;o=o+12|0;Ve[o>>1]=i;Ge=P;return}i=4-(e<<16>>16)|0;a=r<<16>>16;N=a>>1;N=N*327680|0;i=i<<16;N=i+N|0;N=N<<5;N=N>>16;N=N|12;N=N*2622|0;N=N>>>16;i=s&65535;i=i&1;a=a<<17;a=a&131072;N=N<<18;a=N|a;a=a>>>16;i=a|i;i=i&65535;o=o+12|0;Ve[o>>1]=i;Ge=P;return}function Xr(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0;m=n<<16>>16;o=0-m|0;n=i+(o<<2)|0;i=((m-(t<<16>>16)|0)>>>2)+1&65535;if(i<<16>>16<=0)return;m=r<<16>>16>>>1&65535;if(!(m<<16>>16)){while(1){We[n>>2]=0;We[n+4>>2]=0;We[n+8>>2]=0;We[n+12>>2]=0;if(i<<16>>16>1){n=n+16|0;i=i+-1<<16>>16}else break}return}w=e+(o<<1)|0;while(1){f=w+4|0;c=Ve[f>>1]|0;s=Ve[w>>1]|0;u=c;l=m;d=e;h=w;w=w+8|0;a=0;o=0;t=0;r=0;while(1){p=Ve[d>>1]|0;E=(Ze(s<<16>>16,p)|0)+a|0;a=Ve[h+2>>1]|0;o=(Ze(a,p)|0)+o|0;s=(Ze(u<<16>>16,p)|0)+t|0;t=Ve[h+6>>1]|0;u=(Ze(t,p)|0)+r|0;r=Ve[d+2>>1]|0;a=E+(Ze(r,a)|0)|0;o=o+(Ze(c<<16>>16,r)|0)|0;f=f+4|0;t=s+(Ze(r,t)|0)|0;s=Ve[f>>1]|0;r=u+(Ze(s<<16>>16,r)|0)|0;l=l+-1<<16>>16;if(!(l<<16>>16))break;p=c;u=s;c=Ve[h+8>>1]|0;d=d+4|0;h=h+4|0;s=p}We[n>>2]=a<<1;We[n+4>>2]=o<<1;We[n+8>>2]=t<<1;We[n+12>>2]=r<<1;if(i<<16>>16<=1)break;else{n=n+16|0;i=i+-1<<16>>16}}return}function Gr(e,r,n,t,i,o,a,s,l){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;var f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0;v=Ge;Ge=Ge+16|0;S=v+2|0;b=v;do{if(i<<16>>16>0){w=t<<16>>16;E=0;c=0;t=0;u=0;m=0;while(1){f=Ve[e+(E<<1)>>1]|0;d=f<<16>>16;c=(Ze(d,d)|0)+c|0;d=Ve[r+(E<<1)>>1]|0;t=(Ze(d,d)|0)+t|0;u=(Ze(Ve[n+(E<<1)>>1]|0,d)|0)+u|0;d=Ze(d,w)|0;if((d|0)==1073741824){We[l>>2]=1;h=2147483647}else h=d<<1;d=h<<1;d=(Bi(f,Ni((d>>1|0)==(h|0)?d:h>>31^2147483647,l)|0,l)|0)<<16>>16;d=Ze(d,d)|0;if((d|0)!=1073741824){f=(d<<1)+m|0;if((d^m|0)>0&(f^m|0)<0){We[l>>2]=1;f=(m>>>31)+2147483647|0}}else{We[l>>2]=1;f=2147483647}E=E+1|0;if((E&65535)<<16>>16==i<<16>>16){m=f;break}else m=f}c=c<<1;t=t<<1;u=u<<1;if((c|0)>=0){if((c|0)<400){f=m;p=14;break}}else{We[l>>2]=1;c=2147483647}h=bi(c)|0;d=h<<16>>16;if(h<<16>>16>0){f=c<<d;if((f>>d|0)!=(c|0))f=c>>31^2147483647}else{f=0-d<<16;if((f|0)<2031616)f=c>>(f>>16);else f=0}Ve[o>>1]=f>>>16;c=t;w=u;f=m;t=15-(h&65535)&65535}else{t=0;u=0;f=0;p=14}}while(0);if((p|0)==14){Ve[o>>1]=0;c=t;w=u;t=-15}Ve[a>>1]=t;if((c|0)<0){We[l>>2]=1;c=2147483647}d=bi(c)|0;u=d<<16>>16;if(d<<16>>16>0){t=c<<u;if((t>>u|0)!=(c|0))t=c>>31^2147483647}else{t=0-u<<16;if((t|0)<2031616)t=c>>(t>>16);else t=0}Ve[o+2>>1]=t>>>16;Ve[a+2>>1]=15-(d&65535);c=bi(w)|0;u=c<<16>>16;if(c<<16>>16>0){t=w<<u;if((t>>u|0)!=(w|0))t=w>>31^2147483647}else{t=0-u<<16;if((t|0)<2031616)t=w>>(t>>16);else t=0}Ve[o+4>>1]=t>>>16;Ve[a+4>>1]=2-(c&65535);c=bi(f)|0;t=c<<16>>16;if(c<<16>>16>0){u=f<<t;if((u>>t|0)!=(f|0))u=f>>31^2147483647}else{t=0-t<<16;if((t|0)<2031616)u=f>>(t>>16);else u=0}t=u>>>16&65535;f=15-(c&65535)&65535;Ve[o+6>>1]=t;Ve[a+6>>1]=f;if((u>>16|0)<=0){l=0;Ve[s>>1]=l;Ge=v;return}u=Ve[o>>1]|0;if(!(u<<16>>16)){l=0;Ve[s>>1]=l;Ge=v;return}t=Gt(Pi(u,1,l)|0,t)|0;t=(t&65535)<<16;u=((Bi(f,Ve[a>>1]|0,l)|0)&65535)+3|0;f=u&65535;u=u<<16>>16;if(f<<16>>16>0)f=f<<16>>16<31?t>>u:0;else{a=0-u<<16>>16;f=t<<a;f=(f>>a|0)==(t|0)?f:t>>31^2147483647}si(f,S,b,l);b=Un((Xe[S>>1]|0)+65509&65535,Ve[b>>1]|0,l)|0;S=b<<13;l=Ni((S>>13|0)==(b|0)?S:b>>31^2147483647,l)|0;Ve[s>>1]=l;Ge=v;return}function Zr(e,r,n,t,i,o,a,s,l,f,u){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;u=u|0;var c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0;v=Ge;Ge=Ge+80|0;p=v;Ve[a>>1]=Ve[o>>1]|0;Ve[s>>1]=Ve[o+2>>1]|0;h=Ve[o+4>>1]|0;if(h<<16>>16==-32768)h=32767;else h=0-(h&65535)&65535;Ve[a+2>>1]=h;Ve[s+2>>1]=(Xe[o+6>>1]|0)+1;switch(e|0){case 0:case 5:{E=0;d=0;c=0;m=0;break}default:{E=0;d=1;c=1;m=1}}while(1){w=(Ve[i+(E<<1)>>1]|0)>>>3;Ve[p+(E<<1)>>1]=w;w=w<<16>>16;h=Ze(w,w)|0;if((h|0)!=1073741824){o=(h<<1)+d|0;if((h^d|0)>0&(o^d|0)<0){We[u>>2]=1;d=(d>>>31)+2147483647|0}else d=o}else{We[u>>2]=1;d=2147483647}h=Ze(Ve[r+(E<<1)>>1]|0,w)|0;if((h|0)!=1073741824){o=(h<<1)+c|0;if((h^c|0)>0&(o^c|0)<0){We[u>>2]=1;c=(c>>>31)+2147483647|0}else c=o}else{We[u>>2]=1;c=2147483647}h=Ze(Ve[t+(E<<1)>>1]|0,w)|0;if((h|0)!=1073741824){o=(h<<1)+m|0;if((h^m|0)>0&(o^m|0)<0){We[u>>2]=1;o=(m>>>31)+2147483647|0}}else{We[u>>2]=1;o=2147483647}E=E+1|0;if((E|0)==40){t=o;w=c;break}else m=o}c=bi(d)|0;o=c<<16>>16;if(c<<16>>16>0){h=d<<o;if((h>>o|0)!=(d|0))h=d>>31^2147483647}else{h=0-o<<16;if((h|0)<2031616)h=d>>(h>>16);else h=0}i=a+4|0;Ve[i>>1]=h>>>16;r=s+4|0;Ve[r>>1]=-3-(c&65535);d=bi(w)|0;o=d<<16>>16;if(d<<16>>16>0){h=w<<o;if((h>>o|0)!=(w|0))h=w>>31^2147483647}else{h=0-o<<16;if((h|0)<2031616)h=w>>(h>>16);else h=0}o=h>>>16;Ve[a+6>>1]=(o|0)==32768?32767:0-o&65535;Ve[s+6>>1]=7-(d&65535);d=bi(t)|0;o=d<<16>>16;if(d<<16>>16>0){h=t<<o;if((h>>o|0)!=(t|0))h=t>>31^2147483647}else{h=0-o<<16;if((h|0)<2031616)h=t>>(h>>16);else h=0}Ve[a+8>>1]=h>>>16;Ve[s+8>>1]=7-(d&65535);switch(e|0){case 0:case 5:{h=0;c=0;break}default:{Ge=v;return}}do{c=(Ze(Ve[p+(h<<1)>>1]|0,Ve[n+(h<<1)>>1]|0)|0)+c|0;h=h+1|0}while((h|0)!=40);o=c<<1;h=bi(o)|0;d=h<<16>>16;if(h<<16>>16>0){c=o<<d;if((c>>d|0)==(o|0)){S=c;b=40}else{S=o>>31^2147483647;b=40}}else{c=0-d<<16;if((c|0)<2031616){S=o>>(c>>16);b=40}}if((b|0)==40?(S>>16|0)>=1:0){u=Pi(S>>>16&65535,1,u)|0;Ve[l>>1]=Gt(u,Ve[i>>1]|0)|0;Ve[f>>1]=65528-(h&65535)-(Xe[r>>1]|0);Ge=v;return}Ve[l>>1]=0;Ve[f>>1]=0;Ge=v;return}function Jr(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0;o=0;i=0;do{a=Ve[e+(o<<1)>>1]|0;i=(Ze(a,a)|0)+i|0;o=o+1|0}while((o|0)!=40);if((i|0)<0){We[t>>2]=1;i=2147483647}t=bi(i)|0;e=t<<16>>16;if(t<<16>>16>0){o=i<<e;if((o>>e|0)==(i|0))i=o;else i=i>>31^2147483647}else{e=0-e<<16;if((e|0)<2031616)i=i>>(e>>16);else i=0}Ve[n>>1]=i>>>16;Ve[r>>1]=16-(t&65535);return}function Qr(e,r,n,t,i,o,a,s,l,f,u,c,d){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;u=u|0;c=c|0;d=d|0;var h=0,w=0,m=0,E=0;w=Ge;Ge=Ge+16|0;h=w;if(f>>>0<2){a=Yr(u,e,r,n,t,a,s,h,We[c+76>>2]|0,d)|0;d=We[l>>2]|0;Ve[d>>1]=a;a=Ve[h>>1]|0;We[l>>2]=d+4;Ve[d+2>>1]=a;Ge=w;return}switch(f|0){case 2:{a=zr(e,r,n,t,a,s,h,d)|0;d=We[l>>2]|0;Ve[d>>1]=a;a=Ve[h>>1]|0;We[l>>2]=d+4;Ve[d+2>>1]=a;Ge=w;return}case 3:{a=Kr(e,r,n,t,a,s,h,d)|0;d=We[l>>2]|0;Ve[d>>1]=a;a=Ve[h>>1]|0;We[l>>2]=d+4;Ve[d+2>>1]=a;Ge=w;return}default:{if((f&-2|0)==4){a=Vr(e,r,n,t,a,s,h,We[c+36>>2]|0,d)|0;d=We[l>>2]|0;Ve[d>>1]=a;a=Ve[h>>1]|0;We[l>>2]=d+4;Ve[d+2>>1]=a;Ge=w;return}if((f|0)!=6){u=i<<16>>16;u=(u<<17>>17|0)==(u|0)?u<<1:u>>>15^32767;i=n<<16>>16<40;if(!i){Hr(e,o,r,a,s,We[l>>2]|0,We[c+36>>2]|0,d);We[l>>2]=(We[l>>2]|0)+20;Ge=w;return}h=n<<16>>16;f=u<<16>>16;t=h;do{E=(Ze(Ve[r+(t-h<<1)>>1]|0,f)|0)>>>15&65535;m=r+(t<<1)|0;Ve[m>>1]=Wt(Ve[m>>1]|0,E,d)|0;t=t+1|0}while((t&65535)<<16>>16!=40);Hr(e,o,r,a,s,We[l>>2]|0,We[c+36>>2]|0,d);We[l>>2]=(We[l>>2]|0)+20;if(!i){Ge=w;return}i=n<<16>>16;f=u<<16>>16;h=i;do{t=(Ze(Ve[a+(h-i<<1)>>1]|0,f)|0)>>15;if((t|0)>32767){We[d>>2]=1;t=32767}E=a+(h<<1)|0;Ve[E>>1]=Wt(Ve[E>>1]|0,t&65535,d)|0;h=h+1|0}while((h&65535)<<16>>16!=40);Ge=w;return}c=t<<16>>16;c=(c<<17>>17|0)==(c|0)?c<<1:c>>>15^32767;u=n<<16>>16<40;if(!u){Wr(e,o,r,a,s,We[l>>2]|0,d);We[l>>2]=(We[l>>2]|0)+14;Ge=w;return}h=n<<16>>16;f=c<<16>>16;t=h;do{i=(Ze(Ve[r+(t-h<<1)>>1]|0,f)|0)>>15;if((i|0)>32767){We[d>>2]=1;i=32767}E=r+(t<<1)|0;Ve[E>>1]=Wt(Ve[E>>1]|0,i&65535,d)|0;t=t+1|0}while((t&65535)<<16>>16!=40);Wr(e,o,r,a,s,We[l>>2]|0,d);We[l>>2]=(We[l>>2]|0)+14;if(!u){Ge=w;return}i=n<<16>>16;f=c<<16>>16;h=i;do{t=(Ze(Ve[a+(h-i<<1)>>1]|0,f)|0)>>15;if((t|0)>32767){We[d>>2]=1;t=32767}E=a+(h<<1)|0;Ve[E>>1]=Wt(Ve[E>>1]|0,t&65535,d)|0;h=h+1|0}while((h&65535)<<16>>16!=40);Ge=w;return}}}function $r(e){e=e|0;var r=0;if(!e){e=-1;return e|0}We[e>>2]=0;r=xi(4)|0;if(!r){e=-1;return e|0}if(!((Zn(r)|0)<<16>>16)){Jn(We[r>>2]|0)|0;We[e>>2]=r;e=0;return e|0}else{Qn(r);Hi(r);e=-1;return e|0}return 0}function en(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Qn(r);Hi(We[e>>2]|0);We[e>>2]=0;return}function rn(e){e=e|0;if(!e){e=-1;return e|0}Jn(We[e>>2]|0)|0;e=0;return e|0}function nn(e,r,n,t,i,o,a,s,l,f,u,c,d,h,w,m,E,p,S,b){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;u=u|0;c=c|0;d=d|0;h=h|0;w=w|0;m=m|0;E=E|0;p=p|0;S=S|0;b=b|0;var v=0,_=0,k=0,F=0;_=Ge;Ge=Ge+16|0;F=_+2|0;k=_;Ve[d>>1]=$n(We[e>>2]|0,n,i,a,l,o,40,t,h,k,F,b)|0;e=Ve[F>>1]|0;t=We[E>>2]|0;We[E>>2]=t+2;Ve[t>>1]=e;ki(a,Ve[d>>1]|0,Ve[h>>1]|0,40,Ve[k>>1]|0,b);ln(a,o,c,40);Ve[w>>1]=Pn(n,l,c,m,40,b)|0;Ve[p>>1]=32767;if(f<<16>>16!=0?(v=Ve[w>>1]|0,v<<16>>16>15565):0)v=Ct(r,v,b)|0;else v=0;if(n>>>0<2){F=Ve[w>>1]|0;Ve[w>>1]=F<<16>>16>13926?13926:F;if(v<<16>>16)Ve[p>>1]=15565}else{if(v<<16>>16){Ve[p>>1]=15565;Ve[w>>1]=15565}if((n|0)==7){k=pt(7,Ve[p>>1]|0,w,0,0,S,b)|0;F=We[E>>2]|0;We[E>>2]=F+2;Ve[F>>1]=k}}d=Ve[w>>1]|0;v=0;while(1){k=Ze(Ve[c>>1]|0,d)|0;Ve[u>>1]=(Xe[l>>1]|0)-(k>>>14);k=(Ze(Ve[a>>1]|0,d)|0)>>>14;F=s+(v<<1)|0;Ve[F>>1]=(Xe[F>>1]|0)-k;v=v+1|0;if((v|0)==40)break;else{a=a+2|0;l=l+2|0;u=u+2|0;c=c+2|0}}Ge=_;return}function tn(e,r){e=e|0;r=r|0;var n=0,t=0,i=0,o=0;o=Ge;Ge=Ge+16|0;i=o;if(!e){e=-1;Ge=o;return e|0}We[e>>2]=0;n=xi(2532)|0;We[i>>2]=n;if(!n){e=-1;Ge=o;return e|0}ei(n+2392|0);We[n+2188>>2]=0;We[(We[i>>2]|0)+2192>>2]=0;We[(We[i>>2]|0)+2196>>2]=0;We[(We[i>>2]|0)+2200>>2]=0;We[(We[i>>2]|0)+2204>>2]=0;We[(We[i>>2]|0)+2208>>2]=0;We[(We[i>>2]|0)+2212>>2]=0;We[(We[i>>2]|0)+2220>>2]=0;t=We[i>>2]|0;We[t+2216>>2]=r;We[t+2528>>2]=0;n=t;if(((((((($r(t+2196|0)|0)<<16>>16==0?(di(t+2192|0)|0)<<16>>16==0:0)?(yn(t+2200|0)|0)<<16>>16==0:0)?(nt(t+2204|0)|0)<<16>>16==0:0)?(Tt(t+2208|0)|0)<<16>>16==0:0)?(Bt(t+2212|0)|0)<<16>>16==0:0)?(hn(t+2220|0,We[t+2432>>2]|0)|0)<<16>>16==0:0)?(Kn(t+2188|0)|0)<<16>>16==0:0){an(t)|0;We[e>>2]=n;e=0;Ge=o;return e|0}on(i);e=-1;Ge=o;return e|0}function on(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Vn(r+2188|0);wi((We[e>>2]|0)+2192|0);On((We[e>>2]|0)+2200|0);en((We[e>>2]|0)+2196|0);it((We[e>>2]|0)+2204|0);Nt((We[e>>2]|0)+2208|0);Ut((We[e>>2]|0)+2212|0);mn((We[e>>2]|0)+2220|0);Hi(We[e>>2]|0);We[e>>2]=0;return}function an(e){e=e|0;var r=0,n=0,t=0,i=0;if(!e){i=-1;return i|0}We[e+652>>2]=e+320;We[e+640>>2]=e+240;We[e+644>>2]=e+160;We[e+648>>2]=e+80;We[e+1264>>2]=e+942;We[e+1912>>2]=e+1590;t=e+1938|0;We[e+2020>>2]=t;We[e+2384>>2]=e+2304;r=e+2028|0;We[e+2024>>2]=e+2108;We[e+2528>>2]=0;Vi(e|0,0,640)|0;Vi(e+1282|0,0,308)|0;Vi(e+656|0,0,286)|0;n=e+2224|0;i=t+80|0;do{Ve[t>>1]=0;t=t+2|0}while((t|0)<(i|0));t=r;i=t+80|0;do{Ve[t>>1]=0;t=t+2|0}while((t|0)<(i|0));r=e+1268|0;t=n;i=t+80|0;do{Ve[t>>1]=0;t=t+2|0}while((t|0)<(i|0));Ve[r>>1]=40;Ve[e+1270>>1]=40;Ve[e+1272>>1]=40;Ve[e+1274>>1]=40;Ve[e+1276>>1]=40;Wn(We[e+2188>>2]|0)|0;hi(We[e+2192>>2]|0)|0;rn(We[e+2196>>2]|0)|0;Tn(We[e+2200>>2]|0)|0;tt(We[e+2204>>2]|0)|0;Dt(We[e+2208>>2]|0)|0;Lt(We[e+2212>>2]|0)|0;wn(We[e+2220>>2]|0,We[e+2432>>2]|0)|0;Ve[e+2388>>1]=0;i=0;return i|0}function sn(e,r,n,t,i,o){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;var a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0,C=0,I=0,B=0,L=0,U=0,x=0,H=0,z=0,Y=0,j=0,q=0,K=0,V=0,W=0,X=0,G=0,Z=0,J=0,Q=0,$=0,ee=0,re=0,ne=0,te=0,ie=0,oe=0,ae=0,se=0,le=0,fe=0,ue=0,ce=0,de=0,he=0;he=Ge;Ge=Ge+1184|0;Y=he;u=he+1096|0;c=he+1008|0;l=he+904|0;se=he+928|0;le=he+824|0;V=he+744|0;ue=he+664|0;ce=he+584|0;X=he+328|0;ie=he+504|0;oe=he+424|0;fe=he+344|0;de=he+248|0;W=he+168|0;ee=he+88|0;ne=he+68|0;te=he+48|0;re=he+28|0;ae=he+24|0;Q=he+22|0;Z=he+20|0;K=he+16|0;j=he+12|0;q=he+10|0;J=he+8|0;G=he+6|0;$=he+4|0;We[Y>>2]=t;z=e+2528|0;a=e+652|0;qi(We[a>>2]|0,n|0,320)|0;We[i>>2]=r;f=e+2216|0;if(!(We[f>>2]|0)){n=e+2220|0;t=0}else{t=jt(We[e+2212>>2]|0,We[a>>2]|0,z)|0;H=e+2220|0;n=H;t=Sn(We[H>>2]|0,t,i,z)|0}H=e+2392|0;Xn(We[e+2188>>2]|0,r,We[e+644>>2]|0,We[e+648>>2]|0,u,H,z);s=e+2192|0;mi(We[s>>2]|0,r,We[i>>2]|0,u,c,l,Y,z);pn(We[n>>2]|0,l,We[a>>2]|0,z);if((We[i>>2]|0)==8){En(We[n>>2]|0,t,We[(We[s>>2]|0)+40>>2]|0,(We[e+2200>>2]|0)+32|0,Y,z);Vi(e+1282|0,0,308)|0;a=e+2244|0;h=a+20|0;do{Ve[a>>1]=0;a=a+2|0}while((a|0)<(h|0));a=e+2284|0;h=a+20|0;do{Ve[a>>1]=0;a=a+2|0}while((a|0)<(h|0));a=We[e+2020>>2]|0;h=a+80|0;do{Ve[a>>1]=0;a=a+2|0}while((a|0)<(h|0));a=e+2028|0;h=a+80|0;do{Ve[a>>1]=0;a=a+2|0}while((a|0)<(h|0));hi(We[s>>2]|0)|0;a=We[s>>2]|0;n=l;h=a+20|0;do{Ve[a>>1]=Ve[n>>1]|0;a=a+2|0;n=n+2|0}while((a|0)<(h|0));a=(We[s>>2]|0)+20|0;n=l;h=a+20|0;do{Ve[a>>1]=Ve[n>>1]|0;a=a+2|0;n=n+2|0}while((a|0)<(h|0));rn(We[e+2196>>2]|0)|0;Ve[e+2388>>1]=0;x=0}else x=Pt(We[e+2208>>2]|0,We[s>>2]|0,z)|0;B=e+640|0;s=e+2264|0;a=e+1264|0;n=e+2204|0;t=e+2212|0;L=e+1268|0;U=e+1278|0;at(r,2842,2862,2882,u,0,We[B>>2]|0,s,We[a>>2]|0,z);if(r>>>0>1){Gn(We[n>>2]|0,We[t>>2]|0,r,We[a>>2]|0,K,L,U,0,We[f>>2]|0,z);at(r,2842,2862,2882,u,80,We[B>>2]|0,s,We[a>>2]|0,z);Gn(We[n>>2]|0,We[t>>2]|0,r,(We[a>>2]|0)+160|0,K+2|0,L,U,1,We[f>>2]|0,z)}else{at(r,2842,2862,2882,u,80,We[B>>2]|0,s,We[a>>2]|0,z);Gn(We[n>>2]|0,We[t>>2]|0,r,We[a>>2]|0,K,L,U,1,We[f>>2]|0,z);Ve[K+2>>1]=Ve[K>>1]|0}if(We[f>>2]|0)Yt(We[t>>2]|0,K,z);if((We[i>>2]|0)==8){ce=e+656|0;de=e+976|0;qi(ce|0,de|0,286)|0;de=e+320|0;qi(e|0,de|0,320)|0;Ge=he;return 0}k=e+2224|0;F=e+2244|0;M=e+2284|0;g=e+2388|0;R=e+2020|0;A=e+1916|0;y=e+1912|0;O=e+2024|0;T=e+2384|0;D=e+2196|0;N=e+2208|0;P=e+2464|0;C=e+2200|0;I=e+2224|0;b=e+2244|0;v=e+1270|0;_=e+1280|0;S=0;f=0;l=0;m=0;E=0;s=0;p=-1;while(1){d=p;p=p+1<<16>>16;m=1-(m<<16>>16)|0;t=m&65535;w=(m&65535|0)!=0;n=We[i>>2]|0;a=(n|0)==0;do{if(w)if(a){a=ne;n=k;h=a+20|0;do{Ve[a>>1]=Ve[n>>1]|0;a=a+2|0;n=n+2|0}while((a|0)<(h|0));a=te;n=F;h=a+20|0;do{Ve[a>>1]=Ve[n>>1]|0;a=a+2|0;n=n+2|0}while((a|0)<(h|0));a=re;n=M;h=a+20|0;do{Ve[a>>1]=Ve[n>>1]|0;a=a+2|0;n=n+2|0}while((a|0)<(h|0));Ve[ae>>1]=Ve[g>>1]|0;r=(We[B>>2]|0)+(S<<1)|0;a=20;break}else{r=(We[B>>2]|0)+(S<<1)|0;a=19;break}else{r=(We[B>>2]|0)+(S<<1)|0;if(a)a=20;else a=19}}while(0);if((a|0)==19)yt(n,2842,2862,2882,u,c,r,M,b,We[R>>2]|0,A,(We[y>>2]|0)+(S<<1)|0,We[O>>2]|0,se,ie,We[T>>2]|0);else if((a|0)==20?(0,yt(0,2842,2862,2882,u,c,r,M,te,We[R>>2]|0,A,(We[y>>2]|0)+(S<<1)|0,We[O>>2]|0,se,ie,We[T>>2]|0),w):0){a=ee;n=We[O>>2]|0;h=a+80|0;do{Ve[a>>1]=Ve[n>>1]|0;a=a+2|0;n=n+2|0}while((a|0)<(h|0))}a=oe;n=ie;h=a+80|0;do{Ve[a>>1]=Ve[n>>1]|0;a=a+2|0;n=n+2|0}while((a|0)<(h|0));nn(We[D>>2]|0,We[N>>2]|0,We[i>>2]|0,E,K,We[O>>2]|0,(We[y>>2]|0)+(S<<1)|0,oe,se,x,le,ue,j,q,J,X,Y,$,We[P>>2]|0,z);switch(d<<16>>16){case-1:{if((Ve[U>>1]|0)>0)Ve[v>>1]=Ve[j>>1]|0;break}case 2:{if((Ve[_>>1]|0)>0)Ve[L>>1]=Ve[j>>1]|0;break}default:{}}Qr(le,We[O>>2]|0,Ve[j>>1]|0,Ve[g>>1]|0,Ve[J>>1]|0,oe,V,ce,Y,We[i>>2]|0,p,H,z);Dn(We[C>>2]|0,We[i>>2]|0,ie,(We[y>>2]|0)+(S<<1)|0,V,se,le,ue,ce,X,t,Ve[$>>1]|0,Q,Z,J,G,Y,H,z);It(We[N>>2]|0,Ve[J>>1]|0,z);r=We[i>>2]|0;do{if(!r)if(w){a=fe;n=se;h=a+80|0;do{Ve[a>>1]=Ve[n>>1]|0;a=a+2|0;n=n+2|0}while((a|0)<(h|0));a=de;n=ce;h=a+80|0;do{Ve[a>>1]=Ve[n>>1]|0;a=a+2|0;n=n+2|0}while((a|0)<(h|0));a=W;n=V;h=a+80|0;do{Ve[a>>1]=Ve[n>>1]|0;a=a+2|0;n=n+2|0}while((a|0)<(h|0));l=Ve[j>>1]|0;f=Ve[q>>1]|0;Ot(We[B>>2]|0,0,E,Ve[J>>1]|0,Ve[G>>1]|0,c,o,se,V,ue,ce,ne,M,te,We[y>>2]|0,g,z);Ve[g>>1]=Ve[ae>>1]|0;s=E;break}else{a=M;n=re;h=a+20|0;do{Ve[a>>1]=Ve[n>>1]|0;a=a+2|0;n=n+2|0}while((a|0)<(h|0));w=s<<16>>16;ki((We[y>>2]|0)+(w<<1)|0,l,f,40,1,z);ln((We[y>>2]|0)+(w<<1)|0,ee,ue,40);Ot(We[B>>2]|0,We[i>>2]|0,s,Ve[Q>>1]|0,Ve[Z>>1]|0,c+-22|0,o,fe,W,ue,de,I,M,b,We[y>>2]|0,ae,z);yt(We[i>>2]|0,2842,2862,2882,u,c,(We[B>>2]|0)+(S<<1)|0,M,b,We[R>>2]|0,A,(We[y>>2]|0)+(S<<1)|0,We[O>>2]|0,se,ie,We[T>>2]|0);ki((We[y>>2]|0)+(S<<1)|0,Ve[j>>1]|0,Ve[q>>1]|0,40,1,z);ln((We[y>>2]|0)+(S<<1)|0,We[O>>2]|0,ue,40);Ot(We[B>>2]|0,We[i>>2]|0,E,Ve[J>>1]|0,Ve[G>>1]|0,c,o,se,V,ue,ce,I,M,b,We[y>>2]|0,g,z);break}else Ot(We[B>>2]|0,r,E,Ve[J>>1]|0,Ve[G>>1]|0,c,o,se,V,ue,ce,I,M,b,We[y>>2]|0,g,z)}while(0);r=S+40|0;E=r&65535;if(E<<16>>16>=160)break;else{S=r<<16>>16;u=u+22|0;c=c+22|0}}qi(e+1282|0,e+1602|0,308)|0;ce=e+656|0;de=e+976|0;qi(ce|0,de|0,286)|0;de=e+320|0;qi(e|0,de|0,320)|0;Ge=he;return 0}function ln(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0;h=t<<16>>16;if(t<<16>>16>1)d=1;else return;while(1){i=Ve[e>>1]|0;s=r+(d+-1<<1)|0;t=Ze(Ve[r+(d<<1)>>1]|0,i)|0;f=Ve[s>>1]|0;i=Ze(f<<16>>16,i)|0;a=(d+131071|0)>>>1;l=a&65535;o=Ve[e+2>>1]|0;if(!(l<<16>>16)){r=s;a=f}else{u=(a<<1)+131070&131070;c=d-u|0;a=e;do{m=(Ze(f<<16>>16,o)|0)+t|0;w=a;a=a+4|0;t=Ve[s+-2>>1]|0;o=(Ze(t,o)|0)+i|0;i=Ve[a>>1]|0;s=s+-4|0;t=m+(Ze(i,t)|0)|0;f=Ve[s>>1]|0;i=o+(Ze(f<<16>>16,i)|0)|0;l=l+-1<<16>>16;o=Ve[w+6>>1]|0}while(l<<16>>16!=0);a=r+(c+-3<<1)|0;e=e+(u+2<<1)|0;r=a;a=Ve[a>>1]|0}t=(Ze(a<<16>>16,o)|0)+t|0;Ve[n>>1]=i>>>12;Ve[n+2>>1]=t>>>12;t=(d<<16)+131072>>16;if((t|0)<(h|0)){n=n+4|0;e=e+(1-d<<1)|0;d=t}else break}return}function fn(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0;F=Ge;Ge=Ge+80|0;k=F;a=20;o=e;i=1;while(1){_=Ve[o>>1]|0;_=(Ze(_,_)|0)+i|0;i=Ve[o+2>>1]|0;i=_+(Ze(i,i)|0)|0;a=a+-1<<16>>16;if(!(a<<16>>16))break;else o=o+4|0}i=i<<1;if((i|0)<0){o=20;i=e;t=k;while(1){Ve[t>>1]=(Ve[i>>1]|0)>>>1;Ve[t+2>>1]=(Ve[i+2>>1]|0)>>>1;o=o+-1<<16>>16;if(!(o<<16>>16)){_=k;break}else{i=i+4|0;t=t+4|0}}}else{i=ai(i>>1,t)|0;if((i|0)<16777215)i=((i>>9)*32440|0)>>>15<<16>>16;else i=32440;a=20;o=e;t=k;while(1){Ve[t>>1]=((Ze(Ve[o>>1]|0,i)|0)+32|0)>>>6;Ve[t+2>>1]=((Ze(Ve[o+2>>1]|0,i)|0)+32|0)>>>6;a=a+-1<<16>>16;if(!(a<<16>>16)){_=k;break}else{o=o+4|0;t=t+4|0}}}a=20;o=_;t=n+3198|0;i=0;while(1){v=Ve[o>>1]|0;v=(Ze(v,v)|0)+i|0;Ve[t>>1]=(v+16384|0)>>>15;b=Ve[o+2>>1]|0;i=(Ze(b,b)|0)+v|0;Ve[t+-82>>1]=(i+16384|0)>>>15;a=a+-1<<16>>16;if(!(a<<16>>16))break;else{o=o+4|0;t=t+-164|0}}v=r+78|0;b=1;while(1){i=39-b|0;e=n+3120+(i<<1)|0;t=n+(i*80|0)+78|0;i=r+(i<<1)|0;l=k+(b<<1)|0;o=65575-b|0;s=o&65535;a=Ve[_>>1]|0;if(!(s<<16>>16)){s=v;o=0}else{m=o+65535&65535;p=m*41|0;S=(Ze(b,-40)|0)-p|0;E=0-b|0;p=E-p|0;E=E-m|0;w=b+m|0;h=Ve[l>>1]|0;c=_;d=v;f=n+((38-b|0)*80|0)+78|0;o=0;u=0;while(1){l=l+2|0;o=(Ze(h<<16>>16,a)|0)+o|0;c=c+2|0;h=Ve[l>>1]|0;u=(Ze(h<<16>>16,a)|0)+u|0;g=i;i=i+-2|0;a=Ve[i>>1]|0;M=Ve[d>>1]<<1;g=(Ze((Ze(M,Ve[g>>1]|0)|0)>>16,(o<<1)+32768>>16)|0)>>>15&65535;Ve[t>>1]=g;Ve[e>>1]=g;a=(Ze((Ze(M,a)|0)>>16,(u<<1)+32768>>16)|0)>>>15&65535;Ve[e+-2>>1]=a;Ve[f>>1]=a;s=s+-1<<16>>16;a=Ve[c>>1]|0;if(!(s<<16>>16))break;else{d=d+-2|0;e=e+-82|0;t=t+-82|0;f=f+-82|0}}l=k+(w+1<<1)|0;s=r+(38-m<<1)|0;i=r+(E+38<<1)|0;e=n+3040+(p+38<<1)|0;t=n+3040+(S+38<<1)|0}g=(Ze(Ve[l>>1]|0,a)|0)+o|0;g=(Ze((g<<1)+32768>>16,(Ze(Ve[s>>1]<<1,Ve[i>>1]|0)|0)>>16)|0)>>>15&65535;Ve[e>>1]=g;Ve[t>>1]=g;t=(b<<16)+131072|0;if((t|0)<2621440)b=t>>16;else break}Ge=F;return}function un(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;var s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0;w=Ge;Ge=Ge+160|0;h=w;if(i<<16>>16>0){c=o&65535;d=0;s=5;do{if((d|0)<40){u=d;f=d&65535;o=0;while(1){if(f<<16>>16<40){f=f<<16>>16;l=0;do{l=(Ze(Ve[e+(f-u<<1)>>1]|0,Ve[r+(f<<1)>>1]|0)|0)+l|0;f=f+1|0}while((f&65535)<<16>>16!=40)}else l=0;l=l<<1;We[h+(u<<2)>>2]=l;l=Bn(l)|0;o=(l|0)>(o|0)?l:o;l=u+c|0;f=l&65535;if(f<<16>>16>=40)break;else u=l<<16>>16}}else o=0;s=(o>>1)+s|0;d=d+1|0}while((d&65535)<<16>>16!=i<<16>>16)}else s=5;t=((bi(s)|0)&65535)-(t&65535)|0;o=t<<16>>16;l=0-o<<16;s=(l|0)<2031616;l=l>>16;if((t&65535)<<16>>16>0)if(s){s=0;do{t=We[h+(s<<2)>>2]|0;r=t<<o;Ve[n+(s<<1)>>1]=Ni((r>>o|0)==(t|0)?r:t>>31^2147483647,a)|0;s=s+1|0}while((s|0)!=40);Ge=w;return}else{s=0;do{t=We[h+(s<<2)>>2]|0;r=t<<o;Ve[n+(s<<1)>>1]=Ni((r>>o|0)==(t|0)?r:t>>31^2147483647,a)|0;s=s+1|0}while((s|0)!=40);Ge=w;return}else if(s){s=0;do{Ve[n+(s<<1)>>1]=Ni(We[h+(s<<2)>>2]>>l,a)|0;s=s+1|0}while((s|0)!=40);Ge=w;return}else{s=0;do{Ve[n+(s<<1)>>1]=Ni(0,a)|0;s=s+1|0}while((s|0)!=40);Ge=w;return}}function cn(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0;k=Ge;Ge=Ge+160|0;_=k;S=e+2|0;b=Ve[e>>1]|0;v=0;i=5;do{p=v;s=0;while(1){u=r+(p<<1)|0;E=40-p|0;o=(E+131071|0)>>>1&65535;l=r+(p+1<<1)|0;a=Ze(Ve[u>>1]<<1,b)|0;if(!(o<<16>>16))o=S;else{m=131111-p+131070&131070;w=p+m|0;h=S;d=e;c=u;while(1){f=c+4|0;u=d+4|0;a=(Ze(Ve[l>>1]<<1,Ve[h>>1]|0)|0)+a|0;o=o+-1<<16>>16;a=(Ze(Ve[f>>1]<<1,Ve[u>>1]|0)|0)+a|0;if(!(o<<16>>16))break;else{l=c+6|0;h=d+6|0;d=u;c=f}}l=r+(w+3<<1)|0;o=e+(m+3<<1)|0}if(!(E&1))a=(Ze(Ve[l>>1]<<1,Ve[o>>1]|0)|0)+a|0;We[_+(p<<2)>>2]=a;a=(a|0)<0?0-a|0:a;s=(a|0)>(s|0)?a:s;a=p+5|0;if((a&65535)<<16>>16<40)p=a<<16>>16;else break}i=(s>>1)+i|0;v=v+1|0}while((v|0)!=5);t=((bi(i)|0)&65535)-(t&65535)|0;a=t<<16>>16;i=0-a<<16;s=i>>16;if((t&65535)<<16>>16>0){o=20;i=_;while(1){_=We[i>>2]|0;t=_<<a;Ve[n>>1]=(((t>>a|0)==(_|0)?t:_>>31^2147483647)+32768|0)>>>16;_=We[i+4>>2]|0;t=_<<a;Ve[n+2>>1]=(((t>>a|0)==(_|0)?t:_>>31^2147483647)+32768|0)>>>16;o=o+-1<<16>>16;if(!(o<<16>>16))break;else{n=n+4|0;i=i+8|0}}Ge=k;return}if((i|0)<2031616){o=20;i=_;while(1){Ve[n>>1]=((We[i>>2]>>s)+32768|0)>>>16;Ve[n+2>>1]=((We[i+4>>2]>>s)+32768|0)>>>16;o=o+-1<<16>>16;if(!(o<<16>>16))break;else{n=n+4|0;i=i+8|0}}Ge=k;return}else{Ve[n>>1]=0;_=n+4|0;Ve[n+2>>1]=0;Ve[_>>1]=0;t=_+4|0;Ve[_+2>>1]=0;Ve[t>>1]=0;_=t+4|0;Ve[t+2>>1]=0;Ve[_>>1]=0;t=_+4|0;Ve[_+2>>1]=0;Ve[t>>1]=0;_=t+4|0;Ve[t+2>>1]=0;Ve[_>>1]=0;t=_+4|0;Ve[_+2>>1]=0;Ve[t>>1]=0;_=t+4|0;Ve[t+2>>1]=0;Ve[_>>1]=0;t=_+4|0;Ve[_+2>>1]=0;Ve[t>>1]=0;_=t+4|0;Ve[t+2>>1]=0;Ve[_>>1]=0;t=_+4|0;Ve[_+2>>1]=0;Ve[t>>1]=0;_=t+4|0;Ve[t+2>>1]=0;Ve[_>>1]=0;t=_+4|0;Ve[_+2>>1]=0;Ve[t>>1]=0;_=t+4|0;Ve[t+2>>1]=0;Ve[_>>1]=0;t=_+4|0;Ve[_+2>>1]=0;Ve[t>>1]=0;_=t+4|0;Ve[t+2>>1]=0;Ve[_>>1]=0;t=_+4|0;Ve[_+2>>1]=0;Ve[t>>1]=0;_=t+4|0;Ve[t+2>>1]=0;Ve[_>>1]=0;t=_+4|0;Ve[_+2>>1]=0;Ve[t>>1]=0;_=t+4|0;Ve[t+2>>1]=0;Ve[_>>1]=0;Ve[_+2>>1]=0;Ge=k;return}}function dn(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0;a=(Gt(16383,r)|0)<<16>>16;r=Ze(a,r<<16>>16)|0;if((r|0)==1073741824){We[t>>2]=1;i=2147483647}else i=r<<1;o=(Ze(a,n<<16>>16)|0)>>15;r=i+(o<<1)|0;if((i^o|0)>0&(r^i|0)<0){We[t>>2]=1;r=(i>>>31)+2147483647|0}i=2147483647-r|0;n=i>>16;r=Ze(n,a)|0;if((r|0)==1073741824){We[t>>2]=1;o=2147483647}else o=r<<1;a=(Ze((i>>>1)-(n<<15)<<16>>16,a)|0)>>15;r=o+(a<<1)|0;if((o^a|0)>0&(r^o|0)<0){We[t>>2]=1;r=(o>>>31)+2147483647|0}o=r>>16;a=e>>16;n=Ze(o,a)|0;n=(n|0)==1073741824?2147483647:n<<1;i=(Ze((r>>>1)-(o<<15)<<16>>16,a)|0)>>15;t=(i<<1)+n|0;t=(i^n|0)>0&(t^n|0)<0?(n>>>31)+2147483647|0:t;a=(Ze(o,(e>>>1)-(a<<15)<<16>>16)|0)>>15;e=t+(a<<1)|0;e=(t^a|0)>0&(e^t|0)<0?(t>>>31)+2147483647|0:e;t=e<<2;return((t>>2|0)==(e|0)?t:e>>31^2147483647)|0}function hn(e,r){e=e|0;r=r|0;var n=0,t=0,i=0,o=0;if(!e){o=-1;return o|0}We[e>>2]=0;n=xi(192)|0;if(!n){o=-1;return o|0}t=n+176|0;Ve[t>>1]=0;Ve[t+2>>1]=0;Ve[t+4>>1]=0;Ve[t+6>>1]=0;Ve[t+8>>1]=0;Ve[t+10>>1]=0;t=n;i=r;o=t+20|0;do{Ve[t>>1]=Ve[i>>1]|0;t=t+2|0;i=i+2|0}while((t|0)<(o|0));t=n+20|0;i=r;o=t+20|0;do{Ve[t>>1]=Ve[i>>1]|0;t=t+2|0;i=i+2|0}while((t|0)<(o|0));t=n+40|0;i=r;o=t+20|0;do{Ve[t>>1]=Ve[i>>1]|0;t=t+2|0;i=i+2|0}while((t|0)<(o|0));t=n+60|0;i=r;o=t+20|0;do{Ve[t>>1]=Ve[i>>1]|0;t=t+2|0;i=i+2|0}while((t|0)<(o|0));t=n+80|0;i=r;o=t+20|0;do{Ve[t>>1]=Ve[i>>1]|0;t=t+2|0;i=i+2|0}while((t|0)<(o|0));t=n+100|0;i=r;o=t+20|0;do{Ve[t>>1]=Ve[i>>1]|0;t=t+2|0;i=i+2|0}while((t|0)<(o|0));t=n+120|0;i=r;o=t+20|0;do{Ve[t>>1]=Ve[i>>1]|0;t=t+2|0;i=i+2|0}while((t|0)<(o|0));t=n+140|0;i=r;o=t+20|0;do{Ve[t>>1]=Ve[i>>1]|0;t=t+2|0;i=i+2|0}while((t|0)<(o|0));t=n+160|0;o=t+20|0;do{Ve[t>>1]=0;t=t+2|0}while((t|0)<(o|0));Ve[n+188>>1]=7;Ve[n+190>>1]=32767;We[e>>2]=n;o=0;return o|0}function wn(e,r){e=e|0;r=r|0;var n=0,t=0,i=0;if(!e){i=-1;return i|0}n=e+176|0;Ve[n>>1]=0;Ve[n+2>>1]=0;Ve[n+4>>1]=0;Ve[n+6>>1]=0;Ve[n+8>>1]=0;Ve[n+10>>1]=0;n=e;t=r;i=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(i|0));n=e+20|0;t=r;i=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(i|0));n=e+40|0;t=r;i=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(i|0));n=e+60|0;t=r;i=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(i|0));n=e+80|0;t=r;i=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(i|0));n=e+100|0;t=r;i=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(i|0));n=e+120|0;t=r;i=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(i|0));n=e+140|0;t=r;i=n+20|0;do{Ve[n>>1]=Ve[t>>1]|0;n=n+2|0;t=t+2|0}while((n|0)<(i|0));n=e+160|0;i=n+20|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(i|0));Ve[e+188>>1]=7;Ve[e+190>>1]=32767;i=1;return i|0}function mn(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Hi(r);We[e>>2]=0;return}function En(e,r,n,t,i,o){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;var a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0;g=Ge;Ge=Ge+112|0;k=g+80|0;F=g+60|0;M=g+40|0;_=g;if(r<<16>>16==0?(a=e+178|0,(Ve[a>>1]|0)!=0):0){M=e+180|0;o=e+182|0;n=a;M=Ve[M>>1]|0;t=We[i>>2]|0;F=t+2|0;Ve[t>>1]=M;o=Ve[o>>1]|0;M=t+4|0;Ve[F>>1]=o;F=e+184|0;F=Ve[F>>1]|0;o=t+6|0;Ve[M>>1]=F;M=e+186|0;M=Ve[M>>1]|0;e=t+8|0;Ve[o>>1]=M;n=Ve[n>>1]|0;t=t+10|0;We[i>>2]=t;Ve[e>>1]=n;Ge=g;return}m=_+36|0;E=_+32|0;p=_+28|0;S=_+24|0;b=_+20|0;v=_+16|0;d=_+12|0;h=_+8|0;w=_+4|0;r=_;a=r+40|0;do{We[r>>2]=0;r=r+4|0}while((r|0)<(a|0));c=7;r=0;while(1){u=Ve[e+160+(c<<1)>>1]|0;a=u<<16>>16;if(u<<16>>16<0)a=~((a^-4)>>2);else a=a>>>2;r=Wt(r,a&65535,o)|0;l=c*10|0;u=9;while(1){f=_+(u<<2)|0;s=We[f>>2]|0;R=Ve[e+(u+l<<1)>>1]|0;a=R+s|0;if((R^s|0)>-1&(a^s|0)<0){We[o>>2]=1;a=(s>>>31)+2147483647|0}We[f>>2]=a;if((u|0)>0)u=u+-1|0;else break}if((c|0)>0)c=c+-1|0;else break}a=r<<16>>16;if(r<<16>>16<0)a=~((a^-2)>>1);else a=a>>>1;Ve[F+18>>1]=(We[m>>2]|0)>>>3;Ve[F+16>>1]=(We[E>>2]|0)>>>3;Ve[F+14>>1]=(We[p>>2]|0)>>>3;Ve[F+12>>1]=(We[S>>2]|0)>>>3;Ve[F+10>>1]=(We[b>>2]|0)>>>3;Ve[F+8>>1]=(We[v>>2]|0)>>>3;Ve[F+6>>1]=(We[d>>2]|0)>>>3;Ve[F+4>>1]=(We[h>>2]|0)>>>3;Ve[F+2>>1]=(We[w>>2]|0)>>>3;Ve[F>>1]=(We[_>>2]|0)>>>3;r=e+178|0;a=(((a<<16)+167772160|0)>>>16)+128|0;Ve[r>>1]=a;a=a<<16;if((a|0)<0)a=~((a>>16^-256)>>8);else a=a>>24;Ve[r>>1]=a;if((a|0)<=63){if((a|0)<0){Ve[r>>1]=0;a=0}}else{Ve[r>>1]=63;a=63}R=Bi(a<<8&65535,11560,o)|0;R=R<<16>>16>0?0:R<<16>>16<-14436?-14436:R;Ve[t>>1]=R;Ve[t+2>>1]=R;Ve[t+4>>1]=R;Ve[t+6>>1]=R;R=((R<<16>>16)*5443|0)>>>15&65535;Ve[t+8>>1]=R;Ve[t+10>>1]=R;Ve[t+12>>1]=R;Ve[t+14>>1]=R;pi(F,k,10,o);Ti(k,205,10,o);Ei(k,F,10,o);t=e+182|0;R=e+180|0;Fi(n,8,F,M,t,R,o);o=t;t=r;R=Ve[R>>1]|0;n=We[i>>2]|0;M=n+2|0;Ve[n>>1]=R;o=Ve[o>>1]|0;R=n+4|0;Ve[M>>1]=o;M=e+184|0;M=Ve[M>>1]|0;o=n+6|0;Ve[R>>1]=M;e=e+186|0;e=Ve[e>>1]|0;R=n+8|0;Ve[o>>1]=e;e=Ve[t>>1]|0;n=n+10|0;We[i>>2]=n;Ve[R>>1]=e;Ge=g;return}function pn(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0;f=Ge;Ge=Ge+16|0;a=f+2|0;l=f;s=e+176|0;o=(Xe[s>>1]|0)+1|0;o=(o&65535|0)==8?0:o&65535;Ve[s>>1]=o;o=e+((o<<16>>16)*10<<1)|0;i=o+20|0;do{Ve[o>>1]=Ve[r>>1]|0;o=o+2|0;r=r+2|0}while((o|0)<(i|0));r=0;i=160;while(1){o=Ve[n>>1]|0;r=(Ze(o<<1,o)|0)+r|0;if((r|0)<0){r=2147483647;break}i=i+-1<<16>>16;if(!(i<<16>>16))break;else n=n+2|0}si(r,a,l,t);r=Ve[a>>1]|0;a=r<<16>>16;n=a<<10;if((n|0)!=(a<<26>>16|0)){We[t>>2]=1;n=r<<16>>16>0?32767:-32768}Ve[e+160+(Ve[s>>1]<<1)>>1]=(((Ve[l>>1]|0)>>>5)+n<<16)+-558432256>>17;Ge=f;return}function Sn(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0;o=e+190|0;a=Wt(Ve[o>>1]|0,1,t)|0;Ve[o>>1]=a;i=e+188|0;do{if(!(r<<16>>16)){e=Ve[i>>1]|0;if(!(e<<16>>16)){Ve[o>>1]=0;We[n>>2]=8;e=1;break}o=(e&65535)+65535&65535;Ve[i>>1]=o;if((Wt(a,o,t)|0)<<16>>16<30){We[n>>2]=8;e=0}else e=0}else{Ve[i>>1]=7;e=0}}while(0);return e|0}function bn(e,r,n,t,i,o,a,s){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;if(!(o<<16>>16)){o=e<<16>>16;if(((o<<16)+-5570560|0)<65536){r=(o*3|0)+-58+(r<<16>>16)|0;r=r&65535;return r|0}else{r=o+112|0;r=r&65535;return r|0}}if(!(a<<16>>16)){s=(e&65535)-(t&65535)<<16;r=(r<<16>>16)+2+(s>>15)+(s>>16)|0;r=r&65535;return r|0}t=t<<16>>16;t=(((n&65535)-t<<16)+-327680|0)>0?t+5&65535:n;i=i<<16>>16;n=e<<16>>16;t=(((i-(t&65535)<<16)+-262144|0)>0?i+65532&65535:t)<<16>>16;i=t*196608|0;e=i+-393216>>16;o=((r&65535)<<16)+(n*196608|0)>>16;if(!(e-o&32768)){r=n+5-t|0;r=r&65535;return r|0}if((i+196608>>16|0)>(o|0)){r=o+3-e|0;r=r&65535;return r|0}else{r=n+11-t|0;r=r&65535;return r|0}return 0}function vn(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;i=e<<16>>16;do{if(!(t<<16>>16))if(e<<16>>16<95){i=((i*393216|0)+-6881280>>16)+(r<<16>>16)|0;break}else{i=i+368|0;break}else i=((((i-(n&65535)|0)*393216|0)+196608|0)>>>16)+(r&65535)|0}while(0);return i&65535|0}function _n(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0;i=We[t+96>>2]|0;if(e>>>0<8){l=(We[t+100>>2]|0)+(e<<2)|0;s=We[l>>2]|0;Ke[n>>0]=Ve[r+(Ve[s>>1]<<1)>>1]<<4|e|Ve[r+(Ve[s+2>>1]<<1)>>1]<<5|Ve[r+(Ve[s+4>>1]<<1)>>1]<<6|Ve[r+(Ve[s+6>>1]<<1)>>1]<<7;s=i+(e<<1)|0;t=Ve[s>>1]|0;if((t+-7|0)>4){i=4;a=4;e=1;while(1){f=Ve[r+(Ve[(We[l>>2]|0)+(i<<1)>>1]<<1)>>1]|0;t=n+(e<<16>>16)|0;Ke[t>>0]=f;f=Xe[r+(Ve[(We[l>>2]|0)+((a|1)<<16>>16<<1)>>1]<<1)>>1]<<1|f&65535;Ke[t>>0]=f;f=Xe[r+(Ve[(We[l>>2]|0)+((a|2)<<16>>16<<1)>>1]<<1)>>1]<<2|f;Ke[t>>0]=f;f=Xe[r+(Ve[(We[l>>2]|0)+((a|3)<<16>>16<<1)>>1]<<1)>>1]<<3|f;Ke[t>>0]=f;f=Xe[r+(Ve[(We[l>>2]|0)+(a+4<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<4|f;Ke[t>>0]=f;f=Xe[r+(Ve[(We[l>>2]|0)+(a+5<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<5|f;Ke[t>>0]=f;f=Xe[r+(Ve[(We[l>>2]|0)+(a+6<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<6|f;Ke[t>>0]=f;o=a+8<<16>>16;e=e+1<<16>>16;Ke[t>>0]=Xe[r+(Ve[(We[l>>2]|0)+(a+7<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<7|f;i=o<<16>>16;t=Ve[s>>1]|0;if((i|0)>=(t+-7|0))break;else a=o}}else{o=4;e=1}s=t+4&7;if(!s)return;i=n+(e<<16>>16)|0;Ke[i>>0]=0;t=0;a=0;e=0;while(1){a=(Xe[r+(Ve[(We[l>>2]|0)+(o<<16>>16<<1)>>1]<<1)>>1]&255)<<t|a&255;Ke[i>>0]=a;e=e+1<<16>>16;t=e<<16>>16;if((t|0)>=(s|0))break;else o=o+1<<16>>16}return}if((e|0)==15){Ke[n>>0]=15;return}Ke[n>>0]=Ve[r>>1]<<4|e|Ve[r+2>>1]<<5|Ve[r+4>>1]<<6|Ve[r+6>>1]<<7;t=i+(e<<1)|0;e=Ve[t>>1]|0;i=((e&65535)<<16)+262144>>16;l=i&-8;a=(l+524281|0)>>>3&65535;if(a<<16>>16>0){i=((i&-8)+524281|0)>>>3;s=((i<<3)+524280&524280)+12|0;o=1;e=r+8|0;while(1){Ke[n+(o<<16>>16)>>0]=Xe[e+2>>1]<<1|Xe[e>>1]|Xe[e+4>>1]<<2|Xe[e+6>>1]<<3|Xe[e+8>>1]<<4|Xe[e+10>>1]<<5|Xe[e+12>>1]<<6|Xe[e+14>>1]<<7;if(a<<16>>16>1){a=a+-1<<16>>16;o=o+1<<16>>16;e=e+16|0}else break}e=Ve[t>>1]|0;o=(i<<16)+65536>>16}else{s=4;o=1}e=(0-l|4)+(e&65535)<<16;a=e>>16;if(!a)return;o=n+o|0;Ke[o>>0]=0;if((e|0)>0){e=0;i=0;t=0}else return;do{i=i&255|Ve[r+(s+e<<1)>>1]<<e;Ke[o>>0]=i;t=t+1<<16>>16;e=t<<16>>16}while((e|0)<(a|0));return}function kn(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0;u=We[t+100>>2]|0;f=We[t+96>>2]|0;Ke[n>>0]=e&15;f=f+(e<<1)|0;i=Ve[f>>1]|0;if(e>>>0>=8){s=((i&65535)<<16)+-458752|0;if((s|0)>0){l=1;a=r;while(1){r=a+16|0;t=l+1<<16>>16;Ke[n+(l<<16>>16)>>0]=Xe[a+14>>1]|Xe[a+12>>1]<<1|((Xe[a+2>>1]<<6|Xe[a>>1]<<7|Xe[a+4>>1]<<5|Xe[a+6>>1]<<4)&240|Xe[a+8>>1]<<3|Xe[a+10>>1]<<2)&252;s=s+-524288&-65536;if((s|0)<=0)break;else{l=t;a=r}}i=Ve[f>>1]|0}else t=1;l=i&7;i=n+(t<<16>>16)|0;Ke[i>>0]=0;if(!l)return;else{o=0;a=0;s=0;t=r}while(1){a=a&255|Ve[t>>1]<<7-o;Ke[i>>0]=a;s=s+1<<16>>16;o=s<<16>>16;if((o|0)>=(l|0))break;else t=t+2|0}return}a=i<<16>>16;if(i<<16>>16>7){i=u+(e<<2)|0;t=0;l=0;o=1;while(1){c=Xe[r+(Ve[(We[i>>2]|0)+(t<<1)>>1]<<1)>>1]<<7;a=n+(o<<16>>16)|0;Ke[a>>0]=c;c=Xe[r+(Ve[(We[i>>2]|0)+((l|1)<<16>>16<<1)>>1]<<1)>>1]<<6|c;Ke[a>>0]=c;c=Xe[r+(Ve[(We[i>>2]|0)+((l|2)<<16>>16<<1)>>1]<<1)>>1]<<5|c;Ke[a>>0]=c;c=Xe[r+(Ve[(We[i>>2]|0)+((l|3)<<16>>16<<1)>>1]<<1)>>1]<<4|c;Ke[a>>0]=c;c=Xe[r+(Ve[(We[i>>2]|0)+((l|4)<<16>>16<<1)>>1]<<1)>>1]<<3|c&240;Ke[a>>0]=c;c=Xe[r+(Ve[(We[i>>2]|0)+((l|5)<<16>>16<<1)>>1]<<1)>>1]<<2|c;Ke[a>>0]=c;c=Xe[r+(Ve[(We[i>>2]|0)+((l|6)<<16>>16<<1)>>1]<<1)>>1]<<1|c;Ke[a>>0]=c;s=l+8<<16>>16;o=o+1<<16>>16;Ke[a>>0]=c&254|Xe[r+(Ve[(We[i>>2]|0)+((l|7)<<16>>16<<1)>>1]<<1)>>1];t=s<<16>>16;a=Ve[f>>1]|0;if((t|0)>=(a+-7|0))break;else l=s}}else{s=0;o=1}f=a&7;l=n+(o<<16>>16)|0;Ke[l>>0]=0;if(!f)return;o=u+(e<<2)|0;i=0;t=0;a=0;while(1){t=(Xe[r+(Ve[(We[o>>2]|0)+(s<<16>>16<<1)>>1]<<1)>>1]&255)<<7-i|t&255;Ke[l>>0]=t;a=a+1<<16>>16;i=a<<16>>16;if((i|0)>=(f|0))break;else s=s+1<<16>>16}return}function Fn(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0;u=We[t+100>>2]|0;f=We[t+96>>2]|0;Ke[n>>0]=e<<3;f=f+(e<<1)|0;i=Ve[f>>1]|0;if(e>>>0>=8){s=((i&65535)<<16)+-458752|0;if((s|0)>0){l=1;a=r;while(1){r=a+16|0;t=l+1<<16>>16;Ke[n+(l<<16>>16)>>0]=Xe[a+14>>1]|Xe[a+12>>1]<<1|((Xe[a+2>>1]<<6|Xe[a>>1]<<7|Xe[a+4>>1]<<5|Xe[a+6>>1]<<4)&240|Xe[a+8>>1]<<3|Xe[a+10>>1]<<2)&252;s=s+-524288&-65536;if((s|0)<=0)break;else{l=t;a=r}}i=Ve[f>>1]|0}else t=1;l=i&7;i=n+(t<<16>>16)|0;Ke[i>>0]=0;if(!l)return;else{o=0;a=0;s=0;t=r}while(1){a=a&255|Ve[t>>1]<<7-o;Ke[i>>0]=a;s=s+1<<16>>16;o=s<<16>>16;if((o|0)>=(l|0))break;else t=t+2|0}return}a=i<<16>>16;if(i<<16>>16>7){i=u+(e<<2)|0;t=0;l=0;o=1;while(1){c=Xe[r+(Ve[(We[i>>2]|0)+(t<<1)>>1]<<1)>>1]<<7;a=n+(o<<16>>16)|0;Ke[a>>0]=c;c=Xe[r+(Ve[(We[i>>2]|0)+((l|1)<<16>>16<<1)>>1]<<1)>>1]<<6|c;Ke[a>>0]=c;c=Xe[r+(Ve[(We[i>>2]|0)+((l|2)<<16>>16<<1)>>1]<<1)>>1]<<5|c;Ke[a>>0]=c;c=Xe[r+(Ve[(We[i>>2]|0)+((l|3)<<16>>16<<1)>>1]<<1)>>1]<<4|c;Ke[a>>0]=c;c=Xe[r+(Ve[(We[i>>2]|0)+((l|4)<<16>>16<<1)>>1]<<1)>>1]<<3|c&240;Ke[a>>0]=c;c=Xe[r+(Ve[(We[i>>2]|0)+((l|5)<<16>>16<<1)>>1]<<1)>>1]<<2|c;Ke[a>>0]=c;c=Xe[r+(Ve[(We[i>>2]|0)+((l|6)<<16>>16<<1)>>1]<<1)>>1]<<1|c;Ke[a>>0]=c;s=l+8<<16>>16;o=o+1<<16>>16;Ke[a>>0]=c&254|Xe[r+(Ve[(We[i>>2]|0)+((l|7)<<16>>16<<1)>>1]<<1)>>1];t=s<<16>>16;a=Ve[f>>1]|0;if((t|0)>=(a+-7|0))break;else l=s}}else{s=0;o=1}f=a&7;l=n+(o<<16>>16)|0;Ke[l>>0]=0;if(!f)return;o=u+(e<<2)|0;i=0;t=0;a=0;while(1){t=(Xe[r+(Ve[(We[o>>2]|0)+(s<<16>>16<<1)>>1]<<1)>>1]&255)<<7-i|t&255;Ke[l>>0]=t;a=a+1<<16>>16;i=a<<16>>16;if((i|0)>=(f|0))break;else s=s+1<<16>>16}return}function Mn(e){e=e|0;var r=0;if(!e){e=-1;return e|0}We[e>>2]=0;r=xi(16)|0;if(!r){e=-1;return e|0}Ve[r>>1]=0;Ve[r+2>>1]=0;Ve[r+4>>1]=0;Ve[r+6>>1]=0;Ve[r+8>>1]=0;Ve[r+10>>1]=0;Ve[r+12>>1]=0;Ve[r+14>>1]=0;We[e>>2]=r;e=0;return e|0}function gn(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=0;Ve[e+2>>1]=0;Ve[e+4>>1]=0;Ve[e+6>>1]=0;Ve[e+8>>1]=0;Ve[e+10>>1]=0;Ve[e+12>>1]=0;Ve[e+14>>1]=0;e=0;return e|0}function Rn(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Hi(r);We[e>>2]=0;return}function An(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0,a=0,s=0,l=0,f=0;s=r<<16>>16<2722?0:r<<16>>16<5444?1:2;a=Ci(n,1,i)|0;f=e+4|0;if(!(n<<16>>16>200?a<<16>>16>(Ve[f>>1]|0):0)){a=Ve[e>>1]|0;if(a<<16>>16){o=a+-1<<16>>16;Ve[e>>1]=o;o=o<<16>>16!=0;l=5}}else{Ve[e>>1]=8;o=1;l=5}if((l|0)==5)if((s&65535)<2&o)s=(s&65535)+1&65535;l=e+6|0;Ve[l>>1]=r;o=ri(l,5)|0;if(!(s<<16>>16!=0|o<<16>>16>5443))if(o<<16>>16<0)o=16384;else{o=o<<16>>16;o=(((o<<18>>18|0)==(o|0)?o<<2:o>>>15^32767)<<16>>16)*24660>>15;if((o|0)>32767){We[i>>2]=1;o=32767}o=16384-o&65535}else o=0;a=e+2|0;if(!(Ve[a>>1]|0))o=Pi(o,1,i)|0;Ve[t>>1]=o;Ve[a>>1]=o;Ve[f>>1]=n;t=e+12|0;Ve[e+14>>1]=Ve[t>>1]|0;n=e+10|0;Ve[t>>1]=Ve[n>>1]|0;e=e+8|0;Ve[n>>1]=Ve[e>>1]|0;Ve[e>>1]=Ve[l>>1]|0;return}function yn(e){e=e|0;var r=0,n=0,t=0,i=0,o=0,a=0;if(!e){e=-1;return e|0}We[e>>2]=0;r=xi(68)|0;t=r;if(!r){e=-1;return e|0}We[r+28>>2]=0;i=r+64|0;We[i>>2]=0;o=r+32|0;if(((Zt(o)|0)<<16>>16==0?(a=r+48|0,(Zt(a)|0)<<16>>16==0):0)?(Mn(i)|0)<<16>>16==0:0){n=r+32|0;do{Ve[r>>1]=0;r=r+2|0}while((r|0)<(n|0));Zt(o)|0;Zt(a)|0;gn(We[i>>2]|0)|0;We[e>>2]=t;e=0;return e|0}Rn(i);Hi(r);e=-1;return e|0}function On(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Rn(r+64|0);Hi(We[e>>2]|0);We[e>>2]=0;return}function Tn(e){e=e|0;var r=0,n=0,t=0;if(!e){t=-1;return t|0}r=e+32|0;n=e;t=n+32|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));Zt(r)|0;Zt(e+48|0)|0;gn(We[e+64>>2]|0)|0;t=0;return t|0}function Dn(e,r,n,t,i,o,a,s,l,f,u,c,d,h,w,m,E,p,S){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;u=u|0;c=c|0;d=d|0;h=h|0;w=w|0;m=m|0;E=E|0;p=p|0;S=S|0;var b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0;O=Ge;Ge=Ge+48|0;v=O+34|0;k=O+32|0;M=O+30|0;F=O+28|0;_=O+18|0;b=O+8|0;g=O+6|0;R=O+4|0;A=O+2|0;y=O;if(r){u=e+32|0;Jt(u,r,i,v,k,g,R,S);do{if((r|0)!=7){Zr(r,o,a,s,l,f,_,b,y,A,S);if((r|0)==5){mt(We[e+64>>2]|0,n,t,i,_,b,Ve[g>>1]|0,Ve[R>>1]|0,Ve[v>>1]|0,Ve[k>>1]|0,40,Ve[y>>1]|0,Ve[A>>1]|0,c,w,m,M,F,E,p,S);break}else{e=St(r,Ve[v>>1]|0,Ve[k>>1]|0,_,b,c,w,m,M,F,p,S)|0;o=We[E>>2]|0;We[E>>2]=o+2;Ve[o>>1]=e;break}}else{Ve[m>>1]=Nn(a,l,S)|0;e=Et(7,Ve[v>>1]|0,Ve[k>>1]|0,m,M,F,We[p+68>>2]|0,S)|0;o=We[E>>2]|0;We[E>>2]=o+2;Ve[o>>1]=e}}while(0);Qt(u,Ve[M>>1]|0,Ve[F>>1]|0);Ge=O;return}if(!(u<<16>>16)){Jt(e+48|0,0,i,v,k,g,R,S);Zr(0,o,a,s,l,f,_,b,y,A,S);Jr(o,g,R,S);o=ht(e+32|0,Ve[e>>1]|0,Ve[e+2>>1]|0,e+8|0,e+18|0,Ve[e+4>>1]|0,Ve[e+6>>1]|0,i,Ve[v>>1]|0,Ve[k>>1]|0,b,_,Ve[g>>1]|0,Ve[R>>1]|0,c,d,h,w,m,S)|0;Ve[We[e+28>>2]>>1]=o;Ge=O;return}u=We[E>>2]|0;We[E>>2]=u+2;We[e+28>>2]=u;u=e+48|0;n=e+32|0;d=n;d=Xe[d>>1]|Xe[d+2>>1]<<16;n=n+4|0;n=Xe[n>>1]|Xe[n+2>>1]<<16;E=u;h=E;Ve[h>>1]=d;Ve[h+2>>1]=d>>>16;E=E+4|0;Ve[E>>1]=n;Ve[E+2>>1]=n>>>16;E=e+40|0;n=E;n=Xe[n>>1]|Xe[n+2>>1]<<16;E=E+4|0;E=Xe[E>>1]|Xe[E+2>>1]<<16;h=e+56|0;d=h;Ve[d>>1]=n;Ve[d+2>>1]=n>>>16;h=h+4|0;Ve[h>>1]=E;Ve[h+2>>1]=E>>>16;h=e+2|0;Jt(u,0,i,e,h,g,R,S);Zr(0,o,a,s,l,f,e+18|0,e+8|0,y,A,S);s=(Xe[A>>1]|0)+1|0;E=Ve[y>>1]|0;d=s<<16>>16;if((s&65535)<<16>>16<0){p=0-d<<16;if((p|0)<983040)p=E<<16>>16>>(p>>16)&65535;else p=0}else{E=E<<16>>16;p=E<<d;if((p<<16>>16>>d|0)==(E|0))p=p&65535;else p=(E>>>15^32767)&65535}Ve[m>>1]=p;Jr(o,e+4|0,e+6|0,S);dt(u,Ve[e>>1]|0,Ve[h>>1]|0,Ve[A>>1]|0,Ve[y>>1]|0,S);Ge=O;return}function Nn(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0;i=10;n=e;t=r;e=0;while(1){e=(Ze(Ve[t>>1]>>1,Ve[n>>1]|0)|0)+e|0;e=e+(Ze(Ve[t+2>>1]>>1,Ve[n+2>>1]|0)|0)|0;e=e+(Ze(Ve[t+4>>1]>>1,Ve[n+4>>1]|0)|0)|0;e=e+(Ze(Ve[t+6>>1]>>1,Ve[n+6>>1]|0)|0)|0;i=i+-1<<16>>16;if(!(i<<16>>16))break;else{n=n+8|0;t=t+8|0}}n=e<<1;i=bi(n|1)|0;o=i<<16>>16;n=(i<<16>>16<17?n>>17-o:n<<o+-17)&65535;if(n<<16>>16<1){r=0;return r|0}else{i=20;t=r;e=0}while(1){r=Ve[t>>1]>>1;r=((Ze(r,r)|0)>>>2)+e|0;e=Ve[t+2>>1]>>1;e=r+((Ze(e,e)|0)>>>2)|0;i=i+-1<<16>>16;if(!(i<<16>>16))break;else t=t+4|0}e=e<<3;i=bi(e)|0;r=i<<16>>16;n=Gt(n,(i<<16>>16<16?e>>16-r:e<<r+-16)&65535)|0;r=(o<<16)+327680-(r<<16)|0;e=r>>16;if((r|0)>65536)e=n<<16>>16>>e+-1;else e=n<<16>>16<<1-e;r=e&65535;return r|0}function Pn(e,r,n,t,i,o){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;var a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0;We[o>>2]=0;u=i<<16>>16;l=u>>>2&65535;d=l<<16>>16==0;if(d)s=0;else{f=l;a=n;s=0;while(1){h=Ve[a>>1]|0;h=(Ze(h,h)|0)+s|0;s=Ve[a+2>>1]|0;s=h+(Ze(s,s)|0)|0;h=Ve[a+4>>1]|0;h=s+(Ze(h,h)|0)|0;s=Ve[a+6>>1]|0;s=h+(Ze(s,s)|0)|0;f=f+-1<<16>>16;if(!(f<<16>>16))break;else a=a+8|0}}if(!((s>>>31^1)&(s|0)<1073741824)){s=u>>>1&65535;if(!(s<<16>>16))s=1;else{a=s;f=n;s=0;while(1){h=Ve[f>>1]>>2;h=(Ze(h,h)|0)+s|0;s=Ve[f+2>>1]>>2;s=h+(Ze(s,s)|0)|0;a=a+-1<<16>>16;if(!(a<<16>>16))break;else f=f+4|0}s=s<<1|1}h=(bi(s)|0)<<16>>16;c=h+65532&65535;h=Ni(s<<h,o)|0}else{u=s<<1|1;h=bi(u)|0;c=h;h=Ni(u<<(h<<16>>16),o)|0}We[o>>2]=0;do{if(!(i<<16>>16)){s=1;w=14}else{u=i;f=r;s=n;i=0;while(1){m=Ze(Ve[s>>1]|0,Ve[f>>1]|0)|0;a=m+i|0;if((m^i|0)>0&(a^i|0)<0)break;u=u+-1<<16>>16;if(!(u<<16>>16)){w=13;break}else{f=f+2|0;s=s+2|0;i=a}}if((w|0)==13){s=a<<1|1;w=14;break}We[o>>2]=1;if(d)s=1;else{s=r;a=0;while(1){a=(Ze(Ve[n>>1]>>2,Ve[s>>1]|0)|0)+a|0;a=a+(Ze(Ve[n+2>>1]>>2,Ve[s+2>>1]|0)|0)|0;a=a+(Ze(Ve[n+4>>1]>>2,Ve[s+4>>1]|0)|0)|0;a=a+(Ze(Ve[n+6>>1]>>2,Ve[s+6>>1]|0)|0)|0;l=l+-1<<16>>16;if(!(l<<16>>16))break;else{s=s+8|0;n=n+8|0}}s=a<<1|1}n=(bi(s)|0)<<16>>16;a=n+65532&65535;n=Ni(s<<n,o)|0}}while(0);if((w|0)==14){n=bi(s)|0;a=n;n=Ni(s<<(n<<16>>16),o)|0}Ve[t>>1]=h;s=c<<16>>16;Ve[t+2>>1]=15-s;Ve[t+4>>1]=n;a=a<<16>>16;Ve[t+6>>1]=15-a;if(n<<16>>16<4){m=0;return m|0}a=Pi(Gt(n<<16>>16>>>1&65535,h)|0,a-s&65535,o)|0;a=a<<16>>16>19661?19661:a;if((e|0)!=7){m=a;return m|0}m=a&65532;return m|0}function Cn(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;var s=0,l=0,f=0,u=0,c=0,d=0;l=(t&65535)+65535&65535;if(l<<16>>16>i<<16>>16){c=t+-1<<16>>16<<16>>16;t=-2147483648;while(1){f=We[e+(0-c<<2)>>2]|0;s=f<<1;f=(s>>1|0)==(f|0)?s:f>>31^2147483647;s=We[e+(~c<<2)>>2]|0;u=f-s|0;if(((u^f)&(f^s)|0)<0){We[a>>2]=1;u=(f>>>31)+2147483647|0}f=We[e+(1-c<<2)>>2]|0;s=u-f|0;if(((s^u)&(f^u)|0)<0){We[a>>2]=1;s=(u>>>31)+2147483647|0}u=Bn(s)|0;t=(u|0)<(t|0)?t:u;l=l+-1<<16>>16;if(l<<16>>16<=i<<16>>16){i=t;break}else c=c+-1|0}}else i=-2147483648;e=n<<16>>16>0;if(e){t=0;s=r;l=0;while(1){u=Ve[s>>1]|0;u=Ze(u,u)|0;if((u|0)!=1073741824){f=(u<<1)+l|0;if((u^l|0)>0&(f^l|0)<0){We[a>>2]=1;l=(l>>>31)+2147483647|0}else l=f}else{We[a>>2]=1;l=2147483647}t=t+1<<16>>16;if(t<<16>>16>=n<<16>>16)break;else s=s+2|0}if(e){e=0;c=r;t=r+-2|0;s=0;while(1){u=Ze(Ve[t>>1]|0,Ve[c>>1]|0)|0;if((u|0)!=1073741824){f=(u<<1)+s|0;if((u^s|0)>0&(f^s|0)<0){We[a>>2]=1;s=(s>>>31)+2147483647|0}else s=f}else{We[a>>2]=1;s=2147483647}e=e+1<<16>>16;if(e<<16>>16>=n<<16>>16)break;else{c=c+2|0;t=t+2|0}}}else s=0}else{l=0;s=0}t=l<<1;t=(t>>1|0)==(l|0)?t:l>>31^2147483647;n=s<<1;n=(n>>1|0)==(s|0)?n:s>>31^2147483647;l=t-n|0;if(((l^t)&(n^t)|0)<0){We[a>>2]=1;l=(t>>>31)+2147483647|0}e=Bn(l)|0;c=((bi(i)|0)&65535)+65535|0;l=c<<16>>16;if((c&65535)<<16>>16>0){t=i<<l;if((t>>l|0)!=(i|0))t=i>>31^2147483647}else{l=0-l<<16;if((l|0)<2031616)t=i>>(l>>16);else t=0}u=bi(e)|0;s=u<<16>>16;if(u<<16>>16>0){l=e<<s;if((l>>s|0)==(e|0))d=33;else{l=e>>31^2147483647;d=33}}else{l=0-s<<16;if((l|0)<2031616){l=e>>(l>>16);d=33}else f=0}if((d|0)==33)if(l>>>0>65535)f=Gt(t>>>16&65535,l>>>16&65535)|0;else f=0;l=u&65535;d=(c&65535)-l|0;t=d&65535;if(!(d&32768)){a=Pi(f,t,a)|0;Ve[o>>1]=a;return 0}if(t<<16>>16!=-32768){a=l-c|0;s=a<<16>>16;if((a&65535)<<16>>16<0){s=0-s<<16;if((s|0)>=983040){a=0;Ve[o>>1]=a;return 0}a=f<<16>>16>>(s>>16)&65535;Ve[o>>1]=a;return 0}}else s=32767;t=f<<16>>16;l=t<<s;if((l<<16>>16>>s|0)==(t|0)){a=l&65535;Ve[o>>1]=a;return 0}a=(t>>>15^32767)&65535;Ve[o>>1]=a;return 0}function In(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;if(n<<16>>16)r=r<<16>>16<<1&65535;if(r<<16>>16<0){e=e+-2|0;r=(r&65535)+6&65535}n=r<<16>>16;t=6-n<<16>>16;r=(Ze(Ve[3468+(n<<1)>>1]|0,Ve[e>>1]|0)|0)+16384|0;r=r+(Ze(Ve[3468+(t<<1)>>1]|0,Ve[e+2>>1]|0)|0)|0;r=r+(Ze(Ve[3468+(n+6<<1)>>1]|0,Ve[e+-2>>1]|0)|0)|0;r=r+(Ze(Ve[3468+(t+6<<1)>>1]|0,Ve[e+4>>1]|0)|0)|0;r=(Ze(Ve[3468+(n+12<<1)>>1]|0,Ve[e+-4>>1]|0)|0)+r|0;r=r+(Ze(Ve[3468+(t+12<<1)>>1]|0,Ve[e+6>>1]|0)|0)|0;n=r+(Ze(Ve[3468+(n+18<<1)>>1]|0,Ve[e+-6>>1]|0)|0)|0;return(n+(Ze(Ve[3468+(t+18<<1)>>1]|0,Ve[e+8>>1]|0)|0)|0)>>>15&65535|0}function Bn(e){e=e|0;e=e-(e>>>31)|0;return e>>31^e|0}function Ln(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0;if(!(e<<16>>16))return;else{i=3518;o=3538;t=n}while(1){t=t+2|0;r=r+2|0;l=Ve[r>>1]|0;s=Ve[i>>1]|0;n=Ze(s,l)|0;n=(n|0)==1073741824?2147483647:n<<1;l=(Ze(Ve[o>>1]|0,l)|0)>>15;a=(l<<1)+n|0;a=(n^l|0)>0&(a^n|0)<0?(n>>>31)+2147483647|0:a;s=(Ze(s,Ve[t>>1]|0)|0)>>15;n=a+(s<<1)|0;n=(a^s|0)>0&(n^a|0)<0?(a>>>31)+2147483647|0:n;Ve[r>>1]=n>>>16;Ve[t>>1]=(n>>>1)-(n>>16<<15);e=e+-1<<16>>16;if(!(e<<16>>16))break;else{i=i+2|0;o=o+2|0}}return}function Un(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0;t=e&65535;i=t<<16;r=r<<16>>16;e=(r<<1)+i|0;if(!((r^i|0)>0&(e^i|0)<0)){i=e;return i|0}We[n>>2]=1;i=(t>>>15)+2147483647|0;return i|0}function xn(e){e=e|0;var r=0,n=0,t=0;if(!e){t=-1;return t|0}We[e>>2]=0;r=xi(22)|0;if(!r){t=-1;return t|0}Ve[r>>1]=4096;n=r+2|0;t=n+20|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));We[e>>2]=r;t=0;return t|0}function Hn(e){e=e|0;var r=0;if(!e){r=-1;return r|0}Ve[e>>1]=4096;e=e+2|0;r=e+20|0;do{Ve[e>>1]=0;e=e+2|0}while((e|0)<(r|0));r=0;return r|0}function zn(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Hi(r);We[e>>2]=0;return}function Yn(e,r,n,t,i,o){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;var a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0,C=0,I=0;P=Ge;Ge=Ge+96|0;D=P+66|0;N=P+44|0;T=P+22|0;s=P;R=r+2|0;O=n+2|0;y=(Ve[O>>1]<<1)+(Xe[R>>1]<<16)|0;a=Bn(y)|0;a=dn(a,Ve[r>>1]|0,Ve[n>>1]|0,o)|0;if((y|0)>0)a=qn(a)|0;M=a>>16;Ve[i>>1]=Ni(a,o)|0;S=a>>20;A=D+2|0;Ve[A>>1]=S;y=N+2|0;Ve[y>>1]=(a>>>5)-(S<<15);S=Ze(M,M)|0;S=(S|0)==1073741824?2147483647:S<<1;M=(Ze((a>>>1)-(M<<15)<<16>>16,M)|0)>>15;g=M<<1;F=g+S|0;F=(M^S|0)>0&(F^S|0)<0?(S>>>31)+2147483647|0:F;g=F+g|0;g=2147483647-(Bn((F^M|0)>0&(g^F|0)<0?(F>>>31)+2147483647|0:g)|0)|0;F=g>>16;M=Ve[r>>1]|0;S=Ze(F,M)|0;S=(S|0)==1073741824?2147483647:S<<1;M=(Ze((g>>>1)-(F<<15)<<16>>16,M)|0)>>15;g=(M<<1)+S|0;g=(M^S|0)>0&(g^S|0)<0?(S>>>31)+2147483647|0:g;F=(Ze(Ve[n>>1]|0,F)|0)>>15;S=g+(F<<1)|0;S=(g^F|0)>0&(S^g|0)<0?(g>>>31)+2147483647|0:S;g=bi(S)|0;S=S<<(g<<16>>16);F=T+2|0;M=s+2|0;l=S;S=(S>>>1)-(S>>16<<15)|0;b=s+4|0;v=T+4|0;_=2;k=2;while(1){p=l>>>16;a=p&65535;w=S&65535;m=k+-1|0;u=D+(m<<1)|0;E=N+(m<<1)|0;h=1;d=u;c=E;f=R;s=O;l=0;while(1){C=Ve[f>>1]|0;I=((Ze(Ve[c>>1]|0,C)|0)>>15)+l|0;l=Ve[d>>1]|0;l=I+(Ze(l,C)|0)+((Ze(l,Ve[s>>1]|0)|0)>>15)|0;h=h+1<<16>>16;if((h<<16>>16|0)>=(k|0))break;else{d=d+-2|0;c=c+-2|0;f=f+2|0;s=s+2|0}}I=(Xe[r+(k<<1)>>1]<<16)+(l<<5)+(Ve[n+(k<<1)>>1]<<1)|0;l=dn(Bn(I)|0,a,w,o)|0;if((I|0)>0)l=qn(l)|0;s=g<<16>>16;if(g<<16>>16>0){a=l<<s;if((a>>s|0)!=(l|0))a=l>>31^2147483647}else{s=0-s<<16;if((s|0)<2031616)a=l>>(s>>16);else a=0}h=a>>16;if((k|0)<5)Ve[i+(m<<1)>>1]=(a+32768|0)>>>16;I=(a>>>16)-(a>>>31)|0;if(((I<<16>>31^I)&65535)<<16>>16>32750){a=16;break}c=(a>>>1)-(h<<15)<<16>>16;d=1;l=E;s=F;f=M;while(1){C=(Ze(Ve[l>>1]|0,h)|0)>>15;E=Ve[u>>1]|0;I=(Ze(E,c)|0)>>15;E=Ze(E,h)|0;I=E+C+(Ve[N+(d<<1)>>1]|0)+(Ve[D+(d<<1)>>1]<<15)+I|0;Ve[s>>1]=I>>>15;Ve[f>>1]=I&32767;d=d+1|0;if((d&65535)<<16>>16==_<<16>>16)break;else{u=u+-2|0;l=l+-2|0;s=s+2|0;f=f+2|0}}Ve[v>>1]=a>>20;Ve[b>>1]=(a>>>5)-(Ve[T+(k<<1)>>1]<<15);C=Ze(h,h)|0;C=(C|0)==1073741824?2147483647:C<<1;a=(Ze(c,h)|0)>>15;I=a<<1;s=I+C|0;s=(a^C|0)>0&(s^C|0)<0?(C>>>31)+2147483647|0:s;I=s+I|0;I=2147483647-(Bn((s^a|0)>0&(I^s|0)<0?(s>>>31)+2147483647|0:I)|0)|0;s=I>>16;a=p<<16>>16;a=((Ze(s,S<<16>>16)|0)>>15)+(Ze(s,a)|0)+((Ze((I>>>1)-(s<<15)<<16>>16,a)|0)>>15)<<1;s=(bi(a)|0)<<16>>16;a=a<<s;I=k<<1;qi(A|0,F|0,I|0)|0;qi(y|0,M|0,I|0)|0;k=k+1|0;if((k|0)>=11){a=20;break}else{g=s+(g&65535)&65535;l=a;S=(a>>1)-(a>>16<<15)|0;b=b+2|0;v=v+2|0;_=_+1<<16>>16}}if((a|0)==16){a=t+22|0;do{Ve[t>>1]=Ve[e>>1]|0;t=t+2|0;e=e+2|0}while((t|0)<(a|0));I=i;C=I;Ve[C>>1]=0;Ve[C+2>>1]=0>>>16;I=I+4|0;Ve[I>>1]=0;Ve[I+2>>1]=0>>>16;Ge=P;return 0}else if((a|0)==20){Ve[t>>1]=4096;I=((Ve[y>>1]|0)+8192+(Ve[A>>1]<<15)|0)>>>14&65535;Ve[t+2>>1]=I;Ve[e+2>>1]=I;I=((Ve[N+4>>1]|0)+8192+(Ve[D+4>>1]<<15)|0)>>>14&65535;Ve[t+4>>1]=I;Ve[e+4>>1]=I;I=((Ve[N+6>>1]|0)+8192+(Ve[D+6>>1]<<15)|0)>>>14&65535;Ve[t+6>>1]=I;Ve[e+6>>1]=I;I=((Ve[N+8>>1]|0)+8192+(Ve[D+8>>1]<<15)|0)>>>14&65535;Ve[t+8>>1]=I;Ve[e+8>>1]=I;I=((Ve[N+10>>1]|0)+8192+(Ve[D+10>>1]<<15)|0)>>>14&65535;Ve[t+10>>1]=I;Ve[e+10>>1]=I;I=((Ve[N+12>>1]|0)+8192+(Ve[D+12>>1]<<15)|0)>>>14&65535;Ve[t+12>>1]=I;Ve[e+12>>1]=I;I=((Ve[N+14>>1]|0)+8192+(Ve[D+14>>1]<<15)|0)>>>14&65535;Ve[t+14>>1]=I;Ve[e+14>>1]=I;I=((Ve[N+16>>1]|0)+8192+(Ve[D+16>>1]<<15)|0)>>>14&65535;Ve[t+16>>1]=I;Ve[e+16>>1]=I;I=((Ve[N+18>>1]|0)+8192+(Ve[D+18>>1]<<15)|0)>>>14&65535;Ve[t+18>>1]=I;Ve[e+18>>1]=I;I=((Ve[N+20>>1]|0)+8192+(Ve[D+20>>1]<<15)|0)>>>14&65535;Ve[t+20>>1]=I;Ve[e+20>>1]=I;Ge=P;return 0}return 0}function jn(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;t=e>>16;Ve[r>>1]=t;Ve[n>>1]=(e>>>1)-(t<<15);return}function qn(e){e=e|0;return((e|0)==-2147483648?2147483647:0-e|0)|0}function Kn(e){e=e|0;var r=0;if(!e){e=-1;return e|0}We[e>>2]=0;r=xi(4)|0;if(!r){e=-1;return e|0}We[r>>2]=0;if(!((xn(r)|0)<<16>>16)){Hn(We[r>>2]|0)|0;We[e>>2]=r;e=0;return e|0}else{zn(r);Hi(r);e=-1;return e|0}return 0}function Vn(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;zn(r);Hi(We[e>>2]|0);We[e>>2]=0;return}function Wn(e){e=e|0;if(!e){e=-1;return e|0}Hn(We[e>>2]|0)|0;e=0;return e|0}function Xn(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;var s=0,l=0,f=0,u=0;u=Ge;Ge=Ge+64|0;f=u+48|0;l=u+22|0;s=u;if((r|0)==7){n=We[o+116>>2]|0;xr(t,10,s,l,We[o+112>>2]|0,a)|0;Ln(10,s,l,a);Yn(We[e>>2]|0,s,l,i+22|0,f,a)|0;xr(t,10,s,l,n,a)|0;Ln(10,s,l,a);Yn(We[e>>2]|0,s,l,i+66|0,f,a)|0;Ge=u;return}else{xr(n,10,s,l,We[o+108>>2]|0,a)|0;Ln(10,s,l,a);Yn(We[e>>2]|0,s,l,i+66|0,f,a)|0;Ge=u;return}}function Gn(e,r,n,t,i,o,a,s,l,f){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;if((n|0)==6){Ve[i>>1]=ot(e,r,t,20,143,80,o,a,s,l,f)|0;return}Ve[a>>1]=0;Ve[a+2>>1]=0;if(n>>>0<2){Ve[i>>1]=et(r,n,t,20,143,160,s,l,f)|0;return}if(n>>>0<6){Ve[i>>1]=et(r,n,t,20,143,80,s,l,f)|0;return}else{Ve[i>>1]=et(r,n,t,18,143,80,s,l,f)|0;return}}function Zn(e){e=e|0;var r=0;if((e|0)!=0?(We[e>>2]=0,r=xi(2)|0,(r|0)!=0):0){Ve[r>>1]=0;We[e>>2]=r;r=0}else r=-1;return r|0}function Jn(e){e=e|0;if(!e)e=-1;else{Ve[e>>1]=0;e=0}return e|0}function Qn(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Hi(r);We[e>>2]=0;return}function $n(e,r,n,t,i,o,a,s,l,f,u,c){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;u=u|0;c=c|0;var d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0,C=0,I=0,B=0,L=0,U=0,x=0,H=0,z=0,Y=0,j=0;j=Ge;Ge=Ge+240|0;p=j+160|0;S=j+80|0;L=j;B=Ve[3558+(r*18|0)>>1]|0;Y=Ve[3558+(r*18|0)+2>>1]|0;d=Ve[3558+(r*18|0)+4>>1]|0;U=Ve[3558+(r*18|0)+6>>1]|0;m=Ve[3558+(r*18|0)+12>>1]|0;w=Ve[3558+(r*18|0)+14>>1]|0;h=Ve[3558+(r*18|0)+16>>1]|0;e:do{switch(s<<16>>16){case 0:case 80:if(r>>>0<2&s<<16>>16==80){x=(Xe[e>>1]|0)-(m&65535)|0;x=(x<<16>>16|0)<(h<<16>>16|0)?h:x&65535;I=w<<16>>16;H=(x&65535)+I&65535;z=H<<16>>16>143;x=z?143-I&65535:x;H=z?143:H;z=1;break e}else{x=(Xe[n+((s<<16>>16!=0&1)<<1)>>1]|0)-(Xe[3558+(r*18|0)+8>>1]|0)|0;x=(x<<16>>16|0)<(h<<16>>16|0)?h:x&65535;I=Ve[3558+(r*18|0)+10>>1]|0;H=(x&65535)+I&65535;z=H<<16>>16>143;x=z?143-I&65535:x;H=z?143:H;z=0;break e}default:{x=(Xe[e>>1]|0)-(m&65535)|0;x=(x<<16>>16|0)<(h<<16>>16|0)?h:x&65535;I=w<<16>>16;H=(x&65535)+I&65535;z=H<<16>>16>143;x=z?143-I&65535:x;H=z?143:H;z=1}}}while(0);C=x&65535;s=C+65532|0;E=s&65535;P=(H&65535)+4&65535;I=s<<16>>16;s=0-(s&65535)|0;m=s&65535;ln(t+(s<<16>>16<<1)|0,o,p,a);s=a<<16>>16;M=s>>>1&65535;b=M<<16>>16==0;if(b)a=1;else{a=M;h=p;n=S;w=0;while(1){N=Ve[h>>1]|0;Ve[n>>1]=N>>>2;N=(Ze(N,N)|0)+w|0;w=Ve[h+2>>1]|0;Ve[n+2>>1]=w>>>2;w=N+(Ze(w,w)|0)|0;a=a+-1<<16>>16;if(!(a<<16>>16))break;else{h=h+4|0;n=n+4|0}}a=(w|0)<33554433}N=a?0:2;F=a?p:S;v=a?p:S;e:do{if(E<<16>>16<=P<<16>>16){_=s+-1|0;O=F+(_<<1)|0;T=o+(_<<1)|0;D=F+(s+-2<<1)|0;R=_>>>1;A=R&65535;k=A<<16>>16==0;y=a?12:14;R=(R<<1)+131070&131070;n=s+-3-R|0;g=F+(n<<1)|0;R=F+(s+-4-R<<1)|0;o=o+(n<<1)|0;if(!b){b=I;while(1){S=M;p=v;h=i;w=0;a=0;while(1){S=S+-1<<16>>16;s=Ve[p>>1]|0;w=(Ze(s,Ve[h>>1]|0)|0)+w|0;s=(Ze(s,s)|0)+a|0;a=Ve[p+2>>1]|0;w=w+(Ze(a,Ve[h+2>>1]|0)|0)|0;a=s+(Ze(a,a)|0)|0;if(!(S<<16>>16))break;else{p=p+4|0;h=h+4|0}}p=ai(a<<1,c)|0;a=p>>16;h=w<<1>>16;S=Ze(a,h)|0;S=(S|0)==1073741824?2147483647:S<<1;h=(Ze((p>>>1)-(a<<15)<<16>>16,h)|0)>>15;p=(h<<1)+S|0;p=(h^S|0)>0&(p^S|0)<0?(S>>>31)+2147483647|0:p;a=(Ze(a,w&32767)|0)>>15;S=p+(a<<1)|0;Ve[L+(b-I<<1)>>1]=(p^a|0)>0&(S^p|0)<0?(p>>>31)+65535|0:S;if(E<<16>>16!=P<<16>>16){m=m+-1<<16>>16;S=Ve[t+(m<<16>>16<<1)>>1]|0;if(k){p=_;a=D;w=T;h=O}else{p=A;a=D;w=T;h=O;while(1){b=(Ze(Ve[w>>1]|0,S)|0)>>y;Ve[h>>1]=b+(Xe[a>>1]|0);b=(Ze(Ve[w+-2>>1]|0,S)|0)>>y;Ve[h+-2>>1]=b+(Xe[a+-2>>1]|0);p=p+-1<<16>>16;if(!(p<<16>>16)){p=n;a=R;w=o;h=g;break}else{a=a+-4|0;w=w+-4|0;h=h+-4|0}}}b=(Ze(Ve[w>>1]|0,S)|0)>>y;Ve[h>>1]=b+(Xe[a>>1]|0);Ve[F+(p+-1<<1)>>1]=S>>N}E=E+1<<16>>16;if(E<<16>>16>P<<16>>16)break e;else b=E<<16>>16}}if(k){a=F+(s+-2<<1)|0;w=I;while(1){ai(0,c)|0;Ve[L+(w-I<<1)>>1]=0;if(E<<16>>16!=P<<16>>16){m=m+-1<<16>>16;i=Ve[t+(m<<16>>16<<1)>>1]|0;A=(Ze(Ve[T>>1]|0,i)|0)>>y;Ve[O>>1]=A+(Xe[D>>1]|0);Ve[a>>1]=i>>N}E=E+1<<16>>16;if(E<<16>>16>P<<16>>16)break e;else w=E<<16>>16}}p=F+(n+-1<<1)|0;a=I;while(1){ai(0,c)|0;Ve[L+(a-I<<1)>>1]=0;if(E<<16>>16!=P<<16>>16){m=m+-1<<16>>16;a=Ve[t+(m<<16>>16<<1)>>1]|0;w=A;h=D;n=T;s=O;while(1){i=(Ze(Ve[n>>1]|0,a)|0)>>y;Ve[s>>1]=i+(Xe[h>>1]|0);i=(Ze(Ve[n+-2>>1]|0,a)|0)>>y;Ve[s+-2>>1]=i+(Xe[h+-2>>1]|0);w=w+-1<<16>>16;if(!(w<<16>>16))break;else{h=h+-4|0;n=n+-4|0;s=s+-4|0}}i=(Ze(Ve[o>>1]|0,a)|0)>>y;Ve[g>>1]=i+(Xe[R>>1]|0);Ve[p>>1]=a>>N}E=E+1<<16>>16;if(E<<16>>16>P<<16>>16)break;else a=E<<16>>16}}}while(0);E=x<<16>>16;n=C+1&65535;if(n<<16>>16>H<<16>>16)o=x;else{m=x;s=Ve[L+(E-I<<1)>>1]|0;while(1){w=Ve[L+((n<<16>>16)-I<<1)>>1]|0;h=w<<16>>16<s<<16>>16;m=h?m:n;n=n+1<<16>>16;if(n<<16>>16>H<<16>>16){o=m;break}else s=h?s:w}}e:do{if(!(z<<16>>16==0?o<<16>>16>B<<16>>16:0)){if(!(r>>>0<4&z<<16>>16!=0)){m=L+((o<<16>>16)-I<<1)|0;w=In(m,d,Y,c)|0;n=(d&65535)+1&65535;if(n<<16>>16<=U<<16>>16)while(1){h=In(m,n,Y,c)|0;s=h<<16>>16>w<<16>>16;d=s?n:d;n=n+1<<16>>16;if(n<<16>>16>U<<16>>16)break;else w=s?h:w}if((r+-7|0)>>>0<2){U=d<<16>>16==-3;n=(U<<31>>31)+o<<16>>16;d=U?3:d;break}switch(d<<16>>16){case-2:{n=o+-1<<16>>16;d=1;break e}case 2:{n=o+1<<16>>16;d=-1;break e}default:{n=o;break e}}}B=Ve[e>>1]|0;B=((B<<16>>16)-E|0)>5?E+5&65535:B;s=H<<16>>16;B=(s-(B<<16>>16)|0)>4?s+65532&65535:B;s=o<<16>>16;n=B<<16>>16;if((s|0)==(n+-1|0)?1:o<<16>>16==B<<16>>16){m=L+(s-I<<1)|0;s=In(m,d,Y,c)|0;n=(d&65535)+1&65535;if(n<<16>>16<=U<<16>>16)while(1){w=In(m,n,Y,c)|0;h=w<<16>>16>s<<16>>16;d=h?n:d;n=n+1<<16>>16;if(n<<16>>16>U<<16>>16)break;else s=h?w:s}if((r+-7|0)>>>0<2){U=d<<16>>16==-3;n=(U<<31>>31)+o<<16>>16;d=U?3:d;break}switch(d<<16>>16){case-2:{n=o+-1<<16>>16;d=1;break e}case 2:{n=o+1<<16>>16;d=-1;break e}default:{n=o;break e}}}if((s|0)==(n+-2|0)){n=L+(s-I<<1)|0;s=In(n,0,Y,c)|0;if((r|0)!=8){d=0;m=1;while(1){w=In(n,m,Y,c)|0;h=w<<16>>16>s<<16>>16;d=h?m:d;m=m+1<<16>>16;if(m<<16>>16>U<<16>>16)break;else s=h?w:s}if((r+-7|0)>>>0>=2)switch(d<<16>>16){case-2:{n=o+-1<<16>>16;d=1;break e}case 2:{n=o+1<<16>>16;d=-1;break e}default:{n=o;break e}}}else d=0;U=d<<16>>16==-3;n=(U<<31>>31)+o<<16>>16;d=U?3:d;break}if((s|0)==(n+1|0)){m=L+(s-I<<1)|0;n=In(m,d,Y,c)|0;s=(d&65535)+1&65535;if(s<<16>>16<=0)while(1){h=In(m,s,Y,c)|0;w=h<<16>>16>n<<16>>16;d=w?s:d;s=s+1<<16>>16;if(s<<16>>16>0)break;else n=w?h:n}if((r+-7|0)>>>0<2){U=d<<16>>16==-3;n=(U<<31>>31)+o<<16>>16;d=U?3:d;break}switch(d<<16>>16){case-2:{n=o+-1<<16>>16;d=1;break e}case 2:{n=o+1<<16>>16;d=-1;break e}default:{n=o;break e}}}else{n=o;d=0}}else{n=o;d=0}}while(0);if((r+-7|0)>>>0>1){U=e;e=bn(n,d,Ve[e>>1]|0,x,H,z,r>>>0<4&1,c)|0;Ve[u>>1]=e;Ve[U>>1]=n;Ve[f>>1]=Y;Ve[l>>1]=d;Ge=j;return n|0}else{c=vn(n,d,x,z,c)|0;Ve[u>>1]=c;Ve[e>>1]=n;Ve[f>>1]=Y;Ve[l>>1]=d;Ge=j;return n|0}return 0}function et(e,r,n,t,i,o,a,s,l){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;var f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0;R=Ge;Ge=Ge+1200|0;M=R+1188|0;F=R+580|0;g=R+578|0;k=R+576|0;S=R;v=R+582|0;_=(s|0)!=0;do{if(_)if(r>>>0<2){zt(e,1,l);break}else{zt(e,0,l);break}}while(0);b=i<<16>>16;c=0-b|0;u=n+(c<<1)|0;c=c&65535;m=o<<16>>16;do{if(c<<16>>16<o<<16>>16){w=c;h=u;c=0;while(1){E=Ve[h>>1]|0;c=(Ze(E<<1,E)|0)+c|0;if((c|0)<0)break;w=w+1<<16>>16;if(w<<16>>16>=o<<16>>16){p=14;break}else h=h+2|0}if((p|0)==14){if((c|0)<1048576){p=15;break}qi(v|0,u|0,m+b<<1|0)|0;E=0;break}f=m+b|0;d=f>>>1;w=d&65535;if(!(w<<16>>16))c=v;else{E=((d<<1)+131070&131070)+2|0;m=E-b|0;h=v;while(1){Ve[h>>1]=(Ve[u>>1]|0)>>>3;Ve[h+2>>1]=(Ve[u+2>>1]|0)>>>3;w=w+-1<<16>>16;if(!(w<<16>>16))break;else{u=u+4|0;h=h+4|0}}u=n+(m<<1)|0;c=v+(E<<1)|0}if(!(f&1))E=3;else{Ve[c>>1]=(Ve[u>>1]|0)>>>3;E=3}}else p=15}while(0);if((p|0)==15){E=m+b|0;c=E>>>1;d=c&65535;if(!(d<<16>>16))c=v;else{m=((c<<1)+131070&131070)+2|0;h=m-b|0;w=v;while(1){Ve[w>>1]=Ve[u>>1]<<3;Ve[w+2>>1]=Ve[u+2>>1]<<3;d=d+-1<<16>>16;if(!(d<<16>>16))break;else{u=u+4|0;w=w+4|0}}u=n+(h<<1)|0;c=v+(m<<1)|0}if(!(E&1))E=-3;else{Ve[c>>1]=Ve[u>>1]<<3;E=-3}}m=S+(b<<2)|0;h=v+(b<<1)|0;Xr(h,o,i,t,m);f=(r|0)==7&1;c=t<<16>>16;u=c<<2;if((u|0)!=(c<<18>>16|0)){We[l>>2]=1;u=t<<16>>16>0?32767:-32768}w=rt(e,m,h,E,f,o,i,u&65535,M,s,l)|0;c=c<<1;d=rt(e,m,h,E,f,o,u+65535&65535,c&65535,F,s,l)|0;c=rt(e,m,h,E,f,o,c+65535&65535,t,g,s,l)|0;if(a<<16>>16==1&_){Cn(m,h,o,i,t,k,l)|0;xt(e,Ve[k>>1]|0)}u=Ve[M>>1]|0;f=Ve[F>>1]|0;if(((u<<16>>16)*55706>>16|0)>=(f<<16>>16|0)){F=u;M=w;F=F<<16>>16;F=F*55706|0;F=F>>16;g=Ve[g>>1]|0;g=g<<16>>16;g=(F|0)<(g|0);g=g?c:M;Ge=R;return g|0}Ve[M>>1]=f;F=f;M=d;F=F<<16>>16;F=F*55706|0;F=F>>16;g=Ve[g>>1]|0;g=g<<16>>16;g=(F|0)<(g|0);g=g?c:M;Ge=R;return g|0}function rt(e,r,n,t,i,o,a,s,l,f,u){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;u=u|0;var c=0,d=0,h=0,w=0,m=0;if(a<<16>>16<s<<16>>16){s=-2147483648;h=a}else{h=a;c=-2147483648;d=r+(0-(a<<16>>16)<<2)|0;r=a;while(1){a=We[d>>2]|0;m=(a|0)<(c|0);r=m?r:h;c=m?c:a;h=h+-1<<16>>16;if(h<<16>>16<s<<16>>16){s=c;h=r;break}else d=d+4|0}}r=o<<16>>16>>>2&65535;if(!(r<<16>>16))r=0;else{c=r;a=n+(0-(h<<16>>16)<<1)|0;r=0;while(1){m=Ve[a>>1]|0;m=(Ze(m,m)|0)+r|0;r=Ve[a+2>>1]|0;r=m+(Ze(r,r)|0)|0;m=Ve[a+4>>1]|0;m=r+(Ze(m,m)|0)|0;r=Ve[a+6>>1]|0;r=m+(Ze(r,r)|0)|0;c=c+-1<<16>>16;if(!(c<<16>>16))break;else a=a+8|0}r=r<<1}if(f)Ht(e,s,r,u);r=ai(r,u)|0;a=i<<16>>16!=0;if(a)r=(r|0)>1073741823?2147483647:r<<1;i=s>>16;e=r>>16;u=Ze(e,i)|0;u=(u|0)==1073741824?2147483647:u<<1;r=(Ze((r>>>1)-(e<<15)<<16>>16,i)|0)>>15;m=(r<<1)+u|0;m=(r^u|0)>0&(m^u|0)<0?(u>>>31)+2147483647|0:m;i=(Ze(e,(s>>>1)-(i<<15)<<16>>16)|0)>>15;r=m+(i<<1)|0;r=(m^i|0)>0&(r^m|0)<0?(m>>>31)+2147483647|0:r;if(!a){Ve[l>>1]=r;return h|0}a=t<<16>>16;if(t<<16>>16>0)if(t<<16>>16<31){a=r>>a;w=16}else a=0;else{w=0-a<<16>>16;a=r<<w;a=(a>>w|0)==(r|0)?a:r>>31^2147483647;w=16}if((w|0)==16){if((a|0)>65535){Ve[l>>1]=32767;return h|0}if((a|0)<-65536){Ve[l>>1]=-32768;return h|0}}Ve[l>>1]=a>>>1;return h|0}function nt(e){e=e|0;var r=0;if(!e){e=-1;return e|0}We[e>>2]=0;r=xi(6)|0;if(!r){e=-1;return e|0}Ve[r>>1]=40;Ve[r+2>>1]=0;Ve[r+4>>1]=0;We[e>>2]=r;e=0;return e|0}function tt(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=40;Ve[e+2>>1]=0;Ve[e+4>>1]=0;e=0;return e|0}function it(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Hi(r);We[e>>2]=0;return}function ot(e,r,n,t,i,o,a,s,l,f,u){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;u=u|0;var c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0;y=Ge;Ge=Ge+1200|0;b=y+1186|0;v=y+1184|0;A=y+1182|0;S=y;k=y+576|0;_=i<<16>>16;R=k+(_<<1)|0;c=(0-_&65535)<<16>>16<o<<16>>16;if(c){m=0-i<<16>>16<<16>>16;d=0;do{w=Ve[n+(m<<1)>>1]|0;w=Ze(w,w)|0;if((w|0)!=1073741824){h=(w<<1)+d|0;if((w^d|0)>0&(h^d|0)<0){We[u>>2]=1;d=(d>>>31)+2147483647|0}else d=h}else{We[u>>2]=1;d=2147483647}m=m+1|0}while((m&65535)<<16>>16!=o<<16>>16)}else d=0;if((2147483646-d&d|0)>=0)if((d|0)==2147483647){if(c){d=0-i<<16>>16<<16>>16;do{Ve[k+(d+_<<1)>>1]=Pi(Ve[n+(d<<1)>>1]|0,3,u)|0;d=d+1|0}while((d&65535)<<16>>16!=o<<16>>16)}}else E=14;else{We[u>>2]=1;E=14}do{if((E|0)==14){if((1048575-d&d|0)<0){We[u>>2]=1;d=(d>>>31)+2147483647|0}else d=d+-1048576|0;if((d|0)>=0){if(!c)break;g=0-i<<16>>16<<16>>16;qi(k+(_+g<<1)|0,n+(g<<1)|0,(((o+i<<16>>16)+-1&65535)<<1)+2|0)|0;break}if(c){d=0-i<<16>>16<<16>>16;do{g=Ve[n+(d<<1)>>1]|0;Ve[k+(d+_<<1)>>1]=(g<<19>>19|0)==(g|0)?g<<3:g>>>15^32767;d=d+1|0}while((d&65535)<<16>>16!=o<<16>>16)}}}while(0);M=S+(_<<2)|0;Xr(R,o,i,t,M);m=Ve[e>>1]|0;g=e+4|0;F=s+(l<<16>>16<<1)|0;e:do{if(i<<16>>16<t<<16>>16)p=i;else{if((Ve[g>>1]|0)<=0){n=i;s=-2147483648;w=i;E=3402;while(1){jn(We[S+(_-(n<<16>>16)<<2)>>2]|0,b,v,u);h=Ve[v>>1]|0;d=Ve[E>>1]|0;m=Ze(d,Ve[b>>1]|0)|0;if((m|0)==1073741824){We[u>>2]=1;c=2147483647}else c=m<<1;p=(Ze(d,h<<16>>16)|0)>>15;m=c+(p<<1)|0;if((c^p|0)>0&(m^c|0)<0){We[u>>2]=1;m=(c>>>31)+2147483647|0}h=(m|0)<(s|0);w=h?w:n;n=n+-1<<16>>16;if(n<<16>>16<t<<16>>16){p=w;break e}else{s=h?s:m;E=E+-2|0}}}s=i;c=-2147483648;w=i;p=2902+(_+123-(m<<16>>16)<<1)|0;n=3402;while(1){jn(We[S+(_-(s<<16>>16)<<2)>>2]|0,b,v,u);E=Ve[v>>1]|0;h=Ve[n>>1]|0;m=Ze(h,Ve[b>>1]|0)|0;if((m|0)==1073741824){We[u>>2]=1;d=2147483647}else d=m<<1;E=(Ze(h,E<<16>>16)|0)>>15;m=d+(E<<1)|0;if((d^E|0)>0&(m^d|0)<0){We[u>>2]=1;m=(d>>>31)+2147483647|0}jn(m,b,v,u);E=Ve[v>>1]|0;h=Ve[p>>1]|0;m=Ze(h,Ve[b>>1]|0)|0;if((m|0)==1073741824){We[u>>2]=1;d=2147483647}else d=m<<1;E=(Ze(h,E<<16>>16)|0)>>15;m=d+(E<<1)|0;if((d^E|0)>0&(m^d|0)<0){We[u>>2]=1;m=(d>>>31)+2147483647|0}h=(m|0)<(c|0);w=h?w:s;s=s+-1<<16>>16;if(s<<16>>16<t<<16>>16){p=w;break}else{c=h?c:m;p=p+-2|0;n=n+-2|0}}}}while(0);if(o<<16>>16>0){s=0;n=R;E=k+(_-(p<<16>>16)<<1)|0;w=0;d=0;while(1){m=Ve[E>>1]|0;h=Ze(m,Ve[n>>1]|0)|0;if((h|0)!=1073741824){c=(h<<1)+w|0;if((h^w|0)>0&(c^w|0)<0){We[u>>2]=1;w=(w>>>31)+2147483647|0}else w=c}else{We[u>>2]=1;w=2147483647}c=Ze(m,m)|0;if((c|0)!=1073741824){h=(c<<1)+d|0;if((c^d|0)>0&(h^d|0)<0){We[u>>2]=1;d=(d>>>31)+2147483647|0}else d=h}else{We[u>>2]=1;d=2147483647}s=s+1<<16>>16;if(s<<16>>16>=o<<16>>16)break;else{n=n+2|0;E=E+2|0}}}else{w=0;d=0}h=(f|0)==0;if(!h){zt(r,0,u);Ht(r,w,d,u)}c=(Ni(d,u)|0)<<16>>16;if((c*13107|0)==1073741824){We[u>>2]=1;d=2147483647}else d=c*26214|0;c=w-d|0;if(((c^w)&(d^w)|0)<0){We[u>>2]=1;c=(w>>>31)+2147483647|0}f=Ni(c,u)|0;Ve[F>>1]=f;if(f<<16>>16>0){c=a+6|0;Ve[a+8>>1]=Ve[c>>1]|0;f=a+4|0;Ve[c>>1]=Ve[f>>1]|0;c=a+2|0;Ve[f>>1]=Ve[c>>1]|0;Ve[c>>1]=Ve[a>>1]|0;Ve[a>>1]=p;Ve[e>>1]=ri(a,5)|0;Ve[e+2>>1]=32767;c=32767}else{Ve[e>>1]=p;e=e+2|0;c=((Ve[e>>1]|0)*29491|0)>>>15&65535;Ve[e>>1]=c}Ve[g>>1]=((Bi(c,9830,u)|0)&65535)>>>15^1;if(h){Ge=y;return p|0}if((Bi(l,1,u)|0)<<16>>16){Ge=y;return p|0}Cn(M,R,o,i,t,A,u)|0;xt(r,Ve[A>>1]|0);Ge=y;return p|0}function at(e,r,n,t,i,o,a,s,l,f){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;var u=0,c=0;f=Ge;Ge=Ge+48|0;c=f+22|0;u=f;r=e>>>0<6?r:n;n=o<<16>>16>0?22:0;e=i+(n<<1)|0;Ui(e,r,c);Ui(e,t,u);e=o<<16>>16;o=l+(e<<1)|0;Di(c,a+(e<<1)|0,o,40);Li(u,o,o,40,s,1);n=i+(((n<<16)+720896|0)>>>16<<1)|0;Ui(n,r,c);Ui(n,t,u);e=(e<<16)+2621440>>16;l=l+(e<<1)|0;Di(c,a+(e<<1)|0,l,40);Li(u,l,l,40,s,1);Ge=f;return}function st(e){e=e|0;var r=0;if(!e){e=-1;return e|0}We[e>>2]=0;r=xi(12)|0;if(!r){e=-1;return e|0}Ve[r>>1]=0;Ve[r+2>>1]=0;Ve[r+4>>1]=0;Ve[r+6>>1]=0;Ve[r+8>>1]=0;Ve[r+10>>1]=0;We[e>>2]=r;e=0;return e|0}function lt(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=0;Ve[e+2>>1]=0;Ve[e+4>>1]=0;Ve[e+6>>1]=0;Ve[e+8>>1]=0;Ve[e+10>>1]=0;e=0;return e|0}function ft(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Hi(r);We[e>>2]=0;return}function ut(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0;c=e+10|0;i=Ve[c>>1]|0;d=e+8|0;t=Ve[d>>1]|0;if(!(n<<16>>16)){e=t;u=i;Ve[c>>1]=u;Ve[d>>1]=e;return}s=e+4|0;l=e+6|0;f=e+2|0;a=Ve[l>>1]|0;u=Ve[s>>1]|0;o=n;n=i;while(1){h=(Ze(Ve[e>>1]|0,-3733)|0)+(((u<<16>>16)*7807|0)+((a<<16>>16)*7807>>15))|0;Ve[e>>1]=u;h=h+((Ze(Ve[f>>1]|0,-3733)|0)>>15)|0;Ve[f>>1]=a;h=((n<<16>>16)*1899|0)+h+(Ze(t<<16>>16,-3798)|0)|0;n=Ve[r>>1]|0;h=h+((n<<16>>16)*1899|0)|0;Ve[r>>1]=(h+2048|0)>>>12;i=h>>>12;u=i&65535;Ve[s>>1]=u;a=(h<<3)-(i<<15)&65535;Ve[l>>1]=a;o=o+-1<<16>>16;if(!(o<<16>>16))break;else{h=t;r=r+2|0;t=n;n=h}}Ve[c>>1]=t;Ve[d>>1]=n;return}function ct(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0;i=Ve[(We[t+88>>2]|0)+(e<<1)>>1]|0;if(!(i<<16>>16))return;s=n;a=We[(We[t+92>>2]|0)+(e<<2)>>2]|0;while(1){n=Ve[a>>1]|0;if(!(n<<16>>16))n=0;else{e=Ve[r>>1]|0;o=n;t=s+((n<<16>>16)+-1<<1)|0;while(1){n=e<<16>>16;Ve[t>>1]=n&1;o=o+-1<<16>>16;if(!(o<<16>>16))break;else{e=n>>>1&65535;t=t+-2|0}}n=Ve[a>>1]|0}r=r+2|0;i=i+-1<<16>>16;if(!(i<<16>>16))break;else{s=s+(n<<16>>16<<1)|0;a=a+2|0}}return}function dt(e,r,n,t,i,o){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;var a=0,s=0,l=0,f=0,u=0;u=Ge;Ge=Ge+16|0;l=u+2|0;f=u;a=i<<16>>16;if(i<<16>>16<1){o=-5443;f=-32768;Qt(e,f,o);Ge=u;return}s=_i(14,n,o)|0;if((a|0)<(s<<16>>16|0))n=t;else{n=(t&65535)+1&65535;i=a>>>1&65535}t=Gt(i,s&65535)|0;Ve[f>>1]=t;si(t<<16>>16,l,f,o);Ve[l>>1]=((((n&65535)-(r&65535)<<16)+-65536|0)>>>16)+(Xe[l>>1]|0);t=Ci(Ve[f>>1]|0,5,o)|0;a=Ve[l>>1]|0;t=((a&65535)<<10)+(t&65535)&65535;if(t<<16>>16>18284){o=3037;f=18284;Qt(e,f,o);Ge=u;return}i=Ve[f>>1]|0;a=a<<16>>16;if((a*24660|0)==1073741824){We[o>>2]=1;n=2147483647}else n=a*49320|0;f=(i<<16>>16)*24660>>15;a=n+(f<<1)|0;if((n^f|0)>0&(a^n|0)<0){We[o>>2]=1;a=(n>>>31)+2147483647|0}f=a<<13;o=Ni((f>>13|0)==(a|0)?f:a>>31^2147483647,o)|0;f=t;Qt(e,f,o);Ge=u;return}function ht(e,r,n,t,i,o,a,s,l,f,u,c,d,h,w,m,E,p,S,b){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;u=u|0;c=c|0;d=d|0;h=h|0;w=w|0;m=m|0;E=E|0;p=p|0;S=S|0;b=b|0;var v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0,C=0,I=0,B=0,L=0,U=0,x=0,H=0,z=0,Y=0,j=0,q=0,K=0,V=0,W=0,X=0,G=0,Z=0,J=0,Q=0,$=0,ee=0,re=0,ne=0;ne=Ge;Ge=Ge+80|0;Q=ne+66|0;$=ne+64|0;ee=ne+62|0;re=ne+60|0;B=ne+40|0;L=ne+20|0;C=ne;Ve[Q>>1]=r;Ve[$>>1]=l;Ve[ee>>1]=f;P=_i(14,n,b)|0;J=P&65535;Ve[re>>1]=J;I=_i(14,f,b)|0;N=(Xe[t>>1]|0)+65523|0;Ve[C>>1]=N;R=(Xe[t+2>>1]|0)+65522|0;A=C+2|0;Ve[A>>1]=R;y=((r&65535)<<16)+-720896|0;F=y>>16;y=(y>>>15)+15+(Xe[t+4>>1]|0)|0;O=C+4|0;Ve[O>>1]=y;T=(Xe[t+6>>1]|0)+F|0;D=C+6|0;Ve[D>>1]=T;F=F+1+(Xe[t+8>>1]|0)|0;M=C+8|0;Ve[M>>1]=F;v=(Xe[u>>1]|0)+65523&65535;Ve[C+10>>1]=v;g=(Xe[u+2>>1]|0)+65522&65535;Ve[C+12>>1]=g;_=((l&65535)<<16)+-720896|0;t=_>>16;_=(_>>>15)+15+(Xe[u+4>>1]|0)&65535;Ve[C+14>>1]=_;k=(Xe[u+6>>1]|0)+t&65535;Ve[C+16>>1]=k;t=t+1+(Xe[u+8>>1]|0)&65535;Ve[C+18>>1]=t;G=(o&65535)-(d&65535)<<16;l=G>>16;if((G|0)>0){f=a;n=h<<16>>16>>l&65535}else{f=a<<16>>16>>0-l&65535;n=h}if((Ci(n,1,b)|0)<<16>>16>f<<16>>16)n=1;else n=(((f<<16>>16)+3>>2|0)>(n<<16>>16|0))<<31>>31;u=N+n&65535;Ve[C>>1]=u;G=R+n&65535;Ve[A>>1]=G;X=y+n&65535;Ve[O>>1]=X;W=T+n&65535;Ve[D>>1]=W;V=F+n&65535;Ve[M>>1]=V;l=t<<16>>16>u<<16>>16?t:u;l=k<<16>>16>l<<16>>16?k:l;l=_<<16>>16>l<<16>>16?_:l;l=g<<16>>16>l<<16>>16?g:l;l=v<<16>>16>l<<16>>16?v:l;l=V<<16>>16>l<<16>>16?V:l;l=W<<16>>16>l<<16>>16?W:l;l=X<<16>>16>l<<16>>16?X:l;l=(G<<16>>16>l<<16>>16?G:l)+1&65535;t=0;while(1){n=l-(u&65535)|0;u=n&65535;f=Xe[i>>1]<<16;n=n<<16>>16;if(u<<16>>16>0)u=u<<16>>16<31?f>>n:0;else{G=0-n<<16>>16;u=f<<G;u=(u>>G|0)==(f|0)?u:f>>31^2147483647}G=u>>16;Ve[B+(t<<1)>>1]=G;Ve[L+(t<<1)>>1]=(u>>>1)-(G<<15);t=t+1|0;if((t|0)==5){n=5;f=c;break}u=Ve[C+(t<<1)>>1]|0;i=i+2|0}while(1){t=l-(v&65535)|0;v=t&65535;u=Xe[f>>1]<<16;t=t<<16>>16;if(v<<16>>16>0)u=v<<16>>16<31?u>>t:0;else{X=0-t<<16>>16;G=u<<X;u=(G>>X|0)==(u|0)?G:u>>31^2147483647}G=u>>16;Ve[B+(n<<1)>>1]=G;Ve[L+(n<<1)>>1]=(u>>>1)-(G<<15);u=n+1|0;if((u&65535)<<16>>16==10)break;v=Ve[C+(u<<1)>>1]|0;n=u;f=f+2|0}U=P<<16>>16;x=Ve[B>>1]|0;H=Ve[L>>1]|0;z=Ve[B+2>>1]|0;Y=Ve[L+2>>1]|0;j=Ve[B+4>>1]|0;q=Ve[L+4>>1]|0;K=Ve[B+6>>1]|0;V=Ve[L+6>>1]|0;W=Ve[B+8>>1]|0;X=Ve[L+8>>1]|0;G=w&65535;d=I<<16>>16;o=Ve[B+10>>1]|0;k=Ve[L+10>>1]|0;_=Ve[B+12>>1]|0;i=Ve[L+12>>1]|0;n=Ve[B+14>>1]|0;f=Ve[L+14>>1]|0;t=Ve[B+16>>1]|0;v=Ve[L+16>>1]|0;F=Ve[B+18>>1]|0;L=Ve[L+18>>1]|0;l=2147483647;B=0;u=0;M=782;do{C=Ve[M>>1]|0;T=(Ze(U,Ve[M+2>>1]|0)|0)>>>15<<16;c=T>>16;y=C<<1;N=(Ze(y,C)|0)>>16;h=Ze(N,x)|0;if((h|0)==1073741824){We[b>>2]=1;D=2147483647}else D=h<<1;I=(Ze(H,N)|0)>>15;h=D+(I<<1)|0;if((D^I|0)>0&(h^D|0)<0){We[b>>2]=1;h=(D>>>31)+2147483647|0}N=Ze(z,C)|0;if((N|0)==1073741824){We[b>>2]=1;D=2147483647}else D=N<<1;I=(Ze(Y,C)|0)>>15;N=D+(I<<1)|0;if((D^I|0)>0&(N^D|0)<0){We[b>>2]=1;N=(D>>>31)+2147483647|0}T=(Ze(T>>15,c)|0)>>16;D=Ze(j,T)|0;if((D|0)==1073741824){We[b>>2]=1;O=2147483647}else O=D<<1;I=(Ze(q,T)|0)>>15;D=O+(I<<1)|0;if((O^I|0)>0&(D^O|0)<0){We[b>>2]=1;D=(O>>>31)+2147483647|0}T=Ze(K,c)|0;if((T|0)==1073741824){We[b>>2]=1;O=2147483647}else O=T<<1;I=(Ze(V,c)|0)>>15;T=O+(I<<1)|0;if((O^I|0)>0&(T^O|0)<0){We[b>>2]=1;I=(O>>>31)+2147483647|0}else I=T;O=(Ze(y,c)|0)>>16;T=Ze(W,O)|0;if((T|0)==1073741824){We[b>>2]=1;y=2147483647}else y=T<<1;P=(Ze(X,O)|0)>>15;T=y+(P<<1)|0;if((y^P|0)>0&(T^y|0)<0){We[b>>2]=1;T=(y>>>31)+2147483647|0}O=Ve[M+4>>1]|0;y=Ve[M+6>>1]|0;M=M+8|0;if((C-G&65535)<<16>>16<1?(Z=O<<16>>16,O<<16>>16<=w<<16>>16):0){R=(Ze(y<<16>>16,d)|0)>>>15<<16;C=R>>16;g=Z<<1;y=(Ze(g,Z)|0)>>16;O=Ze(o,y)|0;if((O|0)==1073741824){We[b>>2]=1;A=2147483647}else A=O<<1;P=(Ze(k,y)|0)>>15;O=A+(P<<1)|0;if((A^P|0)>0&(O^A|0)<0){We[b>>2]=1;O=(A>>>31)+2147483647|0}y=Ze(_,Z)|0;if((y|0)==1073741824){We[b>>2]=1;A=2147483647}else A=y<<1;P=(Ze(i,Z)|0)>>15;y=A+(P<<1)|0;if((A^P|0)>0&(y^A|0)<0){We[b>>2]=1;P=(A>>>31)+2147483647|0}else P=y;A=(Ze(R>>15,C)|0)>>16;y=Ze(n,A)|0;if((y|0)==1073741824){We[b>>2]=1;R=2147483647}else R=y<<1;c=(Ze(f,A)|0)>>15;y=R+(c<<1)|0;if((R^c|0)>0&(y^R|0)<0){We[b>>2]=1;c=(R>>>31)+2147483647|0}else c=y;y=Ze(t,C)|0;if((y|0)==1073741824){We[b>>2]=1;A=2147483647}else A=y<<1;R=(Ze(v,C)|0)>>15;y=A+(R<<1)|0;if((A^R|0)>0&(y^A|0)<0){We[b>>2]=1;a=(A>>>31)+2147483647|0}else a=y;A=(Ze(g,C)|0)>>16;y=Ze(F,A)|0;if((y|0)==1073741824){We[b>>2]=1;R=2147483647}else R=y<<1;C=(Ze(L,A)|0)>>15;y=R+(C<<1)|0;if((R^C|0)>0&(y^R|0)<0){We[b>>2]=1;y=(R>>>31)+2147483647|0}C=N+h+D+I+T+O+P+c+a+y|0;I=(C|0)<(l|0);l=I?C:l;u=I?B:u}B=B+1<<16>>16}while(B<<16>>16<256);w=(u&65535)<<18>>16;wt(e,782+(w<<1)|0,J,r,m,E,b);Jt(e,0,s,$,ee,Q,re,b);s=(_i(14,Ve[ee>>1]|0,b)|0)&65535;wt(e,782+((w|2)<<1)|0,s,Ve[$>>1]|0,p,S,b);Ge=ne;return u|0}function wt(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;var s=0,l=0,f=0,u=0;u=Ge;Ge=Ge+16|0;l=u+2|0;f=u;Ve[i>>1]=Ve[r>>1]|0;s=Ve[r+2>>1]|0;n=Ze(n<<16>>16<<1,s)|0;i=10-(t&65535)|0;r=i&65535;i=i<<16>>16;if(r<<16>>16>0)r=r<<16>>16<31?n>>i:0;else{i=0-i<<16>>16;r=n<<i;r=(r>>i|0)==(n|0)?r:n>>31^2147483647}Ve[o>>1]=r>>>16;si(s,l,f,a);Ve[l>>1]=(Xe[l>>1]|0)+65524;i=Ci(Ve[f>>1]|0,5,a)|0;t=Ve[l>>1]|0;i=((t&65535)<<10)+(i&65535)&65535;n=Ve[f>>1]|0;t=t<<16>>16;if((t*24660|0)==1073741824){We[a>>2]=1;r=2147483647}else r=t*49320|0;f=(n<<16>>16)*24660>>15;t=r+(f<<1)|0;if(!((r^f|0)>0&(t^r|0)<0)){a=t;a=a<<13;a=a+32768|0;a=a>>>16;a=a&65535;Qt(e,i,a);Ge=u;return}We[a>>2]=1;a=(r>>>31)+2147483647|0;a=a<<13;a=a+32768|0;a=a>>>16;a=a&65535;Qt(e,i,a);Ge=u;return}function mt(e,r,n,t,i,o,a,s,l,f,u,c,d,h,w,m,E,p,S,b,v){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;u=u|0;c=c|0;d=d|0;h=h|0;w=w|0;m=m|0;E=E|0;p=p|0;S=S|0;b=b|0;v=v|0;var _=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0,C=0,I=0,B=0,L=0,U=0,x=0,H=0,z=0,Y=0,j=0,q=0,K=0,V=0,W=0,X=0,G=0,Z=0,J=0,Q=0,$=0,ee=0,re=0,ne=0,te=0,ie=0,oe=0,ae=0;ae=Ge;Ge=Ge+80|0;te=ae+72|0;ie=ae+70|0;oe=ae+68|0;re=ae+66|0;ne=ae+56|0;W=ae+24|0;V=ae+12|0;q=ae+48|0;K=ae+40|0;x=ae+34|0;z=ae+22|0;L=ae+6|0;U=ae;pt(5,h,w,L,U,We[b+72>>2]|0,v)|0;F=_i(14,f,v)|0;H=b+68|0;B=We[H>>2]|0;j=l<<16>>16;Y=j+65526|0;h=(Xe[o>>1]|0)+65523&65535;Ve[ne>>1]=h;b=(Xe[o+2>>1]|0)+65522&65535;Ve[ne+2>>1]=b;Q=Y<<16>>16;$=((Y<<17>>17|0)==(Q|0)?Y<<1:Q>>>15^32767)+15+(Xe[o+4>>1]|0)&65535;Ve[ne+4>>1]=$;ee=(Xe[o+6>>1]|0)+Q&65535;Ve[ne+6>>1]=ee;o=Q+1+(Xe[o+8>>1]|0)&65535;Ve[ne+8>>1]=o;b=b<<16>>16>h<<16>>16?b:h;b=$<<16>>16>b<<16>>16?$:b;b=ee<<16>>16>b<<16>>16?ee:b;b=(Wt(o<<16>>16>b<<16>>16?o:b,1,v)|0)&65535;o=h;h=0;while(1){f=b-(o&65535)|0;o=f&65535;k=Xe[i+(h<<1)>>1]<<16;f=f<<16>>16;if(o<<16>>16>0)f=o<<16>>16<31?k>>f:0;else{ee=0-f<<16>>16;f=k<<ee;f=(f>>ee|0)==(k|0)?f:k>>31^2147483647}jn(f,W+(h<<1)|0,V+(h<<1)|0,v);f=h+1|0;if((f|0)==5)break;o=Ve[ne+(f<<1)>>1]|0;h=f}C=W+2|0;I=V+2|0;ee=F<<16>>16;X=W+4|0;G=V+4|0;Z=W+6|0;J=V+6|0;Q=W+8|0;$=V+8|0;R=0;o=2147483647;i=0;f=0;while(1){P=Ve[L+(i<<1)>>1]|0;F=Ze(P,P)|0;if(F>>>0>1073741823){We[v>>2]=1;F=32767}else F=F>>>15;b=Ve[V>>1]|0;k=F<<16>>16;F=Ze(k,Ve[W>>1]|0)|0;if((F|0)==1073741824){We[v>>2]=1;h=2147483647}else h=F<<1;N=(Ze(b<<16>>16,k)|0)>>15;F=h+(N<<1)|0;if((h^N|0)>0&(F^h|0)<0){We[v>>2]=1;F=(h>>>31)+2147483647|0}b=Ve[I>>1]|0;k=Ze(Ve[C>>1]|0,P)|0;if((k|0)!=1073741824){h=(k<<1)+F|0;if((k^F|0)>0&(h^F|0)<0){We[v>>2]=1;h=(F>>>31)+2147483647|0}}else{We[v>>2]=1;h=2147483647}F=(Ze(b<<16>>16,P)|0)>>15;if((F|0)>32767){We[v>>2]=1;F=32767}N=F<<16;F=(N>>15)+h|0;if((N>>16^h|0)>0&(F^h|0)<0){We[v>>2]=1;N=(h>>>31)+2147483647|0}else N=F;T=(N>>>31)+2147483647|0;D=i&65535;F=R;y=0;O=B;do{k=(Ze(Ve[O>>1]|0,ee)|0)>>15;O=O+6|0;if((k|0)>32767){We[v>>2]=1;k=32767}A=k<<16>>16;k=Ze(A,A)|0;if((k|0)==1073741824){We[v>>2]=1;g=2147483647}else g=k<<1;jn(g,te,ie,v);k=Ze(A,P)|0;if((k|0)==1073741824){We[v>>2]=1;g=2147483647}else g=k<<1;jn(g,oe,re,v);h=Ve[G>>1]|0;M=Ve[ie>>1]|0;k=Ve[X>>1]|0;b=Ve[te>>1]|0;R=Ze(b,k)|0;if((R|0)!=1073741824){g=(R<<1)+N|0;if((R^N|0)>0&(g^N|0)<0){We[v>>2]=1;g=T}}else{We[v>>2]=1;g=2147483647}R=(Ze(M<<16>>16,k)|0)>>15;if((R|0)>32767){We[v>>2]=1;R=32767}M=R<<16;R=(M>>15)+g|0;if((M>>16^g|0)>0&(R^g|0)<0){We[v>>2]=1;R=(g>>>31)+2147483647|0}g=(Ze(b,h<<16>>16)|0)>>15;if((g|0)>32767){We[v>>2]=1;g=32767}M=g<<16;g=(M>>15)+R|0;if((M>>16^R|0)>0&(g^R|0)<0){We[v>>2]=1;g=(R>>>31)+2147483647|0}k=Ve[J>>1]|0;R=Ze(Ve[Z>>1]|0,A)|0;if((R|0)!=1073741824){M=(R<<1)+g|0;if((R^g|0)>0&(M^g|0)<0){We[v>>2]=1;M=(g>>>31)+2147483647|0}}else{We[v>>2]=1;M=2147483647}k=(Ze(k<<16>>16,A)|0)>>15;if((k|0)>32767){We[v>>2]=1;k=32767}A=k<<16;k=(A>>15)+M|0;if((A>>16^M|0)>0&(k^M|0)<0){We[v>>2]=1;k=(M>>>31)+2147483647|0}b=Ve[$>>1]|0;M=Ve[re>>1]|0;h=Ve[Q>>1]|0;_=Ve[oe>>1]|0;R=Ze(_,h)|0;do{if((R|0)==1073741824){We[v>>2]=1;R=2147483647}else{g=(R<<1)+k|0;if(!((R^k|0)>0&(g^k|0)<0)){R=g;break}We[v>>2]=1;R=(k>>>31)+2147483647|0}}while(0);g=(Ze(M<<16>>16,h)|0)>>15;if((g|0)>32767){We[v>>2]=1;g=32767}A=g<<16;g=(A>>15)+R|0;if((A>>16^R|0)>0&(g^R|0)<0){We[v>>2]=1;g=(R>>>31)+2147483647|0}k=(Ze(_,b<<16>>16)|0)>>15;if((k|0)>32767){We[v>>2]=1;k=32767}A=k<<16;k=(A>>15)+g|0;if((A>>16^g|0)>0&(k^g|0)<0){We[v>>2]=1;k=(g>>>31)+2147483647|0}A=(k|0)<(o|0);F=A?y:F;f=A?D:f;o=A?k:o;y=y+1<<16>>16}while(y<<16>>16<32);i=i+1|0;if((i|0)==3){k=F;i=f;break}else R=F}I=(k<<16>>16)*3|0;o=Ve[B+(I<<1)>>1]|0;Ve[E>>1]=Ve[B+(I+1<<1)>>1]|0;Ve[p>>1]=Ve[B+(I+2<<1)>>1]|0;o=Ze(o<<16>>16,ee)|0;if((o|0)==1073741824){We[v>>2]=1;F=2147483647}else F=o<<1;I=9-j|0;B=I&65535;I=I<<16>>16;C=B<<16>>16>0;if(C)F=B<<16>>16<31?F>>I:0;else{N=0-I<<16>>16;P=F<<N;F=(P>>N|0)==(F|0)?P:F>>31^2147483647}Ve[m>>1]=F>>>16;P=i<<16>>16;L=Ve[L+(P<<1)>>1]|0;Ve[w>>1]=L;U=Ve[U+(P<<1)>>1]|0;Gr(r,n,t,L,u,q,K,x,v);An(e,Ve[x>>1]|0,Ve[m>>1]|0,z,v);if(!((Ve[q>>1]|0)!=0&(Ve[z>>1]|0)>0)){v=k;E=We[S>>2]|0;m=E+2|0;Ve[E>>1]=U;E=E+4|0;We[S>>2]=E;Ve[m>>1]=v;Ge=ae;return}A=q+6|0;Ve[A>>1]=s;g=K+6|0;Ve[g>>1]=a;l=((Bi(d,l,v)|0)&65535)+10|0;b=l<<16>>16;if((l&65535)<<16>>16<0){f=0-b<<16;if((f|0)<983040)c=c<<16>>16>>(f>>16)&65535;else c=0}else{f=c<<16>>16;h=f<<b;if((h<<16>>16>>b|0)==(f|0))c=h&65535;else c=(f>>>15^32767)&65535}o=Ve[w>>1]|0;F=Ve[z>>1]|0;H=We[H>>2]|0;h=Ve[m>>1]|0;z=10-j|0;b=z<<16>>16;if((z&65535)<<16>>16<0){f=0-b<<16;if((f|0)<983040)s=h<<16>>16>>(f>>16)&65535;else s=0}else{f=h<<16>>16;h=f<<b;if((h<<16>>16>>b|0)==(f|0))s=h&65535;else s=(f>>>15^32767)&65535}i=o<<16>>16;f=Ze(i,i)|0;if(f>>>0>1073741823){We[v>>2]=1;o=32767}else o=f>>>15;k=Wt(32767-(F&65535)&65535,1,v)|0;F=F<<16>>16;f=Ze(Ve[q+2>>1]|0,F)|0;if((f|0)==1073741824){We[v>>2]=1;f=2147483647}else f=f<<1;z=f<<1;f=Ze(((z>>1|0)==(f|0)?z:f>>31^2147418112)>>16,o<<16>>16)|0;if((f|0)==1073741824){We[v>>2]=1;R=2147483647}else R=f<<1;M=(Xe[K+2>>1]|0)+65521|0;b=M&65535;f=Ze(Ve[q+4>>1]|0,F)|0;if((f|0)==1073741824){We[v>>2]=1;o=2147483647}else o=f<<1;f=o<<1;f=(Ze(((f>>1|0)==(o|0)?f:o>>31^2147418112)>>16,i)|0)>>15;if((f|0)>32767){We[v>>2]=1;f=32767}Ve[X>>1]=f;o=Y&65535;Ve[te>>1]=o;o=Wt(Ve[K+4>>1]|0,o,v)|0;f=Ze(Ve[A>>1]|0,F)|0;if((f|0)==1073741824){We[v>>2]=1;f=2147483647}else f=f<<1;_=f<<1;Ve[Z>>1]=((_>>1|0)==(f|0)?_:f>>31^2147418112)>>>16;_=((j<<17>>17|0)==(j|0)?j<<1:j>>>15^32767)+65529&65535;Ve[te>>1]=_;_=Wt(Ve[g>>1]|0,_,v)|0;f=(Ze(Ve[A>>1]|0,k<<16>>16)|0)>>15;if((f|0)>32767){We[v>>2]=1;f=32767}Ve[Q>>1]=f;k=Wt(_,1,v)|0;h=Ze(Ve[q>>1]|0,F)|0;if((h|0)==1073741824){We[v>>2]=1;f=2147483647}else f=h<<1;g=Ii(f,te,v)|0;i=(Xe[te>>1]|0)+47|0;Ve[te>>1]=i;i=(Xe[K>>1]|0)-(i&65535)|0;F=i+31&65535;F=b<<16>>16>F<<16>>16?b:F;F=o<<16>>16>F<<16>>16?o:F;F=_<<16>>16>F<<16>>16?_:F;F=(k<<16>>16>F<<16>>16?k:F)<<16>>16;h=F-(M&65535)|0;f=h&65535;h=h<<16>>16;if(f<<16>>16>0)N=f<<16>>16<31?R>>h:0;else{K=0-h<<16>>16;N=R<<K;N=(N>>K|0)==(R|0)?N:R>>31^2147483647}b=F-(o&65535)|0;f=b&65535;h=Xe[X>>1]<<16;b=b<<16>>16;if(f<<16>>16>0)h=f<<16>>16<31?h>>b:0;else{q=0-b<<16>>16;K=h<<q;h=(K>>q|0)==(h|0)?K:h>>31^2147483647}jn(h,X,G,v);_=F-(_&65535)|0;h=_&65535;b=Xe[Z>>1]<<16;_=_<<16>>16;if(h<<16>>16>0)h=h<<16>>16<31?b>>_:0;else{K=0-_<<16>>16;h=b<<K;h=(h>>K|0)==(b|0)?h:b>>31^2147483647}jn(h,Z,J,v);_=F-(k&65535)|0;h=_&65535;b=Xe[Q>>1]<<16;_=_<<16>>16;if(h<<16>>16>0)h=h<<16>>16<31?b>>_:0;else{K=0-_<<16>>16;h=b<<K;h=(h>>K|0)==(b|0)?h:b>>31^2147483647}jn(h,Q,$,v);_=F+65505|0;Ve[te>>1]=_;_=_-(i&65535)|0;h=Pi(_&65535,1,v)|0;b=h<<16>>16;if(h<<16>>16>0)b=h<<16>>16<31?g>>b:0;else{K=0-b<<16>>16;b=g<<K;b=(b>>K|0)==(g|0)?b:g>>31^2147483647}do{if(!(_&1))R=b;else{jn(b,W,V,v);h=Ve[V>>1]|0;b=Ve[W>>1]|0;if((b*23170|0)==1073741824){We[v>>2]=1;_=2147483647}else _=b*46340|0;W=(h<<16>>16)*23170>>15;b=_+(W<<1)|0;if(!((_^W|0)>0&(b^_|0)<0)){R=b;break}We[v>>2]=1;R=(_>>>31)+2147483647|0}}while(0);A=(N>>>31)+2147483647|0;g=2147483647;M=0;b=0;y=H;while(1){h=(Ze(Ve[y>>1]|0,ee)|0)>>15;y=y+6|0;if((h|0)>32767){We[v>>2]=1;h=32767}_=h&65535;if(_<<16>>16>=s<<16>>16)break;o=h<<16>>16;h=Ze(o,o)|0;if((h|0)==1073741824){We[v>>2]=1;f=2147483647}else f=h<<1;jn(f,ie,oe,v);h=(Bi(_,c,v)|0)<<16>>16;h=Ze(h,h)|0;if((h|0)==1073741824){We[v>>2]=1;h=2147483647}else h=h<<1;jn(h,re,ne,v);_=Ve[G>>1]|0;f=Ze(Ve[X>>1]|0,o)|0;do{if((f|0)==1073741824){We[v>>2]=1;f=2147483647}else{h=(f<<1)+N|0;if(!((f^N|0)>0&(h^N|0)<0)){f=h;break}We[v>>2]=1;f=A}}while(0);h=(Ze(_<<16>>16,o)|0)>>15;if((h|0)>32767){We[v>>2]=1;h=32767}W=h<<16;h=(W>>15)+f|0;if((W>>16^f|0)>0&(h^f|0)<0){We[v>>2]=1;h=(f>>>31)+2147483647|0}i=Ve[J>>1]|0;k=Ve[oe>>1]|0;o=Ve[Z>>1]|0;F=Ve[ie>>1]|0;f=Ze(F,o)|0;do{if((f|0)==1073741824){We[v>>2]=1;_=2147483647}else{_=(f<<1)+h|0;if(!((f^h|0)>0&(_^h|0)<0))break;We[v>>2]=1;_=(h>>>31)+2147483647|0}}while(0);f=(Ze(k<<16>>16,o)|0)>>15;if((f|0)>32767){We[v>>2]=1;f=32767}W=f<<16;f=(W>>15)+_|0;if((W>>16^_|0)>0&(f^_|0)<0){We[v>>2]=1;f=(_>>>31)+2147483647|0}h=(Ze(F,i<<16>>16)|0)>>15;if((h|0)>32767){We[v>>2]=1;h=32767}W=h<<16;h=(W>>15)+f|0;if((W>>16^f|0)>0&(h^f|0)<0){We[v>>2]=1;h=(f>>>31)+2147483647|0}h=Ii(h,te,v)|0;_=Pi(Ve[te>>1]|0,1,v)|0;f=_<<16>>16;if(_<<16>>16>0)_=_<<16>>16<31?h>>f:0;else{W=0-f<<16>>16;_=h<<W;_=(_>>W|0)==(h|0)?_:h>>31^2147483647}h=_-R|0;if(((h^_)&(_^R)|0)<0){We[v>>2]=1;h=(_>>>31)+2147483647|0}h=(Ni(h,v)|0)<<16>>16;h=Ze(h,h)|0;if((h|0)==1073741824){We[v>>2]=1;_=2147483647}else _=h<<1;F=Ve[$>>1]|0;o=Ve[ne>>1]|0;k=Ve[Q>>1]|0;i=Ve[re>>1]|0;f=Ze(i,k)|0;do{if((f|0)==1073741824){We[v>>2]=1;h=2147483647}else{h=(f<<1)+_|0;if(!((f^_|0)>0&(h^_|0)<0))break;We[v>>2]=1;h=(_>>>31)+2147483647|0}}while(0);f=(Ze(o<<16>>16,k)|0)>>15;if((f|0)>32767){We[v>>2]=1;f=32767}W=f<<16;f=(W>>15)+h|0;if((W>>16^h|0)>0&(f^h|0)<0){We[v>>2]=1;f=(h>>>31)+2147483647|0}h=(Ze(i,F<<16>>16)|0)>>15;if((h|0)>32767){We[v>>2]=1;h=32767}W=h<<16;h=(W>>15)+f|0;if((W>>16^f|0)>0&(h^f|0)<0){We[v>>2]=1;h=(f>>>31)+2147483647|0}f=(h|0)<(g|0);b=f?M:b;M=M+1<<16>>16;if(M<<16>>16>=32)break;else g=f?h:g}oe=(b<<16>>16)*3|0;_=Ve[H+(oe<<1)>>1]|0;Ve[E>>1]=Ve[H+(oe+1<<1)>>1]|0;Ve[p>>1]=Ve[H+(oe+2<<1)>>1]|0;_=Ze(_<<16>>16,ee)|0;if((_|0)==1073741824){We[v>>2]=1;_=2147483647}else _=_<<1;if(C)_=B<<16>>16<31?_>>I:0;else{E=0-I<<16>>16;v=_<<E;_=(v>>E|0)==(_|0)?v:_>>31^2147483647}Ve[m>>1]=_>>>16;v=b;E=We[S>>2]|0;m=E+2|0;Ve[E>>1]=U;E=E+4|0;We[S>>2]=E;Ve[m>>1]=v;Ge=ae;return}function Et(e,r,n,t,i,o,a,s){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;var l=0,f=0,u=0,c=0,d=0;d=(e|0)==7;l=Ve[t>>1]|0;if(d){l=l<<16>>16>>>1&65535;c=_i(r,n,s)|0;r=c<<16;e=r>>16;if((c<<20>>20|0)==(e|0))e=r>>12;else e=e>>>15^32767}else{c=_i(r,n,s)|0;r=c<<16;e=r>>16;if((c<<21>>21|0)==(e|0))e=r>>11;else e=e>>>15^32767}c=e<<16>>16;s=l<<16>>16;r=s-((Ze(c,Ve[a>>1]|0)|0)>>>15&65535)|0;r=((r&32768|0)!=0?0-r|0:r)&65535;f=1;e=0;u=a;while(1){u=u+6|0;l=s-((Ze(Ve[u>>1]|0,c)|0)>>>15&65535)|0;n=l<<16;l=(n|0)<0?0-(n>>16)|0:l;n=(l<<16>>16|0)<(r<<16>>16|0);e=n?f:e;f=f+1<<16>>16;if(f<<16>>16>=32)break;else r=n?l&65535:r}u=(e<<16>>16)*196608>>16;Ve[t>>1]=(Ze(Ve[a+(u<<1)>>1]|0,c)|0)>>>15<<(d&1);Ve[i>>1]=Ve[a+(u+1<<1)>>1]|0;Ve[o>>1]=Ve[a+(u+2<<1)>>1]|0;return e|0}function pt(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;var s=0,l=0,f=0,u=0,c=0,d=0;s=Bi(Ve[n>>1]|0,Ve[o>>1]|0,a)|0;s=(s&65535)-((s&65535)>>>15&65535)|0;s=(s<<16>>31^s)&65535;f=0;u=1;while(1){l=Ve[o+(u<<1)>>1]|0;if(l<<16>>16>r<<16>>16)l=s;else{l=Bi(Ve[n>>1]|0,l,a)|0;l=(l&65535)-((l&65535)>>>15&65535)|0;l=(l<<16>>31^l)&65535;d=l<<16>>16<s<<16>>16;l=d?l:s;f=d?u&65535:f}u=u+1|0;if((u|0)==16)break;else s=l}if((e|0)!=5){s=Ve[o+(f<<16>>16<<1)>>1]|0;if((e|0)==7){Ve[n>>1]=s&65532;return f|0}else{Ve[n>>1]=s;return f|0}}l=f<<16>>16;switch(f<<16>>16){case 0:{s=0;break}case 15:{c=8;break}default:if((Ve[o+(l+1<<1)>>1]|0)>r<<16>>16)c=8;else s=l+65535&65535}if((c|0)==8)s=l+65534&65535;Ve[i>>1]=s;d=s<<16>>16;Ve[t>>1]=Ve[o+(d<<1)>>1]|0;d=d+1|0;Ve[i+2>>1]=d;d=d<<16>>16;Ve[t+2>>1]=Ve[o+(d<<1)>>1]|0;d=d+1|0;Ve[i+4>>1]=d;Ve[t+4>>1]=Ve[o+(d<<16>>16<<1)>>1]|0;Ve[n>>1]=Ve[o+(l<<1)>>1]|0;return f|0}function St(e,r,n,t,i,o,a,s,l,f,u,c){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;u=u|0;c=c|0;var d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0;N=Ge;Ge=Ge+32|0;w=N+20|0;m=N+10|0;h=N;switch(e|0){case 3:case 4:case 6:{u=u+84|0;D=128;break}default:{u=u+80|0;D=64}}T=We[u>>2]|0;d=_i(14,n,c)|0;O=r<<16>>16;y=O+65525|0;e=(Xe[i>>1]|0)+65523&65535;Ve[h>>1]=e;r=(Xe[i+2>>1]|0)+65522&65535;Ve[h+2>>1]=r;A=y<<16>>16;A=Wt(Ve[i+4>>1]|0,((y<<17>>17|0)==(A|0)?y<<1:A>>>15^32767)+15&65535,c)|0;Ve[h+4>>1]=A;y=Wt(Ve[i+6>>1]|0,y&65535,c)|0;Ve[h+6>>1]=y;i=Wt(Ve[i+8>>1]|0,O+65526&65535,c)|0;Ve[h+8>>1]=i;r=r<<16>>16>e<<16>>16?r:e;r=A<<16>>16>r<<16>>16?A:r;r=y<<16>>16>r<<16>>16?y:r;r=(i<<16>>16>r<<16>>16?i:r)+1&65535;i=0;while(1){n=r-(e&65535)|0;u=n&65535;e=Xe[t+(i<<1)>>1]<<16;n=n<<16>>16;if(u<<16>>16>0)u=u<<16>>16<31?e>>n:0;else{y=0-n<<16>>16;u=e<<y;u=(u>>y|0)==(e|0)?u:e>>31^2147483647}jn(u,w+(i<<1)|0,m+(i<<1)|0,c);u=i+1|0;if((u|0)==5)break;e=Ve[h+(u<<1)>>1]|0;i=u}y=d<<16>>16;v=Ve[w>>1]|0;_=Ve[m>>1]|0;k=Ve[w+2>>1]|0;F=Ve[m+2>>1]|0;M=Ve[w+4>>1]|0;g=Ve[m+4>>1]|0;R=Ve[w+6>>1]|0;A=Ve[m+6>>1]|0;b=Ve[w+8>>1]|0;E=Ve[m+8>>1]|0;r=2147483647;p=0;u=0;S=T;while(1){i=Ve[S>>1]|0;if(i<<16>>16>o<<16>>16)d=r;else{d=(Ze(Ve[S+2>>1]|0,y)|0)>>15;if((d|0)>32767){We[c>>2]=1;d=32767}m=i<<16>>16;i=Ze(m,m)|0;if(i>>>0>1073741823){We[c>>2]=1;h=32767}else h=i>>>15;n=d<<16>>16;d=Ze(n,n)|0;if(d>>>0>1073741823){We[c>>2]=1;w=32767}else w=d>>>15;t=(Ze(n,m)|0)>>15;if((t|0)>32767){We[c>>2]=1;t=32767}d=h<<16>>16;h=Ze(v,d)|0;if((h|0)==1073741824){We[c>>2]=1;i=2147483647}else i=h<<1;d=(Ze(_,d)|0)>>15;h=i+(d<<1)|0;if((i^d|0)>0&(h^i|0)<0){We[c>>2]=1;h=(i>>>31)+2147483647|0}d=Ze(k,m)|0;if((d|0)==1073741824){We[c>>2]=1;i=2147483647}else i=d<<1;m=(Ze(F,m)|0)>>15;d=i+(m<<1)|0;if((i^m|0)>0&(d^i|0)<0){We[c>>2]=1;d=(i>>>31)+2147483647|0}i=d+h|0;if((d^h|0)>-1&(i^h|0)<0){We[c>>2]=1;i=(h>>>31)+2147483647|0}d=w<<16>>16;h=Ze(M,d)|0;if((h|0)==1073741824){We[c>>2]=1;e=2147483647}else e=h<<1;m=(Ze(g,d)|0)>>15;h=e+(m<<1)|0;if((e^m|0)>0&(h^e|0)<0){We[c>>2]=1;h=(e>>>31)+2147483647|0}d=h+i|0;if((h^i|0)>-1&(d^i|0)<0){We[c>>2]=1;e=(i>>>31)+2147483647|0}else e=d;d=Ze(R,n)|0;if((d|0)==1073741824){We[c>>2]=1;h=2147483647}else h=d<<1;m=(Ze(A,n)|0)>>15;d=h+(m<<1)|0;if((h^m|0)>0&(d^h|0)<0){We[c>>2]=1;d=(h>>>31)+2147483647|0}i=d+e|0;if((d^e|0)>-1&(i^e|0)<0){We[c>>2]=1;h=(e>>>31)+2147483647|0}else h=i;i=t<<16>>16;d=Ze(b,i)|0;if((d|0)==1073741824){We[c>>2]=1;e=2147483647}else e=d<<1;m=(Ze(E,i)|0)>>15;d=e+(m<<1)|0;if((e^m|0)>0&(d^e|0)<0){We[c>>2]=1;i=(e>>>31)+2147483647|0}else i=d;d=i+h|0;if((i^h|0)>-1&(d^h|0)<0){We[c>>2]=1;d=(h>>>31)+2147483647|0}m=(d|0)<(r|0);d=m?d:r;u=m?p:u}S=S+8|0;p=p+1<<16>>16;if((p<<16>>16|0)>=(D|0))break;else r=d}o=u<<16>>16;o=((o<<18>>18|0)==(o|0)?o<<2:o>>>15^32767)<<16>>16;Ve[a>>1]=Ve[T+(o<<1)>>1]|0;r=Ve[T+(o+1<<1)>>1]|0;Ve[l>>1]=Ve[T+(o+2<<1)>>1]|0;Ve[f>>1]=Ve[T+(o+3<<1)>>1]|0;r=Ze(r<<16>>16,y)|0;if((r|0)==1073741824){We[c>>2]=1;e=2147483647}else e=r<<1;n=10-O|0;r=n&65535;n=n<<16>>16;if(r<<16>>16>0){c=r<<16>>16<31?e>>n:0;c=c>>>16;c=c&65535;Ve[s>>1]=c;Ge=N;return u|0}else{l=0-n<<16>>16;c=e<<l;c=(c>>l|0)==(e|0)?c:e>>31^2147483647;c=c>>>16;c=c&65535;Ve[s>>1]=c;Ge=N;return u|0}return 0}function bt(e,r,n,t,i,o,a,s,l){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;var f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0,C=0,I=0,B=0,L=0,U=0,x=0,H=0,z=0,Y=0,j=0,q=0,K=0,V=0,W=0,X=0,G=0,Z=0,J=0,Q=0,$=0,ee=0,re=0,ne=0,te=0,ie=0,oe=0,ae=0,se=0,le=0,fe=0,ue=0,ce=0,de=0,he=0,we=0,me=0,Ee=0,pe=0,Se=0;Se=Ge;Ge=Ge+160|0;pe=Se;u=e<<16>>16;me=e<<16>>16==10;Ee=Ve[a+(Ve[o>>1]<<1)>>1]|0;if(e<<16>>16>0){l=0;f=s;while(1){Ve[f>>1]=l;l=l+1<<16>>16;if(l<<16>>16>=e<<16>>16)break;else f=f+2|0}}if(n<<16>>16<=1){Ge=Se;return}he=o+2|0;we=Ee<<16>>16;ue=t+(we<<1)|0;ce=i+(we*80|0)+(we<<1)|0;de=o+6|0;V=r&65535;W=o+4|0;X=o+10|0;G=o+8|0;Z=o+14|0;J=o+12|0;Q=o+18|0;$=o+16|0;ee=s+2|0;re=s+4|0;ne=s+6|0;te=s+8|0;ie=s+10|0;oe=s+12|0;ae=s+14|0;se=s+16|0;le=s+18|0;fe=e<<16>>16>2;q=o+(u+-1<<1)|0;Y=1;K=1;B=0;L=0;j=-1;while(1){z=Ve[a+(Ve[he>>1]<<1)>>1]|0;H=z<<16>>16;r=(Xe[t+(H<<1)>>1]|0)+(Xe[ue>>1]|0)|0;f=(Ve[i+(we*80|0)+(H<<1)>>1]<<13)+32768+((Ve[i+(H*80|0)+(H<<1)>>1]|0)+(Ve[ce>>1]|0)<<12)|0;u=Ve[de>>1]|0;if(u<<16>>16<40){u=u<<16>>16;c=pe;while(1){U=(Ve[i+(u*80|0)+(u<<1)>>1]|0)>>>1;I=Ve[i+(u*80|0)+(we<<1)>>1]|0;x=Ve[i+(u*80|0)+(H<<1)>>1]|0;Ve[c>>1]=r+(Xe[t+(u<<1)>>1]|0);Ve[c+2>>1]=(I+2+U+x|0)>>>2;u=u+V|0;if((u&65535)<<16>>16<40){u=u<<16>>16;c=c+4|0}else break}M=Ve[de>>1]|0}else M=u;r=Ve[W>>1]|0;F=f>>12;u=r<<16>>16;e:do{if(r<<16>>16<40){k=M<<16>>16;if(M<<16>>16<40){c=1;h=r;m=M;w=0;d=-1}else while(1){u=u+V|0;if((u&65535)<<16>>16<40)u=u<<16>>16;else{c=1;x=r;U=M;u=0;break e}}while(1){_=((Ve[i+(u*80|0)+(u<<1)>>1]|0)+F>>1)+(Ve[i+(u*80|0)+(we<<1)>>1]|0)+(Ve[i+(u*80|0)+(H<<1)>>1]|0)|0;v=Xe[t+(u<<1)>>1]|0;S=k;b=M;p=pe;E=w;while(1){f=(Xe[p>>1]|0)+v|0;l=f<<16>>16;l=(Ze(l,l)|0)>>>15;w=(_+(Ve[i+(u*80|0)+(S<<1)>>1]|0)>>2)+(Ve[p+2>>1]|0)>>1;if((Ze(l<<16>>16,c<<16>>16)|0)>(Ze(w,d<<16>>16)|0)){c=w&65535;h=r;m=b;w=f&65535;d=l&65535}else w=E;f=S+V|0;b=f&65535;if(b<<16>>16>=40)break;else{S=f<<16>>16;p=p+4|0;E=w}}u=u+V|0;r=u&65535;if(r<<16>>16<40)u=u<<16>>16;else{x=h;U=m;u=w;break}}}else{c=1;x=r;U=M;u=0}}while(0);h=c<<16>>16<<15;c=Ve[X>>1]|0;if(c<<16>>16<40){f=x<<16>>16;l=U<<16>>16;r=u&65535;c=c<<16>>16;u=pe;while(1){N=Ve[i+(c*80|0)+(c<<1)>>1]>>1;D=Ve[i+(c*80|0)+(we<<1)>>1]|0;P=Ve[i+(c*80|0)+(H<<1)>>1]|0;C=Ve[i+(c*80|0)+(f<<1)>>1]|0;I=Ve[i+(c*80|0)+(l<<1)>>1]|0;Ve[u>>1]=(Xe[t+(c<<1)>>1]|0)+r;Ve[u+2>>1]=(D+2+N+P+C+I|0)>>>2;c=c+V|0;if((c&65535)<<16>>16<40){c=c<<16>>16;u=u+4|0}else break}N=Ve[X>>1]|0}else N=c;d=Ve[G>>1]|0;c=d<<16>>16;e:do{if(d<<16>>16<40){g=x<<16>>16;R=U<<16>>16;A=N<<16>>16;M=h+32768|0;if(N<<16>>16<40){w=1;h=d;r=N;m=d;u=0;d=-1}else while(1){c=c+V|0;if((c&65535)<<16>>16<40)c=c<<16>>16;else{c=1;I=d;C=N;u=0;break e}}while(1){l=Xe[t+(c<<1)>>1]|0;F=(Ve[i+(c*80|0)+(H<<1)>>1]|0)+(Ve[i+(c*80|0)+(we<<1)>>1]|0)+(Ve[i+(c*80|0)+(g<<1)>>1]|0)+(Ve[i+(c*80|0)+(R<<1)>>1]|0)|0;k=M+(Ve[i+(c*80|0)+(c<<1)>>1]<<11)|0;v=A;S=N;_=pe;while(1){E=(Xe[_>>1]|0)+l|0;f=k+(Ve[_+2>>1]<<14)+(F+(Ve[i+(c*80|0)+(v<<1)>>1]|0)<<12)|0;p=E<<16>>16;p=(Ze(p,p)|0)>>>15;if((Ze(p<<16>>16,w<<16>>16)|0)>(Ze(f>>16,d<<16>>16)|0)){w=f>>>16&65535;b=m;r=S;u=E&65535;d=p&65535}else b=h;h=v+V|0;S=h&65535;if(S<<16>>16>=40){h=b;break}else{v=h<<16>>16;h=b;_=_+4|0}}c=c+V|0;m=c&65535;if(m<<16>>16<40)c=c<<16>>16;else{c=w;I=h;C=r;break}}}else{c=1;I=d;C=N;u=0}}while(0);w=c<<16>>16<<15;c=Ve[Z>>1]|0;if(c<<16>>16<40){f=x<<16>>16;l=U<<16>>16;d=I<<16>>16;h=C<<16>>16;r=u&65535;c=c<<16>>16;u=pe;while(1){y=Ve[i+(c*80|0)+(c<<1)>>1]>>1;A=Ve[i+(we*80|0)+(c<<1)>>1]|0;O=Ve[i+(H*80|0)+(c<<1)>>1]|0;T=Ve[i+(f*80|0)+(c<<1)>>1]|0;D=Ve[i+(l*80|0)+(c<<1)>>1]|0;N=Ve[i+(d*80|0)+(c<<1)>>1]|0;P=Ve[i+(h*80|0)+(c<<1)>>1]|0;Ve[u>>1]=(Xe[t+(c<<1)>>1]|0)+r;Ve[u+2>>1]=(A+4+y+O+T+D+N+P|0)>>>3;c=c+V|0;if((c&65535)<<16>>16<40){c=c<<16>>16;u=u+4|0}else break}r=Ve[Z>>1]|0}else r=c;m=Ve[J>>1]|0;if(m<<16>>16<40){N=x<<16>>16;y=U<<16>>16;A=I<<16>>16;R=C<<16>>16;g=r<<16>>16;M=r<<16>>16<40;O=w+32768|0;D=m<<16>>16;l=1;b=m;S=r;T=m;h=0;c=-1;while(1){if(M){w=Xe[t+(D<<1)>>1]|0;u=(Ve[i+(D*80|0)+(H<<1)>>1]|0)+(Ve[i+(D*80|0)+(we<<1)>>1]|0)+(Ve[i+(D*80|0)+(N<<1)>>1]|0)+(Ve[i+(D*80|0)+(y<<1)>>1]|0)+(Ve[i+(D*80|0)+(A<<1)>>1]|0)+(Ve[i+(D*80|0)+(R<<1)>>1]|0)|0;d=O+(Ve[i+(D*80|0)+(D<<1)>>1]<<10)|0;p=g;m=r;k=S;F=pe;while(1){_=(Xe[F>>1]|0)+w|0;S=d+(Ve[F+2>>1]<<14)+(u+(Ve[i+(D*80|0)+(p<<1)>>1]|0)<<11)|0;v=_<<16>>16;v=(Ze(v,v)|0)>>>15;if((Ze(v<<16>>16,l<<16>>16)|0)>(Ze(S>>16,c<<16>>16)|0)){l=S>>>16&65535;b=T;S=m;h=_&65535;c=v&65535}else S=k;E=p+V|0;m=E&65535;if(m<<16>>16>=40)break;else{p=E<<16>>16;k=S;F=F+4|0}}}m=D+V|0;T=m&65535;if(T<<16>>16>=40){P=S;break}else D=m<<16>>16}}else{l=1;b=m;P=r;h=0;c=-1}if(me){p=l<<16>>16<<15;c=Ve[Q>>1]|0;if(c<<16>>16<40){u=x<<16>>16;r=U<<16>>16;f=I<<16>>16;l=C<<16>>16;w=b<<16>>16;m=P<<16>>16;d=h&65535;c=c<<16>>16;h=pe;while(1){A=Ve[i+(c*80|0)+(c<<1)>>1]>>1;R=Ve[i+(we*80|0)+(c<<1)>>1]|0;y=Ve[i+(H*80|0)+(c<<1)>>1]|0;O=Ve[i+(u*80|0)+(c<<1)>>1]|0;T=Ve[i+(r*80|0)+(c<<1)>>1]|0;D=Ve[i+(f*80|0)+(c<<1)>>1]|0;N=Ve[i+(l*80|0)+(c<<1)>>1]|0;B=Ve[i+(w*80|0)+(c<<1)>>1]|0;L=Ve[i+(m*80|0)+(c<<1)>>1]|0;Ve[h>>1]=(Xe[t+(c<<1)>>1]|0)+d;Ve[h+2>>1]=(R+4+A+y+O+T+D+N+B+L|0)>>>3;c=c+V|0;if((c&65535)<<16>>16<40){c=c<<16>>16;h=h+4|0}else break}N=Ve[Q>>1]|0}else N=c;w=Ve[$>>1]|0;if(w<<16>>16<40){A=x<<16>>16;R=U<<16>>16;g=I<<16>>16;f=C<<16>>16;y=b<<16>>16;O=P<<16>>16;T=N<<16>>16;D=N<<16>>16<40;M=p+32768|0;u=w<<16>>16;l=1;m=w;h=N;r=w;c=-1;while(1){if(D){p=Xe[t+(u<<1)>>1]|0;d=(Ve[i+(H*80|0)+(u<<1)>>1]|0)+(Ve[i+(we*80|0)+(u<<1)>>1]|0)+(Ve[i+(A*80|0)+(u<<1)>>1]|0)+(Ve[i+(R*80|0)+(u<<1)>>1]|0)+(Ve[i+(g*80|0)+(u<<1)>>1]|0)+(Ve[i+(f*80|0)+(u<<1)>>1]|0)+(Ve[i+(y*80|0)+(u<<1)>>1]|0)+(Ve[i+(O*80|0)+(u<<1)>>1]|0)|0;w=M+(Ve[i+(u*80|0)+(u<<1)>>1]<<9)|0;F=T;v=N;k=pe;while(1){_=(Xe[k>>1]|0)+p<<16>>16;_=(Ze(_,_)|0)>>>15;S=w+(Ve[k+2>>1]<<13)+(d+(Ve[i+(u*80|0)+(F<<1)>>1]|0)<<10)|0;if((Ze(_<<16>>16,l<<16>>16)|0)>(Ze(S>>16,c<<16>>16)|0)){l=S>>>16&65535;m=r;h=v;c=_&65535}E=F+V|0;v=E&65535;if(v<<16>>16>=40)break;else{F=E<<16>>16;k=k+4|0}}}w=u+V|0;r=w&65535;if(r<<16>>16>=40)break;else u=w<<16>>16}}else{l=1;m=w;h=N;c=-1}}else{m=B;h=L}if((Ze(c<<16>>16,Y<<16>>16)|0)>(Ze(l<<16>>16,j<<16>>16)|0)){Ve[s>>1]=Ee;Ve[ee>>1]=z;Ve[re>>1]=x;Ve[ne>>1]=U;Ve[te>>1]=I;Ve[ie>>1]=C;Ve[oe>>1]=b;Ve[ae>>1]=P;if(me){Ve[se>>1]=m;Ve[le>>1]=h}}else{l=Y;c=j}u=Ve[he>>1]|0;if(fe){r=1;f=2;while(1){Ve[o+(r<<1)>>1]=Ve[o+(f<<1)>>1]|0;f=f+1|0;if((f&65535)<<16>>16==e<<16>>16)break;else r=r+1|0}}Ve[q>>1]=u;K=K+1<<16>>16;if(K<<16>>16>=n<<16>>16)break;else{Y=l;B=m;L=h;j=c}}Ge=Se;return}function vt(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0;s=39;while(1){a=e+(s<<1)|0;o=Ve[a>>1]|0;i=r+(s<<1)|0;if(o<<16>>16>-1)Ve[i>>1]=32767;else{Ve[i>>1]=-32767;if(o<<16>>16==-32768)o=32767;else o=0-(o&65535)&65535;Ve[a>>1]=o}Ve[n+(s<<1)>>1]=o;if((s|0)>0)s=s+-1|0;else break}f=8-(t<<16>>16)|0;if((f|0)>0){l=0;i=0}else return;do{t=0;e=0;a=32767;while(1){r=Ve[n+(t<<1)>>1]|0;s=r<<16>>16>-1?r<<16>>16<a<<16>>16:0;i=s?e:i;o=t+5|0;e=o&65535;if(e<<16>>16>=40)break;else{t=o<<16>>16;a=s?r:a}}Ve[n+(i<<16>>16<<1)>>1]=-1;l=l+1<<16>>16}while((l<<16>>16|0)<(f|0));l=0;do{r=1;e=1;o=32767;while(1){t=Ve[n+(r<<1)>>1]|0;s=t<<16>>16>-1?t<<16>>16<o<<16>>16:0;i=s?e:i;a=r+5|0;e=a&65535;if(e<<16>>16>=40)break;else{r=a<<16>>16;o=s?t:o}}Ve[n+(i<<16>>16<<1)>>1]=-1;l=l+1<<16>>16}while((l<<16>>16|0)<(f|0));l=0;do{r=2;e=2;o=32767;while(1){t=Ve[n+(r<<1)>>1]|0;s=t<<16>>16>-1?t<<16>>16<o<<16>>16:0;i=s?e:i;a=r+5|0;e=a&65535;if(e<<16>>16>=40)break;else{r=a<<16>>16;o=s?t:o}}Ve[n+(i<<16>>16<<1)>>1]=-1;l=l+1<<16>>16}while((l<<16>>16|0)<(f|0));l=0;while(1){r=3;e=3;o=32767;while(1){t=Ve[n+(r<<1)>>1]|0;s=t<<16>>16>-1?t<<16>>16<o<<16>>16:0;i=s?e:i;a=r+5|0;e=a&65535;if(e<<16>>16>=40){o=i;break}else{r=a<<16>>16;o=s?t:o}}Ve[n+(o<<16>>16<<1)>>1]=-1;l=l+1<<16>>16;if((l<<16>>16|0)>=(f|0)){i=0;break}else i=o}do{r=4;e=4;l=32767;while(1){t=Ve[n+(r<<1)>>1]|0;s=t<<16>>16>-1?t<<16>>16<l<<16>>16:0;o=s?e:o;a=r+5|0;e=a&65535;if(e<<16>>16>=40)break;else{r=a<<16>>16;l=s?t:l}}Ve[n+(o<<16>>16<<1)>>1]=-1;i=i+1<<16>>16}while((i<<16>>16|0)<(f|0));return}function _t(e,r,n,t,i,o,a,s){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;var l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0;_=Ge;Ge=Ge+80|0;v=_;d=40;h=r;w=e;f=256;u=256;while(1){l=Ve[h>>1]|0;h=h+2|0;l=Ze(l,l)|0;if((l|0)!=1073741824){c=(l<<1)+f|0;if((l^f|0)>0&(c^f|0)<0){We[s>>2]=1;f=(f>>>31)+2147483647|0}else f=c}else{We[s>>2]=1;f=2147483647}b=Ve[w>>1]|0;u=(Ze(b<<1,b)|0)+u|0;d=d+-1<<16>>16;if(!(d<<16>>16))break;else w=w+2|0}b=ai(f,s)|0;p=b<<5;b=((p>>5|0)==(b|0)?p:b>>31^2147418112)>>16;p=(ai(u,s)|0)<<5>>16;S=39;m=r+78|0;E=v+78|0;l=n+78|0;while(1){w=Ze(Ve[m>>1]|0,b)|0;m=m+-2|0;h=w<<1;r=e+(S<<1)|0;f=Ve[r>>1]|0;d=Ze(f<<16>>16,p)|0;if((d|0)!=1073741824){c=(d<<1)+h|0;if((d^h|0)>0&(c^h|0)<0){We[s>>2]=1;c=(w>>>30&1)+2147483647|0}}else{We[s>>2]=1;c=2147483647}u=c<<10;u=Ni((u>>10|0)==(c|0)?u:c>>31^2147483647,s)|0;if(u<<16>>16>-1)Ve[l>>1]=32767;else{Ve[l>>1]=-32767;if(u<<16>>16==-32768)u=32767;else u=0-(u&65535)&65535;if(f<<16>>16==-32768)c=32767;else c=0-(f&65535)&65535;Ve[r>>1]=c}l=l+-2|0;Ve[E>>1]=u;if((S|0)<=0)break;else{S=S+-1|0;E=E+-2|0}}r=i<<16>>16;if(i<<16>>16<=0){Ve[o+(r<<1)>>1]=Ve[o>>1]|0;Ge=_;return}w=a&65535;h=0;d=-1;l=0;while(1){if((h|0)<40){u=h;c=h&65535;f=-1;while(1){s=Ve[v+(u<<1)>>1]|0;a=s<<16>>16>f<<16>>16;f=a?s:f;l=a?c:l;u=u+w|0;c=u&65535;if(c<<16>>16>=40)break;else u=u<<16>>16}}else f=-1;Ve[t+(h<<1)>>1]=l;if(f<<16>>16>d<<16>>16)Ve[o>>1]=h;else f=d;h=h+1|0;if((h&65535)<<16>>16==i<<16>>16)break;else d=f}l=Ve[o>>1]|0;Ve[o+(r<<1)>>1]=l;if(i<<16>>16>1)f=1;else{Ge=_;return}do{t=l+1<<16>>16;l=t<<16>>16>=i<<16>>16?0:t;Ve[o+(f<<1)>>1]=l;Ve[o+(f+r<<1)>>1]=l;f=f+1|0}while((f&65535)<<16>>16!=i<<16>>16);Ge=_;return}function kt(e){e=e|0;var r=0;if(!e){e=-1;return e|0}We[e>>2]=0;r=xi(12)|0;if(!r){e=-1;return e|0}Ve[r>>1]=8;We[e>>2]=r;Ve[r+2>>1]=3;Ve[r+4>>1]=0;We[r+8>>2]=0;e=0;return e|0}function Ft(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Hi(r);We[e>>2]=0;return}function Mt(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0;do{if((r|0)==8){t=e+2|0;i=(Ve[t>>1]|0)+-1<<16>>16;Ve[t>>1]=i;r=e+8|0;if(!(We[r>>2]|0)){We[n>>2]=1;Ve[t>>1]=3;break}o=e+4|0;if(i<<16>>16>2&(Ve[o>>1]|0)>0){We[n>>2]=2;Ve[o>>1]=(Ve[o>>1]|0)+-1<<16>>16;break}if(!(i<<16>>16)){We[n>>2]=2;Ve[t>>1]=Ve[e>>1]|0;break}else{We[n>>2]=3;break}}else{Ve[e+2>>1]=Ve[e>>1]|0;We[n>>2]=0;r=e+8|0}}while(0);We[r>>2]=We[n>>2];return}function gt(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0;if(!e){e=-1;return e|0}We[e>>2]=0;n=xi(12)|0;t=n;if(!n){e=-1;return e|0}We[n>>2]=0;i=n+4|0;We[i>>2]=0;o=n+8|0;We[o>>2]=r;if((st(n)|0)<<16>>16==0?(tn(i,We[o>>2]|0)|0)<<16>>16==0:0){lt(We[n>>2]|0)|0;an(We[i>>2]|0)|0;We[e>>2]=t;e=0;return e|0}ft(n);on(i);Hi(n);e=-1;return e|0}function Rt(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;ft(r);on((We[e>>2]|0)+4|0);Hi(We[e>>2]|0);We[e>>2]=0;return}function At(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0,a=0,s=0,l=0,f=0;l=Ge;Ge=Ge+448|0;a=l+320|0;s=l;Vi(t|0,0,488)|0;o=0;do{f=n+(o<<1)|0;Ve[f>>1]=(Xe[f>>1]|0)&65528;o=o+1|0}while((o|0)!=160);ut(We[e>>2]|0,n,160);f=e+4|0;sn(We[f>>2]|0,r,n,a,i,s)|0;ct(We[i>>2]|0,a,t,(We[f>>2]|0)+2392|0);Ge=l;return}function yt(e,r,n,t,i,o,a,s,l,f,u,c,d,h,w,m){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;u=u|0;c=c|0;d=d|0;h=h|0;w=w|0;m=m|0;var E=0,p=0,S=0;S=Ge;Ge=Ge+48|0;E=S+22|0;p=S;Ui(i,(e&-2|0)==6?n:r,E);Ui(i,t,p);n=u;r=E;i=n+22|0;do{Ve[n>>1]=Ve[r>>1]|0;n=n+2|0;r=r+2|0}while((n|0)<(i|0));Li(o,u,d,40,f,0);Li(p,d,d,40,f,0);Di(o,a,w,40);n=c;r=w;i=n+80|0;do{Ve[n>>1]=Ve[r>>1]|0;n=n+2|0;r=r+2|0}while((n|0)<(i|0));Li(o,c,m,40,s,0);Di(E,m,h,40);Li(p,h,h,40,l,0);Ge=S;return}function Ot(e,r,n,t,i,o,a,s,l,f,u,c,d,h,w,m,E){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;l=l|0;f=f|0;u=u|0;c=c|0;d=d|0;h=h|0;w=w|0;m=m|0;E=E|0;var p=0,S=0,b=0,v=0,_=0;if((r|0)==7){b=11;r=t<<16>>16>>>1&65535;p=2}else{b=13;r=t;p=1}Ve[m>>1]=t<<16>>16<13017?t:13017;S=n<<16>>16;w=w+(S<<1)|0;m=r<<16>>16;i=i<<16>>16;n=20;r=l;E=w;while(1){l=E+2|0;_=Ze(Ve[E>>1]|0,m)|0;v=Ze(Ve[l>>1]|0,m)|0;_=(Ze(Ve[r>>1]|0,i)|0)+_<<1;v=(Ze(Ve[r+2>>1]|0,i)|0)+v<<1<<p;Ve[E>>1]=((_<<p)+32768|0)>>>16;Ve[l>>1]=(v+32768|0)>>>16;n=n+-1<<16>>16;if(!(n<<16>>16))break;else{r=r+4|0;E=E+4|0}}r=t<<16>>16;Li(o,w,a+(S<<1)|0,40,c,1);n=30;E=0;while(1){v=n+S|0;Ve[d+(E<<1)>>1]=(Xe[e+(v<<1)>>1]|0)-(Xe[a+(v<<1)>>1]|0);v=Ze(Ve[f+(n<<1)>>1]|0,r)|0;_=(Ze(Ve[u+(n<<1)>>1]|0,i)|0)>>b;Ve[h+(E<<1)>>1]=(Xe[s+(n<<1)>>1]|0)-(v>>>14)-_;E=E+1|0;if((E|0)==10)break;else n=n+1|0}return}function Tt(e){e=e|0;var r=0;if(!e){e=-1;return e|0}We[e>>2]=0;r=xi(16)|0;if(!r){e=-1;return e|0}Ve[r>>1]=0;Ve[r+2>>1]=0;Ve[r+4>>1]=0;Ve[r+6>>1]=0;Ve[r+8>>1]=0;Ve[r+10>>1]=0;Ve[r+12>>1]=0;Ve[r+14>>1]=0;We[e>>2]=r;e=0;return e|0}function Dt(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=0;Ve[e+2>>1]=0;Ve[e+4>>1]=0;Ve[e+6>>1]=0;Ve[e+8>>1]=0;Ve[e+10>>1]=0;Ve[e+12>>1]=0;Ve[e+14>>1]=0;e=0;return e|0}function Nt(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Hi(r);We[e>>2]=0;return}function Pt(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0,a=0;t=Xe[r+6>>1]|0;n=Xe[r+8>>1]|0;i=t-n|0;i=(i&65535|0)!=32767?i&65535:32767;o=Xe[r+10>>1]|0;n=n-o|0;i=(n<<16>>16|0)<(i<<16>>16|0)?n&65535:i;n=Xe[r+12>>1]|0;o=o-n|0;i=(o<<16>>16|0)<(i<<16>>16|0)?o&65535:i;o=Xe[r+14>>1]|0;n=n-o|0;i=(n<<16>>16|0)<(i<<16>>16|0)?n&65535:i;o=o-(Xe[r+16>>1]|0)|0;n=Ve[r+2>>1]|0;a=Xe[r+4>>1]|0;r=(n&65535)-a|0;r=(r&65535|0)!=32767?r&65535:32767;t=a-t|0;if(((o<<16>>16|0)<(i<<16>>16|0)?o&65535:i)<<16>>16<1500?1:(((t<<16>>16|0)<(r<<16>>16|0)?t&65535:r)<<16>>16|0)<((n<<16>>16>32e3?600:n<<16>>16>30500?800:1100)|0)){o=(Ve[e>>1]|0)+1<<16>>16;a=o<<16>>16>11;Ve[e>>1]=a?12:o;return a&1|0}else{Ve[e>>1]=0;return 0}return 0}function Ct(e,r,n){e=e|0;r=r|0;n=n|0;r=Pi(r,3,n)|0;r=Wt(r,Ve[e+2>>1]|0,n)|0;r=Wt(r,Ve[e+4>>1]|0,n)|0;r=Wt(r,Ve[e+6>>1]|0,n)|0;r=Wt(r,Ve[e+8>>1]|0,n)|0;r=Wt(r,Ve[e+10>>1]|0,n)|0;r=Wt(r,Ve[e+12>>1]|0,n)|0;return(Wt(r,Ve[e+14>>1]|0,n)|0)<<16>>16>15565|0}function It(e,r,n){e=e|0;r=r|0;n=n|0;var t=0;n=e+4|0;Ve[e+2>>1]=Ve[n>>1]|0;t=e+6|0;Ve[n>>1]=Ve[t>>1]|0;n=e+8|0;Ve[t>>1]=Ve[n>>1]|0;t=e+10|0;Ve[n>>1]=Ve[t>>1]|0;n=e+12|0;Ve[t>>1]=Ve[n>>1]|0;e=e+14|0;Ve[n>>1]=Ve[e>>1]|0;Ve[e>>1]=r<<16>>16>>>3;return}function Bt(e){e=e|0;var r=0,n=0,t=0;if(!e){t=-1;return t|0}We[e>>2]=0;r=xi(128)|0;if(!r){t=-1;return t|0}n=r+72|0;t=n+46|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));Ve[r>>1]=150;Ve[r+36>>1]=150;Ve[r+18>>1]=150;Ve[r+54>>1]=0;Ve[r+2>>1]=150;Ve[r+38>>1]=150;Ve[r+20>>1]=150;Ve[r+56>>1]=0;Ve[r+4>>1]=150;Ve[r+40>>1]=150;Ve[r+22>>1]=150;Ve[r+58>>1]=0;Ve[r+6>>1]=150;Ve[r+42>>1]=150;Ve[r+24>>1]=150;Ve[r+60>>1]=0;Ve[r+8>>1]=150;Ve[r+44>>1]=150;Ve[r+26>>1]=150;Ve[r+62>>1]=0;Ve[r+10>>1]=150;Ve[r+46>>1]=150;Ve[r+28>>1]=150;Ve[r+64>>1]=0;Ve[r+12>>1]=150;Ve[r+48>>1]=150;Ve[r+30>>1]=150;Ve[r+66>>1]=0;Ve[r+14>>1]=150;Ve[r+50>>1]=150;Ve[r+32>>1]=150;Ve[r+68>>1]=0;Ve[r+16>>1]=150;Ve[r+52>>1]=150;Ve[r+34>>1]=150;Ve[r+70>>1]=0;Ve[r+118>>1]=13106;Ve[r+120>>1]=0;Ve[r+122>>1]=0;Ve[r+124>>1]=0;Ve[r+126>>1]=13106;We[e>>2]=r;t=0;return t|0}function Lt(e){e=e|0;var r=0,n=0;if(!e){n=-1;return n|0}r=e+72|0;n=r+46|0;do{Ve[r>>1]=0;r=r+2|0}while((r|0)<(n|0));Ve[e>>1]=150;Ve[e+36>>1]=150;Ve[e+18>>1]=150;Ve[e+54>>1]=0;Ve[e+2>>1]=150;Ve[e+38>>1]=150;Ve[e+20>>1]=150;Ve[e+56>>1]=0;Ve[e+4>>1]=150;Ve[e+40>>1]=150;Ve[e+22>>1]=150;Ve[e+58>>1]=0;Ve[e+6>>1]=150;Ve[e+42>>1]=150;Ve[e+24>>1]=150;Ve[e+60>>1]=0;Ve[e+8>>1]=150;Ve[e+44>>1]=150;Ve[e+26>>1]=150;Ve[e+62>>1]=0;Ve[e+10>>1]=150;Ve[e+46>>1]=150;Ve[e+28>>1]=150;Ve[e+64>>1]=0;Ve[e+12>>1]=150;Ve[e+48>>1]=150;Ve[e+30>>1]=150;Ve[e+66>>1]=0;Ve[e+14>>1]=150;Ve[e+50>>1]=150;Ve[e+32>>1]=150;Ve[e+68>>1]=0;Ve[e+16>>1]=150;Ve[e+52>>1]=150;Ve[e+34>>1]=150;Ve[e+70>>1]=0;Ve[e+118>>1]=13106;Ve[e+120>>1]=0;Ve[e+122>>1]=0;Ve[e+124>>1]=0;Ve[e+126>>1]=13106;n=0;return n|0}function Ut(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Hi(r);We[e>>2]=0;return}function xt(e,r){e=e|0;r=r|0;Ve[e+118>>1]=r;return}function Ht(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0;n=Ni(n,t)|0;if(n<<16>>16<=0)return;n=n<<16>>16;if((n*21298|0)==1073741824){We[t>>2]=1;i=2147483647}else i=n*42596|0;n=r-i|0;if(((n^r)&(i^r)|0)<0){We[t>>2]=1;n=(r>>>31)+2147483647|0}if((n|0)<=0)return;e=e+104|0;Ve[e>>1]=Xe[e>>1]|0|16384;return}function zt(e,r,n){e=e|0;r=r|0;n=n|0;var t=0;e=e+104|0;t=Pi(Ve[e>>1]|0,1,n)|0;Ve[e>>1]=t;if(!(r<<16>>16))return;Ve[e>>1]=(Pi(t,1,n)|0)&65535|8192;return}function Yt(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0;i=e+112|0;t=Bi(Ve[i>>1]|0,Ve[r>>1]|0,n)|0;t=(t&65535)-((t&65535)>>>15&65535)|0;t=((t<<16>>31^t)&65535)<<16>>16<4;o=Ve[r>>1]|0;Ve[i>>1]=o;r=r+2|0;o=Bi(o,Ve[r>>1]|0,n)|0;o=(o&65535)-((o&65535)>>>15&65535)|0;t=((o<<16>>31^o)&65535)<<16>>16<4?t?2:1:t&1;Ve[i>>1]=Ve[r>>1]|0;i=e+102|0;Ve[i>>1]=Pi(Ve[i>>1]|0,1,n)|0;r=e+110|0;if((Wt(Ve[r>>1]|0,t,n)|0)<<16>>16<=3){Ve[r>>1]=t;return}Ve[i>>1]=Xe[i>>1]|0|16384;Ve[r>>1]=t;return}function jt(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0;g=Ge;Ge=Ge+352|0;f=g+24|0;F=g;a=0;i=0;do{t=Ve[r+(a+-40<<1)>>1]|0;t=Ze(t,t)|0;if((t|0)!=1073741824){o=(t<<1)+i|0;if((t^i|0)>0&(o^i|0)<0){We[n>>2]=1;i=(i>>>31)+2147483647|0}else i=o}else{We[n>>2]=1;i=2147483647}a=a+1|0}while((a|0)!=160);u=i;if((343039-u&u|0)<0){We[n>>2]=1;i=(u>>>31)+2147483647|0}else i=u+-343040|0;if((i|0)<0){k=e+102|0;Ve[k>>1]=Xe[k>>1]&16383}l=u+-15e3|0;c=(14999-u&u|0)<0;if(c){We[n>>2]=1;o=(u>>>31)+2147483647|0}else o=l;if((o|0)<0){k=e+108|0;Ve[k>>1]=Xe[k>>1]&16383}t=e+72|0;s=e+74|0;o=Ve[t>>1]|0;a=Ve[s>>1]|0;i=0;do{k=i<<2;v=Bi((Ve[r+(k<<1)>>1]|0)>>>2&65535,((o<<16>>16)*21955|0)>>>15&65535,n)|0;p=((v<<16>>16)*21955|0)>>>15&65535;E=Wt(o,p,n)|0;b=k|1;_=Bi((Ve[r+(b<<1)>>1]|0)>>>2&65535,((a<<16>>16)*6390|0)>>>15&65535,n)|0;S=((_<<16>>16)*6390|0)>>>15&65535;o=Wt(a,S,n)|0;Ve[f+(k<<1)>>1]=Wt(E,o,n)|0;Ve[f+(b<<1)>>1]=Bi(E,o,n)|0;b=k|2;o=Bi((Ve[r+(b<<1)>>1]|0)>>>2&65535,p,n)|0;v=Wt(v,((o<<16>>16)*21955|0)>>>15&65535,n)|0;k=k|3;a=Bi((Ve[r+(k<<1)>>1]|0)>>>2&65535,S,n)|0;_=Wt(_,((a<<16>>16)*6390|0)>>>15&65535,n)|0;Ve[f+(b<<1)>>1]=Wt(v,_,n)|0;Ve[f+(k<<1)>>1]=Bi(v,_,n)|0;i=i+1|0}while((i|0)!=40);Ve[t>>1]=o;Ve[s>>1]=a;a=e+76|0;o=e+80|0;i=0;do{k=i<<2;qt(f+(k<<1)|0,f+((k|2)<<1)|0,a,n);qt(f+((k|1)<<1)|0,f+((k|3)<<1)|0,o,n);i=i+1|0}while((i|0)!=40);a=e+84|0;o=e+86|0;i=e+92|0;t=0;do{k=t<<3;Kt(f+(k<<1)|0,f+((k|4)<<1)|0,a,n);Kt(f+((k|2)<<1)|0,f+((k|6)<<1)|0,o,n);Kt(f+((k|3)<<1)|0,f+((k|7)<<1)|0,i,n);t=t+1|0}while((t|0)!=20);a=e+88|0;o=e+90|0;i=0;do{k=i<<4;Kt(f+(k<<1)|0,f+((k|8)<<1)|0,a,n);Kt(f+((k|4)<<1)|0,f+((k|12)<<1)|0,o,n);i=i+1|0}while((i|0)!=10);m=Vt(f,e+70|0,32,40,4,1,15,n)|0;Ve[F+16>>1]=m;E=Vt(f,e+68|0,16,20,8,7,16,n)|0;Ve[F+14>>1]=E;p=Vt(f,e+66|0,16,20,8,3,16,n)|0;Ve[F+12>>1]=p;S=Vt(f,e+64|0,16,20,8,2,16,n)|0;Ve[F+10>>1]=S;b=Vt(f,e+62|0,16,20,8,6,16,n)|0;Ve[F+8>>1]=b;v=Vt(f,e+60|0,8,10,16,4,16,n)|0;Ve[F+6>>1]=v;_=Vt(f,e+58|0,8,10,16,12,16,n)|0;Ve[F+4>>1]=_;k=Vt(f,e+56|0,8,10,16,8,16,n)|0;Ve[F+2>>1]=k;w=Vt(f,e+54|0,8,10,16,0,16,n)|0;Ve[F>>1]=w;a=0;t=0;do{o=e+(t<<1)|0;r=vi(Ve[o>>1]|0)|0;o=Ve[o>>1]|0;i=r<<16>>16;if(r<<16>>16<0){s=0-i<<16;if((s|0)<983040)s=o<<16>>16>>(s>>16)&65535;else s=0}else{s=o<<16>>16;o=s<<i;if((o<<16>>16>>i|0)==(s|0))s=o&65535;else s=(s>>>15^32767)&65535}o=Gt(Pi(Ve[F+(t<<1)>>1]|0,1,n)|0,s)|0;h=Bi(r,5,n)|0;i=h<<16>>16;if(h<<16>>16<0){s=0-i<<16;if((s|0)<983040)s=o<<16>>16>>(s>>16);else s=0}else{o=o<<16>>16;s=o<<i;if((s<<16>>16>>i|0)!=(o|0))s=o>>>15^32767}s=s<<16>>16;s=Ze(s,s)|0;if((s|0)!=1073741824){o=(s<<1)+a|0;if((s^a|0)>0&(o^a|0)<0){We[n>>2]=1;a=(a>>>31)+2147483647|0}else a=o}else{We[n>>2]=1;a=2147483647}t=t+1|0}while((t|0)!=9);h=a<<6;a=(((h>>6|0)==(a|0)?h:a>>31^2147418112)>>16)*3641>>15;if((a|0)>32767){We[n>>2]=1;a=32767}h=Ve[e>>1]|0;s=h<<16>>16;d=Ve[e+2>>1]|0;o=(d<<16>>16)+s|0;if((d^h)<<16>>16>-1&(o^s|0)<0){We[n>>2]=1;o=(s>>>31)+2147483647|0}h=Ve[e+4>>1]|0;s=h+o|0;if((h^o|0)>-1&(s^o|0)<0){We[n>>2]=1;s=(o>>>31)+2147483647|0}h=Ve[e+6>>1]|0;o=h+s|0;if((h^s|0)>-1&(o^s|0)<0){We[n>>2]=1;o=(s>>>31)+2147483647|0}h=Ve[e+8>>1]|0;s=h+o|0;if((h^o|0)>-1&(s^o|0)<0){We[n>>2]=1;s=(o>>>31)+2147483647|0}h=Ve[e+10>>1]|0;o=h+s|0;if((h^s|0)>-1&(o^s|0)<0){We[n>>2]=1;o=(s>>>31)+2147483647|0}h=Ve[e+12>>1]|0;s=h+o|0;if((h^o|0)>-1&(s^o|0)<0){We[n>>2]=1;s=(o>>>31)+2147483647|0}h=Ve[e+14>>1]|0;o=h+s|0;if((h^s|0)>-1&(o^s|0)<0){We[n>>2]=1;o=(s>>>31)+2147483647|0}h=Ve[e+16>>1]|0;s=h+o|0;if((h^o|0)>-1&(s^o|0)<0){We[n>>2]=1;s=(o>>>31)+2147483647|0}d=s<<13;d=((d>>13|0)==(s|0)?d:s>>31^2147418112)>>>16&65535;s=(Ze((Bi(d,0,n)|0)<<16>>16,-2808)|0)>>15;if((s|0)>32767){We[n>>2]=1;s=32767}f=Wt(s&65535,1260,n)|0;h=e+100|0;s=Pi(Ve[h>>1]|0,1,n)|0;if((a<<16>>16|0)>((f<<16>>16<720?720:f<<16>>16)|0))s=(s&65535|16384)&65535;Ve[h>>1]=s;if(c){We[n>>2]=1;l=(u>>>31)+2147483647|0}i=Ve[e+118>>1]|0;c=e+126|0;s=Ve[c>>1]|0;t=s<<16>>16<19660;t=i<<16>>16<s<<16>>16?t?2621:6553:t?2621:655;r=s&65535;a=r<<16;s=Ze(t,s<<16>>16)|0;if((s|0)==1073741824){We[n>>2]=1;s=2147483647}else s=s<<1;o=a-s|0;if(((o^a)&(s^a)|0)<0){We[n>>2]=1;o=(r>>>15)+2147483647|0}a=Ze(t,i<<16>>16)|0;do{if((a|0)==1073741824){We[n>>2]=1;s=2147483647}else{s=o+(a<<1)|0;if(!((o^a|0)>0&(s^o|0)<0))break;We[n>>2]=1;s=(o>>>31)+2147483647|0}}while(0);r=Ni(s,n)|0;u=(l|0)>-1;Ve[c>>1]=u?r<<16>>16<13106?13106:r:13106;r=e+106|0;Ve[r>>1]=Pi(Ve[r>>1]|0,1,n)|0;o=e+108|0;s=Pi(Ve[o>>1]|0,1,n)|0;Ve[o>>1]=s;a=Ve[c>>1]|0;e:do{if(u){do{if(a<<16>>16>19660)Ve[r>>1]=Xe[r>>1]|16384;else{if(a<<16>>16>16383)break;a=e+116|0;s=0;break e}}while(0);Ve[o>>1]=s&65535|16384;M=62}else M=62}while(0);do{if((M|0)==62){s=e+116|0;if(a<<16>>16<=22936){a=s;s=0;break}a=s;s=Wt(Ve[s>>1]|0,1,n)|0}}while(0);Ve[a>>1]=s;if((Ve[r>>1]&32640)!=32640){f=(Ve[o>>1]&32767)==32767;Ve[e+122>>1]=f&1;if(f)M=67}else{Ve[e+122>>1]=1;M=67}do{if((M|0)==67){a=e+98|0;if((Ve[a>>1]|0)>=5)break;Ve[a>>1]=5}}while(0);f=e+102|0;do{if((Ve[f>>1]&24576)==24576)M=71;else{if((Ve[e+104>>1]&31744)==31744){M=71;break}if(!(Ve[h>>1]&32640)){Ve[e+98>>1]=20;o=32767;break}else{o=w;a=0;s=0}while(1){t=Ve[e+18+(a<<1)>>1]|0;i=o<<16>>16>t<<16>>16;l=i?o:t;o=i?t:o;l=l<<16>>16<184?184:l;o=o<<16>>16<184?184:o;t=vi(o)|0;i=t<<16>>16;do{if(t<<16>>16<0){r=0-i<<16;if((r|0)>=983040){r=0;break}r=o<<16>>16>>(r>>16)&65535}else{r=o<<16>>16;o=r<<i;if((o<<16>>16>>i|0)==(r|0)){r=o&65535;break}r=(r>>>15^32767)&65535}}while(0);l=Gt(Pi(l,1,n)|0,r)|0;s=Wt(s,Pi(l,Bi(8,t,n)|0,n)|0,n)|0;a=a+1|0;if((a|0)==9)break;o=Ve[F+(a<<1)>>1]|0}if(s<<16>>16>1e3){Ve[e+98>>1]=20;o=32767;break}o=Ve[h>>1]|0;a=e+98|0;s=Ve[a>>1]|0;do{if(!(o&16384))M=86;else{if(!(s<<16>>16)){s=o;break}s=Bi(s,1,n)|0;Ve[a>>1]=s;M=86}}while(0);if((M|0)==86){if(s<<16>>16==20){o=32767;break}s=Ve[h>>1]|0}o=(s&16384)==0?16383:3276}}while(0);if((M|0)==71){Ve[e+98>>1]=20;o=32767}a=w;s=0;while(1){l=e+18+(s<<1)|0;r=Si(o,Bi(a,Ve[l>>1]|0,n)|0,n)|0;Ve[l>>1]=Wt(Ve[l>>1]|0,r,n)|0;s=s+1|0;if((s|0)==9)break;a=Ve[F+(s<<1)>>1]|0}do{if(!(Ve[h>>1]&30720)){if(Ve[f>>1]&30720){M=95;break}if(!(Ve[e+114>>1]|0)){i=2097;t=1638;r=2}else M=95}else M=95}while(0);do{if((M|0)==95){if((Ve[e+98>>1]|0)==0?(Ve[e+114>>1]|0)==0:0){i=1867;t=491;r=2;break}i=1638;t=0;r=0}}while(0);o=0;do{a=e+(o<<1)|0;s=Bi(Ve[e+36+(o<<1)>>1]|0,Ve[a>>1]|0,n)|0;if(s<<16>>16<0){s=Si(i,s,n)|0;s=Wt(-2,Wt(Ve[a>>1]|0,s,n)|0,n)|0;s=s<<16>>16<40?40:s}else{s=Si(t,s,n)|0;s=Wt(r,Wt(Ve[a>>1]|0,s,n)|0,n)|0;s=s<<16>>16>16e3?16e3:s}Ve[a>>1]=s;o=o+1|0}while((o|0)!=9);Ve[e+36>>1]=w;Ve[e+38>>1]=k;Ve[e+40>>1]=_;Ve[e+42>>1]=v;Ve[e+44>>1]=b;Ve[e+46>>1]=S;Ve[e+48>>1]=p;Ve[e+50>>1]=E;Ve[e+52>>1]=m;a=d<<16>>16>100;o=a?7:4;a=a?4:5;if(!u){Ve[e+94>>1]=0;Ve[e+96>>1]=0;Ve[e+114>>1]=0;Ve[e+116>>1]=0;n=0;e=e+120|0;Ve[e>>1]=n;Ge=g;return n|0}i=e+114|0;t=Ve[i>>1]|0;do{if((Ve[e+116>>1]|0)<=100){if(t<<16>>16)break;t=Ve[h>>1]|0;do{if(!(t&16368)){if((Ve[c>>1]|0)>21298)t=1;else break;e=e+120|0;Ve[e>>1]=t;Ge=g;return t|0}}while(0);i=e+94|0;if(!(t&16384)){Ve[i>>1]=0;t=e+96|0;i=Ve[t>>1]|0;if(i<<16>>16<=0){n=0;e=e+120|0;Ve[e>>1]=n;Ge=g;return n|0}Ve[t>>1]=Bi(i,1,n)|0;n=1;e=e+120|0;Ve[e>>1]=n;Ge=g;return n|0}else{n=Wt(Ve[i>>1]|0,1,n)|0;Ve[i>>1]=n;if((n<<16>>16|0)<(a|0)){n=1;e=e+120|0;Ve[e>>1]=n;Ge=g;return n|0}Ve[e+96>>1]=o;n=1;e=e+120|0;Ve[e>>1]=n;Ge=g;return n|0}}else{if(t<<16>>16>=250)break;Ve[i>>1]=250;t=250}}while(0);Ve[e+94>>1]=4;Ve[i>>1]=Bi(t,1,n)|0;n=1;e=e+120|0;Ve[e>>1]=n;Ge=g;return n|0}function qt(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0;i=(Ve[n>>1]|0)*21955>>15;if((i|0)>32767){We[t>>2]=1;i=32767}o=Bi(Ve[e>>1]|0,i&65535,t)|0;i=(o<<16>>16)*21955>>15;if((i|0)>32767){We[t>>2]=1;i=32767}a=Wt(Ve[n>>1]|0,i&65535,t)|0;Ve[n>>1]=o;n=n+2|0;i=(Ve[n>>1]|0)*6390>>15;if((i|0)>32767){We[t>>2]=1;i=32767}o=Bi(Ve[r>>1]|0,i&65535,t)|0;i=(o<<16>>16)*6390>>15;if((i|0)>32767){We[t>>2]=1;i=32767}i=Wt(Ve[n>>1]|0,i&65535,t)|0;Ve[n>>1]=o;Ve[e>>1]=Pi(Wt(a,i,t)|0,1,t)|0;Ve[r>>1]=Pi(Bi(a,i,t)|0,1,t)|0;return}function Kt(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0;i=(Ve[n>>1]|0)*13363>>15;if((i|0)>32767){We[t>>2]=1;i=32767}o=Bi(Ve[r>>1]|0,i&65535,t)|0;i=(o<<16>>16)*13363>>15;if((i|0)>32767){We[t>>2]=1;i=32767}i=Wt(Ve[n>>1]|0,i&65535,t)|0;Ve[n>>1]=o;Ve[r>>1]=Pi(Bi(Ve[e>>1]|0,i,t)|0,1,t)|0;Ve[e>>1]=Pi(Wt(Ve[e>>1]|0,i,t)|0,1,t)|0;return}function Vt(e,r,n,t,i,o,a,s){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;var l=0,f=0,u=0,c=0,d=0,h=0;if(n<<16>>16<t<<16>>16){c=i<<16>>16;l=o<<16>>16;d=n<<16>>16;f=0;do{h=Ve[e+((Ze(d,c)|0)+l<<1)>>1]|0;h=(h&65535)-((h&65535)>>>15&65535)|0;h=(h<<16>>31^h)<<16;u=(h>>15)+f|0;if((h>>16^f|0)>0&(u^f|0)<0){We[s>>2]=1;f=(f>>>31)+2147483647|0}else f=u;d=d+1|0}while((d&65535)<<16>>16!=t<<16>>16);d=f}else d=0;f=Ve[r>>1]|0;h=Bi(16,a,s)|0;l=h<<16>>16;if(h<<16>>16>0){t=f<<l;if((t>>l|0)!=(f|0))t=f>>31^2147483647}else{l=0-l<<16;if((l|0)<2031616)t=f>>(l>>16);else t=0}l=t+d|0;if((t^d|0)>-1&(l^d|0)<0){We[s>>2]=1;l=(d>>>31)+2147483647|0}h=a<<16>>16;a=a<<16>>16>0;if(a){t=d<<h;if((t>>h|0)!=(d|0))t=d>>31^2147483647}else{t=0-h<<16;if((t|0)<2031616)t=d>>(t>>16);else t=0}Ve[r>>1]=t>>>16;if(n<<16>>16>0){c=i<<16>>16;f=o<<16>>16;u=0;do{o=Ve[e+((Ze(u,c)|0)+f<<1)>>1]|0;o=(o&65535)-((o&65535)>>>15&65535)|0;o=(o<<16>>31^o)<<16;t=(o>>15)+l|0;if((o>>16^l|0)>0&(t^l|0)<0){We[s>>2]=1;l=(l>>>31)+2147483647|0}else l=t;u=u+1|0}while((u&65535)<<16>>16!=n<<16>>16)}if(a){t=l<<h;if((t>>h|0)==(l|0)){s=t;s=s>>>16;s=s&65535;return s|0}s=l>>31^2147483647;s=s>>>16;s=s&65535;return s|0}else{t=0-h<<16;if((t|0)>=2031616){s=0;s=s>>>16;s=s&65535;return s|0}s=l>>(t>>16);s=s>>>16;s=s&65535;return s|0}return 0}function Wt(e,r,n){e=e|0;r=r|0;n=n|0;e=(r<<16>>16)+(e<<16>>16)|0;if((e|0)<=32767){if((e|0)<-32768){We[n>>2]=1;e=-32768}}else{We[n>>2]=1;e=32767}return e&65535|0}function Xt(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0;k=Ge;Ge=Ge+32|0;v=k+12|0;_=k;Ve[v>>1]=1024;Ve[_>>1]=1024;l=Ve[e+2>>1]|0;a=Ve[e+20>>1]|0;t=((a+l|0)>>>2)+64512|0;Ve[v+2>>1]=t;a=((l-a|0)>>>2)+1024|0;Ve[_+2>>1]=a;l=Ve[e+4>>1]|0;i=Ve[e+18>>1]|0;t=((i+l|0)>>>2)-t|0;Ve[v+4>>1]=t;a=((l-i|0)>>>2)+a|0;Ve[_+4>>1]=a;i=Ve[e+6>>1]|0;l=Ve[e+16>>1]|0;t=((l+i|0)>>>2)-t|0;Ve[v+6>>1]=t;a=((i-l|0)>>>2)+a|0;Ve[_+6>>1]=a;l=Ve[e+8>>1]|0;i=Ve[e+14>>1]|0;t=((i+l|0)>>>2)-t|0;Ve[v+8>>1]=t;a=((l-i|0)>>>2)+a|0;Ve[_+8>>1]=a;i=Ve[e+10>>1]|0;l=Ve[e+12>>1]|0;t=((l+i|0)>>>2)-t|0;Ve[v+10>>1]=t;Ve[_+10>>1]=((i-l|0)>>>2)+a;a=Ve[3454]|0;l=a<<16>>16;e=Ve[v+2>>1]|0;i=(e<<16>>16<<14)+(l<<10)|0;E=i&-65536;i=(i>>>1)-(i>>16<<15)<<16;b=(((Ze(i>>16,l)|0)>>15)+(Ze(E>>16,l)|0)<<2)+-16777216|0;b=(Ve[v+4>>1]<<14)+b|0;s=b>>16;b=(b>>>1)-(s<<15)<<16;E=(((Ze(b>>16,l)|0)>>15)+(Ze(s,l)|0)<<2)-((i>>15)+E)|0;E=(Ve[v+6>>1]<<14)+E|0;i=E>>16;E=(E>>>1)-(i<<15)<<16;s=(((Ze(E>>16,l)|0)>>15)+(Ze(i,l)|0)<<2)-((b>>15)+(s<<16))|0;s=(Ve[v+8>>1]<<14)+s|0;b=s>>16;i=(t<<16>>3)+((((Ze((s>>>1)-(b<<15)<<16>>16,l)|0)>>15)+(Ze(b,l)|0)<<1)-((E>>15)+(i<<16)))|0;E=v+4|0;l=v;b=0;s=0;t=0;m=v+10|0;i=(i+33554432|0)>>>0<67108863?i>>>10&65535:(i|0)>33554431?32767:-32768;e:while(1){p=e<<16>>16<<14;w=l+6|0;h=l+8|0;d=s<<16>>16;while(1){if((d|0)>=60)break e;l=(d&65535)+1<<16>>16;f=Ve[6908+(l<<16>>16<<1)>>1]|0;S=f<<16>>16;s=p+(S<<10)|0;o=s&-65536;s=(s>>>1)-(s>>16<<15)<<16;u=(((Ze(s>>16,S)|0)>>15)+(Ze(o>>16,S)|0)<<2)+-16777216|0;c=Ve[E>>1]|0;u=(c<<16>>16<<14)+u|0;g=u>>16;u=(u>>>1)-(g<<15)<<16;o=(((Ze(u>>16,S)|0)>>15)+(Ze(g,S)|0)<<2)-((s>>15)+o)|0;s=Ve[w>>1]|0;o=(s<<16>>16<<14)+o|0;e=o>>16;o=(o>>>1)-(e<<15)<<16;g=(((Ze(o>>16,S)|0)>>15)+(Ze(e,S)|0)<<2)-((u>>15)+(g<<16))|0;u=Ve[h>>1]|0;g=(u<<16>>16<<14)+g|0;M=g>>16;e=(((Ze((g>>>1)-(M<<15)<<16>>16,S)|0)>>15)+(Ze(M,S)|0)<<1)-((o>>15)+(e<<16))|0;o=Ve[m>>1]|0;e=(o<<16>>16<<13)+e|0;e=(e+33554432|0)>>>0<67108863?e>>>10&65535:(e|0)>33554431?32767:-32768;if((Ze(e<<16>>16,i<<16>>16)|0)<1){S=l;l=c;break}else{d=d+1|0;a=f;i=e}}E=o<<16>>16<<13;m=l<<16>>16<<14;c=s<<16>>16<<14;h=u<<16>>16<<14;o=f<<16>>16;d=4;while(1){M=(a<<16>>16>>>1)+(o>>>1)|0;o=M<<16;w=o>>16;o=p+(o>>6)|0;g=o&-65536;o=(o>>>1)-(o>>16<<15)<<16;u=m+((((Ze(o>>16,w)|0)>>15)+(Ze(g>>16,w)|0)<<2)+-16777216)|0;l=u>>16;u=(u>>>1)-(l<<15)<<16;g=c+((((Ze(u>>16,w)|0)>>15)+(Ze(l,w)|0)<<2)-((o>>15)+g))|0;o=g>>16;g=(g>>>1)-(o<<15)<<16;l=h+((((Ze(g>>16,w)|0)>>15)+(Ze(o,w)|0)<<2)-((u>>15)+(l<<16)))|0;u=l>>16;M=M&65535;o=E+((((Ze((l>>>1)-(u<<15)<<16>>16,w)|0)>>15)+(Ze(u,w)|0)<<1)-((g>>15)+(o<<16)))|0;o=(o+33554432|0)>>>0<67108863?o>>>10&65535:(o|0)>33554431?32767:-32768;g=(Ze(o<<16>>16,e<<16>>16)|0)<1;w=g?f:M;e=g?e:o;a=g?M:a;i=g?o:i;d=d+-1<<16>>16;o=w<<16>>16;if(!(d<<16>>16)){f=o;s=a;a=w;break}else f=w}l=t<<16>>16;o=e<<16>>16;e=(i&65535)-o|0;i=e<<16;if(i){g=(e&65535)-(e>>>15&1)|0;g=g<<16>>31^g;e=(vi(g&65535)|0)<<16>>16;e=(Ze((Gt(16383,g<<16>>16<<e&65535)|0)<<16>>16,(s&65535)-f<<16>>16)|0)>>19-e;if((i|0)<0)e=0-(e<<16>>16)|0;a=f-((Ze(e<<16>>16,o)|0)>>>10)&65535}Ve[r+(l<<1)>>1]=a;i=b<<16>>16==0?_:v;M=a<<16>>16;e=Ve[i+2>>1]|0;o=(e<<16>>16<<14)+(M<<10)|0;g=o&-65536;o=(o>>>1)-(o>>16<<15)<<16;p=(((Ze(o>>16,M)|0)>>15)+(Ze(g>>16,M)|0)<<2)+-16777216|0;p=(Ve[i+4>>1]<<14)+p|0;E=p>>16;p=(p>>>1)-(E<<15)<<16;g=(((Ze(p>>16,M)|0)>>15)+(Ze(E,M)|0)<<2)-((o>>15)+g)|0;g=(Ve[i+6>>1]<<14)+g|0;o=g>>16;g=(g>>>1)-(o<<15)<<16;E=(((Ze(g>>16,M)|0)>>15)+(Ze(o,M)|0)<<2)-((p>>15)+(E<<16))|0;E=(Ve[i+8>>1]<<14)+E|0;p=E>>16;t=t+1<<16>>16;o=(((Ze((E>>>1)-(p<<15)<<16>>16,M)|0)>>15)+(Ze(p,M)|0)<<1)-((g>>15)+(o<<16))|0;o=(Ve[i+10>>1]<<13)+o|0;if(t<<16>>16<10){E=i+4|0;l=i;b=b^1;s=S;m=i+10|0;i=(o+33554432|0)>>>0<67108863?o>>>10&65535:(o|0)>33554431?32767:-32768}else{F=13;break}}if((F|0)==13){Ge=k;return}Ve[r>>1]=Ve[n>>1]|0;Ve[r+2>>1]=Ve[n+2>>1]|0;Ve[r+4>>1]=Ve[n+4>>1]|0;Ve[r+6>>1]=Ve[n+6>>1]|0;Ve[r+8>>1]=Ve[n+8>>1]|0;Ve[r+10>>1]=Ve[n+10>>1]|0;Ve[r+12>>1]=Ve[n+12>>1]|0;Ve[r+14>>1]=Ve[n+14>>1]|0;Ve[r+16>>1]=Ve[n+16>>1]|0;Ve[r+18>>1]=Ve[n+18>>1]|0;Ge=k;return}function Gt(e,r){e=e|0;r=r|0;var n=0,t=0,i=0,o=0,a=0,s=0;i=r<<16>>16;if(e<<16>>16<1?1:e<<16>>16>r<<16>>16){i=0;return i|0}if(e<<16>>16==r<<16>>16){i=32767;return i|0}t=i<<1;n=i<<2;o=e<<16>>16<<3;e=(o|0)<(n|0);o=o-(e?0:n)|0;e=e?0:4;a=(o|0)<(t|0);o=o-(a?0:t)|0;r=(o|0)<(i|0);e=(r&1|(a?e:e|2))<<3^8;r=o-(r?0:i)<<3;if((r|0)>=(n|0)){r=r-n|0;e=e&65528|4}o=(r|0)<(t|0);a=r-(o?0:t)|0;r=(a|0)<(i|0);e=(r&1^1|(o?e:e|2))<<16>>13;r=a-(r?0:i)<<3;if((r|0)>=(n|0)){r=r-n|0;e=e&65528|4}o=(r|0)<(t|0);a=r-(o?0:t)|0;r=(a|0)<(i|0);e=(r&1^1|(o?e:e|2))<<16>>13;r=a-(r?0:i)<<3;if((r|0)>=(n|0)){r=r-n|0;e=e&65528|4}s=(r|0)<(t|0);o=r-(s?0:t)|0;a=(o|0)<(i|0);r=(a&1^1|(s?e:e|2))<<16>>13;e=o-(a?0:i)<<3;if((e|0)>=(n|0)){e=e-n|0;r=r&65528|4}s=(e|0)<(t|0);s=((e-(s?0:t)|0)>=(i|0)|(s?r:r|2))&65535;return s|0}function Zt(e){e=e|0;if(!e){e=-1;return e|0}Ve[e>>1]=-14336;Ve[e+8>>1]=-2381;Ve[e+2>>1]=-14336;Ve[e+10>>1]=-2381;Ve[e+4>>1]=-14336;Ve[e+12>>1]=-2381;Ve[e+6>>1]=-14336;Ve[e+14>>1]=-2381;e=0;return e|0}function Jt(e,r,n,t,i,o,a,s){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;var l=0,f=0,u=0,c=0,d=0,h=0;h=Ge;Ge=Ge+16|0;c=h+2|0;d=h;l=0;f=10;while(1){u=Ve[n>>1]|0;u=((Ze(u,u)|0)>>>3)+l|0;l=Ve[n+2>>1]|0;l=u+((Ze(l,l)|0)>>>3)|0;u=Ve[n+4>>1]|0;u=l+((Ze(u,u)|0)>>>3)|0;l=Ve[n+6>>1]|0;l=u+((Ze(l,l)|0)>>>3)|0;f=f+-1<<16>>16;if(!(f<<16>>16))break;else n=n+8|0}f=l<<4;f=(f|0)<0?2147483647:f;if((r|0)==7){si(((Ni(f,s)|0)<<16>>16)*52428|0,c,d,s);u=Xe[c>>1]<<16;f=Ve[d>>1]<<1;r=Ve[e+8>>1]|0;l=(r<<16>>16)*88|0;if(r<<16>>16>-1&(l|0)<-783741){We[s>>2]=1;n=2147483647}else n=l+783741|0;r=(Ve[e+10>>1]|0)*74|0;l=r+n|0;if((r^n|0)>-1&(l^n|0)<0){We[s>>2]=1;n=(n>>>31)+2147483647|0}else n=l;r=(Ve[e+12>>1]|0)*44|0;l=r+n|0;if((r^n|0)>-1&(l^n|0)<0){We[s>>2]=1;n=(n>>>31)+2147483647|0}else n=l;e=(Ve[e+14>>1]|0)*24|0;l=e+n|0;if((e^n|0)>-1&(l^n|0)<0){We[s>>2]=1;l=(n>>>31)+2147483647|0}e=u+-1966080+f|0;n=l-e|0;if(((n^l)&(l^e)|0)<0){We[s>>2]=1;n=(l>>>31)+2147483647|0}s=n>>17;Ve[t>>1]=s;s=(n>>2)-(s<<15)|0;s=s&65535;Ve[i>>1]=s;Ge=h;return}u=bi(f)|0;l=u<<16>>16;if(u<<16>>16>0){n=f<<l;if((n>>l|0)==(f|0))f=n;else f=f>>31^2147483647}else{l=0-l<<16;if((l|0)<2031616)f=f>>(l>>16);else f=0}li(f,u,c,d);c=Ze(Ve[c>>1]|0,-49320)|0;l=(Ze(Ve[d>>1]|0,-24660)|0)>>15;l=(l&65536|0)==0?l:l|-65536;d=l<<1;n=d+c|0;if((d^c|0)>-1&(n^d|0)<0){We[s>>2]=1;n=(l>>>30&1)+2147483647|0}switch(r|0){case 6:{l=n+2134784|0;if((n|0)>-1&(l^n|0)<0){We[s>>2]=1;l=(n>>>31)+2147483647|0}break}case 5:{Ve[a>>1]=f>>>16;Ve[o>>1]=-11-(u&65535);l=n+2183936|0;if((n|0)>-1&(l^n|0)<0){We[s>>2]=1;l=(n>>>31)+2147483647|0}break}case 4:{l=n+2085632|0;if((n|0)>-1&(l^n|0)<0){We[s>>2]=1;l=(n>>>31)+2147483647|0}break}case 3:{l=n+2065152|0;if((n|0)>-1&(l^n|0)<0){We[s>>2]=1;l=(n>>>31)+2147483647|0}break}default:{l=n+2134784|0;if((n|0)>-1&(l^n|0)<0){We[s>>2]=1;l=(n>>>31)+2147483647|0}}}do{if((l|0)<=2097151)if((l|0)<-2097152){We[s>>2]=1;n=-2147483648;break}else{n=l<<10;break}else{We[s>>2]=1;n=2147483647}}while(0);a=(Ve[e>>1]|0)*11142|0;l=a+n|0;if((a^n|0)>-1&(l^n|0)<0){We[s>>2]=1;l=(n>>>31)+2147483647|0}a=(Ve[e+2>>1]|0)*9502|0;n=a+l|0;if((a^l|0)>-1&(n^l|0)<0){We[s>>2]=1;n=(l>>>31)+2147483647|0}a=(Ve[e+4>>1]|0)*5570|0;l=a+n|0;if((a^n|0)>-1&(l^n|0)<0){We[s>>2]=1;l=(n>>>31)+2147483647|0}e=(Ve[e+6>>1]|0)*3112|0;n=e+l|0;if((e^l|0)>-1&(n^l|0)<0){We[s>>2]=1;n=(l>>>31)+2147483647|0}n=Ze(n>>16,(r|0)==4?10878:10886)|0;if((n|0)<0)n=~((n^-256)>>8);else n=n>>8;Ve[t>>1]=n>>>16;if((n|0)<0)l=~((n^-2)>>1);else l=n>>1;t=n>>16<<15;n=l-t|0;if(((n^l)&(t^l)|0)>=0){s=n;s=s&65535;Ve[i>>1]=s;Ge=h;return}We[s>>2]=1;s=(l>>>31)+2147483647|0;s=s&65535;Ve[i>>1]=s;Ge=h;return}function Qt(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0;i=e+4|0;Ve[e+6>>1]=Ve[i>>1]|0;o=e+12|0;Ve[e+14>>1]=Ve[o>>1]|0;t=e+2|0;Ve[i>>1]=Ve[t>>1]|0;i=e+10|0;Ve[o>>1]=Ve[i>>1]|0;Ve[t>>1]=Ve[e>>1]|0;t=e+8|0;Ve[i>>1]=Ve[t>>1]|0;Ve[t>>1]=r;Ve[e>>1]=n;return}function $t(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0;o=Wt(0,Ve[e+8>>1]|0,t)|0;o=Wt(o,Ve[e+10>>1]|0,t)|0;o=Wt(o,Ve[e+12>>1]|0,t)|0;o=Wt(o,Ve[e+14>>1]|0,t)|0;i=o<<16>>16>>2;i=(o<<16>>16<0?i|49152:i)&65535;Ve[r>>1]=i<<16>>16<-2381?-2381:i;r=Wt(0,Ve[e>>1]|0,t)|0;r=Wt(r,Ve[e+2>>1]|0,t)|0;r=Wt(r,Ve[e+4>>1]|0,t)|0;t=Wt(r,Ve[e+6>>1]|0,t)|0;e=t<<16>>16>>2;e=(t<<16>>16<0?e|49152:e)&65535;Ve[n>>1]=e<<16>>16<-14336?-14336:e;return}function ei(e){e=e|0;We[e>>2]=6892;We[e+4>>2]=8180;We[e+8>>2]=21e3;We[e+12>>2]=9716;We[e+16>>2]=22024;We[e+20>>2]=12788;We[e+24>>2]=24072;We[e+28>>2]=26120;We[e+32>>2]=28168;We[e+36>>2]=6876;We[e+40>>2]=7452;We[e+44>>2]=8140;We[e+48>>2]=20980;We[e+52>>2]=16884;We[e+56>>2]=17908;We[e+60>>2]=7980;We[e+64>>2]=8160;We[e+68>>2]=6678;We[e+72>>2]=6646;We[e+76>>2]=6614;We[e+80>>2]=29704;We[e+84>>2]=28680;We[e+88>>2]=3720;We[e+92>>2]=8;We[e+96>>2]=4172;We[e+100>>2]=44;We[e+104>>2]=3436;We[e+108>>2]=30316;We[e+112>>2]=30796;We[e+116>>2]=31276;We[e+120>>2]=7472;We[e+124>>2]=7552;We[e+128>>2]=7632;We[e+132>>2]=7712;return}function ri(e,r){e=e|0;r=r|0;var n=0,t=0,i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0;c=Ge;Ge=Ge+48|0;f=c+18|0;u=c;l=r<<16>>16;qi(u|0,e|0,l<<1|0)|0;if(r<<16>>16>0){n=0;t=0}else{u=l>>1;u=f+(u<<1)|0;u=Ve[u>>1]|0;u=u<<16>>16;u=e+(u<<1)|0;u=Ve[u>>1]|0;Ge=c;return u|0}do{s=0;a=-32767;while(1){i=Ve[u+(s<<1)>>1]|0;o=i<<16>>16<a<<16>>16;t=o?t:s&65535;s=s+1|0;if((s&65535)<<16>>16==r<<16>>16)break;else a=o?a:i}Ve[u+(t<<16>>16<<1)>>1]=-32768;Ve[f+(n<<1)>>1]=t;n=n+1|0}while((n&65535)<<16>>16!=r<<16>>16);u=l>>1;u=f+(u<<1)|0;u=Ve[u>>1]|0;u=u<<16>>16;u=e+(u<<1)|0;u=Ve[u>>1]|0;Ge=c;return u|0}function ni(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0;o=Ge;Ge=Ge+32|0;a=o;M=r+2|0;F=a+2|0;Ve[a>>1]=((Ve[r>>1]|0)>>>1)+((Ve[e>>1]|0)>>>1);k=r+4|0;_=a+4|0;Ve[F>>1]=((Ve[M>>1]|0)>>>1)+((Ve[e+2>>1]|0)>>>1);v=r+6|0;b=a+6|0;Ve[_>>1]=((Ve[k>>1]|0)>>>1)+((Ve[e+4>>1]|0)>>>1);S=r+8|0;p=a+8|0;Ve[b>>1]=((Ve[v>>1]|0)>>>1)+((Ve[e+6>>1]|0)>>>1);E=r+10|0;m=a+10|0;Ve[p>>1]=((Ve[S>>1]|0)>>>1)+((Ve[e+8>>1]|0)>>>1);w=r+12|0;h=a+12|0;Ve[m>>1]=((Ve[E>>1]|0)>>>1)+((Ve[e+10>>1]|0)>>>1);d=r+14|0;c=a+14|0;Ve[h>>1]=((Ve[w>>1]|0)>>>1)+((Ve[e+12>>1]|0)>>>1);u=r+16|0;f=a+16|0;Ve[c>>1]=((Ve[d>>1]|0)>>>1)+((Ve[e+14>>1]|0)>>>1);l=r+18|0;s=a+18|0;Ve[f>>1]=((Ve[u>>1]|0)>>>1)+((Ve[e+16>>1]|0)>>>1);Ve[s>>1]=((Ve[l>>1]|0)>>>1)+((Ve[e+18>>1]|0)>>>1);ci(a,t,i);ci(r,t+22|0,i);Ve[a>>1]=((Ve[n>>1]|0)>>>1)+((Ve[r>>1]|0)>>>1);Ve[F>>1]=((Ve[n+2>>1]|0)>>>1)+((Ve[M>>1]|0)>>>1);Ve[_>>1]=((Ve[n+4>>1]|0)>>>1)+((Ve[k>>1]|0)>>>1);Ve[b>>1]=((Ve[n+6>>1]|0)>>>1)+((Ve[v>>1]|0)>>>1);Ve[p>>1]=((Ve[n+8>>1]|0)>>>1)+((Ve[S>>1]|0)>>>1);Ve[m>>1]=((Ve[n+10>>1]|0)>>>1)+((Ve[E>>1]|0)>>>1);Ve[h>>1]=((Ve[n+12>>1]|0)>>>1)+((Ve[w>>1]|0)>>>1);Ve[c>>1]=((Ve[n+14>>1]|0)>>>1)+((Ve[d>>1]|0)>>>1);Ve[f>>1]=((Ve[n+16>>1]|0)>>>1)+((Ve[u>>1]|0)>>>1);Ve[s>>1]=((Ve[n+18>>1]|0)>>>1)+((Ve[l>>1]|0)>>>1);ci(a,t+44|0,i);ci(n,t+66|0,i);Ge=o;return}function ti(e,r,n,t,i){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;var o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0;o=Ge;Ge=Ge+32|0;a=o;M=r+2|0;F=a+2|0;Ve[a>>1]=((Ve[r>>1]|0)>>>1)+((Ve[e>>1]|0)>>>1);k=r+4|0;_=a+4|0;Ve[F>>1]=((Ve[M>>1]|0)>>>1)+((Ve[e+2>>1]|0)>>>1);v=r+6|0;b=a+6|0;Ve[_>>1]=((Ve[k>>1]|0)>>>1)+((Ve[e+4>>1]|0)>>>1);S=r+8|0;p=a+8|0;Ve[b>>1]=((Ve[v>>1]|0)>>>1)+((Ve[e+6>>1]|0)>>>1);E=r+10|0;m=a+10|0;Ve[p>>1]=((Ve[S>>1]|0)>>>1)+((Ve[e+8>>1]|0)>>>1);w=r+12|0;h=a+12|0;Ve[m>>1]=((Ve[E>>1]|0)>>>1)+((Ve[e+10>>1]|0)>>>1);d=r+14|0;c=a+14|0;Ve[h>>1]=((Ve[w>>1]|0)>>>1)+((Ve[e+12>>1]|0)>>>1);u=r+16|0;f=a+16|0;Ve[c>>1]=((Ve[d>>1]|0)>>>1)+((Ve[e+14>>1]|0)>>>1);l=r+18|0;s=a+18|0;Ve[f>>1]=((Ve[u>>1]|0)>>>1)+((Ve[e+16>>1]|0)>>>1);Ve[s>>1]=((Ve[l>>1]|0)>>>1)+((Ve[e+18>>1]|0)>>>1);ci(a,t,i);Ve[a>>1]=((Ve[n>>1]|0)>>>1)+((Ve[r>>1]|0)>>>1);Ve[F>>1]=((Ve[n+2>>1]|0)>>>1)+((Ve[M>>1]|0)>>>1);Ve[_>>1]=((Ve[n+4>>1]|0)>>>1)+((Ve[k>>1]|0)>>>1);Ve[b>>1]=((Ve[n+6>>1]|0)>>>1)+((Ve[v>>1]|0)>>>1);Ve[p>>1]=((Ve[n+8>>1]|0)>>>1)+((Ve[S>>1]|0)>>>1);Ve[m>>1]=((Ve[n+10>>1]|0)>>>1)+((Ve[E>>1]|0)>>>1);Ve[h>>1]=((Ve[n+12>>1]|0)>>>1)+((Ve[w>>1]|0)>>>1);Ve[c>>1]=((Ve[n+14>>1]|0)>>>1)+((Ve[d>>1]|0)>>>1);Ve[f>>1]=((Ve[n+16>>1]|0)>>>1)+((Ve[u>>1]|0)>>>1);Ve[s>>1]=((Ve[n+18>>1]|0)>>>1)+((Ve[l>>1]|0)>>>1);ci(a,t+44|0,i);Ge=o;return}function ii(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0;i=Ge;Ge=Ge+32|0;o=i;D=Ve[e>>1]|0;Ve[o>>1]=D-(D>>>2)+((Ve[r>>1]|0)>>>2);D=e+2|0;y=Ve[D>>1]|0;N=r+2|0;T=o+2|0;Ve[T>>1]=y-(y>>>2)+((Ve[N>>1]|0)>>>2);y=e+4|0;g=Ve[y>>1]|0;O=r+4|0;A=o+4|0;Ve[A>>1]=g-(g>>>2)+((Ve[O>>1]|0)>>>2);g=e+6|0;k=Ve[g>>1]|0;R=r+6|0;M=o+6|0;Ve[M>>1]=k-(k>>>2)+((Ve[R>>1]|0)>>>2);k=e+8|0;b=Ve[k>>1]|0;F=r+8|0;_=o+8|0;Ve[_>>1]=b-(b>>>2)+((Ve[F>>1]|0)>>>2);b=e+10|0;E=Ve[b>>1]|0;v=r+10|0;S=o+10|0;Ve[S>>1]=E-(E>>>2)+((Ve[v>>1]|0)>>>2);E=e+12|0;h=Ve[E>>1]|0;p=r+12|0;m=o+12|0;Ve[m>>1]=h-(h>>>2)+((Ve[p>>1]|0)>>>2);h=e+14|0;u=Ve[h>>1]|0;w=r+14|0;d=o+14|0;Ve[d>>1]=u-(u>>>2)+((Ve[w>>1]|0)>>>2);u=e+16|0;s=Ve[u>>1]|0;c=r+16|0;f=o+16|0;Ve[f>>1]=s-(s>>>2)+((Ve[c>>1]|0)>>>2);s=e+18|0;P=Ve[s>>1]|0;l=r+18|0;a=o+18|0;Ve[a>>1]=P-(P>>>2)+((Ve[l>>1]|0)>>>2);ci(o,n,t);Ve[o>>1]=((Ve[e>>1]|0)>>>1)+((Ve[r>>1]|0)>>>1);Ve[T>>1]=((Ve[D>>1]|0)>>>1)+((Ve[N>>1]|0)>>>1);Ve[A>>1]=((Ve[y>>1]|0)>>>1)+((Ve[O>>1]|0)>>>1);Ve[M>>1]=((Ve[g>>1]|0)>>>1)+((Ve[R>>1]|0)>>>1);Ve[_>>1]=((Ve[k>>1]|0)>>>1)+((Ve[F>>1]|0)>>>1);Ve[S>>1]=((Ve[b>>1]|0)>>>1)+((Ve[v>>1]|0)>>>1);Ve[m>>1]=((Ve[E>>1]|0)>>>1)+((Ve[p>>1]|0)>>>1);Ve[d>>1]=((Ve[h>>1]|0)>>>1)+((Ve[w>>1]|0)>>>1);Ve[f>>1]=((Ve[u>>1]|0)>>>1)+((Ve[c>>1]|0)>>>1);Ve[a>>1]=((Ve[s>>1]|0)>>>1)+((Ve[l>>1]|0)>>>1);ci(o,n+22|0,t);P=Ve[r>>1]|0;Ve[o>>1]=P-(P>>>2)+((Ve[e>>1]|0)>>>2);e=Ve[N>>1]|0;Ve[T>>1]=e-(e>>>2)+((Ve[D>>1]|0)>>>2);e=Ve[O>>1]|0;Ve[A>>1]=e-(e>>>2)+((Ve[y>>1]|0)>>>2);e=Ve[R>>1]|0;Ve[M>>1]=e-(e>>>2)+((Ve[g>>1]|0)>>>2);e=Ve[F>>1]|0;Ve[_>>1]=e-(e>>>2)+((Ve[k>>1]|0)>>>2);e=Ve[v>>1]|0;Ve[S>>1]=e-(e>>>2)+((Ve[b>>1]|0)>>>2);e=Ve[p>>1]|0;Ve[m>>1]=e-(e>>>2)+((Ve[E>>1]|0)>>>2);e=Ve[w>>1]|0;Ve[d>>1]=e-(e>>>2)+((Ve[h>>1]|0)>>>2);e=Ve[c>>1]|0;Ve[f>>1]=e-(e>>>2)+((Ve[u>>1]|0)>>>2);e=Ve[l>>1]|0;Ve[a>>1]=e-(e>>>2)+((Ve[s>>1]|0)>>>2);ci(o,n+44|0,t);ci(r,n+66|0,t);Ge=i;return}function oi(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0;i=Ge;Ge=Ge+32|0;o=i;D=Ve[e>>1]|0;Ve[o>>1]=D-(D>>>2)+((Ve[r>>1]|0)>>>2);D=e+2|0;y=Ve[D>>1]|0;N=r+2|0;T=o+2|0;Ve[T>>1]=y-(y>>>2)+((Ve[N>>1]|0)>>>2);y=e+4|0;g=Ve[y>>1]|0;O=r+4|0;A=o+4|0;Ve[A>>1]=g-(g>>>2)+((Ve[O>>1]|0)>>>2);g=e+6|0;k=Ve[g>>1]|0;R=r+6|0;M=o+6|0;Ve[M>>1]=k-(k>>>2)+((Ve[R>>1]|0)>>>2);k=e+8|0;b=Ve[k>>1]|0;F=r+8|0;_=o+8|0;Ve[_>>1]=b-(b>>>2)+((Ve[F>>1]|0)>>>2);b=e+10|0;E=Ve[b>>1]|0;v=r+10|0;S=o+10|0;Ve[S>>1]=E-(E>>>2)+((Ve[v>>1]|0)>>>2);E=e+12|0;h=Ve[E>>1]|0;p=r+12|0;m=o+12|0;Ve[m>>1]=h-(h>>>2)+((Ve[p>>1]|0)>>>2);h=e+14|0;u=Ve[h>>1]|0;w=r+14|0;d=o+14|0;Ve[d>>1]=u-(u>>>2)+((Ve[w>>1]|0)>>>2);u=e+16|0;s=Ve[u>>1]|0;c=r+16|0;f=o+16|0;Ve[f>>1]=s-(s>>>2)+((Ve[c>>1]|0)>>>2);s=e+18|0;P=Ve[s>>1]|0;l=r+18|0;a=o+18|0;Ve[a>>1]=P-(P>>>2)+((Ve[l>>1]|0)>>>2);ci(o,n,t);Ve[o>>1]=((Ve[e>>1]|0)>>>1)+((Ve[r>>1]|0)>>>1);Ve[T>>1]=((Ve[D>>1]|0)>>>1)+((Ve[N>>1]|0)>>>1);Ve[A>>1]=((Ve[y>>1]|0)>>>1)+((Ve[O>>1]|0)>>>1);Ve[M>>1]=((Ve[g>>1]|0)>>>1)+((Ve[R>>1]|0)>>>1);Ve[_>>1]=((Ve[k>>1]|0)>>>1)+((Ve[F>>1]|0)>>>1);Ve[S>>1]=((Ve[b>>1]|0)>>>1)+((Ve[v>>1]|0)>>>1);Ve[m>>1]=((Ve[E>>1]|0)>>>1)+((Ve[p>>1]|0)>>>1);Ve[d>>1]=((Ve[h>>1]|0)>>>1)+((Ve[w>>1]|0)>>>1);Ve[f>>1]=((Ve[u>>1]|0)>>>1)+((Ve[c>>1]|0)>>>1);Ve[a>>1]=((Ve[s>>1]|0)>>>1)+((Ve[l>>1]|0)>>>1);ci(o,n+22|0,t);r=Ve[r>>1]|0;Ve[o>>1]=r-(r>>>2)+((Ve[e>>1]|0)>>>2);e=Ve[N>>1]|0;Ve[T>>1]=e-(e>>>2)+((Ve[D>>1]|0)>>>2);e=Ve[O>>1]|0;Ve[A>>1]=e-(e>>>2)+((Ve[y>>1]|0)>>>2);e=Ve[R>>1]|0;Ve[M>>1]=e-(e>>>2)+((Ve[g>>1]|0)>>>2);e=Ve[F>>1]|0;Ve[_>>1]=e-(e>>>2)+((Ve[k>>1]|0)>>>2);e=Ve[v>>1]|0;Ve[S>>1]=e-(e>>>2)+((Ve[b>>1]|0)>>>2);e=Ve[p>>1]|0;Ve[m>>1]=e-(e>>>2)+((Ve[E>>1]|0)>>>2);e=Ve[w>>1]|0;Ve[d>>1]=e-(e>>>2)+((Ve[h>>1]|0)>>>2);e=Ve[c>>1]|0;Ve[f>>1]=e-(e>>>2)+((Ve[u>>1]|0)>>>2);e=Ve[l>>1]|0;Ve[a>>1]=e-(e>>>2)+((Ve[s>>1]|0)>>>2);ci(o,n+44|0,t);Ge=i;return}function ai(e,r){e=e|0;r=r|0;var n=0,t=0;if((e|0)<1){r=1073741823;return r|0}n=(bi(e)|0)<<16>>16;r=30-n|0;e=e<<n>>(r&1^1);n=(e>>25<<16)+-1048576>>16;t=Ve[7030+(n<<1)>>1]|0;r=(t<<16)-(Ze(t-(Xe[7030+(n+1<<1)>>1]|0)<<16>>15,e>>>10&32767)|0)>>(r<<16>>17)+1;return r|0}function si(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;t=bi(e)|0;li(e<<(t<<16>>16),t,r,n);return}function li(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;if((e|0)<1){Ve[n>>1]=0;n=0;Ve[t>>1]=n;return}else{Ve[n>>1]=30-(r&65535);n=(e>>25<<16)+-2097152>>16;r=Ve[7128+(n<<1)>>1]|0;n=((r<<16)-(Ze(e>>>9&65534,r-(Xe[7128+(n+1<<1)>>1]|0)<<16>>16)|0)|0)>>>16&65535;Ve[t>>1]=n;return}}function fi(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0;t=e+2|0;n=Ve[t>>1]|0;Ve[r>>1]=n;i=e+4|0;Ve[r+2>>1]=(Xe[i>>1]|0)-(Xe[e>>1]|0);Ve[r+4>>1]=(Xe[e+6>>1]|0)-(Xe[t>>1]|0);t=e+8|0;Ve[r+6>>1]=(Xe[t>>1]|0)-(Xe[i>>1]|0);Ve[r+8>>1]=(Xe[e+10>>1]|0)-(Xe[e+6>>1]|0);i=e+12|0;Ve[r+10>>1]=(Xe[i>>1]|0)-(Xe[t>>1]|0);Ve[r+12>>1]=(Xe[e+14>>1]|0)-(Xe[e+10>>1]|0);Ve[r+14>>1]=(Xe[e+16>>1]|0)-(Xe[i>>1]|0);Ve[r+16>>1]=(Xe[e+18>>1]|0)-(Xe[e+14>>1]|0);Ve[r+18>>1]=16384-(Xe[e+16>>1]|0);e=10;i=r;while(1){n=n<<16>>16;r=(n<<16)+-120782848|0;if((r|0)>0)r=1843-((r>>16)*12484>>16)|0;else r=3427-((n*56320|0)>>>16)|0;t=i+2|0;Ve[i>>1]=r<<3;e=e+-1<<16>>16;if(!(e<<16>>16))break;n=Ve[t>>1]|0;i=t}return}function ui(e,r,n){e=e|0;r=r|0;n=n|0;n=r<<16>>16;if(r<<16>>16>31){r=0;return r|0}if(r<<16>>16>0)return((1<<n+-1&e|0)!=0&1)+(r<<16>>16<31?e>>n:0)|0;n=0-n<<16>>16;r=e<<n;r=(r>>n|0)==(e|0)?r:e>>31^2147483647;return r|0}function ci(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0;m=Ge;Ge=Ge+48|0;h=m+24|0;w=m;c=h+4|0;We[h>>2]=16777216;t=0-(Ve[e>>1]|0)|0;d=h+8|0;We[c>>2]=t<<10;i=Ve[e+4>>1]|0;l=t>>6;We[d>>2]=33554432-(((Ze((t<<9)-(l<<15)<<16>>16,i)|0)>>15)+(Ze(l,i)|0)<<2);l=h+4|0;i=(We[l>>2]|0)-(i<<10)|0;We[l>>2]=i;l=h+12|0;t=h+4|0;We[l>>2]=i;n=Ve[e+8>>1]|0;o=i;f=1;while(1){s=l+-4|0;a=We[s>>2]|0;u=a>>16;We[l>>2]=o+i-(((Ze((a>>>1)-(u<<15)<<16>>16,n)|0)>>15)+(Ze(u,n)|0)<<2);if((f|0)==2)break;o=We[l+-12>>2]|0;l=s;i=a;f=f+1|0}We[t>>2]=(We[t>>2]|0)-(n<<10);n=h+16|0;t=We[h+8>>2]|0;We[n>>2]=t;s=Ve[e+12>>1]|0;i=t;l=1;while(1){a=n+-4|0;o=We[a>>2]|0;u=o>>16;We[n>>2]=i+t-(((Ze((o>>>1)-(u<<15)<<16>>16,s)|0)>>15)+(Ze(u,s)|0)<<2);if((l|0)==3)break;i=We[n+-12>>2]|0;n=a;t=o;l=l+1|0}n=h+4|0;We[n>>2]=(We[n>>2]|0)-(s<<10);n=h+20|0;i=We[h+12>>2]|0;We[n>>2]=i;t=Ve[e+16>>1]|0;o=i;l=1;while(1){s=n+-4|0;a=We[s>>2]|0;u=a>>16;We[n>>2]=o+i-(((Ze((a>>>1)-(u<<15)<<16>>16,t)|0)>>15)+(Ze(u,t)|0)<<2);if((l|0)==4)break;o=We[n+-12>>2]|0;n=s;i=a;l=l+1|0}l=h+4|0;We[l>>2]=(We[l>>2]|0)-(t<<10);We[w>>2]=16777216;l=0-(Ve[e+2>>1]|0)|0;u=w+8|0;We[w+4>>2]=l<<10;t=Ve[e+6>>1]|0;f=l>>6;We[u>>2]=33554432-(((Ze((l<<9)-(f<<15)<<16>>16,t)|0)>>15)+(Ze(f,t)|0)<<2);f=w+4|0;t=(We[f>>2]|0)-(t<<10)|0;We[f>>2]=t;f=w+12|0;l=w+4|0;We[f>>2]=t;s=Ve[e+10>>1]|0;i=t;n=1;while(1){a=f+-4|0;o=We[a>>2]|0;E=o>>16;We[f>>2]=i+t-(((Ze((o>>>1)-(E<<15)<<16>>16,s)|0)>>15)+(Ze(E,s)|0)<<2);if((n|0)==2)break;i=We[f+-12>>2]|0;f=a;t=o;n=n+1|0}We[l>>2]=(We[l>>2]|0)-(s<<10);l=w+16|0;t=We[w+8>>2]|0;We[l>>2]=t;s=Ve[e+14>>1]|0;i=t;n=1;while(1){a=l+-4|0;o=We[a>>2]|0;E=o>>16;We[l>>2]=i+t-(((Ze((o>>>1)-(E<<15)<<16>>16,s)|0)>>15)+(Ze(E,s)|0)<<2);if((n|0)==3)break;i=We[l+-12>>2]|0;l=a;t=o;n=n+1|0}n=w+4|0;We[n>>2]=(We[n>>2]|0)-(s<<10);n=w+20|0;s=We[w+12>>2]|0;We[n>>2]=s;t=Ve[e+18>>1]|0;a=s;l=1;while(1){i=n+-4|0;o=We[i>>2]|0;E=o>>16;We[n>>2]=a+s-(((Ze((o>>>1)-(E<<15)<<16>>16,t)|0)>>15)+(Ze(E,t)|0)<<2);if((l|0)==4)break;a=We[n+-12>>2]|0;n=i;s=o;l=l+1|0}a=(We[w+4>>2]|0)-(t<<10)|0;f=h+20|0;s=w+20|0;l=We[h+16>>2]|0;e=(We[f>>2]|0)+l|0;We[f>>2]=e;f=We[w+16>>2]|0;E=(We[s>>2]|0)-f|0;We[s>>2]=E;s=We[h+12>>2]|0;l=l+s|0;We[h+16>>2]=l;o=We[w+12>>2]|0;f=f-o|0;We[w+16>>2]=f;t=We[d>>2]|0;s=s+t|0;We[h+12>>2]=s;i=We[u>>2]|0;d=o-i|0;We[w+12>>2]=d;o=We[c>>2]|0;u=t+o|0;We[h+8>>2]=u;c=i-a|0;We[w+8>>2]=c;h=o+(We[h>>2]|0)|0;w=a-(We[w>>2]|0)|0;Ve[r>>1]=4096;h=h+4096|0;Ve[r+2>>1]=(h+w|0)>>>13;Ve[r+20>>1]=(h-w|0)>>>13;w=u+4096|0;Ve[r+4>>1]=(w+c|0)>>>13;Ve[r+18>>1]=(w-c|0)>>>13;w=s+4096|0;Ve[r+6>>1]=(w+d|0)>>>13;Ve[r+16>>1]=(w-d|0)>>>13;w=l+4096|0;Ve[r+8>>1]=(w+f|0)>>>13;Ve[r+14>>1]=(w-f|0)>>>13;w=e+4096|0;Ve[r+10>>1]=(w+E|0)>>>13;Ve[r+12>>1]=(w-E|0)>>>13;Ge=m;return}function di(e){e=e|0;var r=0,n=0,t=0,i=0,o=0;if(!e){o=-1;return o|0}We[e>>2]=0;r=xi(44)|0;if(!r){o=-1;return o|0}n=r+40|0;if((Ai(n)|0)<<16>>16){o=-1;return o|0}t=r;i=7452;o=t+20|0;do{Ve[t>>1]=Ve[i>>1]|0;t=t+2|0;i=i+2|0}while((t|0)<(o|0));t=r+20|0;i=7452;o=t+20|0;do{Ve[t>>1]=Ve[i>>1]|0;t=t+2|0;i=i+2|0}while((t|0)<(o|0));yi(We[n>>2]|0)|0;We[e>>2]=r;o=0;return o|0}function hi(e){e=e|0;var r=0,n=0,t=0;if(!e){t=-1;return t|0}r=e;n=7452;t=r+20|0;do{Ve[r>>1]=Ve[n>>1]|0;r=r+2|0;n=n+2|0}while((r|0)<(t|0));r=e+20|0;n=7452;t=r+20|0;do{Ve[r>>1]=Ve[n>>1]|0;r=r+2|0;n=n+2|0}while((r|0)<(t|0));yi(We[e+40>>2]|0)|0;t=0;return t|0}function wi(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Oi(r+40|0);Hi(We[e>>2]|0);We[e>>2]=0;return}function mi(e,r,n,t,i,o,a,s){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;var l=0,f=0,u=0,c=0,d=0;d=Ge;Ge=Ge+64|0;c=d+44|0;l=d+24|0;f=d+4|0;u=d;if((r|0)==7){Xt(t+22|0,l,e,s);Xt(t+66|0,o,l,s);ti(e,l,o,t,s);if((n|0)==8)t=6;else{gi(We[e+40>>2]|0,l,o,f,c,We[a>>2]|0,s);ni(e+20|0,f,c,i,s);i=(We[a>>2]|0)+10|0;t=7}}else{Xt(t+66|0,o,e,s);oi(e,o,t,s);if((n|0)==8)t=6;else{Fi(We[e+40>>2]|0,r,o,c,We[a>>2]|0,u,s);ii(e+20|0,c,i,s);i=(We[a>>2]|0)+6|0;t=7}}if((t|0)==6){t=e;i=t+20|0;do{Ve[t>>1]=Ve[o>>1]|0;t=t+2|0;o=o+2|0}while((t|0)<(i|0));Ge=d;return}else if((t|0)==7){We[a>>2]=i;t=e;i=t+20|0;do{Ve[t>>1]=Ve[o>>1]|0;t=t+2|0;o=o+2|0}while((t|0)<(i|0));t=e+20|0;o=c;i=t+20|0;do{Ve[t>>1]=Ve[o>>1]|0;t=t+2|0;o=o+2|0}while((t|0)<(i|0));Ge=d;return}}function Ei(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0;if(n<<16>>16>0)t=0;else return;do{o=Ve[e+(t<<1)>>1]|0;a=o>>8;i=Ve[7194+(a<<1)>>1]|0;Ve[r+(t<<1)>>1]=((Ze((Ve[7194+(a+1<<1)>>1]|0)-i|0,o&255)|0)>>>8)+i;t=t+1|0}while((t&65535)<<16>>16!=n<<16>>16);return}function pi(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0;t=(n<<16>>16)+-1|0;n=t&65535;if(n<<16>>16<=-1)return;i=63;a=r+(t<<1)|0;o=e+(t<<1)|0;while(1){e=Ve[o>>1]|0;r=i;while(1){t=r<<16>>16;i=Ve[7194+(t<<1)>>1]|0;if(e<<16>>16>i<<16>>16)r=r+-1<<16>>16;else break}Ve[a>>1]=(((Ze(Ve[7324+(t<<1)>>1]|0,(e<<16>>16)-(i<<16>>16)|0)|0)+2048|0)>>>12)+(t<<8);n=n+-1<<16>>16;if(n<<16>>16>-1){i=r;a=a+-2|0;o=o+-2|0}else break}return}function Si(e,r,n){e=e|0;r=r|0;n=n|0;e=(Ze(r<<16>>16,e<<16>>16)|0)+16384>>15;e=e|0-(e&65536);if((e|0)<=32767){if((e|0)<-32768){We[n>>2]=1;e=-32768}}else{We[n>>2]=1;e=32767}return e&65535|0}function bi(e){e=e|0;var r=0;e:do{if((e|0)!=0?(r=e-(e>>>31)|0,r=r>>31^r,(r&1073741824|0)==0):0){e=r;r=0;while(1){if(e&536870912){e=7;break}if(e&268435456){e=8;break}if(e&134217728){e=9;break}r=r+4<<16>>16;e=e<<4;if(e&1073741824)break e}if((e|0)==7){r=r|1;break}else if((e|0)==8){r=r|2;break}else if((e|0)==9){r=r|3;break}}else r=0}while(0);return r|0}function vi(e){e=e|0;var r=0,n=0;if(!(e<<16>>16)){n=0;return n|0}r=(e&65535)-((e&65535)>>>15&65535)|0;r=(r<<16>>31^r)<<16;e=r>>16;if(!(e&16384)){n=r;r=0}else{n=0;return n|0}while(1){if(e&8192){e=r;n=7;break}if(e&4096){e=r;n=8;break}if(e&2048){e=r;n=9;break}r=r+4<<16>>16;n=n<<4;e=n>>16;if(e&16384){e=r;n=10;break}}if((n|0)==7){n=e|1;return n|0}else if((n|0)==8){n=e|2;return n|0}else if((n|0)==9){n=e|3;return n|0}else if((n|0)==10)return e|0;return 0}function _i(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0;r=r<<16>>16;if((r&134217727|0)==33554432){We[n>>2]=1;r=2147483647}else r=r<<6;t=r>>>16&31;o=Ve[7792+(t<<1)>>1]|0;i=o<<16;r=Ze(o-(Xe[7792+(t+1<<1)>>1]|0)<<16>>16,r>>>1&32767)|0;if((r|0)==1073741824){We[n>>2]=1;t=2147483647}else t=r<<1;r=i-t|0;if(((r^i)&(t^i)|0)>=0){o=r;e=e&65535;e=30-e|0;e=e&65535;n=ui(o,e,n)|0;return n|0}We[n>>2]=1;o=(o>>>15&1)+2147483647|0;e=e&65535;e=30-e|0;e=e&65535;n=ui(o,e,n)|0;return n|0}function ki(e,r,n,t,i,o){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;var a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0;d=Ge;Ge=Ge+48|0;c=d;u=0-(n&65535)|0;u=i<<16>>16==0?u:u<<1&131070;n=u&65535;u=(n<<16>>16<0?u+6|0:u)<<16>>16;o=6-u|0;Ve[c>>1]=Ve[7858+(u<<1)>>1]|0;Ve[c+2>>1]=Ve[7858+(o<<1)>>1]|0;Ve[c+4>>1]=Ve[7858+(u+6<<1)>>1]|0;Ve[c+6>>1]=Ve[7858+(o+6<<1)>>1]|0;Ve[c+8>>1]=Ve[7858+(u+12<<1)>>1]|0;Ve[c+10>>1]=Ve[7858+(o+12<<1)>>1]|0;Ve[c+12>>1]=Ve[7858+(u+18<<1)>>1]|0;Ve[c+14>>1]=Ve[7858+(o+18<<1)>>1]|0;Ve[c+16>>1]=Ve[7858+(u+24<<1)>>1]|0;Ve[c+18>>1]=Ve[7858+(o+24<<1)>>1]|0;Ve[c+20>>1]=Ve[7858+(u+30<<1)>>1]|0;Ve[c+22>>1]=Ve[7858+(o+30<<1)>>1]|0;Ve[c+24>>1]=Ve[7858+(u+36<<1)>>1]|0;Ve[c+26>>1]=Ve[7858+(o+36<<1)>>1]|0;Ve[c+28>>1]=Ve[7858+(u+42<<1)>>1]|0;Ve[c+30>>1]=Ve[7858+(o+42<<1)>>1]|0;Ve[c+32>>1]=Ve[7858+(u+48<<1)>>1]|0;Ve[c+34>>1]=Ve[7858+(o+48<<1)>>1]|0;Ve[c+36>>1]=Ve[7858+(u+54<<1)>>1]|0;Ve[c+38>>1]=Ve[7858+(o+54<<1)>>1]|0;o=t<<16>>16>>>1&65535;if(!(o<<16>>16)){Ge=d;return}u=e+((n<<16>>16>>15<<16>>16)-(r<<16>>16)<<1)|0;while(1){f=u+2|0;a=Ve[f>>1]|0;r=a;t=u;s=5;l=c;i=16384;n=16384;while(1){w=Ve[l>>1]|0;m=(Ze(w,r<<16>>16)|0)+n|0;h=Ve[f+-2>>1]|0;n=(Ze(h,w)|0)+i|0;w=t;t=t+4|0;E=Ve[l+2>>1]|0;n=n+(Ze(E,a<<16>>16)|0)|0;i=Ve[t>>1]|0;E=m+(Ze(i,E)|0)|0;f=f+-4|0;m=Ve[l+4>>1]|0;h=E+(Ze(m,h)|0)|0;r=Ve[f>>1]|0;m=n+(Ze(r<<16>>16,m)|0)|0;n=Ve[l+6>>1]|0;i=m+(Ze(n,i)|0)|0;a=Ve[w+6>>1]|0;n=h+(Ze(a<<16>>16,n)|0)|0;if(s<<16>>16<=1)break;else{s=s+-1<<16>>16;l=l+8|0}}Ve[e>>1]=i>>>15;Ve[e+2>>1]=n>>>15;o=o+-1<<16>>16;if(!(o<<16>>16))break;else{u=u+4|0;e=e+4|0}}Ge=d;return}function Fi(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;var s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0;g=Ge;Ge=Ge+144|0;E=g+120|0;_=g+100|0;F=g+80|0;M=g+60|0;k=g+40|0;h=g+20|0;w=g;pi(n,E,10,a);fi(E,_,a);if((r|0)==8){Ve[o>>1]=0;l=2147483647;m=0;while(1){u=m*10|0;n=0;f=0;do{v=(Xe[7980+(f+u<<1)>>1]|0)+(Xe[8140+(f<<1)>>1]|0)|0;Ve[w+(f<<1)>>1]=v;v=(Xe[E+(f<<1)>>1]|0)-(v&65535)|0;Ve[h+(f<<1)>>1]=v;v=v<<16;n=(Ze(v>>15,v>>16)|0)+n|0;f=f+1|0}while((f|0)!=10);if((n|0)<(l|0)){p=M;d=h;c=p+20|0;do{Ve[p>>1]=Ve[d>>1]|0;p=p+2|0;d=d+2|0}while((p|0)<(c|0));p=F;d=w;c=p+20|0;do{Ve[p>>1]=Ve[d>>1]|0;p=p+2|0;d=d+2|0}while((p|0)<(c|0));p=e;d=7980+(u<<1)|0;c=p+20|0;do{Ve[p>>1]=Ve[d>>1]|0;p=p+2|0;d=d+2|0}while((p|0)<(c|0));Ve[o>>1]=m}else n=l;m=m+1|0;if((m|0)==8)break;else l=n}}else{n=0;do{v=Ze(Ve[8160+(n<<1)>>1]|0,Ve[e+(n<<1)>>1]|0)|0;v=(v>>>15)+(Xe[8140+(n<<1)>>1]|0)|0;Ve[F+(n<<1)>>1]=v;Ve[M+(n<<1)>>1]=(Xe[E+(n<<1)>>1]|0)-v;n=n+1|0}while((n|0)!=10)}do{if(r>>>0>=2){v=M+2|0;b=M+4|0;S=Xe[M>>1]|0;p=Ve[_>>1]<<1;E=Xe[v>>1]|0;h=Ve[_+2>>1]<<1;d=Xe[b>>1]|0;c=Ve[_+4>>1]<<1;if((r|0)==5){w=2147483647;o=0;n=0;m=17908;while(1){f=(Ze(S-(Xe[m>>1]|0)<<16>>16,p)|0)>>16;f=Ze(f,f)|0;u=(Ze(E-(Xe[m+2>>1]|0)<<16>>16,h)|0)>>16;f=(Ze(u,u)|0)+f|0;u=(Ze(d-(Xe[m+4>>1]|0)<<16>>16,c)|0)>>16;u=f+(Ze(u,u)|0)|0;f=(u|0)<(w|0);n=f?o:n;o=o+1<<16>>16;if(o<<16>>16>=512)break;else{w=f?u:w;m=m+6|0}}u=(n<<16>>16)*3|0;Ve[M>>1]=Ve[17908+(u<<1)>>1]|0;Ve[v>>1]=Ve[17908+(u+1<<1)>>1]|0;Ve[b>>1]=Ve[17908+(u+2<<1)>>1]|0;Ve[i>>1]=n;u=M+6|0;f=M+8|0;S=M+10|0;m=Xe[u>>1]|0;o=Ve[_+6>>1]<<1;w=Xe[f>>1]|0;h=Ve[_+8>>1]<<1;d=Xe[S>>1]|0;c=Ve[_+10>>1]<<1;s=2147483647;E=0;n=0;p=9716;while(1){l=(Ze(o,m-(Xe[p>>1]|0)<<16>>16)|0)>>16;l=Ze(l,l)|0;r=(Ze(h,w-(Xe[p+2>>1]|0)<<16>>16)|0)>>16;l=(Ze(r,r)|0)+l|0;r=(Ze(c,d-(Xe[p+4>>1]|0)<<16>>16)|0)>>16;r=l+(Ze(r,r)|0)|0;l=(r|0)<(s|0);n=l?E:n;E=E+1<<16>>16;if(E<<16>>16>=512)break;else{s=l?r:s;p=p+6|0}}s=(n<<16>>16)*3|0;Ve[u>>1]=Ve[9716+(s<<1)>>1]|0;Ve[f>>1]=Ve[9716+(s+1<<1)>>1]|0;Ve[S>>1]=Ve[9716+(s+2<<1)>>1]|0;Ve[i+2>>1]=n;s=M+12|0;Ve[i+4>>1]=Mi(s,12788,_+12|0,512)|0;E=v;m=b;n=S;l=M;break}else{w=2147483647;o=0;n=0;m=8180;while(1){f=(Ze(S-(Xe[m>>1]|0)<<16>>16,p)|0)>>16;f=Ze(f,f)|0;u=(Ze(E-(Xe[m+2>>1]|0)<<16>>16,h)|0)>>16;f=(Ze(u,u)|0)+f|0;u=(Ze(d-(Xe[m+4>>1]|0)<<16>>16,c)|0)>>16;u=f+(Ze(u,u)|0)|0;f=(u|0)<(w|0);n=f?o:n;o=o+1<<16>>16;if(o<<16>>16>=256)break;else{w=f?u:w;m=m+6|0}}u=(n<<16>>16)*3|0;Ve[M>>1]=Ve[8180+(u<<1)>>1]|0;Ve[v>>1]=Ve[8180+(u+1<<1)>>1]|0;Ve[b>>1]=Ve[8180+(u+2<<1)>>1]|0;Ve[i>>1]=n;u=M+6|0;f=M+8|0;S=M+10|0;m=Xe[u>>1]|0;o=Ve[_+6>>1]<<1;w=Xe[f>>1]|0;h=Ve[_+8>>1]<<1;d=Xe[S>>1]|0;c=Ve[_+10>>1]<<1;s=2147483647;E=0;n=0;p=9716;while(1){l=(Ze(o,m-(Xe[p>>1]|0)<<16>>16)|0)>>16;l=Ze(l,l)|0;r=(Ze(h,w-(Xe[p+2>>1]|0)<<16>>16)|0)>>16;l=(Ze(r,r)|0)+l|0;r=(Ze(c,d-(Xe[p+4>>1]|0)<<16>>16)|0)>>16;r=l+(Ze(r,r)|0)|0;l=(r|0)<(s|0);n=l?E:n;E=E+1<<16>>16;if(E<<16>>16>=512)break;else{s=l?r:s;p=p+6|0}}s=(n<<16>>16)*3|0;Ve[u>>1]=Ve[9716+(s<<1)>>1]|0;Ve[f>>1]=Ve[9716+(s+1<<1)>>1]|0;Ve[S>>1]=Ve[9716+(s+2<<1)>>1]|0;Ve[i+2>>1]=n;s=M+12|0;Ve[i+4>>1]=Mi(s,12788,_+12|0,512)|0;E=v;m=b;n=S;l=M;break}}else{b=M+2|0;v=M+4|0;u=Xe[M>>1]|0;f=Ve[_>>1]<<1;l=Xe[b>>1]|0;s=Ve[_+2>>1]<<1;r=Xe[v>>1]|0;c=Ve[_+4>>1]<<1;w=2147483647;o=0;n=0;m=8180;while(1){h=(Ze(f,u-(Xe[m>>1]|0)<<16>>16)|0)>>16;h=Ze(h,h)|0;d=(Ze(s,l-(Xe[m+2>>1]|0)<<16>>16)|0)>>16;h=(Ze(d,d)|0)+h|0;d=(Ze(c,r-(Xe[m+4>>1]|0)<<16>>16)|0)>>16;d=h+(Ze(d,d)|0)|0;h=(d|0)<(w|0);n=h?o:n;o=o+1<<16>>16;if(o<<16>>16>=256)break;else{w=h?d:w;m=m+6|0}}u=(n<<16>>16)*3|0;Ve[M>>1]=Ve[8180+(u<<1)>>1]|0;Ve[b>>1]=Ve[8180+(u+1<<1)>>1]|0;Ve[v>>1]=Ve[8180+(u+2<<1)>>1]|0;Ve[i>>1]=n;u=M+6|0;f=M+8|0;S=M+10|0;m=Xe[u>>1]|0;o=Ve[_+6>>1]<<1;w=Xe[f>>1]|0;h=Ve[_+8>>1]<<1;d=Xe[S>>1]|0;c=Ve[_+10>>1]<<1;s=2147483647;E=0;n=0;p=9716;while(1){l=(Ze(o,m-(Xe[p>>1]|0)<<16>>16)|0)>>16;l=Ze(l,l)|0;r=(Ze(h,w-(Xe[p+2>>1]|0)<<16>>16)|0)>>16;l=(Ze(r,r)|0)+l|0;r=(Ze(c,d-(Xe[p+4>>1]|0)<<16>>16)|0)>>16;r=l+(Ze(r,r)|0)|0;l=(r|0)<(s|0);n=l?E:n;E=E+1<<16>>16;if(E<<16>>16>=256)break;else{s=l?r:s;p=p+12|0}}s=(n<<16>>16)*6|0;Ve[u>>1]=Ve[9716+(s<<1)>>1]|0;Ve[f>>1]=Ve[9716+((s|1)<<1)>>1]|0;Ve[S>>1]=Ve[9716+(s+2<<1)>>1]|0;Ve[i+2>>1]=n;s=M+12|0;Ve[i+4>>1]=Mi(s,16884,_+12|0,128)|0;E=b;m=v;n=S;l=M}}while(0);p=e;d=M;c=p+20|0;do{Ve[p>>1]=Ve[d>>1]|0;p=p+2|0;d=d+2|0}while((p|0)<(c|0));Ve[k>>1]=(Xe[F>>1]|0)+(Xe[l>>1]|0);Ve[k+2>>1]=(Xe[F+2>>1]|0)+(Xe[E>>1]|0);Ve[k+4>>1]=(Xe[F+4>>1]|0)+(Xe[m>>1]|0);Ve[k+6>>1]=(Xe[F+6>>1]|0)+(Xe[u>>1]|0);Ve[k+8>>1]=(Xe[F+8>>1]|0)+(Xe[f>>1]|0);Ve[k+10>>1]=(Xe[F+10>>1]|0)+(Xe[n>>1]|0);Ve[k+12>>1]=(Xe[F+12>>1]|0)+(Xe[s>>1]|0);Ve[k+14>>1]=(Xe[F+14>>1]|0)+(Xe[M+14>>1]|0);Ve[k+16>>1]=(Xe[F+16>>1]|0)+(Xe[M+16>>1]|0);Ve[k+18>>1]=(Xe[F+18>>1]|0)+(Xe[M+18>>1]|0);Ti(k,205,10,a);Ei(k,t,10,a);Ge=g;return}function Mi(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0;p=e+2|0;S=e+4|0;b=e+6|0;if(t<<16>>16>0){u=Xe[e>>1]|0;c=Ve[n>>1]<<1;d=Xe[p>>1]|0;h=Ve[n+2>>1]<<1;w=Xe[S>>1]|0;m=Ve[n+4>>1]<<1;E=Xe[b>>1]|0;i=Ve[n+6>>1]<<1;s=2147483647;l=0;n=0;f=r;while(1){o=(Ze(c,u-(Xe[f>>1]|0)<<16>>16)|0)>>16;o=Ze(o,o)|0;a=(Ze(h,d-(Xe[f+2>>1]|0)<<16>>16)|0)>>16;o=(Ze(a,a)|0)+o|0;a=(Ze(m,w-(Xe[f+4>>1]|0)<<16>>16)|0)>>16;a=o+(Ze(a,a)|0)|0;o=(Ze(i,E-(Xe[f+6>>1]|0)<<16>>16)|0)>>16;o=a+(Ze(o,o)|0)|0;a=(o|0)<(s|0);n=a?l:n;l=l+1<<16>>16;if(l<<16>>16>=t<<16>>16)break;else{s=a?o:s;f=f+8|0}}}else n=0;t=n<<16>>16<<2;E=t|1;Ve[e>>1]=Ve[r+(t<<1)>>1]|0;Ve[p>>1]=Ve[r+(E<<1)>>1]|0;Ve[S>>1]=Ve[r+(E+1<<1)>>1]|0;Ve[b>>1]=Ve[r+((t|3)<<1)>>1]|0;return n|0}function gi(e,r,n,t,i,o,a){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;var s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0;D=Ge;Ge=Ge+192|0;f=D+160|0;l=D+140|0;g=D+120|0;R=D+100|0;A=D+80|0;y=D+60|0;s=D+40|0;O=D+20|0;T=D;pi(r,f,10,a);pi(n,l,10,a);fi(f,g,a);fi(l,R,a);u=0;n=A;r=y;c=s;while(1){M=(((Ve[e+(u<<1)>>1]|0)*21299|0)>>>15)+(Xe[20980+(u<<1)>>1]|0)|0;Ve[n>>1]=M;Ve[r>>1]=(Xe[f>>1]|0)-M;Ve[c>>1]=(Xe[l>>1]|0)-M;u=u+1|0;if((u|0)==10)break;else{f=f+2|0;l=l+2|0;n=n+2|0;r=r+2|0;c=c+2|0}}Ve[o>>1]=Ri(y,s,21e3,Ve[g>>1]|0,Ve[g+2>>1]|0,Ve[R>>1]|0,Ve[R+2>>1]|0,128)|0;Ve[o+2>>1]=Ri(y+4|0,s+4|0,22024,Ve[g+4>>1]|0,Ve[g+6>>1]|0,Ve[R+4>>1]|0,Ve[R+6>>1]|0,256)|0;_=y+8|0;k=s+8|0;F=y+10|0;M=s+10|0;n=Ve[_>>1]|0;d=Ve[g+8>>1]<<1;h=Ve[F>>1]|0;w=Ve[g+10>>1]<<1;m=Ve[k>>1]|0;E=Ve[R+8>>1]<<1;p=Ve[M>>1]|0;S=Ve[R+10>>1]<<1;l=2147483647;b=0;c=0;v=24072;r=0;while(1){f=Ve[v>>1]|0;u=(Ze(n-f<<16>>16,d)|0)>>16;u=Ze(u,u)|0;f=(Ze(f+n<<16>>16,d)|0)>>16;f=Ze(f,f)|0;N=Ve[v+2>>1]|0;P=(Ze(h-N<<16>>16,w)|0)>>16;u=(Ze(P,P)|0)+u|0;N=(Ze(N+h<<16>>16,w)|0)>>16;f=(Ze(N,N)|0)+f|0;if((u|0)<(l|0)|(f|0)<(l|0)){P=Ve[v+4>>1]|0;N=(Ze(m-P<<16>>16,E)|0)>>16;N=(Ze(N,N)|0)+u|0;P=(Ze(P+m<<16>>16,E)|0)>>16;P=(Ze(P,P)|0)+f|0;f=Ve[v+6>>1]|0;u=(Ze(p-f<<16>>16,S)|0)>>16;u=N+(Ze(u,u)|0)|0;f=(Ze(f+p<<16>>16,S)|0)>>16;f=P+(Ze(f,f)|0)|0;P=(u|0)<(l|0);u=P?u:l;N=(f|0)<(u|0);u=N?f:u;c=P|N?b:c;r=N?1:P?0:r}else u=l;b=b+1<<16>>16;if(b<<16>>16>=256)break;else{l=u;v=v+8|0}}u=c<<16>>16;f=u<<2;c=f|1;l=24072+(c<<1)|0;n=Ve[24072+(f<<1)>>1]|0;if(!(r<<16>>16)){Ve[_>>1]=n;Ve[F>>1]=Ve[l>>1]|0;Ve[k>>1]=Ve[24072+(c+1<<1)>>1]|0;Ve[M>>1]=Ve[24072+((f|3)<<1)>>1]|0;r=u<<1}else{Ve[_>>1]=0-(n&65535);Ve[F>>1]=0-(Xe[l>>1]|0);Ve[k>>1]=0-(Xe[24072+(c+1<<1)>>1]|0);Ve[M>>1]=0-(Xe[24072+((f|3)<<1)>>1]|0);r=u<<1&65534|1}Ve[o+4>>1]=r;Ve[o+6>>1]=Ri(y+12|0,s+12|0,26120,Ve[g+12>>1]|0,Ve[g+14>>1]|0,Ve[R+12>>1]|0,Ve[R+14>>1]|0,256)|0;Ve[o+8>>1]=Ri(y+16|0,s+16|0,28168,Ve[g+16>>1]|0,Ve[g+18>>1]|0,Ve[R+16>>1]|0,Ve[R+18>>1]|0,64)|0;l=0;f=O;u=T;n=A;r=y;while(1){N=Xe[n>>1]|0;Ve[f>>1]=N+(Xe[r>>1]|0);P=Ve[s>>1]|0;Ve[u>>1]=N+(P&65535);Ve[e+(l<<1)>>1]=P;l=l+1|0;if((l|0)==10)break;else{f=f+2|0;u=u+2|0;n=n+2|0;r=r+2|0;s=s+2|0}}Ti(O,205,10,a);Ti(T,205,10,a);Ei(O,t,10,a);Ei(T,i,10,a);Ge=D;return}function Ri(e,r,n,t,i,o,a,s){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;a=a|0;s=s|0;var l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0;h=Ve[e>>1]|0;b=e+2|0;m=Ve[b>>1]|0;p=Ve[r>>1]|0;v=r+2|0;S=Ve[v>>1]|0;if(s<<16>>16>0){d=t<<16>>16<<1;c=i<<16>>16<<1;u=o<<16>>16<<1;i=a<<16>>16<<1;o=2147483647;l=0;t=0;f=n;while(1){a=(Ze(d,h-(Ve[f>>1]|0)|0)|0)>>16;a=Ze(a,a)|0;if(((a|0)<(o|0)?(w=(Ze(c,m-(Ve[f+2>>1]|0)|0)|0)>>16,w=(Ze(w,w)|0)+a|0,(w|0)<(o|0)):0)?(E=(Ze(u,p-(Ve[f+4>>1]|0)|0)|0)>>16,E=(Ze(E,E)|0)+w|0,(E|0)<(o|0)):0){a=(Ze(i,S-(Ve[f+6>>1]|0)|0)|0)>>16;a=(Ze(a,a)|0)+E|0;_=(a|0)<(o|0);a=_?a:o;t=_?l:t}else a=o;l=l+1<<16>>16;if(l<<16>>16>=s<<16>>16)break;else{o=a;f=f+8|0}}}else t=0;_=t<<16>>16<<2;s=_|1;Ve[e>>1]=Ve[n+(_<<1)>>1]|0;Ve[b>>1]=Ve[n+(s<<1)>>1]|0;Ve[r>>1]=Ve[n+(s+1<<1)>>1]|0;Ve[v>>1]=Ve[n+((_|3)<<1)>>1]|0;return t|0}function Ai(e){e=e|0;var r=0,n=0,t=0;if(!e){t=-1;return t|0}We[e>>2]=0;r=xi(20)|0;if(!r){t=-1;return t|0}n=r;t=n+20|0;do{Ve[n>>1]=0;n=n+2|0}while((n|0)<(t|0));We[e>>2]=r;t=0;return t|0}function yi(e){e=e|0;var r=0;if(!e){r=-1;return r|0}r=e+20|0;do{Ve[e>>1]=0;e=e+2|0}while((e|0)<(r|0));r=0;return r|0}function Oi(e){e=e|0;var r=0;if(!e)return;r=We[e>>2]|0;if(!r)return;Hi(r);We[e>>2]=0;return}function Ti(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0;if(n<<16>>16<=0)return;i=r<<16>>16;o=r&65535;a=0;while(1){t=Ve[e>>1]|0;if(t<<16>>16<r<<16>>16){Ve[e>>1]=r;t=(r<<16>>16)+i|0}else t=(t&65535)+o|0;a=a+1<<16>>16;if(a<<16>>16>=n<<16>>16)break;else{r=t&65535;e=e+2|0}}return}function Di(e,r,n,t){e=e|0;r=r|0;n=n|0;t=t|0;var i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0;i=t<<16>>16;t=i>>>2&65535;if(!(t<<16>>16))return;d=i+-1|0;S=e+20|0;w=r+(i+-4<<1)|0;m=r+(i+-3<<1)|0;E=r+(i+-2<<1)|0;p=r+(d<<1)|0;h=r+(i+-11<<1)|0;d=n+(d<<1)|0;while(1){r=Ve[S>>1]|0;a=5;s=S;l=h;f=h+-2|0;u=h+-4|0;c=h+-6|0;o=2048;e=2048;i=2048;n=2048;while(1){o=(Ze(Ve[l>>1]|0,r)|0)+o|0;e=(Ze(Ve[f>>1]|0,r)|0)+e|0;i=(Ze(Ve[u>>1]|0,r)|0)+i|0;r=(Ze(Ve[c>>1]|0,r)|0)+n|0;n=Ve[s+-2>>1]|0;o=o+(Ze(Ve[l+2>>1]|0,n)|0)|0;e=e+(Ze(Ve[f+2>>1]|0,n)|0)|0;i=i+(Ze(Ve[u+2>>1]|0,n)|0)|0;s=s+-4|0;n=r+(Ze(Ve[c+2>>1]|0,n)|0)|0;a=a+-1<<16>>16;r=Ve[s>>1]|0;if(!(a<<16>>16))break;else{l=l+4|0;f=f+4|0;u=u+4|0;c=c+4|0}}l=(Ze(Ve[p>>1]|0,r)|0)+o|0;f=(Ze(Ve[E>>1]|0,r)|0)+e|0;u=(Ze(Ve[m>>1]|0,r)|0)+i|0;c=(Ze(Ve[w>>1]|0,r)|0)+n|0;Ve[d>>1]=l>>>12;Ve[d+-2>>1]=f>>>12;Ve[d+-4>>1]=u>>>12;Ve[d+-6>>1]=c>>>12;t=t+-1<<16>>16;if(!(t<<16>>16))break;else{w=w+-8|0;m=m+-8|0;E=E+-8|0;p=p+-8|0;h=h+-8|0;d=d+-8|0}}return}function Ni(e,r){e=e|0;r=r|0;var n=0;n=e+32768|0;if((e|0)>-1&(n^e|0)<0){We[r>>2]=1;n=(e>>>31)+2147483647|0}return n>>>16&65535|0}function Pi(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0;t=r<<16>>16;if(!(r<<16>>16))return e|0;if(r<<16>>16>0){e=e<<16>>16>>(r<<16>>16>15?15:t)&65535;return e|0}i=0-t|0;r=e<<16>>16;i=(i&65535)<<16>>16>15?15:i<<16>>16;t=r<<i;if((t<<16>>16>>i|0)==(r|0)){i=t&65535;return i|0}We[n>>2]=1;i=e<<16>>16>0?32767:-32768;return i|0}function Ci(e,r,n){e=e|0;r=r|0;n=n|0;if(r<<16>>16>15){r=0;return r|0}n=Pi(e,r,n)|0;if(r<<16>>16>0)return n+((1<<(r<<16>>16)+-1&e<<16>>16|0)!=0&1)<<16>>16|0;else{r=n;return r|0}return 0}function Ii(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0;if((e|0)<1){Ve[r>>1]=0;n=0;return n|0}i=(bi(e)|0)&65534;o=i&65535;i=i<<16>>16;if(o<<16>>16>0){t=e<<i;if((t>>i|0)!=(e|0))t=e>>31^2147483647}else{i=0-i<<16;if((i|0)<2031616)t=e>>(i>>16);else t=0}Ve[r>>1]=o;r=t>>>25&63;r=r>>>0>15?r+-16|0:r;o=Ve[30216+(r<<1)>>1]|0;e=o<<16;t=Ze(o-(Xe[30216+(r+1<<1)>>1]|0)<<16>>16,t>>>10&32767)|0;if((t|0)==1073741824){We[n>>2]=1;i=2147483647}else i=t<<1;t=e-i|0;if(((t^e)&(i^e)|0)>=0){n=t;return n|0}We[n>>2]=1;n=(o>>>15&1)+2147483647|0;return n|0}function Bi(e,r,n){e=e|0;r=r|0;n=n|0;e=(e<<16>>16)-(r<<16>>16)|0;if((e+32768|0)>>>0<=65535){n=e;n=n&65535;return n|0}We[n>>2]=1;n=(e|0)>32767?32767:-32768;n=n&65535;return n|0}function Li(e,r,n,t,i,o){e=e|0;r=r|0;n=n|0;t=t|0;i=i|0;o=o|0;var a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0;M=Ge;Ge=Ge+48|0;d=M;l=d;a=i;s=l+20|0;do{Ve[l>>1]=Ve[a>>1]|0;l=l+2|0;a=a+2|0}while((l|0)<(s|0));c=d+18|0;E=e+2|0;p=e+4|0;h=r+20|0;S=e+6|0;b=e+8|0;v=e+10|0;_=e+12|0;k=e+14|0;F=e+16|0;w=e+18|0;m=e+20|0;s=Ve[c>>1]|0;a=5;f=r;u=n;l=d+20|0;while(1){A=Ve[e>>1]|0;R=(Ze(A,Ve[f>>1]|0)|0)+2048|0;A=(Ze(Ve[f+2>>1]|0,A)|0)+2048|0;d=s<<16>>16;R=R-(Ze(d,Ve[E>>1]|0)|0)|0;g=Ve[p>>1]|0;d=A-(Ze(d,g)|0)|0;A=Ve[c+-2>>1]|0;g=R-(Ze(A,g)|0)|0;R=Ve[S>>1]|0;A=d-(Ze(R,A)|0)|0;d=Ve[c+-4>>1]|0;R=g-(Ze(d,R)|0)|0;g=Ve[b>>1]|0;d=A-(Ze(g,d)|0)|0;A=Ve[c+-6>>1]|0;g=R-(Ze(A,g)|0)|0;R=Ve[v>>1]|0;A=d-(Ze(A,R)|0)|0;d=Ve[c+-8>>1]|0;R=g-(Ze(d,R)|0)|0;g=Ve[_>>1]|0;d=A-(Ze(g,d)|0)|0;A=Ve[c+-10>>1]|0;g=R-(Ze(A,g)|0)|0;R=Ve[k>>1]|0;A=d-(Ze(R,A)|0)|0;d=Ve[c+-12>>1]|0;R=g-(Ze(d,R)|0)|0;g=Ve[F>>1]|0;d=A-(Ze(d,g)|0)|0;A=Ve[c+-14>>1]|0;g=R-(Ze(A,g)|0)|0;R=Ve[w>>1]|0;A=d-(Ze(R,A)|0)|0;d=Ve[c+-16>>1]|0;R=g-(Ze(d,R)|0)|0;g=Ve[m>>1]|0;d=A-(Ze(g,d)|0)|0;g=R-(Ze(Ve[c+-18>>1]|0,g)|0)|0;g=(g+134217728|0)>>>0<268435455?g>>>12&65535:(g|0)>134217727?32767:-32768;d=d-(Ze(Ve[E>>1]|0,g<<16>>16)|0)|0;c=l+2|0;Ve[l>>1]=g;Ve[u>>1]=g;s=(d+134217728|0)>>>0<268435455?d>>>12&65535:(d|0)>134217727?32767:-32768;Ve[c>>1]=s;Ve[u+2>>1]=s;a=a+-1<<16>>16;if(!(a<<16>>16))break;else{f=f+4|0;u=u+4|0;l=l+4|0}}t=(t<<16>>16)+-10|0;l=t>>>1&65535;if(l<<16>>16){d=n+18|0;s=r+16|0;c=Ve[d>>1]|0;f=h;a=n+20|0;while(1){g=Ve[e>>1]|0;u=(Ze(g,Ve[f>>1]|0)|0)+2048|0;g=(Ze(Ve[s+6>>1]|0,g)|0)+2048|0;s=Ve[E>>1]|0;R=c<<16>>16;u=u-(Ze(R,s)|0)|0;A=Ve[p>>1]|0;R=g-(Ze(R,A)|0)|0;g=Ve[d+-2>>1]|0;A=u-(Ze(g,A)|0)|0;u=Ve[S>>1]|0;g=R-(Ze(u,g)|0)|0;R=Ve[d+-4>>1]|0;u=A-(Ze(R,u)|0)|0;A=Ve[b>>1]|0;R=g-(Ze(A,R)|0)|0;g=Ve[d+-6>>1]|0;A=u-(Ze(g,A)|0)|0;u=Ve[v>>1]|0;g=R-(Ze(g,u)|0)|0;R=Ve[d+-8>>1]|0;u=A-(Ze(R,u)|0)|0;A=Ve[_>>1]|0;R=g-(Ze(A,R)|0)|0;g=Ve[d+-10>>1]|0;A=u-(Ze(g,A)|0)|0;u=Ve[k>>1]|0;g=R-(Ze(u,g)|0)|0;R=Ve[d+-12>>1]|0;u=A-(Ze(R,u)|0)|0;A=Ve[F>>1]|0;R=g-(Ze(R,A)|0)|0;g=Ve[d+-14>>1]|0;A=u-(Ze(g,A)|0)|0;u=Ve[w>>1]|0;g=R-(Ze(u,g)|0)|0;R=Ve[d+-16>>1]|0;u=A-(Ze(R,u)|0)|0;A=Ve[m>>1]|0;R=g-(Ze(A,R)|0)|0;A=u-(Ze(Ve[d+-18>>1]|0,A)|0)|0;u=f+4|0;A=(A+134217728|0)>>>0<268435455?A>>>12&65535:(A|0)>134217727?32767:-32768;s=R-(Ze(s,A<<16>>16)|0)|0;d=a+2|0;Ve[a>>1]=A;do{if((s+134217728|0)>>>0>=268435455){a=a+4|0;if((s|0)>134217727){Ve[d>>1]=32767;s=32767;break}else{Ve[d>>1]=-32768;s=-32768;break}}else{s=s>>>12&65535;Ve[d>>1]=s;a=a+4|0}}while(0);l=l+-1<<16>>16;if(!(l<<16>>16))break;else{A=f;c=s;f=u;s=A}}}if(!(o<<16>>16)){Ge=M;return}l=i;a=n+(t<<1)|0;s=l+20|0;do{Ve[l>>1]=Ve[a>>1]|0;l=l+2|0;a=a+2|0}while((l|0)<(s|0));Ge=M;return}function Ui(e,r,n){e=e|0;r=r|0;n=n|0;Ve[n>>1]=Ve[e>>1]|0;Ve[n+2>>1]=((Ze(Ve[r>>1]|0,Ve[e+2>>1]|0)|0)+16384|0)>>>15;Ve[n+4>>1]=((Ze(Ve[r+2>>1]|0,Ve[e+4>>1]|0)|0)+16384|0)>>>15;Ve[n+6>>1]=((Ze(Ve[r+4>>1]|0,Ve[e+6>>1]|0)|0)+16384|0)>>>15;Ve[n+8>>1]=((Ze(Ve[r+6>>1]|0,Ve[e+8>>1]|0)|0)+16384|0)>>>15;Ve[n+10>>1]=((Ze(Ve[r+8>>1]|0,Ve[e+10>>1]|0)|0)+16384|0)>>>15;Ve[n+12>>1]=((Ze(Ve[r+10>>1]|0,Ve[e+12>>1]|0)|0)+16384|0)>>>15;Ve[n+14>>1]=((Ze(Ve[r+12>>1]|0,Ve[e+14>>1]|0)|0)+16384|0)>>>15;Ve[n+16>>1]=((Ze(Ve[r+14>>1]|0,Ve[e+16>>1]|0)|0)+16384|0)>>>15;Ve[n+18>>1]=((Ze(Ve[r+16>>1]|0,Ve[e+18>>1]|0)|0)+16384|0)>>>15;Ve[n+20>>1]=((Ze(Ve[r+18>>1]|0,Ve[e+20>>1]|0)|0)+16384|0)>>>15;return}function xi(e){e=e|0;var r=0,n=0,t=0,i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0,A=0,y=0,O=0,T=0,D=0,N=0,P=0,C=0,I=0,B=0,L=0,U=0,x=0,H=0,z=0,Y=0,j=0,q=0,K=0,V=0;do{if(e>>>0<245){p=e>>>0<11?16:e+11&-8;e=p>>>3;c=We[26]|0;l=c>>>e;if(l&3){t=(l&1^1)+e|0;r=t<<1;n=144+(r<<2)|0;r=144+(r+2<<2)|0;i=We[r>>2]|0;o=i+8|0;a=We[o>>2]|0;do{if((n|0)==(a|0))We[26]=c&~(1<<t);else{if(a>>>0>=(We[30]|0)>>>0?(u=a+12|0,(We[u>>2]|0)==(i|0)):0){We[u>>2]=n;We[r>>2]=a;break}re()}}while(0);K=t<<3;We[i+4>>2]=K|3;K=i+(K|4)|0;We[K>>2]=We[K>>2]|1;break}r=We[28]|0;if(p>>>0>r>>>0){if(l){i=2<<e;i=l<<e&(i|0-i);i=(i&0-i)+-1|0;o=i>>>12&16;i=i>>>o;t=i>>>5&8;i=i>>>t;n=i>>>2&4;i=i>>>n;a=i>>>1&2;i=i>>>a;s=i>>>1&1;s=(t|o|n|a|s)+(i>>>s)|0;i=s<<1;a=144+(i<<2)|0;i=144+(i+2<<2)|0;n=We[i>>2]|0;o=n+8|0;t=We[o>>2]|0;do{if((a|0)==(t|0)){We[26]=c&~(1<<s);d=r}else{if(t>>>0>=(We[30]|0)>>>0?(f=t+12|0,(We[f>>2]|0)==(n|0)):0){We[f>>2]=a;We[i>>2]=t;d=We[28]|0;break}re()}}while(0);K=s<<3;r=K-p|0;We[n+4>>2]=p|3;l=n+p|0;We[n+(p|4)>>2]=r|1;We[n+K>>2]=r;if(d){n=We[31]|0;t=d>>>3;a=t<<1;s=144+(a<<2)|0;i=We[26]|0;t=1<<t;if(i&t){i=144+(a+2<<2)|0;a=We[i>>2]|0;if(a>>>0<(We[30]|0)>>>0)re();else{w=i;m=a}}else{We[26]=i|t;w=144+(a+2<<2)|0;m=s}We[w>>2]=n;We[m+12>>2]=n;We[n+8>>2]=m;We[n+12>>2]=s}We[28]=r;We[31]=l;break}e=We[27]|0;if(e){i=(e&0-e)+-1|0;q=i>>>12&16;i=i>>>q;j=i>>>5&8;i=i>>>j;K=i>>>2&4;i=i>>>K;a=i>>>1&2;i=i>>>a;l=i>>>1&1;l=We[408+((j|q|K|a|l)+(i>>>l)<<2)>>2]|0;i=(We[l+4>>2]&-8)-p|0;a=l;while(1){s=We[a+16>>2]|0;if(!s){s=We[a+20>>2]|0;if(!s){r=i;break}}a=(We[s+4>>2]&-8)-p|0;K=a>>>0<i>>>0;i=K?a:i;a=s;l=K?s:l}e=We[30]|0;if(l>>>0>=e>>>0?(v=l+p|0,l>>>0<v>>>0):0){t=We[l+24>>2]|0;s=We[l+12>>2]|0;do{if((s|0)==(l|0)){a=l+20|0;s=We[a>>2]|0;if(!s){a=l+16|0;s=We[a>>2]|0;if(!s){S=0;break}}while(1){o=s+20|0;i=We[o>>2]|0;if(i){s=i;a=o;continue}o=s+16|0;i=We[o>>2]|0;if(!i)break;else{s=i;a=o}}if(a>>>0<e>>>0)re();else{We[a>>2]=0;S=s;break}}else{a=We[l+8>>2]|0;if((a>>>0>=e>>>0?(n=a+12|0,(We[n>>2]|0)==(l|0)):0)?(h=s+8|0,(We[h>>2]|0)==(l|0)):0){We[n>>2]=s;We[h>>2]=a;S=s;break}re()}}while(0);do{if(t){a=We[l+28>>2]|0;o=408+(a<<2)|0;if((l|0)==(We[o>>2]|0)){We[o>>2]=S;if(!S){We[27]=We[27]&~(1<<a);break}}else{if(t>>>0<(We[30]|0)>>>0)re();a=t+16|0;if((We[a>>2]|0)==(l|0))We[a>>2]=S;else We[t+20>>2]=S;if(!S)break}o=We[30]|0;if(S>>>0<o>>>0)re();We[S+24>>2]=t;a=We[l+16>>2]|0;do{if(a)if(a>>>0<o>>>0)re();else{We[S+16>>2]=a;We[a+24>>2]=S;break}}while(0);a=We[l+20>>2]|0;if(a)if(a>>>0<(We[30]|0)>>>0)re();else{We[S+20>>2]=a;We[a+24>>2]=S;break}}}while(0);if(r>>>0<16){K=r+p|0;We[l+4>>2]=K|3;K=l+(K+4)|0;We[K>>2]=We[K>>2]|1}else{We[l+4>>2]=p|3;We[l+(p|4)>>2]=r|1;We[l+(r+p)>>2]=r;t=We[28]|0;if(t){n=We[31]|0;i=t>>>3;a=i<<1;s=144+(a<<2)|0;o=We[26]|0;i=1<<i;if(o&i){a=144+(a+2<<2)|0;o=We[a>>2]|0;if(o>>>0<(We[30]|0)>>>0)re();else{b=a;_=o}}else{We[26]=o|i;b=144+(a+2<<2)|0;_=s}We[b>>2]=n;We[_+12>>2]=n;We[n+8>>2]=_;We[n+12>>2]=s}We[28]=r;We[31]=v}o=l+8|0;break}re()}else V=154}else V=154}else if(e>>>0<=4294967231){e=e+11|0;_=e&-8;c=We[27]|0;if(c){l=0-_|0;e=e>>>8;if(e)if(_>>>0>16777215)u=31;else{v=(e+1048320|0)>>>16&8;V=e<<v;b=(V+520192|0)>>>16&4;V=V<<b;u=(V+245760|0)>>>16&2;u=14-(b|v|u)+(V<<u>>>15)|0;u=_>>>(u+7|0)&1|u<<1}else u=0;e=We[408+(u<<2)>>2]|0;e:do{if(!e){s=0;e=0;V=86}else{n=l;s=0;r=_<<((u|0)==31?0:25-(u>>>1)|0);f=e;e=0;while(1){t=We[f+4>>2]&-8;l=t-_|0;if(l>>>0<n>>>0)if((t|0)==(_|0)){t=f;e=f;V=90;break e}else e=f;else l=n;V=We[f+20>>2]|0;f=We[f+16+(r>>>31<<2)>>2]|0;s=(V|0)==0|(V|0)==(f|0)?s:V;if(!f){V=86;break}else{n=l;r=r<<1}}}}while(0);if((V|0)==86){if((s|0)==0&(e|0)==0){e=2<<u;e=c&(e|0-e);if(!e){p=_;V=154;break}e=(e&0-e)+-1|0;S=e>>>12&16;e=e>>>S;m=e>>>5&8;e=e>>>m;b=e>>>2&4;e=e>>>b;v=e>>>1&2;e=e>>>v;s=e>>>1&1;s=We[408+((m|S|b|v|s)+(e>>>s)<<2)>>2]|0;e=0}if(!s){m=l;w=e}else{t=s;V=90}}if((V|0)==90)while(1){V=0;v=(We[t+4>>2]&-8)-_|0;s=v>>>0<l>>>0;l=s?v:l;e=s?t:e;s=We[t+16>>2]|0;if(s){t=s;V=90;continue}t=We[t+20>>2]|0;if(!t){m=l;w=e;break}else V=90}if((w|0)!=0?m>>>0<((We[28]|0)-_|0)>>>0:0){e=We[30]|0;if(w>>>0>=e>>>0?(N=w+_|0,w>>>0<N>>>0):0){l=We[w+24>>2]|0;s=We[w+12>>2]|0;do{if((s|0)==(w|0)){a=w+20|0;s=We[a>>2]|0;if(!s){a=w+16|0;s=We[a>>2]|0;if(!s){F=0;break}}while(1){o=s+20|0;i=We[o>>2]|0;if(i){s=i;a=o;continue}o=s+16|0;i=We[o>>2]|0;if(!i)break;else{s=i;a=o}}if(a>>>0<e>>>0)re();else{We[a>>2]=0;F=s;break}}else{a=We[w+8>>2]|0;if((a>>>0>=e>>>0?(E=a+12|0,(We[E>>2]|0)==(w|0)):0)?(p=s+8|0,(We[p>>2]|0)==(w|0)):0){We[E>>2]=s;We[p>>2]=a;F=s;break}re()}}while(0);do{if(l){s=We[w+28>>2]|0;a=408+(s<<2)|0;if((w|0)==(We[a>>2]|0)){We[a>>2]=F;if(!F){We[27]=We[27]&~(1<<s);break}}else{if(l>>>0<(We[30]|0)>>>0)re();a=l+16|0;if((We[a>>2]|0)==(w|0))We[a>>2]=F;else We[l+20>>2]=F;if(!F)break}s=We[30]|0;if(F>>>0<s>>>0)re();We[F+24>>2]=l;a=We[w+16>>2]|0;do{if(a)if(a>>>0<s>>>0)re();else{We[F+16>>2]=a;We[a+24>>2]=F;break}}while(0);a=We[w+20>>2]|0;if(a)if(a>>>0<(We[30]|0)>>>0)re();else{We[F+20>>2]=a;We[a+24>>2]=F;break}}}while(0);e:do{if(m>>>0>=16){We[w+4>>2]=_|3;We[w+(_|4)>>2]=m|1;We[w+(m+_)>>2]=m;s=m>>>3;if(m>>>0<256){o=s<<1;t=144+(o<<2)|0;i=We[26]|0;a=1<<s;if(i&a){a=144+(o+2<<2)|0;o=We[a>>2]|0;if(o>>>0<(We[30]|0)>>>0)re();else{M=a;g=o}}else{We[26]=i|a;M=144+(o+2<<2)|0;g=t}We[M>>2]=N;We[g+12>>2]=N;We[w+(_+8)>>2]=g;We[w+(_+12)>>2]=t;break}n=m>>>8;if(n)if(m>>>0>16777215)s=31;else{q=(n+1048320|0)>>>16&8;K=n<<q;j=(K+520192|0)>>>16&4;K=K<<j;s=(K+245760|0)>>>16&2;s=14-(j|q|s)+(K<<s>>>15)|0;s=m>>>(s+7|0)&1|s<<1}else s=0;a=408+(s<<2)|0;We[w+(_+28)>>2]=s;We[w+(_+20)>>2]=0;We[w+(_+16)>>2]=0;o=We[27]|0;i=1<<s;if(!(o&i)){We[27]=o|i;We[a>>2]=N;We[w+(_+24)>>2]=a;We[w+(_+12)>>2]=N;We[w+(_+8)>>2]=N;break}n=We[a>>2]|0;r:do{if((We[n+4>>2]&-8|0)!=(m|0)){s=m<<((s|0)==31?0:25-(s>>>1)|0);while(1){r=n+16+(s>>>31<<2)|0;a=We[r>>2]|0;if(!a)break;if((We[a+4>>2]&-8|0)==(m|0)){A=a;break r}else{s=s<<1;n=a}}if(r>>>0<(We[30]|0)>>>0)re();else{We[r>>2]=N;We[w+(_+24)>>2]=n;We[w+(_+12)>>2]=N;We[w+(_+8)>>2]=N;break e}}else A=n}while(0);n=A+8|0;r=We[n>>2]|0;K=We[30]|0;if(r>>>0>=K>>>0&A>>>0>=K>>>0){We[r+12>>2]=N;We[n>>2]=N;We[w+(_+8)>>2]=r;We[w+(_+12)>>2]=A;We[w+(_+24)>>2]=0;break}else re()}else{K=m+_|0;We[w+4>>2]=K|3;K=w+(K+4)|0;We[K>>2]=We[K>>2]|1}}while(0);o=w+8|0;break}re()}else{p=_;V=154}}else{p=_;V=154}}else{p=-1;V=154}}while(0);e:do{if((V|0)==154){e=We[28]|0;if(e>>>0>=p>>>0){r=e-p|0;n=We[31]|0;if(r>>>0>15){We[31]=n+p;We[28]=r;We[n+(p+4)>>2]=r|1;We[n+e>>2]=r;We[n+4>>2]=p|3}else{We[28]=0;We[31]=0;We[n+4>>2]=e|3;V=n+(e+4)|0;We[V>>2]=We[V>>2]|1}o=n+8|0;break}e=We[29]|0;if(e>>>0>p>>>0){V=e-p|0;We[29]=V;o=We[32]|0;We[32]=o+p;We[o+(p+4)>>2]=V|1;We[o+4>>2]=p|3;o=o+8|0;break}if(!(We[144]|0))Yi();c=p+48|0;n=We[146]|0;u=p+47|0;t=n+u|0;n=0-n|0;f=t&n;if(f>>>0>p>>>0){e=We[136]|0;if((e|0)!=0?(A=We[134]|0,N=A+f|0,N>>>0<=A>>>0|N>>>0>e>>>0):0){o=0;break}r:do{if(!(We[137]&4)){e=We[32]|0;n:do{if(e){s=552;while(1){l=We[s>>2]|0;if(l>>>0<=e>>>0?(k=s+4|0,(l+(We[k>>2]|0)|0)>>>0>e>>>0):0){o=s;e=k;break}s=We[s+8>>2]|0;if(!s){V=172;break n}}l=t-(We[29]|0)&n;if(l>>>0<2147483647){s=te(l|0)|0;N=(s|0)==((We[o>>2]|0)+(We[e>>2]|0)|0);e=N?l:0;if(N){if((s|0)!=(-1|0)){g=s;S=e;V=192;break r}}else V=182}else e=0}else V=172}while(0);do{if((V|0)==172){o=te(0)|0;if((o|0)!=(-1|0)){e=o;l=We[145]|0;s=l+-1|0;if(!(s&e))l=f;else l=f-e+(s+e&0-l)|0;e=We[134]|0;s=e+l|0;if(l>>>0>p>>>0&l>>>0<2147483647){N=We[136]|0;if((N|0)!=0?s>>>0<=e>>>0|s>>>0>N>>>0:0){e=0;break}s=te(l|0)|0;V=(s|0)==(o|0);e=V?l:0;if(V){g=o;S=e;V=192;break r}else V=182}else e=0}else e=0}}while(0);n:do{if((V|0)==182){o=0-l|0;do{if(c>>>0>l>>>0&(l>>>0<2147483647&(s|0)!=(-1|0))?(R=We[146]|0,R=u-l+R&0-R,R>>>0<2147483647):0)if((te(R|0)|0)==(-1|0)){te(o|0)|0;break n}else{l=R+l|0;break}}while(0);if((s|0)!=(-1|0)){g=s;S=l;V=192;break r}}}while(0);We[137]=We[137]|4;V=189}else{e=0;V=189}}while(0);if((((V|0)==189?f>>>0<2147483647:0)?(y=te(f|0)|0,O=te(0)|0,y>>>0<O>>>0&((y|0)!=(-1|0)&(O|0)!=(-1|0))):0)?(T=O-y|0,D=T>>>0>(p+40|0)>>>0,D):0){g=y;S=D?T:e;V=192}if((V|0)==192){l=(We[134]|0)+S|0;We[134]=l;if(l>>>0>(We[135]|0)>>>0)We[135]=l;m=We[32]|0;r:do{if(m){o=552;do{e=We[o>>2]|0;l=o+4|0;s=We[l>>2]|0;if((g|0)==(e+s|0)){P=e;C=l;I=s;B=o;V=202;break}o=We[o+8>>2]|0}while((o|0)!=0);if(((V|0)==202?(We[B+12>>2]&8|0)==0:0)?m>>>0<g>>>0&m>>>0>=P>>>0:0){We[C>>2]=I+S;V=(We[29]|0)+S|0;K=m+8|0;K=(K&7|0)==0?0:0-K&7;q=V-K|0;We[32]=m+K;We[29]=q;We[m+(K+4)>>2]=q|1;We[m+(V+4)>>2]=40;We[33]=We[148];break}l=We[30]|0;if(g>>>0<l>>>0){We[30]=g;l=g}s=g+S|0;e=552;while(1){if((We[e>>2]|0)==(s|0)){o=e;s=e;V=210;break}e=We[e+8>>2]|0;if(!e){s=552;break}}if((V|0)==210)if(!(We[s+12>>2]&8)){We[o>>2]=g;h=s+4|0;We[h>>2]=(We[h>>2]|0)+S;h=g+8|0;h=(h&7|0)==0?0:0-h&7;u=g+(S+8)|0;u=(u&7|0)==0?0:0-u&7;s=g+(u+S)|0;w=h+p|0;d=g+w|0;e=s-(g+h)-p|0;We[g+(h+4)>>2]=p|3;n:do{if((s|0)!=(m|0)){if((s|0)==(We[31]|0)){V=(We[28]|0)+e|0;We[28]=V;We[31]=d;We[g+(w+4)>>2]=V|1;We[g+(V+w)>>2]=V;break}r=S+4|0;a=We[g+(r+u)>>2]|0;if((a&3|0)==1){f=a&-8;t=a>>>3;t:do{if(a>>>0>=256){n=We[g+((u|24)+S)>>2]|0;o=We[g+(S+12+u)>>2]|0;i:do{if((o|0)==(s|0)){i=u|16;o=g+(r+i)|0;a=We[o>>2]|0;if(!a){o=g+(i+S)|0;a=We[o>>2]|0;if(!a){Y=0;break}}while(1){i=a+20|0;t=We[i>>2]|0;if(t){a=t;o=i;continue}i=a+16|0;t=We[i>>2]|0;if(!t)break;else{a=t;o=i}}if(o>>>0<l>>>0)re();else{We[o>>2]=0;Y=a;break}}else{i=We[g+((u|8)+S)>>2]|0;do{if(i>>>0>=l>>>0){l=i+12|0;if((We[l>>2]|0)!=(s|0))break;a=o+8|0;if((We[a>>2]|0)!=(s|0))break;We[l>>2]=o;We[a>>2]=i;Y=o;break i}}while(0);re()}}while(0);if(!n)break;l=We[g+(S+28+u)>>2]|0;a=408+(l<<2)|0;do{if((s|0)!=(We[a>>2]|0)){if(n>>>0<(We[30]|0)>>>0)re();a=n+16|0;if((We[a>>2]|0)==(s|0))We[a>>2]=Y;else We[n+20>>2]=Y;if(!Y)break t}else{We[a>>2]=Y;if(Y)break;We[27]=We[27]&~(1<<l);break t}}while(0);l=We[30]|0;if(Y>>>0<l>>>0)re();We[Y+24>>2]=n;s=u|16;a=We[g+(s+S)>>2]|0;do{if(a)if(a>>>0<l>>>0)re();else{We[Y+16>>2]=a;We[a+24>>2]=Y;break}}while(0);s=We[g+(r+s)>>2]|0;if(!s)break;if(s>>>0<(We[30]|0)>>>0)re();else{We[Y+20>>2]=s;We[s+24>>2]=Y;break}}else{a=We[g+((u|8)+S)>>2]|0;o=We[g+(S+12+u)>>2]|0;i=144+(t<<1<<2)|0;do{if((a|0)!=(i|0)){if(a>>>0>=l>>>0?(We[a+12>>2]|0)==(s|0):0)break;re()}}while(0);if((o|0)==(a|0)){We[26]=We[26]&~(1<<t);break}do{if((o|0)==(i|0))L=o+8|0;else{if(o>>>0>=l>>>0?(U=o+8|0,(We[U>>2]|0)==(s|0)):0){L=U;break}re()}}while(0);We[a+12>>2]=o;We[L>>2]=a}}while(0);s=g+((f|u)+S)|0;e=f+e|0}s=s+4|0;We[s>>2]=We[s>>2]&-2;We[g+(w+4)>>2]=e|1;We[g+(e+w)>>2]=e;s=e>>>3;if(e>>>0<256){o=s<<1;t=144+(o<<2)|0;i=We[26]|0;a=1<<s;do{if(!(i&a)){We[26]=i|a;j=144+(o+2<<2)|0;q=t}else{a=144+(o+2<<2)|0;o=We[a>>2]|0;if(o>>>0>=(We[30]|0)>>>0){j=a;q=o;break}re()}}while(0);We[j>>2]=d;We[q+12>>2]=d;We[g+(w+8)>>2]=q;We[g+(w+12)>>2]=t;break}n=e>>>8;do{if(!n)s=0;else{if(e>>>0>16777215){s=31;break}q=(n+1048320|0)>>>16&8;V=n<<q;j=(V+520192|0)>>>16&4;V=V<<j;s=(V+245760|0)>>>16&2;s=14-(j|q|s)+(V<<s>>>15)|0;s=e>>>(s+7|0)&1|s<<1}}while(0);a=408+(s<<2)|0;We[g+(w+28)>>2]=s;We[g+(w+20)>>2]=0;We[g+(w+16)>>2]=0;o=We[27]|0;i=1<<s;if(!(o&i)){We[27]=o|i;We[a>>2]=d;We[g+(w+24)>>2]=a;We[g+(w+12)>>2]=d;We[g+(w+8)>>2]=d;break}n=We[a>>2]|0;t:do{if((We[n+4>>2]&-8|0)!=(e|0)){s=e<<((s|0)==31?0:25-(s>>>1)|0);while(1){r=n+16+(s>>>31<<2)|0;a=We[r>>2]|0;if(!a)break;if((We[a+4>>2]&-8|0)==(e|0)){K=a;break t}else{s=s<<1;n=a}}if(r>>>0<(We[30]|0)>>>0)re();else{We[r>>2]=d;We[g+(w+24)>>2]=n;We[g+(w+12)>>2]=d;We[g+(w+8)>>2]=d;break n}}else K=n}while(0);n=K+8|0;r=We[n>>2]|0;V=We[30]|0;if(r>>>0>=V>>>0&K>>>0>=V>>>0){We[r+12>>2]=d;We[n>>2]=d;We[g+(w+8)>>2]=r;We[g+(w+12)>>2]=K;We[g+(w+24)>>2]=0;break}else re()}else{V=(We[29]|0)+e|0;We[29]=V;We[32]=d;We[g+(w+4)>>2]=V|1}}while(0);o=g+(h|8)|0;break e}else s=552;while(1){o=We[s>>2]|0;if(o>>>0<=m>>>0?(a=We[s+4>>2]|0,i=o+a|0,i>>>0>m>>>0):0)break;s=We[s+8>>2]|0}s=o+(a+-39)|0;s=o+(a+-47+((s&7|0)==0?0:0-s&7))|0;l=m+16|0;s=s>>>0<l>>>0?m:s;a=s+8|0;o=g+8|0;o=(o&7|0)==0?0:0-o&7;V=S+-40-o|0;We[32]=g+o;We[29]=V;We[g+(o+4)>>2]=V|1;We[g+(S+-36)>>2]=40;We[33]=We[148];o=s+4|0;We[o>>2]=27;We[a>>2]=We[138];We[a+4>>2]=We[139];We[a+8>>2]=We[140];We[a+12>>2]=We[141];We[138]=g;We[139]=S;We[141]=0;We[140]=a;a=s+28|0;We[a>>2]=7;if((s+32|0)>>>0<i>>>0)do{V=a;a=a+4|0;We[a>>2]=7}while((V+8|0)>>>0<i>>>0);if((s|0)!=(m|0)){e=s-m|0;We[o>>2]=We[o>>2]&-2;We[m+4>>2]=e|1;We[s>>2]=e;i=e>>>3;if(e>>>0<256){a=i<<1;s=144+(a<<2)|0;o=We[26]|0;t=1<<i;if(o&t){n=144+(a+2<<2)|0;r=We[n>>2]|0;if(r>>>0<(We[30]|0)>>>0)re();else{x=n;H=r}}else{We[26]=o|t;x=144+(a+2<<2)|0;H=s}We[x>>2]=m;We[H+12>>2]=m;We[m+8>>2]=H;We[m+12>>2]=s;break}n=e>>>8;if(n)if(e>>>0>16777215)a=31;else{K=(n+1048320|0)>>>16&8;V=n<<K;q=(V+520192|0)>>>16&4;V=V<<q;a=(V+245760|0)>>>16&2;a=14-(q|K|a)+(V<<a>>>15)|0;a=e>>>(a+7|0)&1|a<<1}else a=0;t=408+(a<<2)|0;We[m+28>>2]=a;We[m+20>>2]=0;We[l>>2]=0;n=We[27]|0;r=1<<a;if(!(n&r)){We[27]=n|r;We[t>>2]=m;We[m+24>>2]=t;We[m+12>>2]=m;We[m+8>>2]=m;break}n=We[t>>2]|0;n:do{if((We[n+4>>2]&-8|0)!=(e|0)){a=e<<((a|0)==31?0:25-(a>>>1)|0);while(1){r=n+16+(a>>>31<<2)|0;t=We[r>>2]|0;if(!t)break;if((We[t+4>>2]&-8|0)==(e|0)){z=t;break n}else{a=a<<1;n=t}}if(r>>>0<(We[30]|0)>>>0)re();else{We[r>>2]=m;We[m+24>>2]=n;We[m+12>>2]=m;We[m+8>>2]=m;break r}}else z=n}while(0);n=z+8|0;r=We[n>>2]|0;V=We[30]|0;if(r>>>0>=V>>>0&z>>>0>=V>>>0){We[r+12>>2]=m;We[n>>2]=m;We[m+8>>2]=r;We[m+12>>2]=z;We[m+24>>2]=0;break}else re()}}else{V=We[30]|0;if((V|0)==0|g>>>0<V>>>0)We[30]=g;We[138]=g;We[139]=S;We[141]=0;We[35]=We[144];We[34]=-1;n=0;do{V=n<<1;K=144+(V<<2)|0;We[144+(V+3<<2)>>2]=K;We[144+(V+2<<2)>>2]=K;n=n+1|0}while((n|0)!=32);V=g+8|0;V=(V&7|0)==0?0:0-V&7;K=S+-40-V|0;We[32]=g+V;We[29]=K;We[g+(V+4)>>2]=K|1;We[g+(S+-36)>>2]=40;We[33]=We[148]}}while(0);r=We[29]|0;if(r>>>0>p>>>0){V=r-p|0;We[29]=V;o=We[32]|0;We[32]=o+p;We[o+(p+4)>>2]=V|1;We[o+4>>2]=p|3;o=o+8|0;break}}We[(zi()|0)>>2]=12;o=0}else o=0}}while(0);return o|0}function Hi(e){e=e|0;var r=0,n=0,t=0,i=0,o=0,a=0,s=0,l=0,f=0,u=0,c=0,d=0,h=0,w=0,m=0,E=0,p=0,S=0,b=0,v=0,_=0,k=0,F=0,M=0,g=0,R=0;e:do{if(e){i=e+-8|0;f=We[30]|0;r:do{if(i>>>0>=f>>>0?(t=We[e+-4>>2]|0,n=t&3,(n|0)!=1):0){v=t&-8;_=e+(v+-8)|0;do{if(!(t&1)){i=We[i>>2]|0;if(!n)break e;u=-8-i|0;d=e+u|0;h=i+v|0;if(d>>>0<f>>>0)break r;if((d|0)==(We[31]|0)){o=e+(v+-4)|0;i=We[o>>2]|0;if((i&3|0)!=3){R=d;o=h;break}We[28]=h;We[o>>2]=i&-2;We[e+(u+4)>>2]=h|1;We[_>>2]=h;break e}n=i>>>3;if(i>>>0<256){t=We[e+(u+8)>>2]|0;o=We[e+(u+12)>>2]|0;i=144+(n<<1<<2)|0;do{if((t|0)!=(i|0)){if(t>>>0>=f>>>0?(We[t+12>>2]|0)==(d|0):0)break;re()}}while(0);if((o|0)==(t|0)){We[26]=We[26]&~(1<<n);R=d;o=h;break}do{if((o|0)==(i|0))r=o+8|0;else{if(o>>>0>=f>>>0?(a=o+8|0,(We[a>>2]|0)==(d|0)):0){r=a;break}re()}}while(0);We[t+12>>2]=o;We[r>>2]=t;R=d;o=h;break}a=We[e+(u+24)>>2]|0;i=We[e+(u+12)>>2]|0;do{if((i|0)==(d|0)){t=e+(u+20)|0;i=We[t>>2]|0;if(!i){t=e+(u+16)|0;i=We[t>>2]|0;if(!i){c=0;break}}while(1){n=i+20|0;r=We[n>>2]|0;if(r){i=r;t=n;continue}n=i+16|0;r=We[n>>2]|0;if(!r)break;else{i=r;t=n}}if(t>>>0<f>>>0)re();else{We[t>>2]=0;c=i;break}}else{t=We[e+(u+8)>>2]|0;if((t>>>0>=f>>>0?(s=t+12|0,(We[s>>2]|0)==(d|0)):0)?(l=i+8|0,(We[l>>2]|0)==(d|0)):0){We[s>>2]=i;We[l>>2]=t;c=i;break}re()}}while(0);if(a){i=We[e+(u+28)>>2]|0;t=408+(i<<2)|0;if((d|0)==(We[t>>2]|0)){We[t>>2]=c;if(!c){We[27]=We[27]&~(1<<i);R=d;o=h;break}}else{if(a>>>0<(We[30]|0)>>>0)re();i=a+16|0;if((We[i>>2]|0)==(d|0))We[i>>2]=c;else We[a+20>>2]=c;if(!c){R=d;o=h;break}}t=We[30]|0;if(c>>>0<t>>>0)re();We[c+24>>2]=a;i=We[e+(u+16)>>2]|0;do{if(i)if(i>>>0<t>>>0)re();else{We[c+16>>2]=i;We[i+24>>2]=c;break}}while(0);i=We[e+(u+20)>>2]|0;if(i)if(i>>>0<(We[30]|0)>>>0)re();else{We[c+20>>2]=i;We[i+24>>2]=c;R=d;o=h;break}else{R=d;o=h}}else{R=d;o=h}}else{R=i;o=v}}while(0);if(R>>>0<_>>>0?(w=e+(v+-4)|0,m=We[w>>2]|0,(m&1|0)!=0):0){if(!(m&2)){if((_|0)==(We[32]|0)){g=(We[29]|0)+o|0;We[29]=g;We[32]=R;We[R+4>>2]=g|1;if((R|0)!=(We[31]|0))break e;We[31]=0;We[28]=0;break e}if((_|0)==(We[31]|0)){g=(We[28]|0)+o|0;We[28]=g;We[31]=R;We[R+4>>2]=g|1;We[R+g>>2]=g;break e}l=(m&-8)+o|0;n=m>>>3;do{if(m>>>0>=256){r=We[e+(v+16)>>2]|0;o=We[e+(v|4)>>2]|0;do{if((o|0)==(_|0)){i=e+(v+12)|0;o=We[i>>2]|0;if(!o){i=e+(v+8)|0;o=We[i>>2]|0;if(!o){k=0;break}}while(1){t=o+20|0;n=We[t>>2]|0;if(n){o=n;i=t;continue}t=o+16|0;n=We[t>>2]|0;if(!n)break;else{o=n;i=t}}if(i>>>0<(We[30]|0)>>>0)re();else{We[i>>2]=0;k=o;break}}else{i=We[e+v>>2]|0;if((i>>>0>=(We[30]|0)>>>0?(S=i+12|0,(We[S>>2]|0)==(_|0)):0)?(b=o+8|0,(We[b>>2]|0)==(_|0)):0){We[S>>2]=o;We[b>>2]=i;k=o;break}re()}}while(0);if(r){o=We[e+(v+20)>>2]|0;i=408+(o<<2)|0;if((_|0)==(We[i>>2]|0)){We[i>>2]=k;if(!k){We[27]=We[27]&~(1<<o);break}}else{if(r>>>0<(We[30]|0)>>>0)re();o=r+16|0;if((We[o>>2]|0)==(_|0))We[o>>2]=k;else We[r+20>>2]=k;if(!k)break}o=We[30]|0;if(k>>>0<o>>>0)re();We[k+24>>2]=r;i=We[e+(v+8)>>2]|0;do{if(i)if(i>>>0<o>>>0)re();else{We[k+16>>2]=i;We[i+24>>2]=k;break}}while(0);n=We[e+(v+12)>>2]|0;if(n)if(n>>>0<(We[30]|0)>>>0)re();else{We[k+20>>2]=n;We[n+24>>2]=k;break}}}else{t=We[e+v>>2]|0;o=We[e+(v|4)>>2]|0;i=144+(n<<1<<2)|0;do{if((t|0)!=(i|0)){if(t>>>0>=(We[30]|0)>>>0?(We[t+12>>2]|0)==(_|0):0)break;re()}}while(0);if((o|0)==(t|0)){We[26]=We[26]&~(1<<n);break}do{if((o|0)==(i|0))E=o+8|0;else{if(o>>>0>=(We[30]|0)>>>0?(p=o+8|0,(We[p>>2]|0)==(_|0)):0){E=p;break}re()}}while(0);We[t+12>>2]=o;We[E>>2]=t}}while(0);We[R+4>>2]=l|1;We[R+l>>2]=l;if((R|0)==(We[31]|0)){We[28]=l;break e}else o=l}else{We[w>>2]=m&-2;We[R+4>>2]=o|1;We[R+o>>2]=o}i=o>>>3;if(o>>>0<256){t=i<<1;o=144+(t<<2)|0;r=We[26]|0;n=1<<i;if(r&n){n=144+(t+2<<2)|0;r=We[n>>2]|0;if(r>>>0<(We[30]|0)>>>0)re();else{F=n;M=r}}else{We[26]=r|n;F=144+(t+2<<2)|0;M=o}We[F>>2]=R;We[M+12>>2]=R;We[R+8>>2]=M;We[R+12>>2]=o;break e}r=o>>>8;if(r)if(o>>>0>16777215)i=31;else{F=(r+1048320|0)>>>16&8;M=r<<F;e=(M+520192|0)>>>16&4;M=M<<e;i=(M+245760|0)>>>16&2;i=14-(e|F|i)+(M<<i>>>15)|0;i=o>>>(i+7|0)&1|i<<1}else i=0;n=408+(i<<2)|0;We[R+28>>2]=i;We[R+20>>2]=0;We[R+16>>2]=0;r=We[27]|0;t=1<<i;n:do{if(r&t){n=We[n>>2]|0;t:do{if((We[n+4>>2]&-8|0)!=(o|0)){i=o<<((i|0)==31?0:25-(i>>>1)|0);while(1){r=n+16+(i>>>31<<2)|0;t=We[r>>2]|0;if(!t)break;if((We[t+4>>2]&-8|0)==(o|0)){g=t;break t}else{i=i<<1;n=t}}if(r>>>0<(We[30]|0)>>>0)re();else{We[r>>2]=R;We[R+24>>2]=n;We[R+12>>2]=R;We[R+8>>2]=R;break n}}else g=n}while(0);r=g+8|0;n=We[r>>2]|0;M=We[30]|0;if(n>>>0>=M>>>0&g>>>0>=M>>>0){We[n+12>>2]=R;We[r>>2]=R;We[R+8>>2]=n;We[R+12>>2]=g;We[R+24>>2]=0;break}else re()}else{We[27]=r|t;We[n>>2]=R;We[R+24>>2]=n;We[R+12>>2]=R;We[R+8>>2]=R}}while(0);R=(We[34]|0)+-1|0;We[34]=R;if(!R)r=560;else break e;while(1){r=We[r>>2]|0;if(!r)break;else r=r+8|0}We[34]=-1;break e}}}while(0);re()}}while(0);return}function zi(){var e=0;if(!0)e=600;else e=We[(ee()|0)+60>>2]|0;return e|0}function Yi(){var e=0;do{if(!(We[144]|0)){e=$(30)|0;if(!(e+-1&e)){We[146]=e;We[145]=e;We[147]=-1;We[148]=-1;We[149]=0;We[137]=0;We[144]=(ie(0)|0)&-16^1431655768;break}else re()}}while(0);return}function ji(){}function qi(e,r,n){e=e|0;r=r|0;n=n|0;var t=0;if((n|0)>=4096)return ae(e|0,r|0,n|0)|0;t=e|0;if((e&3)==(r&3)){while(e&3){if(!n)return t|0;Ke[e>>0]=Ke[r>>0]|0;e=e+1|0;r=r+1|0;n=n-1|0}while((n|0)>=4){We[e>>2]=We[r>>2];e=e+4|0;r=r+4|0;n=n-4|0}}while((n|0)>0){Ke[e>>0]=Ke[r>>0]|0;e=e+1|0;r=r+1|0;n=n-1|0}return t|0}function Ki(e,r,n){e=e|0;r=r|0;n=n|0;var t=0;if((r|0)<(e|0)&(e|0)<(r+n|0)){t=e;r=r+n|0;e=e+n|0;while((n|0)>0){e=e-1|0;r=r-1|0;n=n-1|0;Ke[e>>0]=Ke[r>>0]|0}e=t}else qi(e,r,n)|0;return e|0}function Vi(e,r,n){e=e|0;r=r|0;n=n|0;var t=0,i=0,o=0,a=0;t=e+n|0;if((n|0)>=20){r=r&255;o=e&3;a=r|r<<8|r<<16|r<<24;i=t&~3;if(o){o=e+4-o|0;while((e|0)<(o|0)){Ke[e>>0]=r;e=e+1|0}}while((e|0)<(i|0)){We[e>>2]=a;e=e+4|0}}while((e|0)<(t|0)){Ke[e>>0]=r;e=e+1|0}return e-n|0}return{_free:Hi,___errno_location:zi,_memmove:Ki,_Decoder_Interface_Decode:ve,_Decoder_Interface_exit:be,_Encoder_Interface_init:_e,_memset:Vi,_malloc:xi,_memcpy:qi,_Encoder_Interface_exit:ke,_Decoder_Interface_init:Se,_Encoder_Interface_Encode:Fe,runPostSets:ji,stackAlloc:fe,stackSave:ue,stackRestore:ce,establishStackSpace:de,setThrew:he,setTempRet0:Ee,getTempRet0:pe}}(Module.asmGlobalArg,Module.asmLibraryArg,buffer),_Encoder_Interface_Encode=Module._Encoder_Interface_Encode=asm._Encoder_Interface_Encode,_free=Module._free=asm._free,runPostSets=Module.runPostSets=asm.runPostSets,_memmove=Module._memmove=asm._memmove,_Decoder_Interface_exit=Module._Decoder_Interface_exit=asm._Decoder_Interface_exit,_Encoder_Interface_init=Module._Encoder_Interface_init=asm._Encoder_Interface_init,_memset=Module._memset=asm._memset,_malloc=Module._malloc=asm._malloc,_memcpy=Module._memcpy=asm._memcpy,_Decoder_Interface_Decode=Module._Decoder_Interface_Decode=asm._Decoder_Interface_Decode,_Decoder_Interface_init=Module._Decoder_Interface_init=asm._Decoder_Interface_init,_Encoder_Interface_exit=Module._Encoder_Interface_exit=asm._Encoder_Interface_exit,___errno_location=Module.___errno_location=asm.___errno_location,initialStackTop;function ExitStatus(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}Runtime.stackAlloc=asm.stackAlloc,Runtime.stackSave=asm.stackSave,Runtime.stackRestore=asm.stackRestore,Runtime.establishStackSpace=asm.establishStackSpace,Runtime.setTempRet0=asm.setTempRet0,Runtime.getTempRet0=asm.getTempRet0,ExitStatus.prototype=new Error,ExitStatus.prototype.constructor=ExitStatus;var preloadStartTime=null,calledMain=!1;function run(e){function r(){Module.calledRun||(Module.calledRun=!0,ABORT||(ensureInitRuntime(),preMain(),Module.onRuntimeInitialized&&Module.onRuntimeInitialized(),Module._main&&shouldRunNow&&Module.callMain(e),postRun()))}e=e||Module.arguments,null===preloadStartTime&&(preloadStartTime=Date.now()),0<runDependencies||(preRun(),0<runDependencies||Module.calledRun||(Module.setStatus?(Module.setStatus("Running..."),setTimeout(function(){setTimeout(function(){Module.setStatus("")},1),r()},1)):r()))}function exit(e,r){if(!r||!Module.noExitRuntime)throw Module.noExitRuntime||(ABORT=!0,EXITSTATUS=e,STACKTOP=initialStackTop,exitRuntime(),Module.onExit&&Module.onExit(e)),ENVIRONMENT_IS_SHELL&&"function"==typeof quit&&quit(e),new ExitStatus(e)}dependenciesFulfilled=function e(){Module.calledRun||run(),Module.calledRun||(dependenciesFulfilled=e)},Module.callMain=Module.callMain=function(e){assert(0==runDependencies,"cannot call main when async dependencies remain! (listen on __ATMAIN__)"),assert(0==__ATPRERUN__.length,"cannot call main when preRun functions remain to be called"),e=e||[],ensureInitRuntime();var r=e.length+1;function n(){for(var e=0;e<3;e++)t.push(0)}var t=[allocate(intArrayFromString(Module.thisProgram),"i8",ALLOC_NORMAL)];n();for(var i=0;i<r-1;i+=1)t.push(allocate(intArrayFromString(e[i]),"i8",ALLOC_NORMAL)),n();t.push(0),t=allocate(t,"i32",ALLOC_NORMAL),initialStackTop=Runtime.stackSave();try{exit(Module._main(r,t,0),!0)}catch(e){if(e instanceof ExitStatus)return;if("SimulateInfiniteLoop"==e)return Module.noExitRuntime=!0,void Runtime.stackRestore(initialStackTop);throw e&&"object"==typeof e&&e.stack&&Module.printErr("exception thrown: "+[e,e.stack]),e}finally{calledMain=!0}},Module.run=Module.run=run,Module.exit=Module.exit=exit;var abortDecorators=[];function abort(r){void 0!==r?(Module.print(r),Module.printErr(r),r=JSON.stringify(r)):r="",ABORT=!0,EXITSTATUS=1;var n="abort("+r+") at "+stackTrace()+"\nIf this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information.";throw abortDecorators&&abortDecorators.forEach(function(e){n=e(n,r)}),n}if(Module.abort=Module.abort=abort,Module.preInit)for("function"==typeof Module.preInit&&(Module.preInit=[Module.preInit]);0<Module.preInit.length;)Module.preInit.pop()();var shouldRunNow=!0;Module.noInitialRun&&(shouldRunNow=!1),Module.noExitRuntime=!0,run(),Recorder.AMR=AMR}(),function(){"use strict";Recorder.prototype.enc_wav={stable:!0,testmsg:"比特率取值范围8位、16位"},Recorder.prototype.wav=function(e,r,n){var t=this.set,i=e.length,o=t.sampleRate,a=8==t.bitRate?8:16,s=i*(a/8),l=new ArrayBuffer(44+s),f=new DataView(l),u=0,c=function(e){for(var r=0;r<e.length;r++,u++)f.setUint8(u,e.charCodeAt(r))},d=function(e){f.setUint16(u,e,!0),u+=2},h=function(e){f.setUint32(u,e,!0),u+=4};if(c("RIFF"),h(36+s),c("WAVE"),c("fmt "),h(16),d(1),d(1),h(o),h(o*(a/8)),d(a/8),d(a),c("data"),h(s),8==a)for(var w=0;w<i;w++,u++){var m=e[w];m=parseInt(255/(65535/(m+32768))),f.setInt8(u,m,!0)}else for(w=0;w<i;w++,u+=2)f.setInt16(u,e[w],!0);r(new Blob([f],{type:"audio/wav"}))}}();
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-19592081"],{2132:function(t,e,i){var o=i("977b");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=i("499e").default;n("90991c08",o,!0,{sourceMap:!1,shadowMode:!1})},"6e55":function(t,e,i){"use strict";i("2132")},"977b":function(t,e,i){var o=i("24fb");e=o(!1),e.push([t.i,"#coupon_edit[data-v-9721ce2c]{overflow:scroll;-webkit-overflow-scrolling:touch}#coupon_edit .content .coupon_open[data-v-9721ce2c]{background:#fff;padding:.625rem 0}#coupon_edit .content .coupon_open h1[data-v-9721ce2c]{font-size:16px;line-height:1.875rem;text-align:left;padding:0 .625rem}#coupon_edit .content .coupon_open .list[data-v-9721ce2c]{padding:0 .875rem}#coupon_edit .content .coupon_open .list li[data-v-9721ce2c]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;line-height:2.5rem;font-size:15px}#coupon_edit .content .coupon_open .list li span[data-v-9721ce2c]{width:6.25rem;text-align:left}#coupon_edit .content .coupon_open .list li input[data-v-9721ce2c]{border:none;width:15rem}#coupon_edit .content .coupon_condition[data-v-9721ce2c]{margin-top:.625rem;background:#fff;padding:.625rem 0}#coupon_edit .content .coupon_condition h1[data-v-9721ce2c]{font-size:16px;line-height:1.875rem;text-align:left;padding:0 .625rem}#coupon_edit .content .coupon_condition .list[data-v-9721ce2c]{padding:0 .875rem}#coupon_edit .content .coupon_condition .list .member-level[data-v-9721ce2c],#coupon_edit .content .coupon_condition .list .timeLimitOne[data-v-9721ce2c],#coupon_edit .content .coupon_condition .list .timeLimitTwo[data-v-9721ce2c]{position:relative}#coupon_edit .content .coupon_condition .list li[data-v-9721ce2c]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;line-height:2.5rem;font-size:15px}#coupon_edit .content .coupon_condition .list li span[data-v-9721ce2c]{width:6.25rem;text-align:left}#coupon_edit .content .coupon_condition .list li input[data-v-9721ce2c]{border:none;width:15rem}#coupon_edit .content .coupon_condition .list li .fa[data-v-9721ce2c]{position:absolute;right:.875rem;color:#c9c9c9;font-size:18px;line-height:2.5rem}#coupon_edit .content .coupon_condition .list .sum[data-v-9721ce2c]{-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:end;-webkit-justify-content:flex-end;-ms-flex-pack:end;justify-content:flex-end}#coupon_edit .content .coupon_condition .list .sum .box[data-v-9721ce2c]{width:15rem;height:1.875rem;background:#f5f5f5;border:.0625rem solid #ebebeb;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;padding:0 .625rem;border-radius:.25rem}#coupon_edit .content .coupon_condition .list .sum .box input[data-v-9721ce2c]{height:1.75rem;width:8.75rem;background:#fff;padding:0 .625rem;border-left:.0625rem solid #ebebeb;border-right:.0625rem solid #ebebeb}#coupon_edit .content .coupon_condition .list .range[data-v-9721ce2c],#coupon_edit .content .coupon_condition .list .range button[data-v-9721ce2c]{-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}#coupon_edit .content .coupon_condition .list .range button[data-v-9721ce2c]{width:6.25rem;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;background:#f15353;border:none;border-radius:.25rem;color:#fff;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;margin-bottom:15px}#coupon_edit .content .coupon_condition .list .range button i[data-v-9721ce2c]{font-size:1rem;color:#fff;margin-right:.25rem}#coupon_edit .content .coupon_condition .list .fenleisort[data-v-9721ce2c]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}#coupon_edit .content .coupon_condition .list .fenleisort .item[data-v-9721ce2c]{width:240px;-ms-flex-wrap:wrap;-webkit-flex-wrap:wrap;flex-wrap:wrap}#coupon_edit .content .coupon_condition .list .fenleisort .item[data-v-9721ce2c],#coupon_edit .content .coupon_condition .list .fenleisort .item p[data-v-9721ce2c]{display:-ms-flexbox;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between}#coupon_edit .content .coupon_condition .list .fenleisort .item p[data-v-9721ce2c]{width:100%;color:#fff;margin-top:15px;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;padding:10px;border-radius:.25rem;height:1.875rem;background-color:#f15353;margin-right:10px}#coupon_edit .content .coupon_condition .list .fenleisort .item p .delete[data-v-9721ce2c]{width:10px;margin-left:5px}#coupon_edit .content .coupon_condition .list .fenleisort .item p .name[data-v-9721ce2c]{width:180px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}#coupon_edit .content .coupon_condition .list .fenleisort .item p[data-v-9721ce2c]:nth-child(2){margin-top:15px;margin-bottom:0}#coupon_edit .content .coupon_condition .list .fenleisort .item p[data-v-9721ce2c]:first-child{margin-top:0}#coupon_edit .content .coupon_condition .list .goodssort[data-v-9721ce2c]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;margin-top:10px}#coupon_edit .content .coupon_condition .list .goodssort .item[data-v-9721ce2c]{width:240px;-ms-flex-wrap:wrap;-webkit-flex-wrap:wrap;flex-wrap:wrap}#coupon_edit .content .coupon_condition .list .goodssort .item[data-v-9721ce2c],#coupon_edit .content .coupon_condition .list .goodssort .item p[data-v-9721ce2c]{display:-ms-flexbox;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between}#coupon_edit .content .coupon_condition .list .goodssort .item p[data-v-9721ce2c]{width:100%;color:#fff;margin-top:15px;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;padding:10px;border-radius:.25rem;height:1.875rem;background-color:#f15353;margin-right:10px}#coupon_edit .content .coupon_condition .list .goodssort .item p .delete[data-v-9721ce2c]{width:10px;margin-left:5px}#coupon_edit .content .coupon_condition .list .goodssort .item p .name[data-v-9721ce2c]{width:180px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}#coupon_edit .content .coupon_condition .list .goodssort .item p[data-v-9721ce2c]:nth-child(2){margin-top:15px;margin-bottom:0}#coupon_edit .content .coupon_condition .list .goodssort .item p[data-v-9721ce2c]:first-child{margin-top:0}#coupon_edit .content .coupon_receive[data-v-9721ce2c]{background:#fff;padding:.625rem 0;margin-top:.625rem}#coupon_edit .content .coupon_receive h1[data-v-9721ce2c]{font-size:16px;line-height:1.875rem;text-align:left;padding:0 .625rem}#coupon_edit .content .coupon_receive .list[data-v-9721ce2c]{padding:0 .875rem}#coupon_edit .content .coupon_receive .list li[data-v-9721ce2c]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;line-height:2.5rem;font-size:15px}#coupon_edit .content .coupon_receive .list li span[data-v-9721ce2c]{width:6.25rem;text-align:left}#coupon_edit .content .coupon_receive .list li input[data-v-9721ce2c]{border:none;width:15rem}#coupon_edit .member_list_popup[data-v-9721ce2c]{background:#fff;padding-top:3.125rem;width:100%}#coupon_edit .member_list_popup .title[data-v-9721ce2c]{position:fixed;top:0;left:0;width:100%;height:3.125rem;line-height:3.125rem;text-align:center;font-size:16px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;border-bottom:.0625rem solid #ebebeb}#coupon_edit .member_list_popup .title i[data-v-9721ce2c]{position:absolute;right:.625rem;font-size:1rem;color:#c9c9c9}#coupon_edit .member_list_popup .list[data-v-9721ce2c]{padding-left:.875rem}#coupon_edit .member_list_popup .list li[data-v-9721ce2c]{line-height:2.875rem;text-align:left;border-bottom:.0625rem solid #ebebeb;font-size:15px}#coupon_edit #btn[data-v-9721ce2c]{background-color:#fff;width:100%;border-top:.0625rem solid #ebebeb;padding:0 .875rem;margin-top:1.25rem}#coupon_edit #btn button[data-v-9721ce2c]{height:2.5rem;line-height:2.5rem;border-radius:.1875rem;margin:.375rem 0;width:100%;color:#fff;font-size:16px;border:none;background-color:#f15353}#coupon_edit .select_box[data-v-9721ce2c]{background:#fff;width:100%;padding-bottom:60px}#coupon_edit .select_box .title[data-v-9721ce2c]{width:100%;height:3.125rem;line-height:3.125rem;text-align:center;font-size:16px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;border-bottom:.0625rem solid #ebebeb}#coupon_edit .select_box .title i[data-v-9721ce2c]{position:absolute;right:.625rem;font-size:1rem;color:#c9c9c9}#coupon_edit .select_box .list[data-v-9721ce2c]{position:fixed;left:0;top:3.5rem;bottom:3.7rem;width:100%;overflow:scroll}#coupon_edit .select_box .list .van-checkbox-group .van-checkbox[data-v-9721ce2c]{padding-left:20px;line-height:2.875rem;border-bottom:.0625rem solid #ebebeb;font-size:15px;width:100%;text-align:left}#coupon_edit .select_box .list .van-radio-group .van-radio[data-v-9721ce2c]:last-child{border-bottom:none}#coupon_edit .select_box .list .van-radio-group[data-v-9721ce2c]{width:100%}#coupon_edit .select_box .list .van-radio-group .van-radio[data-v-9721ce2c]{padding-left:20px;line-height:2.875rem;border-bottom:.0625rem solid #ebebeb;font-size:15px;width:100%}#coupon_edit .select_box .select_btn[data-v-9721ce2c]{background-color:#fff;position:absolute;width:100%;padding:.375rem .875rem;bottom:0;border-top:.0625rem solid #ebebeb}#coupon_edit .select_box .select_btn button[data-v-9721ce2c]{margin:0 auto;width:100%;height:2.5rem;background:#f15353;border-radius:.1875rem;color:#fff;border:none;font-size:16px}#coupon_edit .selectBox[data-v-9721ce2c]{width:100%;padding-bottom:60px}#coupon_edit .selectBox .title[data-v-9721ce2c]{width:100%;height:3.125rem;line-height:3.125rem;text-align:center;font-size:16px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;border-bottom:.0625rem solid #ebebeb}#coupon_edit .selectBox .title i[data-v-9721ce2c]{position:absolute;right:.625rem;font-size:1rem;color:#c9c9c9}#coupon_edit .selectBox .list[data-v-9721ce2c]{position:fixed;left:0;top:6.5rem;bottom:3.7rem;width:100%;overflow:scroll}#coupon_edit .selectBox .list .van-checkbox-group[data-v-9721ce2c]{width:100%}#coupon_edit .selectBox .list .van-checkbox-group .van-checkbox[data-v-9721ce2c]{padding-left:20px;line-height:2.875rem;border-bottom:.0625rem solid #ebebeb;font-size:15px;width:100%;text-align:left}#coupon_edit .selectBox .list .van-checkbox-group .van-checkbox[data-v-9721ce2c]:last-child{border-bottom:none}#coupon_edit .selectBox .list .van-radio-group[data-v-9721ce2c]{width:100%}#coupon_edit .selectBox .list .van-radio-group .van-radio[data-v-9721ce2c]{padding-left:20px;line-height:2.875rem;border-bottom:.0625rem solid #ebebeb;font-size:15px;width:100%}#coupon_edit .selectBox .select_btn[data-v-9721ce2c]{background-color:#fff;position:absolute;width:100%;height:3.2rem;padding:.375rem .875rem;bottom:0;border-top:.0625rem solid #ebebeb}#coupon_edit .selectBox .select_btn button[data-v-9721ce2c]{margin:0 auto;width:100%;height:2.5rem;background:#f15353;border-radius:.1875rem;color:#fff;border:none;font-size:16px}#coupon_edit .selectBox .input[data-v-9721ce2c]{background-color:#fff;height:2.5rem;width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;padding-left:20px;margin-top:15px}#coupon_edit .selectBox .input .classify[data-v-9721ce2c]{width:80%;height:30px;border:1px solid #ccc;border-radius:3px;display:inline-block;overflow:hidden}#coupon_edit .selectBox .input .classify .sousuo[data-v-9721ce2c]{padding-left:20px;border:none;height:30px;width:100%}#coupon_edit .selectBox .input .classify_span[data-v-9721ce2c]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;width:40px;height:30px;background-color:#f5f5f5;border-radius:3px;border-top-left-radius:0;border-bottom-left-radius:0}#coupon_edit .fenlei[data-v-9721ce2c]{position:absolute;top:0;width:100%;z-index:9999}#coupon_edit.pcStyle .fenlei[data-v-9721ce2c]{width:375px}",""]),t.exports=e},d5e91:function(t,e,i){"use strict";i.r(e);var o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{class:[3==this.fun.getPhoneEnv()?"pcStyle":""],attrs:{id:"coupon_edit"}},[i("van-popup",{style:{width:"100vw"},attrs:{position:"bottom",overlay:!1},model:{value:t.taponew,callback:function(e){t.taponew=e},expression:"taponew"}},[i("van-datetime-picker",{attrs:{type:"datetime",title:"选择完整时间","min-date":t.minDate},on:{cancel:function(e){t.taponew=!1},confirm:t.taponewSet},model:{value:t.currentDate,callback:function(e){t.currentDate=e},expression:"currentDate"}})],1),i("van-popup",{style:{width:"100vw"},attrs:{position:"bottom",overlay:!1},model:{value:t.taptwow,callback:function(e){t.taptwow=e},expression:"taptwow"}},[i("van-datetime-picker",{attrs:{type:"datetime",title:"选择完整时间","min-date":t.minDate},on:{cancel:function(e){t.taponew=!1},confirm:t.taptwowSet},model:{value:t.currentDate2,callback:function(e){t.currentDate2=e},expression:"currentDate2"}})],1),i("c-title",{attrs:{hide:!1,text:t.headTitles}}),i("div",{staticClass:"content"},[i("div",{staticClass:"coupon_open"},[i("h1",[t._v("优惠券")]),i("ul",{staticClass:"list"},[i("li",[i("span",[t._v("排序")]),i("input",{directives:[{name:"model",rawName:"v-model",value:t.display_order,expression:"display_order"}],attrs:{type:"text",placeholder:"请输入优惠券排序"},domProps:{value:t.display_order},on:{blur:function(e){return t.clearSrcoll()},input:function(e){e.target.composing||(t.display_order=e.target.value)}}})]),i("li",[i("span",[t._v("名称")]),i("input",{directives:[{name:"model",rawName:"v-model",value:t.name,expression:"name"}],attrs:{type:"text",placeholder:"请输入优惠券名称"},domProps:{value:t.name},on:{blur:function(e){return t.clearSrcoll()},input:function(e){e.target.composing||(t.name=e.target.value)}}})]),i("li",[i("span",[t._v("是否开启")]),i("van-radio-group",{attrs:{"checked-color":"#f15353"},model:{value:t.status,callback:function(e){t.status=e},expression:"status"}},[i("van-radio",{attrs:{name:1}},[t._v("开启")]),i("van-radio",{attrs:{name:0}},[t._v("不开启")])],1)],1)])]),i("div",{staticClass:"coupon_condition"},[i("h1",[t._v("使用条件")]),i("ul",{staticClass:"list"},[i("li",[i("span",[t._v("订单金额")]),i("input",{directives:[{name:"model",rawName:"v-model",value:t.enough,expression:"enough"}],attrs:{type:"text",placeholder:"消费满多少金额才可以使用该优惠券"},domProps:{value:t.enough},on:{blur:function(e){return t.clearSrcoll()},input:function(e){e.target.composing||(t.enough=e.target.value)}}})]),i("li",{staticClass:"member-level"},[i("span",[t._v("会员等级")]),i("input",{attrs:{type:"text",readonly:"",placeholder:"选择会员等级"},domProps:{value:t.chooseVip},on:{click:function(e){t.show1=!0}}}),i("i",{staticClass:"fa fa-angle-right"})]),i("li",[i("span",[t._v("使用时间限制")]),i("van-radio-group",{attrs:{"checked-color":"#f15353"},model:{value:t.time_limit,callback:function(e){t.time_limit=e},expression:"time_limit"}},[i("van-radio",{attrs:{name:0}},[t._v("有效天数")]),i("van-radio",{attrs:{name:1}},[t._v("日期")])],1)],1),0===t.time_limit?i("li",[i("span",[t._v("有效期")]),i("input",{directives:[{name:"model",rawName:"v-model",value:t.time_days,expression:"time_days"}],attrs:{type:"text",placeholder:"0为不限时间"},domProps:{value:t.time_days},on:{blur:function(e){return t.clearSrcoll()},input:function(e){e.target.composing||(t.time_days=e.target.value)}}})]):t._e(),1===t.time_limit?i("li",{staticClass:"timeLimitOne",on:{click:function(e){t.taponew=!0}}},[i("span",[t._v("开始时间")]),i("div",{staticClass:"right"},[t._v(t._s(t.time_start))]),i("i",{staticClass:"fa fa-angle-right"})]):t._e(),1===t.time_limit?i("li",{staticClass:"timeLimitTwo",on:{click:function(e){t.taptwow=!0}}},[i("span",[t._v("结束时间")]),i("div",{staticClass:"right"},[t._v(t._s(t.time_end))]),i("i",{staticClass:"fa fa-angle-right"})]):t._e(),i("li",[i("span",[t._v("使用方式")]),i("van-radio-group",{attrs:{"checked-color":"#f15353"},model:{value:t.is_complex,callback:function(e){t.is_complex=e},expression:"is_complex"}},[i("van-radio",{attrs:{name:0}},[t._v("单张使用")]),i("van-radio",{attrs:{name:1}},[t._v("多张一起使用")])],1)],1),i("li",[i("span",[t._v("优惠方式")]),i("van-radio-group",{attrs:{"checked-color":"#f15353"},model:{value:t.coupon_method,callback:function(e){t.coupon_method=e},expression:"coupon_method"}},[i("van-radio",{attrs:{name:1}},[t._v("立减")]),i("van-radio",{attrs:{name:2}},[t._v("打折")])],1)],1),1===t.coupon_method?i("li",{staticClass:"sum"},[i("div",{staticClass:"box"},[t._v(" 立减 "),i("input",{directives:[{name:"model",rawName:"v-model",value:t.deduct,expression:"deduct"}],attrs:{type:"text",placeholder:"请输入金额"},domProps:{value:t.deduct},on:{blur:function(e){return t.clearSrcoll()},input:function(e){e.target.composing||(t.deduct=e.target.value)}}}),t._v("元 ")])]):t._e(),2===t.coupon_method?i("li",{staticClass:"sum"},[i("div",{staticClass:"box"},[t._v(" 打折 "),i("input",{directives:[{name:"model",rawName:"v-model",value:t.discount,expression:"discount"}],attrs:{type:"text",placeholder:"请输入范围"},domProps:{value:t.discount},on:{blur:function(e){return t.clearSrcoll()},input:function(e){e.target.composing||(t.discount=e.target.value)}}}),t._v("折 ")])]):t._e(),i("li",[i("span",[t._v("使用范围")]),i("van-radio-group",{attrs:{"checked-color":"#f15353"},model:{value:t.use_type,callback:function(e){t.use_type=e},expression:"use_type"}},[i("van-radio",{staticStyle:{"margin-top":"5px","margin-bottom":"5px"},attrs:{name:5}},[t._v("全类适用")]),i("van-radio",{staticStyle:{"margin-bottom":"5px"},attrs:{name:1}},[t._v("指定分类适用")]),i("van-radio",{staticStyle:{"margin-bottom":"5px"},attrs:{name:2}},[t._v("指定商品")])],1)],1),i("li",{staticClass:"range"},[i("span"),1==t.use_type?i("button",{attrs:{type:"button"},on:{click:function(e){return t.inniClassify()}}},[i("i",{staticClass:"iconfont icon-life-game-plus"}),t._v("添加分类 ")]):t._e(),2==t.use_type?i("button",{attrs:{type:"button"},on:{click:function(e){return t.inniGoods()}}},[i("i",{staticClass:"iconfont icon-life-game-plus"}),t._v("添加商品 ")]):t._e()]),t.fenleirange.length>0&&1===t.use_type&&"abc"===t.Id?i("li",{staticClass:"fenleisort"},[i("span",[t._v("添加的分类")]),i("div",{staticClass:"item"},t._l(t.fenleirange,(function(e,o){return i("p",{key:o},[i("span",{staticClass:"name"},[t._v(t._s(e.name))]),i("span",{staticClass:"delete",on:{click:function(i){return t.classifyDelete(e)}}},[t._v("X")])])})),0)]):t._e(),t.category.length>0&&1===t.use_type&&"abc"!=t.Id?i("li",{staticClass:"fenleisort"},[i("span",[t._v("添加的分类")]),i("div",{staticClass:"item"},t._l(t.category,(function(e,o){return i("p",{key:o},[i("span",{staticClass:"name"},[t._v(t._s(e.name))]),i("span",{staticClass:"delete",on:{click:function(i){return t.classifyDDelete(e)}}},[t._v("X")])])})),0)]):t._e(),t.goodsrange.length>0&&2===t.use_type&&"abc"===t.Id?i("li",{staticClass:"goodssort"},[i("span",[t._v("添加的商品")]),i("div",{staticClass:"item"},t._l(t.goodsrange,(function(e,o){return i("p",{key:o},[i("span",{staticClass:"name"},[t._v(t._s(e.title))]),i("span",{staticClass:"delete",on:{click:function(i){return t.goodsDelete(e)}}},[t._v("X")])])})),0)]):t._e(),t.goods.length>0&&2===t.use_type&&"abc"!=t.Id?i("li",{staticClass:"goodssort"},[i("span",[t._v("添加的商品")]),i("div",{staticClass:"item"},t._l(t.goods,(function(e,o){return i("p",{key:o},[i("span",{staticClass:"name"},[t._v(t._s(e.title))]),i("span",{staticClass:"delete",on:{click:function(i){return t.goodsDDelete(e)}}},[t._v("X")])])})),0)]):t._e()])]),i("div",{staticClass:"coupon_receive"},[i("h1",[t._v("领取发放")]),i("ul",{staticClass:"list"},[i("li",[i("span",[t._v("可否领取")]),i("van-radio-group",{attrs:{"checked-color":"#f15353"},model:{value:t.get_type,callback:function(e){t.get_type=e},expression:"get_type"}},[i("van-radio",{attrs:{name:1}},[t._v("可以")]),i("van-radio",{attrs:{name:0}},[t._v("不可以")])],1)],1),1===t.get_type?i("li",[i("span",[t._v("限领张数")]),i("input",{directives:[{name:"model",rawName:"v-model",value:t.get_max,expression:"get_max"}],staticStyle:{"text-align":"right"},attrs:{type:"text",placeholder:"1"},domProps:{value:t.get_max},on:{blur:function(e){return t.clearSrcoll()},input:function(e){e.target.composing||(t.get_max=e.target.value)}}})]):t._e(),i("li",[i("span",[t._v("发放总数")]),i("input",{directives:[{name:"model",rawName:"v-model",value:t.total,expression:"total"}],staticStyle:{"text-align":"right"},attrs:{type:"text",placeholder:"1"},domProps:{value:t.total},on:{blur:function(e){return t.clearSrcoll()},input:function(e){e.target.composing||(t.total=e.target.value)}}})]),i("li",[i("span",[t._v("每人每日限领")]),i("input",{directives:[{name:"model",rawName:"v-model",value:t.onceTotal,expression:"onceTotal"}],staticStyle:{"text-align":"right"},attrs:{type:"text",placeholder:""},domProps:{value:t.onceTotal},on:{blur:function(e){return t.clearSrcoll()},input:function(e){e.target.composing||(t.onceTotal=e.target.value)}}})])])]),!0===t.show1?i("van-popup",{staticClass:"fenlei",attrs:{position:"bottom"},model:{value:t.show1,callback:function(e){t.show1=e},expression:"show1"}},[i("div",{staticClass:"select_box"},[i("div",{staticClass:"title"},[i("h2",[t._v("选择会员等级")]),i("i",{staticClass:"iconfont icon-guanbi",on:{click:function(e){return t.close()}}})]),i("div",{staticClass:"list"},[i("van-radio-group",{attrs:{"checked-color":"#f15353"},model:{value:t.level_limit,callback:function(e){t.level_limit=e},expression:"level_limit"}},[i("van-radio",{staticStyle:{padding:"0.5rem"},attrs:{name:-1}},[t._v("所有会员")]),t._l(t.Vip,(function(e,o){return i("van-radio",{key:o,staticStyle:{padding:"0.5rem"},attrs:{name:e.id}},[t._v("会员"+t._s(e.level_name))])}))],2)],1),i("div",{staticClass:"select_btn",on:{click:function(e){return t.finish()}}},[i("button",{attrs:{type:"button"}},[t._v("完成")])])])]):t._e(),!0===t.show2?i("van-popup",{staticClass:"fenlei",attrs:{position:"bottom"},model:{value:t.show2,callback:function(e){t.show2=e},expression:"show2"}},[i("div",{staticClass:"selectBox"},[i("div",{staticClass:"title"},[i("h2",[t._v("添加分类")]),i("i",{staticClass:"iconfont icon-guanbi",on:{click:function(e){return t.closeClassify()}}})]),i("div",[i("div",{staticClass:"input"},[i("div",{staticClass:"classify"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.keyword,expression:"keyword"}],staticClass:"sousuo",attrs:{type:"text"},domProps:{value:t.keyword},on:{blur:function(e){return t.clearSrcoll()},input:function(e){e.target.composing||(t.keyword=e.target.value)}}})]),1===t.use_type&&"abc"===t.Id?i("span",{staticClass:"classify_span",on:{click:function(e){return t.ClassifySousuo()}}},[t._v("搜索")]):t._e(),1===t.use_type&&"abc"!=t.Id?i("span",{staticClass:"classify_span",on:{click:function(e){return t.categorySousuo()}}},[t._v("搜索")]):t._e()])]),i("div",{staticClass:"list"},[i("van-checkbox-group",{model:{value:t.fenleiSelect,callback:function(e){t.fenleiSelect=e},expression:"fenleiSelect"}},t._l(t.sousuoInfo,(function(e,o){return i("van-checkbox",{key:o,attrs:{name:e,"checked-color":"#f15353"}},[t._v(t._s(e.name))])})),1)],1),"abc"===t.Id?i("div",{staticClass:"select_btn",on:{click:function(e){return t.finishClassify()}}},[i("button",{attrs:{type:"button"}},[t._v("确定")])]):t._e(),"abc"!==t.Id?i("div",{staticClass:"select_btn",on:{click:function(e){return t.finishcategory()}}},[i("button",{attrs:{type:"button"}},[t._v("确定")])]):t._e()])]):t._e(),!0===t.show3?i("van-popup",{staticClass:"fenlei",attrs:{position:"bottom"},model:{value:t.show3,callback:function(e){t.show3=e},expression:"show3"}},[i("div",{staticClass:"selectBox"},[i("div",{staticClass:"title"},[i("h2",[t._v("添加商品")]),i("i",{staticClass:"iconfont icon-guanbi",on:{click:function(e){return t.closeGoods()}}})]),i("div",[i("div",{staticClass:"input"},[i("div",{staticClass:"classify"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.goodsKeyword,expression:"goodsKeyword"}],staticClass:"sousuo",attrs:{type:"text"},domProps:{value:t.goodsKeyword},on:{blur:function(e){return t.clearSrcoll()},input:function(e){e.target.composing||(t.goodsKeyword=e.target.value)}}})]),2===t.use_type&&"abc"===t.Id?i("span",{staticClass:"classify_span",on:{click:function(e){return t.goodsousuo()}}},[t._v("搜索")]):t._e(),2===t.use_type&&"abc"!=t.Id?i("span",{staticClass:"classify_span",on:{click:function(e){return t.goodSousuo()}}},[t._v("搜索")]):t._e()])]),i("div",{staticClass:"list"},[i("van-checkbox-group",{model:{value:t.goodsSelect,callback:function(e){t.goodsSelect=e},expression:"goodsSelect"}},t._l(t.goodsSousuo,(function(e,o){return i("van-checkbox",{key:o,attrs:{name:e,"checked-color":"#f15353"}},[t._v(t._s(e.title))])})),1)],1),"abc"===t.Id?i("div",{staticClass:"select_btn",on:{click:function(e){return t.finishGoods()}}},[i("button",{attrs:{type:"button"}},[t._v("确定")])]):t._e(),"abc"!==t.Id?i("div",{staticClass:"select_btn",on:{click:function(e){return t.finishgoods()}}},[i("button",{attrs:{type:"button"}},[t._v("确定")])]):t._e()])]):t._e()],1),i("div",{attrs:{id:"btn"}},["abc"===t.Id?i("button",{attrs:{type:"button"},on:{click:function(e){return t.publishInfo()}}},[t._v("确认发布")]):i("button",{attrs:{type:"button"},on:{click:function(e){return t.editInfo()}}},[t._v("确认编辑")])])],1)},n=[],s=(i("e7e5"),i("d399")),a=(i("b0c0"),i("a434"),i("99af"),i("b64b"),i("4e82"),i("a9e3"),i("e9c4"),i("d3b7"),i("159b"),i("4de4"),i("ac1f"),i("5319"),i("6968")),c={data:function(){return{minDate:new Date,currentDate:new Date,currentDate2:new Date,taponew:!1,taptwow:!1,goodsrange:[],fenleirange:[],category:[],goods:[],goodsSelect:[],fenleiSelect:[],sousuoInfo:[],goodsSousuo:[],keyword:"",goodsKeyword:"",show3:!1,show1:!1,show2:!1,status:1,is_complex:0,level_limit:-1,coupon_method:1,display_order:"",enough:"",use_type:5,get_type:1,name:"",deduct:"",discount:"",time_days:"",time_start:"",time_end:"",dataInfo:{},get_max:1,total:1,onceTotal:"",id:"",Id:"",Vip:[],chooseVip:"所有会员",time_limit:0,headTitles:""}},activated:function(){void 0===this.$route.params.id?this.$router.go(-1):"abc"===this.$route.params.id?(this.time_start=this.timestampToTime((new Date).getTime()/1e3),this.time_end=this.timestampToTime((new Date).getTime()/1e3),this.initPageData(),this.getData(),this.Id=this.$route.params.id,this.headTitles="优惠券发布",this.fun.setWXTitle("优惠券发布")):(this.initPageData(),this.id=this.$route.params.id,this.getData(),this.headTitles="优惠券编辑",this.fun.setWXTitle("优惠券编辑"))},mounted:function(){var t=document.body.clientHeight;document.getElementById("coupon_edit").style.height=t+"px"},methods:{taponewSet:function(){this.time_start=this.timestampToTime(this.fun.getTimestamp(this.currentDate)),this.taponew=!1},taptwowSet:function(){this.time_end=this.timestampToTime(this.fun.getTimestamp(this.currentDate2)),this.taptwow=!1},clearSrcoll:function(){var t,e,i=1;e=setInterval((function(){t=document.documentElement.scrollTop||document.body.scrollTop,t-=i,window.scrollTo(0,t),t+=i,window.scrollTo(0,t),clearInterval(e)}),1)},timestampToTime:function(t){var e=new Date(1e3*t),i=e.getFullYear()+"-",o=(e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-",n=this.change(e.getDate())+" ",s=this.change(e.getHours())+":",a=this.change(e.getMinutes());return i+o+n+s+a},change:function(t){return t<10?"0"+t:t},getData:function(){var t=this,e={id:this.id};$http.get("plugin.store-cashier.frontend.store.center.coupon.edit",e,"加载中").then((function(e){if(1==e.result)if(t.id){t.dataInfo=e.data,t.status=t.dataInfo.status,t.is_complex=t.dataInfo.is_complex,t.coupon_method=t.dataInfo.coupon_method,t.use_type=t.dataInfo.use_type,t.get_type=t.dataInfo.get_type,t.level_limit=t.dataInfo.level_limit,t.enough=t.dataInfo.enough,t.display_order=t.dataInfo.display_order,t.name=t.dataInfo.name,t.time_limit=t.dataInfo.time_limit,0===t.dataInfo.time_limit&&(t.time_days=t.dataInfo.time_days),1===t.dataInfo.time_limit&&(t.time_start=t.timestampToTime(t.dataInfo.time_start),t.time_end=t.timestampToTime(t.dataInfo.time_end)),1===t.coupon_method&&(t.deduct=t.dataInfo.deduct),2===t.coupon_method&&(t.discount=t.dataInfo.discount),1==t.use_type&&(t.category=t.dataInfo.category),2==t.use_type&&(t.goods=t.dataInfo.goods),t.Vip=e.data.memberLevels,t.get_max=t.dataInfo.get_max,t.total=t.dataInfo.total;for(var i=0;i<t.Vip.length;i++)t.dataInfo.level_limit===t.Vip[i].id&&(t.chooseVip="会员".concat(t.Vip[i].level_name))}else t.Vip=e.data.memberLevels;else 0===e.result&&(e.data.url?window.location.href=e.data.url:(Object(s["a"])(e.msg),t.$router.go(-1)))}))},initPageData:function(){this.show1=!1,this.status=1,this.is_complex=0,this.level_limit=-1,this.coupon_method=1,this.display_order="",this.enough="",this.use_type=5,this.get_type=1,this.display_order="",this.name="",this.deduct="",this.discount="",this.time_days="",this.time_start=this.timestampToTime((new Date).getTime()/1e3),this.time_end=this.timestampToTime((new Date).getTime()/1e3),this.dataInfo={},this.get_max=1,this.total=1,this.id="",this.Id="",this.Vip=[],this.result=-1,this.chooseVip="所有会员",this.time_limit=0,this.classify="",this.show2=!1,this.show3=!1,this.fenleiSelect=[],this.goodsSelect=[],this.goods=[],this.category=[]},classifyDelete:function(t){for(var e=0;e<this.fenleirange.length;e++)t===this.fenleirange[e]&&this.fenleirange.splice(e,1)},goodsDelete:function(t){for(var e=0;e<this.goodsrange.length;e++)t===this.goodsrange[e]&&this.goodsrange.splice(e,1)},classifyDDelete:function(t){for(var e=0;e<this.category.length;e++)t===this.category[e]&&this.category.splice(e,1)},goodsDDelete:function(t){for(var e=0;e<this.goods.length;e++)t===this.goods[e]&&this.goods.splice(e,1)},inniClassify:function(){this.show2=!0,this.sousuoInfo=[],this.keyword=""},inniGoods:function(){this.show3=!0,this.goodsSousuo=[],this.goodsKeyword=""},close:function(){this.show1=!1},closeClassify:function(){this.show2=!1,this.fenleiSelect=[]},closeGoods:function(){this.show3=!1,this.goodsSelect=[]},finishClassify:function(){this.show2=!1,this.fenleirange=this.deteleObject(this.fenleiSelect)},finishcategory:function(){this.show2=!1,this.category=this.deteleObject(this.category.concat(this.fenleiSelect))},deteleObject:function(t){for(var e=[],i={},o=0;o<t.length;o++){var n=Object.keys(t[o]);n.sort((function(t,e){return Number(t)-Number(e)}));for(var s="",a=0;a<n.length;a++)s+=JSON.stringify(n[a]),s+=JSON.stringify(t[o][n[a]]);i.hasOwnProperty(s)||(e.push(t[o]),i[s]=!0)}return e},finishGoods:function(){this.show3=!1,this.goodsrange=this.deteleObject(this.goods.concat(this.goodsSelect))},finishgoods:function(){this.show3=!1,this.goods=this.deteleObject(this.goods.concat(this.goodsSelect))},ClassifySousuo:function(){var t=this;if(this.fun.isTextEmpty(this.keyword))Object(s["a"])("请输入搜索内容");else{var e={keyword:this.keyword};$http.post("plugin.store-cashier.frontend.store.center.coupon.getSearchCategories",e,"搜索中").then((function(e){if(1===e.result){var i=e.data,o=[];t.fenleirange.forEach((function(t){o.push(t.id)})),o.length>0?t.sousuoInfo=i.filter((function(t){return-1==o.indexOf(t.id)})):t.sousuoInfo=i}else 0===e.result&&(e.data.url?window.location.href=e.data.url:Object(s["a"])(e.msg))}))}},categorySousuo:function(){var t=this;if(this.fun.isTextEmpty(this.keyword))Object(s["a"])("请输入搜索内容");else{var e={keyword:this.keyword};$http.post("plugin.store-cashier.frontend.store.center.coupon.getSearchCategories",e,"搜索中").then((function(e){if(1===e.result){var i=e.data,o=[];t.category.forEach((function(t){o.push(t.id)})),o.length>0?t.sousuoInfo=i.filter((function(t){return-1==o.indexOf(t.id)})):t.sousuoInfo=i}else 0===e.result&&(e.data.url?window.location.href=e.data.url:Object(s["a"])(e.msg))}))}},goodsousuo:function(){var t=this;if(this.fun.isTextEmpty(this.goodsKeyword))Object(s["a"])("请输入搜索内容");else{var e={keyword:this.goodsKeyword};$http.post("plugin.store-cashier.frontend.store.center.coupon.getSearchGoods",e,"搜索中").then((function(e){if(1===e.result){var i=e.data,o=[];t.goodsrange.forEach((function(t){o.push(t.id)})),o.length>0?t.goodsSousuo=i.filter((function(t){return-1===o.indexOf(t.id)})):t.goodsSousuo=i}else 0===e.result&&(e.data.url?window.location.href=e.data.url:Object(s["a"])(e.msg))}))}},goodSousuo:function(){var t=this;if(this.fun.isTextEmpty(this.goodsKeyword))Object(s["a"])("请输入搜索内容");else{var e={keyword:this.goodsKeyword};$http.post("plugin.store-cashier.frontend.store.center.coupon.getSearchGoods",e,"搜索中").then((function(e){if(1===e.result){var i=e.data,o=[];t.goods.forEach((function(t){o.push(t.id)})),o.length>0?t.goodsSousuo=i.filter((function(t){return-1===o.indexOf(t.id)})):t.goodsSousuo=i}else 0===e.result&&(e.data.url?window.location.href=e.data.url:Object(s["a"])(e.msg))}))}},finish:function(){if(this.show1=!1,-1==this.level_limit)this.chooseVip="所有会员";else for(var t=0;t<this.Vip.length;t++)this.level_limit==this.Vip[t].id&&(this.chooseVip="会员".concat(this.Vip[t].level_name))},publishInfo:function(){var t=this;if(this.fun.isTextEmpty(this.display_order)||isNaN(this.display_order))Object(s["a"])("请输入排序方法，必须是整数");else if(this.fun.isTextEmpty(this.name))Object(s["a"])("请输入优惠券名称");else if(this.fun.isTextEmpty(this.enough)||isNaN(this.enough))Object(s["a"])("请输入订单金额,必须是整数");else if(0==this.time_limit&&this.fun.isTextEmpty(this.time_days))Object(s["a"])("请输入有效天数");else{if(1==this.time_limit)if("android"==this.fun.isIosOrAndroid()){if(new Date(this.time_start).getTime()>=new Date(this.time_end).getTime())return void Object(s["a"])("开始时间必须早于结束时间")}else if(parseInt(Date.parse(new Date(this.time_start.replace(/-/g,"/"))))>=parseInt(Date.parse(new Date(this.time_end.replace(/-/g,"/")))))return void Object(s["a"])("开始时间必须早于结束时间");if(1===this.coupon_method){if(this.fun.isTextEmpty(this.deduct))return void Object(s["a"])("请输入立减金额");if(Number(this.deduct<1))return void Object(s["a"])("金额必须大于1");if(Number(this.deduct)>Number(this.enough)){var e="输入的金额必须介于1-".concat(this.enough,"之间");return void Object(s["a"])(e)}}if(2===this.coupon_method){if(this.fun.isTextEmpty(this.discount))return void Object(s["a"])("请输入折扣数");if(Number(this.discount<1)||Number(this.discount>=10)){var i="折扣数必须介于1-9之间";return void Object(s["a"])(i)}}if(1===this.use_type&&this.fenleirange.length<=0)Object(s["a"])("分类不能为空");else if(2===this.use_type&&this.goodsrange.length<=0)Object(s["a"])("商品不能为空");else{if(1===this.get_type){if(this.fun.isTextEmpty(this.get_max))return void Object(s["a"])("请输入限领张数");if(this.fun.isTextEmpty(this.total))return void Object(s["a"])("请输入发放总数")}if(0===this.get_type&&this.fun.isTextEmpty(this.total))Object(s["a"])("请输入发放总数");else{var o={name:this.name,get_type:this.get_type,level_limit:this.level_limit,use_type:this.use_type,enough:this.enough,time_limit:this.time_limit,time_days:this.time_days,time_start:"android"==this.fun.isIosOrAndroid()?String(new Date(this.time_start).getTime()).substring(0,10):String(parseInt(Date.parse(new Date(this.time_start.replace(/-/g,"/"))))).substring(0,10),time_end:"android"==this.fun.isIosOrAndroid()?String(new Date(this.time_end).getTime()).substring(0,10):String(parseInt(Date.parse(new Date(this.time_end.replace(/-/g,"/"))))).substring(0,10),coupon_method:this.coupon_method,discount:this.discount,deduct:this.deduct,total:this.total,status:this.status,display_order:this.display_order,is_complex:this.is_complex,get_max:this.get_max,category:this.fenleirange,goods:this.goodsrange,get_limit_max:this.onceTotal?this.onceTotal:0};$http.post("plugin.store-cashier.frontend.store.center.coupon.save",{coupon:o},"发布中").then((function(e){1===e.result?(Object(s["a"])("发布成功"),t.$router.push(t.fun.getUrl("CommodityCoupon"))):0===e.result&&(e.data.url?window.location.href=e.data.url:Object(s["a"])(e.msg))}))}}}},editInfo:function(){var t=this;if(this.fun.isTextEmpty(this.display_order)||isNaN(this.display_order))Object(s["a"])("请输入排序方法，必须是整数");else if(this.fun.isTextEmpty(this.name))Object(s["a"])("请输入优惠券名称");else if(this.fun.isTextEmpty(this.enough)||isNaN(this.enough))Object(s["a"])("请输入订单金额,必须是整数");else if(0==this.time_limit&&this.fun.isTextEmpty(this.time_days))Object(s["a"])("请输入有效天数");else{if(1==this.time_limit)if("android"==this.fun.isIosOrAndroid()){if(new Date(this.time_start).getTime()>=new Date(this.time_end).getTime())return void Object(s["a"])("开始时间必须早于结束时间")}else if(parseInt(Date.parse(new Date(this.time_start.replace(/-/g,"/"))))>=parseInt(Date.parse(new Date(this.time_end.replace(/-/g,"/")))))return void Object(s["a"])("开始时间必须早于结束时间");if(1===this.coupon_method){if(this.fun.isTextEmpty(this.deduct))return void Object(s["a"])("请输入立减金额");if(this.deduct<1)return void Object(s["a"])("金额必须大于1");if(this.deduct>this.enough){var e="输入的金额必须介于1-".concat(this.enough,"之间");return void Object(s["a"])(e)}}if(2===this.coupon_method){if(this.fun.isTextEmpty(this.discount))return void Object(s["a"])("请输入折扣数");if(this.discount<1||this.discount>10){var i="折扣数必须介于1-10之间";return void Object(s["a"])(i)}}if(1===this.use_type&&this.category.length<=0)Object(s["a"])("分类不能为空");else if(2===this.use_type&&this.goods.length<=0)Object(s["a"])("商品不能为空");else{if(1===this.get_type){if(this.fun.isTextEmpty(this.get_max))return void Object(s["a"])("请输入限领张数");if(this.fun.isTextEmpty(this.total))return void Object(s["a"])("请输入发放总数")}if(0===this.get_type&&this.fun.isTextEmpty(this.total))Object(s["a"])("请输入发放总数");else{var o={id:this.id,name:this.name,get_type:this.get_type,level_limit:this.level_limit,use_type:this.use_type,enough:this.enough,time_limit:this.time_limit,time_days:this.time_days,time_start:"android"==this.fun.isIosOrAndroid()?String(new Date(this.time_start).getTime()).substring(0,10):String(parseInt(Date.parse(new Date(this.time_start.replace(/-/g,"/"))))).substring(0,10),time_end:"android"==this.fun.isIosOrAndroid()?String(new Date(this.time_end).getTime()).substring(0,10):String(parseInt(Date.parse(new Date(this.time_end.replace(/-/g,"/"))))).substring(0,10),coupon_method:this.coupon_method,discount:this.discount,deduct:this.deduct,total:this.total,status:this.status,display_order:this.display_order,is_complex:this.is_complex,get_max:this.get_max,category:this.category,goods:this.goods,get_limit_max:this.onceTotal?this.onceTotal:0};$http.post("plugin.store-cashier.frontend.store.center.coupon.save",{coupon:o},"保存中").then((function(e){1===e.result?(Object(s["a"])("保存成功"),t.$router.push(t.fun.getUrl("CommodityCoupon"))):0===e.result&&(e.data.url?window.location.href=e.data.url:Object(s["a"])(e.msg))}))}}}}},components:{cTitle:a["a"]}},r=c,l=r,d=(i("6e55"),i("2877")),u=Object(d["a"])(l,o,n,!1,null,"9721ce2c",null);e["default"]=u.exports}}]);
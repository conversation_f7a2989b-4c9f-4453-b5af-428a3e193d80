(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d224cb5"],{e23e:function(e,t,n){"use strict";(function(o){var r,a,s=n("7037").default;n("6c57"),n("e439"),n("d3b7"),n("fb6a"),n("ac1f"),n("1276"),n("d9e2"),n("25f0"),n("00b4"),n("a15b"),n("99af"),n("7039"),n("a4d3"),n("5319"),n("e01a"),n("d28b"),n("3ca3"),n("ddb0"),n("466d"),n("159b"),n("a630"),n("b64b"),n("4d63"),n("c607"),n("2c3e"),n("498a"),n("3410"),n("131a"),n("b0c0"),n("4de4"),n("dbb4"),n("f8c9"),n("4ae1"),n("4ec9"),n("d81d"),n("6062"),n("10d1"),n("1fe2"),n("ace4"),n("944a"),n("0c47"),n("23dc"),n("7db0"),n("caad"),n("2532"),n("38cf"),n("5377"),n("a9e3"),n("c906"),n("cee85"),n("e9c4"),n("2ca0"),n("2b3d"),n("9861"),n("4e82"),n("a434"),n("841c"),n("07ac"),n("c740"),n("f5b2"),n("c19f"),n("f6d6"),n("a874"),n("fd87"),n("907a"),n("9a8c"),n("a975"),n("735e"),n("c1ac"),n("d139"),n("3a7b"),n("d5d6"),n("82f8"),n("e91f"),n("60bd"),n("5f96"),n("3280"),n("3fcc"),n("ca91"),n("25a1"),n("cd26"),n("3c5d"),n("2954"),n("649e"),n("219c"),n("170b"),n("b39a"),n("72f7"),n("5cc6"),function(o,i){"object"==s(t)&&"undefined"!=typeof e?e.exports=i():(r=i,a="function"===typeof r?r.call(t,n,t,e):r,void 0===a||(e.exports=a))}(0,(function(){var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof o?o:"undefined"!=typeof self?self:{};function t(e,t){return e(t={exports:{}},t.exports),t.exports}var n=function(e){return e&&e.Math==Math&&e},r=n("object"==("undefined"===typeof globalThis?"undefined":s(globalThis))&&globalThis)||n("object"==("undefined"===typeof window?"undefined":s(window))&&window)||n("object"==("undefined"===typeof self?"undefined":s(self))&&self)||n("object"==s(e)&&e)||Function("return this")(),a=function(e){try{return!!e()}catch(t){return!0}},i=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),u={}.propertyIsEnumerable,c=Object.getOwnPropertyDescriptor,l={f:c&&!u.call({1:2},1)?function(e){var t=c(this,e);return!!t&&t.enumerable}:u},d=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},p={}.toString,g=function(e){return p.call(e).slice(8,-1)},h="".split,f=a((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==g(e)?h.call(e,""):Object(e)}:Object,_=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},m=function(e){return f(_(e))},v=function(e){return"object"==s(e)?null!==e:"function"==typeof e},M=function(e,t){if(!v(e))return e;var n,o;if(t&&"function"==typeof(n=e.toString)&&!v(o=n.call(e)))return o;if("function"==typeof(n=e.valueOf)&&!v(o=n.call(e)))return o;if(!t&&"function"==typeof(n=e.toString)&&!v(o=n.call(e)))return o;throw TypeError("Can't convert object to primitive value")},y={}.hasOwnProperty,I=function(e,t){return y.call(e,t)},T=r.document,C=v(T)&&v(T.createElement),S=function(e){return C?T.createElement(e):{}},A=!i&&!a((function(){return 7!=Object.defineProperty(S("div"),"a",{get:function(){return 7}}).a})),E=Object.getOwnPropertyDescriptor,k={f:i?E:function(e,t){if(e=m(e),t=M(t,!0),A)try{return E(e,t)}catch(n){}if(I(e,t))return d(!l.f.call(e,t),e[t])}},D=function(e){if(!v(e))throw TypeError(String(e)+" is not an object");return e},N=Object.defineProperty,O={f:i?N:function(e,t,n){if(D(e),t=M(t,!0),D(n),A)try{return N(e,t,n)}catch(r){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},L=i?function(e,t,n){return O.f(e,t,d(1,n))}:function(e,t,n){return e[t]=n,e},R=function(e,t){try{L(r,e,t)}catch(n){r[e]=t}return t},b=r["__core-js_shared__"]||R("__core-js_shared__",{}),w=Function.toString;"function"!=typeof b.inspectSource&&(b.inspectSource=function(e){return w.call(e)});var G,P,U,F=b.inspectSource,q=r.WeakMap,x="function"==typeof q&&/native code/.test(F(q)),V=t((function(e){(e.exports=function(e,t){return b[e]||(b[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.6.5",mode:"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})})),K=0,B=Math.random(),H=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++K+B).toString(36)},j=V("keys"),W=function(e){return j[e]||(j[e]=H(e))},$={},Y=r.WeakMap;if(x){var z=new Y,J=z.get,X=z.has,Q=z.set;G=function(e,t){return Q.call(z,e,t),t},P=function(e){return J.call(z,e)||{}},U=function(e){return X.call(z,e)}}else{var Z=W("state");$[Z]=!0,G=function(e,t){return L(e,Z,t),t},P=function(e){return I(e,Z)?e[Z]:{}},U=function(e){return I(e,Z)}}var ee,te,ne={set:G,get:P,has:U,enforce:function(e){return U(e)?P(e):G(e,{})},getterFor:function(e){return function(t){var n;if(!v(t)||(n=P(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return n}}},oe=t((function(e){var t=ne.get,n=ne.enforce,o=String(String).split("String");(e.exports=function(e,t,a,s){var i=!!s&&!!s.unsafe,u=!!s&&!!s.enumerable,c=!!s&&!!s.noTargetGet;"function"==typeof a&&("string"!=typeof t||I(a,"name")||L(a,"name",t),n(a).source=o.join("string"==typeof t?t:"")),e!==r?(i?!c&&e[t]&&(u=!0):delete e[t],u?e[t]=a:L(e,t,a)):u?e[t]=a:R(t,a)})(Function.prototype,"toString",(function(){return"function"==typeof this&&t(this).source||F(this)}))})),re=r,ae=function(e){return"function"==typeof e?e:void 0},se=function(e,t){return arguments.length<2?ae(re[e])||ae(r[e]):re[e]&&re[e][t]||r[e]&&r[e][t]},ie=Math.ceil,ue=Math.floor,ce=function(e){return isNaN(e=+e)?0:(e>0?ue:ie)(e)},le=Math.min,de=function(e){return e>0?le(ce(e),9007199254740991):0},pe=Math.max,ge=Math.min,he=function(e,t){var n=ce(e);return n<0?pe(n+t,0):ge(n,t)},fe=function(e){return function(t,n,o){var r,a=m(t),s=de(a.length),i=he(o,s);if(e&&n!=n){for(;s>i;)if((r=a[i++])!=r)return!0}else for(;s>i;i++)if((e||i in a)&&a[i]===n)return e||i||0;return!e&&-1}},_e={includes:fe(!0),indexOf:fe(!1)},me=_e.indexOf,ve=function(e,t){var n,o=m(e),r=0,a=[];for(n in o)!I($,n)&&I(o,n)&&a.push(n);for(;t.length>r;)I(o,n=t[r++])&&(~me(a,n)||a.push(n));return a},Me=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],ye=Me.concat("length","prototype"),Ie={f:Object.getOwnPropertyNames||function(e){return ve(e,ye)}},Te={f:Object.getOwnPropertySymbols},Ce=se("Reflect","ownKeys")||function(e){var t=Ie.f(D(e)),n=Te.f;return n?t.concat(n(e)):t},Se=function(e,t){for(var n=Ce(t),o=O.f,r=k.f,a=0;a<n.length;a++){var s=n[a];I(e,s)||o(e,s,r(t,s))}},Ae=/#|\.prototype\./,Ee=function(e,t){var n=De[ke(e)];return n==Oe||n!=Ne&&("function"==typeof t?a(t):!!t)},ke=Ee.normalize=function(e){return String(e).replace(Ae,".").toLowerCase()},De=Ee.data={},Ne=Ee.NATIVE="N",Oe=Ee.POLYFILL="P",Le=Ee,Re=k.f,be=function(e,t){var n,o,a,i,u,c=e.target,l=e.global,d=e.stat;if(n=l?r:d?r[c]||R(c,{}):(r[c]||{}).prototype)for(o in t){if(i=t[o],a=e.noTargetGet?(u=Re(n,o))&&u.value:n[o],!Le(l?o:c+(d?".":"#")+o,e.forced)&&void 0!==a){if(s(i)==s(a))continue;Se(i,a)}(e.sham||a&&a.sham)&&L(i,"sham",!0),oe(n,o,i,e)}},we=Array.isArray||function(e){return"Array"==g(e)},Ge=function(e){return Object(_(e))},Pe=function(e,t,n){var o=M(t);o in e?O.f(e,o,d(0,n)):e[o]=n},Ue=!!Object.getOwnPropertySymbols&&!a((function(){return!String(Symbol())})),Fe=Ue&&!Symbol.sham&&"symbol"==s(Symbol.iterator),qe=V("wks"),xe=r.Symbol,Ve=Fe?xe:xe&&xe.withoutSetter||H,Ke=function(e){return I(qe,e)||(Ue&&I(xe,e)?qe[e]=xe[e]:qe[e]=Ve("Symbol."+e)),qe[e]},Be=Ke("species"),He=function(e,t){var n;return we(e)&&("function"!=typeof(n=e.constructor)||n!==Array&&!we(n.prototype)?v(n)&&null===(n=n[Be])&&(n=void 0):n=void 0),new(void 0===n?Array:n)(0===t?0:t)},je=se("navigator","userAgent")||"",We=r.process,$e=We&&We.versions,Ye=$e&&$e.v8;Ye?te=(ee=Ye.split("."))[0]+ee[1]:je&&(!(ee=je.match(/Edge\/(\d+)/))||ee[1]>=74)&&(ee=je.match(/Chrome\/(\d+)/))&&(te=ee[1]);var ze=te&&+te,Je=Ke("species"),Xe=function(e){return ze>=51||!a((function(){var t=[];return(t.constructor={})[Je]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},Qe=Ke("isConcatSpreadable"),Ze=ze>=51||!a((function(){var e=[];return e[Qe]=!1,e.concat()[0]!==e})),et=Xe("concat"),nt=function(e){if(!v(e))return!1;var t=e[Qe];return void 0!==t?!!t:we(e)};be({target:"Array",proto:!0,forced:!Ze||!et},{concat:function(e){var t,n,o,r,a,s=Ge(this),i=He(s,0),u=0;for(t=-1,o=arguments.length;t<o;t++)if(nt(a=-1===t?s:arguments[t])){if(u+(r=de(a.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(n=0;n<r;n++,u++)n in a&&Pe(i,u,a[n])}else{if(u>=9007199254740991)throw TypeError("Maximum allowed index exceeded");Pe(i,u++,a)}return i.length=u,i}});var ot=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e},rt=function(e,t,n){if(ot(e),void 0===t)return e;switch(n){case 0:return function(){return e.call(t)};case 1:return function(n){return e.call(t,n)};case 2:return function(n,o){return e.call(t,n,o)};case 3:return function(n,o,r){return e.call(t,n,o,r)}}return function(){return e.apply(t,arguments)}},at=[].push,st=function(e){var t=1==e,n=2==e,o=3==e,r=4==e,a=6==e,s=5==e||a;return function(i,u,c,l){for(var d,p,g=Ge(i),h=f(g),_=rt(u,c,3),m=de(h.length),v=0,M=l||He,y=t?M(i,m):n?M(i,0):void 0;m>v;v++)if((s||v in h)&&(p=_(d=h[v],v,g),e))if(t)y[v]=p;else if(p)switch(e){case 3:return!0;case 5:return d;case 6:return v;case 2:at.call(y,d)}else if(r)return!1;return a?-1:o||r?r:y}},it={forEach:st(0),map:st(1),filter:st(2),some:st(3),every:st(4),find:st(5),findIndex:st(6)},ut=function(e,t){var n=[][e];return!!n&&a((function(){n.call(null,t||function(){throw 1},1)}))},ct=Object.defineProperty,lt={},dt=function(e){throw e},pt=function(e,t){if(I(lt,e))return lt[e];t||(t={});var n=[][e],o=!!I(t,"ACCESSORS")&&t.ACCESSORS,r=I(t,0)?t[0]:dt,s=I(t,1)?t[1]:void 0;return lt[e]=!!n&&!a((function(){if(o&&!i)return!0;var e={length:-1};o?ct(e,1,{enumerable:!0,get:dt}):e[1]=1,n.call(e,r,s)}))},gt=it.forEach,ht=ut("forEach"),ft=pt("forEach"),_t=ht&&ft?[].forEach:function(e){return gt(this,e,arguments.length>1?arguments[1]:void 0)};be({target:"Array",proto:!0,forced:[].forEach!=_t},{forEach:_t});var mt=function(e,t,n,o){try{return o?t(D(n)[0],n[1]):t(n)}catch(i){var r=e.return;throw void 0!==r&&D(r.call(e)),i}},vt={},Mt=Ke("iterator"),yt=Array.prototype,It=function(e){return void 0!==e&&(vt.Array===e||yt[Mt]===e)},Tt={};Tt[Ke("toStringTag")]="z";var Ct="[object z]"===String(Tt),St=Ke("toStringTag"),At="Arguments"==g(function(){return arguments}()),Et=Ct?g:function(e){var t,n,o;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(n){}}(t=Object(e),St))?n:At?g(t):"Object"==(o=g(t))&&"function"==typeof t.callee?"Arguments":o},kt=Ke("iterator"),Dt=function(e){if(null!=e)return e[kt]||e["@@iterator"]||vt[Et(e)]},Nt=function(e){var t,n,o,r,a,s,i=Ge(e),u="function"==typeof this?this:Array,c=arguments.length,l=c>1?arguments[1]:void 0,d=void 0!==l,p=Dt(i),g=0;if(d&&(l=rt(l,c>2?arguments[2]:void 0,2)),null==p||u==Array&&It(p))for(n=new u(t=de(i.length));t>g;g++)s=d?l(i[g],g):i[g],Pe(n,g,s);else for(a=(r=p.call(i)).next,n=new u;!(o=a.call(r)).done;g++)s=d?mt(r,l,[o.value,g],!0):o.value,Pe(n,g,s);return n.length=g,n},Ot=Ke("iterator"),Lt=!1;try{var Rt=0,bt={next:function(){return{done:!!Rt++}},return:function(){Lt=!0}};bt[Ot]=function(){return this},Array.from(bt,(function(){throw 2}))}catch(eT){}var wt=function(e,t){if(!t&&!Lt)return!1;var n=!1;try{var o={};o[Ot]=function(){return{next:function(){return{done:n=!0}}}},e(o)}catch(eT){}return n},Gt=!wt((function(e){Array.from(e)}));be({target:"Array",stat:!0,forced:Gt},{from:Nt});var Pt,Ut=Object.keys||function(e){return ve(e,Me)},Ft=i?Object.defineProperties:function(e,t){D(e);for(var n,o=Ut(t),r=o.length,a=0;r>a;)O.f(e,n=o[a++],t[n]);return e},qt=se("document","documentElement"),xt=W("IE_PROTO"),Vt=function(){},Kt=function(e){return"<script>"+e+"<\/script>"},Bt=function(){try{Pt=document.domain&&new ActiveXObject("htmlfile")}catch(eT){}var e,t;Bt=Pt?function(e){e.write(Kt("")),e.close();var t=e.parentWindow.Object;return e=null,t}(Pt):((t=S("iframe")).style.display="none",qt.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(Kt("document.F=Object")),e.close(),e.F);for(var n=Me.length;n--;)delete Bt.prototype[Me[n]];return Bt()};$[xt]=!0;var Ht=Object.create||function(e,t){var n;return null!==e?(Vt.prototype=D(e),n=new Vt,Vt.prototype=null,n[xt]=e):n=Bt(),void 0===t?n:Ft(n,t)};be({target:"Object",stat:!0,sham:!i},{create:Ht});var jt=a((function(){Ut(1)}));be({target:"Object",stat:!0,forced:jt},{keys:function(e){return Ut(Ge(e))}});var Wt="\t\n\v\f\r                　\u2028\u2029\ufeff",$t="["+Wt+"]",Yt=RegExp("^"+$t+$t+"*"),zt=RegExp($t+$t+"*$"),Jt=function(e){return function(t){var n=String(_(t));return 1&e&&(n=n.replace(Yt,"")),2&e&&(n=n.replace(zt,"")),n}},Xt={start:Jt(1),end:Jt(2),trim:Jt(3)},Qt=Xt.trim,Zt=r.parseInt,en=/^[+-]?0[Xx]/,tn=8!==Zt(Wt+"08")||22!==Zt(Wt+"0x16")?function(e,t){var n=Qt(String(e));return Zt(n,t>>>0||(en.test(n)?16:10))}:Zt;be({global:!0,forced:parseInt!=tn},{parseInt:tn});var nn,on,rn,an=function(e){return function(t,n){var o,r,a=String(_(t)),s=ce(n),i=a.length;return s<0||s>=i?e?"":void 0:(o=a.charCodeAt(s))<55296||o>56319||s+1===i||(r=a.charCodeAt(s+1))<56320||r>57343?e?a.charAt(s):o:e?a.slice(s,s+2):r-56320+(o-55296<<10)+65536}},sn={codeAt:an(!1),charAt:an(!0)},un=!a((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),cn=W("IE_PROTO"),ln=Object.prototype,dn=un?Object.getPrototypeOf:function(e){return e=Ge(e),I(e,cn)?e[cn]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?ln:null},pn=Ke("iterator"),gn=!1;[].keys&&("next"in(rn=[].keys())?(on=dn(dn(rn)))!==Object.prototype&&(nn=on):gn=!0),null==nn&&(nn={}),I(nn,pn)||L(nn,pn,(function(){return this}));var hn={IteratorPrototype:nn,BUGGY_SAFARI_ITERATORS:gn},fn=O.f,_n=Ke("toStringTag"),mn=function(e,t,n){e&&!I(e=n?e:e.prototype,_n)&&fn(e,_n,{configurable:!0,value:t})},vn=hn.IteratorPrototype,Mn=function(){return this},yn=function(e,t,n){var o=t+" Iterator";return e.prototype=Ht(vn,{next:d(1,n)}),mn(e,o,!1),vt[o]=Mn,e},In=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(n,[]),t=n instanceof Array}catch(eT){}return function(n,o){return D(n),function(e){if(!v(e)&&null!==e)throw TypeError("Can't set "+String(e)+" as a prototype")}(o),t?e.call(n,o):n.__proto__=o,n}}():void 0),Tn=hn.IteratorPrototype,Cn=hn.BUGGY_SAFARI_ITERATORS,Sn=Ke("iterator"),An=function(){return this},En=function(e,t,n,o,r,a,s){yn(n,t,o);var i,u,c,l=function(e){if(e===r&&f)return f;if(!Cn&&e in g)return g[e];switch(e){case"keys":case"values":case"entries":return function(){return new n(this,e)}}return function(){return new n(this)}},d=t+" Iterator",p=!1,g=e.prototype,h=g[Sn]||g["@@iterator"]||r&&g[r],f=!Cn&&h||l(r),_="Array"==t&&g.entries||h;if(_&&(i=dn(_.call(new e)),Tn!==Object.prototype&&i.next&&(dn(i)!==Tn&&(In?In(i,Tn):"function"!=typeof i[Sn]&&L(i,Sn,An)),mn(i,d,!0))),"values"==r&&h&&"values"!==h.name&&(p=!0,f=function(){return h.call(this)}),g[Sn]!==f&&L(g,Sn,f),vt[t]=f,r)if(u={values:l("values"),keys:a?f:l("keys"),entries:l("entries")},s)for(c in u)(Cn||p||!(c in g))&&oe(g,c,u[c]);else be({target:t,proto:!0,forced:Cn||p},u);return u},kn=sn.charAt,Dn=ne.set,Nn=ne.getterFor("String Iterator");En(String,"String",(function(e){Dn(this,{type:"String Iterator",string:String(e),index:0})}),(function(){var e,t=Nn(this),n=t.string,o=t.index;return o>=n.length?{value:void 0,done:!0}:(e=kn(n,o),t.index+=e.length,{value:e,done:!1})}));var On={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0};for(var Ln in On){var Rn=r[Ln],bn=Rn&&Rn.prototype;if(bn&&bn.forEach!==_t)try{L(bn,"forEach",_t)}catch(eT){bn.forEach=_t}}function wn(e){return(wn="function"==typeof Symbol&&"symbol"==s(Symbol.iterator)?function(e){return s(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":s(e)})(e)}function Gn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Pn(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function Un(e,t,n){return t&&Pn(e.prototype,t),n&&Pn(e,n),e}function Fn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function qn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function xn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?qn(Object(n),!0).forEach((function(t){Fn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):qn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Vn(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Bn(e,t)}function Kn(e){return(Kn=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Bn(e,t){return(Bn=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Hn(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function jn(e,t,n){return(jn=Hn()?Reflect.construct:function(e,t,n){var o=[null];o.push.apply(o,t);var r=new(Function.bind.apply(e,o));return n&&Bn(r,n.prototype),r}).apply(null,arguments)}function Wn(e){var t="function"==typeof Map?new Map:void 0;return(Wn=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,o)}function o(){return jn(e,arguments,Kn(this).constructor)}return o.prototype=Object.create(e.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),Bn(o,e)})(e)}function $n(e,t){if(null==e)return{};var n,o,r=function(e,t){if(null==e)return{};var n,o,r={},a=Object.keys(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Yn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function zn(e,t){return!t||"object"!=s(t)&&"function"!=typeof t?Yn(e):t}function Jn(e){var t=Hn();return function(){var n,o=Kn(e);if(t){var r=Kn(this).constructor;n=Reflect.construct(o,arguments,r)}else n=o.apply(this,arguments);return zn(this,n)}}function Xn(e,t){return Zn(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var n=[],o=!0,r=!1,a=void 0;try{for(var s,i=e[Symbol.iterator]();!(o=(s=i.next()).done)&&(n.push(s.value),!t||n.length!==t);o=!0);}catch(l){r=!0,a=l}finally{try{o||null==i.return||i.return()}finally{if(r)throw a}}return n}}(e,t)||to(e,t)||oo()}function Qn(e){return function(e){if(Array.isArray(e))return no(e)}(e)||eo(e)||to(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Zn(e){if(Array.isArray(e))return e}function eo(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function to(e,t){if(e){if("string"==typeof e)return no(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?no(e,t):void 0}}function no(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function oo(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ro(e,t){var n;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=to(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var o=0,r=function(){};return{s:r,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,i=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return s=e.done,e},e:function(e){i=!0,a=e},f:function(){try{s||null==n.return||n.return()}finally{if(i)throw a}}}}var ao={SDK_READY:"sdkStateReady",SDK_NOT_READY:"sdkStateNotReady",SDK_DESTROY:"sdkDestroy",MESSAGE_RECEIVED:"onMessageReceived",MESSAGE_MODIFIED:"onMessageModified",MESSAGE_REVOKED:"onMessageRevoked",MESSAGE_READ_BY_PEER:"onMessageReadByPeer",CONVERSATION_LIST_UPDATED:"onConversationListUpdated",GROUP_LIST_UPDATED:"onGroupListUpdated",GROUP_SYSTEM_NOTICE_RECEIVED:"receiveGroupSystemNotice",GROUP_ATTRIBUTES_UPDATED:"groupAttributesUpdated",PROFILE_UPDATED:"onProfileUpdated",BLACKLIST_UPDATED:"blacklistUpdated",FRIEND_LIST_UPDATED:"onFriendListUpdated",FRIEND_GROUP_LIST_UPDATED:"onFriendGroupListUpdated",FRIEND_APPLICATION_LIST_UPDATED:"onFriendApplicationListUpdated",KICKED_OUT:"kickedOut",ERROR:"error",NET_STATE_CHANGE:"netStateChange",SDK_RELOAD:"sdkReload"},so={MSG_TEXT:"TIMTextElem",MSG_IMAGE:"TIMImageElem",MSG_SOUND:"TIMSoundElem",MSG_AUDIO:"TIMSoundElem",MSG_FILE:"TIMFileElem",MSG_FACE:"TIMFaceElem",MSG_VIDEO:"TIMVideoFileElem",MSG_GEO:"TIMLocationElem",MSG_LOCATION:"TIMLocationElem",MSG_GRP_TIP:"TIMGroupTipElem",MSG_GRP_SYS_NOTICE:"TIMGroupSystemNoticeElem",MSG_CUSTOM:"TIMCustomElem",MSG_MERGER:"TIMRelayElem",MSG_PRIORITY_HIGH:"High",MSG_PRIORITY_NORMAL:"Normal",MSG_PRIORITY_LOW:"Low",MSG_PRIORITY_LOWEST:"Lowest",CONV_C2C:"C2C",CONV_GROUP:"GROUP",CONV_SYSTEM:"@TIM#SYSTEM",CONV_AT_ME:1,CONV_AT_ALL:2,CONV_AT_ALL_AT_ME:3,GRP_PRIVATE:"Private",GRP_WORK:"Private",GRP_PUBLIC:"Public",GRP_CHATROOM:"ChatRoom",GRP_MEETING:"ChatRoom",GRP_AVCHATROOM:"AVChatRoom",GRP_MBR_ROLE_OWNER:"Owner",GRP_MBR_ROLE_ADMIN:"Admin",GRP_MBR_ROLE_MEMBER:"Member",GRP_TIP_MBR_JOIN:1,GRP_TIP_MBR_QUIT:2,GRP_TIP_MBR_KICKED_OUT:3,GRP_TIP_MBR_SET_ADMIN:4,GRP_TIP_MBR_CANCELED_ADMIN:5,GRP_TIP_GRP_PROFILE_UPDATED:6,GRP_TIP_MBR_PROFILE_UPDATED:7,MSG_REMIND_ACPT_AND_NOTE:"AcceptAndNotify",MSG_REMIND_ACPT_NOT_NOTE:"AcceptNotNotify",MSG_REMIND_DISCARD:"Discard",GENDER_UNKNOWN:"Gender_Type_Unknown",GENDER_FEMALE:"Gender_Type_Female",GENDER_MALE:"Gender_Type_Male",KICKED_OUT_MULT_ACCOUNT:"multipleAccount",KICKED_OUT_MULT_DEVICE:"multipleDevice",KICKED_OUT_USERSIG_EXPIRED:"userSigExpired",ALLOW_TYPE_ALLOW_ANY:"AllowType_Type_AllowAny",ALLOW_TYPE_NEED_CONFIRM:"AllowType_Type_NeedConfirm",ALLOW_TYPE_DENY_ANY:"AllowType_Type_DenyAny",FORBID_TYPE_NONE:"AdminForbid_Type_None",FORBID_TYPE_SEND_OUT:"AdminForbid_Type_SendOut",JOIN_OPTIONS_FREE_ACCESS:"FreeAccess",JOIN_OPTIONS_NEED_PERMISSION:"NeedPermission",JOIN_OPTIONS_DISABLE_APPLY:"DisableApply",JOIN_STATUS_SUCCESS:"JoinedSuccess",JOIN_STATUS_ALREADY_IN_GROUP:"AlreadyInGroup",JOIN_STATUS_WAIT_APPROVAL:"WaitAdminApproval",GRP_PROFILE_OWNER_ID:"ownerID",GRP_PROFILE_CREATE_TIME:"createTime",GRP_PROFILE_LAST_INFO_TIME:"lastInfoTime",GRP_PROFILE_MEMBER_NUM:"memberNum",GRP_PROFILE_MAX_MEMBER_NUM:"maxMemberNum",GRP_PROFILE_JOIN_OPTION:"joinOption",GRP_PROFILE_INTRODUCTION:"introduction",GRP_PROFILE_NOTIFICATION:"notification",GRP_PROFILE_MUTE_ALL_MBRS:"muteAllMembers",SNS_ADD_TYPE_SINGLE:"Add_Type_Single",SNS_ADD_TYPE_BOTH:"Add_Type_Both",SNS_DELETE_TYPE_SINGLE:"Delete_Type_Single",SNS_DELETE_TYPE_BOTH:"Delete_Type_Both",SNS_APPLICATION_TYPE_BOTH:"Pendency_Type_Both",SNS_APPLICATION_SENT_TO_ME:"Pendency_Type_ComeIn",SNS_APPLICATION_SENT_BY_ME:"Pendency_Type_SendOut",SNS_APPLICATION_AGREE:"Response_Action_Agree",SNS_APPLICATION_AGREE_AND_ADD:"Response_Action_AgreeAndAdd",SNS_CHECK_TYPE_BOTH:"CheckResult_Type_Both",SNS_CHECK_TYPE_SINGLE:"CheckResult_Type_Single",SNS_TYPE_NO_RELATION:"CheckResult_Type_NoRelation",SNS_TYPE_A_WITH_B:"CheckResult_Type_AWithB",SNS_TYPE_B_WITH_A:"CheckResult_Type_BWithA",SNS_TYPE_BOTH_WAY:"CheckResult_Type_BothWay",NET_STATE_CONNECTED:"connected",NET_STATE_CONNECTING:"connecting",NET_STATE_DISCONNECTED:"disconnected",MSG_AT_ALL:"__kImSDK_MesssageAtALL__",READ_ALL_C2C_MSG:"readAllC2CMessage",READ_ALL_GROUP_MSG:"readAllGroupMessage",READ_ALL_MSG:"readAllMessage"},io=it.map,uo=Xe("map"),co=pt("map");be({target:"Array",proto:!0,forced:!uo||!co},{map:function(e){return io(this,e,arguments.length>1?arguments[1]:void 0)}});var lo=[].slice,po={},go=function(e,t,n){if(!(t in po)){for(var o=[],r=0;r<t;r++)o[r]="a["+r+"]";po[t]=Function("C,a","return new C("+o.join(",")+")")}return po[t](e,n)},ho=Function.bind||function(e){var t=ot(this),n=lo.call(arguments,1),o=function o(){var r=n.concat(lo.call(arguments));return this instanceof o?go(t,r.length,r):t.apply(e,r)};return v(t.prototype)&&(o.prototype=t.prototype),o};be({target:"Function",proto:!0},{bind:ho});var fo=function(){function e(){Gn(this,e),this.cache=[],this.options=null}return Un(e,[{key:"use",value:function(e){if("function"!=typeof e)throw"middleware must be a function";return this.cache.push(e),this}},{key:"next",value:function(e){if(this.middlewares&&this.middlewares.length>0)return this.middlewares.shift().call(this,this.options,this.next.bind(this))}},{key:"run",value:function(e){return this.middlewares=this.cache.map((function(e){return e})),this.options=e,this.next()}}]),e}(),_o=O.f,mo=Function.prototype,vo=mo.toString,Mo=/^\s*function ([^ (]*)/;i&&!("name"in mo)&&_o(mo,"name",{configurable:!0,get:function(){try{return vo.call(this).match(Mo)[1]}catch(eT){return""}}});var yo=t((function(t,n){var o,r,a,i,u,c,l,d,p,g,h,f,_,m,v,M,y,I;t.exports=(o="function"==typeof Promise,r="object"==("undefined"===typeof self?"undefined":s(self))?self:e,a="undefined"!=typeof Symbol,i="undefined"!=typeof Map,u="undefined"!=typeof Set,c="undefined"!=typeof WeakMap,l="undefined"!=typeof WeakSet,d="undefined"!=typeof DataView,p=a&&void 0!==Symbol.iterator,g=a&&void 0!==Symbol.toStringTag,h=u&&"function"==typeof Set.prototype.entries,f=i&&"function"==typeof Map.prototype.entries,_=h&&Object.getPrototypeOf((new Set).entries()),m=f&&Object.getPrototypeOf((new Map).entries()),v=p&&"function"==typeof Array.prototype[Symbol.iterator],M=v&&Object.getPrototypeOf([][Symbol.iterator]()),y=p&&"function"==typeof String.prototype[Symbol.iterator],I=y&&Object.getPrototypeOf(""[Symbol.iterator]()),function(e){var t=s(e);if("object"!==t)return t;if(null===e)return"null";if(e===r)return"global";if(Array.isArray(e)&&(!1===g||!(Symbol.toStringTag in e)))return"Array";if("object"==("undefined"===typeof window?"undefined":s(window))&&null!==window){if("object"==s(window.location)&&e===window.location)return"Location";if("object"==s(window.document)&&e===window.document)return"Document";if("object"==s(window.navigator)){if("object"==s(window.navigator.mimeTypes)&&e===window.navigator.mimeTypes)return"MimeTypeArray";if("object"==s(window.navigator.plugins)&&e===window.navigator.plugins)return"PluginArray"}if(("function"==typeof window.HTMLElement||"object"==s(window.HTMLElement))&&e instanceof window.HTMLElement){if("BLOCKQUOTE"===e.tagName)return"HTMLQuoteElement";if("TD"===e.tagName)return"HTMLTableDataCellElement";if("TH"===e.tagName)return"HTMLTableHeaderCellElement"}}var n=g&&e[Symbol.toStringTag];if("string"==typeof n)return n;var a=Object.getPrototypeOf(e);return a===RegExp.prototype?"RegExp":a===Date.prototype?"Date":o&&a===Promise.prototype?"Promise":u&&a===Set.prototype?"Set":i&&a===Map.prototype?"Map":l&&a===WeakSet.prototype?"WeakSet":c&&a===WeakMap.prototype?"WeakMap":d&&a===DataView.prototype?"DataView":i&&a===m?"Map Iterator":u&&a===_?"Set Iterator":v&&a===M?"Array Iterator":y&&a===I?"String Iterator":null===a?"Object":Object.prototype.toString.call(e).slice(8,-1)})}));be({target:"Array",stat:!0},{isArray:we});var Io=Ke("unscopables"),To=Array.prototype;null==To[Io]&&O.f(To,Io,{configurable:!0,value:Ht(null)});var Co=function(e){To[Io][e]=!0},So=it.find,Ao=!0,Eo=pt("find");"find"in[]&&Array(1).find((function(){Ao=!1})),be({target:"Array",proto:!0,forced:Ao||!Eo},{find:function(e){return So(this,e,arguments.length>1?arguments[1]:void 0)}}),Co("find");var ko=_e.includes,Do=pt("indexOf",{ACCESSORS:!0,1:0});be({target:"Array",proto:!0,forced:!Do},{includes:function(e){return ko(this,e,arguments.length>1?arguments[1]:void 0)}}),Co("includes");var No=_e.indexOf,Oo=[].indexOf,Lo=!!Oo&&1/[1].indexOf(1,-0)<0,Ro=ut("indexOf"),bo=pt("indexOf",{ACCESSORS:!0,1:0});be({target:"Array",proto:!0,forced:Lo||!Ro||!bo},{indexOf:function(e){return Lo?Oo.apply(this,arguments)||0:No(this,e,arguments.length>1?arguments[1]:void 0)}});var wo=ne.set,Go=ne.getterFor("Array Iterator"),Po=En(Array,"Array",(function(e,t){wo(this,{type:"Array Iterator",target:m(e),index:0,kind:t})}),(function(){var e=Go(this),t=e.target,n=e.kind,o=e.index++;return!t||o>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:o,done:!1}:"values"==n?{value:t[o],done:!1}:{value:[o,t[o]],done:!1}}),"values");vt.Arguments=vt.Array,Co("keys"),Co("values"),Co("entries");var Uo=[].join,Fo=f!=Object,qo=ut("join",",");be({target:"Array",proto:!0,forced:Fo||!qo},{join:function(e){return Uo.call(m(this),void 0===e?",":e)}});var xo=Xe("slice"),Vo=pt("slice",{ACCESSORS:!0,0:0,1:2}),Ko=Ke("species"),Bo=[].slice,Ho=Math.max;be({target:"Array",proto:!0,forced:!xo||!Vo},{slice:function(e,t){var n,o,r,a=m(this),s=de(a.length),i=he(e,s),u=he(void 0===t?s:t,s);if(we(a)&&("function"!=typeof(n=a.constructor)||n!==Array&&!we(n.prototype)?v(n)&&null===(n=n[Ko])&&(n=void 0):n=void 0,n===Array||void 0===n))return Bo.call(a,i,u);for(o=new(void 0===n?Array:n)(Ho(u-i,0)),r=0;i<u;i++,r++)i in a&&Pe(o,r,a[i]);return o.length=r,o}}),be({target:"Date",stat:!0},{now:function(){return(new Date).getTime()}});var jo="".repeat||function(e){var t=String(_(this)),n="",o=ce(e);if(o<0||1/0==o)throw RangeError("Wrong number of repetitions");for(;o>0;(o>>>=1)&&(t+=t))1&o&&(n+=t);return n},Wo=Math.ceil,$o=function(e){return function(t,n,o){var r,a,s=String(_(t)),i=s.length,u=void 0===o?" ":String(o),c=de(n);return c<=i||""==u?s:(r=c-i,(a=jo.call(u,Wo(r/u.length))).length>r&&(a=a.slice(0,r)),e?s+a:a+s)}},Yo={start:$o(!1),end:$o(!0)}.start,zo=Math.abs,Jo=Date.prototype,Xo=Jo.getTime,Qo=Jo.toISOString,Zo=a((function(){return"0385-07-25T07:06:39.999Z"!=Qo.call(new Date(-50000000000001))}))||!a((function(){Qo.call(new Date(NaN))}))?function(){if(!isFinite(Xo.call(this)))throw RangeError("Invalid time value");var e=this.getUTCFullYear(),t=this.getUTCMilliseconds(),n=e<0?"-":e>9999?"+":"";return n+Yo(zo(e),n?6:4,0)+"-"+Yo(this.getUTCMonth()+1,2,0)+"-"+Yo(this.getUTCDate(),2,0)+"T"+Yo(this.getUTCHours(),2,0)+":"+Yo(this.getUTCMinutes(),2,0)+":"+Yo(this.getUTCSeconds(),2,0)+"."+Yo(t,3,0)+"Z"}:Qo;be({target:"Date",proto:!0,forced:Date.prototype.toISOString!==Zo},{toISOString:Zo});var er=Date.prototype,tr=er.toString,nr=er.getTime;new Date(NaN)+""!="Invalid Date"&&oe(er,"toString",(function(){var e=nr.call(this);return e==e?tr.call(this):"Invalid Date"}));var or=function(e,t,n){var o,r;return In&&"function"==typeof(o=t.constructor)&&o!==n&&v(r=o.prototype)&&r!==n.prototype&&In(e,r),e},rr=Ie.f,ar=k.f,sr=O.f,ir=Xt.trim,ur=r.Number,cr=ur.prototype,lr="Number"==g(Ht(cr)),dr=function(e){var t,n,o,r,a,s,i,u,c=M(e,!1);if("string"==typeof c&&c.length>2)if(43===(t=(c=ir(c)).charCodeAt(0))||45===t){if(88===(n=c.charCodeAt(2))||120===n)return NaN}else if(48===t){switch(c.charCodeAt(1)){case 66:case 98:o=2,r=49;break;case 79:case 111:o=8,r=55;break;default:return+c}for(s=(a=c.slice(2)).length,i=0;i<s;i++)if((u=a.charCodeAt(i))<48||u>r)return NaN;return parseInt(a,o)}return+c};if(Le("Number",!ur(" 0o1")||!ur("0b1")||ur("+0x1"))){for(var pr,gr=function e(t){var n=arguments.length<1?0:t,o=this;return o instanceof e&&(lr?a((function(){cr.valueOf.call(o)})):"Number"!=g(o))?or(new ur(dr(n)),o,e):dr(n)},hr=i?rr(ur):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),fr=0;hr.length>fr;fr++)I(ur,pr=hr[fr])&&!I(gr,pr)&&sr(gr,pr,ar(ur,pr));gr.prototype=cr,cr.constructor=gr,oe(r,"Number",gr)}var _r=Ie.f,mr={}.toString,vr="object"==("undefined"===typeof window?"undefined":s(window))&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],Mr={f:function(e){return vr&&"[object Window]"==mr.call(e)?function(e){try{return _r(e)}catch(eT){return vr.slice()}}(e):_r(m(e))}},yr=Mr.f,Ir=a((function(){return!Object.getOwnPropertyNames(1)}));be({target:"Object",stat:!0,forced:Ir},{getOwnPropertyNames:yr});var Tr=a((function(){dn(1)}));be({target:"Object",stat:!0,forced:Tr,sham:!un},{getPrototypeOf:function(e){return dn(Ge(e))}});var Cr=Ct?{}.toString:function(){return"[object "+Et(this)+"]"};Ct||oe(Object.prototype,"toString",Cr,{unsafe:!0});var Sr,Ar,Er,kr=r.Promise,Dr=function(e,t,n){for(var o in t)oe(e,o,t[o],n);return e},Nr=Ke("species"),Or=function(e){var t=se(e),n=O.f;i&&t&&!t[Nr]&&n(t,Nr,{configurable:!0,get:function(){return this}})},Lr=function(e,t,n){if(!(e instanceof t))throw TypeError("Incorrect "+(n?n+" ":"")+"invocation");return e},Rr=t((function(e){var t=function(e,t){this.stopped=e,this.result=t};(e.exports=function(e,n,o,r,a){var i,u,c,l,d,p,g,h=rt(n,o,r?2:1);if(a)i=e;else{if("function"!=typeof(u=Dt(e)))throw TypeError("Target is not iterable");if(It(u)){for(c=0,l=de(e.length);l>c;c++)if((d=r?h(D(g=e[c])[0],g[1]):h(e[c]))&&d instanceof t)return d;return new t(!1)}i=u.call(e)}for(p=i.next;!(g=p.call(i)).done;)if("object"==s(d=mt(i,h,g.value,r))&&d&&d instanceof t)return d;return new t(!1)}).stop=function(e){return new t(!0,e)}})),br=Ke("species"),wr=function(e,t){var n,o=D(e).constructor;return void 0===o||null==(n=D(o)[br])?t:ot(n)},Gr=/(iphone|ipod|ipad).*applewebkit/i.test(je),Pr=r.location,Ur=r.setImmediate,Fr=r.clearImmediate,qr=r.process,xr=r.MessageChannel,Vr=r.Dispatch,Kr=0,Br={},Hr=function(e){if(Br.hasOwnProperty(e)){var t=Br[e];delete Br[e],t()}},jr=function(e){return function(){Hr(e)}},Wr=function(e){Hr(e.data)},$r=function(e){r.postMessage(e+"",Pr.protocol+"//"+Pr.host)};Ur&&Fr||(Ur=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return Br[++Kr]=function(){("function"==typeof e?e:Function(e)).apply(void 0,t)},Sr(Kr),Kr},Fr=function(e){delete Br[e]},"process"==g(qr)?Sr=function(e){qr.nextTick(jr(e))}:Vr&&Vr.now?Sr=function(e){Vr.now(jr(e))}:xr&&!Gr?(Er=(Ar=new xr).port2,Ar.port1.onmessage=Wr,Sr=rt(Er.postMessage,Er,1)):!r.addEventListener||"function"!=typeof postMessage||r.importScripts||a($r)||"file:"===Pr.protocol?Sr="onreadystatechange"in S("script")?function(e){qt.appendChild(S("script")).onreadystatechange=function(){qt.removeChild(this),Hr(e)}}:function(e){setTimeout(jr(e),0)}:(Sr=$r,r.addEventListener("message",Wr,!1)));var Yr,zr,Jr,Xr,Qr,Zr,ea,ta,na={set:Ur,clear:Fr},oa=k.f,ra=na.set,aa=r.MutationObserver||r.WebKitMutationObserver,sa=r.process,ia=r.Promise,ua="process"==g(sa),ca=oa(r,"queueMicrotask"),la=ca&&ca.value;la||(Yr=function(){var e,t;for(ua&&(e=sa.domain)&&e.exit();zr;){t=zr.fn,zr=zr.next;try{t()}catch(eT){throw zr?Xr():Jr=void 0,eT}}Jr=void 0,e&&e.enter()},ua?Xr=function(){sa.nextTick(Yr)}:aa&&!Gr?(Qr=!0,Zr=document.createTextNode(""),new aa(Yr).observe(Zr,{characterData:!0}),Xr=function(){Zr.data=Qr=!Qr}):ia&&ia.resolve?(ea=ia.resolve(void 0),ta=ea.then,Xr=function(){ta.call(ea,Yr)}):Xr=function(){ra.call(r,Yr)});var da,pa,ga,ha,fa=la||function(e){var t={fn:e,next:void 0};Jr&&(Jr.next=t),zr||(zr=t,Xr()),Jr=t},_a=function(e){var t,n;this.promise=new e((function(e,o){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=o})),this.resolve=ot(t),this.reject=ot(n)},ma={f:function(e){return new _a(e)}},va=function(e,t){if(D(e),v(t)&&t.constructor===e)return t;var n=ma.f(e);return(0,n.resolve)(t),n.promise},Ma=function(e){try{return{error:!1,value:e()}}catch(eT){return{error:!0,value:eT}}},ya=na.set,Ia=Ke("species"),Ta="Promise",Ca=ne.get,Sa=ne.set,Aa=ne.getterFor(Ta),Ea=kr,ka=r.TypeError,Da=r.document,Na=r.process,Oa=se("fetch"),La=ma.f,Ra=La,ba="process"==g(Na),wa=!!(Da&&Da.createEvent&&r.dispatchEvent),Ga=Le(Ta,(function(){if(F(Ea)===String(Ea)){if(66===ze)return!0;if(!ba&&"function"!=typeof PromiseRejectionEvent)return!0}if(ze>=51&&/native code/.test(Ea))return!1;var e=Ea.resolve(1),t=function(e){e((function(){}),(function(){}))};return(e.constructor={})[Ia]=t,!(e.then((function(){}))instanceof t)})),Pa=Ga||!wt((function(e){Ea.all(e).catch((function(){}))})),Ua=function(e){var t;return!(!v(e)||"function"!=typeof(t=e.then))&&t},Fa=function(e,t,n){if(!t.notified){t.notified=!0;var o=t.reactions;fa((function(){for(var r=t.value,a=1==t.state,s=0;o.length>s;){var i,u,c,l=o[s++],d=a?l.ok:l.fail,p=l.resolve,g=l.reject,h=l.domain;try{d?(a||(2===t.rejection&&Ka(e,t),t.rejection=1),!0===d?i=r:(h&&h.enter(),i=d(r),h&&(h.exit(),c=!0)),i===l.promise?g(ka("Promise-chain cycle")):(u=Ua(i))?u.call(i,p,g):p(i)):g(r)}catch(eT){h&&!c&&h.exit(),g(eT)}}t.reactions=[],t.notified=!1,n&&!t.rejection&&xa(e,t)}))}},qa=function(e,t,n){var o,a;wa?((o=Da.createEvent("Event")).promise=t,o.reason=n,o.initEvent(e,!1,!0),r.dispatchEvent(o)):o={promise:t,reason:n},(a=r["on"+e])?a(o):"unhandledrejection"===e&&function(e,t){var n=r.console;n&&n.error&&(1===arguments.length?n.error(e):n.error(e,t))}("Unhandled promise rejection",n)},xa=function(e,t){ya.call(r,(function(){var n,o=t.value;if(Va(t)&&(n=Ma((function(){ba?Na.emit("unhandledRejection",o,e):qa("unhandledrejection",e,o)})),t.rejection=ba||Va(t)?2:1,n.error))throw n.value}))},Va=function(e){return 1!==e.rejection&&!e.parent},Ka=function(e,t){ya.call(r,(function(){ba?Na.emit("rejectionHandled",e):qa("rejectionhandled",e,t.value)}))},Ba=function(e,t,n,o){return function(r){e(t,n,r,o)}},Ha=function(e,t,n,o){t.done||(t.done=!0,o&&(t=o),t.value=n,t.state=2,Fa(e,t,!0))},ja=function e(t,n,o,r){if(!n.done){n.done=!0,r&&(n=r);try{if(t===o)throw ka("Promise can't be resolved itself");var a=Ua(o);a?fa((function(){var r={done:!1};try{a.call(o,Ba(e,t,r,n),Ba(Ha,t,r,n))}catch(eT){Ha(t,r,eT,n)}})):(n.value=o,n.state=1,Fa(t,n,!1))}catch(eT){Ha(t,{done:!1},eT,n)}}};Ga&&(Ea=function(e){Lr(this,Ea,Ta),ot(e),da.call(this);var t=Ca(this);try{e(Ba(ja,this,t),Ba(Ha,this,t))}catch(eT){Ha(this,t,eT)}},(da=function(e){Sa(this,{type:Ta,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=Dr(Ea.prototype,{then:function(e,t){var n=Aa(this),o=La(wr(this,Ea));return o.ok="function"!=typeof e||e,o.fail="function"==typeof t&&t,o.domain=ba?Na.domain:void 0,n.parent=!0,n.reactions.push(o),0!=n.state&&Fa(this,n,!1),o.promise},catch:function(e){return this.then(void 0,e)}}),pa=function(){var e=new da,t=Ca(e);this.promise=e,this.resolve=Ba(ja,e,t),this.reject=Ba(Ha,e,t)},ma.f=La=function(e){return e===Ea||e===ga?new pa(e):Ra(e)},"function"==typeof kr&&(ha=kr.prototype.then,oe(kr.prototype,"then",(function(e,t){var n=this;return new Ea((function(e,t){ha.call(n,e,t)})).then(e,t)}),{unsafe:!0}),"function"==typeof Oa&&be({global:!0,enumerable:!0,forced:!0},{fetch:function(e){return va(Ea,Oa.apply(r,arguments))}}))),be({global:!0,wrap:!0,forced:Ga},{Promise:Ea}),mn(Ea,Ta,!1),Or(Ta),ga=se(Ta),be({target:Ta,stat:!0,forced:Ga},{reject:function(e){var t=La(this);return t.reject.call(void 0,e),t.promise}}),be({target:Ta,stat:!0,forced:Ga},{resolve:function(e){return va(this,e)}}),be({target:Ta,stat:!0,forced:Pa},{all:function(e){var t=this,n=La(t),o=n.resolve,r=n.reject,a=Ma((function(){var n=ot(t.resolve),a=[],s=0,i=1;Rr(e,(function(e){var u=s++,c=!1;a.push(void 0),i++,n.call(t,e).then((function(e){c||(c=!0,a[u]=e,--i||o(a))}),r)})),--i||o(a)}));return a.error&&r(a.value),n.promise},race:function(e){var t=this,n=La(t),o=n.reject,r=Ma((function(){var r=ot(t.resolve);Rr(e,(function(e){r.call(t,e).then(n.resolve,o)}))}));return r.error&&o(r.value),n.promise}});var Wa=function(){var e=D(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t};function $a(e,t){return RegExp(e,t)}var Ya,za,Ja={UNSUPPORTED_Y:a((function(){var e=$a("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),BROKEN_CARET:a((function(){var e=$a("^r","gy");return e.lastIndex=2,null!=e.exec("str")}))},Xa=RegExp.prototype.exec,Qa=String.prototype.replace,Za=Xa,es=(Ya=/a/,za=/b*/g,Xa.call(Ya,"a"),Xa.call(za,"a"),0!==Ya.lastIndex||0!==za.lastIndex),ts=Ja.UNSUPPORTED_Y||Ja.BROKEN_CARET,ns=void 0!==/()??/.exec("")[1];(es||ns||ts)&&(Za=function(e){var t,n,o,r,a=this,s=ts&&a.sticky,i=Wa.call(a),u=a.source,c=0,l=e;return s&&(-1===(i=i.replace("y","")).indexOf("g")&&(i+="g"),l=String(e).slice(a.lastIndex),a.lastIndex>0&&(!a.multiline||a.multiline&&"\n"!==e[a.lastIndex-1])&&(u="(?: "+u+")",l=" "+l,c++),n=new RegExp("^(?:"+u+")",i)),ns&&(n=new RegExp("^"+u+"$(?!\\s)",i)),es&&(t=a.lastIndex),o=Xa.call(s?n:a,l),s?o?(o.input=o.input.slice(c),o[0]=o[0].slice(c),o.index=a.lastIndex,a.lastIndex+=o[0].length):a.lastIndex=0:es&&o&&(a.lastIndex=a.global?o.index+o[0].length:t),ns&&o&&o.length>1&&Qa.call(o[0],n,(function(){for(r=1;r<arguments.length-2;r++)void 0===arguments[r]&&(o[r]=void 0)})),o});var os=Za;be({target:"RegExp",proto:!0,forced:/./.exec!==os},{exec:os});var rs=RegExp.prototype,as=rs.toString,ss=a((function(){return"/a/b"!=as.call({source:"a",flags:"b"})})),is="toString"!=as.name;(ss||is)&&oe(RegExp.prototype,"toString",(function(){var e=D(this),t=String(e.source),n=e.flags;return"/"+t+"/"+String(void 0===n&&e instanceof RegExp&&!("flags"in rs)?Wa.call(e):n)}),{unsafe:!0});var us=sn.codeAt;be({target:"String",proto:!0},{codePointAt:function(e){return us(this,e)}});var cs=Ke("match"),ls=function(e){var t;return v(e)&&(void 0!==(t=e[cs])?!!t:"RegExp"==g(e))},ds=function(e){if(ls(e))throw TypeError("The method doesn't accept regular expressions");return e},ps=Ke("match"),gs=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[ps]=!1,"/./"[e](t)}catch(r){}}return!1};be({target:"String",proto:!0,forced:!gs("includes")},{includes:function(e){return!!~String(_(this)).indexOf(ds(e),arguments.length>1?arguments[1]:void 0)}});var hs=Ke("species"),fs=!a((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),_s="$0"==="a".replace(/./,"$0"),ms=Ke("replace"),vs=!!/./[ms]&&""===/./[ms]("a","$0"),Ms=!a((function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var n="ab".split(e);return 2!==n.length||"a"!==n[0]||"b"!==n[1]})),ys=function(e,t,n,o){var r=Ke(e),s=!a((function(){var t={};return t[r]=function(){return 7},7!=""[e](t)})),i=s&&!a((function(){var t=!1,n=/a/;return"split"===e&&((n={}).constructor={},n.constructor[hs]=function(){return n},n.flags="",n[r]=/./[r]),n.exec=function(){return t=!0,null},n[r](""),!t}));if(!s||!i||"replace"===e&&(!fs||!_s||vs)||"split"===e&&!Ms){var u=/./[r],c=n(r,""[e],(function(e,t,n,o,r){return t.exec===os?s&&!r?{done:!0,value:u.call(t,n,o)}:{done:!0,value:e.call(n,t,o)}:{done:!1}}),{REPLACE_KEEPS_$0:_s,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:vs}),l=c[0],d=c[1];oe(String.prototype,e,l),oe(RegExp.prototype,r,2==t?function(e,t){return d.call(e,this,t)}:function(e){return d.call(e,this)})}o&&L(RegExp.prototype[r],"sham",!0)},Is=sn.charAt,Ts=function(e,t,n){return t+(n?Is(e,t).length:1)},Cs=function(e,t){var n=e.exec;if("function"==typeof n){var o=n.call(e,t);if("object"!=s(o))throw TypeError("RegExp exec method returned something other than an Object or null");return o}if("RegExp"!==g(e))throw TypeError("RegExp#exec called on incompatible receiver");return os.call(e,t)};ys("match",1,(function(e,t,n){return[function(t){var n=_(this),o=null==t?void 0:t[e];return void 0!==o?o.call(t,n):new RegExp(t)[e](String(n))},function(e){var o=n(t,e,this);if(o.done)return o.value;var r=D(e),a=String(this);if(!r.global)return Cs(r,a);var s=r.unicode;r.lastIndex=0;for(var i,u=[],c=0;null!==(i=Cs(r,a));){var l=String(i[0]);u[c]=l,""===l&&(r.lastIndex=Ts(a,de(r.lastIndex),s)),c++}return 0===c?null:u}]}));var Ss=Math.max,As=Math.min,Es=Math.floor,ks=/\$([$&'`]|\d\d?|<[^>]*>)/g,Ds=/\$([$&'`]|\d\d?)/g;ys("replace",2,(function(e,t,n,o){var r=o.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,a=o.REPLACE_KEEPS_$0,s=r?"$":"$0";return[function(n,o){var r=_(this),a=null==n?void 0:n[e];return void 0!==a?a.call(n,r,o):t.call(String(r),n,o)},function(e,o){if(!r&&a||"string"==typeof o&&-1===o.indexOf(s)){var u=n(t,e,this,o);if(u.done)return u.value}var c=D(e),l=String(this),d="function"==typeof o;d||(o=String(o));var p=c.global;if(p){var g=c.unicode;c.lastIndex=0}for(var h=[];;){var f=Cs(c,l);if(null===f)break;if(h.push(f),!p)break;""===String(f[0])&&(c.lastIndex=Ts(l,de(c.lastIndex),g))}for(var _,m="",v=0,M=0;M<h.length;M++){f=h[M];for(var y=String(f[0]),I=Ss(As(ce(f.index),l.length),0),T=[],C=1;C<f.length;C++)T.push(void 0===(_=f[C])?_:String(_));var S=f.groups;if(d){var A=[y].concat(T,I,l);void 0!==S&&A.push(S);var E=String(o.apply(void 0,A))}else E=i(y,l,I,T,S,o);I>=v&&(m+=l.slice(v,I)+E,v=I+y.length)}return m+l.slice(v)}];function i(e,n,o,r,a,s){var i=o+e.length,u=r.length,c=Ds;return void 0!==a&&(a=Ge(a),c=ks),t.call(s,c,(function(t,s){var c;switch(s.charAt(0)){case"$":return"$";case"&":return e;case"`":return n.slice(0,o);case"'":return n.slice(i);case"<":c=a[s.slice(1,-1)];break;default:var l=+s;if(0===l)return t;if(l>u){var d=Es(l/10);return 0===d?t:d<=u?void 0===r[d-1]?s.charAt(1):r[d-1]+s.charAt(1):t}c=r[l-1]}return void 0===c?"":c}))}}));var Ns=Ke("iterator"),Os=Ke("toStringTag"),Ls=Po.values;for(var Rs in On){var bs=r[Rs],ws=bs&&bs.prototype;if(ws){if(ws[Ns]!==Ls)try{L(ws,Ns,Ls)}catch(eT){ws[Ns]=Ls}if(ws[Os]||L(ws,Os,Rs),On[Rs])for(var Gs in Po)if(ws[Gs]!==Po[Gs])try{L(ws,Gs,Po[Gs])}catch(eT){ws[Gs]=Po[Gs]}}}var Ps=Xt.trim,Us=r.parseFloat,Fs=1/Us(Wt+"-0")!=-1/0?function(e){var t=Ps(String(e)),n=Us(t);return 0===n&&"-"==t.charAt(0)?-0:n}:Us;be({global:!0,forced:parseFloat!=Fs},{parseFloat:Fs});var qs=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;Gn(this,e),this.high=t,this.low=n}return Un(e,[{key:"equal",value:function(e){return null!==e&&this.low===e.low&&this.high===e.high}},{key:"toString",value:function(){var e=Number(this.high).toString(16),t=Number(this.low).toString(16);if(t.length<8)for(var n=8-t.length;n;)t="0"+t,n--;return e+t}}]),e}(),xs={TEST:{CHINA:{DEFAULT:"wss://wss-dev.tim.qq.com"},OVERSEA:{DEFAULT:"wss://wss-dev.tim.qq.com"},SINGAPORE:{DEFAULT:"wss://wsssgp-dev.im.qcloud.com"},KOREA:{DEFAULT:"wss://wsskr-dev.im.qcloud.com"},GERMANY:{DEFAULT:"wss://wssger-dev.im.qcloud.com"},IND:{DEFAULT:"wss://wssind-dev.im.qcloud.com"}},PRODUCTION:{CHINA:{DEFAULT:"wss://wss.im.qcloud.com",BACKUP:"wss://wss.tim.qq.com",STAT:"https://api.im.qcloud.com"},OVERSEA:{DEFAULT:"wss://wss.im.qcloud.com",STAT:"https://api.im.qcloud.com"},SINGAPORE:{DEFAULT:"wss://wsssgp.im.qcloud.com",STAT:"https://apisgp.im.qcloud.com"},KOREA:{DEFAULT:"wss://wsskr.im.qcloud.com",STAT:"https://apiskr.im.qcloud.com"},GERMANY:{DEFAULT:"wss://wssger.im.qcloud.com",STAT:"https://apiger.im.qcloud.com"},IND:{DEFAULT:"wss://wssind.im.qcloud.com",STAT:"https://apiind.im.qcloud.com"}}},Vs={WEB:7,WX_MP:8,QQ_MP:9,TT_MP:10,BAIDU_MP:11,ALI_MP:12,UNI_NATIVE_APP:15},Ks="1.7.3",Bs=537048168,Hs="CHINA",js="OVERSEA",Ws="SINGAPORE",$s="KOREA",Ys="GERMANY",zs="IND",Js={HOST:{CURRENT:{DEFAULT:"wss://wss.im.qcloud.com",STAT:"https://api.im.qcloud.com"},setCurrent:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Hs;this.CURRENT=xs.PRODUCTION[e]}},NAME:{OPEN_IM:"openim",GROUP:"group_open_http_svc",GROUP_ATTR:"group_open_attr_http_svc",FRIEND:"sns",PROFILE:"profile",RECENT_CONTACT:"recentcontact",PIC:"openpic",BIG_GROUP_NO_AUTH:"group_open_http_noauth_svc",BIG_GROUP_LONG_POLLING:"group_open_long_polling_http_svc",BIG_GROUP_LONG_POLLING_NO_AUTH:"group_open_long_polling_http_noauth_svc",IM_OPEN_STAT:"imopenstat",WEB_IM:"webim",IM_COS_SIGN:"im_cos_sign_svr",CUSTOM_UPLOAD:"im_cos_msg",HEARTBEAT:"heartbeat",IM_OPEN_PUSH:"im_open_push",IM_OPEN_STATUS:"im_open_status",IM_LONG_MESSAGE:"im_long_msg",IM_CONFIG_MANAGER:"im_sdk_config_mgr",STAT_SERVICE:"StatSvc",OVERLOAD_PUSH:"OverLoadPush"},CMD:{ACCESS_LAYER:"accesslayer",LOGIN:"wslogin",LOGOUT_LONG_POLL:"longpollinglogout",LOGOUT:"wslogout",HELLO:"wshello",PORTRAIT_GET:"portrait_get_all",PORTRAIT_SET:"portrait_set",GET_LONG_POLL_ID:"getlongpollingid",LONG_POLL:"longpolling",AVCHATROOM_LONG_POLL:"get_msg",ADD_FRIEND:"friend_add",UPDATE_FRIEND:"friend_update",GET_FRIEND_LIST:"friend_get",GET_FRIEND_PROFILE:"friend_get_list",DELETE_FRIEND:"friend_delete",CHECK_FRIEND:"friend_check",GET_FRIEND_GROUP_LIST:"group_get",RESPOND_FRIEND_APPLICATION:"friend_response",GET_FRIEND_APPLICATION_LIST:"pendency_get",DELETE_FRIEND_APPLICATION:"pendency_delete",REPORT_FRIEND_APPLICATION:"pendency_report",GET_GROUP_APPLICATION:"get_pendency",CREATE_FRIEND_GROUP:"group_add",DELETE_FRIEND_GROUP:"group_delete",UPDATE_FRIEND_GROUP:"group_update",GET_BLACKLIST:"black_list_get",ADD_BLACKLIST:"black_list_add",DELETE_BLACKLIST:"black_list_delete",CREATE_GROUP:"create_group",GET_JOINED_GROUPS:"get_joined_group_list",SET_GROUP_ATTRIBUTES:"set_group_attr",MODIFY_GROUP_ATTRIBUTES:"modify_group_attr",DELETE_GROUP_ATTRIBUTES:"delete_group_attr",CLEAR_GROUP_ATTRIBUTES:"clear_group_attr",GET_GROUP_ATTRIBUTES:"get_group_attr",SEND_MESSAGE:"sendmsg",REVOKE_C2C_MESSAGE:"msgwithdraw",DELETE_C2C_MESSAGE:"delete_c2c_msg_ramble",SEND_GROUP_MESSAGE:"send_group_msg",REVOKE_GROUP_MESSAGE:"group_msg_recall",DELETE_GROUP_MESSAGE:"delete_group_ramble_msg_by_seq",GET_GROUP_INFO:"get_group_self_member_info",GET_GROUP_MEMBER_INFO:"get_specified_group_member_info",GET_GROUP_MEMBER_LIST:"get_group_member_info",QUIT_GROUP:"quit_group",CHANGE_GROUP_OWNER:"change_group_owner",DESTROY_GROUP:"destroy_group",ADD_GROUP_MEMBER:"add_group_member",DELETE_GROUP_MEMBER:"delete_group_member",SEARCH_GROUP_BY_ID:"get_group_public_info",APPLY_JOIN_GROUP:"apply_join_group",HANDLE_APPLY_JOIN_GROUP:"handle_apply_join_group",HANDLE_GROUP_INVITATION:"handle_invite_join_group",MODIFY_GROUP_INFO:"modify_group_base_info",MODIFY_GROUP_MEMBER_INFO:"modify_group_member_info",DELETE_GROUP_SYSTEM_MESSAGE:"deletemsg",DELETE_GROUP_AT_TIPS:"deletemsg",GET_CONVERSATION_LIST:"get",PAGING_GET_CONVERSATION_LIST:"page_get",DELETE_CONVERSATION:"delete",PIN_CONVERSATION:"top",GET_MESSAGES:"getmsg",GET_C2C_ROAM_MESSAGES:"getroammsg",SET_C2C_PEER_MUTE_NOTIFICATIONS:"set_c2c_peer_mute_notifications",GET_C2C_PEER_MUTE_NOTIFICATIONS:"get_c2c_peer_mute_notifications",GET_GROUP_ROAM_MESSAGES:"group_msg_get",SET_C2C_MESSAGE_READ:"msgreaded",GET_PEER_READ_TIME:"get_peer_read_time",SET_GROUP_MESSAGE_READ:"msg_read_report",FILE_READ_AND_WRITE_AUTHKEY:"authkey",FILE_UPLOAD:"pic_up",COS_SIGN:"cos",COS_PRE_SIG:"pre_sig",TIM_WEB_REPORT_V2:"tim_web_report_v2",BIG_DATA_HALLWAY_AUTH_KEY:"authkey",GET_ONLINE_MEMBER_NUM:"get_online_member_num",ALIVE:"alive",MESSAGE_PUSH:"msg_push",MESSAGE_PUSH_ACK:"ws_msg_push_ack",STATUS_FORCEOFFLINE:"stat_forceoffline",DOWNLOAD_MERGER_MESSAGE:"get_relay_json_msg",UPLOAD_MERGER_MESSAGE:"save_relay_json_msg",FETCH_CLOUD_CONTROL_CONFIG:"fetch_config",PUSHED_CLOUD_CONTROL_CONFIG:"push_configv2",FETCH_COMMERCIAL_CONFIG:"fetch_imsdk_purchase_bitsv2",PUSHED_COMMERCIAL_CONFIG:"push_imsdk_purchase_bitsv2",KICK_OTHER:"KickOther",OVERLOAD_NOTIFY:"notify2",SET_ALL_MESSAGE_READ:"read_all_unread_msg"},CHANNEL:{SOCKET:1,XHR:2,AUTO:0},NAME_VERSION:{openim:"v4",group_open_http_svc:"v4",sns:"v4",profile:"v4",recentcontact:"v4",openpic:"v4",group_open_http_noauth_svc:"v4",group_open_long_polling_http_svc:"v4",group_open_long_polling_http_noauth_svc:"v4",imopenstat:"v4",im_cos_sign_svr:"v4",im_cos_msg:"v4",webim:"v4",im_open_push:"v4",im_open_status:"v4"}},Xs={SEARCH_MSG:new qs(0,Math.pow(2,0)).toString(),SEARCH_GRP_SNS:new qs(0,Math.pow(2,1)).toString(),AVCHATROOM_HISTORY_MSG:new qs(0,Math.pow(2,2)).toString(),GRP_COMMUNITY:new qs(0,Math.pow(2,3)).toString(),MSG_TO_SPECIFIED_GRP_MBR:new qs(0,Math.pow(2,4)).toString()};Js.HOST.setCurrent(Hs);var Qs,Zs,ei,ti="undefined"!=typeof wx&&"function"==typeof wx.getSystemInfoSync&&Boolean(wx.getSystemInfoSync().fontSizeSetting),ni="undefined"!=typeof qq&&"function"==typeof qq.getSystemInfoSync&&Boolean(qq.getSystemInfoSync().fontSizeSetting),oi="undefined"!=typeof tt&&"function"==typeof tt.getSystemInfoSync&&Boolean(tt.getSystemInfoSync().fontSizeSetting),ri="undefined"!=typeof swan&&"function"==typeof swan.getSystemInfoSync&&Boolean(swan.getSystemInfoSync().fontSizeSetting),ai="undefined"!=typeof my&&"function"==typeof my.getSystemInfoSync&&Boolean(my.getSystemInfoSync().fontSizeSetting),si="undefined"!=typeof uni&&"undefined"==typeof window,ii=ti||ni||oi||ri||ai||si,ui=("undefined"!=typeof uni||"undefined"!=typeof window)&&!ii,ci=ni?qq:oi?tt:ri?swan:ai?my:ti?wx:si?uni:{},li=(Qs="WEB",Ti?Qs="WEB":ni?Qs="QQ_MP":oi?Qs="TT_MP":ri?Qs="BAIDU_MP":ai?Qs="ALI_MP":ti?Qs="WX_MP":si&&(Qs="UNI_NATIVE_APP"),Vs[Qs]),di=ui&&window&&window.navigator&&window.navigator.userAgent||"",pi=/AppleWebKit\/([\d.]+)/i.exec(di),gi=(pi&&parseFloat(pi.pop()),/iPad/i.test(di)),hi=/iPhone/i.test(di)&&!gi,fi=/iPod/i.test(di),_i=hi||gi||fi,mi=(function(){var e=di.match(/OS (\d+)_/i);e&&e[1]&&e[1]}(),/Android/i.test(di)),vi=(function(){var e=di.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;var t=e[1]&&parseFloat(e[1]),n=e[2]&&parseFloat(e[2]);t&&n&&parseFloat(e[1]+"."+e[2])}(),mi&&/webkit/i.test(di),/Firefox/i.test(di),/Edge/i.test(di)),Mi=(!vi&&/Chrome/i.test(di),function(){var e=di.match(/Chrome\/(\d+)/);e&&e[1]&&parseFloat(e[1])}(),/MSIE/.test(di)),yi=(/MSIE\s8\.0/.test(di),function(){var e=/MSIE\s(\d+)\.\d/.exec(di),t=e&&parseFloat(e[1]);return!t&&/Trident\/7.0/i.test(di)&&/rv:11.0/.test(di)&&(t=11),t}()),Ii=(/Safari/i.test(di),/TBS\/\d+/i.test(di)),Ti=(function(){var e=di.match(/TBS\/(\d+)/i);e&&e[1]&&e[1]}(),!Ii&&/MQQBrowser\/\d+/i.test(di),!Ii&&/ QQBrowser\/\d+/i.test(di),/(micromessenger|webbrowser)/i.test(di)),Ci=/Windows/i.test(di),Si=/MAC OS X/i.test(di),Ai=(/MicroMessenger/i.test(di),ui&&"undefined"!=typeof Worker),Ei=!a((function(){return Object.isExtensible(Object.preventExtensions({}))})),ki=t((function(e){var t=O.f,n=H("meta"),o=0,r=Object.isExtensible||function(){return!0},a=function(e){t(e,n,{value:{objectID:"O"+ ++o,weakData:{}}})},i=e.exports={REQUIRED:!1,fastKey:function(e,t){if(!v(e))return"symbol"==s(e)?e:("string"==typeof e?"S":"P")+e;if(!I(e,n)){if(!r(e))return"F";if(!t)return"E";a(e)}return e[n].objectID},getWeakData:function(e,t){if(!I(e,n)){if(!r(e))return!0;if(!t)return!1;a(e)}return e[n].weakData},onFreeze:function(e){return Ei&&i.REQUIRED&&r(e)&&!I(e,n)&&a(e),e}};$[n]=!0})),Di=(ki.REQUIRED,ki.fastKey,ki.getWeakData,ki.onFreeze,O.f),Ni=ki.fastKey,Oi=ne.set,Li=ne.getterFor,Ri=(function(e,t,n){var o=-1!==e.indexOf("Map"),s=-1!==e.indexOf("Weak"),i=o?"set":"add",u=r[e],c=u&&u.prototype,l=u,d={},p=function(e){var t=c[e];oe(c,e,"add"==e?function(e){return t.call(this,0===e?0:e),this}:"delete"==e?function(e){return!(s&&!v(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return s&&!v(e)?void 0:t.call(this,0===e?0:e)}:"has"==e?function(e){return!(s&&!v(e))&&t.call(this,0===e?0:e)}:function(e,n){return t.call(this,0===e?0:e,n),this})};if(Le(e,"function"!=typeof u||!(s||c.forEach&&!a((function(){(new u).entries().next()})))))l=n.getConstructor(t,e,o,i),ki.REQUIRED=!0;else if(Le(e,!0)){var g=new l,h=g[i](s?{}:-0,1)!=g,f=a((function(){g.has(1)})),_=wt((function(e){new u(e)})),m=!s&&a((function(){for(var e=new u,t=5;t--;)e[i](t,t);return!e.has(-0)}));_||((l=t((function(t,n){Lr(t,l,e);var r=or(new u,t,l);return null!=n&&Rr(n,r[i],r,o),r}))).prototype=c,c.constructor=l),(f||m)&&(p("delete"),p("has"),o&&p("get")),(m||h)&&p(i),s&&c.clear&&delete c.clear}d[e]=l,be({global:!0,forced:l!=u},d),mn(l,e),s||n.setStrong(l,e,o)}("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),{getConstructor:function(e,t,n,o){var r=e((function(e,a){Lr(e,r,t),Oi(e,{type:t,index:Ht(null),first:void 0,last:void 0,size:0}),i||(e.size=0),null!=a&&Rr(a,e[o],e,n)})),a=Li(t),s=function(e,t,n){var o,r,s=a(e),c=u(e,t);return c?c.value=n:(s.last=c={index:r=Ni(t,!0),key:t,value:n,previous:o=s.last,next:void 0,removed:!1},s.first||(s.first=c),o&&(o.next=c),i?s.size++:e.size++,"F"!==r&&(s.index[r]=c)),e},u=function(e,t){var n,o=a(e),r=Ni(t);if("F"!==r)return o.index[r];for(n=o.first;n;n=n.next)if(n.key==t)return n};return Dr(r.prototype,{clear:function(){for(var e=a(this),t=e.index,n=e.first;n;)n.removed=!0,n.previous&&(n.previous=n.previous.next=void 0),delete t[n.index],n=n.next;e.first=e.last=void 0,i?e.size=0:this.size=0},delete:function(e){var t=a(this),n=u(this,e);if(n){var o=n.next,r=n.previous;delete t.index[n.index],n.removed=!0,r&&(r.next=o),o&&(o.previous=r),t.first==n&&(t.first=o),t.last==n&&(t.last=r),i?t.size--:this.size--}return!!n},forEach:function(e){for(var t,n=a(this),o=rt(e,arguments.length>1?arguments[1]:void 0,3);t=t?t.next:n.first;)for(o(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!u(this,e)}}),Dr(r.prototype,n?{get:function(e){var t=u(this,e);return t&&t.value},set:function(e,t){return s(this,0===e?0:e,t)}}:{add:function(e){return s(this,e=0===e?0:e,e)}}),i&&Di(r.prototype,"size",{get:function(){return a(this).size}}),r},setStrong:function(e,t,n){var o=t+" Iterator",r=Li(t),a=Li(o);En(e,t,(function(e,t){Oi(this,{type:o,target:e,state:r(e),kind:t,last:void 0})}),(function(){for(var e=a(this),t=e.kind,n=e.last;n&&n.removed;)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?"keys"==t?{value:n.key,done:!1}:"values"==t?{value:n.value,done:!1}:{value:[n.key,n.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),n?"entries":"values",!n,!0),Or(t)}}),"undefined"!=typeof o?o:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});Zs="undefined"!=typeof console?console:void 0!==Ri&&Ri.console?Ri.console:"undefined"!=typeof window&&window.console?window.console:{};for(var bi=function(){},wi=["assert","clear","count","debug","dir","dirxml","error","exception","group","groupCollapsed","groupEnd","info","log","markTimeline","profile","profileEnd","table","time","timeEnd","timeStamp","trace","warn"],Gi=wi.length;Gi--;)ei=wi[Gi],console[ei]||(Zs[ei]=bi);Zs.methods=wi;var Pi=Zs,Ui=0,Fi=function(){return(new Date).getTime()+Ui},qi=function(){Ui=0},xi=0,Vi=new Map;function Ki(){var e,t=((e=new Date).setTime(Fi()),e);return"TIM "+t.toLocaleTimeString("en-US",{hour12:!1})+"."+function(e){var t;switch(e.toString().length){case 1:t="00"+e;break;case 2:t="0"+e;break;default:t=e}return t}(t.getMilliseconds())+":"}var Bi={arguments2String:function(e){var t;if(1===e.length)t=Ki()+e[0];else{t=Ki();for(var n=0,o=e.length;n<o;n++)Xi(e[n])?Zi(e[n])?t+=au(e[n]):t+=JSON.stringify(e[n]):t+=e[n],t+=" "}return t},debug:function(){if(xi<=-1){var e=this.arguments2String(arguments);Pi.debug(e)}},log:function(){if(xi<=0){var e=this.arguments2String(arguments);Pi.log(e)}},info:function(){if(xi<=1){var e=this.arguments2String(arguments);Pi.info(e)}},warn:function(){if(xi<=2){var e=this.arguments2String(arguments);Pi.warn(e)}},error:function(){if(xi<=3){var e=this.arguments2String(arguments);Pi.error(e)}},time:function(e){Vi.set(e,ou.now())},timeEnd:function(e){if(Vi.has(e)){var t=ou.now()-Vi.get(e);return Vi.delete(e),t}return Pi.warn("未找到对应label: ".concat(e,", 请在调用 logger.timeEnd 前，调用 logger.time")),0},setLevel:function(e){e<4&&Pi.log(Ki()+"set level from "+xi+" to "+e),xi=e},getLevel:function(){return xi}},Hi=function(e){return"file"===eu(e)},ji=function(e){return null!==e&&("number"==typeof e&&!isNaN(e-0)||"object"===wn(e)&&e.constructor===Number)},Wi=function(e){return"string"==typeof e},$i=function(e){return null!==e&&"object"===wn(e)},Yi=function(e){if("object"!==wn(e)||null===e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return t===n},zi=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"array"===eu(e)},Ji=function(e){return void 0===e},Xi=function(e){return zi(e)||$i(e)},Qi=function(e){return"function"==typeof e},Zi=function(e){return e instanceof Error},eu=function(e){return Object.prototype.toString.call(e).match(/^\[object (.*)\]$/)[1].toLowerCase()},tu=function(e){if("string"!=typeof e)return!1;var t=e[0];return!/[^a-zA-Z0-9]/.test(t)},nu=0;Date.now||(Date.now=function(){return(new Date).getTime()});var ou={now:function(){0===nu&&(nu=Date.now()-1);var e=Date.now()-nu;return e>4294967295?(nu+=4294967295,Date.now()-nu):e},utc:function(){return Math.round(Date.now()/1e3)}},ru=function e(t,n,o,r){if(!Xi(t)||!Xi(n))return 0;for(var a,s=0,i=Object.keys(n),u=0,c=i.length;u<c;u++)if(a=i[u],!(Ji(n[a])||o&&o.includes(a)))if(Xi(t[a])&&Xi(n[a]))s+=e(t[a],n[a],o,r);else{if(r&&r.includes(n[a]))continue;t[a]!==n[a]&&(t[a]=n[a],s+=1)}return s},au=function(e){return JSON.stringify(e,["message","code"])},su=function(e){if(0===e.length)return 0;for(var t=0,n=0,o="undefined"!=typeof document&&void 0!==document.characterSet?document.characterSet:"UTF-8";void 0!==e[t];)n+=e[t++].charCodeAt[t]<=255?1:!1===o?3:2;return n},iu=function(e){var t=e||99999999;return Math.round(Math.random()*t)},uu="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",cu=uu.length,lu=function(e,t){for(var n in e)if(e[n]===t)return!0;return!1},du={},pu=function(){if(ii)return"https:";if(ui&&"undefined"==typeof window)return"https:";var e=window.location.protocol;return["http:","https:"].indexOf(e)<0&&(e="http:"),e},gu=function(e){return-1===e.indexOf("http://")||-1===e.indexOf("https://")?"https://"+e:e.replace(/https|http/,"https")},hu=function e(t){if(0===Object.getOwnPropertyNames(t).length)return Object.create(null);var n=Array.isArray(t)?[]:Object.create(null),o="";for(var r in t)null!==t[r]?void 0!==t[r]?(o=wn(t[r]),["string","number","function","boolean"].indexOf(o)>=0?n[r]=t[r]:n[r]=e(t[r])):n[r]=void 0:n[r]=null;return n};function fu(e,t){zi(e)&&zi(t)?t.forEach((function(t){var n=t.key,o=t.value,r=e.find((function(e){return e.key===n}));r?r.value=o:e.push({key:n,value:o})})):Bi.warn("updateCustomField target 或 source 不是数组，忽略此次更新。")}var _u=function(e){return e===so.GRP_PUBLIC},mu=function(e){return e===so.GRP_AVCHATROOM},vu=function(e){return Wi(e)&&e.slice(0,3)===so.CONV_C2C},Mu=function(e){return Wi(e)&&e.slice(0,5)===so.CONV_GROUP},yu=function(e){return Wi(e)&&e===so.CONV_SYSTEM};function Iu(e,t){var n={};return Object.keys(e).forEach((function(o){n[o]=t(e[o],o)})),n}function Tu(){function e(){return(65536*(1+Math.random())|0).toString(16).substring(1)}return"".concat(e()+e()).concat(e()).concat(e()).concat(e()).concat(e()).concat(e()).concat(e())}function Cu(){var e="unknown";if(Si&&(e="mac"),Ci&&(e="windows"),_i&&(e="ios"),mi&&(e="android"),ii)try{var t=ci.getSystemInfoSync().platform;void 0!==t&&(e=t)}catch(eT){}return e}function Su(e){var t=e.originUrl,n=void 0===t?void 0:t,o=e.originWidth,r=e.originHeight,a=e.min,s=void 0===a?198:a,i=parseInt(o),u=parseInt(r),c={url:void 0,width:0,height:0};if((i<=u?i:u)<=s)c.url=n,c.width=i,c.height=u;else{u<=i?(c.width=Math.ceil(i*s/u),c.height=s):(c.width=s,c.height=Math.ceil(u*s/i));var l=n&&n.indexOf("?")>-1?"".concat(n,"&"):"".concat(n,"?");c.url="".concat(l,198===s?"imageView2/3/w/198/h/198":"imageView2/3/w/720/h/720")}return Ji(n)?$n(c,["url"]):c}function Au(e){var t=e[2];e[2]=e[1],e[1]=t;for(var n=0;n<e.length;n++)e[n].setType(n)}function Eu(e){var t=e.servcmd;return t.slice(t.indexOf(".")+1)}function ku(e,t){return Math.round(Number(e)*Math.pow(10,t))/Math.pow(10,t)}function Du(e,t){return e.includes(t)}function Nu(e,t){return e.includes(t)}var Ou=Object.prototype.hasOwnProperty;function Lu(e){if(null==e)return!0;if("boolean"==typeof e)return!1;if("number"==typeof e)return 0===e;if("string"==typeof e)return 0===e.length;if("function"==typeof e)return 0===e.length;if(Array.isArray(e))return 0===e.length;if(e instanceof Error)return""===e.message;if(Yi(e)){for(var t in e)if(Ou.call(e,t))return!1;return!0}return!("map"!==eu(e)&&!function(e){return"set"===eu(e)}(e)&&!Hi(e))&&0===e.size}function Ru(e,t,n){if(void 0===t)return!0;var o=!0;if("object"===yo(t).toLowerCase())Object.keys(t).forEach((function(r){var a=1===e.length?e[0][r]:void 0;o=!!bu(a,t[r],n,r)&&o}));else if("array"===yo(t).toLowerCase())for(var r=0;r<t.length;r++)o=!!bu(e[r],t[r],n,t[r].name)&&o;if(o)return o;throw new Error("Params validate failed.")}function bu(e,t,n,o){if(void 0===t)return!0;var r=!0;return t.required&&Lu(e)&&(Pi.error("TIM [".concat(n,'] Missing required params: "').concat(o,'".')),r=!1),Lu(e)||yo(e).toLowerCase()===t.type.toLowerCase()||(Pi.error("TIM [".concat(n,'] Invalid params: type check failed for "').concat(o,'".Expected ').concat(t.type,".")),r=!1),t.validator&&!t.validator(e)&&(Pi.error("TIM [".concat(n,"] Invalid params: custom validator check failed for params.")),r=!1),r}var wu={f:Ke},Gu=O.f,Pu=it.forEach,Uu=W("hidden"),Fu=Ke("toPrimitive"),qu=ne.set,xu=ne.getterFor("Symbol"),Vu=Object.prototype,Ku=r.Symbol,Bu=se("JSON","stringify"),Hu=k.f,ju=O.f,Wu=Mr.f,$u=l.f,Yu=V("symbols"),zu=V("op-symbols"),Ju=V("string-to-symbol-registry"),Xu=V("symbol-to-string-registry"),Qu=V("wks"),Zu=r.QObject,ec=!Zu||!Zu.prototype||!Zu.prototype.findChild,tc=i&&a((function(){return 7!=Ht(ju({},"a",{get:function(){return ju(this,"a",{value:7}).a}})).a}))?function(e,t,n){var o=Hu(Vu,t);o&&delete Vu[t],ju(e,t,n),o&&e!==Vu&&ju(Vu,t,o)}:ju,nc=function(e,t){var n=Yu[e]=Ht(Ku.prototype);return qu(n,{type:"Symbol",tag:e,description:t}),i||(n.description=t),n},oc=Fe?function(e){return"symbol"==s(e)}:function(e){return Object(e)instanceof Ku},rc=function e(t,n,o){t===Vu&&e(zu,n,o),D(t);var r=M(n,!0);return D(o),I(Yu,r)?(o.enumerable?(I(t,Uu)&&t[Uu][r]&&(t[Uu][r]=!1),o=Ht(o,{enumerable:d(0,!1)})):(I(t,Uu)||ju(t,Uu,d(1,{})),t[Uu][r]=!0),tc(t,r,o)):ju(t,r,o)},ac=function(e,t){D(e);var n=m(t),o=Ut(n).concat(cc(n));return Pu(o,(function(t){i&&!sc.call(n,t)||rc(e,t,n[t])})),e},sc=function(e){var t=M(e,!0),n=$u.call(this,t);return!(this===Vu&&I(Yu,t)&&!I(zu,t))&&(!(n||!I(this,t)||!I(Yu,t)||I(this,Uu)&&this[Uu][t])||n)},ic=function(e,t){var n=m(e),o=M(t,!0);if(n!==Vu||!I(Yu,o)||I(zu,o)){var r=Hu(n,o);return!r||!I(Yu,o)||I(n,Uu)&&n[Uu][o]||(r.enumerable=!0),r}},uc=function(e){var t=Wu(m(e)),n=[];return Pu(t,(function(e){I(Yu,e)||I($,e)||n.push(e)})),n},cc=function(e){var t=e===Vu,n=Wu(t?zu:m(e)),o=[];return Pu(n,(function(e){!I(Yu,e)||t&&!I(Vu,e)||o.push(Yu[e])})),o};if(Ue||(oe((Ku=function(){if(this instanceof Ku)throw TypeError("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?String(arguments[0]):void 0,t=H(e),n=function e(n){this===Vu&&e.call(zu,n),I(this,Uu)&&I(this[Uu],t)&&(this[Uu][t]=!1),tc(this,t,d(1,n))};return i&&ec&&tc(Vu,t,{configurable:!0,set:n}),nc(t,e)}).prototype,"toString",(function(){return xu(this).tag})),oe(Ku,"withoutSetter",(function(e){return nc(H(e),e)})),l.f=sc,O.f=rc,k.f=ic,Ie.f=Mr.f=uc,Te.f=cc,wu.f=function(e){return nc(Ke(e),e)},i&&(ju(Ku.prototype,"description",{configurable:!0,get:function(){return xu(this).description}}),oe(Vu,"propertyIsEnumerable",sc,{unsafe:!0}))),be({global:!0,wrap:!0,forced:!Ue,sham:!Ue},{Symbol:Ku}),Pu(Ut(Qu),(function(e){!function(e){var t=re.Symbol||(re.Symbol={});I(t,e)||Gu(t,e,{value:wu.f(e)})}(e)})),be({target:"Symbol",stat:!0,forced:!Ue},{for:function(e){var t=String(e);if(I(Ju,t))return Ju[t];var n=Ku(t);return Ju[t]=n,Xu[n]=t,n},keyFor:function(e){if(!oc(e))throw TypeError(e+" is not a symbol");if(I(Xu,e))return Xu[e]},useSetter:function(){ec=!0},useSimple:function(){ec=!1}}),be({target:"Object",stat:!0,forced:!Ue,sham:!i},{create:function(e,t){return void 0===t?Ht(e):ac(Ht(e),t)},defineProperty:rc,defineProperties:ac,getOwnPropertyDescriptor:ic}),be({target:"Object",stat:!0,forced:!Ue},{getOwnPropertyNames:uc,getOwnPropertySymbols:cc}),be({target:"Object",stat:!0,forced:a((function(){Te.f(1)}))},{getOwnPropertySymbols:function(e){return Te.f(Ge(e))}}),Bu){var lc=!Ue||a((function(){var e=Ku();return"[null]"!=Bu([e])||"{}"!=Bu({a:e})||"{}"!=Bu(Object(e))}));be({target:"JSON",stat:!0,forced:lc},{stringify:function(e,t,n){for(var o,r=[e],a=1;arguments.length>a;)r.push(arguments[a++]);if(o=t,(v(t)||void 0!==e)&&!oc(e))return we(t)||(t=function(e,t){if("function"==typeof o&&(t=o.call(this,e,t)),!oc(t))return t}),r[1]=t,Bu.apply(null,r)}})}Ku.prototype[Fu]||L(Ku.prototype,Fu,Ku.prototype.valueOf),mn(Ku,"Symbol"),$[Uu]=!0;var dc=O.f,pc=r.Symbol;if(i&&"function"==typeof pc&&(!("description"in pc.prototype)||void 0!==pc().description)){var gc={},hc=function e(){var t=arguments.length<1||void 0===arguments[0]?void 0:String(arguments[0]),n=this instanceof e?new pc(t):void 0===t?pc():pc(t);return""===t&&(gc[n]=!0),n};Se(hc,pc);var fc=hc.prototype=pc.prototype;fc.constructor=hc;var _c=fc.toString,mc="Symbol(test)"==String(pc("test")),vc=/^Symbol\((.*)\)[^)]+$/;dc(fc,"description",{configurable:!0,get:function(){var e=v(this)?this.valueOf():this,t=_c.call(e);if(I(gc,e))return"";var n=mc?t.slice(7,-1):t.replace(vc,"$1");return""===n?void 0:n}}),be({global:!0,forced:!0},{Symbol:hc})}var Mc,yc=k.f,Ic="".startsWith,Tc=Math.min,Cc=gs("startsWith"),Sc=!(Cc||(Mc=yc(String.prototype,"startsWith"),!Mc||Mc.writable));be({target:"String",proto:!0,forced:!Sc&&!Cc},{startsWith:function(e){var t=String(_(this));ds(e);var n=de(Tc(arguments.length>1?arguments[1]:void 0,t.length)),o=String(e);return Ic?Ic.call(t,o,n):t.slice(n,n+o.length)===o}});var Ac={UNSEND:"unSend",SUCCESS:"success",FAIL:"fail"},Ec={NOT_START:"notStart",PENDING:"pengding",RESOLVED:"resolved",REJECTED:"rejected"},kc=function(e){return!!e&&(!!(vu(e)||Mu(e)||yu(e))||(console.warn("非法的会话 ID:".concat(e,"。会话 ID 组成方式：C2C + userID（单聊）GROUP + groupID（群聊）@TIM#SYSTEM（系统通知会话）")),!1))},Dc="请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#",Nc=function(e){return e.param?"".concat(e.api," ").concat(e.param," ").concat(e.desc,"。").concat(Dc).concat(e.api):"".concat(e.api," ").concat(e.desc,"。").concat(Dc).concat(e.api)},Oc={type:"String",required:!0},Lc={type:"Array",required:!0},Rc={type:"Object",required:!0},bc={login:{userID:Oc,userSig:Oc},addToBlacklist:{userIDList:Lc},on:[{name:"eventName",type:"String",validator:function(e){return"string"==typeof e&&0!==e.length||(console.warn(Nc({api:"on",param:"eventName",desc:"类型必须为 String，且不能为空"})),!1)}},{name:"handler",type:"Function",validator:function(e){return"function"!=typeof e?(console.warn(Nc({api:"on",param:"handler",desc:"参数必须为 Function"})),!1):(""===e.name&&console.warn("on 接口的 handler 参数推荐使用具名函数。具名函数可以使用 off 接口取消订阅，匿名函数无法取消订阅。"),!0)}}],once:[{name:"eventName",type:"String",validator:function(e){return"string"==typeof e&&0!==e.length||(console.warn(Nc({api:"once",param:"eventName",desc:"类型必须为 String，且不能为空"})),!1)}},{name:"handler",type:"Function",validator:function(e){return"function"!=typeof e?(console.warn(Nc({api:"once",param:"handler",desc:"参数必须为 Function"})),!1):(""===e.name&&console.warn("once 接口的 handler 参数推荐使用具名函数。"),!0)}}],off:[{name:"eventName",type:"String",validator:function(e){return"string"==typeof e&&0!==e.length||(console.warn(Nc({api:"off",param:"eventName",desc:"类型必须为 String，且不能为空"})),!1)}},{name:"handler",type:"Function",validator:function(e){return"function"!=typeof e?(console.warn(Nc({api:"off",param:"handler",desc:"参数必须为 Function"})),!1):(""===e.name&&console.warn("off 接口无法为匿名函数取消监听事件。"),!0)}}],sendMessage:[xn({name:"message"},Rc)],getMessageList:{conversationID:xn(xn({},Oc),{},{validator:function(e){return kc(e)}}),nextReqMessageID:{type:"String"},count:{type:"Number",validator:function(e){return!(!Ji(e)&&!/^[1-9][0-9]*$/.test(e))||(console.warn(Nc({api:"getMessageList",param:"count",desc:"必须为正整数"})),!1)}}},setMessageRead:{conversationID:xn(xn({},Oc),{},{validator:function(e){return kc(e)}})},setAllMessageRead:{scope:{type:"String",required:!1,validator:function(e){return!e||-1!==[so.READ_ALL_C2C_MSG,so.READ_ALL_GROUP_MSG,so.READ_ALL_MSG].indexOf(e)||(console.warn(Nc({api:"setAllMessageRead",param:"scope",desc:"取值必须为 TIM.TYPES.READ_ALL_C2C_MSG, TIM.TYPES.READ_ALL_GROUP_MSG 或 TIM.TYPES.READ_ALL_MSG"})),!1)}}},getConversationProfile:[xn(xn({name:"conversationID"},Oc),{},{validator:function(e){return kc(e)}})],deleteConversation:[xn(xn({name:"conversationID"},Oc),{},{validator:function(e){return kc(e)}})],pinConversation:{conversationID:xn(xn({},Oc),{},{validator:function(e){return kc(e)}}),isPinned:xn({},{type:"Boolean",required:!0})},getConversationList:[{name:"options",type:"Array",validator:function(e){return!!Ji(e)||0!==e.length||(console.warn(Nc({api:"getConversationList",desc:"获取指定会话时不能传入空数组"})),!1)}}],getGroupList:{groupProfileFilter:{type:"Array"}},getGroupProfile:{groupID:Oc,groupCustomFieldFilter:{type:"Array"},memberCustomFieldFilter:{type:"Array"}},getGroupProfileAdvance:{groupIDList:Lc},createGroup:{name:Oc},joinGroup:{groupID:Oc,type:{type:"String"},applyMessage:{type:"String"}},quitGroup:[xn({name:"groupID"},Oc)],handleApplication:{message:Rc,handleAction:Oc,handleMessage:{type:"String"}},changeGroupOwner:{groupID:Oc,newOwnerID:Oc},updateGroupProfile:{groupID:Oc,muteAllMembers:{type:"Boolean"}},dismissGroup:[xn({name:"groupID"},Oc)],searchGroupByID:[xn({name:"groupID"},Oc)],initGroupAttributes:{groupID:Oc,groupAttributes:xn(xn({},Rc),{},{validator:function(e){var t=!0;return Object.keys(e).forEach((function(n){if(!Wi(e[n]))return console.warn(Nc({api:"initGroupAttributes",desc:"群属性 value 必须是字符串"})),t=!1})),t}})},setGroupAttributes:{groupID:Oc,groupAttributes:xn(xn({},Rc),{},{validator:function(e){var t=!0;return Object.keys(e).forEach((function(n){if(!Wi(e[n]))return console.warn(Nc({api:"setGroupAttributes",desc:"群属性 value 必须是字符串"})),t=!1})),t}})},deleteGroupAttributes:{groupID:Oc,keyList:{type:"Array",validator:function(e){if(Ji(e))return console.warn(Nc({api:"deleteGroupAttributes",desc:"缺少必填参数：keyList"})),!1;if(!zi(e))return!1;if(!Lu(e)){var t=!0;return e.forEach((function(e){if(!Wi(e))return console.warn(Nc({api:"deleteGroupAttributes",desc:"群属性 key 必须是字符串"})),t=!1})),t}return!0}}},getGroupAttributes:{groupID:Oc,keyList:{type:"Array",validator:function(e){if(Ji(e))return console.warn(Nc({api:"getGroupAttributes",desc:"缺少必填参数：keyList"})),!1;if(!zi(e))return!1;if(!Lu(e)){var t=!0;return e.forEach((function(e){if(!Wi(e))return console.warn(Nc({api:"getGroupAttributes",desc:"群属性 key 必须是字符串"})),t=!1})),t}return!0}}},getGroupMemberList:{groupID:Oc,offset:{type:"Number"},count:{type:"Number"}},getGroupMemberProfile:{groupID:Oc,userIDList:Lc,memberCustomFieldFilter:{type:"Array"}},addGroupMember:{groupID:Oc,userIDList:Lc},setGroupMemberRole:{groupID:Oc,userID:Oc,role:Oc},setGroupMemberMuteTime:{groupID:Oc,userID:Oc,muteTime:{type:"Number",validator:function(e){return e>=0}}},setGroupMemberNameCard:{groupID:Oc,userID:{type:"String"},nameCard:{type:"String",validator:function(e){return Wi(e)?(e.length,!0):(console.warn(Nc({api:"setGroupMemberNameCard",param:"nameCard",desc:"类型必须为 String"})),!1)}}},setGroupMemberCustomField:{groupID:Oc,userID:{type:"String"},memberCustomField:Lc},deleteGroupMember:{groupID:Oc},createTextMessage:{to:Oc,conversationType:Oc,payload:xn(xn({},Rc),{},{validator:function(e){return Yi(e)?Wi(e.text)?0!==e.text.length||(console.warn(Nc({api:"createTextMessage",desc:"消息内容不能为空"})),!1):(console.warn(Nc({api:"createTextMessage",param:"payload.text",desc:"类型必须为 String"})),!1):(console.warn(Nc({api:"createTextMessage",param:"payload",desc:"类型必须为 plain object"})),!1)}})},createTextAtMessage:{to:Oc,conversationType:Oc,payload:xn(xn({},Rc),{},{validator:function(e){return Yi(e)?Wi(e.text)?0===e.text.length?(console.warn(Nc({api:"createTextAtMessage",desc:"消息内容不能为空"})),!1):!(e.atUserList&&!zi(e.atUserList))||(console.warn(Nc({api:"createTextAtMessage",desc:"payload.atUserList 类型必须为数组"})),!1):(console.warn(Nc({api:"createTextAtMessage",param:"payload.text",desc:"类型必须为 String"})),!1):(console.warn(Nc({api:"createTextAtMessage",param:"payload",desc:"类型必须为 plain object"})),!1)}})},createCustomMessage:{to:Oc,conversationType:Oc,payload:xn(xn({},Rc),{},{validator:function(e){return Yi(e)?e.data&&!Wi(e.data)?(console.warn(Nc({api:"createCustomMessage",param:"payload.data",desc:"类型必须为 String"})),!1):e.description&&!Wi(e.description)?(console.warn(Nc({api:"createCustomMessage",param:"payload.description",desc:"类型必须为 String"})),!1):!(e.extension&&!Wi(e.extension))||(console.warn(Nc({api:"createCustomMessage",param:"payload.extension",desc:"类型必须为 String"})),!1):(console.warn(Nc({api:"createCustomMessage",param:"payload",desc:"类型必须为 plain object"})),!1)}})},createImageMessage:{to:Oc,conversationType:Oc,payload:xn(xn({},Rc),{},{validator:function(e){if(!Yi(e))return console.warn(Nc({api:"createImageMessage",param:"payload",desc:"类型必须为 plain object"})),!1;if(Ji(e.file))return console.warn(Nc({api:"createImageMessage",param:"payload.file",desc:"不能为 undefined"})),!1;if(ui){if(!(e.file instanceof HTMLInputElement||Hi(e.file)))return Yi(e.file)&&"undefined"!=typeof uni?0!==e.file.tempFilePaths.length&&0!==e.file.tempFiles.length||(console.warn(Nc({api:"createImageMessage",param:"payload.file",desc:"您没有选择文件，无法发送"})),!1):(console.warn(Nc({api:"createImageMessage",param:"payload.file",desc:"类型必须是 HTMLInputElement 或 File"})),!1);if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return console.warn(Nc({api:"createImageMessage",param:"payload.file",desc:"您没有选择文件，无法发送"})),!1}return!0},onProgress:{type:"Function",required:!1,validator:function(e){return Ji(e)&&console.warn(Nc({api:"createImageMessage",desc:"没有 onProgress 回调，您将无法获取上传进度"})),!0}}})},createAudioMessage:{to:Oc,conversationType:Oc,payload:xn(xn({},Rc),{},{validator:function(e){return!!Yi(e)||(console.warn(Nc({api:"createAudioMessage",param:"payload",desc:"类型必须为 plain object"})),!1)}}),onProgress:{type:"Function",required:!1,validator:function(e){return Ji(e)&&console.warn(Nc({api:"createAudioMessage",desc:"没有 onProgress 回调，您将无法获取上传进度"})),!0}}},createVideoMessage:{to:Oc,conversationType:Oc,payload:xn(xn({},Rc),{},{validator:function(e){if(!Yi(e))return console.warn(Nc({api:"createVideoMessage",param:"payload",desc:"类型必须为 plain object"})),!1;if(Ji(e.file))return console.warn(Nc({api:"createVideoMessage",param:"payload.file",desc:"不能为 undefined"})),!1;if(ui){if(!(e.file instanceof HTMLInputElement||Hi(e.file)))return Yi(e.file)&&"undefined"!=typeof uni?!!Hi(e.file.tempFile)||(console.warn(Nc({api:"createVideoMessage",param:"payload.file",desc:"您没有选择文件，无法发送"})),!1):(console.warn(Nc({api:"createVideoMessage",param:"payload.file",desc:"类型必须是 HTMLInputElement 或 File"})),!1);if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return console.warn(Nc({api:"createVideoMessage",param:"payload.file",desc:"您没有选择文件，无法发送"})),!1}return!0}}),onProgress:{type:"Function",required:!1,validator:function(e){return Ji(e)&&console.warn(Nc({api:"createVideoMessage",desc:"没有 onProgress 回调，您将无法获取上传进度"})),!0}}},createFaceMessage:{to:Oc,conversationType:Oc,payload:xn(xn({},Rc),{},{validator:function(e){return Yi(e)?ji(e.index)?!!Wi(e.data)||(console.warn(Nc({api:"createFaceMessage",param:"payload.data",desc:"类型必须为 String"})),!1):(console.warn(Nc({api:"createFaceMessage",param:"payload.index",desc:"类型必须为 Number"})),!1):(console.warn(Nc({api:"createFaceMessage",param:"payload",desc:"类型必须为 plain object"})),!1)}})},createFileMessage:{to:Oc,conversationType:Oc,payload:xn(xn({},Rc),{},{validator:function(e){if(!Yi(e))return console.warn(Nc({api:"createFileMessage",param:"payload",desc:"类型必须为 plain object"})),!1;if(Ji(e.file))return console.warn(Nc({api:"createFileMessage",param:"payload.file",desc:"不能为 undefined"})),!1;if(ui){if(!(e.file instanceof HTMLInputElement||Hi(e.file)))return Yi(e.file)&&"undefined"!=typeof uni?0!==e.file.tempFilePaths.length&&0!==e.file.tempFiles.length||(console.warn(Nc({api:"createFileMessage",param:"payload.file",desc:"您没有选择文件，无法发送"})),!1):(console.warn(Nc({api:"createFileMessage",param:"payload.file",desc:"类型必须是 HTMLInputElement 或 File"})),!1);if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return console.warn(Nc({api:"createFileMessage",desc:"您没有选择文件，无法发送"})),!1}return!0}}),onProgress:{type:"Function",required:!1,validator:function(e){return Ji(e)&&console.warn(Nc({api:"createFileMessage",desc:"没有 onProgress 回调，您将无法获取上传进度"})),!0}}},createLocationMessage:{to:Oc,conversationType:Oc,payload:xn(xn({},Rc),{},{validator:function(e){return Yi(e)?Wi(e.description)?ji(e.longitude)?!!ji(e.latitude)||(console.warn(Nc({api:"createLocationMessage",param:"payload.latitude",desc:"类型必须为 Number"})),!1):(console.warn(Nc({api:"createLocationMessage",param:"payload.longitude",desc:"类型必须为 Number"})),!1):(console.warn(Nc({api:"createLocationMessage",param:"payload.description",desc:"类型必须为 String"})),!1):(console.warn(Nc({api:"createLocationMessage",param:"payload",desc:"类型必须为 plain object"})),!1)}})},createMergerMessage:{to:Oc,conversationType:Oc,payload:xn(xn({},Rc),{},{validator:function(e){if(Lu(e.messageList))return console.warn(Nc({api:"createMergerMessage",desc:"不能为空数组"})),!1;if(Lu(e.compatibleText))return console.warn(Nc({api:"createMergerMessage",desc:"类型必须为 String，且不能为空"})),!1;var t=!1;return e.messageList.forEach((function(e){e.status===Ac.FAIL&&(t=!0)})),!t||(console.warn(Nc({api:"createMergerMessage",desc:"不支持合并已发送失败的消息"})),!1)}})},revokeMessage:[xn(xn({name:"message"},Rc),{},{validator:function(e){return Lu(e)?(console.warn("revokeMessage 请传入消息（Message）实例"),!1):e.conversationType===so.CONV_SYSTEM?(console.warn("revokeMessage 不能撤回系统会话消息，只能撤回单聊消息或群消息"),!1):!0!==e.isRevoked||(console.warn("revokeMessage 消息已经被撤回，请勿重复操作"),!1)}})],deleteMessage:[xn(xn({name:"messageList"},Lc),{},{validator:function(e){return!Lu(e)||(console.warn(Nc({api:"deleteMessage",param:"messageList",desc:"不能为空数组"})),!1)}})],getUserProfile:{userIDList:{type:"Array",validator:function(e){return zi(e)?(0===e.length&&console.warn(Nc({api:"getUserProfile",param:"userIDList",desc:"不能为空数组"})),!0):(console.warn(Nc({api:"getUserProfile",param:"userIDList",desc:"必须为数组"})),!1)}}},updateMyProfile:{profileCustomField:{type:"Array",validator:function(e){return!!Ji(e)||!!zi(e)||(console.warn(Nc({api:"updateMyProfile",param:"profileCustomField",desc:"必须为数组"})),!1)}}},addFriend:{to:Oc,source:{type:"String",required:!0,validator:function(e){return!!e&&(e.startsWith("AddSource_Type_")?!(e.replace("AddSource_Type_","").length>8)||(console.warn(Nc({api:"addFriend",desc:"加好友来源字段的关键字长度不得超过8字节"})),!1):(console.warn(Nc({api:"addFriend",desc:"加好友来源字段的前缀必须是：AddSource_Type_"})),!1))}},remark:{type:"String",required:!1,validator:function(e){return!(Wi(e)&&e.length>96)||(console.warn(Nc({api:"updateFriend",desc:" 备注长度最长不得超过 96 个字节"})),!1)}}},deleteFriend:{userIDList:Lc},checkFriend:{userIDList:Lc},getFriendProfile:{userIDList:Lc},updateFriend:{userID:Oc,remark:{type:"String",required:!1,validator:function(e){return!(Wi(e)&&e.length>96)||(console.warn(Nc({api:"updateFriend",desc:" 备注长度最长不得超过 96 个字节"})),!1)}},friendCustomField:{type:"Array",required:!1,validator:function(e){if(e){if(!zi(e))return console.warn(Nc({api:"updateFriend",param:"friendCustomField",desc:"必须为数组"})),!1;var t=!0;return e.forEach((function(e){return Wi(e.key)&&-1!==e.key.indexOf("Tag_SNS_Custom")?Wi(e.value)?e.value.length>8?(console.warn(Nc({api:"updateFriend",desc:"好友自定义字段的关键字长度不得超过8字节"})),t=!1):void 0:(console.warn(Nc({api:"updateFriend",desc:"类型必须为 String"})),t=!1):(console.warn(Nc({api:"updateFriend",desc:"好友自定义字段的前缀必须是 Tag_SNS_Custom"})),t=!1)})),t}return!0}}},acceptFriendApplication:{userID:Oc},refuseFriendApplication:{userID:Oc},deleteFriendApplication:{userID:Oc},createFriendGroup:{name:Oc},deleteFriendGroup:{name:Oc},addToFriendGroup:{name:Oc,userIDList:Lc},removeFromFriendGroup:{name:Oc,userIDList:Lc},renameFriendGroup:{oldName:Oc,newName:Oc}},wc={login:"login",logout:"logout",on:"on",once:"once",off:"off",setLogLevel:"setLogLevel",registerPlugin:"registerPlugin",destroy:"destroy",createTextMessage:"createTextMessage",createTextAtMessage:"createTextAtMessage",createImageMessage:"createImageMessage",createAudioMessage:"createAudioMessage",createVideoMessage:"createVideoMessage",createCustomMessage:"createCustomMessage",createFaceMessage:"createFaceMessage",createFileMessage:"createFileMessage",createLocationMessage:"createLocationMessage",createMergerMessage:"createMergerMessage",downloadMergerMessage:"downloadMergerMessage",createForwardMessage:"createForwardMessage",sendMessage:"sendMessage",resendMessage:"resendMessage",revokeMessage:"revokeMessage",deleteMessage:"deleteMessage",getMessageList:"getMessageList",setMessageRead:"setMessageRead",setAllMessageRead:"setAllMessageRead",getConversationList:"getConversationList",getConversationProfile:"getConversationProfile",deleteConversation:"deleteConversation",pinConversation:"pinConversation",getGroupList:"getGroupList",getGroupProfile:"getGroupProfile",createGroup:"createGroup",joinGroup:"joinGroup",updateGroupProfile:"updateGroupProfile",quitGroup:"quitGroup",dismissGroup:"dismissGroup",changeGroupOwner:"changeGroupOwner",searchGroupByID:"searchGroupByID",setMessageRemindType:"setMessageRemindType",handleGroupApplication:"handleGroupApplication",initGroupAttributes:"initGroupAttributes",setGroupAttributes:"setGroupAttributes",deleteGroupAttributes:"deleteGroupAttributes",getGroupAttributes:"getGroupAttributes",getGroupMemberProfile:"getGroupMemberProfile",getGroupMemberList:"getGroupMemberList",addGroupMember:"addGroupMember",deleteGroupMember:"deleteGroupMember",setGroupMemberNameCard:"setGroupMemberNameCard",setGroupMemberMuteTime:"setGroupMemberMuteTime",setGroupMemberRole:"setGroupMemberRole",setGroupMemberCustomField:"setGroupMemberCustomField",getGroupOnlineMemberCount:"getGroupOnlineMemberCount",getMyProfile:"getMyProfile",getUserProfile:"getUserProfile",updateMyProfile:"updateMyProfile",getBlacklist:"getBlacklist",addToBlacklist:"addToBlacklist",removeFromBlacklist:"removeFromBlacklist",getFriendList:"getFriendList",addFriend:"addFriend",deleteFriend:"deleteFriend",checkFriend:"checkFriend",updateFriend:"updateFriend",getFriendProfile:"getFriendProfile",getFriendApplicationList:"getFriendApplicationList",refuseFriendApplication:"refuseFriendApplication",deleteFriendApplication:"deleteFriendApplication",acceptFriendApplication:"acceptFriendApplication",setFriendApplicationRead:"setFriendApplicationRead",getFriendGroupList:"getFriendGroupList",createFriendGroup:"createFriendGroup",renameFriendGroup:"renameFriendGroup",deleteFriendGroup:"deleteFriendGroup",addToFriendGroup:"addToFriendGroup",removeFromFriendGroup:"removeFromFriendGroup",callExperimentalAPI:"callExperimentalAPI"},Gc=!!kr&&a((function(){kr.prototype.finally.call({then:function(){}},(function(){}))}));be({target:"Promise",proto:!0,real:!0,forced:Gc},{finally:function(e){var t=wr(this,se("Promise")),n="function"==typeof e;return this.then(n?function(n){return va(t,e()).then((function(){return n}))}:e,n?function(n){return va(t,e()).then((function(){throw n}))}:e)}}),"function"!=typeof kr||kr.prototype.finally||oe(kr.prototype,"finally",se("Promise").prototype.finally);var Pc=[].slice,Uc=/MSIE .\./.test(je),Fc=function(e){return function(t,n){var o=arguments.length>2,r=o?Pc.call(arguments,2):void 0;return e(o?function(){("function"==typeof t?t:Function(t)).apply(this,r)}:t,n)}};be({global:!0,bind:!0,forced:Uc},{setTimeout:Fc(r.setTimeout),setInterval:Fc(r.setInterval)});var qc=it.filter,xc=Xe("filter"),Vc=pt("filter");be({target:"Array",proto:!0,forced:!xc||!Vc},{filter:function(e){return qc(this,e,arguments.length>1?arguments[1]:void 0)}});var Kc,Bc="sign",Hc="message",jc="user",Wc="c2c",$c="group",Yc="sns",zc="groupMember",Jc="conversation",Xc="context",Qc="storage",Zc="eventStat",el="netMonitor",tl="bigDataChannel",nl="upload",ol="plugin",rl="syncUnreadMessage",al="session",sl="channel",il="message_loss_detection",ul="cloudControl",cl="worker",ll="pullGroupMessage",dl="qualityStat",pl="commercialConfig",gl=function(){function e(t){Gn(this,e),this._moduleManager=t,this._className=""}return Un(e,[{key:"isLoggedIn",value:function(){return this._moduleManager.getModule(Xc).isLoggedIn()}},{key:"isOversea",value:function(){return this._moduleManager.getModule(Xc).isOversea()}},{key:"getMyUserID",value:function(){return this._moduleManager.getModule(Xc).getUserID()}},{key:"getModule",value:function(e){return this._moduleManager.getModule(e)}},{key:"getPlatform",value:function(){return li}},{key:"getNetworkType",value:function(){return this._moduleManager.getModule(el).getNetworkType()}},{key:"probeNetwork",value:function(){return this._moduleManager.getModule(el).probe()}},{key:"getCloudConfig",value:function(e){return this._moduleManager.getModule(ul).getCloudConfig(e)}},{key:"emitOuterEvent",value:function(e,t){this._moduleManager.getOuterEmitterInstance().emit(e,t)}},{key:"emitInnerEvent",value:function(e,t){this._moduleManager.getInnerEmitterInstance().emit(e,t)}},{key:"getInnerEmitterInstance",value:function(){return this._moduleManager.getInnerEmitterInstance()}},{key:"generateTjgID",value:function(e){return this._moduleManager.getModule(Xc).getTinyID()+"-"+e.random}},{key:"filterModifiedMessage",value:function(e){if(!Lu(e)){var t=e.filter((function(e){return!0===e.isModified}));t.length>0&&this.emitOuterEvent(ao.MESSAGE_MODIFIED,t)}}},{key:"filterUnmodifiedMessage",value:function(e){return Lu(e)?[]:e.filter((function(e){return!1===e.isModified}))}},{key:"request",value:function(e){return this._moduleManager.getModule(al).request(e)}},{key:"canIUse",value:function(e){return this._moduleManager.getModule(pl).hasPurchasedFeature(e)}}]),e}(),hl="wslogin",fl="wslogout",_l="wshello",ml="KickOther",vl="getmsg",Ml="authkey",yl="sendmsg",Il="send_group_msg",Tl="portrait_get_all",Cl="portrait_set",Sl="black_list_get",Al="black_list_add",El="black_list_delete",kl="msgwithdraw",Dl="msgreaded",Nl="set_c2c_peer_mute_notifications",Ol="get_c2c_peer_mute_notifications",Ll="getroammsg",Rl="get_peer_read_time",bl="delete_c2c_msg_ramble",wl="page_get",Gl="get",Pl="delete",Ul="top",Fl="deletemsg",ql="get_joined_group_list",xl="get_group_self_member_info",Vl="create_group",Kl="destroy_group",Bl="modify_group_base_info",Hl="apply_join_group",jl="apply_join_group_noauth",Wl="quit_group",$l="get_group_public_info",Yl="change_group_owner",zl="handle_apply_join_group",Jl="handle_invite_join_group",Xl="group_msg_recall",Ql="msg_read_report",Zl="read_all_unread_msg",ed="group_msg_get",td="get_pendency",nd="deletemsg",od="get_msg",rd="get_msg_noauth",ad="get_online_member_num",sd="delete_group_ramble_msg_by_seq",id="set_group_attr",ud="modify_group_attr",cd="delete_group_attr",ld="clear_group_attr",dd="get_group_attr",pd="get_group_member_info",gd="get_specified_group_member_info",hd="add_group_member",fd="delete_group_member",_d="modify_group_member_info",md="cos",vd="pre_sig",Md="tim_web_report_v2",yd="alive",Id="msg_push",Td="ws_msg_push_ack",Cd="stat_forceoffline",Sd="save_relay_json_msg",Ad="get_relay_json_msg",Ed="fetch_config",kd="push_configv2",Dd="fetch_imsdk_purchase_bitsv2",Nd="push_imsdk_purchase_bitsv2",Od="notify2",Ld={NO_SDKAPPID:2e3,NO_ACCOUNT_TYPE:2001,NO_IDENTIFIER:2002,NO_USERSIG:2003,NO_TINYID:2022,NO_A2KEY:2023,USER_NOT_LOGGED_IN:2024,REPEAT_LOGIN:2025,COS_UNDETECTED:2040,COS_GET_SIG_FAIL:2041,MESSAGE_SEND_FAIL:2100,MESSAGE_LIST_CONSTRUCTOR_NEED_OPTIONS:2103,MESSAGE_SEND_NEED_MESSAGE_INSTANCE:2105,MESSAGE_SEND_INVALID_CONVERSATION_TYPE:2106,MESSAGE_FILE_IS_EMPTY:2108,MESSAGE_ONPROGRESS_FUNCTION_ERROR:2109,MESSAGE_REVOKE_FAIL:2110,MESSAGE_DELETE_FAIL:2111,MESSAGE_UNREAD_ALL_FAIL:2112,MESSAGE_IMAGE_SELECT_FILE_FIRST:2251,MESSAGE_IMAGE_TYPES_LIMIT:2252,MESSAGE_IMAGE_SIZE_LIMIT:2253,MESSAGE_AUDIO_UPLOAD_FAIL:2300,MESSAGE_AUDIO_SIZE_LIMIT:2301,MESSAGE_VIDEO_UPLOAD_FAIL:2350,MESSAGE_VIDEO_SIZE_LIMIT:2351,MESSAGE_VIDEO_TYPES_LIMIT:2352,MESSAGE_FILE_UPLOAD_FAIL:2400,MESSAGE_FILE_SELECT_FILE_FIRST:2401,MESSAGE_FILE_SIZE_LIMIT:2402,MESSAGE_FILE_URL_IS_EMPTY:2403,MESSAGE_MERGER_TYPE_INVALID:2450,MESSAGE_MERGER_KEY_INVALID:2451,MESSAGE_MERGER_DOWNLOAD_FAIL:2452,MESSAGE_FORWARD_TYPE_INVALID:2453,CONVERSATION_NOT_FOUND:2500,USER_OR_GROUP_NOT_FOUND:2501,CONVERSATION_UN_RECORDED_TYPE:2502,ILLEGAL_GROUP_TYPE:2600,CANNOT_JOIN_WORK:2601,CANNOT_CHANGE_OWNER_IN_AVCHATROOM:2620,CANNOT_CHANGE_OWNER_TO_SELF:2621,CANNOT_DISMISS_Work:2622,MEMBER_NOT_IN_GROUP:2623,CANNOT_USE_GRP_ATTR_NOT_AVCHATROOM:2641,CANNOT_USE_GRP_ATTR_AVCHATROOM_UNJOIN:2642,JOIN_GROUP_FAIL:2660,CANNOT_ADD_MEMBER_IN_AVCHATROOM:2661,CANNOT_JOIN_NON_AVCHATROOM_WITHOUT_LOGIN:2662,CANNOT_KICK_MEMBER_IN_AVCHATROOM:2680,NOT_OWNER:2681,CANNOT_SET_MEMBER_ROLE_IN_WORK_AND_AVCHATROOM:2682,INVALID_MEMBER_ROLE:2683,CANNOT_SET_SELF_MEMBER_ROLE:2684,CANNOT_MUTE_SELF:2685,NOT_MY_FRIEND:2700,ALREADY_MY_FRIEND:2701,FRIEND_GROUP_EXISTED:2710,FRIEND_GROUP_NOT_EXIST:2711,FRIEND_APPLICATION_NOT_EXIST:2716,UPDATE_PROFILE_INVALID_PARAM:2721,UPDATE_PROFILE_NO_KEY:2722,ADD_BLACKLIST_INVALID_PARAM:2740,DEL_BLACKLIST_INVALID_PARAM:2741,CANNOT_ADD_SELF_TO_BLACKLIST:2742,ADD_FRIEND_INVALID_PARAM:2760,NETWORK_ERROR:2800,NETWORK_TIMEOUT:2801,NETWORK_BASE_OPTIONS_NO_URL:2802,NETWORK_UNDEFINED_SERVER_NAME:2803,NETWORK_PACKAGE_UNDEFINED:2804,NO_NETWORK:2805,CONVERTOR_IRREGULAR_PARAMS:2900,NOTICE_RUNLOOP_UNEXPECTED_CONDITION:2901,NOTICE_RUNLOOP_OFFSET_LOST:2902,UNCAUGHT_ERROR:2903,GET_LONGPOLL_ID_FAILED:2904,INVALID_OPERATION:2905,OVER_FREQUENCY_LIMIT:2996,CANNOT_FIND_PROTOCOL:2997,CANNOT_FIND_MODULE:2998,SDK_IS_NOT_READY:2999,LONG_POLL_KICK_OUT:91101,MESSAGE_A2KEY_EXPIRED:20002,ACCOUNT_A2KEY_EXPIRED:70001,LONG_POLL_API_PARAM_ERROR:90001,HELLO_ANSWER_KICKED_OUT:1002,OPEN_SERVICE_OVERLOAD_ERROR:60022},Rd="无 SDKAppID",bd="无 userID",wd="无 userSig",Gd="无 tinyID",Pd="无 a2key",Ud="用户未登录",Fd="重复登录",qd="未检测到 COS 上传插件",xd="获取 COS 预签名 URL 失败",Vd="消息发送失败",Kd="需要 Message 的实例",Bd='Message.conversationType 只能为 "C2C" 或 "GROUP"',Hd="无法发送空文件",jd="回调函数运行时遇到错误，请检查接入侧代码",Wd="消息撤回失败",$d="消息删除失败",Yd="设置所有未读消息为已读处理失败",zd="请先选择一个图片",Jd="只允许上传 jpg png jpeg gif bmp image 格式的图片",Xd="图片大小超过20M，无法发送",Qd="语音上传失败",Zd="语音大小大于20M，无法发送",ep="视频上传失败",tp="视频大小超过100M，无法发送",np="只允许上传 mp4 格式的视频",op="文件上传失败",rp="请先选择一个文件",ap="文件大小超过100M，无法发送 ",sp="缺少必要的参数文件 URL",ip="非合并消息",up="合并消息的 messageKey 无效",cp="下载合并消息失败",lp="选择的消息类型（如群提示消息）不可以转发",dp="没有找到相应的会话，请检查传入参数",pp="没有找到相应的用户或群组，请检查传入参数",gp="未记录的会话类型",hp="非法的群类型，请检查传入参数",fp="不能加入 Work 类型的群组",_p="AVChatRoom 类型的群组不能转让群主",mp="不能把群主转让给自己",vp="不能解散 Work 类型的群组",Mp="用户不在该群组内",yp="加群失败，请检查传入参数或重试",Ip="AVChatRoom 类型的群不支持邀请群成员",Tp="非 AVChatRoom 类型的群组不允许匿名加群，请先登录后再加群",Cp="不能在 AVChatRoom 类型的群组踢人",Sp="你不是群主，只有群主才有权限操作",Ap="不能在 Work / AVChatRoom 类型的群中设置群成员身份",Ep="不合法的群成员身份，请检查传入参数",kp="不能设置自己的群成员身份，请检查传入参数",Dp="不能将自己禁言，请检查传入参数",Np="传入 updateMyProfile 接口的参数无效",Op="updateMyProfile 无标配资料字段或自定义资料字段",Lp="传入 addToBlacklist 接口的参数无效",Rp="传入 removeFromBlacklist 接口的参数无效",bp="不能拉黑自己",wp="网络错误",Gp="请求超时",Pp="未连接到网络",Up="无效操作，如调用了未定义或者未实现的方法等",Fp="无法找到协议",qp="无法找到模块",xp="接口需要 SDK 处于 ready 状态后才能调用",Vp="超出 SDK 频率控制",Kp="后台服务正忙，请稍后再试",Bp="networkRTT",Hp="messageE2EDelay",jp="sendMessageC2C",Wp="sendMessageGroup",$p="sendMessageGroupAV",Yp="sendMessageRichMedia",zp="cosUpload",Jp="messageReceivedGroup",Xp="messageReceivedGroupAVPush",Qp="messageReceivedGroupAVPull",Zp=(Fn(Kc={},Bp,2),Fn(Kc,Hp,3),Fn(Kc,jp,4),Fn(Kc,Wp,5),Fn(Kc,$p,6),Fn(Kc,Yp,7),Fn(Kc,Jp,8),Fn(Kc,Xp,9),Fn(Kc,Qp,10),Fn(Kc,zp,11),Kc),eg={info:4,warning:5,error:6},tg={wifi:1,"2g":2,"3g":3,"4g":4,"5g":5,unknown:6,none:7,online:8},ng={login:4},og=function(){function e(t){Gn(this,e),this.eventType=ng[t]||0,this.timestamp=0,this.networkType=8,this.code=0,this.message="",this.moreMessage="",this.extension=t,this.costTime=0,this.duplicate=!1,this.level=4,this._sentFlag=!1,this._startts=Fi()}return Un(e,[{key:"updateTimeStamp",value:function(){this.timestamp=Fi()}},{key:"start",value:function(e){return this._startts=e,this}},{key:"end",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this._sentFlag){var n=Fi();0===this.costTime&&(this.costTime=n-this._startts),this.setMoreMessage("startts:".concat(this._startts," endts:").concat(n)),t?(this._sentFlag=!0,this._eventStatModule&&this._eventStatModule.pushIn(this)):setTimeout((function(){e._sentFlag=!0,e._eventStatModule&&e._eventStatModule.pushIn(e)}),0)}}},{key:"setError",value:function(e,t,n){return e instanceof Error?(this._sentFlag||(this.setNetworkType(n),t?(e.code&&this.setCode(e.code),e.message&&this.setMoreMessage(e.message)):(this.setCode(Ld.NO_NETWORK),this.setMoreMessage(Pp)),this.setLevel("error")),this):(Bi.warn("SSOLogData.setError value not instanceof Error, please check!"),this)}},{key:"setCode",value:function(e){return Ji(e)||this._sentFlag||("ECONNABORTED"===e&&(this.code=103),ji(e)?this.code=e:Bi.warn("SSOLogData.setCode value not a number, please check!",e,wn(e))),this}},{key:"setMessage",value:function(e){return Ji(e)||this._sentFlag||(ji(e)&&(this.message=e.toString()),Wi(e)&&(this.message=e)),this}},{key:"setCostTime",value:function(e){return this.costTime=e,this}},{key:"setLevel",value:function(e){return Ji(e)||this._sentFlag||(this.level=eg[e]),this}},{key:"setMoreMessage",value:function(e){return Lu(this.moreMessage)?this.moreMessage="".concat(e):this.moreMessage+=" ".concat(e),this}},{key:"setNetworkType",value:function(e){if(Ji(e))Bi.warn("SSOLogData.setNetworkType value is undefined, please check!");else{var t=tg[e.toLowerCase()];Ji(t)||(this.networkType=t)}return this}},{key:"getStartTs",value:function(){return this._startts}}],[{key:"bindEventStatModule",value:function(t){e.prototype._eventStatModule=t}}]),e}(),rg="sdkConstruct",ag="sdkReady",sg="login",ig="logout",ug="kickedOut",cg="registerPlugin",lg="kickOther",dg="wsConnect",pg="wsOnOpen",gg="wsOnClose",hg="wsOnError",fg="networkChange",_g="getCosAuthKey",mg="getCosPreSigUrl",vg="upload",Mg="sendMessage",yg="getC2CRoamingMessages",Ig="getGroupRoamingMessages",Tg="revokeMessage",Cg="deleteMessage",Sg="setC2CMessageRead",Ag="setGroupMessageRead",Eg="emptyMessageBody",kg="getPeerReadTime",Dg="uploadMergerMessage",Ng="downloadMergerMessage",Og="jsonParseError",Lg="messageE2EDelayException",Rg="getConversationList",bg="getConversationProfile",wg="deleteConversation",Gg="pinConversation",Pg="getConversationListInStorage",Ug="syncConversationList",Fg="setAllMessageRead",qg="createGroup",xg="applyJoinGroup",Vg="quitGroup",Kg="searchGroupByID",Bg="changeGroupOwner",Hg="handleGroupApplication",jg="handleGroupInvitation",Wg="setMessageRemindType",$g="dismissGroup",Yg="updateGroupProfile",zg="getGroupList",Jg="getGroupProfile",Xg="getGroupListInStorage",Qg="getGroupLastSequence",Zg="getGroupMissingMessage",eh="pagingGetGroupList",th="getGroupSimplifiedInfo",nh="joinWithoutAuth",oh="initGroupAttributes",rh="setGroupAttributes",ah="deleteGroupAttributes",sh="getGroupAttributes",ih="getGroupMemberList",uh="getGroupMemberProfile",ch="addGroupMember",lh="deleteGroupMember",dh="setGroupMemberMuteTime",ph="setGroupMemberNameCard",gh="setGroupMemberRole",hh="setGroupMemberCustomField",fh="getGroupOnlineMemberCount",_h="longPollingAVError",mh="messageLoss",vh="messageStacked",Mh="getUserProfile",yh="updateMyProfile",Ih="getBlacklist",Th="addToBlacklist",Ch="removeFromBlacklist",Sh="callbackFunctionError",Ah="fetchCloudControlConfig",Eh="pushedCloudControlConfig",kh="fetchCommercialConfig",Dh="pushedCommercialConfig",Nh="error",Oh="lastMessageNotExist",Lh=l.f,Rh=function(e){return function(t){for(var n,o=m(t),r=Ut(o),a=r.length,s=0,u=[];a>s;)n=r[s++],i&&!Lh.call(o,n)||u.push(e?[n,o[n]]:o[n]);return u}},bh={entries:Rh(!0),values:Rh(!1)}.values;be({target:"Object",stat:!0},{values:function(e){return bh(e)}});var wh=function(){function e(t){Gn(this,e),this.type=so.MSG_TEXT,this.content={text:t.text||""}}return Un(e,[{key:"setText",value:function(e){this.content.text=e}},{key:"sendable",value:function(){return 0!==this.content.text.length}}]),e}(),Gh=Object.assign,Ph=Object.defineProperty,Uh=!Gh||a((function(){if(i&&1!==Gh({b:1},Gh(Ph({},"a",{enumerable:!0,get:function(){Ph(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol();return e[n]=7,"abcdefghijklmnopqrst".split("").forEach((function(e){t[e]=e})),7!=Gh({},e)[n]||"abcdefghijklmnopqrst"!=Ut(Gh({},t)).join("")}))?function(e,t){for(var n=Ge(e),o=arguments.length,r=1,a=Te.f,s=l.f;o>r;)for(var u,c=f(arguments[r++]),d=a?Ut(c).concat(a(c)):Ut(c),p=d.length,g=0;p>g;)u=d[g++],i&&!s.call(c,u)||(n[u]=c[u]);return n}:Gh;be({target:"Object",stat:!0,forced:Object.assign!==Uh},{assign:Uh});var Fh=Ke("iterator"),qh=!a((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),t=e.searchParams,n="";return e.pathname="c%20d",t.forEach((function(e,o){t.delete("b"),n+=o+e})),!t.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[Fh]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==n||"x"!==new URL("http://x",void 0).host})),xh=/[^\0-\u007E]/,Vh=/[.\u3002\uFF0E\uFF61]/g,Kh="Overflow: input needs wider integers to process",Bh=Math.floor,Hh=String.fromCharCode,jh=function(e){return e+22+75*(e<26)},Wh=function(e,t,n){var o=0;for(e=n?Bh(e/700):e>>1,e+=Bh(e/t);e>455;o+=36)e=Bh(e/35);return Bh(o+36*e/(e+38))},$h=function(e){var t,n,o=[],r=(e=function(e){for(var t=[],n=0,o=e.length;n<o;){var r=e.charCodeAt(n++);if(r>=55296&&r<=56319&&n<o){var a=e.charCodeAt(n++);56320==(64512&a)?t.push(((1023&r)<<10)+(1023&a)+65536):(t.push(r),n--)}else t.push(r)}return t}(e)).length,a=128,s=0,i=72;for(t=0;t<e.length;t++)(n=e[t])<128&&o.push(Hh(n));var u=o.length,c=u;for(u&&o.push("-");c<r;){var l=2147483647;for(t=0;t<e.length;t++)(n=e[t])>=a&&n<l&&(l=n);var d=c+1;if(l-a>Bh((2147483647-s)/d))throw RangeError(Kh);for(s+=(l-a)*d,a=l,t=0;t<e.length;t++){if((n=e[t])<a&&++s>2147483647)throw RangeError(Kh);if(n==a){for(var p=s,g=36;;g+=36){var h=g<=i?1:g>=i+26?26:g-i;if(p<h)break;var f=p-h,_=36-h;o.push(Hh(jh(h+f%_))),p=Bh(f/_)}o.push(Hh(jh(p))),i=Wh(s,d,c==u),s=0,++c}}++s,++a}return o.join("")},Yh=function(e){var t=Dt(e);if("function"!=typeof t)throw TypeError(String(e)+" is not iterable");return D(t.call(e))},zh=se("fetch"),Jh=se("Headers"),Xh=Ke("iterator"),Qh=ne.set,Zh=ne.getterFor("URLSearchParams"),ef=ne.getterFor("URLSearchParamsIterator"),tf=/\+/g,nf=Array(4),of=function(e){return nf[e-1]||(nf[e-1]=RegExp("((?:%[\\da-f]{2}){"+e+"})","gi"))},rf=function(e){try{return decodeURIComponent(e)}catch(eT){return e}},af=function(e){var t=e.replace(tf," "),n=4;try{return decodeURIComponent(t)}catch(eT){for(;n;)t=t.replace(of(n--),rf);return t}},sf=/[!'()~]|%20/g,uf={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},cf=function(e){return uf[e]},lf=function(e){return encodeURIComponent(e).replace(sf,cf)},df=function(e,t){if(t)for(var n,o,r=t.split("&"),a=0;a<r.length;)(n=r[a++]).length&&(o=n.split("="),e.push({key:af(o.shift()),value:af(o.join("="))}))},pf=function(e){this.entries.length=0,df(this.entries,e)},gf=function(e,t){if(e<t)throw TypeError("Not enough arguments")},hf=yn((function(e,t){Qh(this,{type:"URLSearchParamsIterator",iterator:Yh(Zh(e).entries),kind:t})}),"Iterator",(function(){var e=ef(this),t=e.kind,n=e.iterator.next(),o=n.value;return n.done||(n.value="keys"===t?o.key:"values"===t?o.value:[o.key,o.value]),n})),ff=function e(){Lr(this,e,"URLSearchParams");var t,n,o,r,a,s,i,u,c,l=arguments.length>0?arguments[0]:void 0,d=this,p=[];if(Qh(d,{type:"URLSearchParams",entries:p,updateURL:function(){},updateSearchParams:pf}),void 0!==l)if(v(l))if("function"==typeof(t=Dt(l)))for(o=(n=t.call(l)).next;!(r=o.call(n)).done;){if((i=(s=(a=Yh(D(r.value))).next).call(a)).done||(u=s.call(a)).done||!s.call(a).done)throw TypeError("Expected sequence with length 2");p.push({key:i.value+"",value:u.value+""})}else for(c in l)I(l,c)&&p.push({key:c,value:l[c]+""});else df(p,"string"==typeof l?"?"===l.charAt(0)?l.slice(1):l:l+"")},_f=ff.prototype;Dr(_f,{append:function(e,t){gf(arguments.length,2);var n=Zh(this);n.entries.push({key:e+"",value:t+""}),n.updateURL()},delete:function(e){gf(arguments.length,1);for(var t=Zh(this),n=t.entries,o=e+"",r=0;r<n.length;)n[r].key===o?n.splice(r,1):r++;t.updateURL()},get:function(e){gf(arguments.length,1);for(var t=Zh(this).entries,n=e+"",o=0;o<t.length;o++)if(t[o].key===n)return t[o].value;return null},getAll:function(e){gf(arguments.length,1);for(var t=Zh(this).entries,n=e+"",o=[],r=0;r<t.length;r++)t[r].key===n&&o.push(t[r].value);return o},has:function(e){gf(arguments.length,1);for(var t=Zh(this).entries,n=e+"",o=0;o<t.length;)if(t[o++].key===n)return!0;return!1},set:function(e,t){gf(arguments.length,1);for(var n,o=Zh(this),r=o.entries,a=!1,s=e+"",i=t+"",u=0;u<r.length;u++)(n=r[u]).key===s&&(a?r.splice(u--,1):(a=!0,n.value=i));a||r.push({key:s,value:i}),o.updateURL()},sort:function(){var e,t,n,o=Zh(this),r=o.entries,a=r.slice();for(r.length=0,n=0;n<a.length;n++){for(e=a[n],t=0;t<n;t++)if(r[t].key>e.key){r.splice(t,0,e);break}t===n&&r.push(e)}o.updateURL()},forEach:function(e){for(var t,n=Zh(this).entries,o=rt(e,arguments.length>1?arguments[1]:void 0,3),r=0;r<n.length;)o((t=n[r++]).value,t.key,this)},keys:function(){return new hf(this,"keys")},values:function(){return new hf(this,"values")},entries:function(){return new hf(this,"entries")}},{enumerable:!0}),oe(_f,Xh,_f.entries),oe(_f,"toString",(function(){for(var e,t=Zh(this).entries,n=[],o=0;o<t.length;)e=t[o++],n.push(lf(e.key)+"="+lf(e.value));return n.join("&")}),{enumerable:!0}),mn(ff,"URLSearchParams"),be({global:!0,forced:!qh},{URLSearchParams:ff}),qh||"function"!=typeof zh||"function"!=typeof Jh||be({global:!0,enumerable:!0,forced:!0},{fetch:function(e){var t,n,o,r=[e];return arguments.length>1&&(v(t=arguments[1])&&(n=t.body,"URLSearchParams"===Et(n)&&((o=t.headers?new Jh(t.headers):new Jh).has("content-type")||o.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),t=Ht(t,{body:d(0,String(n)),headers:d(0,o)}))),r.push(t)),zh.apply(this,r)}});var mf,vf={URLSearchParams:ff,getState:Zh},Mf=sn.codeAt,yf=r.URL,If=vf.URLSearchParams,Tf=vf.getState,Cf=ne.set,Sf=ne.getterFor("URL"),Af=Math.floor,Ef=Math.pow,kf=/[A-Za-z]/,Df=/[\d+-.A-Za-z]/,Nf=/\d/,Of=/^(0x|0X)/,Lf=/^[0-7]+$/,Rf=/^\d+$/,bf=/^[\dA-Fa-f]+$/,wf=/[\u0000\u0009\u000A\u000D #%/:?@[\\]]/,Gf=/[\u0000\u0009\u000A\u000D #/:?@[\\]]/,Pf=/^[\u0000-\u001F ]+|[\u0000-\u001F ]+$/g,Uf=/[\u0009\u000A\u000D]/g,Ff=function(e,t){var n,o,r;if("["==t.charAt(0)){if("]"!=t.charAt(t.length-1))return"Invalid host";if(!(n=xf(t.slice(1,-1))))return"Invalid host";e.host=n}else if(Yf(e)){if(t=function(e){var t,n,o=[],r=e.toLowerCase().replace(Vh,".").split(".");for(t=0;t<r.length;t++)n=r[t],o.push(xh.test(n)?"xn--"+$h(n):n);return o.join(".")}(t),wf.test(t))return"Invalid host";if(null===(n=qf(t)))return"Invalid host";e.host=n}else{if(Gf.test(t))return"Invalid host";for(n="",o=Nt(t),r=0;r<o.length;r++)n+=Wf(o[r],Kf);e.host=n}},qf=function(e){var t,n,o,r,a,s,i,u=e.split(".");if(u.length&&""==u[u.length-1]&&u.pop(),(t=u.length)>4)return e;for(n=[],o=0;o<t;o++){if(""==(r=u[o]))return e;if(a=10,r.length>1&&"0"==r.charAt(0)&&(a=Of.test(r)?16:8,r=r.slice(8==a?1:2)),""===r)s=0;else{if(!(10==a?Rf:8==a?Lf:bf).test(r))return e;s=parseInt(r,a)}n.push(s)}for(o=0;o<t;o++)if(s=n[o],o==t-1){if(s>=Ef(256,5-t))return null}else if(s>255)return null;for(i=n.pop(),o=0;o<n.length;o++)i+=n[o]*Ef(256,3-o);return i},xf=function(e){var t,n,o,r,a,s,i,u=[0,0,0,0,0,0,0,0],c=0,l=null,d=0,p=function(){return e.charAt(d)};if(":"==p()){if(":"!=e.charAt(1))return;d+=2,l=++c}for(;p();){if(8==c)return;if(":"!=p()){for(t=n=0;n<4&&bf.test(p());)t=16*t+parseInt(p(),16),d++,n++;if("."==p()){if(0==n)return;if(d-=n,c>6)return;for(o=0;p();){if(r=null,o>0){if(!("."==p()&&o<4))return;d++}if(!Nf.test(p()))return;for(;Nf.test(p());){if(a=parseInt(p(),10),null===r)r=a;else{if(0==r)return;r=10*r+a}if(r>255)return;d++}u[c]=256*u[c]+r,2!=++o&&4!=o||c++}if(4!=o)return;break}if(":"==p()){if(d++,!p())return}else if(p())return;u[c++]=t}else{if(null!==l)return;d++,l=++c}}if(null!==l)for(s=c-l,c=7;0!=c&&s>0;)i=u[c],u[c--]=u[l+s-1],u[l+--s]=i;else if(8!=c)return;return u},Vf=function(e){var t,n,o,r;if("number"==typeof e){for(t=[],n=0;n<4;n++)t.unshift(e%256),e=Af(e/256);return t.join(".")}if("object"==s(e)){for(t="",o=function(e){for(var t=null,n=1,o=null,r=0,a=0;a<8;a++)0!==e[a]?(r>n&&(t=o,n=r),o=null,r=0):(null===o&&(o=a),++r);return r>n&&(t=o,n=r),t}(e),n=0;n<8;n++)r&&0===e[n]||(r&&(r=!1),o===n?(t+=n?":":"::",r=!0):(t+=e[n].toString(16),n<7&&(t+=":")));return"["+t+"]"}return e},Kf={},Bf=Uh({},Kf,{" ":1,'"':1,"<":1,">":1,"`":1}),Hf=Uh({},Bf,{"#":1,"?":1,"{":1,"}":1}),jf=Uh({},Hf,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),Wf=function(e,t){var n=Mf(e,0);return n>32&&n<127&&!I(t,e)?e:encodeURIComponent(e)},$f={ftp:21,file:null,http:80,https:443,ws:80,wss:443},Yf=function(e){return I($f,e.scheme)},zf=function(e){return""!=e.username||""!=e.password},Jf=function(e){return!e.host||e.cannotBeABaseURL||"file"==e.scheme},Xf=function(e,t){var n;return 2==e.length&&kf.test(e.charAt(0))&&(":"==(n=e.charAt(1))||!t&&"|"==n)},Qf=function(e){var t;return e.length>1&&Xf(e.slice(0,2))&&(2==e.length||"/"===(t=e.charAt(2))||"\\"===t||"?"===t||"#"===t)},Zf=function(e){var t=e.path,n=t.length;!n||"file"==e.scheme&&1==n&&Xf(t[0],!0)||t.pop()},e_=function(e){return"."===e||"%2e"===e.toLowerCase()},t_={},n_={},o_={},r_={},a_={},s_={},i_={},u_={},c_={},l_={},d_={},p_={},g_={},h_={},f_={},__={},m_={},v_={},M_={},y_={},I_={},T_=function(e,t,n,o){var r,a,s,i,u,c=n||t_,l=0,d="",p=!1,g=!1,h=!1;for(n||(e.scheme="",e.username="",e.password="",e.host=null,e.port=null,e.path=[],e.query=null,e.fragment=null,e.cannotBeABaseURL=!1,t=t.replace(Pf,"")),t=t.replace(Uf,""),r=Nt(t);l<=r.length;){switch(a=r[l],c){case t_:if(!a||!kf.test(a)){if(n)return"Invalid scheme";c=o_;continue}d+=a.toLowerCase(),c=n_;break;case n_:if(a&&(Df.test(a)||"+"==a||"-"==a||"."==a))d+=a.toLowerCase();else{if(":"!=a){if(n)return"Invalid scheme";d="",c=o_,l=0;continue}if(n&&(Yf(e)!=I($f,d)||"file"==d&&(zf(e)||null!==e.port)||"file"==e.scheme&&!e.host))return;if(e.scheme=d,n)return void(Yf(e)&&$f[e.scheme]==e.port&&(e.port=null));d="","file"==e.scheme?c=h_:Yf(e)&&o&&o.scheme==e.scheme?c=r_:Yf(e)?c=u_:"/"==r[l+1]?(c=a_,l++):(e.cannotBeABaseURL=!0,e.path.push(""),c=M_)}break;case o_:if(!o||o.cannotBeABaseURL&&"#"!=a)return"Invalid scheme";if(o.cannotBeABaseURL&&"#"==a){e.scheme=o.scheme,e.path=o.path.slice(),e.query=o.query,e.fragment="",e.cannotBeABaseURL=!0,c=I_;break}c="file"==o.scheme?h_:s_;continue;case r_:if("/"!=a||"/"!=r[l+1]){c=s_;continue}c=c_,l++;break;case a_:if("/"==a){c=l_;break}c=v_;continue;case s_:if(e.scheme=o.scheme,a==mf)e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,e.path=o.path.slice(),e.query=o.query;else if("/"==a||"\\"==a&&Yf(e))c=i_;else if("?"==a)e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,e.path=o.path.slice(),e.query="",c=y_;else{if("#"!=a){e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,e.path=o.path.slice(),e.path.pop(),c=v_;continue}e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,e.path=o.path.slice(),e.query=o.query,e.fragment="",c=I_}break;case i_:if(!Yf(e)||"/"!=a&&"\\"!=a){if("/"!=a){e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,c=v_;continue}c=l_}else c=c_;break;case u_:if(c=c_,"/"!=a||"/"!=d.charAt(l+1))continue;l++;break;case c_:if("/"!=a&&"\\"!=a){c=l_;continue}break;case l_:if("@"==a){p&&(d="%40"+d),p=!0,s=Nt(d);for(var f=0;f<s.length;f++){var _=s[f];if(":"!=_||h){var m=Wf(_,jf);h?e.password+=m:e.username+=m}else h=!0}d=""}else if(a==mf||"/"==a||"?"==a||"#"==a||"\\"==a&&Yf(e)){if(p&&""==d)return"Invalid authority";l-=Nt(d).length+1,d="",c=d_}else d+=a;break;case d_:case p_:if(n&&"file"==e.scheme){c=__;continue}if(":"!=a||g){if(a==mf||"/"==a||"?"==a||"#"==a||"\\"==a&&Yf(e)){if(Yf(e)&&""==d)return"Invalid host";if(n&&""==d&&(zf(e)||null!==e.port))return;if(i=Ff(e,d))return i;if(d="",c=m_,n)return;continue}"["==a?g=!0:"]"==a&&(g=!1),d+=a}else{if(""==d)return"Invalid host";if(i=Ff(e,d))return i;if(d="",c=g_,n==p_)return}break;case g_:if(!Nf.test(a)){if(a==mf||"/"==a||"?"==a||"#"==a||"\\"==a&&Yf(e)||n){if(""!=d){var v=parseInt(d,10);if(v>65535)return"Invalid port";e.port=Yf(e)&&v===$f[e.scheme]?null:v,d=""}if(n)return;c=m_;continue}return"Invalid port"}d+=a;break;case h_:if(e.scheme="file","/"==a||"\\"==a)c=f_;else{if(!o||"file"!=o.scheme){c=v_;continue}if(a==mf)e.host=o.host,e.path=o.path.slice(),e.query=o.query;else if("?"==a)e.host=o.host,e.path=o.path.slice(),e.query="",c=y_;else{if("#"!=a){Qf(r.slice(l).join(""))||(e.host=o.host,e.path=o.path.slice(),Zf(e)),c=v_;continue}e.host=o.host,e.path=o.path.slice(),e.query=o.query,e.fragment="",c=I_}}break;case f_:if("/"==a||"\\"==a){c=__;break}o&&"file"==o.scheme&&!Qf(r.slice(l).join(""))&&(Xf(o.path[0],!0)?e.path.push(o.path[0]):e.host=o.host),c=v_;continue;case __:if(a==mf||"/"==a||"\\"==a||"?"==a||"#"==a){if(!n&&Xf(d))c=v_;else if(""==d){if(e.host="",n)return;c=m_}else{if(i=Ff(e,d))return i;if("localhost"==e.host&&(e.host=""),n)return;d="",c=m_}continue}d+=a;break;case m_:if(Yf(e)){if(c=v_,"/"!=a&&"\\"!=a)continue}else if(n||"?"!=a)if(n||"#"!=a){if(a!=mf&&(c=v_,"/"!=a))continue}else e.fragment="",c=I_;else e.query="",c=y_;break;case v_:if(a==mf||"/"==a||"\\"==a&&Yf(e)||!n&&("?"==a||"#"==a)){if(".."===(u=(u=d).toLowerCase())||"%2e."===u||".%2e"===u||"%2e%2e"===u?(Zf(e),"/"==a||"\\"==a&&Yf(e)||e.path.push("")):e_(d)?"/"==a||"\\"==a&&Yf(e)||e.path.push(""):("file"==e.scheme&&!e.path.length&&Xf(d)&&(e.host&&(e.host=""),d=d.charAt(0)+":"),e.path.push(d)),d="","file"==e.scheme&&(a==mf||"?"==a||"#"==a))for(;e.path.length>1&&""===e.path[0];)e.path.shift();"?"==a?(e.query="",c=y_):"#"==a&&(e.fragment="",c=I_)}else d+=Wf(a,Hf);break;case M_:"?"==a?(e.query="",c=y_):"#"==a?(e.fragment="",c=I_):a!=mf&&(e.path[0]+=Wf(a,Kf));break;case y_:n||"#"!=a?a!=mf&&("'"==a&&Yf(e)?e.query+="%27":e.query+="#"==a?"%23":Wf(a,Kf)):(e.fragment="",c=I_);break;case I_:a!=mf&&(e.fragment+=Wf(a,Bf))}l++}},C_=function e(t){var n,o,r=Lr(this,e,"URL"),a=arguments.length>1?arguments[1]:void 0,s=String(t),u=Cf(r,{type:"URL"});if(void 0!==a)if(a instanceof e)n=Sf(a);else if(o=T_(n={},String(a)))throw TypeError(o);if(o=T_(u,s,null,n))throw TypeError(o);var c=u.searchParams=new If,l=Tf(c);l.updateSearchParams(u.query),l.updateURL=function(){u.query=String(c)||null},i||(r.href=A_.call(r),r.origin=E_.call(r),r.protocol=k_.call(r),r.username=D_.call(r),r.password=N_.call(r),r.host=O_.call(r),r.hostname=L_.call(r),r.port=R_.call(r),r.pathname=b_.call(r),r.search=w_.call(r),r.searchParams=G_.call(r),r.hash=P_.call(r))},S_=C_.prototype,A_=function(){var e=Sf(this),t=e.scheme,n=e.username,o=e.password,r=e.host,a=e.port,s=e.path,i=e.query,u=e.fragment,c=t+":";return null!==r?(c+="//",zf(e)&&(c+=n+(o?":"+o:"")+"@"),c+=Vf(r),null!==a&&(c+=":"+a)):"file"==t&&(c+="//"),c+=e.cannotBeABaseURL?s[0]:s.length?"/"+s.join("/"):"",null!==i&&(c+="?"+i),null!==u&&(c+="#"+u),c},E_=function(){var e=Sf(this),t=e.scheme,n=e.port;if("blob"==t)try{return new URL(t.path[0]).origin}catch(eT){return"null"}return"file"!=t&&Yf(e)?t+"://"+Vf(e.host)+(null!==n?":"+n:""):"null"},k_=function(){return Sf(this).scheme+":"},D_=function(){return Sf(this).username},N_=function(){return Sf(this).password},O_=function(){var e=Sf(this),t=e.host,n=e.port;return null===t?"":null===n?Vf(t):Vf(t)+":"+n},L_=function(){var e=Sf(this).host;return null===e?"":Vf(e)},R_=function(){var e=Sf(this).port;return null===e?"":String(e)},b_=function(){var e=Sf(this),t=e.path;return e.cannotBeABaseURL?t[0]:t.length?"/"+t.join("/"):""},w_=function(){var e=Sf(this).query;return e?"?"+e:""},G_=function(){return Sf(this).searchParams},P_=function(){var e=Sf(this).fragment;return e?"#"+e:""},U_=function(e,t){return{get:e,set:t,configurable:!0,enumerable:!0}};if(i&&Ft(S_,{href:U_(A_,(function(e){var t=Sf(this),n=String(e),o=T_(t,n);if(o)throw TypeError(o);Tf(t.searchParams).updateSearchParams(t.query)})),origin:U_(E_),protocol:U_(k_,(function(e){var t=Sf(this);T_(t,String(e)+":",t_)})),username:U_(D_,(function(e){var t=Sf(this),n=Nt(String(e));if(!Jf(t)){t.username="";for(var o=0;o<n.length;o++)t.username+=Wf(n[o],jf)}})),password:U_(N_,(function(e){var t=Sf(this),n=Nt(String(e));if(!Jf(t)){t.password="";for(var o=0;o<n.length;o++)t.password+=Wf(n[o],jf)}})),host:U_(O_,(function(e){var t=Sf(this);t.cannotBeABaseURL||T_(t,String(e),d_)})),hostname:U_(L_,(function(e){var t=Sf(this);t.cannotBeABaseURL||T_(t,String(e),p_)})),port:U_(R_,(function(e){var t=Sf(this);Jf(t)||(""==(e=String(e))?t.port=null:T_(t,e,g_))})),pathname:U_(b_,(function(e){var t=Sf(this);t.cannotBeABaseURL||(t.path=[],T_(t,e+"",m_))})),search:U_(w_,(function(e){var t=Sf(this);""==(e=String(e))?t.query=null:("?"==e.charAt(0)&&(e=e.slice(1)),t.query="",T_(t,e,y_)),Tf(t.searchParams).updateSearchParams(t.query)})),searchParams:U_(G_),hash:U_(P_,(function(e){var t=Sf(this);""!=(e=String(e))?("#"==e.charAt(0)&&(e=e.slice(1)),t.fragment="",T_(t,e,I_)):t.fragment=null}))}),oe(S_,"toJSON",(function(){return A_.call(this)}),{enumerable:!0}),oe(S_,"toString",(function(){return A_.call(this)}),{enumerable:!0}),yf){var F_=yf.createObjectURL,q_=yf.revokeObjectURL;F_&&oe(C_,"createObjectURL",(function(e){return F_.apply(yf,arguments)})),q_&&oe(C_,"revokeObjectURL",(function(e){return q_.apply(yf,arguments)}))}mn(C_,"URL"),be({global:!0,forced:!qh,sham:!i},{URL:C_});var x_={JSON:{TYPE:{C2C:{NOTICE:1,COMMON:9,EVENT:10},GROUP:{COMMON:3,TIP:4,SYSTEM:5,TIP2:6},FRIEND:{NOTICE:7},PROFILE:{NOTICE:8}},SUBTYPE:{C2C:{COMMON:0,READED:92,KICKEDOUT:96},GROUP:{COMMON:0,LOVEMESSAGE:1,TIP:2,REDPACKET:3}},OPTIONS:{GROUP:{JOIN:1,QUIT:2,KICK:3,SET_ADMIN:4,CANCEL_ADMIN:5,MODIFY_GROUP_INFO:6,MODIFY_MEMBER_INFO:7}}},PROTOBUF:{},IMAGE_TYPES:{ORIGIN:1,LARGE:2,SMALL:3},IMAGE_FORMAT:{JPG:1,JPEG:1,GIF:2,PNG:3,BMP:4,UNKNOWN:255}},V_={NICK:"Tag_Profile_IM_Nick",GENDER:"Tag_Profile_IM_Gender",BIRTHDAY:"Tag_Profile_IM_BirthDay",LOCATION:"Tag_Profile_IM_Location",SELFSIGNATURE:"Tag_Profile_IM_SelfSignature",ALLOWTYPE:"Tag_Profile_IM_AllowType",LANGUAGE:"Tag_Profile_IM_Language",AVATAR:"Tag_Profile_IM_Image",MESSAGESETTINGS:"Tag_Profile_IM_MsgSettings",ADMINFORBIDTYPE:"Tag_Profile_IM_AdminForbidType",LEVEL:"Tag_Profile_IM_Level",ROLE:"Tag_Profile_IM_Role"},K_={UNKNOWN:"Gender_Type_Unknown",FEMALE:"Gender_Type_Female",MALE:"Gender_Type_Male"},B_={NONE:"AdminForbid_Type_None",SEND_OUT:"AdminForbid_Type_SendOut"},H_={NEED_CONFIRM:"AllowType_Type_NeedConfirm",ALLOW_ANY:"AllowType_Type_AllowAny",DENY_ANY:"AllowType_Type_DenyAny"},j_="JoinedSuccess",W_="WaitAdminApproval",$_=function(){function e(t){Gn(this,e),this._imageMemoryURL="",ii?this.createImageDataASURLInWXMiniApp(t.file):this.createImageDataASURLInWeb(t.file),this._initImageInfoModel(),this.type=so.MSG_IMAGE,this._percent=0,this.content={imageFormat:t.imageFormat||x_.IMAGE_FORMAT.UNKNOWN,uuid:t.uuid,imageInfoArray:[]},this.initImageInfoArray(t.imageInfoArray),this._defaultImage="http://imgcache.qq.com/open/qcloud/video/act/webim-images/default.jpg",this._autoFixUrl()}return Un(e,[{key:"_initImageInfoModel",value:function(){var e=this;this._ImageInfoModel=function(t){this.instanceID=iu(9999999),this.sizeType=t.type||0,this.type=0,this.size=t.size||0,this.width=t.width||0,this.height=t.height||0,this.imageUrl=t.url||"",this.url=t.url||e._imageMemoryURL||e._defaultImage},this._ImageInfoModel.prototype={setSizeType:function(e){this.sizeType=e},setType:function(e){this.type=e},setImageUrl:function(e){e&&(this.imageUrl=e)},getImageUrl:function(){return this.imageUrl}}}},{key:"initImageInfoArray",value:function(e){for(var t=0,n=null,o=null;t<=2;)o=Ji(e)||Ji(e[t])?{type:0,size:0,width:0,height:0,url:""}:e[t],(n=new this._ImageInfoModel(o)).setSizeType(t+1),n.setType(t),this.addImageInfo(n),t++;this.updateAccessSideImageInfoArray()}},{key:"updateImageInfoArray",value:function(e){for(var t,n=this.content.imageInfoArray.length,o=0;o<n;o++)t=this.content.imageInfoArray[o],e[o].size&&(t.size=e[o].size),e[o].url&&t.setImageUrl(e[o].url),e[o].width&&(t.width=e[o].width),e[o].height&&(t.height=e[o].height)}},{key:"_autoFixUrl",value:function(){for(var e=this.content.imageInfoArray.length,t="",n="",o=["http","https"],r=null,a=0;a<e;a++)this.content.imageInfoArray[a].url&&""!==(r=this.content.imageInfoArray[a]).imageUrl&&(n=r.imageUrl.slice(0,r.imageUrl.indexOf("://")+1),t=r.imageUrl.slice(r.imageUrl.indexOf("://")+1),o.indexOf(n)<0&&(n="https:"),this.content.imageInfoArray[a].setImageUrl([n,t].join("")))}},{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateImageFormat",value:function(e){this.content.imageFormat=x_.IMAGE_FORMAT[e.toUpperCase()]||x_.IMAGE_FORMAT.UNKNOWN}},{key:"createImageDataASURLInWeb",value:function(e){void 0!==e&&e.files.length>0&&(this._imageMemoryURL=window.URL.createObjectURL(e.files[0]))}},{key:"createImageDataASURLInWXMiniApp",value:function(e){e&&e.url&&(this._imageMemoryURL=e.url)}},{key:"replaceImageInfo",value:function(e,t){this.content.imageInfoArray[t]instanceof this._ImageInfoModel||(this.content.imageInfoArray[t]=e)}},{key:"addImageInfo",value:function(e){this.content.imageInfoArray.length>=3||this.content.imageInfoArray.push(e)}},{key:"updateAccessSideImageInfoArray",value:function(){var e=this.content.imageInfoArray,t=e[0],n=t.width,o=void 0===n?0:n,r=t.height,a=void 0===r?0:r;0!==o&&0!==a&&(Au(e),Object.assign(e[2],Su({originWidth:o,originHeight:a,min:720})))}},{key:"sendable",value:function(){return 0!==this.content.imageInfoArray.length&&""!==this.content.imageInfoArray[0].imageUrl&&0!==this.content.imageInfoArray[0].size}}]),e}(),Y_=function(){function e(t){Gn(this,e),this.type=so.MSG_FACE,this.content=t||null}return Un(e,[{key:"sendable",value:function(){return null!==this.content}}]),e}(),z_=function(){function e(t){Gn(this,e),this.type=so.MSG_AUDIO,this._percent=0,this.content={downloadFlag:2,second:t.second,size:t.size,url:t.url,remoteAudioUrl:t.url||"",uuid:t.uuid}}return Un(e,[{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateAudioUrl",value:function(e){this.content.remoteAudioUrl=e}},{key:"sendable",value:function(){return""!==this.content.remoteAudioUrl}}]),e}();be({target:"Object",stat:!0,forced:!i,sham:!i},{defineProperty:O.f});var J_={from:!0,groupID:!0,groupName:!0,to:!0},X_=function(){function e(t){Gn(this,e),this.type=so.MSG_GRP_TIP,this.content={},this._initContent(t)}return Un(e,[{key:"_initContent",value:function(e){var t=this;Object.keys(e).forEach((function(n){switch(n){case"remarkInfo":break;case"groupProfile":t.content.groupProfile={},t._initGroupProfile(e[n]);break;case"operatorInfo":case"memberInfoList":break;case"msgMemberInfo":t.content.memberList=e[n],Object.defineProperty(t.content,"msgMemberInfo",{get:function(){return Bi.warn("!!! 禁言的群提示消息中的 payload.msgMemberInfo 属性即将废弃，请使用 payload.memberList 属性替代。 \n","msgMemberInfo 中的 shutupTime 属性对应更改为 memberList 中的 muteTime 属性，表示禁言时长。 \n","参考：群提示消息 https://web.sdk.qcloud.com/im/doc/zh-cn/Message.html#.GroupTipPayload"),t.content.memberList.map((function(e){return{userID:e.userID,shutupTime:e.muteTime}}))}});break;case"onlineMemberInfo":break;case"memberNum":t.content[n]=e[n],t.content.memberCount=e[n];break;default:t.content[n]=e[n]}})),this.content.userIDList||(this.content.userIDList=[this.content.operatorID])}},{key:"_initGroupProfile",value:function(e){for(var t=Object.keys(e),n=0;n<t.length;n++){var o=t[n];J_[o]&&(this.content.groupProfile[o]=e[o])}}}]),e}(),Q_={from:!0,groupID:!0,groupName:!0,to:!0},Z_=function(){function e(t){Gn(this,e),this.type=so.MSG_GRP_SYS_NOTICE,this.content={},this._initContent(t)}return Un(e,[{key:"_initContent",value:function(e){var t=this;Object.keys(e).forEach((function(n){switch(n){case"memberInfoList":break;case"remarkInfo":t.content.handleMessage=e[n];break;case"groupProfile":t.content.groupProfile={},t._initGroupProfile(e[n]);break;default:t.content[n]=e[n]}}))}},{key:"_initGroupProfile",value:function(e){for(var t=Object.keys(e),n=0;n<t.length;n++){var o=t[n];Q_[o]&&("groupName"===o?this.content.groupProfile.name=e[o]:this.content.groupProfile[o]=e[o])}}}]),e}(),em=Math.min,tm=[].lastIndexOf,nm=!!tm&&1/[1].lastIndexOf(1,-0)<0,om=ut("lastIndexOf"),rm=pt("indexOf",{ACCESSORS:!0,1:0}),am=!nm&&om&&rm?tm:function(e){if(nm)return tm.apply(this,arguments)||0;var t=m(this),n=de(t.length),o=n-1;for(arguments.length>1&&(o=em(o,ce(arguments[1]))),o<0&&(o=n+o);o>=0;o--)if(o in t&&t[o]===e)return o||0;return-1};be({target:"Array",proto:!0,forced:am!==[].lastIndexOf},{lastIndexOf:am});var sm=function(){function e(t){Gn(this,e),this.type=so.MSG_FILE,this._percent=0;var n=this._getFileInfo(t);this.content={downloadFlag:2,fileUrl:t.url||"",uuid:t.uuid,fileName:n.name||"",fileSize:n.size||0}}return Un(e,[{key:"_getFileInfo",value:function(e){if(!Ji(e.fileName)&&!Ji(e.fileSize))return{size:e.fileSize,name:e.fileName};var t=e.file.files[0];if(si){if(t.path&&-1!==t.path.indexOf(".")){var n=t.path.slice(t.path.lastIndexOf(".")+1).toLowerCase();t.type=n,t.name||(t.name="".concat(iu(999999),".").concat(n))}t.name||(t.type="",t.name=t.path.slice(t.path.lastIndexOf("/")+1).toLowerCase()),t.suffix&&(t.type=t.suffix),t.url||(t.url=t.path)}return{size:t.size,name:t.name}}},{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateFileUrl",value:function(e){this.content.fileUrl=e}},{key:"sendable",value:function(){return""!==this.content.fileUrl&&""!==this.content.fileName&&0!==this.content.fileSize}}]),e}(),im=function(){function e(t){Gn(this,e),this.type=so.MSG_CUSTOM,this.content={data:t.data||"",description:t.description||"",extension:t.extension||""}}return Un(e,[{key:"setData",value:function(e){return this.content.data=e,this}},{key:"setDescription",value:function(e){return this.content.description=e,this}},{key:"setExtension",value:function(e){return this.content.extension=e,this}},{key:"sendable",value:function(){return 0!==this.content.data.length||0!==this.content.description.length||0!==this.content.extension.length}}]),e}(),um=function(){function e(t){Gn(this,e),this.type=so.MSG_VIDEO,this._percent=0,this.content={remoteVideoUrl:t.remoteVideoUrl||t.videoUrl||"",videoFormat:t.videoFormat,videoSecond:parseInt(t.videoSecond,10),videoSize:t.videoSize,videoUrl:t.videoUrl,videoDownloadFlag:2,videoUUID:t.videoUUID,thumbUUID:t.thumbUUID,thumbFormat:t.thumbFormat,thumbWidth:t.thumbWidth,thumbHeight:t.thumbHeight,thumbSize:t.thumbSize,thumbDownloadFlag:2,thumbUrl:t.thumbUrl}}return Un(e,[{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateVideoUrl",value:function(e){e&&(this.content.remoteVideoUrl=e)}},{key:"sendable",value:function(){return""!==this.content.remoteVideoUrl}}]),e}(),cm=function(){function e(t){Gn(this,e),this.type=so.MSG_LOCATION;var n=t.description,o=t.longitude,r=t.latitude;this.content={description:n,longitude:o,latitude:r}}return Un(e,[{key:"sendable",value:function(){return!0}}]),e}(),lm=function(){function e(t){if(Gn(this,e),this.from=t.from,this.messageSender=t.from,this.time=t.time,this.messageSequence=t.sequence,this.clientSequence=t.clientSequence||t.sequence,this.messageRandom=t.random,this.cloudCustomData=t.cloudCustomData||"",t.ID)this.nick=t.nick||"",this.avatar=t.avatar||"",this.messageBody=[{type:t.type,payload:t.payload}],t.conversationType.startsWith(so.CONV_C2C)?this.receiverUserID=t.to:t.conversationType.startsWith(so.CONV_GROUP)&&(this.receiverGroupID=t.to),this.messageReceiver=t.to;else{this.nick=t.nick||"",this.avatar=t.avatar||"",this.messageBody=[];var n=t.elements[0].type,o=t.elements[0].content;this._patchRichMediaPayload(n,o),n===so.MSG_MERGER?this.messageBody.push({type:n,payload:new dm(o).content}):this.messageBody.push({type:n,payload:o}),t.groupID&&(this.receiverGroupID=t.groupID,this.messageReceiver=t.groupID),t.to&&(this.receiverUserID=t.to,this.messageReceiver=t.to)}}return Un(e,[{key:"_patchRichMediaPayload",value:function(e,t){e===so.MSG_IMAGE?t.imageInfoArray.forEach((function(e){!e.imageUrl&&e.url&&(e.imageUrl=e.url,e.sizeType=e.type,1===e.type?e.type=0:3===e.type&&(e.type=1))})):e===so.MSG_VIDEO?!t.remoteVideoUrl&&t.videoUrl&&(t.remoteVideoUrl=t.videoUrl):e===so.MSG_AUDIO?!t.remoteAudioUrl&&t.url&&(t.remoteAudioUrl=t.url):e===so.MSG_FILE&&!t.fileUrl&&t.url&&(t.fileUrl=t.url,t.url=void 0)}}]),e}(),dm=function(){function e(t){if(Gn(this,e),this.type=so.MSG_MERGER,this.content={downloadKey:"",pbDownloadKey:"",messageList:[],title:"",abstractList:[],compatibleText:"",version:0,layersOverLimit:!1},t.downloadKey){var n=t.downloadKey,o=t.pbDownloadKey,r=t.title,a=t.abstractList,s=t.compatibleText,i=t.version;this.content.downloadKey=n,this.content.pbDownloadKey=o,this.content.title=r,this.content.abstractList=a,this.content.compatibleText=s,this.content.version=i||0}else if(Lu(t.messageList))1===t.layersOverLimit&&(this.content.layersOverLimit=!0);else{var u=t.messageList,c=t.title,l=t.abstractList,d=t.compatibleText,p=t.version,g=[];u.forEach((function(e){if(!Lu(e)){var t=new lm(e);g.push(t)}})),this.content.messageList=g,this.content.title=c,this.content.abstractList=l,this.content.compatibleText=d,this.content.version=p||0}Bi.debug("MergerElement.content:",this.content)}return Un(e,[{key:"sendable",value:function(){return!Lu(this.content.messageList)||!Lu(this.content.downloadKey)}}]),e}(),pm={1:so.MSG_PRIORITY_HIGH,2:so.MSG_PRIORITY_NORMAL,3:so.MSG_PRIORITY_LOW,4:so.MSG_PRIORITY_LOWEST},gm=function(){function e(t){Gn(this,e),this.ID="",this.conversationID=t.conversationID||null,this.conversationType=t.conversationType||so.CONV_C2C,this.conversationSubType=t.conversationSubType,this.time=t.time||Math.ceil(Date.now()/1e3),this.sequence=t.sequence||0,this.clientSequence=t.clientSequence||t.sequence||0,this.random=t.random||0===t.random?t.random:iu(),this.priority=this._computePriority(t.priority),this.nick=t.nick||"",this.avatar=t.avatar||"",this.isPeerRead=!1,this.nameCard="",this._elements=[],this.isPlaceMessage=t.isPlaceMessage||0,this.isRevoked=2===t.isPlaceMessage||8===t.msgFlagBits,this.from=t.from||null,this.to=t.to||null,this.flow="",this.isSystemMessage=t.isSystemMessage||!1,this.protocol=t.protocol||"JSON",this.isResend=!1,this.isRead=!1,this.status=t.status||Ac.SUCCESS,this._onlineOnlyFlag=!1,this._groupAtInfoList=[],this._relayFlag=!1,this.atUserList=[],this.cloudCustomData=t.cloudCustomData||"",this.isDeleted=!1,this.isModified=!1,this._isExcludedFromUnreadCount=!(!t.messageControlInfo||1!==t.messageControlInfo.excludedFromUnreadCount),this._isExcludedFromLastMessage=!(!t.messageControlInfo||1!==t.messageControlInfo.excludedFromLastMessage),this.reInitialize(t.currentUser),this.extractGroupInfo(t.groupProfile||null),this.handleGroupAtInfo(t)}return Un(e,[{key:"getElements",value:function(){return this._elements}},{key:"extractGroupInfo",value:function(e){if(null!==e){Wi(e.nick)&&(this.nick=e.nick),Wi(e.avatar)&&(this.avatar=e.avatar);var t=e.messageFromAccountExtraInformation;Yi(t)&&Wi(t.nameCard)&&(this.nameCard=t.nameCard)}}},{key:"handleGroupAtInfo",value:function(e){var t=this;e.payload&&e.payload.atUserList&&e.payload.atUserList.forEach((function(e){e!==so.MSG_AT_ALL?(t._groupAtInfoList.push({groupAtAllFlag:0,groupAtUserID:e}),t.atUserList.push(e)):(t._groupAtInfoList.push({groupAtAllFlag:1}),t.atUserList.push(so.MSG_AT_ALL))})),zi(e.groupAtInfo)&&e.groupAtInfo.forEach((function(e){0===e.groupAtAllFlag?t.atUserList.push(e.groupAtUserID):1===e.groupAtAllFlag&&t.atUserList.push(so.MSG_AT_ALL)}))}},{key:"getGroupAtInfoList",value:function(){return this._groupAtInfoList}},{key:"_initProxy",value:function(){this._elements[0]&&(this.payload=this._elements[0].content,this.type=this._elements[0].type)}},{key:"reInitialize",value:function(e){e&&(this.status=this.from?Ac.SUCCESS:Ac.UNSEND,!this.from&&(this.from=e)),this._initFlow(e),this._initSequence(e),this._concatConversationID(e),this.generateMessageID(e)}},{key:"isSendable",value:function(){return 0!==this._elements.length&&("function"!=typeof this._elements[0].sendable?(Bi.warn("".concat(this._elements[0].type,' need "boolean : sendable()" method')),!1):this._elements[0].sendable())}},{key:"_initTo",value:function(e){this.conversationType===so.CONV_GROUP&&(this.to=e.groupID)}},{key:"_initSequence",value:function(e){0===this.clientSequence&&e&&(this.clientSequence=function(e){if(!e)return Bi.error("autoIncrementIndex(string: key) need key parameter"),!1;if(void 0===du[e]){var t=new Date,n="3".concat(t.getHours()).slice(-2),o="0".concat(t.getMinutes()).slice(-2),r="0".concat(t.getSeconds()).slice(-2);du[e]=parseInt([n,o,r,"0001"].join("")),n=null,o=null,r=null,Bi.log("autoIncrementIndex start index:".concat(du[e]))}return du[e]++}(e)),0===this.sequence&&this.conversationType===so.CONV_C2C&&(this.sequence=this.clientSequence)}},{key:"generateMessageID",value:function(e){var t=e===this.from?1:0,n=this.sequence>0?this.sequence:this.clientSequence;this.ID="".concat(this.conversationID,"-").concat(n,"-").concat(this.random,"-").concat(t)}},{key:"_initFlow",value:function(e){""!==e&&(e===this.from?(this.flow="out",this.isRead=!0):this.flow="in")}},{key:"_concatConversationID",value:function(e){var t=this.to,n="",o=this.conversationType;o!==so.CONV_SYSTEM?(n=o===so.CONV_C2C?e===this.from?t:this.from:this.to,this.conversationID="".concat(o).concat(n)):this.conversationID=so.CONV_SYSTEM}},{key:"isElement",value:function(e){return e instanceof wh||e instanceof $_||e instanceof Y_||e instanceof z_||e instanceof sm||e instanceof um||e instanceof X_||e instanceof Z_||e instanceof im||e instanceof cm||e instanceof dm}},{key:"setElement",value:function(e){var t=this;if(this.isElement(e))return this._elements=[e],void this._initProxy();var n=function(e){if(e.type&&e.content)switch(e.type){case so.MSG_TEXT:t.setTextElement(e.content);break;case so.MSG_IMAGE:t.setImageElement(e.content);break;case so.MSG_AUDIO:t.setAudioElement(e.content);break;case so.MSG_FILE:t.setFileElement(e.content);break;case so.MSG_VIDEO:t.setVideoElement(e.content);break;case so.MSG_CUSTOM:t.setCustomElement(e.content);break;case so.MSG_LOCATION:t.setLocationElement(e.content);break;case so.MSG_GRP_TIP:t.setGroupTipElement(e.content);break;case so.MSG_GRP_SYS_NOTICE:t.setGroupSystemNoticeElement(e.content);break;case so.MSG_FACE:t.setFaceElement(e.content);break;case so.MSG_MERGER:t.setMergerElement(e.content);break;default:Bi.warn(e.type,e.content,"no operation......")}};if(zi(e))for(var o=0;o<e.length;o++)n(e[o]);else n(e);this._initProxy()}},{key:"clearElement",value:function(){this._elements.length=0}},{key:"setTextElement",value:function(e){var t="string"==typeof e?e:e.text,n=new wh({text:t});this._elements.push(n)}},{key:"setImageElement",value:function(e){var t=new $_(e);this._elements.push(t)}},{key:"setAudioElement",value:function(e){var t=new z_(e);this._elements.push(t)}},{key:"setFileElement",value:function(e){var t=new sm(e);this._elements.push(t)}},{key:"setVideoElement",value:function(e){var t=new um(e);this._elements.push(t)}},{key:"setLocationElement",value:function(e){var t=new cm(e);this._elements.push(t)}},{key:"setCustomElement",value:function(e){var t=new im(e);this._elements.push(t)}},{key:"setGroupTipElement",value:function(e){var t={},n=e.operationType;Lu(e.memberInfoList)?e.operatorInfo&&(t=e.operatorInfo):n!==so.GRP_TIP_MBR_JOIN&&n!==so.GRP_TIP_MBR_KICKED_OUT&&n!==so.GRP_TIP_MBR_SET_ADMIN&&n!==so.GRP_TIP_MBR_CANCELED_ADMIN||(t=e.memberInfoList[0]);var o=t,r=o.nick,a=o.avatar;Wi(r)&&(this.nick=r),Wi(a)&&(this.avatar=a);var s=new X_(e);this._elements.push(s)}},{key:"setGroupSystemNoticeElement",value:function(e){var t=new Z_(e);this._elements.push(t)}},{key:"setFaceElement",value:function(e){var t=new Y_(e);this._elements.push(t)}},{key:"setMergerElement",value:function(e){var t=new dm(e);this._elements.push(t)}},{key:"setIsRead",value:function(e){this.isRead=e}},{key:"setRelayFlag",value:function(e){this._relayFlag=e}},{key:"getRelayFlag",value:function(){return this._relayFlag}},{key:"_computePriority",value:function(e){if(Ji(e))return so.MSG_PRIORITY_NORMAL;if(Wi(e)&&-1!==Object.values(pm).indexOf(e))return e;if(ji(e)){var t=""+e;if(-1!==Object.keys(pm).indexOf(t))return pm[t]}return so.MSG_PRIORITY_NORMAL}},{key:"setNickAndAvatar",value:function(e){var t=e.nick,n=e.avatar;Wi(t)&&(this.nick=t),Wi(n)&&(this.avatar=n)}},{key:"setNameCard",value:function(e){Wi(e)&&(this.nameCard=e)}},{key:"elements",get:function(){return Bi.warn("！！！Message 实例的 elements 属性即将废弃，请尽快修改。使用 type 和 payload 属性处理单条消息，兼容组合消息使用 _elements 属性！！！"),this._elements}}]),e}(),hm=function(e){return{code:0,data:e||{}}},fm="https://cloud.tencent.com/document/product/",_m="您可以在即时通信 IM 控制台的【开发辅助工具(https://console.cloud.tencent.com/im-detail/tool-usersig)】页面校验 UserSig。",mm="UserSig 非法，请使用官网提供的 API 重新生成 UserSig(".concat(fm,"269/32688)。"),vm="#.E6.B6.88.E6.81.AF.E5.85.83.E7.B4.A0-timmsgelement",Mm={70001:"UserSig 已过期，请重新生成。建议 UserSig 有效期设置不小于24小时。",70002:"UserSig 长度为0，请检查传入的 UserSig 是否正确。",70003:mm,70005:mm,70009:"UserSig 验证失败，可能因为生成 UserSig 时混用了其他 SDKAppID 的私钥或密钥导致，请使用对应 SDKAppID 下的私钥或密钥重新生成 UserSig(".concat(fm,"269/32688)。"),70013:"请求中的 UserID 与生成 UserSig 时使用的 UserID 不匹配。".concat(_m),70014:"请求中的 SDKAppID 与生成 UserSig 时使用的 SDKAppID 不匹配。".concat(_m),70016:"密钥不存在，UserSig 验证失败，请在即时通信 IM 控制台获取密钥(".concat(fm,"269/32578#.E8.8E.B7.E5.8F.96.E5.AF.86.E9.92.A5)。"),70020:"SDKAppID 未找到，请在即时通信 IM 控制台确认应用信息。",70050:"UserSig 验证次数过于频繁。请检查 UserSig 是否正确，并于1分钟后重新验证。".concat(_m),70051:"帐号被拉入黑名单。",70052:"UserSig 已经失效，请重新生成，再次尝试。",70107:"因安全原因被限制登录，请不要频繁登录。",70169:"请求的用户帐号不存在。",70114:"".concat("服务端内部超时，请稍后重试。"),70202:"".concat("服务端内部超时，请稍后重试。"),70206:"请求中批量数量不合法。",70402:"参数非法，请检查必填字段是否填充，或者字段的填充是否满足协议要求。",70403:"请求失败，需要 App 管理员权限。",70398:"帐号数超限。如需创建多于100个帐号，请将应用升级为专业版，具体操作指引请参见购买指引(".concat(fm,"269/32458)。"),70500:"".concat("服务端内部错误，请重试。"),71e3:"删除帐号失败。仅支持删除体验版帐号，您当前应用为专业版，暂不支持帐号删除。",20001:"请求包非法。",20002:"UserSig 或 A2 失效。",20003:"消息发送方或接收方 UserID 无效或不存在，请检查 UserID 是否已导入即时通信 IM。",20004:"网络异常，请重试。",20005:"".concat("服务端内部错误，请重试。"),20006:"触发发送".concat("单聊消息","之前回调，App 后台返回禁止下发该消息。"),20007:"发送".concat("单聊消息","，被对方拉黑，禁止发送。消息发送状态默认展示为失败，您可以登录控制台修改该场景下的消息发送状态展示结果，具体操作请参见消息保留设置(").concat(fm,"269/38656)。"),20009:"消息发送双方互相不是好友，禁止发送（配置".concat("单聊消息","校验好友关系才会出现）。"),20010:"发送".concat("单聊消息","，自己不是对方的好友（单向关系），禁止发送。"),20011:"发送".concat("单聊消息","，对方不是自己的好友（单向关系），禁止发送。"),20012:"发送方被禁言，该条消息被禁止发送。",20016:"消息撤回超过了时间限制（默认2分钟）。",20018:"删除漫游内部错误。",90001:"JSON 格式解析失败，请检查请求包是否符合 JSON 规范。",90002:"".concat("JSON 格式请求包体","中 MsgBody 不符合消息格式描述，或者 MsgBody 不是 Array 类型，请参考 TIMMsgElement 对象的定义(").concat(fm,"269/2720").concat(vm,")。"),90003:"".concat("JSON 格式请求包体","中缺少 To_Account 字段或者 To_Account 帐号不存在。"),90005:"".concat("JSON 格式请求包体","中缺少 MsgRandom 字段或者 MsgRandom 字段不是 Integer 类型。"),90006:"".concat("JSON 格式请求包体","中缺少 MsgTimeStamp 字段或者 MsgTimeStamp 字段不是 Integer 类型。"),90007:"".concat("JSON 格式请求包体","中 MsgBody 类型不是 Array 类型，请将其修改为 Array 类型。"),90008:"".concat("JSON 格式请求包体","中缺少 From_Account 字段或者 From_Account 帐号不存在。"),90009:"请求需要 App 管理员权限。",90010:"".concat("JSON 格式请求包体","不符合消息格式描述，请参考 TIMMsgElement 对象的定义(").concat(fm,"269/2720").concat(vm,")。"),90011:"批量发消息目标帐号超过500，请减少 To_Account 中目标帐号数量。",90012:"To_Account 没有注册或不存在，请确认 To_Account 是否导入即时通信 IM 或者是否拼写错误。",90026:"消息离线存储时间错误（最多不能超过7天）。",90031:"".concat("JSON 格式请求包体","中 SyncOtherMachine 字段不是 Integer 类型。"),90044:"".concat("JSON 格式请求包体","中 MsgLifeTime 字段不是 Integer 类型。"),90048:"请求的用户帐号不存在。",90054:"撤回请求中的 MsgKey 不合法。",90994:"".concat("服务端内部错误，请重试。"),90995:"".concat("服务端内部错误，请重试。"),91e3:"".concat("服务端内部错误，请重试。"),90992:"".concat("服务端内部错误，请重试。","如果所有请求都返回该错误码，且 App 配置了第三方回调，请检查 App 服务端是否正常向即时通信 IM 后台服务端返回回调结果。"),93e3:"JSON 数据包超长，消息包体请不要超过8k。",91101:"Web 端长轮询被踢（Web 端同时在线实例个数超出限制）。",10002:"".concat("服务端内部错误，请重试。"),10003:"请求中的接口名称错误，请核对接口名称并重试。",10004:"参数非法，请根据错误描述检查请求是否正确。",10005:"请求包体中携带的帐号数量过多。",10006:"操作频率限制，请尝试降低调用的频率。",10007:"操作权限不足，例如 Work ".concat("群组","中普通成员尝试执行踢人操作，但只有 App 管理员才有权限。"),10008:"请求非法，可能是请求中携带的签名信息验证不正确，请再次尝试。",10009:"该群不允许群主主动退出。",10010:"".concat("群组","不存在，或者曾经存在过，但是目前已经被解散。"),10011:"解析 JSON 包体失败，请检查包体的格式是否符合 JSON 格式。",10012:"发起操作的 UserID 非法，请检查发起操作的用户 UserID 是否填写正确。",10013:"被邀请加入的用户已经是群成员。",10014:"群已满员，无法将请求中的用户加入".concat("群组","，如果是批量加人，可以尝试减少加入用户的数量。"),10015:"找不到指定 ID 的".concat("群组","。"),10016:"App 后台通过第三方回调拒绝本次操作。",10017:"因被禁言而不能发送消息，请检查发送者是否被设置禁言。",10018:"应答包长度超过最大包长（1MB），请求的内容过多，请尝试减少单次请求的数据量。",10019:"请求的用户帐号不存在。",10021:"".concat("群组"," ID 已被使用，请选择其他的").concat("群组"," ID。"),10023:"发消息的频率超限，请延长两次发消息时间的间隔。",10024:"此邀请或者申请请求已经被处理。",10025:"".concat("群组"," ID 已被使用，并且操作者为群主，可以直接使用。"),10026:"该 SDKAppID 请求的命令字已被禁用。",10030:"请求撤回的消息不存在。",10031:"消息撤回超过了时间限制（默认2分钟）。",10032:"请求撤回的消息不支持撤回操作。",10033:"".concat("群组","类型不支持消息撤回操作。"),10034:"该消息类型不支持删除操作。",10035:"直播群和在线成员广播大群不支持删除消息。",10036:"直播群创建数量超过了限制，请参考价格说明(".concat(fm,"269/11673)购买预付费套餐“IM直播群”。"),10037:"单个用户可创建和加入的".concat("群组","数量超过了限制，请参考价格说明(").concat(fm,"269/11673)购买或升级预付费套餐“单人可创建与加入").concat("群组","数”。"),10038:"群成员数量超过限制，请参考价格说明(".concat(fm,"269/11673)购买或升级预付费套餐“扩展群人数上限”。"),10041:"该应用（SDKAppID）已配置不支持群消息撤回。",10050:"群属性 key 不存在",10056:"请在写入群属性前先使用 getGroupAttributes 接口更新本地群属性，避免冲突。",30001:"请求参数错误，请根据错误描述检查请求参数",30002:"SDKAppID 不匹配",30003:"请求的用户帐号不存在",30004:"请求需要 App 管理员权限",30005:"关系链字段中包含敏感词",30006:"".concat("服务端内部错误，请重试。"),30007:"".concat("网络超时，请稍后重试. "),30008:"并发写导致写冲突，建议使用批量方式",30009:"后台禁止该用户发起加好友请求",30010:"自己的好友数已达系统上限",30011:"分组已达系统上限",30012:"未决数已达系统上限",30014:"对方的好友数已达系统上限",30515:"请求添加好友时，对方在自己的黑名单中，不允许加好友",30516:"请求添加好友时，对方的加好友验证方式是不允许任何人添加自己为好友",30525:"请求添加好友时，自己在对方的黑名单中，不允许加好友",30539:"等待对方同意",30540:"添加好友请求被安全策略打击，请勿频繁发起添加好友请求",31704:"与请求删除的帐号之间不存在好友关系",31707:"删除好友请求被安全策略打击，请勿频繁发起删除好友请求"},ym=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this)).code=e.code,o.message=Mm[e.code]||e.message,o.data=e.data||{},o}return n}(Wn(Error)),Im=null,Tm=function(e){Im=e},Cm=function(e){return Promise.resolve(hm(e))},Sm=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e instanceof ym)return t&&null!==Im&&Im.emit(ao.ERROR,e),Promise.reject(e);if(e instanceof Error){var n=new ym({code:Ld.UNCAUGHT_ERROR,message:e.message});return t&&null!==Im&&Im.emit(ao.ERROR,n),Promise.reject(n)}if(Ji(e)||Ji(e.code)||Ji(e.message))Bi.error("IMPromise.reject 必须指定code(错误码)和message(错误信息)!!!");else{if(ji(e.code)&&Wi(e.message)){var o=new ym(e);return t&&null!==Im&&Im.emit(ao.ERROR,o),Promise.reject(o)}Bi.error("IMPromise.reject code(错误码)必须为数字，message(错误信息)必须为字符串!!!")}},Am=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="C2CModule",o}return Un(n,[{key:"onNewC2CMessage",value:function(e){var t=e.dataList,n=e.isInstantMessage,o=e.C2CRemainingUnreadList,r=e.C2CPairUnreadList;Bi.debug("".concat(this._className,".onNewC2CMessage count:").concat(t.length," isInstantMessage:").concat(n));var a=this._newC2CMessageStoredAndSummary({dataList:t,C2CRemainingUnreadList:o,C2CPairUnreadList:r,isInstantMessage:n}),s=a.conversationOptionsList,i=a.messageList,u=a.isUnreadC2CMessage;this.filterModifiedMessage(i),s.length>0&&this.getModule(Jc).onNewMessage({conversationOptionsList:s,isInstantMessage:n,isUnreadC2CMessage:u});var c=this.filterUnmodifiedMessage(i);n&&c.length>0&&this.emitOuterEvent(ao.MESSAGE_RECEIVED,c),i.length=0}},{key:"_newC2CMessageStoredAndSummary",value:function(e){for(var t=e.dataList,n=e.C2CRemainingUnreadList,o=e.C2CPairUnreadList,r=e.isInstantMessage,a=null,s=[],i=[],u={},c=this.getModule(tl),l=!1,d=0,p=t.length;d<p;d++){var g=t[d];g.currentUser=this.getMyUserID(),g.conversationType=so.CONV_C2C,g.isSystemMessage=!!g.isSystemMessage,(Ji(g.nick)||Ji(g.avatar))&&(l=!0,Bi.debug("".concat(this._className,"._newC2CMessageStoredAndSummary nick or avatar missing!"))),a=new gm(g),g.elements=c.parseElements(g.elements,g.from),a.setElement(g.elements),a.setNickAndAvatar({nick:g.nick,avatar:g.avatar});var h=a.conversationID;if(r){var f=!1,_=this.getModule(Jc);if(a.from!==this.getMyUserID()){var m=_.getLatestMessageSentByPeer(h);if(m){var v=m.nick,M=m.avatar;l?a.setNickAndAvatar({nick:v,avatar:M}):v===a.nick&&M===a.avatar||(f=!0)}}else{var y=_.getLatestMessageSentByMe(h);if(y){var I=y.nick,T=y.avatar;I===a.nick&&T===a.avatar||_.modifyMessageSentByMe({conversationID:h,latestNick:a.nick,latestAvatar:a.avatar})}}var C=1===t[d].isModified;if(_.isMessageSentByCurrentInstance(a)?a.isModified=C:C=!1,0===g.msgLifeTime)a._onlineOnlyFlag=!0,i.push(a);else{if(!_.pushIntoMessageList(i,a,C))continue;f&&(_.modifyMessageSentByPeer({conversationID:h,latestNick:a.nick,latestAvatar:a.avatar}),_.updateUserProfileSpecifiedKey({conversationID:h,nick:a.nick,avatar:a.avatar}))}this.getModule(dl).addMessageDelay({currentTime:Date.now(),time:a.time})}if(0!==g.msgLifeTime){if(!1===a._onlineOnlyFlag)if(Ji(u[h])){var S=0;"in"===a.flow&&(a._isExcludedFromUnreadCount||(S=1)),u[h]=s.push({conversationID:h,unreadCount:S,type:a.conversationType,subType:a.conversationSubType,lastMessage:a._isExcludedFromLastMessage?"":a})-1}else{var A=u[h];s[A].type=a.conversationType,s[A].subType=a.conversationSubType,s[A].lastMessage=a._isExcludedFromLastMessage?"":a,"in"===a.flow&&(a._isExcludedFromUnreadCount||s[A].unreadCount++)}}else a._onlineOnlyFlag=!0}var E=!1;if(zi(o))for(var k=function(e,t){if(o[e].unreadCount>0){E=!0;var n=s.find((function(t){return t.conversationID==="C2C".concat(o[e].from)}));n?n.unreadCount=o[e].unreadCount:s.push({conversationID:"C2C".concat(o[e].from),unreadCount:o[e].unreadCount,type:so.CONV_C2C})}},D=0,N=o.length;D<N;D++)k(D);if(zi(n))for(var O=function(e,t){s.find((function(t){return t.conversationID==="C2C".concat(n[e].from)}))||s.push({conversationID:"C2C".concat(n[e].from),type:so.CONV_C2C,lastMsgTime:n[e].lastMsgTime})},L=0,R=n.length;L<R;L++)O(L);return{conversationOptionsList:s,messageList:i,isUnreadC2CMessage:E}}},{key:"onC2CMessageRevoked",value:function(e){var t=this;Bi.debug("".concat(this._className,".onC2CMessageRevoked count:").concat(e.dataList.length));var n=this.getModule(Jc),o=[],r=null;e.dataList.forEach((function(e){if(e.c2cMessageRevokedNotify){var a=e.c2cMessageRevokedNotify.revokedInfos;Ji(a)||a.forEach((function(e){var a=t.getMyUserID()===e.from?"".concat(so.CONV_C2C).concat(e.to):"".concat(so.CONV_C2C).concat(e.from);(r=n.revoke(a,e.sequence,e.random))&&o.push(r)}))}})),0!==o.length&&(n.onMessageRevoked(o),this.emitOuterEvent(ao.MESSAGE_REVOKED,o))}},{key:"onC2CMessageReadReceipt",value:function(e){var t=this;e.dataList.forEach((function(e){if(!Lu(e.c2cMessageReadReceipt)){var n=e.c2cMessageReadReceipt.to;e.c2cMessageReadReceipt.uinPairReadArray.forEach((function(e){var o=e.peerReadTime;Bi.debug("".concat(t._className,"._onC2CMessageReadReceipt to:").concat(n," peerReadTime:").concat(o));var r="".concat(so.CONV_C2C).concat(n),a=t.getModule(Jc);a.recordPeerReadTime(r,o),a.updateMessageIsPeerReadProperty(r,o)}))}}))}},{key:"onC2CMessageReadNotice",value:function(e){var t=this;e.dataList.forEach((function(e){if(!Lu(e.c2cMessageReadNotice)){var n=t.getModule(Jc);e.c2cMessageReadNotice.uinPairReadArray.forEach((function(e){var o=e.from,r=e.peerReadTime;Bi.debug("".concat(t._className,".onC2CMessageReadNotice from:").concat(o," lastReadTime:").concat(r));var a="".concat(so.CONV_C2C).concat(o);n.updateIsReadAfterReadReport({conversationID:a,lastMessageTime:r}),n.updateUnreadCount(a)}))}}))}},{key:"sendMessage",value:function(e,t){var n=this._createC2CMessagePack(e,t);return this.request(n)}},{key:"_createC2CMessagePack",value:function(e,t){var n=null;t&&(t.offlinePushInfo&&(n=t.offlinePushInfo),!0===t.onlineUserOnly&&(n?n.disablePush=!0:n={disablePush:!0}));var o="";Wi(e.cloudCustomData)&&e.cloudCustomData.length>0&&(o=e.cloudCustomData);var r=[];if(Yi(t)&&Yi(t.messageControlInfo)){var a=t.messageControlInfo,s=a.excludedFromUnreadCount,i=a.excludedFromLastMessage;!0===s&&r.push("NoUnread"),!0===i&&r.push("NoLastMsg")}return{protocolName:yl,tjgID:this.generateTjgID(e),requestData:{fromAccount:this.getMyUserID(),toAccount:e.to,msgTimeStamp:Math.ceil(Date.now()/1e3),msgBody:e.getElements(),cloudCustomData:o,msgSeq:e.sequence,msgRandom:e.random,msgLifeTime:this.isOnlineMessage(e,t)?0:void 0,nick:e.nick,avatar:e.avatar,offlinePushInfo:n?{pushFlag:!0===n.disablePush?1:0,title:n.title||"",desc:n.description||"",ext:n.extension||"",apnsInfo:{badgeMode:!0===n.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:n.androidOPPOChannelID||""}}:void 0,messageControlInfo:r}}}},{key:"isOnlineMessage",value:function(e,t){return!(!t||!0!==t.onlineUserOnly)}},{key:"revokeMessage",value:function(e){return this.request({protocolName:kl,requestData:{msgInfo:{fromAccount:e.from,toAccount:e.to,msgSeq:e.sequence,msgRandom:e.random,msgTimeStamp:e.time}}})}},{key:"deleteMessage",value:function(e){var t=e.to,n=e.keyList;return Bi.log("".concat(this._className,".deleteMessage toAccount:").concat(t," count:").concat(n.length)),this.request({protocolName:bl,requestData:{fromAccount:this.getMyUserID(),to:t,keyList:n}})}},{key:"setMessageRead",value:function(e){var t=this,n=e.conversationID,o=e.lastMessageTime,r="".concat(this._className,".setMessageRead");Bi.log("".concat(r," conversationID:").concat(n," lastMessageTime:").concat(o)),ji(o)||Bi.warn("".concat(r," 请勿修改 Conversation.lastMessage.lastTime，否则可能会导致已读上报结果不准确"));var a=new og(Sg);return a.setMessage("conversationID:".concat(n," lastMessageTime:").concat(o)),this.request({protocolName:Dl,requestData:{C2CMsgReaded:{cookie:"",C2CMsgReadedItem:[{toAccount:n.replace("C2C",""),lastMessageTime:o,receipt:1}]}}}).then((function(){a.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(r," ok"));var e=t.getModule(Jc);return e.updateIsReadAfterReadReport({conversationID:n,lastMessageTime:o}),e.updateUnreadCount(n),hm()})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),Bi.log("".concat(r," failed. error:"),e),Sm(e)}))}},{key:"getRoamingMessage",value:function(e){var t=this,n="".concat(this._className,".getRoamingMessage"),o=e.peerAccount,r=e.conversationID,a=e.count,s=e.lastMessageTime,i=e.messageKey,u="peerAccount:".concat(o," count:").concat(a||15," lastMessageTime:").concat(s||0," messageKey:").concat(i);Bi.log("".concat(n," ").concat(u));var c=new og(yg);return this.request({protocolName:Ll,requestData:{peerAccount:o,count:a||15,lastMessageTime:s||0,messageKey:i}}).then((function(e){var o=e.data,a=o.complete,s=o.messageList,i=o.messageKey,l=o.lastMessageTime;Ji(s)?Bi.log("".concat(n," ok. complete:").concat(a," but messageList is undefined!")):Bi.log("".concat(n," ok. complete:").concat(a," count:").concat(s.length)),c.setNetworkType(t.getNetworkType()).setMessage("".concat(u," complete:").concat(a," length:").concat(s.length)).end();var d=t.getModule(Jc);1===a&&d.setCompleted(r);var p=d.storeRoamingMessage(s,r);d.modifyMessageList(r),d.updateIsRead(r),d.updateRoamingMessageKeyAndTime(r,i,l);var g=d.getPeerReadTime(r);if(Bi.log("".concat(n," update isPeerRead property. conversationID:").concat(r," peerReadTime:").concat(g)),g)d.updateMessageIsPeerReadProperty(r,g);else{var h=r.replace(so.CONV_C2C,"");t.getRemotePeerReadTime([h]).then((function(){d.updateMessageIsPeerReadProperty(r,d.getPeerReadTime(r))}))}return p})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];c.setMessage(u).setError(e,o,r).end()})),Bi.warn("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"getRemotePeerReadTime",value:function(e){var t=this,n="".concat(this._className,".getRemotePeerReadTime");if(Lu(e))return Bi.warn("".concat(n," userIDList is empty!")),Promise.resolve();var o=new og(kg);return Bi.log("".concat(n," userIDList:").concat(e)),this.request({protocolName:Rl,requestData:{userIDList:e}}).then((function(r){var a=r.data.peerReadTimeList;Bi.log("".concat(n," ok. peerReadTimeList:").concat(a));for(var s="",i=t.getModule(Jc),u=0;u<e.length;u++)s+="".concat(e[u],"-").concat(a[u]," "),a[u]>0&&i.recordPeerReadTime("C2C".concat(e[u]),a[u]);o.setNetworkType(t.getNetworkType()).setMessage(s).end()})).catch((function(e){t.probeNetwork().then((function(t){var n=Xn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.warn("".concat(n," failed. error:"),e)}))}}]),n}(gl),Em=it.findIndex,km=!0,Dm=pt("findIndex");"findIndex"in[]&&Array(1).findIndex((function(){km=!1})),be({target:"Array",proto:!0,forced:km||!Dm},{findIndex:function(e){return Em(this,e,arguments.length>1?arguments[1]:void 0)}}),Co("findIndex");var Nm=[],Om=Nm.sort,Lm=a((function(){Nm.sort(void 0)})),Rm=a((function(){Nm.sort(null)})),bm=ut("sort");be({target:"Array",proto:!0,forced:Lm||!Rm||!bm},{sort:function(e){return void 0===e?Om.call(Ge(this)):Om.call(Ge(this),ot(e))}});var wm=function(){function e(t){Gn(this,e),this.list=new Map,this._className="MessageListHandler",this._latestMessageSentByPeerMap=new Map,this._latestMessageSentByMeMap=new Map,this._groupLocalLastMessageSequenceMap=new Map}return Un(e,[{key:"getLocalOldestMessageByConversationID",value:function(e){if(!e)return null;if(!this.list.has(e))return null;var t=this.list.get(e).values();return t?t.next().value:null}},{key:"pushIn",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.conversationID,o=e.ID,r=!0;this.list.has(n)||this.list.set(n,new Map);var a=this.list.get(n).has(o);if(a){var s=this.list.get(n).get(o);if(!t||!0===s.isModified)return!1}return this.list.get(n).set(o,e),this._setLatestMessageSentByPeer(n,e),this._setLatestMessageSentByMe(n,e),this._setGroupLocalLastMessageSequence(n,e),r}},{key:"unshift",value:function(e){var t;if(zi(e)){if(e.length>0){t=e[0].conversationID;var n=e.length;this._unshiftMultipleMessages(e),this._setGroupLocalLastMessageSequence(t,e[n-1])}}else t=e.conversationID,this._unshiftSingleMessage(e),this._setGroupLocalLastMessageSequence(t,e);if(t&&t.startsWith(so.CONV_C2C)){var o=Array.from(this.list.get(t).values()),r=o.length;if(0===r)return;for(var a=r-1;a>=0;a--)if("out"===o[a].flow){this._setLatestMessageSentByMe(t,o[a]);break}for(var s=r-1;s>=0;s--)if("in"===o[s].flow){this._setLatestMessageSentByPeer(t,o[s]);break}}}},{key:"_unshiftSingleMessage",value:function(e){var t=e.conversationID,n=e.ID;if(!this.list.has(t))return this.list.set(t,new Map),void this.list.get(t).set(n,e);var o=Array.from(this.list.get(t));o.unshift([n,e]),this.list.set(t,new Map(o))}},{key:"_unshiftMultipleMessages",value:function(e){for(var t=e.length,n=[],o=e[0].conversationID,r=this.list.has(o)?Array.from(this.list.get(o)):[],a=0;a<t;a++)n.push([e[a].ID,e[a]]);this.list.set(o,new Map(n.concat(r)))}},{key:"remove",value:function(e){var t=e.conversationID,n=e.ID;this.list.has(t)&&this.list.get(t).delete(n)}},{key:"revoke",value:function(e,t,n){if(Bi.debug("revoke message",e,t,n),this.list.has(e)){var o,r=ro(this.list.get(e));try{for(r.s();!(o=r.n()).done;){var a=Xn(o.value,2)[1];if(a.sequence===t&&!a.isRevoked&&(Ji(n)||a.random===n))return a.isRevoked=!0,a}}catch(u){r.e(u)}finally{r.f()}}return null}},{key:"removeByConversationID",value:function(e){this.list.has(e)&&(this.list.delete(e),this._latestMessageSentByPeerMap.delete(e),this._latestMessageSentByMeMap.delete(e))}},{key:"updateMessageIsPeerReadProperty",value:function(e,t){var n=[];if(this.list.has(e)){var o,r=ro(this.list.get(e));try{for(r.s();!(o=r.n()).done;){var a=Xn(o.value,2)[1];a.time<=t&&!a.isPeerRead&&"out"===a.flow&&(a.isPeerRead=!0,n.push(a))}}catch(u){r.e(u)}finally{r.f()}Bi.log("".concat(this._className,".updateMessageIsPeerReadProperty conversationID:").concat(e," peerReadTime:").concat(t," count:").concat(n.length))}return n}},{key:"updateMessageIsModifiedProperty",value:function(e){var t=e.conversationID,n=e.ID;if(this.list.has(t)){var o=this.list.get(t).get(n);o&&(o.isModified=!0)}}},{key:"hasLocalMessageList",value:function(e){return this.list.has(e)}},{key:"getLocalMessageList",value:function(e){return this.hasLocalMessageList(e)?Qn(this.list.get(e).values()):[]}},{key:"hasLocalMessage",value:function(e,t){return!!this.hasLocalMessageList(e)&&this.list.get(e).has(t)}},{key:"getLocalMessage",value:function(e,t){return this.hasLocalMessage(e,t)?this.list.get(e).get(t):null}},{key:"getLocalLastMessage",value:function(e){var t=this.getLocalMessageList(e);return t[t.length-1]}},{key:"_setLatestMessageSentByPeer",value:function(e,t){e.startsWith(so.CONV_C2C)&&"in"===t.flow&&this._latestMessageSentByPeerMap.set(e,t)}},{key:"_setLatestMessageSentByMe",value:function(e,t){e.startsWith(so.CONV_C2C)&&"out"===t.flow&&this._latestMessageSentByMeMap.set(e,t)}},{key:"_setGroupLocalLastMessageSequence",value:function(e,t){e.startsWith(so.CONV_GROUP)&&this._groupLocalLastMessageSequenceMap.set(e,t.sequence)}},{key:"getLatestMessageSentByPeer",value:function(e){return this._latestMessageSentByPeerMap.get(e)}},{key:"getLatestMessageSentByMe",value:function(e){return this._latestMessageSentByMeMap.get(e)}},{key:"getGroupLocalLastMessageSequence",value:function(e){return this._groupLocalLastMessageSequenceMap.get(e)||0}},{key:"modifyMessageSentByPeer",value:function(e){var t=e.conversationID,n=e.latestNick,o=e.latestAvatar,r=this.list.get(t);if(!Lu(r)){var a=Array.from(r.values()),s=a.length;if(0!==s){for(var i=null,u=0,c=!1,l=s-1;l>=0;l--)"in"===a[l].flow&&((i=a[l]).nick!==n&&(i.setNickAndAvatar({nick:n}),c=!0),i.avatar!==o&&(i.setNickAndAvatar({avatar:o}),c=!0),c&&(u+=1));Bi.log("".concat(this._className,".modifyMessageSentByPeer conversationID:").concat(t," count:").concat(u))}}}},{key:"modifyMessageSentByMe",value:function(e){var t=e.conversationID,n=e.latestNick,o=e.latestAvatar,r=this.list.get(t);if(!Lu(r)){var a=Array.from(r.values()),s=a.length;if(0!==s){for(var i=null,u=0,c=!1,l=s-1;l>=0;l--)"out"===a[l].flow&&((i=a[l]).nick!==n&&(i.setNickAndAvatar({nick:n}),c=!0),i.avatar!==o&&(i.setNickAndAvatar({avatar:o}),c=!0),c&&(u+=1));Bi.log("".concat(this._className,".modifyMessageSentByMe conversationID:").concat(t," count:").concat(u))}}}},{key:"traversal",value:function(){if(0!==this.list.size&&-1===Bi.getLevel()){console.group("conversationID-messageCount");var e,t=ro(this.list);try{for(t.s();!(e=t.n()).done;){var n=Xn(e.value,2),o=n[0],r=n[1];console.log("".concat(o,"-").concat(r.size))}}catch(i){t.e(i)}finally{t.f()}console.groupEnd()}}},{key:"reset",value:function(){this.list.clear(),this._latestMessageSentByPeerMap.clear(),this._latestMessageSentByMeMap.clear(),this._groupLocalLastMessageSequenceMap.clear()}}]),e}(),Gm="_a2KeyAndTinyIDUpdated",Pm="_cloudConfigUpdated",Um="_profileUpdated";function Fm(e){this.mixin(e)}Fm.mixin=function(e){var t=e.prototype||e;t._isReady=!1,t.ready=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e)return this._isReady?void(t?e.call(this):setTimeout(e,1)):(this._readyQueue=this._readyQueue||[],void this._readyQueue.push(e))},t.triggerReady=function(){var e=this;this._isReady=!0,setTimeout((function(){var t=e._readyQueue;e._readyQueue=[],t&&t.length>0&&t.forEach((function(e){e.call(this)}),e)}),1)},t.resetReady=function(){this._isReady=!1,this._readyQueue=[]},t.isReady=function(){return this._isReady}};var qm=["jpg","jpeg","gif","png","bmp","image"],xm=["mp4"],Vm=1,Km=2,Bm=3,Hm=255,jm=function(){function e(t){var n=this;Gn(this,e),Lu(t)||(this.userID=t.userID||"",this.nick=t.nick||"",this.gender=t.gender||"",this.birthday=t.birthday||0,this.location=t.location||"",this.selfSignature=t.selfSignature||"",this.allowType=t.allowType||so.ALLOW_TYPE_ALLOW_ANY,this.language=t.language||0,this.avatar=t.avatar||"",this.messageSettings=t.messageSettings||0,this.adminForbidType=t.adminForbidType||so.FORBID_TYPE_NONE,this.level=t.level||0,this.role=t.role||0,this.lastUpdatedTime=0,this.profileCustomField=[],Lu(t.profileCustomField)||t.profileCustomField.forEach((function(e){n.profileCustomField.push({key:e.key,value:e.value})})))}return Un(e,[{key:"validate",value:function(e){var t=!0,n="";if(Lu(e))return{valid:!1,tips:"empty options"};if(e.profileCustomField)for(var o=e.profileCustomField.length,r=null,a=0;a<o;a++){if(r=e.profileCustomField[a],!Wi(r.key)||-1===r.key.indexOf("Tag_Profile_Custom"))return{valid:!1,tips:"自定义资料字段的前缀必须是 Tag_Profile_Custom"};if(!Wi(r.value))return{valid:!1,tips:"自定义资料字段的 value 必须是字符串"}}for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if("profileCustomField"===s)continue;if(Lu(e[s])&&!Wi(e[s])&&!ji(e[s])){n="key:"+s+", invalid value:"+e[s],t=!1;continue}switch(s){case"nick":Wi(e[s])||(n="nick should be a string",t=!1),su(e[s])>500&&(n="nick name limited: must less than or equal to ".concat(500," bytes, current size: ").concat(su(e[s])," bytes"),t=!1);break;case"gender":lu(K_,e.gender)||(n="key:gender, invalid value:"+e.gender,t=!1);break;case"birthday":ji(e.birthday)||(n="birthday should be a number",t=!1);break;case"location":Wi(e.location)||(n="location should be a string",t=!1);break;case"selfSignature":Wi(e.selfSignature)||(n="selfSignature should be a string",t=!1);break;case"allowType":lu(H_,e.allowType)||(n="key:allowType, invalid value:"+e.allowType,t=!1);break;case"language":ji(e.language)||(n="language should be a number",t=!1);break;case"avatar":Wi(e.avatar)||(n="avatar should be a string",t=!1);break;case"messageSettings":0!==e.messageSettings&&1!==e.messageSettings&&(n="messageSettings should be 0 or 1",t=!1);break;case"adminForbidType":lu(B_,e.adminForbidType)||(n="key:adminForbidType, invalid value:"+e.adminForbidType,t=!1);break;case"level":ji(e.level)||(n="level should be a number",t=!1);break;case"role":ji(e.role)||(n="role should be a number",t=!1);break;default:n="unknown key:"+s+"  "+e[s],t=!1}}return{valid:t,tips:n}}}]),e}(),Wm=function e(t){Gn(this,e),this.value=t,this.next=null},$m=function(){function e(t){Gn(this,e),this.MAX_LENGTH=t,this.pTail=null,this.pNodeToDel=null,this.map=new Map,Bi.debug("SinglyLinkedList init MAX_LENGTH:".concat(this.MAX_LENGTH))}return Un(e,[{key:"set",value:function(e){var t=new Wm(e);if(this.map.size<this.MAX_LENGTH)null===this.pTail?(this.pTail=t,this.pNodeToDel=t):(this.pTail.next=t,this.pTail=t),this.map.set(e,1);else{var n=this.pNodeToDel;this.pNodeToDel=this.pNodeToDel.next,this.map.delete(n.value),n.next=null,n=null,this.pTail.next=t,this.pTail=t,this.map.set(e,1)}}},{key:"has",value:function(e){return this.map.has(e)}},{key:"delete",value:function(e){this.has(e)&&this.map.delete(e)}},{key:"tail",value:function(){return this.pTail}},{key:"size",value:function(){return this.map.size}},{key:"data",value:function(){return Array.from(this.map.keys())}},{key:"reset",value:function(){for(var e;null!==this.pNodeToDel;)e=this.pNodeToDel,this.pNodeToDel=this.pNodeToDel.next,e.next=null,e=null;this.pTail=null,this.map.clear()}}]),e}(),Ym=["groupID","name","avatar","type","introduction","notification","ownerID","selfInfo","createTime","infoSequence","lastInfoTime","lastMessage","nextMessageSeq","memberNum","maxMemberNum","memberList","joinOption","groupCustomField","muteAllMembers"],zm=function(){function e(t){Gn(this,e),this.groupID="",this.name="",this.avatar="",this.type="",this.introduction="",this.notification="",this.ownerID="",this.createTime="",this.infoSequence="",this.lastInfoTime="",this.selfInfo={messageRemindType:"",joinTime:"",nameCard:"",role:"",userID:"",memberCustomField:void 0,readedSequence:0,excludedUnreadSequenceList:void 0},this.lastMessage={lastTime:"",lastSequence:"",fromAccount:"",messageForShow:""},this.nextMessageSeq="",this.memberNum="",this.memberCount="",this.maxMemberNum="",this.maxMemberCount="",this.joinOption="",this.groupCustomField=[],this.muteAllMembers=void 0,this._initGroup(t)}return Un(e,[{key:"_initGroup",value:function(e){for(var t in e)Ym.indexOf(t)<0||("selfInfo"!==t?("memberNum"===t&&(this.memberCount=e[t]),"maxMemberNum"===t&&(this.maxMemberCount=e[t]),this[t]=e[t]):this.updateSelfInfo(e[t]))}},{key:"updateGroup",value:function(e){var t=this,n=JSON.parse(JSON.stringify(e));n.lastMsgTime&&(this.lastMessage.lastTime=n.lastMsgTime),Ji(n.muteAllMembers)||("On"===n.muteAllMembers?n.muteAllMembers=!0:n.muteAllMembers=!1),n.groupCustomField&&fu(this.groupCustomField,n.groupCustomField),Ji(n.memberNum)||(this.memberCount=n.memberNum),Ji(n.maxMemberNum)||(this.maxMemberCount=n.maxMemberNum),ru(this,n,["members","errorCode","lastMsgTime","groupCustomField","memberNum","maxMemberNum"]),zi(n.members)&&n.members.length>0&&n.members.forEach((function(e){e.userID===t.selfInfo.userID&&ru(t.selfInfo,e,["sequence"])}))}},{key:"updateSelfInfo",value:function(e){var t=e.nameCard,n=e.joinTime,o=e.role,r=e.messageRemindType,a=e.readedSequence,s=e.excludedUnreadSequenceList;ru(this.selfInfo,{nameCard:t,joinTime:n,role:o,messageRemindType:r,readedSequence:a,excludedUnreadSequenceList:s},[],["",null,void 0,0,NaN])}},{key:"setSelfNameCard",value:function(e){this.selfInfo.nameCard=e}},{key:"memberNum",set:function(e){},get:function(){return Bi.warn("！！！v2.8.0起弃用memberNum，请使用 memberCount"),this.memberCount}},{key:"maxMemberNum",set:function(e){},get:function(){return Bi.warn("！！！v2.8.0起弃用maxMemberNum，请使用 maxMemberCount"),this.maxMemberCount}}]),e}(),Jm=function(e,t){if(Ji(t))return"";switch(e){case so.MSG_TEXT:return t.text;case so.MSG_IMAGE:return"[图片]";case so.MSG_LOCATION:return"[位置]";case so.MSG_AUDIO:return"[语音]";case so.MSG_VIDEO:return"[视频]";case so.MSG_FILE:return"[文件]";case so.MSG_CUSTOM:return"[自定义消息]";case so.MSG_GRP_TIP:return"[群提示消息]";case so.MSG_GRP_SYS_NOTICE:return"[群系统通知]";case so.MSG_FACE:return"[动画表情]";case so.MSG_MERGER:return"[聊天记录]";default:return""}},Xm=function(e){return Ji(e)?{lastTime:0,lastSequence:0,fromAccount:0,messageForShow:"",payload:null,type:"",isRevoked:!1,cloudCustomData:"",onlineOnlyFlag:!1,nick:"",nameCard:""}:e instanceof gm?{lastTime:e.time||0,lastSequence:e.sequence||0,fromAccount:e.from||"",messageForShow:Jm(e.type,e.payload),payload:e.payload||null,type:e.type||null,isRevoked:e.isRevoked||!1,cloudCustomData:e.cloudCustomData||"",onlineOnlyFlag:e._onlineOnlyFlag||!1,nick:e.nick||"",nameCard:e.nameCard||""}:xn(xn({},e),{},{messageForShow:Jm(e.type,e.payload)})},Qm=function(){function e(t){Gn(this,e),this.conversationID=t.conversationID||"",this.unreadCount=t.unreadCount||0,this.type=t.type||"",this.lastMessage=Xm(t.lastMessage),t.lastMsgTime&&(this.lastMessage.lastTime=t.lastMsgTime),this._isInfoCompleted=!1,this.peerReadTime=t.peerReadTime||0,this.groupAtInfoList=[],this.remark="",this.isPinned=t.isPinned||!1,this.messageRemindType="",this._initProfile(t)}return Un(e,[{key:"_initProfile",value:function(e){var t=this;Object.keys(e).forEach((function(n){switch(n){case"userProfile":t.userProfile=e.userProfile;break;case"groupProfile":t.groupProfile=e.groupProfile}})),Ji(this.userProfile)&&this.type===so.CONV_C2C?this.userProfile=new jm({userID:e.conversationID.replace("C2C","")}):Ji(this.groupProfile)&&this.type===so.CONV_GROUP&&(this.groupProfile=new zm({groupID:e.conversationID.replace("GROUP","")}))}},{key:"updateUnreadCount",value:function(e){var t=e.nextUnreadCount,n=e.isFromGetConversationList,o=e.isUnreadC2CMessage;Ji(t)||(mu(this.subType)?this.unreadCount=0:n&&this.type===so.CONV_GROUP||o&&this.type===so.CONV_C2C?this.unreadCount=t:this.unreadCount=this.unreadCount+t)}},{key:"updateLastMessage",value:function(e){this.lastMessage=Xm(e)}},{key:"updateGroupAtInfoList",value:function(e){var t,n=(Zn(t=e.groupAtType)||eo(t)||to(t)||oo()).slice(0);-1!==n.indexOf(so.CONV_AT_ME)&&-1!==n.indexOf(so.CONV_AT_ALL)&&(n=[so.CONV_AT_ALL_AT_ME]);var o={from:e.from,groupID:e.groupID,messageSequence:e.sequence,atTypeArray:n,__random:e.__random,__sequence:e.__sequence};this.groupAtInfoList.push(o),Bi.debug("Conversation.updateGroupAtInfoList conversationID:".concat(this.conversationID),this.groupAtInfoList)}},{key:"clearGroupAtInfoList",value:function(){this.groupAtInfoList.length=0}},{key:"reduceUnreadCount",value:function(){this.unreadCount>=1&&(this.unreadCount-=1)}},{key:"isLastMessageRevoked",value:function(e){var t=e.sequence,n=e.time;return this.type===so.CONV_C2C&&t===this.lastMessage.lastSequence&&n===this.lastMessage.lastTime||this.type===so.CONV_GROUP&&t===this.lastMessage.lastSequence}},{key:"setLastMessageRevoked",value:function(e){this.lastMessage.isRevoked=e}},{key:"toAccount",get:function(){return this.conversationID.startsWith(so.CONV_C2C)?this.conversationID.replace(so.CONV_C2C,""):this.conversationID.startsWith(so.CONV_GROUP)?this.conversationID.replace(so.CONV_GROUP,""):""}},{key:"subType",get:function(){return this.groupProfile?this.groupProfile.type:""}}]),e}(),Zm=function(){function e(t){Gn(this,e),this._conversationModule=t,this._className="MessageRemindHandler",this._updateSequence=0}return Un(e,[{key:"getC2CMessageRemindType",value:function(){var e=this,t="".concat(this._className,".getC2CMessageRemindType");return this._conversationModule.request({protocolName:Ol,updateSequence:this._updateSequence}).then((function(n){Bi.log("".concat(t," ok"));var o=n.data,r=o.updateSequence,a=o.muteFlagList;e._updateSequence=r,e._patchC2CMessageRemindType(a)})).catch((function(e){Bi.error("".concat(t," failed. error:"),e)}))}},{key:"_patchC2CMessageRemindType",value:function(e){var t=this,n=0,o="";zi(e)&&e.length>0&&e.forEach((function(e){var r=e.userID,a=e.muteFlag;0===a?o=so.MSG_REMIND_ACPT_AND_NOTE:1===a?o=so.MSG_REMIND_DISCARD:2===a&&(o=so.MSG_REMIND_ACPT_NOT_NOTE),!0===t._conversationModule.patchMessageRemindType({ID:r,isC2CConversation:!0,messageRemindType:o})&&(n+=1)})),Bi.log("".concat(this._className,"._patchC2CMessageRemindType count:").concat(n))}},{key:"set",value:function(e){return e.groupID?this._setGroupMessageRemindType(e):zi(e.userIDList)?this._setC2CMessageRemindType(e):void 0}},{key:"_setGroupMessageRemindType",value:function(e){var t=this,n="".concat(this._className,"._setGroupMessageRemindType"),o=e.groupID,r=e.messageRemindType,a="groupID:".concat(o," messageRemindType:").concat(r),s=new og(Wg);return s.setMessage(a),this._getModule(zc).modifyGroupMemberInfo({groupID:o,messageRemindType:r,userID:this._conversationModule.getMyUserID()}).then((function(){s.setNetworkType(t._conversationModule.getNetworkType()).end(),Bi.log("".concat(n," ok. ").concat(a));var e=t._getModule($c).getLocalGroupProfile(o);return e&&(e.selfInfo.messageRemindType=r),t._conversationModule.patchMessageRemindType({ID:o,isC2CConversation:!1,messageRemindType:r})&&t._emitConversationUpdate(),hm({group:e})})).catch((function(e){return t._conversationModule.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];s.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"_setC2CMessageRemindType",value:function(e){var t=this,n="".concat(this._className,"._setC2CMessageRemindType"),o=e.userIDList,r=e.messageRemindType,a=o.slice(0,30),s=0;r===so.MSG_REMIND_DISCARD?s=1:r===so.MSG_REMIND_ACPT_NOT_NOTE&&(s=2);var i="userIDList:".concat(a," messageRemindType:").concat(r),u=new og(Wg);return u.setMessage(i),this._conversationModule.request({protocolName:Nl,requestData:{userIDList:a,muteFlag:s}}).then((function(e){u.setNetworkType(t._conversationModule.getNetworkType()).end();var o=e.data,s=o.updateSequence,i=o.errorList;t._updateSequence=s;var c=[],l=[];zi(i)&&i.forEach((function(e){c.push(e.userID),l.push({userID:e.userID,code:e.errorCode})}));var d=a.filter((function(e){return-1===c.indexOf(e)}));Bi.log("".concat(n," ok. successUserIDList:").concat(d," failureUserIDList:").concat(JSON.stringify(l)));var p=0;return d.forEach((function(e){t._conversationModule.patchMessageRemindType({ID:e,isC2CConversation:!0,messageRemindType:r})&&(p+=1)})),p>=1&&t._emitConversationUpdate(),a.length=c.length=0,Cm({successUserIDList:d.map((function(e){return{userID:e}})),failureUserIDList:l})})).catch((function(e){return t._conversationModule.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];u.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"_getModule",value:function(e){return this._conversationModule.getModule(e)}},{key:"_emitConversationUpdate",value:function(){this._conversationModule.emitConversationUpdate(!0,!1)}},{key:"setUpdateSequence",value:function(e){this._updateSequence=e}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._updateSequence=0}}]),e}(),ev=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="ConversationModule",Fm.mixin(Yn(o)),o._messageListHandler=new wm,o._messageRemindHandler=new Zm(Yn(o)),o.singlyLinkedList=new $m(100),o._pagingStatus=Ec.NOT_START,o._pagingTimeStamp=0,o._pagingStartIndex=0,o._pagingPinnedTimeStamp=0,o._pagingPinnedStartIndex=0,o._conversationMap=new Map,o._tmpGroupList=[],o._tmpGroupAtTipsList=[],o._peerReadTimeMap=new Map,o._completedMap=new Map,o._roamingMessageKeyAndTimeMap=new Map,o._remoteGroupReadSequenceMap=new Map,o._initListeners(),o}return Un(n,[{key:"_initListeners",value:function(){var e=this.getInnerEmitterInstance();e.on(Gm,this._initLocalConversationList,this),e.on(Um,this._onProfileUpdated,this)}},{key:"onCheckTimer",value:function(e){e%60==0&&this._messageListHandler.traversal()}},{key:"_initLocalConversationList",value:function(){var e=this,t=new og(Pg);Bi.log("".concat(this._className,"._initLocalConversationList."));var n="",o=this._getStorageConversationList();if(o){for(var r=o.length,a=0;a<r;a++){var s=o[a];if(s){if(s.conversationID==="".concat(so.CONV_C2C,"@TLS#ERROR")||s.conversationID==="".concat(so.CONV_C2C,"@TLS#NOT_FOUND"))continue;if(s.groupProfile){var i=s.groupProfile.type;if(mu(i))continue}}this._conversationMap.set(o[a].conversationID,new Qm(o[a]))}this.emitConversationUpdate(!0,!1),n="count:".concat(r)}else n="count:0";t.setNetworkType(this.getNetworkType()).setMessage(n).end(),this.getModule(Wc)||this.triggerReady(),this.ready((function(){e._tmpGroupList.length>0&&(e.updateConversationGroupProfile(e._tmpGroupList),e._tmpGroupList.length=0)})),this._syncConversationList()}},{key:"onMessageSent",value:function(e){this._onSendOrReceiveMessage({conversationOptionsList:e.conversationOptionsList,isInstantMessage:!0})}},{key:"onNewMessage",value:function(e){this._onSendOrReceiveMessage(e)}},{key:"_onSendOrReceiveMessage",value:function(e){var t=this,n=e.conversationOptionsList,o=e.isInstantMessage,r=void 0===o||o,a=e.isUnreadC2CMessage,s=void 0!==a&&a;this._isReady?0!==n.length&&(this._getC2CPeerReadTime(n),this._updateLocalConversationList({conversationOptionsList:n,isInstantMessage:r,isUnreadC2CMessage:s,isFromGetConversations:!1}),this._setStorageConversationList(),this.emitConversationUpdate()):this.ready((function(){t._onSendOrReceiveMessage(e)}))}},{key:"updateConversationGroupProfile",value:function(e){var t=this;zi(e)&&0===e.length||(0!==this._conversationMap.size?(e.forEach((function(e){var n="GROUP".concat(e.groupID);if(t._conversationMap.has(n)){var o=t._conversationMap.get(n);o.groupProfile=e,o.lastMessage.lastSequence<e.nextMessageSeq&&(o.lastMessage.lastSequence=e.nextMessageSeq-1),o.subType||(o.subType=e.type)}})),this.emitConversationUpdate(!0,!1)):this._tmpGroupList=e)}},{key:"_updateConversationUserProfile",value:function(e){var t=this;e.data.forEach((function(e){var n="C2C".concat(e.userID);t._conversationMap.has(n)&&(t._conversationMap.get(n).userProfile=e)})),this.emitConversationUpdate(!0,!1)}},{key:"onMessageRevoked",value:function(e){var t=this;if(0!==e.length){var n=null,o=!1;e.forEach((function(e){(n=t._conversationMap.get(e.conversationID))&&n.isLastMessageRevoked(e)&&(o=!0,n.setLastMessageRevoked(!0))})),o&&this.emitConversationUpdate(!0,!1)}}},{key:"onMessageDeleted",value:function(e){if(0!==e.length){e.forEach((function(e){e.isDeleted=!0}));for(var t=e[0].conversationID,n=this._messageListHandler.getLocalMessageList(t),o={},r=n.length-1;r>=0;r--)if(!n[r].isDeleted){o=n[r];break}var a=this._conversationMap.get(t);if(a){var s=!1;a.lastMessage.lastSequence===o.sequence&&a.lastMessage.lastTime===o.time||(Lu(o)&&(o=void 0),a.updateLastMessage(o),s=!0,Bi.log("".concat(this._className,".onMessageDeleted. update conversationID:").concat(t," with lastMessage:"),a.lastMessage)),t.startsWith(so.CONV_C2C)&&this.updateUnreadCount(t),s&&this.emitConversationUpdate(!0,!1)}}}},{key:"onNewGroupAtTips",value:function(e){var t=this,n=e.dataList,o=null;n.forEach((function(e){e.groupAtTips?o=e.groupAtTips:e.elements&&(o=e.elements),o.__random=e.random,o.__sequence=e.clientSequence,t._tmpGroupAtTipsList.push(o)})),Bi.debug("".concat(this._className,".onNewGroupAtTips isReady:").concat(this._isReady),this._tmpGroupAtTipsList),this._isReady&&this._handleGroupAtTipsList()}},{key:"_handleGroupAtTipsList",value:function(){var e=this;if(0!==this._tmpGroupAtTipsList.length){var t=!1;this._tmpGroupAtTipsList.forEach((function(n){var o=n.groupID;if(n.from!==e.getMyUserID()){var r=e._conversationMap.get("".concat(so.CONV_GROUP).concat(o));r&&(r.updateGroupAtInfoList(n),t=!0)}})),t&&this.emitConversationUpdate(!0,!1),this._tmpGroupAtTipsList.length=0}}},{key:"_getC2CPeerReadTime",value:function(e){var t=this,n=[];if(e.forEach((function(e){t._conversationMap.has(e.conversationID)||e.type!==so.CONV_C2C||n.push(e.conversationID.replace(so.CONV_C2C,""))})),n.length>0){Bi.debug("".concat(this._className,"._getC2CPeerReadTime userIDList:").concat(n));var o=this.getModule(Wc);o&&o.getRemotePeerReadTime(n)}}},{key:"_getStorageConversationList",value:function(){return this.getModule(Qc).getItem("conversationMap")}},{key:"_setStorageConversationList",value:function(){var e=this.getLocalConversationList().slice(0,20).map((function(e){return{conversationID:e.conversationID,type:e.type,subType:e.subType,lastMessage:e.lastMessage,groupProfile:e.groupProfile,userProfile:e.userProfile}}));this.getModule(Qc).setItem("conversationMap",e)}},{key:"emitConversationUpdate",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=Qn(this._conversationMap.values());if(t){var o=this.getModule($c);o&&o.updateGroupLastMessage(n)}e&&this.emitOuterEvent(ao.CONVERSATION_LIST_UPDATED,n)}},{key:"getLocalConversationList",value:function(){return Qn(this._conversationMap.values())}},{key:"getLocalConversation",value:function(e){return this._conversationMap.get(e)}},{key:"_syncConversationList",value:function(){var e=this,t=new og(Ug);return this._pagingStatus===Ec.NOT_START&&this._conversationMap.clear(),this._pagingGetConversationList().then((function(n){return e._pagingStatus=Ec.RESOLVED,e._setStorageConversationList(),e._handleC2CPeerReadTime(),e._patchConversationProperties(),t.setMessage(e._conversationMap.size).setNetworkType(e.getNetworkType()).end(),n})).catch((function(n){return e._pagingStatus=Ec.REJECTED,t.setMessage(e._pagingTimeStamp),e.probeNetwork().then((function(e){var o=Xn(e,2),r=o[0],a=o[1];t.setError(n,r,a).end()})),Sm(n)}))}},{key:"_patchConversationProperties",value:function(){var e=this,t=Date.now(),n=this.checkAndPatchRemark(),o=this._messageRemindHandler.getC2CMessageRemindType(),r=this.getModule($c).getGroupList();Promise.all([n,o,r]).then((function(){var n=Date.now()-t;Bi.log("".concat(e._className,"._patchConversationProperties ok. cost ").concat(n," ms")),e.emitConversationUpdate(!0,!1)}))}},{key:"_pagingGetConversationList",value:function(){var e=this,t="".concat(this._className,"._pagingGetConversationList");return Bi.log("".concat(t," timeStamp:").concat(this._pagingTimeStamp," startIndex:").concat(this._pagingStartIndex)+" pinnedTimeStamp:".concat(this._pagingPinnedTimeStamp," pinnedStartIndex:").concat(this._pagingPinnedStartIndex)),this._pagingStatus=Ec.PENDING,this.request({protocolName:wl,requestData:{fromAccount:this.getMyUserID(),timeStamp:this._pagingTimeStamp,startIndex:this._pagingStartIndex,pinnedTimeStamp:this._pagingPinnedTimeStamp,pinnedStartIndex:this._pagingStartIndex,orderType:1}}).then((function(n){var o=n.data,r=o.completeFlag,a=o.conversations,s=void 0===a?[]:a,i=o.timeStamp,u=o.startIndex,c=o.pinnedTimeStamp,l=o.pinnedStartIndex;if(Bi.log("".concat(t," ok. completeFlag:").concat(r," count:").concat(s.length," isReady:").concat(e._isReady)),s.length>0){var d=e._getConversationOptions(s);e._updateLocalConversationList({conversationOptionsList:d,isFromGetConversations:!0}),e.isLoggedIn()&&e.emitConversationUpdate()}if(!e._isReady){if(!e.isLoggedIn())return Cm();e.triggerReady()}return e._pagingTimeStamp=i,e._pagingStartIndex=u,e._pagingPinnedTimeStamp=c,e._pagingPinnedStartIndex=l,1!==r?e._pagingGetConversationList():(e._handleGroupAtTipsList(),Cm())})).catch((function(n){throw e.isLoggedIn()&&(e._isReady||(Bi.warn("".concat(t," failed. error:"),n),e.triggerReady())),n}))}},{key:"_updateLocalConversationList",value:function(e){var t,n=e.isFromGetConversations,o=Date.now();t=this._getTmpConversationListMapping(e),this._conversationMap=new Map(this._sortConversationList([].concat(Qn(t.toBeUpdatedConversationList),Qn(this._conversationMap)))),n||this._updateUserOrGroupProfile(t.newConversationList),Bi.debug("".concat(this._className,"._updateLocalConversationList cost ").concat(Date.now()-o," ms"))}},{key:"_getTmpConversationListMapping",value:function(e){for(var t=e.conversationOptionsList,n=e.isFromGetConversations,o=e.isInstantMessage,r=e.isUnreadC2CMessage,a=void 0!==r&&r,s=[],i=[],u=this.getModule($c),c=this.getModule(Yc),l=0,d=t.length;l<d;l++){var p=new Qm(t[l]),g=p.conversationID;if(g!=="".concat(so.CONV_C2C,"@TLS#ERROR")&&g!=="".concat(so.CONV_C2C,"@TLS#NOT_FOUND"))if(this._conversationMap.has(g)){var h=this._conversationMap.get(g),f=["unreadCount","allowType","adminForbidType","payload","isPinned"];!1===o&&f.push("lastMessage");var _=t[l].lastMessage,m=!Ji(_);m||this._onLastMessageNotExist(t[l]),Ji(o)&&m&&null===h.lastMessage.payload&&(h.lastMessage.payload=_.payload),ru(h,p,f,[null,void 0,"",0,NaN]),h.updateUnreadCount({nextUnreadCount:p.unreadCount,isFromGetConversations:n,isUnreadC2CMessage:a}),o&&m&&(h.lastMessage.payload=_.payload,h.type===so.CONV_GROUP&&(h.lastMessage.nameCard=_.nameCard,h.lastMessage.nick=_.nick)),m&&h.lastMessage.cloudCustomData!==_.cloudCustomData&&(h.lastMessage.cloudCustomData=_.cloudCustomData||""),this._conversationMap.delete(g),s.push([g,h])}else{if(p.type===so.CONV_GROUP&&u){var v=p.groupProfile.groupID,M=u.getLocalGroupProfile(v);M&&(p.groupProfile=M,p.updateUnreadCount({nextUnreadCount:0}))}else if(p.type===so.CONV_C2C){var y=g.replace(so.CONV_C2C,"");c&&c.isMyFriend(y)&&(p.remark=c.getFriendRemark(y))}i.push(p),s.push([g,p])}}return{toBeUpdatedConversationList:s,newConversationList:i}}},{key:"_onLastMessageNotExist",value:function(e){new og(Oh).setMessage("".concat(JSON.stringify(e))).setNetworkType(this.getNetworkType()).end()}},{key:"_sortConversationList",value:function(e){var t=[],n=[];return e.forEach((function(e){!0===e[1].isPinned?t.push(e):n.push(e)})),t.sort((function(e,t){return t[1].lastMessage.lastTime-e[1].lastMessage.lastTime})).concat(n.sort((function(e,t){return t[1].lastMessage.lastTime-e[1].lastMessage.lastTime})))}},{key:"_sortConversationListAndEmitEvent",value:function(){this._conversationMap=new Map(this._sortConversationList(Qn(this._conversationMap))),this.emitConversationUpdate(!0,!1)}},{key:"_updateUserOrGroupProfile",value:function(e){var t=this;if(0!==e.length){var n=[],o=[],r=this.getModule(jc),a=this.getModule($c);e.forEach((function(e){if(e.type===so.CONV_C2C)n.push(e.toAccount);else if(e.type===so.CONV_GROUP){var t=e.toAccount;a.hasLocalGroup(t)?e.groupProfile=a.getLocalGroupProfile(t):o.push(t)}})),Bi.log("".concat(this._className,"._updateUserOrGroupProfile c2cUserIDList:").concat(n," groupIDList:").concat(o)),n.length>0&&r.getUserProfile({userIDList:n}).then((function(e){var n=e.data;zi(n)?n.forEach((function(e){t._conversationMap.get("C2C".concat(e.userID)).userProfile=e})):t._conversationMap.get("C2C".concat(n.userID)).userProfile=n})),o.length>0&&a.getGroupProfileAdvance({groupIDList:o,responseFilter:{groupBaseInfoFilter:["Type","Name","FaceUrl"]}}).then((function(e){e.data.successGroupList.forEach((function(e){var n="GROUP".concat(e.groupID);if(t._conversationMap.has(n)){var o=t._conversationMap.get(n);ru(o.groupProfile,e,[],[null,void 0,"",0,NaN]),!o.subType&&e.type&&(o.subType=e.type)}}))}))}}},{key:"_getConversationOptions",value:function(e){var t=[],n=e.filter((function(e){var t=e.lastMsg;return Yi(t)})).filter((function(e){var t=e.type,n=e.userID;return 1===t&&"@TLS#NOT_FOUND"!==n&&"@TLS#ERROR"!==n||2===t})).map((function(e){if(1===e.type){var n={userID:e.userID,nick:e.peerNick,avatar:e.peerAvatar};return t.push(n),{conversationID:"C2C".concat(e.userID),type:"C2C",lastMessage:{lastTime:e.time,lastSequence:e.sequence,fromAccount:e.lastC2CMsgFromAccount,messageForShow:e.messageShow,type:e.lastMsg.elements[0]?e.lastMsg.elements[0].type:null,payload:e.lastMsg.elements[0]?e.lastMsg.elements[0].content:null,cloudCustomData:e.cloudCustomData||"",isRevoked:8===e.lastMessageFlag,onlineOnlyFlag:!1,nick:"",nameCard:""},userProfile:new jm(n),peerReadTime:e.c2cPeerReadTime,isPinned:1===e.isPinned,messageRemindType:""}}return{conversationID:"GROUP".concat(e.groupID),type:"GROUP",lastMessage:{lastTime:e.time,lastSequence:e.messageReadSeq+e.unreadCount,fromAccount:e.msgGroupFromAccount,messageForShow:e.messageShow,type:e.lastMsg.elements[0]?e.lastMsg.elements[0].type:null,payload:e.lastMsg.elements[0]?e.lastMsg.elements[0].content:null,cloudCustomData:e.cloudCustomData||"",isRevoked:2===e.lastMessageFlag,onlineOnlyFlag:!1,nick:e.senderNick||"",nameCard:e.senderNameCard||""},groupProfile:new zm({groupID:e.groupID,name:e.groupNick,avatar:e.groupImage}),unreadCount:e.unreadCount,peerReadTime:0,isPinned:1===e.isPinned,messageRemindType:""}}));return t.length>0&&this.getModule(jc).onConversationsProfileUpdated(t),n}},{key:"getLocalMessageList",value:function(e){return this._messageListHandler.getLocalMessageList(e)}},{key:"deleteLocalMessage",value:function(e){e instanceof gm&&this._messageListHandler.remove(e)}},{key:"onConversationDeleted",value:function(e){var t=this;Bi.log("".concat(this._className,".onConversationDeleted")),zi(e)&&e.forEach((function(e){var n=e.type,o=e.userID,r=e.groupID,a="";1===n?a="".concat(so.CONV_C2C).concat(o):2===n&&(a="".concat(so.CONV_GROUP).concat(r)),t.deleteLocalConversation(a)}))}},{key:"onConversationPinned",value:function(e){var t=this;if(zi(e)){var n=!1;e.forEach((function(e){var o,r=e.type,a=e.userID,s=e.groupID;1===r?o=t.getLocalConversation("".concat(so.CONV_C2C).concat(a)):2===r&&(o=t.getLocalConversation("".concat(so.CONV_GROUP).concat(s))),o&&(Bi.log("".concat(t._className,".onConversationPinned conversationID:").concat(o.conversationID," isPinned:").concat(o.isPinned)),o.isPinned||(o.isPinned=!0,n=!0))})),n&&this._sortConversationListAndEmitEvent()}}},{key:"onConversationUnpinned",value:function(e){var t=this;if(zi(e)){var n=!1;e.forEach((function(e){var o,r=e.type,a=e.userID,s=e.groupID;1===r?o=t.getLocalConversation("".concat(so.CONV_C2C).concat(a)):2===r&&(o=t.getLocalConversation("".concat(so.CONV_GROUP).concat(s))),o&&(Bi.log("".concat(t._className,".onConversationUnpinned conversationID:").concat(o.conversationID," isPinned:").concat(o.isPinned)),o.isPinned&&(o.isPinned=!1,n=!0))})),n&&this._sortConversationListAndEmitEvent()}}},{key:"getMessageList",value:function(e){var t=this,n=e.conversationID,o=e.nextReqMessageID,r=e.count,a="".concat(this._className,".getMessageList"),s=this.getLocalConversation(n),i="";if(s&&s.groupProfile&&(i=s.groupProfile.type),mu(i))return Bi.log("".concat(a," not available in avchatroom. conversationID:").concat(n)),Cm({messageList:[],nextReqMessageID:"",isCompleted:!0});(Ji(r)||r>15)&&(r=15);var u=this._computeLeftCount({conversationID:n,nextReqMessageID:o});return Bi.log("".concat(a," conversationID:").concat(n," leftCount:").concat(u," count:").concat(r," nextReqMessageID:").concat(o)),this._needGetHistory({conversationID:n,leftCount:u,count:r})?this.getHistoryMessages({conversationID:n,nextReqMessageID:o,count:20}).then((function(){return u=t._computeLeftCount({conversationID:n,nextReqMessageID:o}),hm(t._computeResult({conversationID:n,nextReqMessageID:o,count:r,leftCount:u}))})):(Bi.log("".concat(a,".getMessageList get message list from memory")),this.modifyMessageList(n),Cm(this._computeResult({conversationID:n,nextReqMessageID:o,count:r,leftCount:u})))}},{key:"_computeLeftCount",value:function(e){var t=e.conversationID,n=e.nextReqMessageID;return n?this._messageListHandler.getLocalMessageList(t).findIndex((function(e){return e.ID===n})):this._getMessageListSize(t)}},{key:"_getMessageListSize",value:function(e){return this._messageListHandler.getLocalMessageList(e).length}},{key:"_needGetHistory",value:function(e){var t=e.conversationID,n=e.leftCount,o=e.count,r=this.getLocalConversation(t),a="";return r&&r.groupProfile&&(a=r.groupProfile.type),!yu(t)&&!mu(a)&&n<o&&!this._completedMap.has(t)}},{key:"_computeResult",value:function(e){var t=e.conversationID,n=e.nextReqMessageID,o=e.count,r=e.leftCount,a=this._computeMessageList({conversationID:t,nextReqMessageID:n,count:o}),s=this._computeIsCompleted({conversationID:t,leftCount:r,count:o}),i=this._computeNextReqMessageID({messageList:a,isCompleted:s,conversationID:t}),u="".concat(this._className,"._computeResult. conversationID:").concat(t);return Bi.log("".concat(u," leftCount:").concat(r," count:").concat(o," nextReqMessageID:").concat(i," isCompleted:").concat(s)),{messageList:a,nextReqMessageID:i,isCompleted:s}}},{key:"_computeMessageList",value:function(e){var t=e.conversationID,n=e.nextReqMessageID,o=e.count,r=this._messageListHandler.getLocalMessageList(t),a=this._computeIndexEnd({nextReqMessageID:n,messageList:r}),s=this._computeIndexStart({indexEnd:a,count:o});return r.slice(s,a)}},{key:"_computeNextReqMessageID",value:function(e){var t=e.messageList,n=e.isCompleted,o=e.conversationID;if(!n)return 0===t.length?"":t[0].ID;var r=this._messageListHandler.getLocalMessageList(o);return 0===r.length?"":r[0].ID}},{key:"_computeIndexEnd",value:function(e){var t=e.messageList,n=void 0===t?[]:t,o=e.nextReqMessageID;return o?n.findIndex((function(e){return e.ID===o})):n.length}},{key:"_computeIndexStart",value:function(e){var t=e.indexEnd,n=e.count;return t>n?t-n:0}},{key:"_computeIsCompleted",value:function(e){var t=e.conversationID;return!!(e.leftCount<=e.count&&this._completedMap.has(t))}},{key:"getHistoryMessages",value:function(e){var t=e.conversationID,n=e.nextReqMessageID;if(t===so.CONV_SYSTEM)return Cm();e.count?e.count>20&&(e.count=20):e.count=15;var o=this._messageListHandler.getLocalOldestMessageByConversationID(t);o||((o={}).time=0,o.sequence=0,0===t.indexOf(so.CONV_C2C)?(o.to=t.replace(so.CONV_C2C,""),o.conversationType=so.CONV_C2C):0===t.indexOf(so.CONV_GROUP)&&(o.to=t.replace(so.CONV_GROUP,""),o.conversationType=so.CONV_GROUP));var r="",a=null,s=this._roamingMessageKeyAndTimeMap.has(t);switch(o.conversationType){case so.CONV_C2C:return r=t.replace(so.CONV_C2C,""),(a=this.getModule(Wc))?a.getRoamingMessage({conversationID:e.conversationID,peerAccount:r,count:e.count,lastMessageTime:s?this._roamingMessageKeyAndTimeMap.get(t).lastMessageTime:0,messageKey:s?this._roamingMessageKeyAndTimeMap.get(t).messageKey:""}):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp});case so.CONV_GROUP:return(a=this.getModule($c))?a.getRoamingMessage({conversationID:e.conversationID,groupID:o.to,count:e.count,sequence:n&&!1===o._onlineOnlyFlag?o.sequence-1:o.sequence}):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp});default:return Cm()}}},{key:"patchConversationLastMessage",value:function(e){var t=this.getLocalConversation(e);if(t){var n=t.lastMessage,o=n.messageForShow,r=n.payload;if(Lu(o)||Lu(r)){var a=this._messageListHandler.getLocalMessageList(e);if(0===a.length)return;var s=a[a.length-1];Bi.log("".concat(this._className,".patchConversationLastMessage conversationID:").concat(e," payload:"),s.payload),t.updateLastMessage(s)}}}},{key:"storeRoamingMessage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=t.startsWith(so.CONV_C2C)?so.CONV_C2C:so.CONV_GROUP,o=null,r=[],a=0,s=e.length,i=null,u=n===so.CONV_GROUP,c=this.getModule(tl),l=function(){a=u?e.length-1:0,s=u?0:e.length},d=function(){u?--a:++a},p=function(){return u?a>=s:a<s};for(l();p();d())if(u&&1===e[a].sequence&&this.setCompleted(t),1!==e[a].isPlaceMessage)if((o=new gm(e[a])).to=e[a].to,o.isSystemMessage=!!e[a].isSystemMessage,o.conversationType=n,4===e[a].event?i={type:so.MSG_GRP_TIP,content:xn(xn({},e[a].elements),{},{groupProfile:e[a].groupProfile})}:(e[a].elements=c.parseElements(e[a].elements,e[a].from),i=e[a].elements),u||o.setNickAndAvatar({nick:e[a].nick,avatar:e[a].avatar}),Lu(i)){var g=new og(Eg);g.setMessage("from:".concat(o.from," to:").concat(o.to," sequence:").concat(o.sequence," event:").concat(e[a].event)),g.setNetworkType(this.getNetworkType()).setLevel("warning").end()}else o.setElement(i),o.reInitialize(this.getMyUserID()),r.push(o);return this._messageListHandler.unshift(r),l=d=p=null,r}},{key:"setMessageRead",value:function(e){var t=e.conversationID,n=(e.messageID,this.getLocalConversation(t));if(Bi.log("".concat(this._className,".setMessageRead conversationID:").concat(t," unreadCount:").concat(n?n.unreadCount:0)),!n)return Cm();if(n.type!==so.CONV_GROUP||Lu(n.groupAtInfoList)||this.deleteGroupAtTips(t),0===n.unreadCount)return Cm();var o=this._messageListHandler.getLocalLastMessage(t),r=n.lastMessage.lastTime;o&&r<o.time&&(r=o.time);var a=n.lastMessage.lastSequence;o&&a<o.sequence&&(a=o.sequence);var s=null;switch(n.type){case so.CONV_C2C:return(s=this.getModule(Wc))?s.setMessageRead({conversationID:t,lastMessageTime:r}):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp});case so.CONV_GROUP:return(s=this._moduleManager.getModule($c))?s.setMessageRead({conversationID:t,lastMessageSeq:a}):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp});case so.CONV_SYSTEM:return n.unreadCount=0,this.emitConversationUpdate(!0,!1),Cm();default:return Cm()}}},{key:"setAllMessageRead",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n="".concat(this._className,".setAllMessageRead");t.scope||(t.scope=so.READ_ALL_MSG),Bi.log("".concat(n," options:"),t);var o=this._createSetAllMessageReadPack(t);if(0===o.readAllC2CMessage&&0===o.groupMessageReadInfoList.length)return Cm();var r=new og(Fg);return this.request({protocolName:Zl,requestData:o}).then((function(n){var o=n.data,a=e._handleAllMessageRead(o);return r.setMessage("scope:".concat(t.scope," failureGroups:").concat(JSON.stringify(a))).setNetworkType(e.getNetworkType()).end(),Cm()})).catch((function(t){return e.probeNetwork().then((function(e){var n=Xn(e,2),o=n[0],a=n[1];r.setError(t,o,a).end()})),Bi.warn("".concat(n," failed. error:"),t),Sm({code:t&&t.code?t.code:Ld.MESSAGE_UNREAD_ALL_FAIL,message:t&&t.message?t.message:Yd})}))}},{key:"_getConversationLastMessageSequence",value:function(e){var t=this._messageListHandler.getLocalLastMessage(e.conversationID),n=e.lastMessage.lastSequence;return t&&n<t.sequence&&(n=t.sequence),n}},{key:"_getConversationLastMessageTime",value:function(e){var t=this._messageListHandler.getLocalLastMessage(e.conversationID),n=e.lastMessage.lastTime;return t&&n<t.time&&(n=t.time),n}},{key:"_createSetAllMessageReadPack",value:function(e){var t,n={readAllC2CMessage:0,groupMessageReadInfoList:[]},o=e.scope,r=ro(this._conversationMap);try{for(r.s();!(t=r.n()).done;){var a=Xn(t.value,2)[1];if(a.unreadCount>0)if(a.type===so.CONV_C2C&&0===n.readAllC2CMessage){if(o===so.READ_ALL_MSG)n.readAllC2CMessage=1;else if(o===so.READ_ALL_C2C_MSG){n.readAllC2CMessage=1;break}}else if(a.type===so.CONV_GROUP&&(o===so.READ_ALL_GROUP_MSG||o===so.READ_ALL_MSG)){var s=this._getConversationLastMessageSequence(a);n.groupMessageReadInfoList.push({groupID:a.groupProfile.groupID,messageSequence:s})}}}catch(c){r.e(c)}finally{r.f()}return n}},{key:"onPushedAllMessageRead",value:function(e){this._handleAllMessageRead(e)}},{key:"_handleAllMessageRead",value:function(e){var t=e.groupMessageReadInfoList,n=e.readAllC2CMessage,o=this._parseGroupReadInfo(t);return this._updateAllConversationUnreadCount({readAllC2CMessage:n})>=1&&this.emitConversationUpdate(!0,!1),o}},{key:"_parseGroupReadInfo",value:function(e){var t=[];if(e&&e.length)for(var n=0,o=e.length;n<o;n++){var r=e[n],a=r.groupID,s=r.sequence,i=r.retCode,u=r.lastMessageSeq;Ji(i)?this._remoteGroupReadSequenceMap.set(a,u):(this._remoteGroupReadSequenceMap.set(a,s),0!==i&&t.push("".concat(a,"-").concat(s,"-").concat(i)))}return t}},{key:"_updateAllConversationUnreadCount",value:function(e){var t,n=e.readAllC2CMessage,o=0,r=ro(this._conversationMap);try{for(r.s();!(t=r.n()).done;){var a=Xn(t.value,2),s=a[0],i=a[1];if(i.unreadCount>=1){if(1===n&&i.type===so.CONV_C2C){var u=this._getConversationLastMessageTime(i);this.updateIsReadAfterReadReport({conversationID:s,lastMessageTime:u})}else if(i.type===so.CONV_GROUP){var c=s.replace(so.CONV_GROUP,"");if(this._remoteGroupReadSequenceMap.has(c)){var l=this._remoteGroupReadSequenceMap.get(c),d=this._getConversationLastMessageSequence(i);this.updateIsReadAfterReadReport({conversationID:s,remoteReadSequence:l}),d>=l&&this._remoteGroupReadSequenceMap.delete(c)}}this.updateUnreadCount(s,!1)&&(o+=1)}}}catch(h){r.e(h)}finally{r.f()}return o}},{key:"isRemoteRead",value:function(e){var t=e.conversationID,n=e.sequence,o=t.replace(so.CONV_GROUP,""),r=!1;if(this._remoteGroupReadSequenceMap.has(o)){var a=this._remoteGroupReadSequenceMap.get(o);n<=a&&(r=!0,Bi.log("".concat(this._className,".isRemoteRead conversationID:").concat(t," messageSequence:").concat(n," remoteReadSequence:").concat(a))),n>=a+10&&this._remoteGroupReadSequenceMap.delete(o)}return r}},{key:"updateIsReadAfterReadReport",value:function(e){var t=e.conversationID,n=e.lastMessageSeq,o=e.lastMessageTime,r=this._messageListHandler.getLocalMessageList(t);if(0!==r.length)for(var a,s=r.length-1;s>=0;s--)if(a=r[s],!(o&&a.time>o||n&&a.sequence>n)){if("in"===a.flow&&a.isRead)break;a.setIsRead(!0)}}},{key:"updateUnreadCount",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=!1,o=this.getLocalConversation(e),r=this._messageListHandler.getLocalMessageList(e);if(o){var a=o.unreadCount,s=r.filter((function(e){return!e.isRead&&!e._onlineOnlyFlag&&!e.isDeleted})).length;return a!==s&&(o.unreadCount=s,n=!0,Bi.log("".concat(this._className,".updateUnreadCount from ").concat(a," to ").concat(s,", conversationID:").concat(e)),!0===t&&this.emitConversationUpdate(!0,!1)),n}}},{key:"recomputeGroupUnreadCount",value:function(e){var t=e.conversationID,n=e.count,o=this.getLocalConversation(t);if(o){var r=o.unreadCount,a=r-n;a<0&&(a=0),o.unreadCount=a,Bi.log("".concat(this._className,".recomputeGroupUnreadCount from ").concat(r," to ").concat(a,", conversationID:").concat(t))}}},{key:"updateIsRead",value:function(e){var t=this.getLocalConversation(e),n=this.getLocalMessageList(e);if(t&&0!==n.length&&!yu(t.type)){for(var o=[],r=0,a=n.length;r<a;r++)"in"!==n[r].flow?"out"!==n[r].flow||n[r].isRead||n[r].setIsRead(!0):o.push(n[r]);var s=0;if(t.type===so.CONV_C2C){var i=o.slice(-t.unreadCount).filter((function(e){return e.isRevoked})).length;s=o.length-t.unreadCount-i}else s=o.length-t.unreadCount;for(var u=0;u<s&&!o[u].isRead;u++)o[u].setIsRead(!0)}}},{key:"deleteGroupAtTips",value:function(e){var t="".concat(this._className,".deleteGroupAtTips");Bi.log("".concat(t));var n=this._conversationMap.get(e);if(!n)return Promise.resolve();var o=n.groupAtInfoList;if(0===o.length)return Promise.resolve();var r=this.getMyUserID();return this.request({protocolName:Fl,requestData:{messageListToDelete:o.map((function(e){return{from:e.from,to:r,messageSeq:e.__sequence,messageRandom:e.__random,groupID:e.groupID}}))}}).then((function(){return Bi.log("".concat(t," ok. count:").concat(o.length)),n.clearGroupAtInfoList(),Promise.resolve()})).catch((function(e){return Bi.error("".concat(t," failed. error:"),e),Sm(e)}))}},{key:"appendToMessageList",value:function(e){this._messageListHandler.pushIn(e)}},{key:"setMessageRandom",value:function(e){this.singlyLinkedList.set(e.random)}},{key:"deleteMessageRandom",value:function(e){this.singlyLinkedList.delete(e.random)}},{key:"pushIntoMessageList",value:function(e,t,n){return!(!this._messageListHandler.pushIn(t,n)||this._isMessageFromCurrentInstance(t)&&!n)&&(e.push(t),!0)}},{key:"_isMessageFromCurrentInstance",value:function(e){return this.singlyLinkedList.has(e.random)}},{key:"revoke",value:function(e,t,n){return this._messageListHandler.revoke(e,t,n)}},{key:"getPeerReadTime",value:function(e){return this._peerReadTimeMap.get(e)}},{key:"recordPeerReadTime",value:function(e,t){this._peerReadTimeMap.has(e)?this._peerReadTimeMap.get(e)<t&&this._peerReadTimeMap.set(e,t):this._peerReadTimeMap.set(e,t)}},{key:"updateMessageIsPeerReadProperty",value:function(e,t){if(e.startsWith(so.CONV_C2C)&&t>0){var n=this._messageListHandler.updateMessageIsPeerReadProperty(e,t);n.length>0&&this.emitOuterEvent(ao.MESSAGE_READ_BY_PEER,n)}}},{key:"updateMessageIsModifiedProperty",value:function(e){this._messageListHandler.updateMessageIsModifiedProperty(e)}},{key:"setCompleted",value:function(e){Bi.log("".concat(this._className,".setCompleted. conversationID:").concat(e)),this._completedMap.set(e,!0)}},{key:"updateRoamingMessageKeyAndTime",value:function(e,t,n){this._roamingMessageKeyAndTimeMap.set(e,{messageKey:t,lastMessageTime:n})}},{key:"getConversationList",value:function(e){var t=this,n="".concat(this._className,".getConversationList"),o="pagingStatus:".concat(this._pagingStatus,", local conversation count:").concat(this._conversationMap.size,", options:").concat(e);if(Bi.log("".concat(n,". ").concat(o)),this._pagingStatus===Ec.REJECTED){var r=new og(Rg);return r.setMessage(o),this._syncConversationList().then((function(){r.setNetworkType(t.getNetworkType()).end();var n=t._getConversationList(e);return hm({conversationList:n})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],a=n[1];r.setError(e,o,a).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))}if(0===this._conversationMap.size){var a=new og(Rg);return a.setMessage(o),this._syncConversationList().then((function(){a.setNetworkType(t.getNetworkType()).end();var n=t._getConversationList(e);return hm({conversationList:n})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))}var s=this._getConversationList(e);return Bi.log("".concat(n,". returned conversation count:").concat(s.length)),Cm({conversationList:s})}},{key:"_getConversationList",value:function(e){var t=this;if(Ji(e))return this.getLocalConversationList();if(zi(e)){var n=[];return e.forEach((function(e){if(t._conversationMap.has(e)){var o=t.getLocalConversation(e);n.push(o)}})),n}}},{key:"_handleC2CPeerReadTime",value:function(){var e,t=ro(this._conversationMap);try{for(t.s();!(e=t.n()).done;){var n=Xn(e.value,2),o=n[0],r=n[1];r.type===so.CONV_C2C&&(Bi.debug("".concat(this._className,"._handleC2CPeerReadTime conversationID:").concat(o," peerReadTime:").concat(r.peerReadTime)),this.recordPeerReadTime(o,r.peerReadTime))}}catch(i){t.e(i)}finally{t.f()}}},{key:"getConversationProfile",value:function(e){var t,n=this;if((t=this._conversationMap.has(e)?this._conversationMap.get(e):new Qm({conversationID:e,type:e.slice(0,3)===so.CONV_C2C?so.CONV_C2C:so.CONV_GROUP}))._isInfoCompleted||t.type===so.CONV_SYSTEM)return Cm({conversation:t});var o=new og(bg),r="".concat(this._className,".getConversationProfile");return Bi.log("".concat(r,". conversationID:").concat(e," remark:").concat(t.remark," lastMessage:"),t.lastMessage),this._updateUserOrGroupProfileCompletely(t).then((function(a){o.setNetworkType(n.getNetworkType()).setMessage("conversationID:".concat(e," unreadCount:").concat(a.data.conversation.unreadCount)).end();var s=n.getModule(Yc);if(s&&t.type===so.CONV_C2C){var i=e.replace(so.CONV_C2C,"");if(s.isMyFriend(i)){var u=s.getFriendRemark(i);t.remark!==u&&(t.remark=u,Bi.log("".concat(r,". conversationID:").concat(e," patch remark:").concat(t.remark)))}}return Bi.log("".concat(r," ok. conversationID:").concat(e)),a})).catch((function(t){return n.probeNetwork().then((function(n){var r=Xn(n,2),a=r[0],s=r[1];o.setError(t,a,s).setMessage("conversationID:".concat(e)).end()})),Bi.error("".concat(r," failed. error:"),t),Sm(t)}))}},{key:"_updateUserOrGroupProfileCompletely",value:function(e){var t=this;return e.type===so.CONV_C2C?this.getModule(jc).getUserProfile({userIDList:[e.toAccount]}).then((function(n){var o=n.data;return 0===o.length?Sm(new ym({code:Ld.USER_OR_GROUP_NOT_FOUND,message:pp})):(e.userProfile=o[0],e._isInfoCompleted=!0,t._unshiftConversation(e),Cm({conversation:e}))})):this.getModule($c).getGroupProfile({groupID:e.toAccount}).then((function(n){return e.groupProfile=n.data.group,e._isInfoCompleted=!0,t._unshiftConversation(e),Cm({conversation:e})}))}},{key:"_unshiftConversation",value:function(e){e instanceof Qm&&!this._conversationMap.has(e.conversationID)&&(this._conversationMap=new Map([[e.conversationID,e]].concat(Qn(this._conversationMap))),this._setStorageConversationList(),this.emitConversationUpdate(!0,!1))}},{key:"_onProfileUpdated",value:function(e){var t=this;e.data.forEach((function(e){var n=e.userID;if(n===t.getMyUserID())t._onMyProfileModified({latestNick:e.nick,latestAvatar:e.avatar});else{var o=t._conversationMap.get("".concat(so.CONV_C2C).concat(n));o&&(o.userProfile=e)}}))}},{key:"deleteConversation",value:function(e){var t=this,n={fromAccount:this.getMyUserID(),toAccount:void 0,type:void 0};if(!this._conversationMap.has(e)){var o=new ym({code:Ld.CONVERSATION_NOT_FOUND,message:dp});return Sm(o)}switch(this._conversationMap.get(e).type){case so.CONV_C2C:n.type=1,n.toAccount=e.replace(so.CONV_C2C,"");break;case so.CONV_GROUP:n.type=2,n.toGroupID=e.replace(so.CONV_GROUP,"");break;case so.CONV_SYSTEM:return this.getModule($c).deleteGroupSystemNotice({messageList:this._messageListHandler.getLocalMessageList(e)}),this.deleteLocalConversation(e),Cm({conversationID:e});default:var r=new ym({code:Ld.CONVERSATION_UN_RECORDED_TYPE,message:gp});return Sm(r)}var a=new og(wg);a.setMessage("conversationID:".concat(e));var s="".concat(this._className,".deleteConversation");return Bi.log("".concat(s,". conversationID:").concat(e)),this.setMessageRead({conversationID:e}).then((function(){return t.request({protocolName:Pl,requestData:n})})).then((function(){return a.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(s," ok")),t.deleteLocalConversation(e),Cm({conversationID:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),Bi.error("".concat(s," failed. error:"),e),Sm(e)}))}},{key:"pinConversation",value:function(e){var t=this,n=e.conversationID,o=e.isPinned;if(!this._conversationMap.has(n))return Sm({code:Ld.CONVERSATION_NOT_FOUND,message:dp});var r=this.getLocalConversation(n);if(r.isPinned===o)return Cm({conversationID:n});var a=new og(Gg);a.setMessage("conversationID:".concat(n," isPinned:").concat(o));var s="".concat(this._className,".pinConversation");Bi.log("".concat(s,". conversationID:").concat(n," isPinned:").concat(o));var i=null;return vu(n)?i={type:1,toAccount:n.replace(so.CONV_C2C,"")}:Mu(n)&&(i={type:2,groupID:n.replace(so.CONV_GROUP,"")}),this.request({protocolName:Ul,requestData:{fromAccount:this.getMyUserID(),operationType:!0===o?1:2,itemList:[i]}}).then((function(){return a.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(s," ok")),r.isPinned!==o&&(r.isPinned=o,t._sortConversationListAndEmitEvent()),hm({conversationID:n})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),Bi.error("".concat(s," failed. error:"),e),Sm(e)}))}},{key:"setMessageRemindType",value:function(e){return this._messageRemindHandler.set(e)}},{key:"patchMessageRemindType",value:function(e){var t=e.ID,n=e.isC2CConversation,o=e.messageRemindType,r=!1,a=this.getLocalConversation(n?"".concat(so.CONV_C2C).concat(t):"".concat(so.CONV_GROUP).concat(t));return a&&a.messageRemindType!==o&&(a.messageRemindType=o,r=!0),r}},{key:"onC2CMessageRemindTypeSynced",value:function(e){var t=this;Bi.debug("".concat(this._className,".onC2CMessageRemindTypeSynced options:"),e),e.dataList.forEach((function(e){if(!Lu(e.muteNotificationsSync)){var n,o=e.muteNotificationsSync,r=o.to,a=o.updateSequence,s=o.muteFlag;t._messageRemindHandler.setUpdateSequence(a),0===s?n=so.MSG_REMIND_ACPT_AND_NOTE:1===s?n=so.MSG_REMIND_DISCARD:2===s&&(n=so.MSG_REMIND_ACPT_NOT_NOTE);var i=0;t.patchMessageRemindType({ID:r,isC2CConversation:!0,messageRemindType:n})&&(i+=1),Bi.log("".concat(t._className,".onC2CMessageRemindTypeSynced updateCount:").concat(i)),i>=1&&t.emitConversationUpdate(!0,!1)}}))}},{key:"deleteLocalConversation",value:function(e){var t=this._conversationMap.has(e);Bi.log("".concat(this._className,".deleteLocalConversation conversationID:").concat(e," has:").concat(t)),t&&(this._conversationMap.delete(e),this._roamingMessageKeyAndTimeMap.delete(e),this._setStorageConversationList(),this._messageListHandler.removeByConversationID(e),this._completedMap.delete(e),this.emitConversationUpdate(!0,!1))}},{key:"isMessageSentByCurrentInstance",value:function(e){return!(!this._messageListHandler.hasLocalMessage(e.conversationID,e.ID)&&!this.singlyLinkedList.has(e.random))}},{key:"modifyMessageList",value:function(e){if(e.startsWith(so.CONV_C2C)&&this._conversationMap.has(e)){var t=this._conversationMap.get(e),n=Date.now();this._messageListHandler.modifyMessageSentByPeer({conversationID:e,latestNick:t.userProfile.nick,latestAvatar:t.userProfile.avatar});var o=this.getModule(jc).getNickAndAvatarByUserID(this.getMyUserID());this._messageListHandler.modifyMessageSentByMe({conversationID:e,latestNick:o.nick,latestAvatar:o.avatar}),Bi.log("".concat(this._className,".modifyMessageList conversationID:").concat(e," cost ").concat(Date.now()-n," ms"))}}},{key:"updateUserProfileSpecifiedKey",value:function(e){Bi.log("".concat(this._className,".updateUserProfileSpecifiedKey options:"),e);var t=e.conversationID,n=e.nick,o=e.avatar;if(this._conversationMap.has(t)){var r=this._conversationMap.get(t).userProfile;Wi(n)&&r.nick!==n&&(r.nick=n),Wi(o)&&r.avatar!==o&&(r.avatar=o),this.emitConversationUpdate(!0,!1)}}},{key:"_onMyProfileModified",value:function(e){var t=this,n=this.getLocalConversationList(),o=Date.now();n.forEach((function(n){t.modifyMessageSentByMe(xn({conversationID:n.conversationID},e))})),Bi.log("".concat(this._className,"._onMyProfileModified. modify all messages sent by me, cost ").concat(Date.now()-o," ms"))}},{key:"modifyMessageSentByMe",value:function(e){this._messageListHandler.modifyMessageSentByMe(e)}},{key:"getLatestMessageSentByMe",value:function(e){return this._messageListHandler.getLatestMessageSentByMe(e)}},{key:"modifyMessageSentByPeer",value:function(e){this._messageListHandler.modifyMessageSentByPeer(e)}},{key:"getLatestMessageSentByPeer",value:function(e){return this._messageListHandler.getLatestMessageSentByPeer(e)}},{key:"pushIntoNoticeResult",value:function(e,t){return!(!this._messageListHandler.pushIn(t)||this.singlyLinkedList.has(t.random))&&(e.push(t),!0)}},{key:"getGroupLocalLastMessageSequence",value:function(e){return this._messageListHandler.getGroupLocalLastMessageSequence(e)}},{key:"checkAndPatchRemark",value:function(){var e=Promise.resolve();if(0===this._conversationMap.size)return e;var t=this.getModule(Yc);if(!t)return e;var n=Qn(this._conversationMap.values()).filter((function(e){return e.type===so.CONV_C2C}));if(0===n.length)return e;var o=0;return n.forEach((function(e){var n=e.conversationID.replace(so.CONV_C2C,"");if(t.isMyFriend(n)){var r=t.getFriendRemark(n);e.remark!==r&&(e.remark=r,o+=1)}})),Bi.log("".concat(this._className,".checkAndPatchRemark. c2c conversation count:").concat(n.length,", patched count:").concat(o)),e}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._pagingStatus=Ec.NOT_START,this._messageListHandler.reset(),this._messageRemindHandler.reset(),this._roamingMessageKeyAndTimeMap.clear(),this.singlyLinkedList.reset(),this._peerReadTimeMap.clear(),this._completedMap.clear(),this._conversationMap.clear(),this._pagingTimeStamp=0,this._pagingStartIndex=0,this._pagingPinnedTimeStamp=0,this._pagingPinnedStartIndex=0,this._remoteGroupReadSequenceMap.clear(),this.resetReady()}}]),n}(gl),tv=function(){function e(t){Gn(this,e),this._groupModule=t,this._className="GroupTipsHandler",this._cachedGroupTipsMap=new Map,this._checkCountMap=new Map,this.MAX_CHECK_COUNT=4}return Un(e,[{key:"onCheckTimer",value:function(e){e%1==0&&this._cachedGroupTipsMap.size>0&&this._checkCachedGroupTips()}},{key:"_checkCachedGroupTips",value:function(){var e=this;this._cachedGroupTipsMap.forEach((function(t,n){var o=e._checkCountMap.get(n),r=e._groupModule.hasLocalGroup(n);Bi.log("".concat(e._className,"._checkCachedGroupTips groupID:").concat(n," hasLocalGroup:").concat(r," checkCount:").concat(o)),r?(e._notifyCachedGroupTips(n),e._checkCountMap.delete(n),e._groupModule.deleteUnjoinedAVChatRoom(n)):o>=e.MAX_CHECK_COUNT?(e._deleteCachedGroupTips(n),e._checkCountMap.delete(n)):(o++,e._checkCountMap.set(n,o))}))}},{key:"onNewGroupTips",value:function(e){Bi.debug("".concat(this._className,".onReceiveGroupTips count:").concat(e.dataList.length));var t=this.newGroupTipsStoredAndSummary(e),n=t.eventDataList,o=t.result,r=t.AVChatRoomMessageList;r.length>0&&this._groupModule.onAVChatRoomMessage(r),n.length>0&&(this._groupModule.getModule(Jc).onNewMessage({conversationOptionsList:n,isInstantMessage:!0}),this._groupModule.updateNextMessageSeq(n)),o.length>0&&(this._groupModule.emitOuterEvent(ao.MESSAGE_RECEIVED,o),this.handleMessageList(o))}},{key:"newGroupTipsStoredAndSummary",value:function(e){for(var t=e.event,n=e.dataList,o=null,r=[],a=[],s={},i=[],u=0,c=n.length;u<c;u++){var l=n[u],d=l.groupProfile.groupID,p=this._groupModule.hasLocalGroup(d);if(p||!this._groupModule.isUnjoinedAVChatRoom(d))if(p)if(this._groupModule.isMessageFromAVChatroom(d)){var g=hu(l);g.event=t,i.push(g)}else{l.currentUser=this._groupModule.getMyUserID(),l.conversationType=so.CONV_GROUP,(o=new gm(l)).setElement({type:so.MSG_GRP_TIP,content:xn(xn({},l.elements),{},{groupProfile:l.groupProfile})}),o.isSystemMessage=!1;var h=this._groupModule.getModule(Jc),f=o,_=f.conversationID,m=f.sequence;if(6===t)o._onlineOnlyFlag=!0,a.push(o);else if(!h.pushIntoNoticeResult(a,o))continue;if(6!==t||!h.getLocalConversation(_)){6!==t&&this._groupModule.getModule(dl).addMessageSequence({key:Jp,message:o});var v=h.isRemoteRead({conversationID:_,sequence:m});if(Ji(s[_]))s[_]=r.push({conversationID:_,unreadCount:"in"!==o.flow||o._onlineOnlyFlag||v?0:1,type:o.conversationType,subType:o.conversationSubType,lastMessage:o})-1;else{var M=s[_];r[M].type=o.conversationType,r[M].subType=o.conversationSubType,r[M].lastMessage=o,"in"!==o.flow||o._onlineOnlyFlag||v||r[M].unreadCount++}}}else this._cacheGroupTipsAndProbe({groupID:d,event:t,item:l})}return{eventDataList:r,result:a,AVChatRoomMessageList:i}}},{key:"handleMessageList",value:function(e){var t=this;e.forEach((function(e){switch(e.payload.operationType){case 1:t._onNewMemberComeIn(e);break;case 2:t._onMemberQuit(e);break;case 3:t._onMemberKickedOut(e);break;case 4:t._onMemberSetAdmin(e);break;case 5:t._onMemberCancelledAdmin(e);break;case 6:t._onGroupProfileModified(e);break;case 7:t._onMemberInfoModified(e);break;default:Bi.warn("".concat(t._className,".handleMessageList unknown operationType:").concat(e.payload.operationType))}}))}},{key:"_onNewMemberComeIn",value:function(e){var t=e.payload,n=t.memberNum,o=t.groupProfile.groupID,r=this._groupModule.getLocalGroupProfile(o);r&&ji(n)&&(r.memberNum=n)}},{key:"_onMemberQuit",value:function(e){var t=e.payload,n=t.memberNum,o=t.groupProfile.groupID,r=this._groupModule.getLocalGroupProfile(o);r&&ji(n)&&(r.memberNum=n),this._groupModule.deleteLocalGroupMembers(o,e.payload.userIDList)}},{key:"_onMemberKickedOut",value:function(e){var t=e.payload,n=t.memberNum,o=t.groupProfile.groupID,r=this._groupModule.getLocalGroupProfile(o);r&&ji(n)&&(r.memberNum=n),this._groupModule.deleteLocalGroupMembers(o,e.payload.userIDList)}},{key:"_onMemberSetAdmin",value:function(e){var t=e.payload.groupProfile.groupID,n=e.payload.userIDList,o=this._groupModule.getModule(zc);n.forEach((function(e){var n=o.getLocalGroupMemberInfo(t,e);n&&n.updateRole(so.GRP_MBR_ROLE_ADMIN)}))}},{key:"_onMemberCancelledAdmin",value:function(e){var t=e.payload.groupProfile.groupID,n=e.payload.userIDList,o=this._groupModule.getModule(zc);n.forEach((function(e){var n=o.getLocalGroupMemberInfo(t,e);n&&n.updateRole(so.GRP_MBR_ROLE_MEMBER)}))}},{key:"_onGroupProfileModified",value:function(e){var t=this,n=e.payload,o=n.newGroupProfile,r=n.groupProfile.groupID,a=this._groupModule.getLocalGroupProfile(r);Object.keys(o).forEach((function(e){switch(e){case"ownerID":t._ownerChanged(a,o);break;default:a[e]=o[e]}})),this._groupModule.emitGroupListUpdate(!0,!0)}},{key:"_ownerChanged",value:function(e,t){var n=e.groupID,o=this._groupModule.getLocalGroupProfile(n),r=this._groupModule.getMyUserID();if(r===t.ownerID){o.updateGroup({selfInfo:{role:so.GRP_MBR_ROLE_OWNER}});var a=this._groupModule.getModule(zc),s=a.getLocalGroupMemberInfo(n,r),i=this._groupModule.getLocalGroupProfile(n).ownerID,u=a.getLocalGroupMemberInfo(n,i);s&&s.updateRole(so.GRP_MBR_ROLE_OWNER),u&&u.updateRole(so.GRP_MBR_ROLE_MEMBER)}}},{key:"_onMemberInfoModified",value:function(e){var t=e.payload.groupProfile.groupID,n=this._groupModule.getModule(zc);e.payload.memberList.forEach((function(e){var o=n.getLocalGroupMemberInfo(t,e.userID);o&&e.muteTime&&o.updateMuteUntil(e.muteTime)}))}},{key:"_cacheGroupTips",value:function(e,t){this._cachedGroupTipsMap.has(e)||this._cachedGroupTipsMap.set(e,[]),this._cachedGroupTipsMap.get(e).push(t)}},{key:"_deleteCachedGroupTips",value:function(e){this._cachedGroupTipsMap.has(e)&&this._cachedGroupTipsMap.delete(e)}},{key:"_notifyCachedGroupTips",value:function(e){var t=this,n=this._cachedGroupTipsMap.get(e)||[];n.forEach((function(e){t.onNewGroupTips(e)})),this._deleteCachedGroupTips(e),Bi.log("".concat(this._className,"._notifyCachedGroupTips groupID:").concat(e," count:").concat(n.length))}},{key:"_cacheGroupTipsAndProbe",value:function(e){var t=this,n=e.groupID,o=e.event,r=e.item;this._cacheGroupTips(n,{event:o,dataList:[r]}),this._groupModule.getGroupSimplifiedInfo(n).then((function(e){e.type===so.GRP_AVCHATROOM?t._groupModule.hasLocalGroup(n)?t._notifyCachedGroupTips(n):t._groupModule.setUnjoinedAVChatRoom(n):(t._groupModule.updateGroupMap([e]),t._notifyCachedGroupTips(n))})),this._checkCountMap.has(n)||this._checkCountMap.set(n,0),Bi.log("".concat(this._className,"._cacheGroupTipsAndProbe groupID:").concat(n))}},{key:"reset",value:function(){this._cachedGroupTipsMap.clear(),this._checkCountMap.clear()}}]),e}(),nv=[].push,ov=Math.min,rv=!a((function(){return!RegExp(4294967295,"y")}));ys("split",2,(function(e,t,n){var o;return o="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(e,n){var o=String(_(this)),r=void 0===n?4294967295:n>>>0;if(0===r)return[];if(void 0===e)return[o];if(!ls(e))return t.call(o,e,r);for(var a,s,i,u=[],c=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),l=0,d=new RegExp(e.source,c+"g");(a=os.call(d,o))&&!((s=d.lastIndex)>l&&(u.push(o.slice(l,a.index)),a.length>1&&a.index<o.length&&nv.apply(u,a.slice(1)),i=a[0].length,l=s,u.length>=r));)d.lastIndex===a.index&&d.lastIndex++;return l===o.length?!i&&d.test("")||u.push(""):u.push(o.slice(l)),u.length>r?u.slice(0,r):u}:"0".split(void 0,0).length?function(e,n){return void 0===e&&0===n?[]:t.call(this,e,n)}:t,[function(t,n){var r=_(this),a=null==t?void 0:t[e];return void 0!==a?a.call(t,r,n):o.call(String(r),t,n)},function(e,r){var a=n(o,e,this,r,o!==t);if(a.done)return a.value;var s=D(e),i=String(this),u=wr(s,RegExp),c=s.unicode,l=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.unicode?"u":"")+(rv?"y":"g"),d=new u(rv?s:"^(?:"+s.source+")",l),p=void 0===r?4294967295:r>>>0;if(0===p)return[];if(0===i.length)return null===Cs(d,i)?[i]:[];for(var g=0,h=0,f=[];h<i.length;){d.lastIndex=rv?h:0;var _,m=Cs(d,rv?i:i.slice(h));if(null===m||(_=ov(de(d.lastIndex+(rv?0:h)),i.length))===g)h=Ts(i,h,c);else{if(f.push(i.slice(g,h)),f.length===p)return f;for(var v=1;v<=m.length-1;v++)if(f.push(m[v]),f.length===p)return f;h=g=_}}return f.push(i.slice(g)),f}]}),!rv);var av=function(){function e(t){Gn(this,e),this._groupModule=t,this._className="CommonGroupHandler",this.tempConversationList=null,this._cachedGroupMessageMap=new Map,this._checkCountMap=new Map,this.MAX_CHECK_COUNT=4,t.getInnerEmitterInstance().once(Gm,this._initGroupList,this)}return Un(e,[{key:"onCheckTimer",value:function(e){e%1==0&&this._cachedGroupMessageMap.size>0&&this._checkCachedGroupMessage()}},{key:"_checkCachedGroupMessage",value:function(){var e=this;this._cachedGroupMessageMap.forEach((function(t,n){var o=e._checkCountMap.get(n),r=e._groupModule.hasLocalGroup(n);Bi.log("".concat(e._className,"._checkCachedGroupMessage groupID:").concat(n," hasLocalGroup:").concat(r," checkCount:").concat(o)),r?(e._notifyCachedGroupMessage(n),e._checkCountMap.delete(n),e._groupModule.deleteUnjoinedAVChatRoom(n)):o>=e.MAX_CHECK_COUNT?(e._deleteCachedGroupMessage(n),e._checkCountMap.delete(n)):(o++,e._checkCountMap.set(n,o))}))}},{key:"_initGroupList",value:function(){var e=this;Bi.log("".concat(this._className,"._initGroupList"));var t=new og(Xg),n=this._groupModule.getStorageGroupList();if(zi(n)&&n.length>0){n.forEach((function(t){e._groupModule.initGroupMap(t)})),this._groupModule.emitGroupListUpdate(!0,!1);var o=this._groupModule.getLocalGroupList().length;t.setNetworkType(this._groupModule.getNetworkType()).setMessage("group count:".concat(o)).end()}else t.setNetworkType(this._groupModule.getNetworkType()).setMessage("group count:0").end();Bi.log("".concat(this._className,"._initGroupList ok"))}},{key:"handleUpdateGroupLastMessage",value:function(e){var t="".concat(this._className,".handleUpdateGroupLastMessage");if(Bi.debug("".concat(t," conversation count:").concat(e.length,", local group count:").concat(this._groupModule.getLocalGroupList().length)),0!==this._groupModule.getGroupMap().size){for(var n,o,r,a=!1,s=0,i=e.length;s<i;s++)(n=e[s]).type===so.CONV_GROUP&&(o=n.conversationID.split(/^GROUP/)[1],(r=this._groupModule.getLocalGroupProfile(o))&&(r.lastMessage=n.lastMessage,a=!0));a&&(this._groupModule.sortLocalGroupList(),this._groupModule.emitGroupListUpdate(!0,!1))}else this.tempConversationList=e}},{key:"onNewGroupMessage",value:function(e){Bi.debug("".concat(this._className,".onNewGroupMessage count:").concat(e.dataList.length));var t=this._newGroupMessageStoredAndSummary(e),n=t.conversationOptionsList,o=t.messageList,r=t.AVChatRoomMessageList;r.length>0&&this._groupModule.onAVChatRoomMessage(r),this._groupModule.filterModifiedMessage(o),n.length>0&&(this._groupModule.getModule(Jc).onNewMessage({conversationOptionsList:n,isInstantMessage:!0}),this._groupModule.updateNextMessageSeq(n));var a=this._groupModule.filterUnmodifiedMessage(o);a.length>0&&this._groupModule.emitOuterEvent(ao.MESSAGE_RECEIVED,a),o.length=0}},{key:"_newGroupMessageStoredAndSummary",value:function(e){var t=e.dataList,n=e.event,o=e.isInstantMessage,r=null,a=[],s=[],i=[],u={},c=so.CONV_GROUP,l=this._groupModule.getModule(tl),d=t.length;d>1&&t.sort((function(e,t){return e.sequence-t.sequence}));for(var p=0;p<d;p++){var g=t[p],h=g.groupProfile.groupID,f=this._groupModule.hasLocalGroup(h);if(f||!this._groupModule.isUnjoinedAVChatRoom(h))if(f)if(this._groupModule.isMessageFromAVChatroom(h)){var _=hu(g);_.event=n,i.push(_)}else{g.currentUser=this._groupModule.getMyUserID(),g.conversationType=c,g.isSystemMessage=!!g.isSystemMessage,r=new gm(g),g.elements=l.parseElements(g.elements,g.from),r.setElement(g.elements);var m=1===t[p].isModified,v=this._groupModule.getModule(Jc);v.isMessageSentByCurrentInstance(r)?r.isModified=m:m=!1;var M=this._groupModule.getModule(dl);if(o&&M.addMessageDelay({currentTime:Date.now(),time:r.time}),1===g.onlineOnlyFlag)r._onlineOnlyFlag=!0,s.push(r);else{if(!v.pushIntoMessageList(s,r,m))continue;M.addMessageSequence({key:Jp,message:r});var y=r,I=y.conversationID,T=y.sequence,C=v.isRemoteRead({conversationID:I,sequence:T});if(Ji(u[I])){var S=0;"in"===r.flow&&(r._isExcludedFromUnreadCount||C||(S=1)),u[I]=a.push({conversationID:I,unreadCount:S,type:r.conversationType,subType:r.conversationSubType,lastMessage:r._isExcludedFromLastMessage?"":r})-1}else{var A=u[I];a[A].type=r.conversationType,a[A].subType=r.conversationSubType,a[A].lastMessage=r._isExcludedFromLastMessage?"":r,"in"===r.flow&&(r._isExcludedFromUnreadCount||C||a[A].unreadCount++)}}}else this._cacheGroupMessageAndProbe({groupID:h,event:n,item:g})}return{conversationOptionsList:a,messageList:s,AVChatRoomMessageList:i}}},{key:"onGroupMessageRevoked",value:function(e){Bi.debug("".concat(this._className,".onGroupMessageRevoked nums:").concat(e.dataList.length));var t=this._groupModule.getModule(Jc),n=[],o=null;e.dataList.forEach((function(e){var r=e.elements.revokedInfos;Ji(r)||r.forEach((function(e){(o=t.revoke("GROUP".concat(e.groupID),e.sequence,e.random))&&n.push(o)}))})),0!==n.length&&(t.onMessageRevoked(n),this._groupModule.emitOuterEvent(ao.MESSAGE_REVOKED,n))}},{key:"_groupListTreeShaking",value:function(e){for(var t=new Map(Qn(this._groupModule.getGroupMap())),n=0,o=e.length;n<o;n++)t.delete(e[n].groupID);this._groupModule.hasJoinedAVChatRoom()&&this._groupModule.getJoinedAVChatRoom().forEach((function(e){t.delete(e)}));for(var r=Qn(t.keys()),a=0,s=r.length;a<s;a++)this._groupModule.deleteGroup(r[a])}},{key:"getGroupList",value:function(e){var t=this,n="".concat(this._className,".getGroupList"),o=new og(zg);Bi.log("".concat(n));var r={introduction:"Introduction",notification:"Notification",createTime:"CreateTime",ownerID:"Owner_Account",lastInfoTime:"LastInfoTime",memberNum:"MemberNum",maxMemberNum:"MaxMemberNum",joinOption:"ApplyJoinOption",muteAllMembers:"ShutUpAllMember"},a=["Type","Name","FaceUrl","NextMsgSeq","LastMsgTime"],s=[];return e&&e.groupProfileFilter&&e.groupProfileFilter.forEach((function(e){r[e]&&a.push(r[e])})),this._pagingGetGroupList({limit:50,offset:0,groupBaseInfoFilter:a,groupList:s}).then((function(){Bi.log("".concat(n," ok. count:").concat(s.length)),t._groupListTreeShaking(s),t._groupModule.updateGroupMap(s);var e=t._groupModule.getLocalGroupList().length;return o.setNetworkType(t._groupModule.getNetworkType()).setMessage("remote count:".concat(s.length,", after tree shaking, local count:").concat(e)).end(),t.tempConversationList&&(Bi.log("".concat(n," update last message with tempConversationList, count:").concat(t.tempConversationList.length)),t.handleUpdateGroupLastMessage({data:t.tempConversationList}),t.tempConversationList=null),t._groupModule.emitGroupListUpdate(),t._groupModule.patchGroupMessageRemindType(),t._groupModule.recomputeUnreadCount(),hm({groupList:t._groupModule.getLocalGroupList()})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Xn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"_pagingGetGroupList",value:function(e){var t=this,n="".concat(this._className,"._pagingGetGroupList"),o=e.limit,r=e.offset,a=e.groupBaseInfoFilter,s=e.groupList,i=new og(eh);return this._groupModule.request({protocolName:ql,requestData:{memberAccount:this._groupModule.getMyUserID(),limit:o,offset:r,responseFilter:{groupBaseInfoFilter:a,selfInfoFilter:["Role","JoinTime","MsgFlag","MsgSeq"]}}}).then((function(e){var u=e.data,c=u.groups,l=u.totalCount;s.push.apply(s,Qn(c));var d=r+o,p=!(l>d);return i.setNetworkType(t._groupModule.getNetworkType()).setMessage("offset:".concat(r," totalCount:").concat(l," isCompleted:").concat(p," currentCount:").concat(s.length)).end(),p?(Bi.log("".concat(n," ok. totalCount:").concat(l)),hm({groupList:s})):(r=d,t._pagingGetGroupList({limit:o,offset:r,groupBaseInfoFilter:a,groupList:s}))})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];i.setError(e,o,r).end()})),Sm(e)}))}},{key:"_cacheGroupMessage",value:function(e,t){this._cachedGroupMessageMap.has(e)||this._cachedGroupMessageMap.set(e,[]),this._cachedGroupMessageMap.get(e).push(t)}},{key:"_deleteCachedGroupMessage",value:function(e){this._cachedGroupMessageMap.has(e)&&this._cachedGroupMessageMap.delete(e)}},{key:"_notifyCachedGroupMessage",value:function(e){var t=this,n=this._cachedGroupMessageMap.get(e)||[];n.forEach((function(e){t.onNewGroupMessage(e)})),this._deleteCachedGroupMessage(e),Bi.log("".concat(this._className,"._notifyCachedGroupMessage groupID:").concat(e," count:").concat(n.length))}},{key:"_cacheGroupMessageAndProbe",value:function(e){var t=this,n=e.groupID,o=e.event,r=e.item;this._cacheGroupMessage(n,{event:o,dataList:[r]}),this._groupModule.getGroupSimplifiedInfo(n).then((function(e){e.type===so.GRP_AVCHATROOM?t._groupModule.hasLocalGroup(n)?t._notifyCachedGroupMessage(n):t._groupModule.setUnjoinedAVChatRoom(n):(t._groupModule.updateGroupMap([e]),t._notifyCachedGroupMessage(n))})),this._checkCountMap.has(n)||this._checkCountMap.set(n,0),Bi.log("".concat(this._className,"._cacheGroupMessageAndProbe groupID:").concat(n))}},{key:"reset",value:function(){this._cachedGroupMessageMap.clear(),this._checkCountMap.clear(),this._groupModule.getInnerEmitterInstance().once(Gm,this._initGroupList,this)}}]),e}(),sv={1:"init",2:"modify",3:"clear",4:"delete"},iv=function(){function e(t){Gn(this,e),this._groupModule=t,this._className="GroupAttributesHandler",this._groupAttributesMap=new Map,this.CACHE_EXPIRE_TIME=3e4,this._groupModule.getInnerEmitterInstance().on(Pm,this._onCloudConfigUpdated,this)}return Un(e,[{key:"_onCloudConfigUpdated",value:function(){var e=this._groupModule.getCloudConfig("grp_attr_cache_time");Ji(e)||(this.CACHE_EXPIRE_TIME=Number(e))}},{key:"updateLocalMainSequenceOnReconnected",value:function(){this._groupAttributesMap.forEach((function(e){e.localMainSequence=0}))}},{key:"onGroupAttributesUpdated",value:function(e){var t=this,n=e.groupID,o=e.groupAttributeOption,r=o.mainSequence,a=o.hasChangedAttributeInfo,s=o.groupAttributeList,i=void 0===s?[]:s,u=o.operationType;if(Bi.log("".concat(this._className,".onGroupAttributesUpdated. hasChangedAttributeInfo:").concat(a," operationType:").concat(u)),!Ji(u)){if(1===a){if(4===u){var c=[];i.forEach((function(e){c.push(e.key)})),i=Qn(c),c=null}return this._refreshCachedGroupAttributes({groupID:n,remoteMainSequence:r,groupAttributeList:i,operationType:sv[u]}),void this._emitGroupAttributesUpdated(n)}if(this._groupAttributesMap.has(n)){var l=this._groupAttributesMap.get(n).avChatRoomKey;this._getGroupAttributes({groupID:n,avChatRoomKey:l}).then((function(){t._emitGroupAttributesUpdated(n)}))}}}},{key:"initGroupAttributesCache",value:function(e){var t=e.groupID,n=e.avChatRoomKey;this._groupAttributesMap.set(t,{lastUpdateTime:0,localMainSequence:0,remoteMainSequence:0,attributes:new Map,avChatRoomKey:n}),Bi.log("".concat(this._className,".initGroupAttributesCache groupID:").concat(t," avChatRoomKey:").concat(n))}},{key:"initGroupAttributes",value:function(e){var t=this,n=e.groupID,o=e.groupAttributes,r=this._checkCachedGroupAttributes({groupID:n,funcName:"initGroupAttributes"});if(!0!==r)return Sm(r);var a=this._groupAttributesMap.get(n),s=a.remoteMainSequence,i=a.avChatRoomKey,u=new og(oh);return u.setMessage("groupID:".concat(n," mainSequence:").concat(s," groupAttributes:").concat(JSON.stringify(o))),this._groupModule.request({protocolName:id,requestData:{groupID:n,avChatRoomKey:i,mainSequence:s,groupAttributeList:this._transformGroupAttributes(o)}}).then((function(e){var r=e.data,a=r.mainSequence,s=Qn(r.groupAttributeList);return s.forEach((function(e){e.value=o[e.key]})),t._refreshCachedGroupAttributes({groupID:n,remoteMainSequence:a,groupAttributeList:s,operationType:"init"}),u.setNetworkType(t._groupModule.getNetworkType()).end(),Bi.log("".concat(t._className,".initGroupAttributes ok. groupID:").concat(n)),hm({groupAttributes:o})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];u.setError(e,o,r).end()})),Sm(e)}))}},{key:"setGroupAttributes",value:function(e){var t=this,n=e.groupID,o=e.groupAttributes,r=this._checkCachedGroupAttributes({groupID:n,funcName:"setGroupAttributes"});if(!0!==r)return Sm(r);var a=this._groupAttributesMap.get(n),s=a.remoteMainSequence,i=a.avChatRoomKey,u=a.attributes,c=this._transformGroupAttributes(o);c.forEach((function(e){var t=e.key;e.sequence=0,u.has(t)&&(e.sequence=u.get(t).sequence)}));var l=new og(rh);return l.setMessage("groupID:".concat(n," mainSequence:").concat(s," groupAttributes:").concat(JSON.stringify(o))),this._groupModule.request({protocolName:ud,requestData:{groupID:n,avChatRoomKey:i,mainSequence:s,groupAttributeList:c}}).then((function(e){var r=e.data,a=r.mainSequence,s=Qn(r.groupAttributeList);return s.forEach((function(e){e.value=o[e.key]})),t._refreshCachedGroupAttributes({groupID:n,remoteMainSequence:a,groupAttributeList:s,operationType:"modify"}),l.setNetworkType(t._groupModule.getNetworkType()).end(),Bi.log("".concat(t._className,".setGroupAttributes ok. groupID:").concat(n)),hm({groupAttributes:o})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];l.setError(e,o,r).end()})),Sm(e)}))}},{key:"deleteGroupAttributes",value:function(e){var t=this,n=e.groupID,o=e.keyList,r=void 0===o?[]:o,a=this._checkCachedGroupAttributes({groupID:n,funcName:"deleteGroupAttributes"});if(!0!==a)return Sm(a);var s=this._groupAttributesMap.get(n),i=s.remoteMainSequence,u=s.avChatRoomKey,c=s.attributes,l=Qn(c.keys()),d=ld,p="clear",g={groupID:n,avChatRoomKey:u,mainSequence:i};if(r.length>0){var h=[];l=[],d=cd,p="delete",r.forEach((function(e){var t=0;c.has(e)&&(t=c.get(e).sequence,l.push(e)),h.push({key:e,sequence:t})})),g.groupAttributeList=h}var f=new og(ah);return f.setMessage("groupID:".concat(n," mainSequence:").concat(i," keyList:").concat(r," protocolName:").concat(d)),this._groupModule.request({protocolName:d,requestData:g}).then((function(e){var o=e.data.mainSequence;return t._refreshCachedGroupAttributes({groupID:n,remoteMainSequence:o,groupAttributeList:r,operationType:p}),f.setNetworkType(t._groupModule.getNetworkType()).end(),Bi.log("".concat(t._className,".deleteGroupAttributes ok. groupID:").concat(n)),hm({keyList:l})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];f.setError(e,o,r).end()})),Sm(e)}))}},{key:"getGroupAttributes",value:function(e){var t=this,n=e.groupID,o=this._checkCachedGroupAttributes({groupID:n,funcName:"getGroupAttributes"});if(!0!==o)return Sm(o);var r=this._groupAttributesMap.get(n),a=r.avChatRoomKey,s=r.lastUpdateTime,i=r.localMainSequence,u=r.remoteMainSequence,c=new og(sh);if(c.setMessage("groupID:".concat(n," localMainSequence:").concat(i," remoteMainSequence:").concat(u," keyList:").concat(e.keyList)),Date.now()-s>=this.CACHE_EXPIRE_TIME||i<u)return this._getGroupAttributes({groupID:n,avChatRoomKey:a}).then((function(o){c.setMoreMessage("get attributes from remote. count:".concat(o.length)).setNetworkType(t._groupModule.getNetworkType()).end(),Bi.log("".concat(t._className,".getGroupAttributes from remote. groupID:").concat(n));var r=t._getLocalGroupAttributes(e);return hm({groupAttributes:r})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];c.setError(e,o,r).end()})),Sm(e)}));c.setMoreMessage("get attributes from cache").setNetworkType(this._groupModule.getNetworkType()).end(),Bi.log("".concat(this._className,".getGroupAttributes from cache. groupID:").concat(n));var l=this._getLocalGroupAttributes(e);return Cm({groupAttributes:l})}},{key:"_getGroupAttributes",value:function(e){var t=this;return this._groupModule.request({protocolName:dd,requestData:xn({},e)}).then((function(n){var o=n.data,r=o.mainSequence,a=o.groupAttributeList,s=Qn(a);return Ji(r)||t._refreshCachedGroupAttributes({groupID:e.groupID,remoteMainSequence:r,groupAttributeList:s,operationType:"get"}),Bi.log("".concat(t._className,"._getGroupAttributes ok. groupID:").concat(e.groupID)),a})).catch((function(e){return Sm(e)}))}},{key:"_getLocalGroupAttributes",value:function(e){var t=e.groupID,n=e.keyList,o=void 0===n?[]:n,r={};if(!this._groupAttributesMap.has(t))return r;var a=this._groupAttributesMap.get(t).attributes;if(o.length>0)o.forEach((function(e){a.has(e)&&(r[e]=a.get(e).value)}));else{var s,i=ro(a.keys());try{for(i.s();!(s=i.n()).done;){var u=s.value;r[u]=a.get(u).value}}catch(d){i.e(d)}finally{i.f()}}return r}},{key:"_refreshCachedGroupAttributes",value:function(e){var t=e.groupID,n=e.remoteMainSequence,o=e.groupAttributeList,r=e.operationType;if(this._groupAttributesMap.has(t)){var a=this._groupAttributesMap.get(t),s=a.localMainSequence;if("get"===r||n-s==1)a.remoteMainSequence=n,a.localMainSequence=n,a.lastUpdateTime=Date.now(),this._updateCachedAttributes({groupAttributes:a,groupAttributeList:o,operationType:r});else{if(s===n)return;a.remoteMainSequence=n}this._groupAttributesMap.set(t,a);var i="operationType:".concat(r," localMainSequence:").concat(s," remoteMainSequence:").concat(n);Bi.log("".concat(this._className,"._refreshCachedGroupAttributes. ").concat(i))}}},{key:"_updateCachedAttributes",value:function(e){var t=e.groupAttributes,n=e.groupAttributeList,o=e.operationType;"clear"!==o?"delete"!==o?("init"===o&&t.attributes.clear(),n.forEach((function(e){var n=e.key,o=e.value,r=e.sequence;t.attributes.set(n,{value:o,sequence:r})}))):n.forEach((function(e){t.attributes.delete(e)})):t.attributes.clear()}},{key:"_checkCachedGroupAttributes",value:function(e){var t=e.groupID,n=e.funcName;if(this._groupModule.hasLocalGroup(t)&&this._groupModule.getLocalGroupProfile(t).type!==so.GRP_AVCHATROOM)return Bi.warn("".concat(this._className,"._checkCachedGroupAttributes. ").concat("非直播群不能使用群属性 API")),new ym({code:Ld.CANNOT_USE_GRP_ATTR_NOT_AVCHATROOM,message:"非直播群不能使用群属性 API"});var o=this._groupAttributesMap.get(t);if(Ji(o)){var r="如果 groupID:".concat(t," 是直播群，使用 ").concat(n," 前先使用 joinGroup 接口申请加入群组，详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#joinGroup");return Bi.warn("".concat(this._className,"._checkCachedGroupAttributes. ").concat(r)),new ym({code:Ld.CANNOT_USE_GRP_ATTR_AVCHATROOM_UNJOIN,message:r})}return!0}},{key:"_transformGroupAttributes",value:function(e){var t=[];return Object.keys(e).forEach((function(n){t.push({key:n,value:e[n]})})),t}},{key:"_emitGroupAttributesUpdated",value:function(e){var t=this._getLocalGroupAttributes({groupID:e});this._groupModule.emitOuterEvent(ao.GROUP_ATTRIBUTES_UPDATED,{groupID:e,groupAttributes:t})}},{key:"reset",value:function(){this._groupAttributesMap.clear(),this.CACHE_EXPIRE_TIME=3e4}}]),e}(),uv=function(){function e(t){Gn(this,e);var n=t.manager,o=t.groupID,r=t.onInit,a=t.onSuccess,s=t.onFail;this._className="Polling",this._manager=n,this._groupModule=n._groupModule,this._onInit=r,this._onSuccess=a,this._onFail=s,this._groupID=o,this._timeoutID=-1,this._isRunning=!1,this._protocolName=od}return Un(e,[{key:"start",value:function(){var e=this._groupModule.isLoggedIn();e||(this._protocolName=rd),Bi.log("".concat(this._className,".start pollingInterval:").concat(this._manager.getPollingInterval()," isLoggedIn:").concat(e)),this._isRunning=!0,this._request()}},{key:"isRunning",value:function(){return this._isRunning}},{key:"_request",value:function(){var e=this,t=this._onInit(this._groupID);this._groupModule.request({protocolName:this._protocolName,requestData:t}).then((function(t){e._onSuccess(e._groupID,t),e.isRunning()&&(clearTimeout(e._timeoutID),e._timeoutID=setTimeout(e._request.bind(e),e._manager.getPollingInterval()))})).catch((function(t){e._onFail(e._groupID,t),e.isRunning()&&(clearTimeout(e._timeoutID),e._timeoutID=setTimeout(e._request.bind(e),e._manager.MAX_POLLING_INTERVAL))}))}},{key:"stop",value:function(){Bi.log("".concat(this._className,".stop")),this._timeoutID>0&&(clearTimeout(this._timeoutID),this._timeoutID=-1),this._isRunning=!1}}]),e}(),cv={3:!0,4:!0,5:!0,6:!0},lv=function(){function e(t){Gn(this,e),this._groupModule=t,this._className="AVChatRoomHandler",this._joinedGroupMap=new Map,this._pollingRequestInfoMap=new Map,this._pollingInstanceMap=new Map,this.sequencesLinkedList=new $m(100),this.messageIDLinkedList=new $m(100),this.receivedMessageCount=0,this._reportMessageStackedCount=0,this._onlineMemberCountMap=new Map,this.DEFAULT_EXPIRE_TIME=60,this.DEFAULT_POLLING_INTERVAL=300,this.MAX_POLLING_INTERVAL=2e3,this._pollingInterval=this.DEFAULT_POLLING_INTERVAL}return Un(e,[{key:"hasJoinedAVChatRoom",value:function(){return this._joinedGroupMap.size>0}},{key:"checkJoinedAVChatRoomByID",value:function(e){return this._joinedGroupMap.has(e)}},{key:"getJoinedAVChatRoom",value:function(){return this._joinedGroupMap.size>0?Qn(this._joinedGroupMap.keys()):null}},{key:"_updateRequestData",value:function(e){return xn({},this._pollingRequestInfoMap.get(e))}},{key:"_handleSuccess",value:function(e,t){var n=t.data,o=n.key,r=n.nextSeq,a=n.rspMsgList;if(0!==n.errorCode){var s=this._pollingRequestInfoMap.get(e),i=new og(_h),u=s?"".concat(s.key,"-").concat(s.startSeq):"requestInfo is undefined";i.setMessage("".concat(e,"-").concat(u,"-").concat(t.errorInfo)).setCode(t.errorCode).setNetworkType(this._groupModule.getNetworkType()).end(!0)}else{if(!this.checkJoinedAVChatRoomByID(e))return;Wi(o)&&ji(r)&&this._pollingRequestInfoMap.set(e,{key:o,startSeq:r}),zi(a)&&a.length>0&&(a.forEach((function(e){e.to=e.groupID})),this.onMessage(a))}}},{key:"_handleFailure",value:function(e,t){}},{key:"onMessage",value:function(e){if(zi(e)&&0!==e.length){var t=null,n=[],o=this._getModule(Jc),r=e.length;r>1&&e.sort((function(e,t){return e.sequence-t.sequence}));for(var a=this._getModule(Xc),s=0;s<r;s++)if(cv[e[s].event]){this.receivedMessageCount+=1,t=this.packMessage(e[s],e[s].event);var i=1===e[s].isModified,u=1===e[s].isHistoryMessage;if((a.isUnlimitedAVChatRoom()||!this.sequencesLinkedList.has(t.sequence))&&!this.messageIDLinkedList.has(t.ID)){var c=t.conversationID;if(this.receivedMessageCount%40==0&&this._getModule(il).detectMessageLoss(c,this.sequencesLinkedList.data()),null!==this.sequencesLinkedList.tail()){var l=this.sequencesLinkedList.tail().value,d=t.sequence-l;d>1&&d<=20?this._getModule(il).onMessageMaybeLost(c,l+1,d-1):d<-1&&d>=-20&&this._getModule(il).onMessageMaybeLost(c,t.sequence+1,Math.abs(d)-1)}this.sequencesLinkedList.set(t.sequence),this.messageIDLinkedList.set(t.ID);var p=!1;if(this._isMessageSentByCurrentInstance(t)?i&&(p=!0,t.isModified=i,o.updateMessageIsModifiedProperty(t)):p=!0,p){if(t.conversationType,so.CONV_SYSTEM,!u&&t.conversationType!==so.CONV_SYSTEM){var g=this._getModule(dl),h=t.conversationID.replace(so.CONV_GROUP,"");this._pollingInstanceMap.has(h)?g.addMessageSequence({key:Qp,message:t}):(t.type!==so.MSG_GRP_TIP&&g.addMessageDelay({currentTime:Date.now(),time:t.time}),g.addMessageSequence({key:Xp,message:t}))}n.push(t)}}}else Bi.warn("".concat(this._className,".onMessage 未处理的 event 类型: ").concat(e[s].event));if(0!==n.length){this._groupModule.filterModifiedMessage(n);var f=this.packConversationOption(n);f.length>0&&this._getModule(Jc).onNewMessage({conversationOptionsList:f,isInstantMessage:!0}),Bi.debug("".concat(this._className,".onMessage count:").concat(n.length)),this._checkMessageStacked(n);var _=this._groupModule.filterUnmodifiedMessage(n);_.length>0&&this._groupModule.emitOuterEvent(ao.MESSAGE_RECEIVED,_),n.length=0}}}},{key:"_checkMessageStacked",value:function(e){var t=e.length;t>=100&&(Bi.warn("".concat(this._className,"._checkMessageStacked 直播群消息堆积数:").concat(e.length,'！可能会导致微信小程序渲染时遇到 "Dom limit exceeded" 的错误，建议接入侧此时只渲染最近的10条消息')),this._reportMessageStackedCount<5&&(new og(vh).setNetworkType(this._groupModule.getNetworkType()).setMessage("count:".concat(t," groupID:").concat(Qn(this._joinedGroupMap.keys()))).setLevel("warning").end(),this._reportMessageStackedCount+=1))}},{key:"_isMessageSentByCurrentInstance",value:function(e){return!!this._getModule(Jc).isMessageSentByCurrentInstance(e)}},{key:"packMessage",value:function(e,t){e.currentUser=this._groupModule.getMyUserID(),e.conversationType=5===t?so.CONV_SYSTEM:so.CONV_GROUP,e.isSystemMessage=!!e.isSystemMessage;var n=new gm(e),o=this.packElements(e,t);return n.setElement(o),n}},{key:"packElements",value:function(e,t){return 4===t||6===t?(this._updateMemberCountByGroupTips(e),this._onGroupAttributesUpdated(e),{type:so.MSG_GRP_TIP,content:xn(xn({},e.elements),{},{groupProfile:e.groupProfile})}):5===t?{type:so.MSG_GRP_SYS_NOTICE,content:xn(xn({},e.elements),{},{groupProfile:e.groupProfile})}:this._getModule(tl).parseElements(e.elements,e.from)}},{key:"packConversationOption",value:function(e){for(var t=new Map,n=0;n<e.length;n++){var o=e[n],r=o.conversationID;if(t.has(r)){var a=t.get(r);a.lastMessage=o,"in"===o.flow&&a.unreadCount++}else t.set(r,{conversationID:o.conversationID,unreadCount:"out"===o.flow?0:1,type:o.conversationType,subType:o.conversationSubType,lastMessage:o})}return Qn(t.values())}},{key:"_updateMemberCountByGroupTips",value:function(e){var t=e.groupProfile.groupID,n=e.elements.onlineMemberInfo,o=void 0===n?void 0:n;if(!Lu(o)){var r=o.onlineMemberNum,a=void 0===r?0:r,s=o.expireTime,i=void 0===s?this.DEFAULT_EXPIRE_TIME:s,u=this._onlineMemberCountMap.get(t)||{},c=Date.now();Lu(u)?Object.assign(u,{lastReqTime:0,lastSyncTime:0,latestUpdateTime:c,memberCount:a,expireTime:i}):(u.latestUpdateTime=c,u.memberCount=a),Bi.debug("".concat(this._className,"._updateMemberCountByGroupTips info:"),u),this._onlineMemberCountMap.set(t,u)}}},{key:"start",value:function(e){if(this._pollingInstanceMap.has(e)){var t=this._pollingInstanceMap.get(e);t.isRunning()||t.start()}else{var n=new uv({manager:this,groupID:e,onInit:this._updateRequestData.bind(this),onSuccess:this._handleSuccess.bind(this),onFail:this._handleFailure.bind(this)});n.start(),this._pollingInstanceMap.set(e,n),Bi.log("".concat(this._className,".start groupID:").concat(e))}}},{key:"handleJoinResult",value:function(e){var t=this;return this._preCheck().then((function(){var n=e.longPollingKey,o=e.group,r=o.groupID;return t._joinedGroupMap.set(r,o),t._groupModule.updateGroupMap([o]),t._groupModule.deleteUnjoinedAVChatRoom(r),t._groupModule.emitGroupListUpdate(!0,!1),Ji(n)?Cm({status:j_,group:o}):Promise.resolve()}))}},{key:"startRunLoop",value:function(e){var t=this;return this.handleJoinResult(e).then((function(){var n=e.longPollingKey,o=e.group,r=o.groupID;return t._pollingRequestInfoMap.set(r,{key:n,startSeq:0}),t.start(r),t._groupModule.isLoggedIn()?Cm({status:j_,group:o}):Cm({status:j_})}))}},{key:"_preCheck",value:function(){if(this._getModule(Xc).isUnlimitedAVChatRoom())return Promise.resolve();if(!this.hasJoinedAVChatRoom())return Promise.resolve();var e=Xn(this._joinedGroupMap.entries().next().value,2),t=e[0],n=e[1];if(this._groupModule.isLoggedIn()){if(n.selfInfo.role!==so.GRP_MBR_ROLE_OWNER&&n.ownerID!==this._groupModule.getMyUserID())return this._groupModule.quitGroup(t);this._groupModule.deleteLocalGroupAndConversation(t)}else this._groupModule.deleteLocalGroupAndConversation(t);return this.reset(t),Promise.resolve()}},{key:"joinWithoutAuth",value:function(e){var t=this,n=e.groupID,o="".concat(this._className,".joinWithoutAuth"),r=new og(nh);return this._groupModule.request({protocolName:jl,requestData:e}).then((function(e){var a=e.data.longPollingKey;if(t._groupModule.probeNetwork().then((function(e){var t=Xn(e,2),o=(t[0],t[1]);r.setNetworkType(o).setMessage("groupID:".concat(n," longPollingKey:").concat(a)).end(!0)})),Ji(a))return Sm(new ym({code:Ld.CANNOT_JOIN_NON_AVCHATROOM_WITHOUT_LOGIN,message:Tp}));Bi.log("".concat(o," ok. groupID:").concat(n)),t._getModule(Jc).setCompleted("".concat(so.CONV_GROUP).concat(n));var s=new zm({groupID:n});return t.startRunLoop({group:s,longPollingKey:a}),hm({status:j_})})).catch((function(e){return Bi.error("".concat(o," failed. groupID:").concat(n," error:"),e),t._groupModule.probeNetwork().then((function(t){var o=Xn(t,2),a=o[0],s=o[1];r.setError(e,a,s).setMessage("groupID:".concat(n)).end(!0)})),Sm(e)})).finally((function(){t._groupModule.getModule(Zc).reportAtOnce()}))}},{key:"getGroupOnlineMemberCount",value:function(e){var t=this._onlineMemberCountMap.get(e)||{},n=Date.now();return Lu(t)||n-t.lastSyncTime>1e3*t.expireTime&&n-t.latestUpdateTime>1e4&&n-t.lastReqTime>3e3?(t.lastReqTime=n,this._onlineMemberCountMap.set(e,t),this._getGroupOnlineMemberCount(e).then((function(e){return hm({memberCount:e.memberCount})})).catch((function(e){return Sm(e)}))):Cm({memberCount:t.memberCount})}},{key:"_getGroupOnlineMemberCount",value:function(e){var t=this,n="".concat(this._className,"._getGroupOnlineMemberCount");return this._groupModule.request({protocolName:ad,requestData:{groupID:e}}).then((function(o){var r=t._onlineMemberCountMap.get(e)||{},a=o.data,s=a.onlineMemberNum,i=void 0===s?0:s,u=a.expireTime,c=void 0===u?t.DEFAULT_EXPIRE_TIME:u;Bi.log("".concat(n," ok. groupID:").concat(e," memberCount:").concat(i," expireTime:").concat(c));var l=Date.now();return Lu(r)&&(r.lastReqTime=l),t._onlineMemberCountMap.set(e,Object.assign(r,{lastSyncTime:l,latestUpdateTime:l,memberCount:i,expireTime:c})),{memberCount:i}})).catch((function(o){return Bi.warn("".concat(n," failed. error:"),o),new og(fh).setCode(o.code).setMessage("groupID:".concat(e," error:").concat(JSON.stringify(o))).setNetworkType(t._groupModule.getNetworkType()).end(),Promise.reject(o)}))}},{key:"_onGroupAttributesUpdated",value:function(e){var t=e.groupProfile.groupID,n=e.elements,o=n.operationType,r=n.newGroupProfile;if(6===o){var a=(void 0===r?void 0:r).groupAttributeOption;Lu(a)||this._groupModule.onGroupAttributesUpdated({groupID:t,groupAttributeOption:a})}}},{key:"_getModule",value:function(e){return this._groupModule.getModule(e)}},{key:"setPollingInterval",value:function(e){Ji(e)||ji(e)||(this._pollingInterval=parseInt(e,10),Bi.log("".concat(this._className,".setPollingInterval value:").concat(this._pollingInterval)))}},{key:"getPollingInterval",value:function(){return this._pollingInterval}},{key:"reset",value:function(e){if(e){Bi.log("".concat(this._className,".reset groupID:").concat(e));var t=this._pollingInstanceMap.get(e);t&&t.stop(),this._pollingInstanceMap.delete(e),this._joinedGroupMap.delete(e),this._pollingRequestInfoMap.delete(e),this._onlineMemberCountMap.delete(e)}else{Bi.log("".concat(this._className,".reset all"));var n,o=ro(this._pollingInstanceMap.values());try{for(o.s();!(n=o.n()).done;)n.value.stop()}catch(a){o.e(a)}finally{o.f()}this._pollingInstanceMap.clear(),this._joinedGroupMap.clear(),this._pollingRequestInfoMap.clear(),this._onlineMemberCountMap.clear()}this.sequencesLinkedList.reset(),this.messageIDLinkedList.reset(),this.receivedMessageCount=0,this._reportMessageStackedCount=0,this._pollingInterval=this.DEFAULT_POLLING_INTERVAL}}]),e}(),dv=1,pv=15,gv=function(){function e(t){Gn(this,e),this._groupModule=t,this._className="GroupSystemNoticeHandler",this.pendencyMap=new Map}return Un(e,[{key:"onNewGroupSystemNotice",value:function(e){var t=e.dataList,n=e.isSyncingEnded,o=e.isInstantMessage;Bi.debug("".concat(this._className,".onReceiveSystemNotice count:").concat(t.length));var r=this.newSystemNoticeStoredAndSummary({notifiesList:t,isInstantMessage:o}),a=r.eventDataList,s=r.result;a.length>0&&(this._groupModule.getModule(Jc).onNewMessage({conversationOptionsList:a,isInstantMessage:o}),this._onReceivedGroupSystemNotice({result:s,isInstantMessage:o})),o?s.length>0&&this._groupModule.emitOuterEvent(ao.MESSAGE_RECEIVED,s):!0===n&&this._clearGroupSystemNotice()}},{key:"newSystemNoticeStoredAndSummary",value:function(e){var t=e.notifiesList,n=e.isInstantMessage,o=null,r=t.length,a=0,s=[],i={conversationID:so.CONV_SYSTEM,unreadCount:0,type:so.CONV_SYSTEM,subType:null,lastMessage:null};for(a=0;a<r;a++){var u=t[a];u.elements.operationType!==pv&&(u.currentUser=this._groupModule.getMyUserID(),u.conversationType=so.CONV_SYSTEM,u.conversationID=so.CONV_SYSTEM,(o=new gm(u)).setElement({type:so.MSG_GRP_SYS_NOTICE,content:xn(xn({},u.elements),{},{groupProfile:u.groupProfile})}),o.isSystemMessage=!0,(1===o.sequence&&1===o.random||2===o.sequence&&2===o.random)&&(o.sequence=iu(),o.random=iu(),o.generateMessageID(u.currentUser),Bi.log("".concat(this._className,".newSystemNoticeStoredAndSummary sequence and random maybe duplicated, regenerate. ID:").concat(o.ID))),this._groupModule.getModule(Jc).pushIntoNoticeResult(s,o)&&(n?i.unreadCount++:o.setIsRead(!0),i.subType=o.conversationSubType))}return i.lastMessage=s[s.length-1],{eventDataList:s.length>0?[i]:[],result:s}}},{key:"_clearGroupSystemNotice",value:function(){var e=this;this.getPendencyList().then((function(t){t.forEach((function(t){e.pendencyMap.set("".concat(t.from,"_").concat(t.groupID,"_").concat(t.to),t)}));var n=e._groupModule.getModule(Jc).getLocalMessageList(so.CONV_SYSTEM),o=[];n.forEach((function(t){var n=t.payload,r=n.operatorID,a=n.operationType,s=n.groupProfile;if(a===dv){var i="".concat(r,"_").concat(s.groupID,"_").concat(s.to),u=e.pendencyMap.get(i);u&&ji(u.handled)&&0!==u.handled&&o.push(t)}})),e.deleteGroupSystemNotice({messageList:o})}))}},{key:"deleteGroupSystemNotice",value:function(e){var t=this,n="".concat(this._className,".deleteGroupSystemNotice");return zi(e.messageList)&&0!==e.messageList.length?(Bi.log("".concat(n)+e.messageList.map((function(e){return e.ID}))),this._groupModule.request({protocolName:nd,requestData:{messageListToDelete:e.messageList.map((function(e){return{from:so.CONV_SYSTEM,messageSeq:e.clientSequence,messageRandom:e.random}}))}}).then((function(){Bi.log("".concat(n," ok"));var o=t._groupModule.getModule(Jc);return e.messageList.forEach((function(e){o.deleteLocalMessage(e)})),hm()})).catch((function(e){return Bi.error("".concat(n," error:"),e),Sm(e)}))):Cm()}},{key:"getPendencyList",value:function(e){var t=this;return this._groupModule.request({protocolName:td,requestData:{startTime:e&&e.startTime?e.startTime:0,limit:e&&e.limit?e.limit:10,handleAccount:this._groupModule.getMyUserID()}}).then((function(e){var n=e.data.pendencyList;return 0!==e.data.nextStartTime?t.getPendencyList({startTime:e.data.nextStartTime}).then((function(e){return[].concat(Qn(n),Qn(e))})):n}))}},{key:"_onReceivedGroupSystemNotice",value:function(e){var t=this,n=e.result;e.isInstantMessage&&n.forEach((function(e){switch(e.payload.operationType){case 1:break;case 2:t._onApplyGroupRequestAgreed(e);break;case 3:break;case 4:t._onMemberKicked(e);break;case 5:t._onGroupDismissed(e);break;case 6:break;case 7:t._onInviteGroup(e);break;case 8:t._onQuitGroup(e);break;case 9:t._onSetManager(e);break;case 10:t._onDeleteManager(e)}}))}},{key:"_onApplyGroupRequestAgreed",value:function(e){var t=this,n=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(n)||this._groupModule.getGroupProfile({groupID:n}).then((function(e){var n=e.data.group;n&&(t._groupModule.updateGroupMap([n]),t._groupModule.emitGroupListUpdate())}))}},{key:"_onMemberKicked",value:function(e){var t=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(t)&&this._groupModule.deleteLocalGroupAndConversation(t)}},{key:"_onGroupDismissed",value:function(e){var t=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(t)&&this._groupModule.deleteLocalGroupAndConversation(t);var n=this._groupModule._AVChatRoomHandler;n&&n.checkJoinedAVChatRoomByID(t)&&n.reset(t)}},{key:"_onInviteGroup",value:function(e){var t=this,n=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(n)||this._groupModule.getGroupProfile({groupID:n}).then((function(e){var n=e.data.group;n&&(t._groupModule.updateGroupMap([n]),t._groupModule.emitGroupListUpdate())}))}},{key:"_onQuitGroup",value:function(e){var t=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(t)&&this._groupModule.deleteLocalGroupAndConversation(t)}},{key:"_onSetManager",value:function(e){var t=e.payload.groupProfile,n=t.to,o=t.groupID,r=this._groupModule.getModule(zc).getLocalGroupMemberInfo(o,n);r&&r.updateRole(so.GRP_MBR_ROLE_ADMIN)}},{key:"_onDeleteManager",value:function(e){var t=e.payload.groupProfile,n=t.to,o=t.groupID,r=this._groupModule.getModule(zc).getLocalGroupMemberInfo(o,n);r&&r.updateRole(so.GRP_MBR_ROLE_MEMBER)}},{key:"reset",value:function(){this.pendencyMap.clear()}}]),e}(),hv=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="GroupModule",o._commonGroupHandler=null,o._AVChatRoomHandler=null,o._groupSystemNoticeHandler=null,o._commonGroupHandler=new av(Yn(o)),o._groupAttributesHandler=new iv(Yn(o)),o._AVChatRoomHandler=new lv(Yn(o)),o._groupTipsHandler=new tv(Yn(o)),o._groupSystemNoticeHandler=new gv(Yn(o)),o.groupMap=new Map,o._unjoinedAVChatRoomList=new Map,o.getInnerEmitterInstance().on(Pm,o._onCloudConfigUpdated,Yn(o)),o}return Un(n,[{key:"_onCloudConfigUpdated",value:function(){var e=this.getCloudConfig("polling_interval");this._AVChatRoomHandler&&this._AVChatRoomHandler.setPollingInterval(e)}},{key:"onCheckTimer",value:function(e){this.isLoggedIn()&&(this._commonGroupHandler.onCheckTimer(e),this._groupTipsHandler.onCheckTimer(e))}},{key:"guardForAVChatRoom",value:function(e){var t=this;if(e.conversationType===so.CONV_GROUP){var n=e.to;return this.hasLocalGroup(n)?Cm():this.getGroupProfile({groupID:n}).then((function(o){var r=o.data.group.type;if(Bi.log("".concat(t._className,".guardForAVChatRoom. groupID:").concat(n," type:").concat(r)),r===so.GRP_AVCHATROOM){var a="userId:".concat(e.from," 未加入群 groupID:").concat(n,"。发消息前先使用 joinGroup 接口申请加群，详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#joinGroup");return Bi.warn("".concat(t._className,".guardForAVChatRoom sendMessage not allowed. ").concat(a)),Sm(new ym({code:Ld.MESSAGE_SEND_FAIL,message:a,data:{message:e}}))}return Cm()}))}return Cm()}},{key:"checkJoinedAVChatRoomByID",value:function(e){return!!this._AVChatRoomHandler&&this._AVChatRoomHandler.checkJoinedAVChatRoomByID(e)}},{key:"onNewGroupMessage",value:function(e){this._commonGroupHandler&&this._commonGroupHandler.onNewGroupMessage(e)}},{key:"updateNextMessageSeq",value:function(e){var t=this;zi(e)&&e.forEach((function(e){var n=e.conversationID.replace(so.CONV_GROUP,"");t.groupMap.has(n)&&(t.groupMap.get(n).nextMessageSeq=e.lastMessage.sequence+1)}))}},{key:"onNewGroupTips",value:function(e){this._groupTipsHandler&&this._groupTipsHandler.onNewGroupTips(e)}},{key:"onGroupMessageRevoked",value:function(e){this._commonGroupHandler&&this._commonGroupHandler.onGroupMessageRevoked(e)}},{key:"onNewGroupSystemNotice",value:function(e){this._groupSystemNoticeHandler&&this._groupSystemNoticeHandler.onNewGroupSystemNotice(e)}},{key:"onGroupMessageReadNotice",value:function(e){var t=this;e.dataList.forEach((function(e){var n=e.elements.groupMessageReadNotice;if(!Ji(n)){var o=t.getModule(Jc);n.forEach((function(e){var n=e.groupID,r=e.lastMessageSeq;Bi.debug("".concat(t._className,".onGroupMessageReadNotice groupID:").concat(n," lastMessageSeq:").concat(r));var a="".concat(so.CONV_GROUP).concat(n);o.updateIsReadAfterReadReport({conversationID:a,lastMessageSeq:r}),o.updateUnreadCount(a)}))}}))}},{key:"deleteGroupSystemNotice",value:function(e){this._groupSystemNoticeHandler&&this._groupSystemNoticeHandler.deleteGroupSystemNotice(e)}},{key:"initGroupMap",value:function(e){this.groupMap.set(e.groupID,new zm(e))}},{key:"deleteGroup",value:function(e){this.groupMap.delete(e)}},{key:"updateGroupMap",value:function(e){var t=this;e.forEach((function(e){t.groupMap.has(e.groupID)?t.groupMap.get(e.groupID).updateGroup(e):t.groupMap.set(e.groupID,new zm(e))}));var n,o=this.getMyUserID(),r=ro(this.groupMap);try{for(r.s();!(n=r.n()).done;)Xn(n.value,2)[1].selfInfo.userID=o}catch(i){r.e(i)}finally{r.f()}this._setStorageGroupList()}},{key:"getStorageGroupList",value:function(){return this.getModule(Qc).getItem("groupMap")}},{key:"_setStorageGroupList",value:function(){var e=this.getLocalGroupList().filter((function(e){var t=e.type;return!mu(t)})).slice(0,20).map((function(e){return{groupID:e.groupID,name:e.name,avatar:e.avatar,type:e.type}}));this.getModule(Qc).setItem("groupMap",e)}},{key:"getGroupMap",value:function(){return this.groupMap}},{key:"getLocalGroupList",value:function(){return Qn(this.groupMap.values())}},{key:"getLocalGroupProfile",value:function(e){return this.groupMap.get(e)}},{key:"sortLocalGroupList",value:function(){var e=Qn(this.groupMap).filter((function(e){var t=Xn(e,2);return t[0],!Lu(t[1].lastMessage)}));e.sort((function(e,t){return t[1].lastMessage.lastTime-e[1].lastMessage.lastTime})),this.groupMap=new Map(Qn(e))}},{key:"updateGroupLastMessage",value:function(e){this._commonGroupHandler&&this._commonGroupHandler.handleUpdateGroupLastMessage(e)}},{key:"emitGroupListUpdate",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=this.getLocalGroupList();if(e&&this.emitOuterEvent(ao.GROUP_LIST_UPDATED,n),t){var o=JSON.parse(JSON.stringify(n)),r=this.getModule(Jc);r.updateConversationGroupProfile(o)}}},{key:"patchGroupMessageRemindType",value:function(){var e=this.getLocalGroupList(),t=this.getModule(Jc),n=0;e.forEach((function(e){!0===t.patchMessageRemindType({ID:e.groupID,isC2CConversation:!1,messageRemindType:e.selfInfo.messageRemindType})&&(n+=1)})),Bi.log("".concat(this._className,".patchGroupMessageRemindType count:").concat(n))}},{key:"recomputeUnreadCount",value:function(){var e=this.getLocalGroupList(),t=this.getModule(Jc);e.forEach((function(e){var n=e.groupID,o=e.selfInfo,r=o.excludedUnreadSequenceList,a=o.readedSequence;if(zi(r)){var s=0;r.forEach((function(t){t>=a&&t<=e.nextMessageSeq-1&&(s+=1)})),s>=1&&t.recomputeGroupUnreadCount({conversationID:"".concat(so.CONV_GROUP).concat(n),count:s})}}))}},{key:"getMyNameCardByGroupID",value:function(e){var t=this.getLocalGroupProfile(e);return t?t.selfInfo.nameCard:""}},{key:"getGroupList",value:function(e){return this._commonGroupHandler?this._commonGroupHandler.getGroupList(e):Cm()}},{key:"getGroupProfile",value:function(e){var t=this,n=new og(Jg),o="".concat(this._className,".getGroupProfile"),r=e.groupID,a=e.groupCustomFieldFilter;Bi.log("".concat(o," groupID:").concat(r));var s={groupIDList:[r],responseFilter:{groupBaseInfoFilter:["Type","Name","Introduction","Notification","FaceUrl","Owner_Account","CreateTime","InfoSeq","LastInfoTime","LastMsgTime","MemberNum","MaxMemberNum","ApplyJoinOption","NextMsgSeq","ShutUpAllMember"],groupCustomFieldFilter:a,memberInfoFilter:["Role","JoinTime","MsgSeq","MsgFlag","NameCard"]}};return this.getGroupProfileAdvance(s).then((function(e){var a,s=e.data,i=s.successGroupList,u=s.failureGroupList;return Bi.log("".concat(o," ok")),u.length>0?Sm(u[0]):(mu(i[0].type)&&!t.hasLocalGroup(r)?a=new zm(i[0]):(t.updateGroupMap(i),a=t.getLocalGroupProfile(r)),n.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(r," type:").concat(a.type," muteAllMembers:").concat(a.muteAllMembers," ownerID:").concat(a.ownerID)).end(),hm({group:a}))})).catch((function(r){return t.probeNetwork().then((function(t){var o=Xn(t,2),a=o[0],s=o[1];n.setError(r,a,s).setMessage("groupID:".concat(e.groupID)).end()})),Bi.error("".concat(o," failed. error:"),r),Sm(r)}))}},{key:"getGroupProfileAdvance",value:function(e){var t="".concat(this._className,".getGroupProfileAdvance");return zi(e.groupIDList)&&e.groupIDList.length>50&&(Bi.warn("".concat(t," 获取群资料的数量不能超过50个")),e.groupIDList.length=50),Bi.log("".concat(t," groupIDList:").concat(e.groupIDList)),this.request({protocolName:xl,requestData:e}).then((function(e){Bi.log("".concat(t," ok"));var n=e.data.groups,o=n.filter((function(e){return Ji(e.errorCode)||0===e.errorCode})),r=n.filter((function(e){return e.errorCode&&0!==e.errorCode})).map((function(e){return new ym({code:e.errorCode,message:e.errorInfo,data:{groupID:e.groupID}})}));return hm({successGroupList:o,failureGroupList:r})})).catch((function(e){return Bi.error("".concat(t," failed. error:"),e),Sm(e)}))}},{key:"createGroup",value:function(e){var t=this,n="".concat(this._className,".createGroup");if(!["Public","Private","ChatRoom","AVChatRoom"].includes(e.type)){var o=new ym({code:Ld.ILLEGAL_GROUP_TYPE,message:hp});return Sm(o)}mu(e.type)&&!Ji(e.memberList)&&e.memberList.length>0&&(Bi.warn("".concat(n," 创建 AVChatRoom 时不能添加群成员，自动忽略该字段")),e.memberList=void 0),_u(e.type)||Ji(e.joinOption)||(Bi.warn("".concat(n," 创建 Work/Meeting/AVChatRoom 群时不能设置字段 joinOption，自动忽略该字段")),e.joinOption=void 0);var r=new og(qg);Bi.log("".concat(n," options:"),e);var a=[];return this.request({protocolName:Vl,requestData:xn(xn({},e),{},{ownerID:this.getMyUserID(),webPushFlag:1})}).then((function(o){var s=o.data,i=s.groupID,u=s.overLimitUserIDList,c=void 0===u?[]:u;if(a=c,r.setNetworkType(t.getNetworkType()).setMessage("groupType:".concat(e.type," groupID:").concat(i," overLimitUserIDList=").concat(c)).end(),Bi.log("".concat(n," ok groupID:").concat(i," overLimitUserIDList:"),c),e.type===so.GRP_AVCHATROOM)return t.getGroupProfile({groupID:i});Lu(e.memberList)||Lu(c)||(e.memberList=e.memberList.filter((function(e){return-1===c.indexOf(e.userID)}))),t.updateGroupMap([xn(xn({},e),{},{groupID:i})]);var l=t.getModule(Hc),d=l.createCustomMessage({to:i,conversationType:so.CONV_GROUP,payload:{data:"group_create",extension:"".concat(t.getMyUserID(),"创建群组")}});return l.sendMessageInstance(d),t.emitGroupListUpdate(),t.getGroupProfile({groupID:i})})).then((function(e){var t=e.data.group,n=t.selfInfo,o=n.nameCard,r=n.joinTime;return t.updateSelfInfo({nameCard:o,joinTime:r,messageRemindType:so.MSG_REMIND_ACPT_AND_NOTE,role:so.GRP_MBR_ROLE_OWNER}),hm({group:t,overLimitUserIDList:a})})).catch((function(o){return r.setMessage("groupType:".concat(e.type)),t.probeNetwork().then((function(e){var t=Xn(e,2),n=t[0],a=t[1];r.setError(o,n,a).end()})),Bi.error("".concat(n," failed. error:"),o),Sm(o)}))}},{key:"dismissGroup",value:function(e){var t=this,n="".concat(this._className,".dismissGroup");if(this.hasLocalGroup(e)&&this.getLocalGroupProfile(e).type===so.GRP_WORK)return Sm(new ym({code:Ld.CANNOT_DISMISS_WORK,message:vp}));var o=new og($g);return o.setMessage("groupID:".concat(e)),Bi.log("".concat(n," groupID:").concat(e)),this.request({protocolName:Kl,requestData:{groupID:e}}).then((function(){return o.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),t.deleteLocalGroupAndConversation(e),t.checkJoinedAVChatRoomByID(e)&&t._AVChatRoomHandler.reset(e),hm({groupID:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"updateGroupProfile",value:function(e){var t=this,n="".concat(this._className,".updateGroupProfile");!this.hasLocalGroup(e.groupID)||_u(this.getLocalGroupProfile(e.groupID).type)||Ji(e.joinOption)||(Bi.warn("".concat(n," Work/Meeting/AVChatRoom 群不能设置字段 joinOption，自动忽略该字段")),e.joinOption=void 0),Ji(e.muteAllMembers)||(e.muteAllMembers?e.muteAllMembers="On":e.muteAllMembers="Off");var o=new og(Yg);return o.setMessage(JSON.stringify(e)),Bi.log("".concat(n," groupID:").concat(e.groupID)),this.request({protocolName:Bl,requestData:e}).then((function(){return o.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),t.hasLocalGroup(e.groupID)&&(t.groupMap.get(e.groupID).updateGroup(e),t._setStorageGroupList()),hm({group:t.groupMap.get(e.groupID)})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.log("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"joinGroup",value:function(e){var t=this,n=e.groupID,o=e.type,r="".concat(this._className,".joinGroup");if(o===so.GRP_WORK){var a=new ym({code:Ld.CANNOT_JOIN_WORK,message:fp});return Sm(a)}if(this.deleteUnjoinedAVChatRoom(n),this.hasLocalGroup(n)){if(!this.isLoggedIn())return Cm({status:so.JOIN_STATUS_ALREADY_IN_GROUP});var s=new og(xg);return this.getGroupProfile({groupID:n}).then((function(){return s.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(n," joinedStatus:").concat(so.JOIN_STATUS_ALREADY_IN_GROUP)).end(),Cm({status:so.JOIN_STATUS_ALREADY_IN_GROUP})})).catch((function(o){return s.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(n," unjoined")).end(),Bi.warn("".concat(r," ").concat(n," was unjoined, now join!")),t.groupMap.delete(n),t.applyJoinGroup(e)}))}return Bi.log("".concat(r," groupID:").concat(n)),this.isLoggedIn()?this.applyJoinGroup(e):this._AVChatRoomHandler.joinWithoutAuth(e)}},{key:"applyJoinGroup",value:function(e){var t=this,n="".concat(this._className,".applyJoinGroup"),o=e.groupID,r=new og(xg),a=xn({},e),s=this.canIUse(Xs.AVCHATROOM_HISTORY_MSG);return s&&(a.historyMessageFlag=1),this.request({protocolName:Hl,requestData:a}).then((function(e){var a=e.data,i=a.joinedStatus,u=a.longPollingKey,c=a.avChatRoomFlag,l=a.avChatRoomKey,d=a.messageList,p="groupID:".concat(o," joinedStatus:").concat(i," longPollingKey:").concat(u)+" avChatRoomFlag:".concat(c," canGetAVChatRoomHistoryMessage:").concat(s);switch(r.setNetworkType(t.getNetworkType()).setMessage("".concat(p)).end(),Bi.log("".concat(n," ok. ").concat(p)),i){case W_:return hm({status:W_});case j_:return t.getGroupProfile({groupID:o}).then((function(e){var n,r=e.data.group,a={status:j_,group:r};return 1===c?(t.getModule(Jc).setCompleted("".concat(so.CONV_GROUP).concat(o)),t._groupAttributesHandler.initGroupAttributesCache({groupID:o,avChatRoomKey:l}),(n=Ji(u)?t._AVChatRoomHandler.handleJoinResult({group:r}):t._AVChatRoomHandler.startRunLoop({longPollingKey:u,group:r})).then((function(){t._onAVChatRoomHistoryMessage(d)})),n):(t.emitGroupListUpdate(!0,!1),hm(a))}));default:var g=new ym({code:Ld.JOIN_GROUP_FAIL,message:yp});return Bi.error("".concat(n," error:"),g),Sm(g)}})).catch((function(o){return r.setMessage("groupID:".concat(e.groupID)),t.probeNetwork().then((function(e){var t=Xn(e,2),n=t[0],a=t[1];r.setError(o,n,a).end()})),Bi.error("".concat(n," error:"),o),Sm(o)}))}},{key:"quitGroup",value:function(e){var t=this,n="".concat(this._className,".quitGroup");Bi.log("".concat(n," groupID:").concat(e));var o=this.checkJoinedAVChatRoomByID(e);if(!o&&!this.hasLocalGroup(e)){var r=new ym({code:Ld.MEMBER_NOT_IN_GROUP,message:Mp});return Sm(r)}if(o&&!this.isLoggedIn())return Bi.log("".concat(n," anonymously ok. groupID:").concat(e)),this.deleteLocalGroupAndConversation(e),this._AVChatRoomHandler.reset(e),Cm({groupID:e});var a=new og(Vg);return a.setMessage("groupID:".concat(e)),this.request({protocolName:Wl,requestData:{groupID:e}}).then((function(){return a.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),o&&t._AVChatRoomHandler.reset(e),t.deleteLocalGroupAndConversation(e),hm({groupID:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"searchGroupByID",value:function(e){var t=this,n="".concat(this._className,".searchGroupByID"),o={groupIDList:[e]},r=new og(Kg);return r.setMessage("groupID:".concat(e)),Bi.log("".concat(n," groupID:").concat(e)),this.request({protocolName:$l,requestData:o}).then((function(e){var o=e.data.groupProfile;if(0!==o[0].errorCode)throw new ym({code:o[0].errorCode,message:o[0].errorInfo});return r.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),hm({group:new zm(o[0])})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],a=n[1];r.setError(e,o,a).end()})),Bi.warn("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"changeGroupOwner",value:function(e){var t=this,n="".concat(this._className,".changeGroupOwner");if(this.hasLocalGroup(e.groupID)&&this.getLocalGroupProfile(e.groupID).type===so.GRP_AVCHATROOM)return Sm(new ym({code:Ld.CANNOT_CHANGE_OWNER_IN_AVCHATROOM,message:_p}));if(e.newOwnerID===this.getMyUserID())return Sm(new ym({code:Ld.CANNOT_CHANGE_OWNER_TO_SELF,message:mp}));var o=new og(Bg);return o.setMessage("groupID:".concat(e.groupID," newOwnerID:").concat(e.newOwnerID)),Bi.log("".concat(n," groupID:").concat(e.groupID)),this.request({protocolName:Yl,requestData:e}).then((function(){o.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok"));var r=e.groupID,a=e.newOwnerID;t.groupMap.get(r).ownerID=a;var s=t.getModule(zc).getLocalGroupMemberList(r);if(s instanceof Map){var i=s.get(t.getMyUserID());Ji(i)||(i.updateRole("Member"),t.groupMap.get(r).selfInfo.role="Member");var u=s.get(a);Ji(u)||u.updateRole("Owner")}return t.emitGroupListUpdate(!0,!1),hm({group:t.groupMap.get(r)})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"handleGroupApplication",value:function(e){var t=this,n="".concat(this._className,".handleGroupApplication"),o=e.message.payload,r=o.groupProfile.groupID,a=o.authentication,s=o.messageKey,i=o.operatorID,u=new og(Hg);return u.setMessage("groupID:".concat(r)),Bi.log("".concat(n," groupID:").concat(r)),this.request({protocolName:zl,requestData:xn(xn({},e),{},{applicant:i,groupID:r,authentication:a,messageKey:s})}).then((function(){return u.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),t._groupSystemNoticeHandler.deleteGroupSystemNotice({messageList:[e.message]}),hm({group:t.getLocalGroupProfile(r)})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];u.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error"),e),Sm(e)}))}},{key:"handleGroupInvitation",value:function(e){var t=this,n="".concat(this._className,".handleGroupInvitation"),o=e.message.payload,r=o.groupProfile.groupID,a=o.authentication,s=o.messageKey,i=o.operatorID,u=e.handleAction,c=new og(jg);return c.setMessage("groupID:".concat(r," inviter:").concat(i," handleAction:").concat(u)),Bi.log("".concat(n," groupID:").concat(r," inviter:").concat(i," handleAction:").concat(u)),this.request({protocolName:Jl,requestData:xn(xn({},e),{},{inviter:i,groupID:r,authentication:a,messageKey:s})}).then((function(){return c.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),t._groupSystemNoticeHandler.deleteGroupSystemNotice({messageList:[e.message]}),hm({group:t.getLocalGroupProfile(r)})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];c.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error"),e),Sm(e)}))}},{key:"getGroupOnlineMemberCount",value:function(e){return this._AVChatRoomHandler?this._AVChatRoomHandler.checkJoinedAVChatRoomByID(e)?this._AVChatRoomHandler.getGroupOnlineMemberCount(e):Cm({memberCount:0}):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"hasLocalGroup",value:function(e){return this.groupMap.has(e)}},{key:"deleteLocalGroupAndConversation",value:function(e){this._deleteLocalGroup(e),this.getModule(Jc).deleteLocalConversation("GROUP".concat(e)),this.emitGroupListUpdate(!0,!1)}},{key:"_deleteLocalGroup",value:function(e){this.groupMap.delete(e),this.getModule(zc).deleteGroupMemberList(e),this._setStorageGroupList()}},{key:"sendMessage",value:function(e,t){var n=this.createGroupMessagePack(e,t);return this.request(n)}},{key:"createGroupMessagePack",value:function(e,t){var n=null;t&&t.offlinePushInfo&&(n=t.offlinePushInfo);var o="";Wi(e.cloudCustomData)&&e.cloudCustomData.length>0&&(o=e.cloudCustomData);var r=[];if(Yi(t)&&Yi(t.messageControlInfo)){var a=t.messageControlInfo,s=a.excludedFromUnreadCount,i=a.excludedFromLastMessage;!0===s&&r.push("NoUnread"),!0===i&&r.push("NoLastMsg")}var u=e.getGroupAtInfoList();return{protocolName:Il,tjgID:this.generateTjgID(e),requestData:{fromAccount:this.getMyUserID(),groupID:e.to,msgBody:e.getElements(),cloudCustomData:o,random:e.random,priority:e.priority,clientSequence:e.clientSequence,groupAtInfo:e.type!==so.MSG_TEXT||Lu(u)?void 0:u,onlineOnlyFlag:this.isOnlineMessage(e,t)?1:0,offlinePushInfo:n?{pushFlag:!0===n.disablePush?1:0,title:n.title||"",desc:n.description||"",ext:n.extension||"",apnsInfo:{badgeMode:!0===n.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:n.androidOPPOChannelID||""}}:void 0,messageControlInfo:r}}}},{key:"revokeMessage",value:function(e){return this.request({protocolName:Xl,requestData:{to:e.to,msgSeqList:[{msgSeq:e.sequence}]}})}},{key:"deleteMessage",value:function(e){var t=e.to,n=e.keyList;return Bi.log("".concat(this._className,".deleteMessage groupID:").concat(t," count:").concat(n.length)),this.request({protocolName:sd,requestData:{groupID:t,deleter:this.getMyUserID(),keyList:n}})}},{key:"getRoamingMessage",value:function(e){var t=this,n="".concat(this._className,".getRoamingMessage"),o=new og(Ig),r=0;return this._computeLastSequence(e).then((function(n){return r=n,Bi.log("".concat(t._className,".getRoamingMessage groupID:").concat(e.groupID," lastSequence:").concat(r)),t.request({protocolName:ed,requestData:{groupID:e.groupID,count:21,sequence:r}})})).then((function(a){var s=a.data,i=s.messageList,u=s.complete;Ji(i)?Bi.log("".concat(n," ok. complete:").concat(u," but messageList is undefined!")):Bi.log("".concat(n," ok. complete:").concat(u," count:").concat(i.length)),o.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(e.groupID," lastSequence:").concat(r," complete:").concat(u," count:").concat(i?i.length:"undefined")).end();var c="GROUP".concat(e.groupID),l=t.getModule(Jc);if(2===u||Lu(i))return l.setCompleted(c),[];var d=l.storeRoamingMessage(i,c);return l.updateIsRead(c),l.patchConversationLastMessage(c),d})).catch((function(a){return t.probeNetwork().then((function(t){var n=Xn(t,2),s=n[0],i=n[1];o.setError(a,s,i).setMessage("groupID:".concat(e.groupID," lastSequence:").concat(r)).end()})),Bi.warn("".concat(n," failed. error:"),a),Sm(a)}))}},{key:"setMessageRead",value:function(e){var t=this,n=e.conversationID,o=e.lastMessageSeq,r="".concat(this._className,".setMessageRead");Bi.log("".concat(r," conversationID:").concat(n," lastMessageSeq:").concat(o)),ji(o)||Bi.warn("".concat(r," 请勿修改 Conversation.lastMessage.lastSequence，否则可能会导致已读上报结果不准确"));var a=new og(Ag);return a.setMessage("".concat(n,"-").concat(o)),this.request({protocolName:Ql,requestData:{groupID:n.replace("GROUP",""),messageReadSeq:o}}).then((function(){a.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(r," ok."));var e=t.getModule(Jc);return e.updateIsReadAfterReadReport({conversationID:n,lastMessageSeq:o}),e.updateUnreadCount(n),hm()})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),Bi.log("".concat(r," failed. error:"),e),Sm(e)}))}},{key:"_computeLastSequence",value:function(e){return e.sequence>0?Promise.resolve(e.sequence):this.getGroupLastSequence(e.groupID)}},{key:"getGroupLastSequence",value:function(e){var t=this,n="".concat(this._className,".getGroupLastSequence"),o=new og(Qg),r=0,a="";if(this.hasLocalGroup(e)){var s=this.getLocalGroupProfile(e),i=s.lastMessage;if(i.lastSequence>0&&!1===i.onlineOnlyFlag)return r=i.lastSequence,a="got lastSequence:".concat(r," from local group profile[lastMessage.lastSequence]. groupID:").concat(e),Bi.log("".concat(n," ").concat(a)),o.setNetworkType(this.getNetworkType()).setMessage("".concat(a)).end(),Promise.resolve(r);if(s.nextMessageSeq>1)return r=s.nextMessageSeq-1,a="got lastSequence:".concat(r," from local group profile[nextMessageSeq]. groupID:").concat(e),Bi.log("".concat(n," ").concat(a)),o.setNetworkType(this.getNetworkType()).setMessage("".concat(a)).end(),Promise.resolve(r)}var u="GROUP".concat(e),c=this.getModule(Jc).getLocalConversation(u);if(c&&c.lastMessage.lastSequence&&!1===c.lastMessage.onlineOnlyFlag)return r=c.lastMessage.lastSequence,a="got lastSequence:".concat(r," from local conversation profile[lastMessage.lastSequence]. groupID:").concat(e),Bi.log("".concat(n," ").concat(a)),o.setNetworkType(this.getNetworkType()).setMessage("".concat(a)).end(),Promise.resolve(r);var l={groupIDList:[e],responseFilter:{groupBaseInfoFilter:["NextMsgSeq"]}};return this.getGroupProfileAdvance(l).then((function(s){var i=s.data.successGroupList;return Lu(i)?Bi.log("".concat(n," successGroupList is empty. groupID:").concat(e)):(r=i[0].nextMessageSeq-1,a="got lastSequence:".concat(r," from getGroupProfileAdvance. groupID:").concat(e),Bi.log("".concat(n," ").concat(a))),o.setNetworkType(t.getNetworkType()).setMessage("".concat(a)).end(),r})).catch((function(r){return t.probeNetwork().then((function(t){var n=Xn(t,2),a=n[0],s=n[1];o.setError(r,a,s).setMessage("get lastSequence failed from getGroupProfileAdvance. groupID:".concat(e)).end()})),Bi.warn("".concat(n," failed. error:"),r),Sm(r)}))}},{key:"isMessageFromAVChatroom",value:function(e){return!!this._AVChatRoomHandler&&this._AVChatRoomHandler.checkJoinedAVChatRoomByID(e)}},{key:"hasJoinedAVChatRoom",value:function(){return this._AVChatRoomHandler?this._AVChatRoomHandler.hasJoinedAVChatRoom():0}},{key:"getJoinedAVChatRoom",value:function(){return this._AVChatRoomHandler?this._AVChatRoomHandler.getJoinedAVChatRoom():[]}},{key:"isOnlineMessage",value:function(e,t){return!(!this._canIUseOnlineOnlyFlag(e)||!t||!0!==t.onlineUserOnly)}},{key:"_canIUseOnlineOnlyFlag",value:function(e){var t=this.getJoinedAVChatRoom();return!t||!t.includes(e.to)||e.conversationType!==so.CONV_GROUP}},{key:"deleteLocalGroupMembers",value:function(e,t){this.getModule(zc).deleteLocalGroupMembers(e,t)}},{key:"_onAVChatRoomHistoryMessage",value:function(e){if(!Lu(e)){Bi.log("".concat(this._className,"._onAVChatRoomHistoryMessage count:").concat(e.length));var t=[];e.forEach((function(e){t.push(xn(xn({},e),{},{isHistoryMessage:1}))})),this.onAVChatRoomMessage(t)}}},{key:"onAVChatRoomMessage",value:function(e){this._AVChatRoomHandler&&this._AVChatRoomHandler.onMessage(e)}},{key:"getGroupSimplifiedInfo",value:function(e){var t=this,n=new og(th),o={groupIDList:[e],responseFilter:{groupBaseInfoFilter:["Type","Name"]}};return this.getGroupProfileAdvance(o).then((function(o){var r=o.data.successGroupList;return n.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(e," type:").concat(r[0].type)).end(),r[0]})).catch((function(o){t.probeNetwork().then((function(t){var r=Xn(t,2),a=r[0],s=r[1];n.setError(o,a,s).setMessage("groupID:".concat(e)).end()}))}))}},{key:"setUnjoinedAVChatRoom",value:function(e){this._unjoinedAVChatRoomList.set(e,1)}},{key:"deleteUnjoinedAVChatRoom",value:function(e){this._unjoinedAVChatRoomList.has(e)&&this._unjoinedAVChatRoomList.delete(e)}},{key:"isUnjoinedAVChatRoom",value:function(e){return this._unjoinedAVChatRoomList.has(e)}},{key:"onGroupAttributesUpdated",value:function(e){this._groupAttributesHandler&&this._groupAttributesHandler.onGroupAttributesUpdated(e)}},{key:"updateLocalMainSequenceOnReconnected",value:function(){this._groupAttributesHandler&&this._groupAttributesHandler.updateLocalMainSequenceOnReconnected()}},{key:"initGroupAttributes",value:function(e){return this._groupAttributesHandler.initGroupAttributes(e)}},{key:"setGroupAttributes",value:function(e){return this._groupAttributesHandler.setGroupAttributes(e)}},{key:"deleteGroupAttributes",value:function(e){return this._groupAttributesHandler.deleteGroupAttributes(e)}},{key:"getGroupAttributes",value:function(e){return this._groupAttributesHandler.getGroupAttributes(e)}},{key:"reset",value:function(){this.groupMap.clear(),this._unjoinedAVChatRoomList.clear(),this._commonGroupHandler.reset(),this._groupSystemNoticeHandler.reset(),this._groupTipsHandler.reset(),this._AVChatRoomHandler&&this._AVChatRoomHandler.reset()}}]),n}(gl),fv=function(){function e(t){Gn(this,e),this.userID="",this.avatar="",this.nick="",this.role="",this.joinTime="",this.lastSendMsgTime="",this.nameCard="",this.muteUntil=0,this.memberCustomField=[],this._initMember(t)}return Un(e,[{key:"_initMember",value:function(e){this.updateMember(e)}},{key:"updateMember",value:function(e){var t=[null,void 0,"",0,NaN];e.memberCustomField&&fu(this.memberCustomField,e.memberCustomField),ru(this,e,["memberCustomField"],t)}},{key:"updateRole",value:function(e){["Owner","Admin","Member"].indexOf(e)<0||(this.role=e)}},{key:"updateMuteUntil",value:function(e){Ji(e)||(this.muteUntil=Math.floor((Date.now()+1e3*e)/1e3))}},{key:"updateNameCard",value:function(e){Ji(e)||(this.nameCard=e)}},{key:"updateMemberCustomField",value:function(e){e&&fu(this.memberCustomField,e)}}]),e}(),_v=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="GroupMemberModule",o.groupMemberListMap=new Map,o.getInnerEmitterInstance().on(Um,o._onProfileUpdated,Yn(o)),o}return Un(n,[{key:"_onProfileUpdated",value:function(e){for(var t=this,n=e.data,o=function(e){var o=n[e];t.groupMemberListMap.forEach((function(e){e.has(o.userID)&&e.get(o.userID).updateMember({nick:o.nick,avatar:o.avatar})}))},r=0;r<n.length;r++)o(r)}},{key:"deleteGroupMemberList",value:function(e){this.groupMemberListMap.delete(e)}},{key:"getGroupMemberList",value:function(e){var t=this,n=e.groupID,o=e.offset,r=void 0===o?0:o,a=e.count,s=void 0===a?15:a,i="".concat(this._className,".getGroupMemberList"),u=new og(ih);Bi.log("".concat(i," groupID:").concat(n," offset:").concat(r," count:").concat(s));var c=[];return this.request({protocolName:pd,requestData:{groupID:n,offset:r,limit:s>100?100:s}}).then((function(e){var o=e.data,r=o.members,a=o.memberNum;if(!zi(r)||0===r.length)return Promise.resolve([]);var s=t.getModule($c);return s.hasLocalGroup(n)&&(s.getLocalGroupProfile(n).memberNum=a),c=t._updateLocalGroupMemberMap(n,r),t.getModule(jc).getUserProfile({userIDList:r.map((function(e){return e.userID})),tagList:[V_.NICK,V_.AVATAR]})})).then((function(e){var o=e.data;if(!zi(o)||0===o.length)return Cm({memberList:[]});var a=o.map((function(e){return{userID:e.userID,nick:e.nick,avatar:e.avatar}}));return t._updateLocalGroupMemberMap(n,a),u.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(n," offset:").concat(r," count:").concat(s)).end(),Bi.log("".concat(i," ok.")),hm({memberList:c})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];u.setError(e,o,r).end()})),Bi.error("".concat(i," failed. error:"),e),Sm(e)}))}},{key:"getGroupMemberProfile",value:function(e){var t=this,n="".concat(this._className,".getGroupMemberProfile"),o=new og(uh);o.setMessage(e.userIDList.length>5?"userIDList.length:".concat(e.userIDList.length):"userIDList:".concat(e.userIDList)),Bi.log("".concat(n," groupID:").concat(e.groupID," userIDList:").concat(e.userIDList.join(","))),e.userIDList.length>50&&(e.userIDList=e.userIDList.slice(0,50));var r=e.groupID,a=e.userIDList;return this._getGroupMemberProfileAdvance(xn(xn({},e),{},{userIDList:a})).then((function(e){var n=e.data.members;return zi(n)&&0!==n.length?(t._updateLocalGroupMemberMap(r,n),t.getModule(jc).getUserProfile({userIDList:n.map((function(e){return e.userID})),tagList:[V_.NICK,V_.AVATAR]})):Cm([])})).then((function(e){var n=e.data.map((function(e){return{userID:e.userID,nick:e.nick,avatar:e.avatar}}));t._updateLocalGroupMemberMap(r,n);var s=a.filter((function(e){return t.hasLocalGroupMember(r,e)})).map((function(e){return t.getLocalGroupMemberInfo(r,e)}));return o.setNetworkType(t.getNetworkType()).end(),hm({memberList:s})}))}},{key:"addGroupMember",value:function(e){var t=this,n="".concat(this._className,".addGroupMember"),o=e.groupID,r=this.getModule($c).getLocalGroupProfile(o),a=r.type,s=new og(ch);if(s.setMessage("groupID:".concat(o," groupType:").concat(a)),mu(a)){var i=new ym({code:Ld.CANNOT_ADD_MEMBER_IN_AVCHATROOM,message:Ip});return s.setCode(Ld.CANNOT_ADD_MEMBER_IN_AVCHATROOM).setError(Ip).setNetworkType(this.getNetworkType()).end(),Sm(i)}return e.userIDList=e.userIDList.map((function(e){return{userID:e}})),Bi.log("".concat(n," groupID:").concat(o)),this.request({protocolName:hd,requestData:e}).then((function(o){var a=o.data.members;Bi.log("".concat(n," ok"));var i=a.filter((function(e){return 1===e.result})).map((function(e){return e.userID})),u=a.filter((function(e){return 0===e.result})).map((function(e){return e.userID})),c=a.filter((function(e){return 2===e.result})).map((function(e){return e.userID})),l=a.filter((function(e){return 4===e.result})).map((function(e){return e.userID})),d="groupID:".concat(e.groupID,", ")+"successUserIDList:".concat(i,", ")+"failureUserIDList:".concat(u,", ")+"existedUserIDList:".concat(c,", ")+"overLimitUserIDList:".concat(l);return s.setNetworkType(t.getNetworkType()).setMoreMessage(d).end(),0===i.length?hm({successUserIDList:i,failureUserIDList:u,existedUserIDList:c,overLimitUserIDList:l}):(r.memberNum+=i.length,hm({successUserIDList:i,failureUserIDList:u,existedUserIDList:c,overLimitUserIDList:l,group:r}))})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];s.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"deleteGroupMember",value:function(e){var t=this,n="".concat(this._className,".deleteGroupMember"),o=e.groupID,r=e.userIDList,a=new og(lh),s="groupID:".concat(o," ").concat(r.length>5?"userIDList.length:".concat(r.length):"userIDList:".concat(r));a.setMessage(s),Bi.log("".concat(n," groupID:").concat(o," userIDList:"),r);var i=this.getModule($c).getLocalGroupProfile(o);return mu(i.type)?Sm(new ym({code:Ld.CANNOT_KICK_MEMBER_IN_AVCHATROOM,message:Cp})):this.request({protocolName:fd,requestData:e}).then((function(){return a.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),i.memberNum--,t.deleteLocalGroupMembers(o,r),hm({group:i,userIDList:r})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"setGroupMemberMuteTime",value:function(e){var t=this,n=e.groupID,o=e.userID,r=e.muteTime,a="".concat(this._className,".setGroupMemberMuteTime");if(o===this.getMyUserID())return Sm(new ym({code:Ld.CANNOT_MUTE_SELF,message:Dp}));Bi.log("".concat(a," groupID:").concat(n," userID:").concat(o));var s=new og(dh);return s.setMessage("groupID:".concat(n," userID:").concat(o," muteTime:").concat(r)),this.modifyGroupMemberInfo({groupID:n,userID:o,muteTime:r}).then((function(e){s.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(a," ok"));var o=t.getModule($c);return hm({group:o.getLocalGroupProfile(n),member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];s.setError(e,o,r).end()})),Bi.error("".concat(a," failed. error:"),e),Sm(e)}))}},{key:"setGroupMemberRole",value:function(e){var t=this,n="".concat(this._className,".setGroupMemberRole"),o=e.groupID,r=e.userID,a=e.role,s=this.getModule($c).getLocalGroupProfile(o);if(s.selfInfo.role!==so.GRP_MBR_ROLE_OWNER)return Sm(new ym({code:Ld.NOT_OWNER,message:Sp}));if([so.GRP_WORK,so.GRP_AVCHATROOM].includes(s.type))return Sm(new ym({code:Ld.CANNOT_SET_MEMBER_ROLE_IN_WORK_AND_AVCHATROOM,message:Ap}));if([so.GRP_MBR_ROLE_ADMIN,so.GRP_MBR_ROLE_MEMBER].indexOf(a)<0)return Sm(new ym({code:Ld.INVALID_MEMBER_ROLE,message:Ep}));if(r===this.getMyUserID())return Sm(new ym({code:Ld.CANNOT_SET_SELF_MEMBER_ROLE,message:kp}));var i=new og(gh);return i.setMessage("groupID:".concat(o," userID:").concat(r," role:").concat(a)),Bi.log("".concat(n," groupID:").concat(o," userID:").concat(r)),this.modifyGroupMemberInfo({groupID:o,userID:r,role:a}).then((function(e){return i.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),hm({group:s,member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];i.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"setGroupMemberNameCard",value:function(e){var t=this,n="".concat(this._className,".setGroupMemberNameCard"),o=e.groupID,r=e.userID,a=void 0===r?this.getMyUserID():r,s=e.nameCard;Bi.log("".concat(n," groupID:").concat(o," userID:").concat(a));var i=new og(ph);return i.setMessage("groupID:".concat(o," userID:").concat(a," nameCard:").concat(s)),this.modifyGroupMemberInfo({groupID:o,userID:a,nameCard:s}).then((function(e){Bi.log("".concat(n," ok")),i.setNetworkType(t.getNetworkType()).end();var r=t.getModule($c).getLocalGroupProfile(o);return a===t.getMyUserID()&&r&&r.setSelfNameCard(s),hm({group:r,member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];i.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"setGroupMemberCustomField",value:function(e){var t=this,n="".concat(this._className,".setGroupMemberCustomField"),o=e.groupID,r=e.userID,a=void 0===r?this.getMyUserID():r,s=e.memberCustomField;Bi.log("".concat(n," groupID:").concat(o," userID:").concat(a));var i=new og(hh);return i.setMessage("groupID:".concat(o," userID:").concat(a," memberCustomField:").concat(JSON.stringify(s))),this.modifyGroupMemberInfo({groupID:o,userID:a,memberCustomField:s}).then((function(e){i.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok"));var r=t.getModule($c).getLocalGroupProfile(o);return hm({group:r,member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];i.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"modifyGroupMemberInfo",value:function(e){var t=this,n=e.groupID,o=e.userID;return this.request({protocolName:_d,requestData:e}).then((function(){if(t.hasLocalGroupMember(n,o)){var r=t.getLocalGroupMemberInfo(n,o);return Ji(e.muteTime)||r.updateMuteUntil(e.muteTime),Ji(e.role)||r.updateRole(e.role),Ji(e.nameCard)||r.updateNameCard(e.nameCard),Ji(e.memberCustomField)||r.updateMemberCustomField(e.memberCustomField),r}return t.getGroupMemberProfile({groupID:n,userIDList:[o]}).then((function(e){return Xn(e.data.memberList,1)[0]}))}))}},{key:"_getGroupMemberProfileAdvance",value:function(e){return this.request({protocolName:gd,requestData:xn(xn({},e),{},{memberInfoFilter:e.memberInfoFilter?e.memberInfoFilter:["Role","JoinTime","NameCard","ShutUpUntil"]})})}},{key:"_updateLocalGroupMemberMap",value:function(e,t){var n=this;return zi(t)&&0!==t.length?t.map((function(t){return n.hasLocalGroupMember(e,t.userID)?n.getLocalGroupMemberInfo(e,t.userID).updateMember(t):n.setLocalGroupMember(e,new fv(t)),n.getLocalGroupMemberInfo(e,t.userID)})):[]}},{key:"deleteLocalGroupMembers",value:function(e,t){var n=this.groupMemberListMap.get(e);n&&t.forEach((function(e){n.delete(e)}))}},{key:"getLocalGroupMemberInfo",value:function(e,t){return this.groupMemberListMap.has(e)?this.groupMemberListMap.get(e).get(t):null}},{key:"setLocalGroupMember",value:function(e,t){if(this.groupMemberListMap.has(e))this.groupMemberListMap.get(e).set(t.userID,t);else{var n=(new Map).set(t.userID,t);this.groupMemberListMap.set(e,n)}}},{key:"getLocalGroupMemberList",value:function(e){return this.groupMemberListMap.get(e)}},{key:"hasLocalGroupMember",value:function(e,t){return this.groupMemberListMap.has(e)&&this.groupMemberListMap.get(e).has(t)}},{key:"hasLocalGroupMemberMap",value:function(e){return this.groupMemberListMap.has(e)}},{key:"reset",value:function(){this.groupMemberListMap.clear()}}]),n}(gl),mv=function(){function e(t){Gn(this,e),this._userModule=t,this._className="ProfileHandler",this.TAG="profile",this.accountProfileMap=new Map,this.expirationTime=864e5}return Un(e,[{key:"setExpirationTime",value:function(e){this.expirationTime=e}},{key:"getUserProfile",value:function(e){var t=this,n=e.userIDList;e.fromAccount=this._userModule.getMyAccount(),n.length>100&&(Bi.warn("".concat(this._className,".getUserProfile 获取用户资料人数不能超过100人")),n.length=100);for(var o,r=[],a=[],s=0,i=n.length;s<i;s++)o=n[s],this._userModule.isMyFriend(o)&&this._containsAccount(o)?a.push(this._getProfileFromMap(o)):r.push(o);if(0===r.length)return Cm(a);e.toAccount=r;var u=e.bFromGetMyProfile||!1,c=[];e.toAccount.forEach((function(e){c.push({toAccount:e,standardSequence:0,customSequence:0})})),e.userItem=c;var l=new og(Mh);return l.setMessage(n.length>5?"userIDList.length:".concat(n.length):"userIDList:".concat(n)),this._userModule.request({protocolName:Tl,requestData:e}).then((function(e){l.setNetworkType(t._userModule.getNetworkType()).end(),Bi.info("".concat(t._className,".getUserProfile ok"));var n=t._handleResponse(e).concat(a);return hm(u?n[0]:n)})).catch((function(e){return t._userModule.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];l.setError(e,o,r).end()})),Bi.error("".concat(t._className,".getUserProfile failed. error:"),e),Sm(e)}))}},{key:"getMyProfile",value:function(){var e=this._userModule.getMyAccount();if(Bi.log("".concat(this._className,".getMyProfile myAccount:").concat(e)),this._fillMap(),this._containsAccount(e)){var t=this._getProfileFromMap(e);return Bi.debug("".concat(this._className,".getMyProfile from cache, myProfile:")+JSON.stringify(t)),Cm(t)}return this.getUserProfile({fromAccount:e,userIDList:[e],bFromGetMyProfile:!0})}},{key:"_handleResponse",value:function(e){for(var t,n,o=ou.now(),r=e.data.userProfileItem,a=[],s=0,i=r.length;s<i;s++)"@TLS#NOT_FOUND"!==r[s].to&&""!==r[s].to&&(t=r[s].to,n=this._updateMap(t,this._getLatestProfileFromResponse(t,r[s].profileItem)),a.push(n));return Bi.log("".concat(this._className,"._handleResponse cost ").concat(ou.now()-o," ms")),a}},{key:"_getLatestProfileFromResponse",value:function(e,t){var n={};if(n.userID=e,n.profileCustomField=[],!Lu(t))for(var o=0,r=t.length;o<r;o++)if(t[o].tag.indexOf("Tag_Profile_Custom")>-1)n.profileCustomField.push({key:t[o].tag,value:t[o].value});else switch(t[o].tag){case V_.NICK:n.nick=t[o].value;break;case V_.GENDER:n.gender=t[o].value;break;case V_.BIRTHDAY:n.birthday=t[o].value;break;case V_.LOCATION:n.location=t[o].value;break;case V_.SELFSIGNATURE:n.selfSignature=t[o].value;break;case V_.ALLOWTYPE:n.allowType=t[o].value;break;case V_.LANGUAGE:n.language=t[o].value;break;case V_.AVATAR:n.avatar=t[o].value;break;case V_.MESSAGESETTINGS:n.messageSettings=t[o].value;break;case V_.ADMINFORBIDTYPE:n.adminForbidType=t[o].value;break;case V_.LEVEL:n.level=t[o].value;break;case V_.ROLE:n.role=t[o].value;break;default:Bi.warn("".concat(this._className,"._handleResponse unknown tag:"),t[o].tag,t[o].value)}return n}},{key:"updateMyProfile",value:function(e){var t=this,n="".concat(this._className,".updateMyProfile"),o=new og(yh);o.setMessage(JSON.stringify(e));var r=(new jm).validate(e);if(!r.valid)return o.setCode(Ld.UPDATE_PROFILE_INVALID_PARAM).setMoreMessage("".concat(n," info:").concat(r.tips)).setNetworkType(this._userModule.getNetworkType()).end(),Bi.error("".concat(n," info:").concat(r.tips,"，请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#updateMyProfile")),Sm({code:Ld.UPDATE_PROFILE_INVALID_PARAM,message:Np});var a=[];for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&("profileCustomField"===s?e.profileCustomField.forEach((function(e){a.push({tag:e.key,value:e.value})})):a.push({tag:V_[s.toUpperCase()],value:e[s]}));return 0===a.length?(o.setCode(Ld.UPDATE_PROFILE_NO_KEY).setMoreMessage(Op).setNetworkType(this._userModule.getNetworkType()).end(),Bi.error("".concat(n," info:").concat(Op,"，请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#updateMyProfile")),Sm({code:Ld.UPDATE_PROFILE_NO_KEY,message:Op})):this._userModule.request({protocolName:Cl,requestData:{fromAccount:this._userModule.getMyAccount(),profileItem:a}}).then((function(r){o.setNetworkType(t._userModule.getNetworkType()).end(),Bi.info("".concat(n," ok"));var a=t._updateMap(t._userModule.getMyAccount(),e);return t._userModule.emitOuterEvent(ao.PROFILE_UPDATED,[a]),Cm(a)})).catch((function(e){return t._userModule.probeNetwork().then((function(t){var n=Xn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))}},{key:"onProfileModified",value:function(e){var t=e.dataList;if(!Lu(t)){var n,o,r=t.length;Bi.debug("".concat(this._className,".onProfileModified count:").concat(r," dataList:"),e.dataList);for(var a=[],s=0;s<r;s++)n=t[s].userID,o=this._updateMap(n,this._getLatestProfileFromResponse(n,t[s].profileList)),a.push(o);a.length>0&&(this._userModule.emitInnerEvent(Um,a),this._userModule.emitOuterEvent(ao.PROFILE_UPDATED,a))}}},{key:"_fillMap",value:function(){if(0===this.accountProfileMap.size){for(var e=this._getCachedProfiles(),t=Date.now(),n=0,o=e.length;n<o;n++)t-e[n].lastUpdatedTime<this.expirationTime&&this.accountProfileMap.set(e[n].userID,e[n]);Bi.log("".concat(this._className,"._fillMap from cache, map.size:").concat(this.accountProfileMap.size))}}},{key:"_updateMap",value:function(e,t){var n,o=Date.now();return this._containsAccount(e)?(n=this._getProfileFromMap(e),t.profileCustomField&&fu(n.profileCustomField,t.profileCustomField),ru(n,t,["profileCustomField"]),n.lastUpdatedTime=o):(n=new jm(t),(this._userModule.isMyFriend(e)||e===this._userModule.getMyAccount())&&(n.lastUpdatedTime=o,this.accountProfileMap.set(e,n))),this._flushMap(e===this._userModule.getMyAccount()),n}},{key:"_flushMap",value:function(e){var t=Qn(this.accountProfileMap.values()),n=this._userModule.getStorageModule();Bi.debug("".concat(this._className,"._flushMap length:").concat(t.length," flushAtOnce:").concat(e)),n.setItem(this.TAG,t,e)}},{key:"_containsAccount",value:function(e){return this.accountProfileMap.has(e)}},{key:"_getProfileFromMap",value:function(e){return this.accountProfileMap.get(e)}},{key:"_getCachedProfiles",value:function(){var e=this._userModule.getStorageModule().getItem(this.TAG);return Lu(e)?[]:e}},{key:"onConversationsProfileUpdated",value:function(e){for(var t,n,o,r=[],a=0,s=e.length;a<s;a++)n=(t=e[a]).userID,this._userModule.isMyFriend(n)||(this._containsAccount(n)?(o=this._getProfileFromMap(n),ru(o,t)>0&&r.push(n)):r.push(t.userID));0!==r.length&&(Bi.info("".concat(this._className,".onConversationsProfileUpdated toAccountList:").concat(r)),this.getUserProfile({userIDList:r}))}},{key:"getNickAndAvatarByUserID",value:function(e){if(this._containsAccount(e)){var t=this._getProfileFromMap(e);return{nick:t.nick,avatar:t.avatar}}return{nick:"",avatar:""}}},{key:"reset",value:function(){this._flushMap(!0),this.accountProfileMap.clear()}}]),e}(),vv=function e(t){Gn(this,e),Lu||(this.userID=t.userID||"",this.timeStamp=t.timeStamp||0)},Mv=function(){function e(t){Gn(this,e),this._userModule=t,this._className="BlacklistHandler",this._blacklistMap=new Map,this.startIndex=0,this.maxLimited=100,this.currentSequence=0}return Un(e,[{key:"getLocalBlacklist",value:function(){return Qn(this._blacklistMap.keys())}},{key:"getBlacklist",value:function(){var e=this,t="".concat(this._className,".getBlacklist"),n={fromAccount:this._userModule.getMyAccount(),maxLimited:this.maxLimited,startIndex:0,lastSequence:this.currentSequence},o=new og(Ih);return this._userModule.request({protocolName:Sl,requestData:n}).then((function(n){var r=n.data,a=r.blackListItem,s=r.currentSequence,i=Lu(a)?0:a.length;o.setNetworkType(e._userModule.getNetworkType()).setMessage("blackList count:".concat(i)).end(),Bi.info("".concat(t," ok")),e.currentSequence=s,e._handleResponse(a,!0),e._userModule.emitOuterEvent(ao.BLACKLIST_UPDATED,Qn(e._blacklistMap.keys()))})).catch((function(n){return e._userModule.probeNetwork().then((function(e){var t=Xn(e,2),r=t[0],a=t[1];o.setError(n,r,a).end()})),Bi.error("".concat(t," failed. error:"),n),Sm(n)}))}},{key:"addBlacklist",value:function(e){var t=this,n="".concat(this._className,".addBlacklist"),o=new og(Th);if(!zi(e.userIDList))return o.setCode(Ld.ADD_BLACKLIST_INVALID_PARAM).setMessage(Lp).setNetworkType(this._userModule.getNetworkType()).end(),Bi.error("".concat(n," options.userIDList 必需是数组")),Sm({code:Ld.ADD_BLACKLIST_INVALID_PARAM,message:Lp});var r=this._userModule.getMyAccount();return 1===e.userIDList.length&&e.userIDList[0]===r?(o.setCode(Ld.CANNOT_ADD_SELF_TO_BLACKLIST).setMessage(bp).setNetworkType(this._userModule.getNetworkType()).end(),Bi.error("".concat(n," 不能把自己拉黑")),Sm({code:Ld.CANNOT_ADD_SELF_TO_BLACKLIST,message:bp})):(e.userIDList.includes(r)&&(e.userIDList=e.userIDList.filter((function(e){return e!==r})),Bi.warn("".concat(n," 不能把自己拉黑，已过滤"))),e.fromAccount=this._userModule.getMyAccount(),e.toAccount=e.userIDList,this._userModule.request({protocolName:Al,requestData:e}).then((function(r){return o.setNetworkType(t._userModule.getNetworkType()).setMessage(e.userIDList.length>5?"userIDList.length:".concat(e.userIDList.length):"userIDList:".concat(e.userIDList)).end(),Bi.info("".concat(n," ok")),t._handleResponse(r.resultItem,!0),hm(Qn(t._blacklistMap.keys()))})).catch((function(e){return t._userModule.probeNetwork().then((function(t){var n=Xn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)})))}},{key:"_handleResponse",value:function(e,t){if(!Lu(e))for(var n,o,r,a=0,s=e.length;a<s;a++)o=e[a].to,r=e[a].resultCode,(Ji(r)||0===r)&&(t?((n=this._blacklistMap.has(o)?this._blacklistMap.get(o):new vv).userID=o,!Lu(e[a].addBlackTimeStamp)&&(n.timeStamp=e[a].addBlackTimeStamp),this._blacklistMap.set(o,n)):this._blacklistMap.has(o)&&(n=this._blacklistMap.get(o),this._blacklistMap.delete(o)));Bi.log("".concat(this._className,"._handleResponse total:").concat(this._blacklistMap.size," bAdd:").concat(t))}},{key:"deleteBlacklist",value:function(e){var t=this,n="".concat(this._className,".deleteBlacklist"),o=new og(Ch);return zi(e.userIDList)?(e.fromAccount=this._userModule.getMyAccount(),e.toAccount=e.userIDList,this._userModule.request({protocolName:El,requestData:e}).then((function(r){return o.setNetworkType(t._userModule.getNetworkType()).setMessage(e.userIDList.length>5?"userIDList.length:".concat(e.userIDList.length):"userIDList:".concat(e.userIDList)).end(),Bi.info("".concat(n," ok")),t._handleResponse(r.data.resultItem,!1),hm(Qn(t._blacklistMap.keys()))})).catch((function(e){return t._userModule.probeNetwork().then((function(t){var n=Xn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.error("".concat(n," failed. error:"),e),Sm(e)}))):(o.setCode(Ld.DEL_BLACKLIST_INVALID_PARAM).setMessage(Rp).setNetworkType(this._userModule.getNetworkType()).end(),Bi.error("".concat(n," options.userIDList 必需是数组")),Sm({code:Ld.DEL_BLACKLIST_INVALID_PARAM,message:Rp}))}},{key:"onAccountDeleted",value:function(e){for(var t,n=[],o=0,r=e.length;o<r;o++)t=e[o],this._blacklistMap.has(t)&&(this._blacklistMap.delete(t),n.push(t));n.length>0&&(Bi.log("".concat(this._className,".onAccountDeleted count:").concat(n.length," userIDList:"),n),this._userModule.emitOuterEvent(ao.BLACKLIST_UPDATED,Qn(this._blacklistMap.keys())))}},{key:"onAccountAdded",value:function(e){for(var t,n=[],o=0,r=e.length;o<r;o++)t=e[o],this._blacklistMap.has(t)||(this._blacklistMap.set(t,new vv({userID:t})),n.push(t));n.length>0&&(Bi.log("".concat(this._className,".onAccountAdded count:").concat(n.length," userIDList:"),n),this._userModule.emitOuterEvent(ao.BLACKLIST_UPDATED,Qn(this._blacklistMap.keys())))}},{key:"reset",value:function(){this._blacklistMap.clear(),this.startIndex=0,this.maxLimited=100,this.currentSequence=0}}]),e}(),yv=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="UserModule",o._profileHandler=new mv(Yn(o)),o._blacklistHandler=new Mv(Yn(o)),o.getInnerEmitterInstance().on(Gm,o.onContextUpdated,Yn(o)),o}return Un(n,[{key:"onContextUpdated",value:function(e){this._profileHandler.getMyProfile(),this._blacklistHandler.getBlacklist()}},{key:"onProfileModified",value:function(e){this._profileHandler.onProfileModified(e)}},{key:"onRelationChainModified",value:function(e){var t=e.dataList;if(!Lu(t)){var n=[];t.forEach((function(e){e.blackListDelAccount&&n.push.apply(n,Qn(e.blackListDelAccount))})),n.length>0&&this._blacklistHandler.onAccountDeleted(n);var o=[];t.forEach((function(e){e.blackListAddAccount&&o.push.apply(o,Qn(e.blackListAddAccount))})),o.length>0&&this._blacklistHandler.onAccountAdded(o)}}},{key:"onConversationsProfileUpdated",value:function(e){this._profileHandler.onConversationsProfileUpdated(e)}},{key:"getMyAccount",value:function(){return this.getMyUserID()}},{key:"getMyProfile",value:function(){return this._profileHandler.getMyProfile()}},{key:"getStorageModule",value:function(){return this.getModule(Qc)}},{key:"isMyFriend",value:function(e){var t=this.getModule(Yc);return!!t&&t.isMyFriend(e)}},{key:"getUserProfile",value:function(e){return this._profileHandler.getUserProfile(e)}},{key:"updateMyProfile",value:function(e){return this._profileHandler.updateMyProfile(e)}},{key:"getNickAndAvatarByUserID",value:function(e){return this._profileHandler.getNickAndAvatarByUserID(e)}},{key:"getLocalBlacklist",value:function(){var e=this._blacklistHandler.getLocalBlacklist();return Cm(e)}},{key:"addBlacklist",value:function(e){return this._blacklistHandler.addBlacklist(e)}},{key:"deleteBlacklist",value:function(e){return this._blacklistHandler.deleteBlacklist(e)}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._profileHandler.reset(),this._blacklistHandler.reset()}}]),n}(gl),Iv=function(){function e(t,n){Gn(this,e),this._moduleManager=t,this._isLoggedIn=!1,this._SDKAppID=n.SDKAppID,this._userID=n.userID||"",this._userSig=n.userSig||"",this._version="2.16.3",this._a2Key="",this._tinyID="",this._contentType="json",this._unlimitedAVChatRoom=n.unlimitedAVChatRoom,this._scene=n.scene||"",this._oversea=n.oversea,this._instanceID=n.instanceID,this._statusInstanceID=0,this._isDevMode=n.devMode,this._proxyServer=n.proxyServer}return Un(e,[{key:"isLoggedIn",value:function(){return this._isLoggedIn}},{key:"isOversea",value:function(){return this._oversea}},{key:"isDevMode",value:function(){return this._isDevMode}},{key:"isSingaporeSite",value:function(){return this._SDKAppID>=2e7&&this._SDKAppID<3e7}},{key:"isKoreaSite",value:function(){return this._SDKAppID>=3e7&&this._SDKAppID<4e7}},{key:"isGermanySite",value:function(){return this._SDKAppID>=4e7&&this._SDKAppID<5e7}},{key:"isIndiaSite",value:function(){return this._SDKAppID>=5e7&&this._SDKAppID<6e7}},{key:"isUnlimitedAVChatRoom",value:function(){return this._unlimitedAVChatRoom}},{key:"getUserID",value:function(){return this._userID}},{key:"setUserID",value:function(e){this._userID=e}},{key:"setUserSig",value:function(e){this._userSig=e}},{key:"getUserSig",value:function(){return this._userSig}},{key:"getSDKAppID",value:function(){return this._SDKAppID}},{key:"getTinyID",value:function(){return this._tinyID}},{key:"setTinyID",value:function(e){this._tinyID=e,this._isLoggedIn=!0}},{key:"getScene",value:function(){return this._isTUIKit()?"tuikit":this._scene}},{key:"getInstanceID",value:function(){return this._instanceID}},{key:"getStatusInstanceID",value:function(){return this._statusInstanceID}},{key:"setStatusInstanceID",value:function(e){this._statusInstanceID=e}},{key:"getVersion",value:function(){return this._version}},{key:"getA2Key",value:function(){return this._a2Key}},{key:"setA2Key",value:function(e){this._a2Key=e}},{key:"getContentType",value:function(){return this._contentType}},{key:"getProxyServer",value:function(){return this._proxyServer}},{key:"_isTUIKit",value:function(){var e=!1,t=!1,n=!1,o=!1,r=[];ii&&(r=Object.keys(ci)),ui&&(r=Object.keys(window));for(var a=0,s=r.length;a<s;a++)if(r[a].toLowerCase().includes("uikit")){e=!0;break}if(r=null,ii&&Qi(getApp)){var i=getApp().globalData;Yi(i)&&!0===i.isTUIKit&&(t=!0)}!0===this._moduleManager.getModule(Qc).getStorageSync("TIM_".concat(this._SDKAppID,"_isTUIKit"))&&(n=!0);var u=null;if(ti&&"undefined"==typeof uni&&__wxConfig&&(u=__wxConfig.pages),ni&&"undefined"==typeof uni&&__qqConfig&&(u=__qqConfig.pages),zi(u)&&u.length>0){for(var c=0,l=u.length;c<l;c++)if(u[c].toLowerCase().includes("tui")){o=!0;break}u=null}return e||t||n||o}},{key:"reset",value:function(){this._isLoggedIn=!1,this._userSig="",this._a2Key="",this._tinyID="",this._statusInstanceID=0}}]),e}(),Tv=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="SignModule",o._helloInterval=120,o._lastLoginTs=0,o._lastWsHelloTs=0,Fm.mixin(Yn(o)),o}return Un(n,[{key:"onCheckTimer",value:function(e){this.isLoggedIn()&&e%this._helloInterval==0&&this._hello()}},{key:"login",value:function(e){if(this.isLoggedIn()){var t="您已经登录账号".concat(e.userID,"！如需切换账号登录，请先调用 logout 接口登出，再调用 login 接口登录。");return Bi.warn(t),Cm({actionStatus:"OK",errorCode:0,errorInfo:t,repeatLogin:!0})}if(Date.now()-this._lastLoginTs<=15e3)return Bi.warn("您正在尝试登录账号".concat(e.userID,"！请勿重复登录。")),Sm({code:Ld.REPEAT_LOGIN,message:Fd});Bi.log("".concat(this._className,".login userID:").concat(e.userID));var n=this._checkLoginInfo(e);if(0!==n.code)return Sm(n);var o=this.getModule(Xc),r=e.userID,a=e.userSig;return o.setUserID(r),o.setUserSig(a),this.getModule(al).updateProtocolConfig(),this._login()}},{key:"_login",value:function(){var e=this,t=this.getModule(Xc),n=t.getScene(),o=new og(sg);return o.setMessage("".concat(n)).setMoreMessage("identifier:".concat(this.getMyUserID())),this._lastLoginTs=Date.now(),this.request({protocolName:hl}).then((function(r){e._lastLoginTs=0;var a=Date.now(),s=null,i=r.data,u=i.a2Key,c=i.tinyID,l=i.helloInterval,d=i.instanceID,p=i.timeStamp;Bi.log("".concat(e._className,".login ok. scene:").concat(n," helloInterval:").concat(l," instanceID:").concat(d," timeStamp:").concat(p));var g=1e3*p,h=a-o.getStartTs(),f=g+parseInt(h/2)-a,_=o.getStartTs()+f;if(o.start(_),function(e,t){Ui=t;var n=new Date;n.setTime(e),Bi.info("baseTime from server: ".concat(n," offset: ").concat(Ui))}(g,f),!c)throw s=new ym({code:Ld.NO_TINYID,message:Gd}),o.setError(s,!0,e.getNetworkType()).end(),s;if(!u)throw s=new ym({code:Ld.NO_A2KEY,message:Pd}),o.setError(s,!0,e.getNetworkType()).end(),s;return o.setNetworkType(e.getNetworkType()).setMoreMessage("helloInterval:".concat(l," instanceID:").concat(d," offset:").concat(f)).end(),t.setA2Key(u),t.setTinyID(c),t.setStatusInstanceID(d),e.getModule(al).updateProtocolConfig(),e.emitInnerEvent(Gm),e._helloInterval=l,e.triggerReady(),e._fetchCloudControlConfig(),r})).catch((function(t){return e.probeNetwork().then((function(e){var n=Xn(e,2),r=n[0],a=n[1];o.setError(t,r,a).end(!0)})),Bi.error("".concat(e._className,".login failed. error:"),t),e._moduleManager.onLoginFailed(),Sm(t)}))}},{key:"logout",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(!this.isLoggedIn())return Sm({code:Ld.USER_NOT_LOGGED_IN,message:Ud});var n=new og(ig);return n.setNetworkType(this.getNetworkType()).setMessage("identifier:".concat(this.getMyUserID())).end(!0),Bi.info("".concat(this._className,".logout type:").concat(t)),this.request({protocolName:fl,requestData:{type:t}}).then((function(){return e.resetReady(),Cm({})})).catch((function(t){return Bi.error("".concat(e._className,"._logout error:"),t),e.resetReady(),Cm({})}))}},{key:"_fetchCloudControlConfig",value:function(){this.getModule(ul).fetchConfig()}},{key:"_hello",value:function(){var e=this;this._lastWsHelloTs=Date.now(),this.request({protocolName:_l}).catch((function(t){Bi.warn("".concat(e._className,"._hello error:"),t)}))}},{key:"getLastWsHelloTs",value:function(){return this._lastWsHelloTs}},{key:"_checkLoginInfo",value:function(e){var t=0,n="";return Lu(this.getModule(Xc).getSDKAppID())?(t=Ld.NO_SDKAPPID,n=Rd):Lu(e.userID)?(t=Ld.NO_IDENTIFIER,n=bd):Lu(e.userSig)&&(t=Ld.NO_USERSIG,n=wd),{code:t,message:n}}},{key:"onMultipleAccountKickedOut",value:function(e){var t=this;new og(ug).setNetworkType(this.getNetworkType()).setMessage("type:".concat(so.KICKED_OUT_MULT_ACCOUNT," newInstanceInfo:").concat(JSON.stringify(e))).end(!0),Bi.warn("".concat(this._className,".onMultipleAccountKickedOut userID:").concat(this.getMyUserID()," newInstanceInfo:"),e),this.logout(1).then((function(){t.emitOuterEvent(ao.KICKED_OUT,{type:so.KICKED_OUT_MULT_ACCOUNT}),t._moduleManager.reset()}))}},{key:"onMultipleDeviceKickedOut",value:function(e){var t=this;new og(ug).setNetworkType(this.getNetworkType()).setMessage("type:".concat(so.KICKED_OUT_MULT_DEVICE," newInstanceInfo:").concat(JSON.stringify(e))).end(!0),Bi.warn("".concat(this._className,".onMultipleDeviceKickedOut userID:").concat(this.getMyUserID()," newInstanceInfo:"),e),this.logout(1).then((function(){t.emitOuterEvent(ao.KICKED_OUT,{type:so.KICKED_OUT_MULT_DEVICE}),t._moduleManager.reset()}))}},{key:"onUserSigExpired",value:function(){new og(ug).setNetworkType(this.getNetworkType()).setMessage(so.KICKED_OUT_USERSIG_EXPIRED).end(!0),Bi.warn("".concat(this._className,".onUserSigExpired: userSig 签名过期被踢下线")),0!==this.getModule(Xc).getStatusInstanceID()&&(this.emitOuterEvent(ao.KICKED_OUT,{type:so.KICKED_OUT_USERSIG_EXPIRED}),this._moduleManager.reset())}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this.resetReady(),this._helloInterval=120,this._lastLoginTs=0,this._lastWsHelloTs=0}}]),n}(gl);function Cv(){return null}var Sv=function(){function e(t){Gn(this,e),this._moduleManager=t,this._className="StorageModule",this._storageQueue=new Map,this._errorTolerantHandle()}return Un(e,[{key:"_errorTolerantHandle",value:function(){ii||!Ji(window)&&!Ji(window.localStorage)||(this.getItem=Cv,this.setItem=Cv,this.removeItem=Cv,this.clear=Cv)}},{key:"onCheckTimer",value:function(e){if(e%20==0){if(0===this._storageQueue.size)return;this._doFlush()}}},{key:"_doFlush",value:function(){try{var e,t=ro(this._storageQueue);try{for(t.s();!(e=t.n()).done;){var n=Xn(e.value,2),o=n[0],r=n[1];this._setStorageSync(this._getKey(o),r)}}catch(i){t.e(i)}finally{t.f()}this._storageQueue.clear()}catch(eT){Bi.warn("".concat(this._className,"._doFlush error:"),eT)}}},{key:"_getPrefix",value:function(){var e=this._moduleManager.getModule(Xc);return"TIM_".concat(e.getSDKAppID(),"_").concat(e.getUserID(),"_")}},{key:"_getKey",value:function(e){return"".concat(this._getPrefix()).concat(e)}},{key:"getItem",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];try{var n=t?this._getKey(e):e;return this.getStorageSync(n)}catch(eT){return Bi.warn("".concat(this._className,".getItem error:"),eT),{}}}},{key:"setItem",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(n){var r=o?this._getKey(e):e;this._setStorageSync(r,t)}else this._storageQueue.set(e,t)}},{key:"clear",value:function(){try{ii?ci.clearStorageSync():localStorage&&localStorage.clear()}catch(eT){Bi.warn("".concat(this._className,".clear error:"),eT)}}},{key:"removeItem",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];try{var n=t?this._getKey(e):e;this._removeStorageSync(n)}catch(eT){Bi.warn("".concat(this._className,".removeItem error:"),eT)}}},{key:"getSize",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"b";try{var o={size:0,limitSize:5242880,unit:n};if(Object.defineProperty(o,"leftSize",{enumerable:!0,get:function(){return o.limitSize-o.size}}),ii&&(o.limitSize=1024*ci.getStorageInfoSync().limitSize),e)o.size=JSON.stringify(this.getItem(e)).length+this._getKey(e).length;else if(ii){var r=ci.getStorageInfoSync(),a=r.keys;a.forEach((function(e){o.size+=JSON.stringify(t.getStorageSync(e)).length+t._getKey(e).length}))}else if(localStorage)for(var s in localStorage)localStorage.hasOwnProperty(s)&&(o.size+=localStorage.getItem(s).length+s.length);return this._convertUnit(o)}catch(eT){Bi.warn("".concat(this._className," error:"),eT)}}},{key:"_convertUnit",value:function(e){var t={},n=e.unit;for(var o in t.unit=n,e)"number"==typeof e[o]&&("kb"===n.toLowerCase()?t[o]=Math.round(e[o]/1024):"mb"===n.toLowerCase()?t[o]=Math.round(e[o]/1024/1024):t[o]=e[o]);return t}},{key:"_setStorageSync",value:function(e,t){ii?ai?my.setStorageSync({key:e,data:t}):ci.setStorageSync(e,t):localStorage&&localStorage.setItem(e,JSON.stringify(t))}},{key:"getStorageSync",value:function(e){return ii?ai?my.getStorageSync({key:e}).data:ci.getStorageSync(e):localStorage?JSON.parse(localStorage.getItem(e)):{}}},{key:"_removeStorageSync",value:function(e){ii?ai?my.removeStorageSync({key:e}):ci.removeStorageSync(e):localStorage&&localStorage.removeItem(e)}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._doFlush()}}]),e}(),Av=function(){function e(t){Gn(this,e),this._className="SSOLogBody",this._report=[]}return Un(e,[{key:"pushIn",value:function(e){Bi.debug("".concat(this._className,".pushIn"),this._report.length,e),this._report.push(e)}},{key:"backfill",value:function(e){var t;zi(e)&&0!==e.length&&(Bi.debug("".concat(this._className,".backfill"),this._report.length,e.length),(t=this._report).unshift.apply(t,Qn(e)))}},{key:"getLogsNumInMemory",value:function(){return this._report.length}},{key:"isEmpty",value:function(){return 0===this._report.length}},{key:"_reset",value:function(){this._report.length=0,this._report=[]}},{key:"getLogsInMemory",value:function(){var e=this._report.slice();return this._reset(),e}}]),e}(),Ev=function(e){var t=e.getModule(Xc);return{SDKType:10,SDKAppID:t.getSDKAppID(),SDKVersion:t.getVersion(),tinyID:Number(t.getTinyID()),userID:t.getUserID(),platform:e.getPlatform(),instanceID:t.getInstanceID(),traceID:Fi()}},kv=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;Gn(this,n),(o=t.call(this,e))._className="EventStatModule",o.TAG="im-ssolog-event",o._reportBody=new Av,o.MIN_THRESHOLD=20,o.MAX_THRESHOLD=100,o.WAITING_TIME=6e4,o.REPORT_LEVEL=[4,5,6],o.REPORT_SDKAPPID_BLACKLIST=[],o.REPORT_TINYID_WHITELIST=[],o._lastReportTime=Date.now();var r=o.getInnerEmitterInstance();return r.on(Gm,o._onLoginSuccess,Yn(o)),r.on(Pm,o._onCloudConfigUpdated,Yn(o)),o}return Un(n,[{key:"reportAtOnce",value:function(){Bi.debug("".concat(this._className,".reportAtOnce")),this._report()}},{key:"_onLoginSuccess",value:function(){var e=this,t=this.getModule(Qc),n=t.getItem(this.TAG,!1);!Lu(n)&&Qi(n.forEach)&&(Bi.log("".concat(this._className,"._onLoginSuccess get ssolog in storage, count:").concat(n.length)),n.forEach((function(t){e._reportBody.pushIn(t)})),t.removeItem(this.TAG,!1))}},{key:"_onCloudConfigUpdated",value:function(){var e=this.getCloudConfig("evt_rpt_threshold"),t=this.getCloudConfig("evt_rpt_waiting"),n=this.getCloudConfig("evt_rpt_level"),o=this.getCloudConfig("evt_rpt_sdkappid_bl"),r=this.getCloudConfig("evt_rpt_tinyid_wl");Ji(e)||(this.MIN_THRESHOLD=Number(e)),Ji(t)||(this.WAITING_TIME=Number(t)),Ji(n)||(this.REPORT_LEVEL=n.split(",").map((function(e){return Number(e)}))),Ji(o)||(this.REPORT_SDKAPPID_BLACKLIST=o.split(",").map((function(e){return Number(e)}))),Ji(r)||(this.REPORT_TINYID_WHITELIST=r.split(","))}},{key:"pushIn",value:function(e){e instanceof og&&(e.updateTimeStamp(),this._reportBody.pushIn(e),this._reportBody.getLogsNumInMemory()>=this.MIN_THRESHOLD&&this._report())}},{key:"onCheckTimer",value:function(){Date.now()<this._lastReportTime+this.WAITING_TIME||this._reportBody.isEmpty()||this._report()}},{key:"_filterLogs",value:function(e){var t=this,n=this.getModule(Xc),o=n.getSDKAppID(),r=n.getTinyID();return Du(this.REPORT_SDKAPPID_BLACKLIST,o)&&!Nu(this.REPORT_TINYID_WHITELIST,r)?[]:e.filter((function(e){return t.REPORT_LEVEL.includes(e.level)}))}},{key:"_report",value:function(){var e=this;if(!this._reportBody.isEmpty()){var t=this._reportBody.getLogsInMemory(),n=this._filterLogs(t);if(0!==n.length){var o={header:Ev(this),event:n};this.request({protocolName:Md,requestData:xn({},o)}).then((function(){e._lastReportTime=Date.now()})).catch((function(n){Bi.warn("".concat(e._className,".report failed. networkType:").concat(e.getNetworkType()," error:"),n),e._reportBody.backfill(t),e._reportBody.getLogsNumInMemory()>e.MAX_THRESHOLD&&e._flushAtOnce()}))}else this._lastReportTime=Date.now()}}},{key:"_flushAtOnce",value:function(){var e=this.getModule(Qc),t=e.getItem(this.TAG,!1),n=this._reportBody.getLogsInMemory();if(Lu(t))Bi.log("".concat(this._className,"._flushAtOnce count:").concat(n.length)),e.setItem(this.TAG,n,!0,!1);else{var o=n.concat(t);o.length>this.MAX_THRESHOLD&&(o=o.slice(0,this.MAX_THRESHOLD)),Bi.log("".concat(this._className,"._flushAtOnce count:").concat(o.length)),e.setItem(this.TAG,o,!0,!1)}}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._lastReportTime=0,this._report(),this.REPORT_SDKAPPID_BLACKLIST=[],this.REPORT_TINYID_WHITELIST=[]}}]),n}(gl),Dv="none",Nv="online",Ov=function(){function e(t){Gn(this,e),this._moduleManager=t,this._networkType="",this._className="NetMonitorModule",this.MAX_WAIT_TIME=3e3,this._mpNetworkStatusCallback=null,this._webOnlineCallback=null,this._webOfflineCallback=null}return Un(e,[{key:"start",value:function(){var e=this;ii?(ci.getNetworkType({success:function(t){e._networkType=t.networkType,t.networkType===Dv?Bi.warn("".concat(e._className,".start no network, please check!")):Bi.info("".concat(e._className,".start networkType:").concat(t.networkType))}}),this._mpNetworkStatusCallback=this._onNetworkStatusChange.bind(this),ci.onNetworkStatusChange(this._mpNetworkStatusCallback)):(this._networkType=Nv,this._webOnlineCallback=this._onWebOnline.bind(this),this._webOfflineCallback=this._onWebOffline.bind(this),window&&(window.addEventListener("online",this._webOnlineCallback),window.addEventListener("offline",this._webOfflineCallback)))}},{key:"_onWebOnline",value:function(){this._onNetworkStatusChange({isConnected:!0,networkType:Nv})}},{key:"_onWebOffline",value:function(){this._onNetworkStatusChange({isConnected:!1,networkType:Dv})}},{key:"_onNetworkStatusChange",value:function(e){var t=e.isConnected,n=e.networkType,o=!1;t?(Bi.info("".concat(this._className,"._onNetworkStatusChange previousNetworkType:").concat(this._networkType," currentNetworkType:").concat(n)),this._networkType!==n&&(o=!0,this._moduleManager.getModule(sl).reConnect(!0))):this._networkType!==n&&(o=!0,Bi.warn("".concat(this._className,"._onNetworkStatusChange no network, please check!"))),o&&(new og(fg).setMessage("isConnected:".concat(t," previousNetworkType:").concat(this._networkType," networkType:").concat(n)).end(),this._networkType=n)}},{key:"probe",value:function(){var e=this;return new Promise((function(t,n){if(ii)ci.getNetworkType({success:function(n){e._networkType=n.networkType,n.networkType===Dv?(Bi.warn("".concat(e._className,".probe no network, please check!")),t([!1,n.networkType])):(Bi.info("".concat(e._className,".probe networkType:").concat(n.networkType)),t([!0,n.networkType]))}});else if(window&&window.fetch)fetch("".concat(pu(),"//web.sdk.qcloud.com/im/assets/speed.xml?random=").concat(Math.random())).then((function(e){e.ok?t([!0,Nv]):t([!1,Dv])})).catch((function(e){t([!1,Dv])}));else{var o=new XMLHttpRequest,r=setTimeout((function(){Bi.warn("".concat(e._className,".probe fetch timeout. Probably no network, please check!")),o.abort(),e._networkType=Dv,t([!1,Dv])}),e.MAX_WAIT_TIME);o.onreadystatechange=function(){4===o.readyState&&(clearTimeout(r),200===o.status||304===o.status?(this._networkType=Nv,t([!0,Nv])):(Bi.warn("".concat(this.className,".probe fetch status:").concat(o.status,". Probably no network, please check!")),this._networkType=Dv,t([!1,Dv])))},o.open("GET","".concat(pu(),"//web.sdk.qcloud.com/im/assets/speed.xml?random=").concat(Math.random())),o.send()}}))}},{key:"getNetworkType",value:function(){return this._networkType}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),ii?null!==this._mpNetworkStatusCallback&&(ci.offNetworkStatusChange&&(si||oi?ci.offNetworkStatusChange(this._mpNetworkStatusCallback):ci.offNetworkStatusChange()),this._mpNetworkStatusCallback=null):window&&(null!==this._webOnlineCallback&&(window.removeEventListener("online",this._webOnlineCallback),this._webOnlineCallback=null),null!==this._onWebOffline&&(window.removeEventListener("offline",this._webOfflineCallback),this._webOfflineCallback=null))}}]),e}(),Lv=t((function(e){var t=Object.prototype.hasOwnProperty,n="~";function o(){}function r(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function a(e,t,o,a,s){if("function"!=typeof o)throw new TypeError("The listener must be a function");var i=new r(o,a||e,s),u=n?n+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],i]:e._events[u].push(i):(e._events[u]=i,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new o:delete e._events[t]}function i(){this._events=new o,this._eventsCount=0}Object.create&&(o.prototype=Object.create(null),(new o).__proto__||(n=!1)),i.prototype.eventNames=function(){var e,o,r=[];if(0===this._eventsCount)return r;for(o in e=this._events)t.call(e,o)&&r.push(n?o.slice(1):o);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(e)):r},i.prototype.listeners=function(e){var t=n?n+e:e,o=this._events[t];if(!o)return[];if(o.fn)return[o.fn];for(var r=0,a=o.length,s=new Array(a);r<a;r++)s[r]=o[r].fn;return s},i.prototype.listenerCount=function(e){var t=n?n+e:e,o=this._events[t];return o?o.fn?1:o.length:0},i.prototype.emit=function(e,t,o,r,a,s){var i=n?n+e:e;if(!this._events[i])return!1;var u,c,l=this._events[i],d=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),d){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,o),!0;case 4:return l.fn.call(l.context,t,o,r),!0;case 5:return l.fn.call(l.context,t,o,r,a),!0;case 6:return l.fn.call(l.context,t,o,r,a,s),!0}for(c=1,u=new Array(d-1);c<d;c++)u[c-1]=arguments[c];l.fn.apply(l.context,u)}else{var p,g=l.length;for(c=0;c<g;c++)switch(l[c].once&&this.removeListener(e,l[c].fn,void 0,!0),d){case 1:l[c].fn.call(l[c].context);break;case 2:l[c].fn.call(l[c].context,t);break;case 3:l[c].fn.call(l[c].context,t,o);break;case 4:l[c].fn.call(l[c].context,t,o,r);break;default:if(!u)for(p=1,u=new Array(d-1);p<d;p++)u[p-1]=arguments[p];l[c].fn.apply(l[c].context,u)}}return!0},i.prototype.on=function(e,t,n){return a(this,e,t,n,!1)},i.prototype.once=function(e,t,n){return a(this,e,t,n,!0)},i.prototype.removeListener=function(e,t,o,r){var a=n?n+e:e;if(!this._events[a])return this;if(!t)return s(this,a),this;var i=this._events[a];if(i.fn)i.fn!==t||r&&!i.once||o&&i.context!==o||s(this,a);else{for(var u=0,c=[],l=i.length;u<l;u++)(i[u].fn!==t||r&&!i[u].once||o&&i[u].context!==o)&&c.push(i[u]);c.length?this._events[a]=1===c.length?c[0]:c:s(this,a)}return this},i.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&s(this,t)):(this._events=new o,this._eventsCount=0),this},i.prototype.off=i.prototype.removeListener,i.prototype.addListener=i.prototype.on,i.prefixed=n,i.EventEmitter=i,e.exports=i})),Rv=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="BigDataChannelModule",o.FILETYPE={SOUND:2106,FILE:2107,VIDEO:2113},o._bdh_download_server="grouptalk.c2c.qq.com",o._BDHBizID=10001,o._authKey="",o._expireTime=0,o.getInnerEmitterInstance().on(Gm,o._getAuthKey,Yn(o)),o}return Un(n,[{key:"_getAuthKey",value:function(){var e=this;this.request({protocolName:Ml}).then((function(t){t.data.authKey&&(e._authKey=t.data.authKey,e._expireTime=parseInt(t.data.expireTime))}))}},{key:"_isFromOlderVersion",value:function(e){return!(!e.content||2===e.content.downloadFlag)}},{key:"parseElements",value:function(e,t){if(!zi(e)||!t)return[];for(var n=[],o=null,r=0;r<e.length;r++)o=e[r],this._needParse(o)?n.push(this._parseElement(o,t)):n.push(e[r]);return n}},{key:"_needParse",value:function(e){return!e.cloudCustomData&&!(!this._isFromOlderVersion(e)||e.type!==so.MSG_AUDIO&&e.type!==so.MSG_FILE&&e.type!==so.MSG_VIDEO)}},{key:"_parseElement",value:function(e,t){switch(e.type){case so.MSG_AUDIO:return this._parseAudioElement(e,t);case so.MSG_FILE:return this._parseFileElement(e,t);case so.MSG_VIDEO:return this._parseVideoElement(e,t)}}},{key:"_parseAudioElement",value:function(e,t){return e.content.url=this._genAudioUrl(e.content.uuid,t),e}},{key:"_parseFileElement",value:function(e,t){return e.content.url=this._genFileUrl(e.content.uuid,t,e.content.fileName),e}},{key:"_parseVideoElement",value:function(e,t){return e.content.url=this._genVideoUrl(e.content.uuid,t),e}},{key:"_genAudioUrl",value:function(e,t){if(""===this._authKey)return Bi.warn("".concat(this._className,"._genAudioUrl no authKey!")),"";var n=this.getModule(Xc).getSDKAppID();return"https://".concat(this._bdh_download_server,"/asn.com/stddownload_common_file?authkey=").concat(this._authKey,"&bid=").concat(this._BDHBizID,"&subbid=").concat(n,"&fileid=").concat(e,"&filetype=").concat(this.FILETYPE.SOUND,"&openid=").concat(t,"&ver=0")}},{key:"_genFileUrl",value:function(e,t,n){if(""===this._authKey)return Bi.warn("".concat(this._className,"._genFileUrl no authKey!")),"";n||(n="".concat(Math.floor(1e5*Math.random()),"-").concat(Date.now()));var o=this.getModule(Xc).getSDKAppID();return"https://".concat(this._bdh_download_server,"/asn.com/stddownload_common_file?authkey=").concat(this._authKey,"&bid=").concat(this._BDHBizID,"&subbid=").concat(o,"&fileid=").concat(e,"&filetype=").concat(this.FILETYPE.FILE,"&openid=").concat(t,"&ver=0&filename=").concat(encodeURIComponent(n))}},{key:"_genVideoUrl",value:function(e,t){if(""===this._authKey)return Bi.warn("".concat(this._className,"._genVideoUrl no authKey!")),"";var n=this.getModule(Xc).getSDKAppID();return"https://".concat(this._bdh_download_server,"/asn.com/stddownload_common_file?authkey=").concat(this._authKey,"&bid=").concat(this._BDHBizID,"&subbid=").concat(n,"&fileid=").concat(e,"&filetype=").concat(this.FILETYPE.VIDEO,"&openid=").concat(t,"&ver=0")}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._authKey="",this.expireTime=0}}]),n}(gl),bv=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="UploadModule",o.TIMUploadPlugin=null,o.timUploadPlugin=null,o.COSSDK=null,o._cosUploadMethod=null,o.expiredTimeLimit=600,o.appid=0,o.bucketName="",o.ciUrl="",o.directory="",o.downloadUrl="",o.uploadUrl="",o.region="ap-shanghai",o.cos=null,o.cosOptions={secretId:"",secretKey:"",sessionToken:"",expiredTime:0},o.uploadFileType="",o.duration=900,o.tryCount=0,o.getInnerEmitterInstance().on(Gm,o._init,Yn(o)),o}return Un(n,[{key:"_init",value:function(){var e="".concat(this._className,"._init"),t=this.getModule(ol);if(this.TIMUploadPlugin=t.getPlugin("tim-upload-plugin"),this.TIMUploadPlugin)this._initUploaderMethod();else{var n=ii?"cos-wx-sdk":"cos-js-sdk";this.COSSDK=t.getPlugin(n),this.COSSDK?(this._getAuthorizationKey(),Bi.warn("".concat(e," v2.9.2起推荐使用 tim-upload-plugin 代替 ").concat(n,"，上传更快更安全。详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#registerPlugin"))):Bi.warn("".concat(e," 没有检测到上传插件，将无法发送图片、音频、视频、文件等类型的消息。详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#registerPlugin"))}}},{key:"_getAuthorizationKey",value:function(){var e=this,t=new og(_g),n=Math.ceil(Date.now()/1e3);this.request({protocolName:md,requestData:{duration:this.expiredTimeLimit}}).then((function(o){var r=o.data;Bi.log("".concat(e._className,"._getAuthorizationKey ok. data:"),r);var a=r.expiredTime-n;t.setMessage("requestId:".concat(r.requestId," requestTime:").concat(n," expiredTime:").concat(r.expiredTime," diff:").concat(a,"s")).setNetworkType(e.getNetworkType()).end(),!ii&&r.region&&(e.region=r.region),e.appid=r.appid,e.bucketName=r.bucketName,e.ciUrl=r.ciUrl,e.directory=r.directory,e.downloadUrl=r.downloadUrl,e.uploadUrl=r.uploadUrl,e.cosOptions={secretId:r.secretId,secretKey:r.secretKey,sessionToken:r.sessionToken,expiredTime:r.expiredTime},Bi.log("".concat(e._className,"._getAuthorizationKey ok. region:").concat(e.region," bucketName:").concat(e.bucketName)),e._initUploaderMethod()})).catch((function(n){e.probeNetwork().then((function(e){var o=Xn(e,2),r=o[0],a=o[1];t.setError(n,r,a).end()})),Bi.warn("".concat(e._className,"._getAuthorizationKey failed. error:"),n)}))}},{key:"_getCosPreSigUrl",value:function(e){var t=this,n="".concat(this._className,"._getCosPreSigUrl"),o=Math.ceil(Date.now()/1e3),r=new og(mg);return this.request({protocolName:vd,requestData:{fileType:e.fileType,fileName:e.fileName,uploadMethod:e.uploadMethod,duration:e.duration}}).then((function(e){t.tryCount=0;var a=e.data||{},s=a.expiredTime-o;return Bi.log("".concat(n," ok. data:"),a),r.setMessage("requestId:".concat(a.requestId," expiredTime:").concat(a.expiredTime," diff:").concat(s,"s")).setNetworkType(t.getNetworkType()).end(),a})).catch((function(o){return-1===o.code&&(o.code=Ld.COS_GET_SIG_FAIL),t.probeNetwork().then((function(e){var t=Xn(e,2),n=t[0],a=t[1];r.setError(o,n,a).end()})),Bi.warn("".concat(n," failed. error:"),o),t.tryCount<1?(t.tryCount++,t._getCosPreSigUrl(e)):(t.tryCount=0,Sm({code:Ld.COS_GET_SIG_FAIL,message:xd}))}))}},{key:"_initUploaderMethod",value:function(){var e=this;if(this.TIMUploadPlugin)return this.timUploadPlugin=new this.TIMUploadPlugin,void(this._cosUploadMethod=function(t,n){e.timUploadPlugin.uploadFile(t,n)});this.appid&&(this.cos=ii?new this.COSSDK({ForcePathStyle:!0,getAuthorization:this._getAuthorization.bind(this)}):new this.COSSDK({getAuthorization:this._getAuthorization.bind(this)}),this._cosUploadMethod=ii?function(t,n){e.cos.postObject(t,n)}:function(t,n){e.cos.uploadFiles(t,n)})}},{key:"onCheckTimer",value:function(e){this.COSSDK&&(this.TIMUploadPlugin||this.isLoggedIn()&&e%60==0&&Math.ceil(Date.now()/1e3)>=this.cosOptions.expiredTime-120&&this._getAuthorizationKey())}},{key:"_getAuthorization",value:function(e,t){t({TmpSecretId:this.cosOptions.secretId,TmpSecretKey:this.cosOptions.secretKey,XCosSecurityToken:this.cosOptions.sessionToken,ExpiredTime:this.cosOptions.expiredTime})}},{key:"upload",value:function(e){if(!0===e.getRelayFlag())return Promise.resolve();var t=this.getModule(dl);switch(e.type){case so.MSG_IMAGE:return t.addTotalCount(zp),this._uploadImage(e);case so.MSG_FILE:return t.addTotalCount(zp),this._uploadFile(e);case so.MSG_AUDIO:return t.addTotalCount(zp),this._uploadAudio(e);case so.MSG_VIDEO:return t.addTotalCount(zp),this._uploadVideo(e);default:return Promise.resolve()}}},{key:"_uploadImage",value:function(e){var t=this.getModule(Hc),n=e.getElements()[0],o=t.getMessageOptionByID(e.ID);return this.doUploadImage({file:o.payload.file,to:o.to,onProgress:function(e){if(n.updatePercent(e),Qi(o.onProgress))try{o.onProgress(e)}catch(t){return Sm({code:Ld.MESSAGE_ONPROGRESS_FUNCTION_ERROR,message:jd})}}}).then((function(t){var o=t.location,r=t.fileType,a=t.fileSize,s=t.width,i=t.height,u=gu(o);n.updateImageFormat(r);var c=Su({originUrl:u,originWidth:s,originHeight:i,min:198}),l=Su({originUrl:u,originWidth:s,originHeight:i,min:720});return n.updateImageInfoArray([{size:a,url:u,width:s,height:i},xn({},l),xn({},c)]),e}))}},{key:"_uploadFile",value:function(e){var t=this.getModule(Hc),n=e.getElements()[0],o=t.getMessageOptionByID(e.ID);return this.doUploadFile({file:o.payload.file,to:o.to,onProgress:function(e){if(n.updatePercent(e),Qi(o.onProgress))try{o.onProgress(e)}catch(t){return Sm({code:Ld.MESSAGE_ONPROGRESS_FUNCTION_ERROR,message:jd})}}}).then((function(t){var o=t.location,r=gu(o);return n.updateFileUrl(r),e}))}},{key:"_uploadAudio",value:function(e){var t=this.getModule(Hc),n=e.getElements()[0],o=t.getMessageOptionByID(e.ID);return this.doUploadAudio({file:o.payload.file,to:o.to,onProgress:function(e){if(n.updatePercent(e),Qi(o.onProgress))try{o.onProgress(e)}catch(t){return Sm({code:Ld.MESSAGE_ONPROGRESS_FUNCTION_ERROR,message:jd})}}}).then((function(t){var o=t.location,r=gu(o);return n.updateAudioUrl(r),e}))}},{key:"_uploadVideo",value:function(e){var t=this.getModule(Hc),n=e.getElements()[0],o=t.getMessageOptionByID(e.ID);return this.doUploadVideo({file:o.payload.file,to:o.to,onProgress:function(e){if(n.updatePercent(e),Qi(o.onProgress))try{o.onProgress(e)}catch(t){return Sm({code:Ld.MESSAGE_ONPROGRESS_FUNCTION_ERROR,message:jd})}}}).then((function(t){var o=gu(t.location);return n.updateVideoUrl(o),e}))}},{key:"doUploadImage",value:function(e){if(!e.file)return Sm({code:Ld.MESSAGE_IMAGE_SELECT_FILE_FIRST,message:zd});var t=this._checkImageType(e.file);if(!0!==t)return t;var n=this._checkImageSize(e.file);if(!0!==n)return n;var o=null;return this._setUploadFileType(Vm),this.uploadByCOS(e).then((function(e){return o=e,t="https://".concat(e.location),ii?new Promise((function(e,n){ci.getImageInfo({src:t,success:function(t){e({width:t.width,height:t.height})},fail:function(){e({width:0,height:0})}})})):Mi&&9===yi?Promise.resolve({width:0,height:0}):new Promise((function(e,n){var o=new Image;o.onload=function(){e({width:this.width,height:this.height}),o=null},o.onerror=function(){e({width:0,height:0}),o=null},o.src=t}));var t})).then((function(e){return o.width=e.width,o.height=e.height,Promise.resolve(o)}))}},{key:"_checkImageType",value:function(e){var t="";return t=ii?e.url.slice(e.url.lastIndexOf(".")+1):e.files[0].name.slice(e.files[0].name.lastIndexOf(".")+1),qm.indexOf(t.toLowerCase())>=0||Sm({code:Ld.MESSAGE_IMAGE_TYPES_LIMIT,message:Jd})}},{key:"_checkImageSize",value:function(e){var t=0;return 0===(t=ii?e.size:e.files[0].size)?Sm({code:Ld.MESSAGE_FILE_IS_EMPTY,message:"".concat(Hd)}):t<20971520||Sm({code:Ld.MESSAGE_IMAGE_SIZE_LIMIT,message:"".concat(Xd)})}},{key:"doUploadFile",value:function(e){var t=null;return e.file?e.file.files[0].size>104857600?Sm(t={code:Ld.MESSAGE_FILE_SIZE_LIMIT,message:ap}):0===e.file.files[0].size?(t={code:Ld.MESSAGE_FILE_IS_EMPTY,message:"".concat(Hd)},Sm(t)):(this._setUploadFileType(Hm),this.uploadByCOS(e)):Sm(t={code:Ld.MESSAGE_FILE_SELECT_FILE_FIRST,message:rp})}},{key:"doUploadVideo",value:function(e){return e.file.videoFile.size>104857600?Sm({code:Ld.MESSAGE_VIDEO_SIZE_LIMIT,message:"".concat(tp)}):0===e.file.videoFile.size?Sm({code:Ld.MESSAGE_FILE_IS_EMPTY,message:"".concat(Hd)}):-1===xm.indexOf(e.file.videoFile.type)?Sm({code:Ld.MESSAGE_VIDEO_TYPES_LIMIT,message:"".concat(np)}):(this._setUploadFileType(Km),ii?this.handleVideoUpload({file:e.file.videoFile,onProgress:e.onProgress}):ui?this.handleVideoUpload(e):void 0)}},{key:"handleVideoUpload",value:function(e){var t=this;return new Promise((function(n,o){t.uploadByCOS(e).then((function(e){n(e)})).catch((function(){t.uploadByCOS(e).then((function(e){n(e)})).catch((function(){o(new ym({code:Ld.MESSAGE_VIDEO_UPLOAD_FAIL,message:ep}))}))}))}))}},{key:"doUploadAudio",value:function(e){return e.file?e.file.size>20971520?Sm(new ym({code:Ld.MESSAGE_AUDIO_SIZE_LIMIT,message:"".concat(Zd)})):0===e.file.size?Sm(new ym({code:Ld.MESSAGE_FILE_IS_EMPTY,message:"".concat(Hd)})):(this._setUploadFileType(Bm),this.uploadByCOS(e)):Sm(new ym({code:Ld.MESSAGE_AUDIO_UPLOAD_FAIL,message:Qd}))}},{key:"uploadByCOS",value:function(e){var t=this,n="".concat(this._className,".uploadByCOS");if(!Qi(this._cosUploadMethod))return Bi.warn("".concat(n," 没有检测到上传插件，将无法发送图片、音频、视频、文件等类型的消息。详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#registerPlugin")),Sm({code:Ld.COS_UNDETECTED,message:qd});if(this.timUploadPlugin)return this._uploadWithPreSigUrl(e);var o=new og(vg),r=Date.now(),a=this._getFile(e);return new Promise((function(s,i){var u=ii?t._createCosOptionsWXMiniApp(e):t._createCosOptionsWeb(e),c=t;t._cosUploadMethod(u,(function(e,u){var l=Object.create(null);if(u){if(e||zi(u.files)&&u.files[0].error){var d=new ym({code:Ld.MESSAGE_FILE_UPLOAD_FAIL,message:op});return o.setError(d,!0,t.getNetworkType()).end(),Bi.log("".concat(n," failed. error:"),u.files[0].error),403===u.files[0].error.statusCode&&(Bi.warn("".concat(n," failed. cos AccessKeyId was invalid, regain auth key!")),t._getAuthorizationKey()),void i(d)}l.fileName=a.name,l.fileSize=a.size,l.fileType=a.type.slice(a.type.indexOf("/")+1).toLowerCase(),l.location=ii?u.Location:u.files[0].data.Location;var p=Date.now()-r,g=c._formatFileSize(a.size),h=c._formatSpeed(1e3*a.size/p),f="size:".concat(g," time:").concat(p,"ms speed:").concat(h);Bi.log("".concat(n," success. name:").concat(a.name," ").concat(f)),s(l);var _=t.getModule(dl);return _.addCost(zp,p),_.addFileSize(zp,a.size),void o.setNetworkType(t.getNetworkType()).setMessage(f).end()}var m=new ym({code:Ld.MESSAGE_FILE_UPLOAD_FAIL,message:op});o.setError(m,!0,c.getNetworkType()).end(),Bi.warn("".concat(n," failed. error:"),e),403===e.statusCode&&(Bi.warn("".concat(n," failed. cos AccessKeyId was invalid, regain auth key!")),t._getAuthorizationKey()),i(m)}))}))}},{key:"_uploadWithPreSigUrl",value:function(e){var t=this,n="".concat(this._className,"._uploadWithPreSigUrl"),o=this._getFile(e);return this._createCosOptionsPreSigUrl(e).then((function(e){return new Promise((function(r,a){var s=new og(vg),i=Date.now();t._cosUploadMethod(e,(function(e,u){var c=Object.create(null);if(e||403===u.statusCode){var l=new ym({code:Ld.MESSAGE_FILE_UPLOAD_FAIL,message:op});return s.setError(l,!0,t.getNetworkType()).end(),Bi.log("".concat(n," failed, error:"),e),void a(l)}var d=u.data.location||"";0!==d.indexOf("https://")&&0!==d.indexOf("http://")||(d=d.split("//")[1]),c.fileName=o.name,c.fileSize=o.size,c.fileType=o.type.slice(o.type.indexOf("/")+1).toLowerCase(),c.location=d;var p=Date.now()-i,g=t._formatFileSize(o.size),h=t._formatSpeed(1e3*o.size/p),f="size:".concat(g,",time:").concat(p,"ms,speed:").concat(h," res:").concat(JSON.stringify(u.data));Bi.log("".concat(n," success name:").concat(o.name,",").concat(f)),s.setNetworkType(t.getNetworkType()).setMessage(f).end();var _=t.getModule(dl);_.addCost(zp,p),_.addFileSize(zp,o.size),r(c)}))}))}))}},{key:"_getFile",value:function(e){var t=null;return ii?t=si&&zi(e.file.files)?e.file.files[0]:e.file:ui&&(t=e.file.files[0]),t}},{key:"_formatFileSize",value:function(e){return e<1024?e+"B":e<1048576?Math.floor(e/1024)+"KB":Math.floor(e/1048576)+"MB"}},{key:"_formatSpeed",value:function(e){return e<=1048576?ku(e/1024,1)+"KB/s":ku(e/1048576,1)+"MB/s"}},{key:"_createCosOptionsWeb",value:function(e){var t=e.file.files[0].name,n=t.slice(t.lastIndexOf(".")),o=this._genFileName("".concat(iu(999999)).concat(n));return{files:[{Bucket:"".concat(this.bucketName,"-").concat(this.appid),Region:this.region,Key:"".concat(this.directory,"/").concat(o),Body:e.file.files[0]}],SliceSize:1048576,onProgress:function(t){if("function"==typeof e.onProgress)try{e.onProgress(t.percent)}catch(n){Bi.warn("onProgress callback error:",n)}},onFileFinish:function(e,t,n){}}}},{key:"_createCosOptionsWXMiniApp",value:function(e){var t=this._getFile(e),n=this._genFileName(t.name),o=t.url;return{Bucket:"".concat(this.bucketName,"-").concat(this.appid),Region:this.region,Key:"".concat(this.directory,"/").concat(n),FilePath:o,onProgress:function(t){if(Bi.log(JSON.stringify(t)),"function"==typeof e.onProgress)try{e.onProgress(t.percent)}catch(n){Bi.warn("onProgress callback error:",n)}}}}},{key:"_createCosOptionsPreSigUrl",value:function(e){var t=this,n="",o="",r=0;if(ii){var a=this._getFile(e);n=this._genFileName(a.name),o=a.url,r=1}else{var s=e.file.files[0].name,i=s.slice(s.lastIndexOf("."));n=this._genFileName("".concat(iu(999999)).concat(i)),o=e.file.files[0],r=0}return this._getCosPreSigUrl({fileType:this.uploadFileType,fileName:n,uploadMethod:r,duration:this.duration}).then((function(r){var a=r.uploadUrl,s=r.downloadUrl;return{url:a,fileType:t.uploadFileType,fileName:n,resources:o,downloadUrl:s,onProgress:function(t){if("function"==typeof e.onProgress)try{e.onProgress(t.percent)}catch(n){Bi.warn("onProgress callback error:",n),Bi.error(n)}}}}))}},{key:"_genFileName",value:function(e){return"".concat(Tu(),"-").concat(e)}},{key:"_setUploadFileType",value:function(e){this.uploadFileType=e}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset"))}}]),n}(gl),wv=function(){function e(t){Gn(this,e),this._className="MergerMessageHandler",this._messageModule=t}return Un(e,[{key:"uploadMergerMessage",value:function(e,t){var n=this;Bi.debug("".concat(this._className,".uploadMergerMessage message:"),e,"messageBytes:".concat(t));var o=e.payload.messageList,r=o.length,a=new og(Dg);return this._messageModule.request({protocolName:Sd,requestData:{messageList:o}}).then((function(e){Bi.debug("".concat(n._className,".uploadMergerMessage ok. response:"),e.data);var o=e.data,s=o.pbDownloadKey,i=o.downloadKey,u={pbDownloadKey:s,downloadKey:i,messageNumber:r};return a.setNetworkType(n._messageModule.getNetworkType()).setMessage("".concat(r,"-").concat(t,"-").concat(i)).end(),u})).catch((function(e){throw Bi.warn("".concat(n._className,".uploadMergerMessage failed. error:"),e),n._messageModule.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),e}))}},{key:"downloadMergerMessage",value:function(e){var t=this;Bi.debug("".concat(this._className,".downloadMergerMessage message:"),e);var n=e.payload.downloadKey,o=new og(Ng);return o.setMessage("downloadKey:".concat(n)),this._messageModule.request({protocolName:Ad,requestData:{downloadKey:n}}).then((function(n){if(Bi.debug("".concat(t._className,".downloadMergerMessage ok. response:"),n.data),Qi(e.clearElement)){var r=e.payload,a=(r.downloadKey,r.pbDownloadKey,r.messageList,$n(r,["downloadKey","pbDownloadKey","messageList"]));e.clearElement(),e.setElement({type:e.type,content:xn({messageList:n.data.messageList},a)})}else{var s=[];n.data.messageList.forEach((function(e){if(!Lu(e)){var t=new lm(e);s.push(t)}})),e.payload.messageList=s,e.payload.downloadKey="",e.payload.pbDownloadKey=""}return o.setNetworkType(t._messageModule.getNetworkType()).end(),e})).catch((function(e){throw Bi.warn("".concat(t._className,".downloadMergerMessage failed. key:").concat(n," error:"),e),t._messageModule.probeNetwork().then((function(t){var n=Xn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),e}))}},{key:"createMergerMessagePack",value:function(e,t,n){return e.conversationType===so.CONV_C2C?this._createC2CMergerMessagePack(e,t,n):this._createGroupMergerMessagePack(e,t,n)}},{key:"_createC2CMergerMessagePack",value:function(e,t,n){var o=null;t&&(t.offlinePushInfo&&(o=t.offlinePushInfo),!0===t.onlineUserOnly&&(o?o.disablePush=!0:o={disablePush:!0}));var r="";Wi(e.cloudCustomData)&&e.cloudCustomData.length>0&&(r=e.cloudCustomData);var a=n.pbDownloadKey,s=n.downloadKey,i=n.messageNumber,u=e.payload,c=u.title,l=u.abstractList,d=u.compatibleText,p=this._messageModule.getModule(Wc);return{protocolName:yl,tjgID:this._messageModule.generateTjgID(e),requestData:{fromAccount:this._messageModule.getMyUserID(),toAccount:e.to,msgBody:[{msgType:e.type,msgContent:{pbDownloadKey:a,downloadKey:s,title:c,abstractList:l,compatibleText:d,messageNumber:i}}],cloudCustomData:r,msgSeq:e.sequence,msgRandom:e.random,msgLifeTime:p&&p.isOnlineMessage(e,t)?0:void 0,offlinePushInfo:o?{pushFlag:!0===o.disablePush?1:0,title:o.title||"",desc:o.description||"",ext:o.extension||"",apnsInfo:{badgeMode:!0===o.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:o.androidOPPOChannelID||""}}:void 0}}}},{key:"_createGroupMergerMessagePack",value:function(e,t,n){var o=null;t&&t.offlinePushInfo&&(o=t.offlinePushInfo);var r="";Wi(e.cloudCustomData)&&e.cloudCustomData.length>0&&(r=e.cloudCustomData);var a=n.pbDownloadKey,s=n.downloadKey,i=n.messageNumber,u=e.payload,c=u.title,l=u.abstractList,d=u.compatibleText,p=this._messageModule.getModule($c);return{protocolName:Il,tjgID:this._messageModule.generateTjgID(e),requestData:{fromAccount:this._messageModule.getMyUserID(),groupID:e.to,msgBody:[{msgType:e.type,msgContent:{pbDownloadKey:a,downloadKey:s,title:c,abstractList:l,compatibleText:d,messageNumber:i}}],random:e.random,priority:e.priority,clientSequence:e.clientSequence,groupAtInfo:void 0,cloudCustomData:r,onlineOnlyFlag:p&&p.isOnlineMessage(e,t)?1:0,offlinePushInfo:o?{pushFlag:!0===o.disablePush?1:0,title:o.title||"",desc:o.description||"",ext:o.extension||"",apnsInfo:{badgeMode:!0===o.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:o.androidOPPOChannelID||""}}:void 0}}}}]),e}(),Gv={ERR_SVR_COMM_SENSITIVE_TEXT:80001,ERR_SVR_COMM_BODY_SIZE_LIMIT:80002,OPEN_SERVICE_OVERLOAD_ERROR:60022,ERR_SVR_MSG_PKG_PARSE_FAILED:20001,ERR_SVR_MSG_INTERNAL_AUTH_FAILED:20002,ERR_SVR_MSG_INVALID_ID:20003,ERR_SVR_MSG_PUSH_DENY:20006,ERR_SVR_MSG_IN_PEER_BLACKLIST:20007,ERR_SVR_MSG_BOTH_NOT_FRIEND:20009,ERR_SVR_MSG_NOT_PEER_FRIEND:20010,ERR_SVR_MSG_NOT_SELF_FRIEND:20011,ERR_SVR_MSG_SHUTUP_DENY:20012,ERR_SVR_GROUP_INVALID_PARAMETERS:10004,ERR_SVR_GROUP_PERMISSION_DENY:10007,ERR_SVR_GROUP_NOT_FOUND:10010,ERR_SVR_GROUP_INVALID_GROUPID:10015,ERR_SVR_GROUP_REJECT_FROM_THIRDPARTY:10016,ERR_SVR_GROUP_SHUTUP_DENY:10017,MESSAGE_SEND_FAIL:2100,OVER_FREQUENCY_LIMIT:2996},Pv=[Ld.MESSAGE_ONPROGRESS_FUNCTION_ERROR,Ld.MESSAGE_IMAGE_SELECT_FILE_FIRST,Ld.MESSAGE_IMAGE_TYPES_LIMIT,Ld.MESSAGE_FILE_IS_EMPTY,Ld.MESSAGE_IMAGE_SIZE_LIMIT,Ld.MESSAGE_FILE_SELECT_FILE_FIRST,Ld.MESSAGE_FILE_SIZE_LIMIT,Ld.MESSAGE_VIDEO_SIZE_LIMIT,Ld.MESSAGE_VIDEO_TYPES_LIMIT,Ld.MESSAGE_AUDIO_UPLOAD_FAIL,Ld.MESSAGE_AUDIO_SIZE_LIMIT,Ld.COS_UNDETECTED],Uv=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="MessageModule",o._messageOptionsMap=new Map,o._mergerMessageHandler=new wv(Yn(o)),o}return Un(n,[{key:"createTextMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new gm(e),o="string"==typeof e.payload?e.payload:e.payload.text,r=new wh({text:o}),a=this._getNickAndAvatarByUserID(t);return n.setElement(r),n.setNickAndAvatar(a),n.setNameCard(this._getNameCardByGroupID(n)),n}},{key:"createImageMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new gm(e);if(ii){var o=e.payload.file;if(Hi(o))return void Bi.warn("小程序环境下调用 createImageMessage 接口时，payload.file 不支持传入 File 对象");var r=o.tempFilePaths[0],a={url:r,name:r.slice(r.lastIndexOf("/")+1),size:o.tempFiles&&o.tempFiles[0].size||1,type:r.slice(r.lastIndexOf(".")+1).toLowerCase()};e.payload.file=a}else if(ui)if(Hi(e.payload.file)){var s=e.payload.file;e.payload.file={files:[s]}}else if(Yi(e.payload.file)&&"undefined"!=typeof uni){var i=e.payload.file.tempFiles[0];e.payload.file={files:[i]}}var u=new $_({imageFormat:x_.IMAGE_FORMAT.UNKNOWN,uuid:this._generateUUID(),file:e.payload.file}),c=this._getNickAndAvatarByUserID(t);return n.setElement(u),n.setNickAndAvatar(c),n.setNameCard(this._getNameCardByGroupID(n)),this._messageOptionsMap.set(n.ID,e),n}},{key:"createAudioMessage",value:function(e){if(ii){var t=e.payload.file;if(ii){var n={url:t.tempFilePath,name:t.tempFilePath.slice(t.tempFilePath.lastIndexOf("/")+1),size:t.fileSize,second:parseInt(t.duration)/1e3,type:t.tempFilePath.slice(t.tempFilePath.lastIndexOf(".")+1).toLowerCase()};e.payload.file=n}var o=this.getMyUserID();e.currentUser=o;var r=new gm(e),a=new z_({second:Math.floor(t.duration/1e3),size:t.fileSize,url:t.tempFilePath,uuid:this._generateUUID()}),s=this._getNickAndAvatarByUserID(o);return r.setElement(a),r.setNickAndAvatar(s),r.setNameCard(this._getNameCardByGroupID(r)),this._messageOptionsMap.set(r.ID,e),r}Bi.warn("createAudioMessage 目前只支持小程序环境下发语音消息")}},{key:"createVideoMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t,e.payload.file.thumbUrl="https://web.sdk.qcloud.com/im/assets/images/transparent.png",e.payload.file.thumbSize=1668;var n={};if(ii){if(ai)return void Bi.warn("createVideoMessage 不支持在支付宝小程序环境下使用");if(Hi(e.payload.file))return void Bi.warn("小程序环境下调用 createVideoMessage 接口时，payload.file 不支持传入 File 对象");var o=e.payload.file;n.url=o.tempFilePath,n.name=o.tempFilePath.slice(o.tempFilePath.lastIndexOf("/")+1),n.size=o.size,n.second=o.duration,n.type=o.tempFilePath.slice(o.tempFilePath.lastIndexOf(".")+1).toLowerCase()}else if(ui){if(Hi(e.payload.file)){var r=e.payload.file;e.payload.file.files=[r]}else if(Yi(e.payload.file)&&"undefined"!=typeof uni){var a=e.payload.file.tempFile;e.payload.file.files=[a]}var s=e.payload.file;n.url=window.URL.createObjectURL(s.files[0]),n.name=s.files[0].name,n.size=s.files[0].size,n.second=s.files[0].duration||0,n.type=s.files[0].type.split("/")[1]}e.payload.file.videoFile=n;var i=new gm(e),u=new um({videoFormat:n.type,videoSecond:ku(n.second,0),videoSize:n.size,remoteVideoUrl:"",videoUrl:n.url,videoUUID:this._generateUUID(),thumbUUID:this._generateUUID(),thumbWidth:e.payload.file.width||200,thumbHeight:e.payload.file.height||200,thumbUrl:e.payload.file.thumbUrl,thumbSize:e.payload.file.thumbSize,thumbFormat:e.payload.file.thumbUrl.slice(e.payload.file.thumbUrl.lastIndexOf(".")+1).toLowerCase()}),c=this._getNickAndAvatarByUserID(t);return i.setElement(u),i.setNickAndAvatar(c),i.setNameCard(this._getNameCardByGroupID(i)),this._messageOptionsMap.set(i.ID,e),i}},{key:"createCustomMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new gm(e),o=new im({data:e.payload.data,description:e.payload.description,extension:e.payload.extension}),r=this._getNickAndAvatarByUserID(t);return n.setElement(o),n.setNickAndAvatar(r),n.setNameCard(this._getNameCardByGroupID(n)),n}},{key:"createFaceMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new gm(e),o=new Y_(e.payload),r=this._getNickAndAvatarByUserID(t);return n.setElement(o),n.setNickAndAvatar(r),n.setNameCard(this._getNameCardByGroupID(n)),n}},{key:"createMergerMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=this._getNickAndAvatarByUserID(t),o=new gm(e),r=new dm(e.payload);return o.setElement(r),o.setNickAndAvatar(n),o.setNameCard(this._getNameCardByGroupID(o)),o.setRelayFlag(!0),o}},{key:"createForwardMessage",value:function(e){var t=e.to,n=e.conversationType,o=e.priority,r=e.payload,a=this.getMyUserID(),s=this._getNickAndAvatarByUserID(a);if(r.type===so.MSG_GRP_TIP)return Sm(new ym({code:Ld.MESSAGE_FORWARD_TYPE_INVALID,message:lp}));var i={to:t,conversationType:n,conversationID:"".concat(n).concat(t),priority:o,isPlaceMessage:0,status:Ac.UNSEND,currentUser:a,cloudCustomData:e.cloudCustomData||r.cloudCustomData||""},u=new gm(i);return u.setElement(r.getElements()[0]),u.setNickAndAvatar(s),u.setNameCard(this._getNameCardByGroupID(r)),u.setRelayFlag(!0),u}},{key:"downloadMergerMessage",value:function(e){return this._mergerMessageHandler.downloadMergerMessage(e)}},{key:"createFileMessage",value:function(e){if(!ii||si){if(ui||si)if(Hi(e.payload.file)){var t=e.payload.file;e.payload.file={files:[t]}}else if(Yi(e.payload.file)&&"undefined"!=typeof uni){var n=e.payload.file,o=n.tempFiles,r=n.files,a=null;zi(o)?a=o[0]:zi(r)&&(a=r[0]),e.payload.file={files:[a]}}var s=this.getMyUserID();e.currentUser=s;var i=new gm(e),u=new sm({uuid:this._generateUUID(),file:e.payload.file}),c=this._getNickAndAvatarByUserID(s);return i.setElement(u),i.setNickAndAvatar(c),i.setNameCard(this._getNameCardByGroupID(i)),this._messageOptionsMap.set(i.ID,e),i}Bi.warn("小程序目前不支持选择文件， createFileMessage 接口不可用！")}},{key:"createLocationMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new gm(e),o=new cm(e.payload),r=this._getNickAndAvatarByUserID(t);return n.setElement(o),n.setNickAndAvatar(r),n.setNameCard(this._getNameCardByGroupID(n)),this._messageOptionsMap.set(n.ID,e),n}},{key:"_onCannotFindModule",value:function(){return Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"sendMessageInstance",value:function(e,t){var n,o=this,r=null;switch(e.conversationType){case so.CONV_C2C:if(!(r=this.getModule(Wc)))return this._onCannotFindModule();break;case so.CONV_GROUP:if(!(r=this.getModule($c)))return this._onCannotFindModule();break;default:return Sm({code:Ld.MESSAGE_SEND_INVALID_CONVERSATION_TYPE,message:Bd})}var a=this.getModule(nl),s=this.getModule($c);return a.upload(e).then((function(){return o._getSendMessageSpecifiedKey(e)===Yp&&o.getModule(dl).addSuccessCount(zp),s.guardForAVChatRoom(e).then((function(){if(!e.isSendable())return Sm({code:Ld.MESSAGE_FILE_URL_IS_EMPTY,message:sp});o._addSendMessageTotalCount(e),n=Date.now();var a=function(e){var t="utf-8";ui&&document&&(t=document.charset.toLowerCase());var n,o,r=0;if(o=e.length,"utf-8"===t||"utf8"===t)for(var a=0;a<o;a++)(n=e.codePointAt(a))<=127?r+=1:n<=2047?r+=2:n<=65535?r+=3:(r+=4,a++);else if("utf-16"===t||"utf16"===t)for(var s=0;s<o;s++)(n=e.codePointAt(s))<=65535?r+=2:(r+=4,s++);else r=e.replace(/[^\x00-\xff]/g,"aa").length;return r}(JSON.stringify(e));return e.type===so.MSG_MERGER&&a>7e3?o._mergerMessageHandler.uploadMergerMessage(e,a).then((function(n){var r=o._mergerMessageHandler.createMergerMessagePack(e,t,n);return o.request(r)})):(o.getModule(Jc).setMessageRandom(e),e.conversationType===so.CONV_C2C||e.conversationType===so.CONV_GROUP?r.sendMessage(e,t):void 0)})).then((function(a){var s=a.data,i=s.time,u=s.sequence;o._addSendMessageSuccessCount(e,n),o._messageOptionsMap.delete(e.ID);var c=o.getModule(Jc);e.status=Ac.SUCCESS,e.time=i;var l=!1;if(e.conversationType===so.CONV_GROUP)e.sequence=u,e.generateMessageID(o.getMyUserID());else if(e.conversationType===so.CONV_C2C){var d=c.getLatestMessageSentByMe(e.conversationID);if(d){var p=d.nick,g=d.avatar;p===e.nick&&g===e.avatar||(l=!0)}}if(c.appendToMessageList(e),l&&c.modifyMessageSentByMe({conversationID:e.conversationID,latestNick:e.nick,latestAvatar:e.avatar}),r.isOnlineMessage(e,t))e._onlineOnlyFlag=!0;else{var h=e;Yi(t)&&Yi(t.messageControlInfo)&&(!0===t.messageControlInfo.excludedFromLastMessage&&(e._isExcludedFromLastMessage=!0,h=""),!0===t.messageControlInfo.excludedFromUnreadCount&&(e._isExcludedFromUnreadCount=!0)),c.onMessageSent({conversationOptionsList:[{conversationID:e.conversationID,unreadCount:0,type:e.conversationType,subType:e.conversationSubType,lastMessage:h}]})}return e.getRelayFlag()||"TIMImageElem"!==e.type||Au(e.payload.imageInfoArray),hm({message:e})}))})).catch((function(t){return o._onSendMessageFailed(e,t)}))}},{key:"_onSendMessageFailed",value:function(e,t){e.status=Ac.FAIL,this.getModule(Jc).deleteMessageRandom(e),this._addSendMessageFailCountOnUser(e,t);var n=new og(Mg);return n.setMessage("tjg_id:".concat(this.generateTjgID(e)," type:").concat(e.type," from:").concat(e.from," to:").concat(e.to)),this.probeNetwork().then((function(e){var o=Xn(e,2),r=o[0],a=o[1];n.setError(t,r,a).end()})),Bi.error("".concat(this._className,"._onSendMessageFailed error:"),t),Sm(new ym({code:t&&t.code?t.code:Ld.MESSAGE_SEND_FAIL,message:t&&t.message?t.message:Vd,data:{message:e}}))}},{key:"_getSendMessageSpecifiedKey",value:function(e){if([so.MSG_IMAGE,so.MSG_AUDIO,so.MSG_VIDEO,so.MSG_FILE].includes(e.type))return Yp;if(e.conversationType===so.CONV_C2C)return jp;if(e.conversationType===so.CONV_GROUP){var t=this.getModule($c).getLocalGroupProfile(e.to);if(!t)return;var n=t.type;return mu(n)?$p:Wp}}},{key:"_addSendMessageTotalCount",value:function(e){var t=this._getSendMessageSpecifiedKey(e);t&&this.getModule(dl).addTotalCount(t)}},{key:"_addSendMessageSuccessCount",value:function(e,t){var n=Math.abs(Date.now()-t),o=this._getSendMessageSpecifiedKey(e);if(o){var r=this.getModule(dl);r.addSuccessCount(o),r.addCost(o,n)}}},{key:"_addSendMessageFailCountOnUser",value:function(e,t){var n,o,r=t.code,a=void 0===r?-1:r,s=this.getModule(dl),i=this._getSendMessageSpecifiedKey(e);i===Yp&&(n=a,o=!1,Pv.includes(n)&&(o=!0),o)?s.addFailedCountOfUserSide(zp):function(e){var t=!1;return Object.values(Gv).includes(e)&&(t=!0),(e>=120001&&e<=13e4||e>=10100&&e<=10200)&&(t=!0),t}(a)&&i&&s.addFailedCountOfUserSide(i)}},{key:"resendMessage",value:function(e){return e.isResend=!0,e.status=Ac.UNSEND,e.random=iu(),e.generateMessageID(this.getMyUserID()),this.sendMessageInstance(e)}},{key:"revokeMessage",value:function(e){var t=this,n=null;if(e.conversationType===so.CONV_C2C){if(!(n=this.getModule(Wc)))return this._onCannotFindModule()}else if(e.conversationType===so.CONV_GROUP&&!(n=this.getModule($c)))return this._onCannotFindModule();var o=new og(Tg);return o.setMessage("tjg_id:".concat(this.generateTjgID(e)," type:").concat(e.type," from:").concat(e.from," to:").concat(e.to)),n.revokeMessage(e).then((function(n){var r=n.data.recallRetList;if(!Lu(r)&&0!==r[0].retCode){var a=new ym({code:r[0].retCode,message:Mm[r[0].retCode]||Wd,data:{message:e}});return o.setCode(a.code).setMoreMessage(a.message).end(),Sm(a)}return Bi.info("".concat(t._className,".revokeMessage ok. ID:").concat(e.ID)),e.isRevoked=!0,o.end(),t.getModule(Jc).onMessageRevoked([e]),hm({message:e})})).catch((function(n){t.probeNetwork().then((function(e){var t=Xn(e,2),r=t[0],a=t[1];o.setError(n,r,a).end()}));var r=new ym({code:n&&n.code?n.code:Ld.MESSAGE_REVOKE_FAIL,message:n&&n.message?n.message:Wd,data:{message:e}});return Bi.warn("".concat(t._className,".revokeMessage failed. error:"),n),Sm(r)}))}},{key:"deleteMessage",value:function(e){var t=this,n=null,o=e[0],r=o.conversationID,a="",s=[],i=[];if(o.conversationType===so.CONV_C2C?(n=this.getModule(Wc),a=r.replace(so.CONV_C2C,""),e.forEach((function(e){e&&e.status===Ac.SUCCESS&&e.conversationID===r&&(e._onlineOnlyFlag||s.push("".concat(e.sequence,"_").concat(e.random,"_").concat(e.time)),i.push(e))}))):o.conversationType===so.CONV_GROUP&&(n=this.getModule($c),a=r.replace(so.CONV_GROUP,""),e.forEach((function(e){e&&e.status===Ac.SUCCESS&&e.conversationID===r&&(e._onlineOnlyFlag||s.push("".concat(e.sequence)),i.push(e))}))),!n)return this._onCannotFindModule();if(0===s.length)return this._onMessageDeleted(i);s.length>30&&(s=s.slice(0,30),i=i.slice(0,30));var u=new og(Cg);return u.setMessage("to:".concat(a," count:").concat(s.length)),n.deleteMessage({to:a,keyList:s}).then((function(e){return u.end(),Bi.info("".concat(t._className,".deleteMessage ok")),t._onMessageDeleted(i)})).catch((function(e){t.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];u.setError(e,o,r).end()})),Bi.warn("".concat(t._className,".deleteMessage failed. error:"),e);var n=new ym({code:e&&e.code?e.code:Ld.MESSAGE_DELETE_FAIL,message:e&&e.message?e.message:$d});return Sm(n)}))}},{key:"_onMessageDeleted",value:function(e){return this.getModule(Jc).onMessageDeleted(e),Cm({messageList:e})}},{key:"_generateUUID",value:function(){var e=this.getModule(Xc);return"".concat(e.getSDKAppID(),"-").concat(e.getUserID(),"-").concat(function(){for(var e="",t=32;t>0;--t)e+=uu[Math.floor(Math.random()*cu)];return e}())}},{key:"getMessageOptionByID",value:function(e){return this._messageOptionsMap.get(e)}},{key:"_getNickAndAvatarByUserID",value:function(e){return this.getModule(jc).getNickAndAvatarByUserID(e)}},{key:"_getNameCardByGroupID",value:function(e){if(e.conversationType===so.CONV_GROUP){var t=this.getModule($c);if(t)return t.getMyNameCardByGroupID(e.to)}return""}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._messageOptionsMap.clear()}}]),n}(gl),Fv=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="PluginModule",o.plugins={},o}return Un(n,[{key:"registerPlugin",value:function(e){var t=this;Object.keys(e).forEach((function(n){t.plugins[n]=e[n]})),new og(cg).setMessage("key=".concat(Object.keys(e))).end()}},{key:"getPlugin",value:function(e){return this.plugins[e]}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset"))}}]),n}(gl),qv=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="SyncUnreadMessageModule",o._cookie="",o._onlineSyncFlag=!1,o.getInnerEmitterInstance().on(Gm,o._onLoginSuccess,Yn(o)),o}return Un(n,[{key:"_onLoginSuccess",value:function(e){this._startSync({cookie:this._cookie,syncFlag:0,isOnlineSync:0})}},{key:"_startSync",value:function(e){var t=this,n=e.cookie,o=e.syncFlag,r=e.isOnlineSync;Bi.log("".concat(this._className,"._startSync cookie:").concat(n," syncFlag:").concat(o," isOnlineSync:").concat(r)),this.request({protocolName:vl,requestData:{cookie:n,syncFlag:o,isOnlineSync:r}}).then((function(e){var n=e.data,o=n.cookie,r=n.syncFlag,a=n.eventArray,s=n.messageList,i=n.C2CRemainingUnreadList,u=n.C2CPairUnreadList;t._cookie=o,Lu(o)||(0===r||1===r?(a&&t.getModule(al).onMessage({head:{},body:{eventArray:a,isInstantMessage:t._onlineSyncFlag,isSyncingEnded:!1}}),t.getModule(Wc).onNewC2CMessage({dataList:s,isInstantMessage:!1,C2CRemainingUnreadList:i,C2CPairUnreadList:u}),t._startSync({cookie:o,syncFlag:r,isOnlineSync:0})):2===r&&(a&&t.getModule(al).onMessage({head:{},body:{eventArray:a,isInstantMessage:t._onlineSyncFlag,isSyncingEnded:!0}}),t.getModule(Wc).onNewC2CMessage({dataList:s,isInstantMessage:t._onlineSyncFlag,C2CRemainingUnreadList:i,C2CPairUnreadList:u})))})).catch((function(e){Bi.error("".concat(t._className,"._startSync failed. error:"),e)}))}},{key:"startOnlineSync",value:function(){Bi.log("".concat(this._className,".startOnlineSync")),this._onlineSyncFlag=!0,this._startSync({cookie:this._cookie,syncFlag:0,isOnlineSync:1})}},{key:"startSyncOnReconnected",value:function(){Bi.log("".concat(this._className,".startSyncOnReconnected.")),this._onlineSyncFlag=!0,this._startSync({cookie:this._cookie,syncFlag:0,isOnlineSync:0})}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._onlineSyncFlag=!1,this._cookie=""}}]),n}(gl),xv={request:{toAccount:"To_Account",fromAccount:"From_Account",to:"To_Account",from:"From_Account",groupID:"GroupId",groupAtUserID:"GroupAt_Account",extension:"Ext",data:"Data",description:"Desc",elements:"MsgBody",sizeType:"Type",downloadFlag:"Download_Flag",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",videoUrl:"",imageUrl:"URL",fileUrl:"Url",uuid:"UUID",priority:"MsgPriority",receiverUserID:"To_Account",receiverGroupID:"GroupId",messageSender:"SenderId",messageReceiver:"ReceiverId",nick:"From_AccountNick",avatar:"From_AccountHeadurl",messageNumber:"MsgNum",pbDownloadKey:"PbMsgKey",downloadKey:"JsonMsgKey",applicationType:"PendencyType",userIDList:"To_Account",groupNameList:"GroupName",userID:"To_Account",groupAttributeList:"GroupAttr",mainSequence:"AttrMainSeq",avChatRoomKey:"BytesKey",attributeControl:"AttrControl",sequence:"seq",messageControlInfo:"SendMsgControl",updateSequence:"UpdateSeq"},response:{MsgPriority:"priority",ThumbUUID:"thumbUUID",VideoUUID:"videoUUID",Download_Flag:"downloadFlag",GroupId:"groupID",Member_Account:"userID",MsgList:"messageList",SyncFlag:"syncFlag",To_Account:"to",From_Account:"from",MsgSeq:"sequence",MsgRandom:"random",MsgTime:"time",MsgTimeStamp:"time",MsgContent:"content",MsgBody:"elements",From_AccountNick:"nick",From_AccountHeadurl:"avatar",GroupWithdrawInfoArray:"revokedInfos",GroupReadInfoArray:"groupMessageReadNotice",LastReadMsgSeq:"lastMessageSeq",WithdrawC2cMsgNotify:"c2cMessageRevokedNotify",C2cWithdrawInfoArray:"revokedInfos",C2cReadedReceipt:"c2cMessageReadReceipt",ReadC2cMsgNotify:"c2cMessageReadNotice",LastReadTime:"peerReadTime",MsgRand:"random",MsgType:"type",MsgShow:"messageShow",NextMsgSeq:"nextMessageSeq",FaceUrl:"avatar",ProfileDataMod:"profileModify",Profile_Account:"userID",ValueBytes:"value",ValueNum:"value",NoticeSeq:"noticeSequence",NotifySeq:"notifySequence",MsgFrom_AccountExtraInfo:"messageFromAccountExtraInformation",Operator_Account:"operatorID",OpType:"operationType",ReportType:"operationType",UserId:"userID",User_Account:"userID",List_Account:"userIDList",MsgOperatorMemberExtraInfo:"operatorInfo",MsgMemberExtraInfo:"memberInfoList",ImageUrl:"avatar",NickName:"nick",MsgGroupNewInfo:"newGroupProfile",MsgAppDefinedData:"groupCustomField",Owner_Account:"ownerID",GroupFaceUrl:"avatar",GroupIntroduction:"introduction",GroupNotification:"notification",GroupApplyJoinOption:"joinOption",MsgKey:"messageKey",GroupInfo:"groupProfile",ShutupTime:"muteTime",Desc:"description",Ext:"extension",GroupAt_Account:"groupAtUserID",MsgNum:"messageNumber",PbMsgKey:"pbDownloadKey",JsonMsgKey:"downloadKey",MsgModifiedFlag:"isModified",PendencyItem:"applicationItem",PendencyType:"applicationType",AddTime:"time",AddSource:"source",AddWording:"wording",ProfileImImage:"avatar",PendencyAdd:"friendApplicationAdded",FrienPencydDel_Account:"friendApplicationDeletedUserIDList",Peer_Account:"userID",GroupAttr:"groupAttributeList",GroupAttrAry:"groupAttributeList",AttrMainSeq:"mainSequence",seq:"sequence",GroupAttrOption:"groupAttributeOption",BytesChangedKeys:"changedKeyList",GroupAttrInfo:"groupAttributeList",GroupAttrSeq:"mainSequence",PushChangedAttrValFlag:"hasChangedAttributeInfo",SubKeySeq:"sequence",Val:"value",MsgGroupFromCardName:"senderNameCard",MsgGroupFromNickName:"senderNick",C2cNick:"peerNick",C2cImage:"peerAvatar",SendMsgControl:"messageControlInfo",NoLastMsg:"excludedFromLastMessage",NoUnread:"excludedFromUnreadCount",UpdateSeq:"updateSequence",MuteNotifications:"muteFlag"},ignoreKeyWord:["C2C","ID","USP"]},Vv=Xt.trim;function Kv(e,t){if("string"!=typeof e&&!Array.isArray(e))throw new TypeError("Expected the input to be `string | string[]`");var n;return t=Object.assign({pascalCase:!1},t),0===(e=Array.isArray(e)?e.map((function(e){return e.trim()})).filter((function(e){return e.length})).join("-"):e.trim()).length?"":1===e.length?t.pascalCase?e.toUpperCase():e.toLowerCase():(e!==e.toLowerCase()&&(e=Bv(e)),e=e.replace(/^[_.\- ]+/,"").toLowerCase().replace(/[_.\- ]+(\w|$)/g,(function(e,t){return t.toUpperCase()})).replace(/\d+(\w|$)/g,(function(e){return e.toUpperCase()})),n=e,t.pascalCase?n.charAt(0).toUpperCase()+n.slice(1):n)}be({target:"String",proto:!0,forced:function(e){return a((function(){return!!Wt[e]()||"​᠎"!="​᠎"[e]()||Wt[e].name!==e}))}("trim")},{trim:function(){return Vv(this)}});var Bv=function(e){for(var t=!1,n=!1,o=!1,r=0;r<e.length;r++){var a=e[r];t&&/[a-zA-Z]/.test(a)&&a.toUpperCase()===a?(e=e.slice(0,r)+"-"+e.slice(r),t=!1,o=n,n=!0,r++):n&&o&&/[a-zA-Z]/.test(a)&&a.toLowerCase()===a?(e=e.slice(0,r-1)+"-"+e.slice(r-1),o=n,n=!1,t=!0):(t=a.toLowerCase()===a&&a.toUpperCase()!==a,o=n,n=a.toUpperCase()===a&&a.toLowerCase()!==a)}return e};function Hv(e,t){var n=0;return function e(t,o){if(++n>100)return n--,t;if(zi(t)){var r=t.map((function(t){return $i(t)?e(t,o):t}));return n--,r}if($i(t)){var a=(s=t,i=function(e,t){if(!tu(t))return!1;if((r=t)!==Kv(r))for(var n=0;n<xv.ignoreKeyWord.length&&!t.includes(xv.ignoreKeyWord[n]);n++);var r;return Ji(o[t])?function(e){return"OPPOChannelID"===e?e:e[0].toUpperCase()+Kv(e).slice(1)}(t):o[t]},u=Object.create(null),Object.keys(s).forEach((function(e){var t=i(s[e],e);t&&(u[t]=s[e])})),u);return a=Iu(a,(function(t,n){return zi(t)||$i(t)?e(t,o):t})),n--,a}var s,i,u}(e,t)}function jv(e,t){if(zi(e))return e.map((function(e){return $i(e)?jv(e,t):e}));if($i(e)){var n=(o=e,r=function(e,n){return Ji(t[n])?Kv(n):t[n]},a={},Object.keys(o).forEach((function(e){a[r(o[e],e)]=o[e]})),a);return Iu(n,(function(e){return zi(e)||$i(e)?jv(e,t):e}))}var o,r,a}var Wv="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView,$v=function(e){if(void 0===e)return 0;var t=ce(e),n=de(t);if(t!==n)throw RangeError("Wrong length or index");return n},Yv=Math.abs,zv=Math.pow,Jv=Math.floor,Xv=Math.log,Qv=Math.LN2,Zv=function(e,t,n){var o,r,a,s=new Array(n),i=8*n-t-1,u=(1<<i)-1,c=u>>1,l=23===t?zv(2,-24)-zv(2,-77):0,d=e<0||0===e&&1/e<0?1:0,p=0;for((e=Yv(e))!=e||e===1/0?(r=e!=e?1:0,o=u):(o=Jv(Xv(e)/Qv),e*(a=zv(2,-o))<1&&(o--,a*=2),(e+=o+c>=1?l/a:l*zv(2,1-c))*a>=2&&(o++,a/=2),o+c>=u?(r=0,o=u):o+c>=1?(r=(e*a-1)*zv(2,t),o+=c):(r=e*zv(2,c-1)*zv(2,t),o=0));t>=8;s[p++]=255&r,r/=256,t-=8);for(o=o<<t|r,i+=t;i>0;s[p++]=255&o,o/=256,i-=8);return s[--p]|=128*d,s},eM=function(e,t){var n,o=e.length,r=8*o-t-1,a=(1<<r)-1,s=a>>1,i=r-7,u=o-1,c=e[u--],l=127&c;for(c>>=7;i>0;l=256*l+e[u],u--,i-=8);for(n=l&(1<<-i)-1,l>>=-i,i+=t;i>0;n=256*n+e[u],u--,i-=8);if(0===l)l=1-s;else{if(l===a)return n?NaN:c?-1/0:1/0;n+=zv(2,t),l-=s}return(c?-1:1)*n*zv(2,l-t)},tM=function(e){for(var t=Ge(this),n=de(t.length),o=arguments.length,r=he(o>1?arguments[1]:void 0,n),a=o>2?arguments[2]:void 0,s=void 0===a?n:he(a,n);s>r;)t[r++]=e;return t},nM=Ie.f,oM=O.f,rM=ne.get,aM=ne.set,sM=r.ArrayBuffer,iM=sM,uM=r.DataView,cM=uM&&uM.prototype,lM=Object.prototype,dM=r.RangeError,pM=Zv,gM=eM,hM=function(e){return[255&e]},fM=function(e){return[255&e,e>>8&255]},_M=function(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]},mM=function(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]},vM=function(e){return pM(e,23,4)},MM=function(e){return pM(e,52,8)},yM=function(e,t){oM(e.prototype,t,{get:function(){return rM(this)[t]}})},IM=function(e,t,n,o){var r=$v(n),a=rM(e);if(r+t>a.byteLength)throw dM("Wrong index");var s=rM(a.buffer).bytes,i=r+a.byteOffset,u=s.slice(i,i+t);return o?u:u.reverse()},TM=function(e,t,n,o,r,a){var s=$v(n),i=rM(e);if(s+t>i.byteLength)throw dM("Wrong index");for(var u=rM(i.buffer).bytes,c=s+i.byteOffset,l=o(+r),d=0;d<t;d++)u[c+d]=l[a?d:t-d-1]};if(Wv){if(!a((function(){sM(1)}))||!a((function(){new sM(-1)}))||a((function(){return new sM,new sM(1.5),new sM(NaN),"ArrayBuffer"!=sM.name}))){for(var CM,SM=(iM=function(e){return Lr(this,iM),new sM($v(e))}).prototype=sM.prototype,AM=nM(sM),EM=0;AM.length>EM;)(CM=AM[EM++])in iM||L(iM,CM,sM[CM]);SM.constructor=iM}In&&dn(cM)!==lM&&In(cM,lM);var kM=new uM(new iM(2)),DM=cM.setInt8;kM.setInt8(0,2147483648),kM.setInt8(1,2147483649),!kM.getInt8(0)&&kM.getInt8(1)||Dr(cM,{setInt8:function(e,t){DM.call(this,e,t<<24>>24)},setUint8:function(e,t){DM.call(this,e,t<<24>>24)}},{unsafe:!0})}else iM=function(e){Lr(this,iM,"ArrayBuffer");var t=$v(e);aM(this,{bytes:tM.call(new Array(t),0),byteLength:t}),i||(this.byteLength=t)},uM=function(e,t,n){Lr(this,uM,"DataView"),Lr(e,iM,"DataView");var o=rM(e).byteLength,r=ce(t);if(r<0||r>o)throw dM("Wrong offset");if(r+(n=void 0===n?o-r:de(n))>o)throw dM("Wrong length");aM(this,{buffer:e,byteLength:n,byteOffset:r}),i||(this.buffer=e,this.byteLength=n,this.byteOffset=r)},i&&(yM(iM,"byteLength"),yM(uM,"buffer"),yM(uM,"byteLength"),yM(uM,"byteOffset")),Dr(uM.prototype,{getInt8:function(e){return IM(this,1,e)[0]<<24>>24},getUint8:function(e){return IM(this,1,e)[0]},getInt16:function(e){var t=IM(this,2,e,arguments.length>1?arguments[1]:void 0);return(t[1]<<8|t[0])<<16>>16},getUint16:function(e){var t=IM(this,2,e,arguments.length>1?arguments[1]:void 0);return t[1]<<8|t[0]},getInt32:function(e){return mM(IM(this,4,e,arguments.length>1?arguments[1]:void 0))},getUint32:function(e){return mM(IM(this,4,e,arguments.length>1?arguments[1]:void 0))>>>0},getFloat32:function(e){return gM(IM(this,4,e,arguments.length>1?arguments[1]:void 0),23)},getFloat64:function(e){return gM(IM(this,8,e,arguments.length>1?arguments[1]:void 0),52)},setInt8:function(e,t){TM(this,1,e,hM,t)},setUint8:function(e,t){TM(this,1,e,hM,t)},setInt16:function(e,t){TM(this,2,e,fM,t,arguments.length>2?arguments[2]:void 0)},setUint16:function(e,t){TM(this,2,e,fM,t,arguments.length>2?arguments[2]:void 0)},setInt32:function(e,t){TM(this,4,e,_M,t,arguments.length>2?arguments[2]:void 0)},setUint32:function(e,t){TM(this,4,e,_M,t,arguments.length>2?arguments[2]:void 0)},setFloat32:function(e,t){TM(this,4,e,vM,t,arguments.length>2?arguments[2]:void 0)},setFloat64:function(e,t){TM(this,8,e,MM,t,arguments.length>2?arguments[2]:void 0)}});mn(iM,"ArrayBuffer"),mn(uM,"DataView");var NM={ArrayBuffer:iM,DataView:uM},OM=NM.ArrayBuffer,LM=NM.DataView,RM=OM.prototype.slice,bM=a((function(){return!new OM(2).slice(1,void 0).byteLength}));be({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:bM},{slice:function(e,t){if(void 0!==RM&&void 0===t)return RM.call(D(this),e);for(var n=D(this).byteLength,o=he(e,n),r=he(void 0===t?n:t,n),a=new(wr(this,OM))(de(r-o)),s=new LM(this),i=new LM(a),u=0;o<r;)i.setUint8(u++,s.getUint8(o++));return a}});var wM=String.fromCharCode,GM=String.fromCodePoint,PM=!!GM&&1!=GM.length;be({target:"String",stat:!0,forced:PM},{fromCodePoint:function(e){for(var t,n=[],o=arguments.length,r=0;o>r;){if(t=+arguments[r++],he(t,1114111)!==t)throw RangeError(t+" is not a valid code point");n.push(t<65536?wM(t):wM(55296+((t-=65536)>>10),t%1024+56320))}return n.join("")}});var UM,FM=O.f,qM=r.Int8Array,xM=qM&&qM.prototype,VM=r.Uint8ClampedArray,KM=VM&&VM.prototype,BM=qM&&dn(qM),HM=xM&&dn(xM),jM=Object.prototype,WM=jM.isPrototypeOf,$M=Ke("toStringTag"),YM=H("TYPED_ARRAY_TAG"),zM=Wv&&!!In&&"Opera"!==Et(r.opera),JM=!1,XM={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},QM=function(e){return v(e)&&I(XM,Et(e))};for(UM in XM)r[UM]||(zM=!1);if((!zM||"function"!=typeof BM||BM===Function.prototype)&&(BM=function(){throw TypeError("Incorrect invocation")},zM))for(UM in XM)r[UM]&&In(r[UM],BM);if((!zM||!HM||HM===jM)&&(HM=BM.prototype,zM))for(UM in XM)r[UM]&&In(r[UM].prototype,HM);if(zM&&dn(KM)!==HM&&In(KM,HM),i&&!I(HM,$M))for(UM in JM=!0,FM(HM,$M,{get:function(){return v(this)?this[YM]:void 0}}),XM)r[UM]&&L(r[UM],YM,UM);var ZM={NATIVE_ARRAY_BUFFER_VIEWS:zM,TYPED_ARRAY_TAG:JM&&YM,aTypedArray:function(e){if(QM(e))return e;throw TypeError("Target is not a typed array")},aTypedArrayConstructor:function(e){if(In){if(WM.call(BM,e))return e}else for(var t in XM)if(I(XM,UM)){var n=r[t];if(n&&(e===n||WM.call(n,e)))return e}throw TypeError("Target is not a typed array constructor")},exportTypedArrayMethod:function(e,t,n){if(i){if(n)for(var o in XM){var a=r[o];a&&I(a.prototype,e)&&delete a.prototype[e]}HM[e]&&!n||oe(HM,e,n?t:zM&&xM[e]||t)}},exportTypedArrayStaticMethod:function(e,t,n){var o,a;if(i){if(In){if(n)for(o in XM)(a=r[o])&&I(a,e)&&delete a[e];if(BM[e]&&!n)return;try{return oe(BM,e,n?t:zM&&qM[e]||t)}catch(eT){}}for(o in XM)!(a=r[o])||a[e]&&!n||oe(a,e,t)}},isView:function(e){var t=Et(e);return"DataView"===t||I(XM,t)},isTypedArray:QM,TypedArray:BM,TypedArrayPrototype:HM},ey=ZM.NATIVE_ARRAY_BUFFER_VIEWS,ty=r.ArrayBuffer,ny=r.Int8Array,oy=!ey||!a((function(){ny(1)}))||!a((function(){new ny(-1)}))||!wt((function(e){new ny,new ny(null),new ny(1.5),new ny(e)}),!0)||a((function(){return 1!==new ny(new ty(2),1,void 0).length})),ry=function(e,t){var n=function(e){var t=ce(e);if(t<0)throw RangeError("The argument can't be less than 0");return t}(e);if(n%t)throw RangeError("Wrong offset");return n},ay=ZM.aTypedArrayConstructor,sy=function(e){var t,n,o,r,a,s,i=Ge(e),u=arguments.length,c=u>1?arguments[1]:void 0,l=void 0!==c,d=Dt(i);if(null!=d&&!It(d))for(s=(a=d.call(i)).next,i=[];!(r=s.call(a)).done;)i.push(r.value);for(l&&u>2&&(c=rt(c,arguments[2],2)),n=de(i.length),o=new(ay(this))(n),t=0;n>t;t++)o[t]=l?c(i[t],t):i[t];return o};t((function(e){var t=Ie.f,n=it.forEach,o=ne.get,a=ne.set,u=O.f,c=k.f,l=Math.round,p=r.RangeError,g=NM.ArrayBuffer,h=NM.DataView,f=ZM.NATIVE_ARRAY_BUFFER_VIEWS,_=ZM.TYPED_ARRAY_TAG,m=ZM.TypedArray,y=ZM.TypedArrayPrototype,T=ZM.aTypedArrayConstructor,C=ZM.isTypedArray,S=function(e,t){for(var n=0,o=t.length,r=new(T(e))(o);o>n;)r[n]=t[n++];return r},A=function(e,t){u(e,t,{get:function(){return o(this)[t]}})},E=function(e){var t;return e instanceof g||"ArrayBuffer"==(t=Et(e))||"SharedArrayBuffer"==t},D=function(e,t){return C(e)&&"symbol"!=s(t)&&t in e&&String(+t)==String(t)},N=function(e,t){return D(e,t=M(t,!0))?d(2,e[t]):c(e,t)},R=function(e,t,n){return!(D(e,t=M(t,!0))&&v(n)&&I(n,"value"))||I(n,"get")||I(n,"set")||n.configurable||I(n,"writable")&&!n.writable||I(n,"enumerable")&&!n.enumerable?u(e,t,n):(e[t]=n.value,e)};i?(f||(k.f=N,O.f=R,A(y,"buffer"),A(y,"byteOffset"),A(y,"byteLength"),A(y,"length")),be({target:"Object",stat:!0,forced:!f},{getOwnPropertyDescriptor:N,defineProperty:R}),e.exports=function(e,s,i){var c=e.match(/\d+$/)[0]/8,d=e+(i?"Clamped":"")+"Array",M="get"+e,I="set"+e,T=r[d],A=T,k=A&&A.prototype,D={},N=function(e,t){u(e,t,{get:function(){return function(e,t){var n=o(e);return n.view[M](t*c+n.byteOffset,!0)}(this,t)},set:function(e){return function(e,t,n){var r=o(e);i&&(n=(n=l(n))<0?0:n>255?255:255&n),r.view[I](t*c+r.byteOffset,n,!0)}(this,t,e)},enumerable:!0})};f?oy&&(A=s((function(e,t,n,o){return Lr(e,A,d),or(v(t)?E(t)?void 0!==o?new T(t,ry(n,c),o):void 0!==n?new T(t,ry(n,c)):new T(t):C(t)?S(A,t):sy.call(A,t):new T($v(t)),e,A)})),In&&In(A,m),n(t(T),(function(e){e in A||L(A,e,T[e])})),A.prototype=k):(A=s((function(e,t,n,o){Lr(e,A,d);var r,s,i,u=0,l=0;if(v(t)){if(!E(t))return C(t)?S(A,t):sy.call(A,t);r=t,l=ry(n,c);var f=t.byteLength;if(void 0===o){if(f%c)throw p("Wrong length");if((s=f-l)<0)throw p("Wrong length")}else if((s=de(o)*c)+l>f)throw p("Wrong length");i=s/c}else i=$v(t),r=new g(s=i*c);for(a(e,{buffer:r,byteOffset:l,byteLength:s,length:i,view:new h(r)});u<i;)N(e,u++)})),In&&In(A,m),k=A.prototype=Ht(y)),k.constructor!==A&&L(k,"constructor",A),_&&L(k,_,d),D[d]=A,be({global:!0,forced:A!=T,sham:!f},D),"BYTES_PER_ELEMENT"in A||L(A,"BYTES_PER_ELEMENT",c),"BYTES_PER_ELEMENT"in k||L(k,"BYTES_PER_ELEMENT",c),Or(d)}):e.exports=function(){}}))("Uint8",(function(e){return function(t,n,o){return e(this,t,n,o)}}));var iy=Math.min,uy=[].copyWithin||function(e,t){var n=Ge(this),o=de(n.length),r=he(e,o),a=he(t,o),s=arguments.length>2?arguments[2]:void 0,i=iy((void 0===s?o:he(s,o))-a,o-r),u=1;for(a<r&&r<a+i&&(u=-1,a+=i-1,r+=i-1);i-- >0;)a in n?n[r]=n[a]:delete n[r],r+=u,a+=u;return n},cy=ZM.aTypedArray;(0,ZM.exportTypedArrayMethod)("copyWithin",(function(e,t){return uy.call(cy(this),e,t,arguments.length>2?arguments[2]:void 0)}));var ly=it.every,dy=ZM.aTypedArray;(0,ZM.exportTypedArrayMethod)("every",(function(e){return ly(dy(this),e,arguments.length>1?arguments[1]:void 0)}));var py=ZM.aTypedArray;(0,ZM.exportTypedArrayMethod)("fill",(function(e){return tM.apply(py(this),arguments)}));var gy=it.filter,hy=ZM.aTypedArray,fy=ZM.aTypedArrayConstructor;(0,ZM.exportTypedArrayMethod)("filter",(function(e){for(var t=gy(hy(this),e,arguments.length>1?arguments[1]:void 0),n=wr(this,this.constructor),o=0,r=t.length,a=new(fy(n))(r);r>o;)a[o]=t[o++];return a}));var _y=it.find,vy=ZM.aTypedArray;(0,ZM.exportTypedArrayMethod)("find",(function(e){return _y(vy(this),e,arguments.length>1?arguments[1]:void 0)}));var My=it.findIndex,yy=ZM.aTypedArray;(0,ZM.exportTypedArrayMethod)("findIndex",(function(e){return My(yy(this),e,arguments.length>1?arguments[1]:void 0)}));var Iy=it.forEach,Ty=ZM.aTypedArray;(0,ZM.exportTypedArrayMethod)("forEach",(function(e){Iy(Ty(this),e,arguments.length>1?arguments[1]:void 0)}));var Cy=_e.includes,Sy=ZM.aTypedArray;(0,ZM.exportTypedArrayMethod)("includes",(function(e){return Cy(Sy(this),e,arguments.length>1?arguments[1]:void 0)}));var Ay=_e.indexOf,Ey=ZM.aTypedArray;(0,ZM.exportTypedArrayMethod)("indexOf",(function(e){return Ay(Ey(this),e,arguments.length>1?arguments[1]:void 0)}));var ky=Ke("iterator"),Dy=r.Uint8Array,Ny=Po.values,Oy=Po.keys,Ly=Po.entries,Ry=ZM.aTypedArray,by=ZM.exportTypedArrayMethod,wy=Dy&&Dy.prototype[ky],Gy=!!wy&&("values"==wy.name||null==wy.name),Py=function(){return Ny.call(Ry(this))};by("entries",(function(){return Ly.call(Ry(this))})),by("keys",(function(){return Oy.call(Ry(this))})),by("values",Py,!Gy),by(ky,Py,!Gy);var Uy=ZM.aTypedArray,Fy=[].join;(0,ZM.exportTypedArrayMethod)("join",(function(e){return Fy.apply(Uy(this),arguments)}));var qy=ZM.aTypedArray;(0,ZM.exportTypedArrayMethod)("lastIndexOf",(function(e){return am.apply(qy(this),arguments)}));var xy=it.map,Vy=ZM.aTypedArray,Ky=ZM.aTypedArrayConstructor;(0,ZM.exportTypedArrayMethod)("map",(function(e){return xy(Vy(this),e,arguments.length>1?arguments[1]:void 0,(function(e,t){return new(Ky(wr(e,e.constructor)))(t)}))}));var By=function(e){return function(t,n,o,r){ot(n);var a=Ge(t),s=f(a),i=de(a.length),u=e?i-1:0,c=e?-1:1;if(o<2)for(;;){if(u in s){r=s[u],u+=c;break}if(u+=c,e?u<0:i<=u)throw TypeError("Reduce of empty array with no initial value")}for(;e?u>=0:i>u;u+=c)u in s&&(r=n(r,s[u],u,a));return r}},Hy={left:By(!1),right:By(!0)},jy=Hy.left,Wy=ZM.aTypedArray;(0,ZM.exportTypedArrayMethod)("reduce",(function(e){return jy(Wy(this),e,arguments.length,arguments.length>1?arguments[1]:void 0)}));var $y=Hy.right,Yy=ZM.aTypedArray;(0,ZM.exportTypedArrayMethod)("reduceRight",(function(e){return $y(Yy(this),e,arguments.length,arguments.length>1?arguments[1]:void 0)}));var zy=ZM.aTypedArray,Jy=ZM.exportTypedArrayMethod,Xy=Math.floor;Jy("reverse",(function(){for(var e,t=zy(this).length,n=Xy(t/2),o=0;o<n;)e=this[o],this[o++]=this[--t],this[t]=e;return this}));var Qy=ZM.aTypedArray;(0,ZM.exportTypedArrayMethod)("set",(function(e){Qy(this);var t=ry(arguments.length>1?arguments[1]:void 0,1),n=this.length,o=Ge(e),r=de(o.length),a=0;if(r+t>n)throw RangeError("Wrong length");for(;a<r;)this[t+a]=o[a++]}),a((function(){new Int8Array(1).set({})})));var Zy=ZM.aTypedArray,eI=ZM.aTypedArrayConstructor,tI=[].slice;(0,ZM.exportTypedArrayMethod)("slice",(function(e,t){for(var n=tI.call(Zy(this),e,t),o=wr(this,this.constructor),r=0,a=n.length,s=new(eI(o))(a);a>r;)s[r]=n[r++];return s}),a((function(){new Int8Array(1).slice()})));var nI=it.some,oI=ZM.aTypedArray;(0,ZM.exportTypedArrayMethod)("some",(function(e){return nI(oI(this),e,arguments.length>1?arguments[1]:void 0)}));var rI=ZM.aTypedArray,aI=[].sort;(0,ZM.exportTypedArrayMethod)("sort",(function(e){return aI.call(rI(this),e)}));var sI=ZM.aTypedArray;(0,ZM.exportTypedArrayMethod)("subarray",(function(e,t){var n=sI(this),o=n.length,r=he(e,o);return new(wr(n,n.constructor))(n.buffer,n.byteOffset+r*n.BYTES_PER_ELEMENT,de((void 0===t?o:he(t,o))-r))}));var iI=r.Int8Array,uI=ZM.aTypedArray,cI=ZM.exportTypedArrayMethod,lI=[].toLocaleString,dI=[].slice,pI=!!iI&&a((function(){lI.call(new iI(1))}));cI("toLocaleString",(function(){return lI.apply(pI?dI.call(uI(this)):uI(this),arguments)}),a((function(){return[1,2].toLocaleString()!=new iI([1,2]).toLocaleString()}))||!a((function(){iI.prototype.toLocaleString.call([1,2])})));var gI=ZM.exportTypedArrayMethod,hI=r.Uint8Array,fI=hI&&hI.prototype||{},_I=[].toString,mI=[].join;a((function(){_I.call({})}))&&(_I=function(){return mI.call(this)});var vI=fI.toString!=_I;gI("toString",_I,vI);var MI=String.fromCharCode,yI=function(e){var t=0|e.charCodeAt(0);if(55296<=t)if(t<56320){var n=0|e.charCodeAt(1);if(56320<=n&&n<=57343){if((t=(t<<10)+n-56613888|0)>65535)return MI(240|t>>>18,128|t>>>12&63,128|t>>>6&63,128|63&t)}else t=65533}else t<=57343&&(t=65533);return t<=2047?MI(192|t>>>6,128|63&t):MI(224|t>>>12,128|t>>>6&63,128|63&t)},II=function(e){for(var t=void 0===e?"":(""+e).replace(/[\x80-\uD7ff\uDC00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]?/g,yI),n=0|t.length,o=new Uint8Array(n),r=0;r<n;r=r+1|0)o[r]=0|t.charCodeAt(r);return o},TI=function(e){for(var t=new Uint8Array(e),n="",o=0,r=t.length;o<r;){var a=t[o],s=0,i=0;if(a<=127?(s=0,i=255&a):a<=223?(s=1,i=31&a):a<=239?(s=2,i=15&a):a<=244&&(s=3,i=7&a),r-o-s>0)for(var u=0;u<s;)i=i<<6|63&(a=t[o+u+1]),u+=1;else i=65533,s=r-o;n+=String.fromCodePoint(i),o+=s+1}return n},CI=function(){function e(t){Gn(this,e),this._handler=t;var n=t.getURL();this._socket=null,this._id=iu(),ii?ai?(ci.connectSocket({url:n,header:{"content-type":"application/json"}}),ci.onSocketClose(this._onClose.bind(this)),ci.onSocketOpen(this._onOpen.bind(this)),ci.onSocketMessage(this._onMessage.bind(this)),ci.onSocketError(this._onError.bind(this))):(this._socket=ci.connectSocket({url:n,header:{"content-type":"application/json"},complete:function(){}}),this._socket.onClose(this._onClose.bind(this)),this._socket.onOpen(this._onOpen.bind(this)),this._socket.onMessage(this._onMessage.bind(this)),this._socket.onError(this._onError.bind(this))):ui&&(this._socket=new WebSocket(n),this._socket.binaryType="arraybuffer",this._socket.onopen=this._onOpen.bind(this),this._socket.onmessage=this._onMessage.bind(this),this._socket.onclose=this._onClose.bind(this),this._socket.onerror=this._onError.bind(this))}return Un(e,[{key:"getID",value:function(){return this._id}},{key:"_onOpen",value:function(){this._handler.onOpen({id:this._id})}},{key:"_onClose",value:function(e){this._handler.onClose({id:this._id,e:e})}},{key:"_onMessage",value:function(e){this._handler.onMessage({data:this._handler.canIUseBinaryFrame()?TI(e.data):e.data})}},{key:"_onError",value:function(e){this._handler.onError({id:this._id,e:e})}},{key:"close",value:function(e){if(ai)return ci.offSocketClose(),ci.offSocketMessage(),ci.offSocketOpen(),ci.offSocketError(),void ci.closeSocket();this._socket&&(ii?(this._socket.onClose((function(){})),this._socket.onOpen((function(){})),this._socket.onMessage((function(){})),this._socket.onError((function(){}))):ui&&(this._socket.onopen=null,this._socket.onmessage=null,this._socket.onclose=null,this._socket.onerror=null),ri?this._socket.close({code:e}):this._socket.close(e),this._socket=null)}},{key:"send",value:function(e){ai?ci.sendSocketMessage({data:e.data,fail:function(){e.fail&&e.requestID&&e.fail(e.requestID)}}):this._socket&&(ii?this._socket.send({data:this._handler.canIUseBinaryFrame()?II(e.data).buffer:e.data,fail:function(){e.fail&&e.requestID&&e.fail(e.requestID)}}):ui&&this._socket.send(this._handler.canIUseBinaryFrame()?II(e.data).buffer:e.data))}}]),e}(),SI=4e3,AI=4001,EI="connected",kI="connecting",DI="disconnected",NI=function(){function e(t){Gn(this,e),this._channelModule=t,this._className="SocketHandler",this._promiseMap=new Map,this._readyState=DI,this._simpleRequestMap=new Map,this.MAX_SIZE=100,this._startSequence=iu(),this._startTs=0,this._reConnectFlag=!1,this._nextPingTs=0,this._reConnectCount=0,this.MAX_RECONNECT_COUNT=3,this._socketID=-1,this._random=0,this._socket=null,this._url="",this._onOpenTs=0,this._canIUseBinaryFrame=!0,this._setWebsocketHost(),this._initConnection()}return Un(e,[{key:"_setWebsocketHost",value:function(){var e=this._channelModule.getModule(Xc),t=Hs;this._channelModule.isOversea()&&(t=js),e.isSingaporeSite()?t=Ws:e.isKoreaSite()?t=$s:e.isGermanySite()?t=Ys:e.isIndiaSite()&&(t=zs),Js.HOST.setCurrent(t)}},{key:"_initConnection",value:function(){Ji(Js.HOST.CURRENT.BACKUP)||""===this._url?this._url=Js.HOST.CURRENT.DEFAULT:this._url===Js.HOST.CURRENT.DEFAULT?this._url=Js.HOST.CURRENT.BACKUP:this._url===Js.HOST.CURRENT.BACKUP&&(this._url=Js.HOST.CURRENT.DEFAULT);var e=this._channelModule.getModule(Xc).getProxyServer();Lu(e)||(this._url=e),this._connect(),this._nextPingTs=0}},{key:"onCheckTimer",value:function(e){e%1==0&&this._checkPromiseMap()}},{key:"_checkPromiseMap",value:function(){var e=this;0!==this._promiseMap.size&&this._promiseMap.forEach((function(t,n){var o=t.reject,r=t.timestamp;Date.now()-r>=15e3&&(Bi.log("".concat(e._className,"._checkPromiseMap request timeout, delete requestID:").concat(n)),e._promiseMap.delete(n),o(new ym({code:Ld.NETWORK_TIMEOUT,message:Gp})),e._channelModule.onRequestTimeout(n))}))}},{key:"onOpen",value:function(e){this._onOpenTs=Date.now();var t=e.id;this._socketID=t;var n=Date.now()-this._startTs;Bi.log("".concat(this._className,"._onOpen cost ").concat(n," ms. socketID:").concat(t)),new og(pg).setMessage(n).setCostTime(n).setMoreMessage("socketID:".concat(t)).end(),e.id===this._socketID&&(this._readyState=EI,this._reConnectCount=0,this._resend(),!0===this._reConnectFlag&&(this._channelModule.onReconnected(),this._reConnectFlag=!1),this._channelModule.onOpen())}},{key:"onClose",value:function(e){var t=new og(gg),n=e.id,o=e.e,r="sourceSocketID:".concat(n," currentSocketID:").concat(this._socketID," code:").concat(o.code," reason:").concat(o.reason),a=0;0!==this._onOpenTs&&(a=Date.now()-this._onOpenTs),t.setMessage(a).setCostTime(a).setMoreMessage(r).setCode(o.code).end(),Bi.log("".concat(this._className,"._onClose ").concat(r," onlineTime:").concat(a)),n===this._socketID&&(this._readyState=DI,a<1e3?this._channelModule.onReconnectFailed():this._channelModule.onClose())}},{key:"onError",value:function(e){var t=e.id,n=e.e,o="sourceSocketID:".concat(t," currentSocketID:").concat(this._socketID);new og(hg).setMessage(n.errMsg||au(n)).setMoreMessage(o).setLevel("error").end(),Bi.warn("".concat(this._className,"._onError"),n,o),t===this._socketID&&(this._readyState="",this._channelModule.onError())}},{key:"onMessage",value:function(e){var t;try{t=JSON.parse(e.data)}catch(eT){new og(Og).setMessage(e.data).end()}if(t&&t.head){var n=this._getRequestIDFromHead(t.head),o=Eu(t.head),r=jv(t.body,this._getResponseKeyMap(o));if(Bi.debug("".concat(this._className,".onMessage ret:").concat(JSON.stringify(r)," requestID:").concat(n," has:").concat(this._promiseMap.has(n))),this._setNextPingTs(),this._promiseMap.has(n)){var a=this._promiseMap.get(n),s=a.resolve,i=a.reject,u=a.timestamp;return this._promiseMap.delete(n),this._calcRTT(u),void(r.errorCode&&0!==r.errorCode?(this._channelModule.onErrorCodeNotZero(r),i(new ym({code:r.errorCode,message:r.errorInfo||""}))):s(hm(r)))}this._channelModule.onMessage({head:t.head,body:r})}}},{key:"_calcRTT",value:function(e){var t=Date.now()-e;this._channelModule.getModule(dl).addRTT(t)}},{key:"_connect",value:function(){this._startTs=Date.now(),this._onOpenTs=0,this._socket=new CI(this),this._socketID=this._socket.getID(),this._readyState=kI,Bi.log("".concat(this._className,"._connect socketID:").concat(this._socketID," url:").concat(this.getURL())),new og(dg).setMessage("socketID:".concat(this._socketID," url:").concat(this.getURL())).end()}},{key:"getURL",value:function(){var e=this._channelModule.getModule(Xc);return e.isDevMode()&&(this._canIUseBinaryFrame=!1),(ai||ti&&"windows"===Cu()||si)&&(this._canIUseBinaryFrame=!1),this._canIUseBinaryFrame?"".concat(this._url,"/binfo?sdkappid=").concat(e.getSDKAppID(),"&instanceid=").concat(e.getInstanceID(),"&random=").concat(this._getRandom()):"".concat(this._url,"/info?sdkappid=").concat(e.getSDKAppID(),"&instanceid=").concat(e.getInstanceID(),"&random=").concat(this._getRandom())}},{key:"_closeConnection",value:function(e){Bi.log("".concat(this._className,"._closeConnection")),this._socket&&(this._socket.close(e),this._socketID=-1,this._socket=null,this._readyState=DI)}},{key:"_resend",value:function(){var e=this;if(Bi.log("".concat(this._className,"._resend reConnectFlag:").concat(this._reConnectFlag),"promiseMap.size:".concat(this._promiseMap.size," simpleRequestMap.size:").concat(this._simpleRequestMap.size)),this._promiseMap.size>0&&this._promiseMap.forEach((function(t,n){var o=t.uplinkData,r=t.resolve,a=t.reject;e._promiseMap.set(n,{resolve:r,reject:a,timestamp:Date.now(),uplinkData:o}),e._execute(n,o)})),this._simpleRequestMap.size>0){var t,n=ro(this._simpleRequestMap);try{for(n.s();!(t=n.n()).done;){var o=Xn(t.value,2),r=o[0],a=o[1];this._execute(r,a)}}catch(u){n.e(u)}finally{n.f()}this._simpleRequestMap.clear()}}},{key:"send",value:function(e){var t=this;e.head.seq=this._getSequence(),e.head.reqtime=Math.floor(Date.now()/1e3),e.keyMap;var n=$n(e,["keyMap"]),o=this._getRequestIDFromHead(e.head),r=JSON.stringify(n);return new Promise((function(e,a){t._promiseMap.set(o,{resolve:e,reject:a,timestamp:Date.now(),uplinkData:r}),Bi.debug("".concat(t._className,".send uplinkData:").concat(JSON.stringify(n)," requestID:").concat(o," readyState:").concat(t._readyState)),t._readyState!==EI?t._reConnect():(t._execute(o,r),t._channelModule.getModule(dl).addRequestCount())}))}},{key:"simplySend",value:function(e){e.head.seq=this._getSequence(),e.head.reqtime=Math.floor(Date.now()/1e3),e.keyMap;var t=$n(e,["keyMap"]),n=this._getRequestIDFromHead(e.head),o=JSON.stringify(t);this._readyState!==EI?(this._simpleRequestMap.size<this.MAX_SIZE?this._simpleRequestMap.set(n,o):Bi.log("".concat(this._className,".simplySend. simpleRequestMap is full, drop request!")),this._reConnect()):this._execute(n,o)}},{key:"_execute",value:function(e,t){this._socket.send({data:t,fail:ii?this._onSendFail.bind(this):void 0,requestID:e})}},{key:"_onSendFail",value:function(e){Bi.log("".concat(this._className,"._onSendFail requestID:").concat(e))}},{key:"_getSequence",value:function(){var e;if(this._startSequence<2415919103)return e=this._startSequence,this._startSequence+=1,2415919103===this._startSequence&&(this._startSequence=iu()),e}},{key:"_getRequestIDFromHead",value:function(e){return e.servcmd+e.seq}},{key:"_getResponseKeyMap",value:function(e){var t=this._channelModule.getKeyMap(e);return xn(xn({},xv.response),t.response)}},{key:"_reConnect",value:function(){this._readyState!==EI&&this._readyState!==kI&&this.forcedReconnect()}},{key:"forcedReconnect",value:function(){var e=this;Bi.log("".concat(this._className,".forcedReconnect count:").concat(this._reConnectCount," readyState:").concat(this._readyState)),this._reConnectFlag=!0,this._resetRandom(),this._reConnectCount<this.MAX_RECONNECT_COUNT?(this._reConnectCount+=1,this._closeConnection(AI),this._initConnection()):(this._reConnectCount=0,this._channelModule.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0];n[1],o?(Bi.warn("".concat(e._className,".forcedReconnect disconnected from wsserver but network is ok, continue...")),e._closeConnection(AI),e._initConnection()):e._channelModule.onReconnectFailed()})))}},{key:"getReconnectFlag",value:function(){return this._reConnectFlag}},{key:"_setNextPingTs",value:function(){this._nextPingTs=Date.now()+1e4}},{key:"getNextPingTs",value:function(){return this._nextPingTs}},{key:"isConnected",value:function(){return this._readyState===EI}},{key:"canIUseBinaryFrame",value:function(){return this._canIUseBinaryFrame}},{key:"_getRandom",value:function(){return 0===this._random&&(this._random=Math.random()),this._random}},{key:"_resetRandom",value:function(){this._random=0}},{key:"close",value:function(){Bi.log("".concat(this._className,".close")),this._closeConnection(SI),this._promiseMap.clear(),this._startSequence=iu(),this._readyState=DI,this._simpleRequestMap.clear(),this._reConnectFlag=!1,this._reConnectCount=0,this._onOpenTs=0,this._url="",this._random=0,this._canIUseBinaryFrame=!0}}]),e}(),OI=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;if(Gn(this,n),(o=t.call(this,e))._className="ChannelModule",o._socketHandler=new NI(Yn(o)),o._probing=!1,o._isAppShowing=!0,o._previousState=so.NET_STATE_CONNECTED,ii&&"function"==typeof ci.onAppShow&&"function"==typeof ci.onAppHide){var r=o._onAppHide.bind(Yn(o)),a=o._onAppShow.bind(Yn(o));"function"==typeof ci.offAppHide&&ci.offAppHide(r),"function"==typeof ci.offAppShow&&ci.offAppShow(a),ci.onAppHide(r),ci.onAppShow(a)}return o._timerForNotLoggedIn=-1,o._timerForNotLoggedIn=setInterval(o.onCheckTimer.bind(Yn(o)),1e3),o._fatalErrorFlag=!1,o}return Un(n,[{key:"onCheckTimer",value:function(e){this._socketHandler&&(this.isLoggedIn()?(this._timerForNotLoggedIn>0&&(clearInterval(this._timerForNotLoggedIn),this._timerForNotLoggedIn=-1),this._socketHandler.onCheckTimer(e)):this._socketHandler.onCheckTimer(1),this._checkNextPing())}},{key:"onErrorCodeNotZero",value:function(e){this.getModule(al).onErrorCodeNotZero(e)}},{key:"onMessage",value:function(e){this.getModule(al).onMessage(e)}},{key:"send",value:function(e){return this._socketHandler?this._previousState!==so.NET_STATE_CONNECTED&&e.head.servcmd.includes(Md)?(this.reConnect(),this._sendLogViaHTTP(e)):this._socketHandler.send(e):Promise.reject()}},{key:"_sendLogViaHTTP",value:function(e){var t=Js.HOST.CURRENT.STAT;return new Promise((function(n,o){var r="".concat(t,"/v4/imopenstat/tim_web_report_v2?sdkappid=").concat(e.head.sdkappid,"&reqtime=").concat(Date.now()),a=JSON.stringify(e.body),s="application/x-www-form-urlencoded;charset=UTF-8";if(ii)ci.request({url:r,data:a,method:"POST",timeout:3e3,header:{"content-type":s},success:function(){n()},fail:function(){o(new ym({code:Ld.NETWORK_ERROR,message:wp}))}});else{var i=new XMLHttpRequest,u=setTimeout((function(){i.abort(),o(new ym({code:Ld.NETWORK_TIMEOUT,message:Gp}))}),3e3);i.onreadystatechange=function(){4===i.readyState&&(clearTimeout(u),200===i.status||304===i.status?n():o(new ym({code:Ld.NETWORK_ERROR,message:wp})))},i.open("POST",r,!0),i.setRequestHeader("Content-type",s),i.send(a)}}))}},{key:"simplySend",value:function(e){return this._socketHandler?this._socketHandler.simplySend(e):Promise.reject()}},{key:"onOpen",value:function(){this._ping()}},{key:"onClose",value:function(){this._socketHandler&&this._socketHandler.getReconnectFlag()&&this._emitNetStateChangeEvent(so.NET_STATE_DISCONNECTED),this.reConnect()}},{key:"onError",value:function(){ii&&Bi.error("".concat(this._className,".onError 从v2.11.2起，SDK 支持了 WebSocket，如您未添加相关受信域名，请先添加！升级指引: https://web.sdk.qcloud.com/im/doc/zh-cn/tutorial-02-upgradeguideline.html"))}},{key:"getKeyMap",value:function(e){return this.getModule(al).getKeyMap(e)}},{key:"_onAppHide",value:function(){this._isAppShowing=!1}},{key:"_onAppShow",value:function(){this._isAppShowing=!0}},{key:"onRequestTimeout",value:function(e){}},{key:"onReconnected",value:function(){Bi.log("".concat(this._className,".onReconnected")),this.getModule(al).onReconnected(),this._emitNetStateChangeEvent(so.NET_STATE_CONNECTED)}},{key:"onReconnectFailed",value:function(){Bi.log("".concat(this._className,".onReconnectFailed")),this._emitNetStateChangeEvent(so.NET_STATE_DISCONNECTED)}},{key:"reConnect",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=!1;this._socketHandler&&(t=this._socketHandler.getReconnectFlag());var n="forcedFlag:".concat(e," fatalErrorFlag:").concat(this._fatalErrorFlag," previousState:").concat(this._previousState," reconnectFlag:").concat(t);if(Bi.log("".concat(this._className,".reConnect ").concat(n)),!this._fatalErrorFlag&&this._socketHandler){if(!0===e)this._socketHandler.forcedReconnect();else{if(this._previousState===so.NET_STATE_CONNECTING&&t)return;this._socketHandler.forcedReconnect()}this._emitNetStateChangeEvent(so.NET_STATE_CONNECTING)}}},{key:"_emitNetStateChangeEvent",value:function(e){this._previousState!==e&&(Bi.log("".concat(this._className,"._emitNetStateChangeEvent from ").concat(this._previousState," to ").concat(e)),this._previousState=e,this.emitOuterEvent(ao.NET_STATE_CHANGE,{state:e}))}},{key:"_ping",value:function(){var e=this;if(!0!==this._probing){this._probing=!0;var t=this.getModule(al).getProtocolData({protocolName:yd});this.send(t).then((function(){e._probing=!1})).catch((function(t){if(Bi.warn("".concat(e._className,"._ping failed. error:"),t),e._probing=!1,t&&60002===t.code)return new og(Nh).setMessage("code:".concat(t.code," message:").concat(t.message)).setNetworkType(e.getModule(el).getNetworkType()).end(),e._fatalErrorFlag=!0,void e._emitNetStateChangeEvent(so.NET_STATE_DISCONNECTED);e.probeNetwork().then((function(t){var n=Xn(t,2),o=n[0],r=n[1];Bi.log("".concat(e._className,"._ping failed. probe network, isAppShowing:").concat(e._isAppShowing," online:").concat(o," networkType:").concat(r)),o?e.reConnect():e._emitNetStateChangeEvent(so.NET_STATE_DISCONNECTED)}))}))}}},{key:"_checkNextPing",value:function(){this._socketHandler&&this._socketHandler.isConnected()&&Date.now()>=this._socketHandler.getNextPingTs()&&this._ping()}},{key:"dealloc",value:function(){this._socketHandler&&(this._socketHandler.close(),this._socketHandler=null),this._timerForNotLoggedIn>-1&&clearInterval(this._timerForNotLoggedIn)}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._previousState=so.NET_STATE_CONNECTED,this._probing=!1,this._fatalErrorFlag=!1,this._timerForNotLoggedIn=setInterval(this.onCheckTimer.bind(this),1e3)}}]),n}(gl),LI=function(){function e(t){Gn(this,e),this._className="ProtocolHandler",this._sessionModule=t,this._configMap=new Map,this._fillConfigMap()}return Un(e,[{key:"_fillConfigMap",value:function(){this._configMap.clear();var e=this._sessionModule.genCommonHead(),t=this._sessionModule.genCosSpecifiedHead(),n=this._sessionModule.genSSOReportHead();this._configMap.set(hl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_OPEN_STATUS,".").concat(Js.CMD.LOGIN)}),body:{state:"Online"},keyMap:{response:{TinyId:"tinyID",InstId:"instanceID",HelloInterval:"helloInterval"}}}}(e)),this._configMap.set(fl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_OPEN_STATUS,".").concat(Js.CMD.LOGOUT)}),body:{type:0},keyMap:{request:{type:"wslogout_type"}}}}(e)),this._configMap.set(_l,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_OPEN_STATUS,".").concat(Js.CMD.HELLO)}),body:{},keyMap:{response:{NewInstInfo:"newInstanceInfo"}}}}(e)),this._configMap.set(ml,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.STAT_SERVICE,".").concat(Js.CMD.KICK_OTHER)}),body:{}}}(e)),this._configMap.set(md,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_COS_SIGN,".").concat(Js.CMD.COS_SIGN)}),body:{cmd:"open_im_cos_svc",subCmd:"get_cos_token",duration:300,version:2},keyMap:{request:{userSig:"usersig",subCmd:"sub_cmd",cmd:"cmd",duration:"duration",version:"version"},response:{expired_time:"expiredTime",bucket_name:"bucketName",session_token:"sessionToken",tmp_secret_id:"secretId",tmp_secret_key:"secretKey"}}}}(t)),this._configMap.set(vd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.CUSTOM_UPLOAD,".").concat(Js.CMD.COS_PRE_SIG)}),body:{fileType:void 0,fileName:void 0,uploadMethod:0,duration:900},keyMap:{request:{userSig:"usersig",fileType:"file_type",fileName:"file_name",uploadMethod:"upload_method"},response:{expired_time:"expiredTime",request_id:"requestId",head_url:"headUrl",upload_url:"uploadUrl",download_url:"downloadUrl",ci_url:"ciUrl"}}}}(t)),this._configMap.set(Dd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_CONFIG_MANAGER,".").concat(Js.CMD.FETCH_COMMERCIAL_CONFIG)}),body:{SDKAppID:0},keyMap:{request:{SDKAppID:"uint32_sdkappid"},response:{int32_error_code:"errorCode",str_error_message:"errorMessage",str_purchase_bits:"purchaseBits",uint32_expired_time:"expiredTime"}}}}(e)),this._configMap.set(Nd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_CONFIG_MANAGER,".").concat(Js.CMD.PUSHED_COMMERCIAL_CONFIG)}),body:{},keyMap:{response:{int32_error_code:"errorCode",str_error_message:"errorMessage",str_purchase_bits:"purchaseBits",uint32_expired_time:"expiredTime"}}}}(e)),this._configMap.set(Ed,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_CONFIG_MANAGER,".").concat(Js.CMD.FETCH_CLOUD_CONTROL_CONFIG)}),body:{SDKAppID:0,version:0},keyMap:{request:{SDKAppID:"uint32_sdkappid",version:"uint64_version"},response:{int32_error_code:"errorCode",str_error_message:"errorMessage",str_json_config:"cloudControlConfig",uint32_expired_time:"expiredTime",uint32_sdkappid:"SDKAppID",uint64_version:"version"}}}}(e)),this._configMap.set(kd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_CONFIG_MANAGER,".").concat(Js.CMD.PUSHED_CLOUD_CONTROL_CONFIG)}),body:{},keyMap:{response:{int32_error_code:"errorCode",str_error_message:"errorMessage",str_json_config:"cloudControlConfig",uint32_expired_time:"expiredTime",uint32_sdkappid:"SDKAppID",uint64_version:"version"}}}}(e)),this._configMap.set(Od,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OVERLOAD_PUSH,".").concat(Js.CMD.OVERLOAD_NOTIFY)}),body:{},keyMap:{response:{OverLoadServCmd:"overloadCommand",DelaySecs:"waitingTime"}}}}(e)),this._configMap.set(vl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.GET_MESSAGES)}),body:{cookie:"",syncFlag:0,needAbstract:1,isOnlineSync:0},keyMap:{request:{fromAccount:"From_Account",toAccount:"To_Account",from:"From_Account",to:"To_Account",time:"MsgTimeStamp",sequence:"MsgSeq",random:"MsgRandom",elements:"MsgBody"},response:{MsgList:"messageList",SyncFlag:"syncFlag",To_Account:"to",From_Account:"from",ClientSeq:"clientSequence",MsgSeq:"sequence",NoticeSeq:"noticeSequence",NotifySeq:"notifySequence",MsgRandom:"random",MsgTimeStamp:"time",MsgContent:"content",ToGroupId:"groupID",MsgKey:"messageKey",GroupTips:"groupTips",MsgBody:"elements",MsgType:"type",C2CRemainingUnreadCount:"C2CRemainingUnreadList",C2CPairUnreadCount:"C2CPairUnreadList"}}}}(e)),this._configMap.set(Ml,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.BIG_DATA_HALLWAY_AUTH_KEY)}),body:{}}}(e)),this._configMap.set(yl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.SEND_MESSAGE)}),body:{fromAccount:"",toAccount:"",msgTimeStamp:void 0,msgSeq:0,msgRandom:0,msgBody:[],cloudCustomData:void 0,nick:"",avatar:"",msgLifeTime:void 0,offlinePushInfo:{pushFlag:0,title:"",desc:"",ext:"",apnsInfo:{badgeMode:0},androidInfo:{OPPOChannelID:""}},messageControlInfo:void 0},keyMap:{request:{fromAccount:"From_Account",toAccount:"To_Account",msgTimeStamp:"MsgTimeStamp",msgSeq:"MsgSeq",msgRandom:"MsgRandom",msgBody:"MsgBody",count:"MaxCnt",lastMessageTime:"LastMsgTime",messageKey:"MsgKey",peerAccount:"Peer_Account",data:"Data",description:"Desc",extension:"Ext",type:"MsgType",content:"MsgContent",sizeType:"Type",uuid:"UUID",url:"",imageUrl:"URL",fileUrl:"Url",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",videoUrl:"",downloadFlag:"Download_Flag",nick:"From_AccountNick",avatar:"From_AccountHeadurl",from:"From_Account",time:"MsgTimeStamp",messageRandom:"MsgRandom",messageSequence:"MsgSeq",elements:"MsgBody",clientSequence:"ClientSeq",payload:"MsgContent",messageList:"MsgList",messageNumber:"MsgNum",abstractList:"AbstractList",messageBody:"MsgBody"}}}}(e)),this._configMap.set(Il,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.SEND_GROUP_MESSAGE)}),body:{fromAccount:"",groupID:"",random:0,clientSequence:0,priority:"",msgBody:[],cloudCustomData:void 0,onlineOnlyFlag:0,offlinePushInfo:{pushFlag:0,title:"",desc:"",ext:"",apnsInfo:{badgeMode:0},androidInfo:{OPPOChannelID:""}},groupAtInfo:[],messageControlInfo:void 0},keyMap:{request:{to:"GroupId",extension:"Ext",data:"Data",description:"Desc",random:"Random",sequence:"ReqMsgSeq",count:"ReqMsgNumber",type:"MsgType",priority:"MsgPriority",content:"MsgContent",elements:"MsgBody",sizeType:"Type",uuid:"UUID",url:"",imageUrl:"URL",fileUrl:"Url",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",videoUrl:"",downloadFlag:"Download_Flag",clientSequence:"ClientSeq",from:"From_Account",time:"MsgTimeStamp",messageRandom:"MsgRandom",messageSequence:"MsgSeq",payload:"MsgContent",messageList:"MsgList",messageNumber:"MsgNum",abstractList:"AbstractList",messageBody:"MsgBody"},response:{MsgTime:"time",MsgSeq:"sequence"}}}}(e)),this._configMap.set(kl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.REVOKE_C2C_MESSAGE)}),body:{msgInfo:{fromAccount:"",toAccount:"",msgTimeStamp:0,msgSeq:0,msgRandom:0}},keyMap:{request:{msgInfo:"MsgInfo",msgTimeStamp:"MsgTimeStamp",msgSeq:"MsgSeq",msgRandom:"MsgRandom"}}}}(e)),this._configMap.set(Xl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.REVOKE_GROUP_MESSAGE)}),body:{to:"",msgSeqList:void 0},keyMap:{request:{to:"GroupId",msgSeqList:"MsgSeqList",msgSeq:"MsgSeq"}}}}(e)),this._configMap.set(Ll,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.GET_C2C_ROAM_MESSAGES)}),body:{peerAccount:"",count:15,lastMessageTime:0,messageKey:"",withRecalledMessage:1},keyMap:{request:{messageKey:"MsgKey",peerAccount:"Peer_Account",count:"MaxCnt",lastMessageTime:"LastMsgTime",withRecalledMessage:"WithRecalledMsg"},response:{LastMsgTime:"lastMessageTime"}}}}(e)),this._configMap.set(ed,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.GET_GROUP_ROAM_MESSAGES)}),body:{withRecalledMsg:1,groupID:"",count:15,sequence:""},keyMap:{request:{sequence:"ReqMsgSeq",count:"ReqMsgNumber",withRecalledMessage:"WithRecalledMsg"},response:{Random:"random",MsgTime:"time",MsgSeq:"sequence",ReqMsgSeq:"sequence",RspMsgList:"messageList",IsPlaceMsg:"isPlaceMessage",IsSystemMsg:"isSystemMessage",ToGroupId:"to",EnumFrom_AccountType:"fromAccountType",EnumTo_AccountType:"toAccountType",GroupCode:"groupCode",MsgPriority:"priority",MsgBody:"elements",MsgType:"type",MsgContent:"content",IsFinished:"complete",Download_Flag:"downloadFlag",ClientSeq:"clientSequence",ThumbUUID:"thumbUUID",VideoUUID:"videoUUID"}}}}(e)),this._configMap.set(Dl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.SET_C2C_MESSAGE_READ)}),body:{C2CMsgReaded:void 0},keyMap:{request:{lastMessageTime:"LastedMsgTime"}}}}(e)),this._configMap.set(Nl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.SET_C2C_PEER_MUTE_NOTIFICATIONS)}),body:{userIDList:void 0,muteFlag:0},keyMap:{request:{userIDList:"Peer_Account",muteFlag:"Mute_Notifications"}}}}(e)),this._configMap.set(Ol,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.GET_C2C_PEER_MUTE_NOTIFICATIONS)}),body:{updateSequence:0},keyMap:{response:{MuteNotificationsList:"muteFlagList"}}}}(e)),this._configMap.set(Ql,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.SET_GROUP_MESSAGE_READ)}),body:{groupID:void 0,messageReadSeq:void 0},keyMap:{request:{messageReadSeq:"MsgReadedSeq"}}}}(e)),this._configMap.set(Zl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.SET_ALL_MESSAGE_READ)}),body:{readAllC2CMessage:0,groupMessageReadInfoList:[]},keyMap:{request:{readAllC2CMessage:"C2CReadAllMsg",groupMessageReadInfoList:"GroupReadInfo",messageSequence:"MsgSeq"},response:{C2CReadAllMsg:"readAllC2CMessage",GroupReadInfoArray:"groupMessageReadInfoList"}}}}(e)),this._configMap.set(bl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.DELETE_C2C_MESSAGE)}),body:{fromAccount:"",to:"",keyList:void 0},keyMap:{request:{keyList:"MsgKeyList"}}}}(e)),this._configMap.set(sd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.DELETE_GROUP_MESSAGE)}),body:{groupID:"",deleter:"",keyList:void 0},keyMap:{request:{deleter:"Deleter_Account",keyList:"Seqs"}}}}(e)),this._configMap.set(Rl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.GET_PEER_READ_TIME)}),body:{userIDList:void 0},keyMap:{request:{userIDList:"To_Account"},response:{ReadTime:"peerReadTimeList"}}}}(e)),this._configMap.set(Gl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.RECENT_CONTACT,".").concat(Js.CMD.GET_CONVERSATION_LIST)}),body:{fromAccount:void 0,count:0},keyMap:{request:{},response:{SessionItem:"conversations",ToAccount:"groupID",To_Account:"userID",UnreadMsgCount:"unreadCount",MsgGroupReadedSeq:"messageReadSeq",C2cPeerReadTime:"c2cPeerReadTime"}}}}(e)),this._configMap.set(wl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.RECENT_CONTACT,".").concat(Js.CMD.PAGING_GET_CONVERSATION_LIST)}),body:{fromAccount:void 0,timeStamp:void 0,startIndex:void 0,pinnedTimeStamp:void 0,pinnedStartIndex:void 0,orderType:void 0,messageAssistFlag:4,assistFlag:7},keyMap:{request:{messageAssistFlag:"MsgAssistFlags",assistFlag:"AssistFlags",pinnedTimeStamp:"TopTimeStamp",pinnedStartIndex:"TopStartIndex"},response:{SessionItem:"conversations",ToAccount:"groupID",To_Account:"userID",UnreadMsgCount:"unreadCount",MsgGroupReadedSeq:"messageReadSeq",C2cPeerReadTime:"c2cPeerReadTime",LastMsgFlags:"lastMessageFlag",TopFlags:"isPinned",TopTimeStamp:"pinnedTimeStamp",TopStartIndex:"pinnedStartIndex"}}}}(e)),this._configMap.set(Pl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.RECENT_CONTACT,".").concat(Js.CMD.DELETE_CONVERSATION)}),body:{fromAccount:"",toAccount:void 0,type:1,toGroupID:void 0,clearHistoryMessage:1},keyMap:{request:{toGroupID:"ToGroupid",clearHistoryMessage:"ClearRamble"}}}}(e)),this._configMap.set(Ul,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.RECENT_CONTACT,".").concat(Js.CMD.PIN_CONVERSATION)}),body:{fromAccount:"",operationType:1,itemList:void 0},keyMap:{request:{itemList:"RecentContactItem"}}}}(e)),this._configMap.set(Fl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.DELETE_GROUP_AT_TIPS)}),body:{messageListToDelete:void 0},keyMap:{request:{messageListToDelete:"DelMsgList",messageSeq:"MsgSeq",messageRandom:"MsgRandom"}}}}(e)),this._configMap.set(Tl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.PROFILE,".").concat(Js.CMD.PORTRAIT_GET)}),body:{fromAccount:"",userItem:[]},keyMap:{request:{toAccount:"To_Account",standardSequence:"StandardSequence",customSequence:"CustomSequence"}}}}(e)),this._configMap.set(Cl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.PROFILE,".").concat(Js.CMD.PORTRAIT_SET)}),body:{fromAccount:"",profileItem:[{tag:V_.NICK,value:""},{tag:V_.GENDER,value:""},{tag:V_.ALLOWTYPE,value:""},{tag:V_.AVATAR,value:""}]},keyMap:{request:{toAccount:"To_Account",standardSequence:"StandardSequence",customSequence:"CustomSequence"}}}}(e)),this._configMap.set(Sl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.FRIEND,".").concat(Js.CMD.GET_BLACKLIST)}),body:{fromAccount:"",startIndex:0,maxLimited:30,lastSequence:0},keyMap:{response:{CurruentSequence:"currentSequence"}}}}(e)),this._configMap.set(Al,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.FRIEND,".").concat(Js.CMD.ADD_BLACKLIST)}),body:{fromAccount:"",toAccount:[]}}}(e)),this._configMap.set(El,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.FRIEND,".").concat(Js.CMD.DELETE_BLACKLIST)}),body:{fromAccount:"",toAccount:[]}}}(e)),this._configMap.set(ql,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.GET_JOINED_GROUPS)}),body:{memberAccount:"",limit:void 0,offset:void 0,groupType:void 0,responseFilter:{groupBaseInfoFilter:void 0,selfInfoFilter:void 0}},keyMap:{request:{memberAccount:"Member_Account"},response:{GroupIdList:"groups",MsgFlag:"messageRemindType",NoUnreadSeqList:"excludedUnreadSequenceList",MsgSeq:"readedSequence"}}}}(e)),this._configMap.set(xl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.GET_GROUP_INFO)}),body:{groupIDList:void 0,responseFilter:{groupBaseInfoFilter:["Type","Name","Introduction","Notification","FaceUrl","Owner_Account","CreateTime","InfoSeq","LastInfoTime","LastMsgTime","MemberNum","MaxMemberNum","ApplyJoinOption","NextMsgSeq","ShutUpAllMember"],groupCustomFieldFilter:void 0,memberInfoFilter:void 0,memberCustomFieldFilter:void 0}},keyMap:{request:{groupIDList:"GroupIdList",groupCustomField:"AppDefinedData",memberCustomField:"AppMemberDefinedData",groupCustomFieldFilter:"AppDefinedDataFilter_Group",memberCustomFieldFilter:"AppDefinedDataFilter_GroupMember"},response:{GroupIdList:"groups",MsgFlag:"messageRemindType",AppDefinedData:"groupCustomField",AppMemberDefinedData:"memberCustomField",AppDefinedDataFilter_Group:"groupCustomFieldFilter",AppDefinedDataFilter_GroupMember:"memberCustomFieldFilter",InfoSeq:"infoSequence",MemberList:"members",GroupInfo:"groups",ShutUpUntil:"muteUntil",ShutUpAllMember:"muteAllMembers",ApplyJoinOption:"joinOption"}}}}(e)),this._configMap.set(Vl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.CREATE_GROUP)}),body:{type:void 0,name:void 0,groupID:void 0,ownerID:void 0,introduction:void 0,notification:void 0,maxMemberNum:void 0,joinOption:void 0,memberList:void 0,groupCustomField:void 0,memberCustomField:void 0,webPushFlag:1,avatar:"FaceUrl"},keyMap:{request:{ownerID:"Owner_Account",userID:"Member_Account",avatar:"FaceUrl",maxMemberNum:"MaxMemberCount",joinOption:"ApplyJoinOption",groupCustomField:"AppDefinedData",memberCustomField:"AppMemberDefinedData"},response:{HugeGroupFlag:"avChatRoomFlag",OverJoinedGroupLimit_Account:"overLimitUserIDList"}}}}(e)),this._configMap.set(Kl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.DESTROY_GROUP)}),body:{groupID:void 0}}}(e)),this._configMap.set(Bl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.MODIFY_GROUP_INFO)}),body:{groupID:void 0,name:void 0,introduction:void 0,notification:void 0,avatar:void 0,maxMemberNum:void 0,joinOption:void 0,groupCustomField:void 0,muteAllMembers:void 0},keyMap:{request:{maxMemberNum:"MaxMemberCount",groupCustomField:"AppDefinedData",muteAllMembers:"ShutUpAllMember",joinOption:"ApplyJoinOption",avatar:"FaceUrl"},response:{AppDefinedData:"groupCustomField",ShutUpAllMember:"muteAllMembers",ApplyJoinOption:"joinOption"}}}}(e)),this._configMap.set(Hl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.APPLY_JOIN_GROUP)}),body:{groupID:void 0,applyMessage:void 0,userDefinedField:void 0,webPushFlag:1,historyMessageFlag:void 0},keyMap:{request:{applyMessage:"ApplyMsg",historyMessageFlag:"HugeGroupHistoryMsgFlag"},response:{HugeGroupFlag:"avChatRoomFlag",AVChatRoomKey:"avChatRoomKey",RspMsgList:"messageList",ToGroupId:"to"}}}}(e)),this._configMap.set(jl,function(e){return e.a2,e.tinyid,{head:xn(xn({},$n(e,["a2","tinyid"])),{},{servcmd:"".concat(Js.NAME.BIG_GROUP_NO_AUTH,".").concat(Js.CMD.APPLY_JOIN_GROUP)}),body:{groupID:void 0,applyMessage:void 0,userDefinedField:void 0,webPushFlag:1},keyMap:{request:{applyMessage:"ApplyMsg"},response:{HugeGroupFlag:"avChatRoomFlag"}}}}(e)),this._configMap.set(Wl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.QUIT_GROUP)}),body:{groupID:void 0}}}(e)),this._configMap.set($l,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.SEARCH_GROUP_BY_ID)}),body:{groupIDList:void 0,responseFilter:{groupBasePublicInfoFilter:["Type","Name","Introduction","Notification","FaceUrl","CreateTime","Owner_Account","LastInfoTime","LastMsgTime","NextMsgSeq","MemberNum","MaxMemberNum","ApplyJoinOption"]}},keyMap:{response:{ApplyJoinOption:"joinOption"}}}}(e)),this._configMap.set(Yl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.CHANGE_GROUP_OWNER)}),body:{groupID:void 0,newOwnerID:void 0},keyMap:{request:{newOwnerID:"NewOwner_Account"}}}}(e)),this._configMap.set(zl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.HANDLE_APPLY_JOIN_GROUP)}),body:{groupID:void 0,applicant:void 0,handleAction:void 0,handleMessage:void 0,authentication:void 0,messageKey:void 0,userDefinedField:void 0},keyMap:{request:{applicant:"Applicant_Account",handleAction:"HandleMsg",handleMessage:"ApprovalMsg",messageKey:"MsgKey"}}}}(e)),this._configMap.set(Jl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.HANDLE_GROUP_INVITATION)}),body:{groupID:void 0,inviter:void 0,handleAction:void 0,handleMessage:void 0,authentication:void 0,messageKey:void 0,userDefinedField:void 0},keyMap:{request:{inviter:"Inviter_Account",handleAction:"HandleMsg",handleMessage:"ApprovalMsg",messageKey:"MsgKey"}}}}(e)),this._configMap.set(td,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.GET_GROUP_APPLICATION)}),body:{startTime:void 0,limit:void 0,handleAccount:void 0},keyMap:{request:{handleAccount:"Handle_Account"}}}}(e)),this._configMap.set(nd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.DELETE_GROUP_SYSTEM_MESSAGE)}),body:{messageListToDelete:void 0},keyMap:{request:{messageListToDelete:"DelMsgList",messageSeq:"MsgSeq",messageRandom:"MsgRandom"}}}}(e)),this._configMap.set(od,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.BIG_GROUP_LONG_POLLING,".").concat(Js.CMD.AVCHATROOM_LONG_POLL)}),body:{USP:1,startSeq:1,holdTime:90,key:void 0},keyMap:{request:{USP:"USP"},response:{ToGroupId:"groupID"}}}}(e)),this._configMap.set(rd,function(e){return e.a2,e.tinyid,{head:xn(xn({},$n(e,["a2","tinyid"])),{},{servcmd:"".concat(Js.NAME.BIG_GROUP_LONG_POLLING_NO_AUTH,".").concat(Js.CMD.AVCHATROOM_LONG_POLL)}),body:{USP:1,startSeq:1,holdTime:90,key:void 0},keyMap:{request:{USP:"USP"},response:{ToGroupId:"groupID"}}}}(e)),this._configMap.set(ad,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.GET_ONLINE_MEMBER_NUM)}),body:{groupID:void 0}}}(e)),this._configMap.set(id,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.SET_GROUP_ATTRIBUTES)}),body:{groupID:void 0,groupAttributeList:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]},keyMap:{request:{key:"key",value:"value"}}}}(e)),this._configMap.set(ud,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.MODIFY_GROUP_ATTRIBUTES)}),body:{groupID:void 0,groupAttributeList:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]},keyMap:{request:{key:"key",value:"value"}}}}(e)),this._configMap.set(cd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.DELETE_GROUP_ATTRIBUTES)}),body:{groupID:void 0,groupAttributeList:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]},keyMap:{request:{key:"key"}}}}(e)),this._configMap.set(ld,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.CLEAR_GROUP_ATTRIBUTES)}),body:{groupID:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]}}}(e)),this._configMap.set(dd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP_ATTR,".").concat(Js.CMD.GET_GROUP_ATTRIBUTES)}),body:{groupID:void 0,avChatRoomKey:void 0,groupType:1},keyMap:{request:{avChatRoomKey:"Key",groupType:"GroupType"}}}}(e)),this._configMap.set(pd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.GET_GROUP_MEMBER_LIST)}),body:{groupID:void 0,limit:0,offset:0,memberRoleFilter:void 0,memberInfoFilter:["Role","NameCard","ShutUpUntil","JoinTime"],memberCustomFieldFilter:void 0},keyMap:{request:{memberCustomFieldFilter:"AppDefinedDataFilter_GroupMember"},response:{AppMemberDefinedData:"memberCustomField",AppDefinedDataFilter_GroupMember:"memberCustomFieldFilter",MemberList:"members",ShutUpUntil:"muteUntil"}}}}(e)),this._configMap.set(gd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.GET_GROUP_MEMBER_INFO)}),body:{groupID:void 0,userIDList:void 0,memberInfoFilter:void 0,memberCustomFieldFilter:void 0},keyMap:{request:{userIDList:"Member_List_Account",memberCustomFieldFilter:"AppDefinedDataFilter_GroupMember"},response:{MemberList:"members",ShutUpUntil:"muteUntil",AppDefinedDataFilter_GroupMember:"memberCustomFieldFilter",AppMemberDefinedData:"memberCustomField"}}}}(e)),this._configMap.set(hd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.ADD_GROUP_MEMBER)}),body:{groupID:void 0,silence:void 0,userIDList:void 0},keyMap:{request:{userID:"Member_Account",userIDList:"MemberList"},response:{MemberList:"members"}}}}(e)),this._configMap.set(fd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.DELETE_GROUP_MEMBER)}),body:{groupID:void 0,userIDList:void 0,reason:void 0},keyMap:{request:{userIDList:"MemberToDel_Account"}}}}(e)),this._configMap.set(_d,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.MODIFY_GROUP_MEMBER_INFO)}),body:{groupID:void 0,userID:void 0,messageRemindType:void 0,nameCard:void 0,role:void 0,memberCustomField:void 0,muteTime:void 0},keyMap:{request:{userID:"Member_Account",memberCustomField:"AppMemberDefinedData",muteTime:"ShutUpTime",messageRemindType:"MsgFlag"}}}}(e)),this._configMap.set(Md,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_OPEN_STAT,".").concat(Js.CMD.TIM_WEB_REPORT_V2)}),body:{header:{},event:[],quality:[]},keyMap:{request:{SDKType:"sdk_type",SDKVersion:"sdk_version",deviceType:"device_type",platform:"platform",instanceID:"instance_id",traceID:"trace_id",SDKAppID:"sdk_app_id",userID:"user_id",tinyID:"tiny_id",extension:"extension",timestamp:"timestamp",networkType:"network_type",eventType:"event_type",code:"error_code",message:"error_message",moreMessage:"more_message",duplicate:"duplicate",costTime:"cost_time",level:"level",qualityType:"quality_type",reportIndex:"report_index",wholePeriod:"whole_period",totalCount:"total_count",rttCount:"success_count_business",successRateOfRequest:"percent_business",countLessThan1Second:"success_count_business",percentOfCountLessThan1Second:"percent_business",countLessThan3Second:"success_count_platform",percentOfCountLessThan3Second:"percent_platform",successCountOfBusiness:"success_count_business",successRateOfBusiness:"percent_business",successCountOfPlatform:"success_count_platform",successRateOfPlatform:"percent_platform",successCountOfMessageReceived:"success_count_business",successRateOfMessageReceived:"percent_business",avgRTT:"average_value",avgDelay:"average_value",avgValue:"average_value"}}}}(n)),this._configMap.set(yd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.HEARTBEAT,".").concat(Js.CMD.ALIVE)}),body:{}}}(e)),this._configMap.set(Id,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_OPEN_PUSH,".").concat(Js.CMD.MESSAGE_PUSH)}),body:{},keyMap:{response:{C2cMsgArray:"C2CMessageArray",GroupMsgArray:"groupMessageArray",GroupTips:"groupTips",C2cNotifyMsgArray:"C2CNotifyMessageArray",ClientSeq:"clientSequence",MsgPriority:"priority",NoticeSeq:"noticeSequence",MsgContent:"content",MsgType:"type",MsgBody:"elements",ToGroupId:"to",Desc:"description",Ext:"extension",IsSyncMsg:"isSyncMessage",Flag:"needSync",NeedAck:"needAck",PendencyAdd_Account:"userID",ProfileImNick:"nick",PendencyType:"applicationType",C2CReadAllMsg:"readAllC2CMessage"}}}}(e)),this._configMap.set(Td,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.MESSAGE_PUSH_ACK)}),body:{sessionData:void 0},keyMap:{request:{sessionData:"SessionData"}}}}(e)),this._configMap.set(Cd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_OPEN_STATUS,".").concat(Js.CMD.STATUS_FORCEOFFLINE)}),body:{},keyMap:{response:{C2cNotifyMsgArray:"C2CNotifyMessageArray",NoticeSeq:"noticeSequence",KickoutMsgNotify:"kickoutMsgNotify",NewInstInfo:"newInstanceInfo"}}}}(e)),this._configMap.set(Ad,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_LONG_MESSAGE,".").concat(Js.CMD.DOWNLOAD_MERGER_MESSAGE)}),body:{downloadKey:""},keyMap:{response:{Data:"data",Desc:"description",Ext:"extension",Download_Flag:"downloadFlag",ThumbUUID:"thumbUUID",VideoUUID:"videoUUID"}}}}(e)),this._configMap.set(Sd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_LONG_MESSAGE,".").concat(Js.CMD.UPLOAD_MERGER_MESSAGE)}),body:{messageList:[]},keyMap:{request:{fromAccount:"From_Account",toAccount:"To_Account",msgTimeStamp:"MsgTimeStamp",msgSeq:"MsgSeq",msgRandom:"MsgRandom",msgBody:"MsgBody",type:"MsgType",content:"MsgContent",data:"Data",description:"Desc",extension:"Ext",sizeType:"Type",uuid:"UUID",url:"",imageUrl:"URL",fileUrl:"Url",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",videoUrl:"",downloadFlag:"Download_Flag",from:"From_Account",time:"MsgTimeStamp",messageRandom:"MsgRandom",messageSequence:"MsgSeq",elements:"MsgBody",clientSequence:"ClientSeq",payload:"MsgContent",messageList:"MsgList",messageNumber:"MsgNum",abstractList:"AbstractList",messageBody:"MsgBody"}}}}(e))}},{key:"has",value:function(e){return this._configMap.has(e)}},{key:"get",value:function(e){return this._configMap.get(e)}},{key:"update",value:function(){this._fillConfigMap()}},{key:"getKeyMap",value:function(e){return this.has(e)?this.get(e).keyMap||{}:(Bi.warn("".concat(this._className,".getKeyMap unknown protocolName:").concat(e)),{})}},{key:"getProtocolData",value:function(e){var t=e.protocolName,n=e.requestData,o=this.get(t),r=null;if(n){var a=this._simpleDeepCopy(o),s=a.body,i=Object.create(null);for(var u in s)if(Object.prototype.hasOwnProperty.call(s,u)){if(i[u]=s[u],void 0===n[u])continue;i[u]=n[u]}a.body=i,r=this._getUplinkData(a)}else r=this._getUplinkData(o);return r}},{key:"_getUplinkData",value:function(e){var t=this._requestDataCleaner(e),n=Eu(t.head),o=Hv(t.body,this._getRequestKeyMap(n));return t.body=o,t}},{key:"_getRequestKeyMap",value:function(e){var t=this.getKeyMap(e);return xn(xn({},xv.request),t.request)}},{key:"_requestDataCleaner",value:function(e){var t=Array.isArray(e)?[]:Object.create(null);for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&tu(n)&&null!==e[n]&&void 0!==e[n]&&("object"!==wn(e[n])?t[n]=e[n]:t[n]=this._requestDataCleaner.bind(this)(e[n]));return t}},{key:"_simpleDeepCopy",value:function(e){for(var t,n=Object.keys(e),o={},r=0,a=n.length;r<a;r++)t=n[r],zi(e[t])?o[t]=Array.from(e[t]):$i(e[t])?o[t]=this._simpleDeepCopy(e[t]):o[t]=e[t];return o}}]),e}(),RI=[Td],bI=function(){function e(t){Gn(this,e),this._sessionModule=t,this._className="DownlinkHandler",this._eventHandlerMap=new Map,this._eventHandlerMap.set("C2CMessageArray",this._c2cMessageArrayHandler.bind(this)),this._eventHandlerMap.set("groupMessageArray",this._groupMessageArrayHandler.bind(this)),this._eventHandlerMap.set("groupTips",this._groupTipsHandler.bind(this)),this._eventHandlerMap.set("C2CNotifyMessageArray",this._C2CNotifyMessageArrayHandler.bind(this)),this._eventHandlerMap.set("profileModify",this._profileHandler.bind(this)),this._eventHandlerMap.set("friendListMod",this._relationChainHandler.bind(this)),this._eventHandlerMap.set("recentContactMod",this._recentContactHandler.bind(this)),this._eventHandlerMap.set("readAllC2CMessage",this._allMessageReadHandler.bind(this)),this._keys=Qn(this._eventHandlerMap.keys())}return Un(e,[{key:"_c2cMessageArrayHandler",value:function(e){var t=this._sessionModule.getModule(Wc);t&&(e.dataList.forEach((function(e){if(1===e.isSyncMessage){var t=e.from;e.from=e.to,e.to=t}})),1===e.needSync&&this._sessionModule.getModule(rl).startOnlineSync(),t.onNewC2CMessage({dataList:e.dataList,isInstantMessage:!0}))}},{key:"_groupMessageArrayHandler",value:function(e){var t=this._sessionModule.getModule($c);t&&t.onNewGroupMessage({event:e.event,dataList:e.dataList,isInstantMessage:!0})}},{key:"_groupTipsHandler",value:function(e){var t=this._sessionModule.getModule($c);if(t){var n=e.event,o=e.dataList,r=e.isInstantMessage,a=void 0===r||r,s=e.isSyncingEnded;switch(n){case 4:case 6:t.onNewGroupTips({event:n,dataList:o});break;case 5:o.forEach((function(e){zi(e.elements.revokedInfos)?t.onGroupMessageRevoked({dataList:o}):zi(e.elements.groupMessageReadNotice)?t.onGroupMessageReadNotice({dataList:o}):t.onNewGroupSystemNotice({dataList:o,isInstantMessage:a,isSyncingEnded:s})}));break;case 12:this._sessionModule.getModule(Jc).onNewGroupAtTips({dataList:o});break;default:Bi.log("".concat(this._className,"._groupTipsHandler unknown event:").concat(n," dataList:"),o)}}}},{key:"_C2CNotifyMessageArrayHandler",value:function(e){var t=this,n=e.dataList;if(zi(n)){var o=this._sessionModule.getModule(Wc);n.forEach((function(e){if(Yi(e))if(e.hasOwnProperty("kickoutMsgNotify")){var r=e.kickoutMsgNotify,a=r.kickType,s=r.newInstanceInfo,i=void 0===s?{}:s;1===a?t._sessionModule.onMultipleAccountKickedOut(i):2===a&&t._sessionModule.onMultipleDeviceKickedOut(i)}else e.hasOwnProperty("c2cMessageRevokedNotify")?o&&o.onC2CMessageRevoked({dataList:n}):e.hasOwnProperty("c2cMessageReadReceipt")?o&&o.onC2CMessageReadReceipt({dataList:n}):e.hasOwnProperty("c2cMessageReadNotice")?o&&o.onC2CMessageReadNotice({dataList:n}):e.hasOwnProperty("muteNotificationsSync")&&t._sessionModule.getModule(Jc).onC2CMessageRemindTypeSynced({dataList:n})}))}}},{key:"_profileHandler",value:function(e){this._sessionModule.getModule(jc).onProfileModified({dataList:e.dataList});var t=this._sessionModule.getModule(Yc);t&&t.onFriendProfileModified({dataList:e.dataList})}},{key:"_relationChainHandler",value:function(e){this._sessionModule.getModule(jc).onRelationChainModified({dataList:e.dataList});var t=this._sessionModule.getModule(Yc);t&&t.onRelationChainModified({dataList:e.dataList})}},{key:"_recentContactHandler",value:function(e){var t=e.dataList;if(zi(t)){var n=this._sessionModule.getModule(Jc);n&&t.forEach((function(e){var t=e.pushType,o=e.recentContactTopItem,r=e.recentContactDeleteItem;1===t?n.onConversationDeleted(r.recentContactList):2===t?n.onConversationPinned(o.recentContactList):3===t&&n.onConversationUnpinned(o.recentContactList)}))}}},{key:"_allMessageReadHandler",value:function(e){var t=e.dataList,n=this._sessionModule.getModule(Jc);n&&n.onPushedAllMessageRead(t)}},{key:"onMessage",value:function(e){var t=this,n=e.body;if(this._filterMessageFromIMOpenPush(e)){var o=n.eventArray,r=n.isInstantMessage,a=n.isSyncingEnded,s=n.needSync;if(zi(o))for(var i=null,u=null,c=0,l=0,d=o.length;l<d;l++){c=(i=o[l]).event;var p=Object.keys(i).find((function(e){return-1!==t._keys.indexOf(e)}));p?(u=14!==c?i[p]:{readAllC2CMessage:i[p],groupMessageReadInfoList:i.groupMessageReadNotice||[]},this._eventHandlerMap.get(p)({event:c,dataList:u,isInstantMessage:r,isSyncingEnded:a,needSync:s})):Bi.log("".concat(this._className,".onMessage unknown eventItem:").concat(i))}}}},{key:"_filterMessageFromIMOpenPush",value:function(e){var t=e.head,n=e.body,o=t.servcmd,r=!1;if(Ji(o)||(r=o.includes(Js.NAME.IM_CONFIG_MANAGER)||o.includes(Js.NAME.OVERLOAD_PUSH)||o.includes(Js.NAME.STAT_SERVICE)),!r)return!0;if(o.includes(Js.CMD.PUSHED_CLOUD_CONTROL_CONFIG))this._sessionModule.getModule(ul).onPushedCloudControlConfig(n);else if(o.includes(Js.CMD.PUSHED_COMMERCIAL_CONFIG))this._sessionModule.getModule(pl).onPushedConfig(n);else if(o.includes(Js.CMD.OVERLOAD_NOTIFY))this._sessionModule.onPushedServerOverload(n);else if(o.includes(Js.CMD.KICK_OTHER)){var a=Date.now();this._sessionModule.reLoginOnKickOther();var s=new og(lg),i=this._sessionModule.getModule(Bc).getLastWsHelloTs(),u=a-i;s.setMessage("last wshello time:".concat(i," diff:").concat(u,"ms")).setNetworkType(this._sessionModule.getNetworkType()).end()}return!1}}]),e}(),wI=[{cmd:Js.CMD.GET_GROUP_INFO,interval:1,count:20},{cmd:Js.CMD.SET_GROUP_ATTRIBUTES,interval:5,count:10},{cmd:Js.CMD.MODIFY_GROUP_ATTRIBUTES,interval:5,count:10},{cmd:Js.CMD.DELETE_GROUP_ATTRIBUTES,interval:5,count:10},{cmd:Js.CMD.CLEAR_GROUP_ATTRIBUTES,interval:5,count:10},{cmd:Js.CMD.GET_GROUP_ATTRIBUTES,interval:5,count:20},{cmd:Js.CMD.SET_ALL_MESSAGE_READ,interval:1,count:1}],GI=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="SessionModule",o._platform=o.getPlatform(),o._protocolHandler=new LI(Yn(o)),o._messageDispatcher=new bI(Yn(o)),o._commandFrequencyLimitMap=new Map,o._commandRequestInfoMap=new Map,o._serverOverloadInfoMap=new Map,o._init(),o.getInnerEmitterInstance().on(Pm,o._onCloudConfigUpdated,Yn(o)),o}return Un(n,[{key:"_init",value:function(){this._updateCommandFrequencyLimitMap(wI)}},{key:"_onCloudConfigUpdated",value:function(){var e=this.getCloudConfig("cmd_frequency_limit");Ji(e)||(e=JSON.parse(e),this._updateCommandFrequencyLimitMap(e))}},{key:"_updateCommandFrequencyLimitMap",value:function(e){var t=this;e.forEach((function(e){t._commandFrequencyLimitMap.set(e.cmd,{interval:e.interval,count:e.count})}))}},{key:"updateProtocolConfig",value:function(){this._protocolHandler.update()}},{key:"request",value:function(e){Bi.debug("".concat(this._className,".request options:"),e);var t=e.protocolName,n=e.tjgID;if(!this._protocolHandler.has(t))return Bi.warn("".concat(this._className,".request unknown protocol:").concat(t)),Sm({code:Ld.CANNOT_FIND_PROTOCOL,message:Fp});var o=this.getProtocolData(e),r=o.head.servcmd;if(this._isFrequencyOverLimit(r))return Sm({code:Ld.OVER_FREQUENCY_LIMIT,message:Vp});if(this._isServerOverload(r))return Sm({code:Ld.OPEN_SERVICE_OVERLOAD_ERROR,message:Kp});Lu(n)||(o.head.tjgID=n);var a=this.getModule(sl);return RI.includes(t)?a.simplySend(o):a.send(o)}},{key:"getKeyMap",value:function(e){return this._protocolHandler.getKeyMap(e)}},{key:"genCommonHead",value:function(){var e=this.getModule(Xc);return{ver:"v4",platform:this._platform,websdkappid:Bs,websdkversion:Ks,a2:e.getA2Key()||void 0,tinyid:e.getTinyID()||void 0,status_instid:e.getStatusInstanceID(),sdkappid:e.getSDKAppID(),contenttype:e.getContentType(),reqtime:0,identifier:e.getA2Key()?void 0:e.getUserID(),usersig:e.getA2Key()?void 0:e.getUserSig(),sdkability:35,tjgID:""}}},{key:"genCosSpecifiedHead",value:function(){var e=this.getModule(Xc);return{ver:"v4",platform:this._platform,websdkappid:Bs,websdkversion:Ks,sdkappid:e.getSDKAppID(),contenttype:e.getContentType(),reqtime:0,identifier:e.getUserID(),usersig:e.getUserSig(),status_instid:e.getStatusInstanceID(),sdkability:35}}},{key:"genSSOReportHead",value:function(){var e=this.getModule(Xc);return{ver:"v4",platform:this._platform,websdkappid:Bs,websdkversion:Ks,sdkappid:e.getSDKAppID(),contenttype:"",reqtime:0,identifier:"",usersig:"",status_instid:e.getStatusInstanceID(),sdkability:35}}},{key:"getProtocolData",value:function(e){return this._protocolHandler.getProtocolData(e)}},{key:"onErrorCodeNotZero",value:function(e){var t=e.errorCode;if(t===Ld.HELLO_ANSWER_KICKED_OUT){var n=e.kickType,o=e.newInstanceInfo,r=void 0===o?{}:o;1===n?this.onMultipleAccountKickedOut(r):2===n&&this.onMultipleDeviceKickedOut(r)}t!==Ld.MESSAGE_A2KEY_EXPIRED&&t!==Ld.ACCOUNT_A2KEY_EXPIRED||(this._onUserSigExpired(),this.getModule(sl).reConnect())}},{key:"onMessage",value:function(e){var t=e.body,n=t.needAck,o=void 0===n?0:n,r=t.sessionData;1===o&&this._sendACK(r),this._messageDispatcher.onMessage(e)}},{key:"onReconnected",value:function(){this._reLoginOnReconnected()}},{key:"reLoginOnKickOther",value:function(){Bi.log("".concat(this._className,".reLoginOnKickOther.")),this._reLogin()}},{key:"_reLoginOnReconnected",value:function(){Bi.log("".concat(this._className,"._reLoginOnReconnected.")),this._reLogin()}},{key:"_reLogin",value:function(){var e=this;this.isLoggedIn()&&this.request({protocolName:hl}).then((function(t){var n=t.data.instanceID;e.getModule(Xc).setStatusInstanceID(n),Bi.log("".concat(e._className,"._reLogin ok. start to sync unread messages.")),e.getModule(rl).startSyncOnReconnected(),e.getModule(ll).startPull(),e.getModule($c).updateLocalMainSequenceOnReconnected()}))}},{key:"onMultipleAccountKickedOut",value:function(e){this.getModule(Bc).onMultipleAccountKickedOut(e)}},{key:"onMultipleDeviceKickedOut",value:function(e){this.getModule(Bc).onMultipleDeviceKickedOut(e)}},{key:"_onUserSigExpired",value:function(){this.getModule(Bc).onUserSigExpired()}},{key:"_sendACK",value:function(e){this.request({protocolName:Td,requestData:{sessionData:e}})}},{key:"_isFrequencyOverLimit",value:function(e){var t=e.split(".")[1];if(!this._commandFrequencyLimitMap.has(t))return!1;if(!this._commandRequestInfoMap.has(t))return this._commandRequestInfoMap.set(t,{startTime:Date.now(),requestCount:1}),!1;var n=this._commandFrequencyLimitMap.get(t),o=n.count,r=n.interval,a=this._commandRequestInfoMap.get(t),s=a.startTime,i=a.requestCount;if(Date.now()-s>1e3*r)return this._commandRequestInfoMap.set(t,{startTime:Date.now(),requestCount:1}),!1;i+=1,this._commandRequestInfoMap.set(t,{startTime:s,requestCount:i});var u=!1;return i>o&&(u=!0),u}},{key:"_isServerOverload",value:function(e){if(!this._serverOverloadInfoMap.has(e))return!1;var t=this._serverOverloadInfoMap.get(e),n=t.overloadTime,o=t.waitingTime,r=!1;return Date.now()-n<=1e3*o?r=!0:(this._serverOverloadInfoMap.delete(e),r=!1),r}},{key:"onPushedServerOverload",value:function(e){var t=e.overloadCommand,n=e.waitingTime;this._serverOverloadInfoMap.set(t,{overloadTime:Date.now(),waitingTime:n}),Bi.warn("".concat(this._className,".onPushedServerOverload waitingTime:").concat(n,"s"))}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._updateCommandFrequencyLimitMap(wI),this._commandRequestInfoMap.clear(),this._serverOverloadInfoMap.clear()}}]),n}(gl),PI=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="MessageLossDetectionModule",o._maybeLostSequencesMap=new Map,o}return Un(n,[{key:"onMessageMaybeLost",value:function(e,t,n){this._maybeLostSequencesMap.has(e)||this._maybeLostSequencesMap.set(e,[]);for(var o=this._maybeLostSequencesMap.get(e),r=0;r<n;r++)o.push(t+r);Bi.debug("".concat(this._className,".onMessageMaybeLost. maybeLostSequences:").concat(o))}},{key:"detectMessageLoss",value:function(e,t){var n=this._maybeLostSequencesMap.get(e);if(!Lu(n)&&!Lu(t)){var o=t.filter((function(e){return-1!==n.indexOf(e)}));if(Bi.debug("".concat(this._className,".detectMessageLoss. matchedSequences:").concat(o)),n.length===o.length)Bi.info("".concat(this._className,".detectMessageLoss no message loss. conversationID:").concat(e));else{var r,a=n.filter((function(e){return-1===o.indexOf(e)})),s=a.length;s<=5?r=e+"-"+a.join("-"):(a.sort((function(e,t){return e-t})),r=e+" start:"+a[0]+" end:"+a[s-1]+" count:"+s),new og(mh).setMessage(r).setNetworkType(this.getNetworkType()).setLevel("warning").end(),Bi.warn("".concat(this._className,".detectMessageLoss message loss detected. conversationID:").concat(e," lostSequences:").concat(a))}n.length=0}}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._maybeLostSequencesMap.clear()}}]),n}(gl),UI=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="CloudControlModule",o._cloudConfig=new Map,o._expiredTime=0,o._version=0,o._isFetching=!1,o}return Un(n,[{key:"getCloudConfig",value:function(e){return Ji(e)?this._cloudConfig:this._cloudConfig.has(e)?this._cloudConfig.get(e):void 0}},{key:"_canFetchConfig",value:function(){return this.isLoggedIn()&&!this._isFetching&&Date.now()>=this._expiredTime}},{key:"fetchConfig",value:function(){var e=this,t=this._canFetchConfig();if(Bi.log("".concat(this._className,".fetchConfig canFetchConfig:").concat(t)),t){var n=new og(Ah),o=this.getModule(Xc).getSDKAppID();this._isFetching=!0,this.request({protocolName:Ed,requestData:{SDKAppID:o,version:this._version}}).then((function(t){e._isFetching=!1,n.setMessage("version:".concat(e._version," newVersion:").concat(t.data.version," config:").concat(t.data.cloudControlConfig)).setNetworkType(e.getNetworkType()).end(),Bi.log("".concat(e._className,".fetchConfig ok")),e._parseCloudControlConfig(t.data)})).catch((function(t){e._isFetching=!1,e.probeNetwork().then((function(e){var o=Xn(e,2),r=o[0],a=o[1];n.setError(t,r,a).end()})),Bi.log("".concat(e._className,".fetchConfig failed. error:"),t),e._setExpiredTimeOnResponseError(12e4)}))}}},{key:"onPushedCloudControlConfig",value:function(e){Bi.log("".concat(this._className,".onPushedCloudControlConfig")),new og(Eh).setNetworkType(this.getNetworkType()).setMessage("newVersion:".concat(e.version," config:").concat(e.cloudControlConfig)).end(),this._parseCloudControlConfig(e)}},{key:"onCheckTimer",value:function(e){this._canFetchConfig()&&this.fetchConfig()}},{key:"_parseCloudControlConfig",value:function(e){var t=this,n="".concat(this._className,"._parseCloudControlConfig"),o=e.errorCode,r=e.errorMessage,a=e.cloudControlConfig,s=e.version,i=e.expiredTime;if(0===o){if(this._version!==s){var u=null;try{u=JSON.parse(a)}catch(eT){Bi.error("".concat(n," JSON parse error:").concat(a))}u&&(this._cloudConfig.clear(),Object.keys(u).forEach((function(e){t._cloudConfig.set(e,u[e])})),this._version=s,this.emitInnerEvent(Pm))}this._expiredTime=Date.now()+1e3*i}else Ji(o)?(Bi.log("".concat(n," failed. Invalid message format:"),e),this._setExpiredTimeOnResponseError(36e5)):(Bi.error("".concat(n," errorCode:").concat(o," errorMessage:").concat(r)),this._setExpiredTimeOnResponseError(12e4))}},{key:"_setExpiredTimeOnResponseError",value:function(e){this._expiredTime=Date.now()+e}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._cloudConfig.clear(),this._expiredTime=0,this._version=0,this._isFetching=!1}}]),n}(gl),FI=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="PullGroupMessageModule",o._remoteLastMessageSequenceMap=new Map,o.PULL_LIMIT_COUNT=15,o}return Un(n,[{key:"startPull",value:function(){var e=this,t=this._getNeedPullConversationList();this._getRemoteLastMessageSequenceList().then((function(){var n=e.getModule(Jc);t.forEach((function(t){var o=t.conversationID,r=o.replace(so.CONV_GROUP,""),a=n.getGroupLocalLastMessageSequence(o),s=e._remoteLastMessageSequenceMap.get(r)||0,i=s-a;Bi.log("".concat(e._className,".startPull groupID:").concat(r," localLastMessageSequence:").concat(a," ")+"remoteLastMessageSequence:".concat(s," diff:").concat(i)),a>0&&i>=1&&i<300&&e._pullMissingMessage({groupID:r,localLastMessageSequence:a,remoteLastMessageSequence:s,diff:i})}))}))}},{key:"_getNeedPullConversationList",value:function(){return this.getModule(Jc).getLocalConversationList().filter((function(e){return e.type===so.CONV_GROUP&&e.groupProfile.type!==so.GRP_AVCHATROOM}))}},{key:"_getRemoteLastMessageSequenceList",value:function(){var e=this;return this.getModule($c).getGroupList().then((function(t){for(var n=t.data.groupList,o=void 0===n?[]:n,r=0;r<o.length;r++){var a=o[r],s=a.groupID,i=a.nextMessageSeq;if(a.type!==so.GRP_AVCHATROOM){var u=i-1;e._remoteLastMessageSequenceMap.set(s,u)}}}))}},{key:"_pullMissingMessage",value:function(e){var t=this,n=e.localLastMessageSequence,o=e.remoteLastMessageSequence,r=e.diff;e.count=r>this.PULL_LIMIT_COUNT?this.PULL_LIMIT_COUNT:r,e.sequence=r>this.PULL_LIMIT_COUNT?n+this.PULL_LIMIT_COUNT:n+r,this._getGroupMissingMessage(e).then((function(a){a.length>0&&(a[0].sequence+1<=o&&(e.localLastMessageSequence=n+t.PULL_LIMIT_COUNT,e.diff=r-t.PULL_LIMIT_COUNT,t._pullMissingMessage(e)),t.getModule($c).onNewGroupMessage({dataList:a,isInstantMessage:!1}))}))}},{key:"_getGroupMissingMessage",value:function(e){var t=this,n=new og(Zg);return this.request({protocolName:ed,requestData:{groupID:e.groupID,count:e.count,sequence:e.sequence}}).then((function(o){var r=o.data.messageList,a=void 0===r?[]:r;return n.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(e.groupID," count:").concat(e.count," sequence:").concat(e.sequence," messageList length:").concat(a.length)).end(),a})).catch((function(e){t.probeNetwork().then((function(t){var o=Xn(t,2),r=o[0],a=o[1];n.setError(e,r,a).end()}))}))}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._remoteLastMessageSequenceMap.clear()}}]),n}(gl),qI=function(){function e(){Gn(this,e),this._className="AvgE2EDelay",this._e2eDelayArray=[]}return Un(e,[{key:"addMessageDelay",value:function(e){var t=ku(e.currentTime/1e3-e.time,2);this._e2eDelayArray.push(t)}},{key:"_calcAvg",value:function(e,t){if(0===t)return 0;var n=0;return e.forEach((function(e){n+=e})),ku(n/t,1)}},{key:"_calcTotalCount",value:function(){return this._e2eDelayArray.length}},{key:"_calcCountWithLimit",value:function(e){var t=e.e2eDelayArray,n=e.min,o=e.max;return t.filter((function(e){return n<e&&e<=o})).length}},{key:"_calcPercent",value:function(e,t){var n=ku(e/t*100,2);return n>100&&(n=100),n}},{key:"_checkE2EDelayException",value:function(e,t){var n=e.filter((function(e){return e>t}));if(n.length>0){var o=n.length,r=Math.min.apply(Math,Qn(n)),a=Math.max.apply(Math,Qn(n)),s=this._calcAvg(n,o),i=ku(o/e.length*100,2);new og(Lg).setMessage("message e2e delay exception. count:".concat(o," min:").concat(r," max:").concat(a," avg:").concat(s," percent:").concat(i)).setLevel("warning").end()}}},{key:"getStatResult",value:function(){var e=this._calcTotalCount();if(0===e)return null;var t=Qn(this._e2eDelayArray),n=this._calcCountWithLimit({e2eDelayArray:t,min:0,max:1}),o=this._calcCountWithLimit({e2eDelayArray:t,min:1,max:3}),r=this._calcPercent(n,e),a=this._calcPercent(o,e),s=this._calcAvg(t,e);return this._checkE2EDelayException(t,3),this.reset(),{totalCount:e,countLessThan1Second:n,percentOfCountLessThan1Second:r,countLessThan3Second:o,percentOfCountLessThan3Second:a,avgDelay:s}}},{key:"reset",value:function(){this._e2eDelayArray.length=0}}]),e}(),xI=function(){function e(){Gn(this,e),this._className="AvgRTT",this._requestCount=0,this._rttArray=[]}return Un(e,[{key:"addRequestCount",value:function(){this._requestCount+=1}},{key:"addRTT",value:function(e){this._rttArray.push(e)}},{key:"_calcTotalCount",value:function(){return this._requestCount}},{key:"_calcRTTCount",value:function(e){return e.length}},{key:"_calcSuccessRateOfRequest",value:function(e,t){if(0===t)return 0;var n=ku(e/t*100,2);return n>100&&(n=100),n}},{key:"_calcAvg",value:function(e,t){if(0===t)return 0;var n=0;return e.forEach((function(e){n+=e})),parseInt(n/t)}},{key:"_calcMax",value:function(){return Math.max.apply(Math,Qn(this._rttArray))}},{key:"_calcMin",value:function(){return Math.min.apply(Math,Qn(this._rttArray))}},{key:"getStatResult",value:function(){var e=this._calcTotalCount(),t=Qn(this._rttArray);if(0===e)return null;var n=this._calcRTTCount(t),o=this._calcSuccessRateOfRequest(n,e),r=this._calcAvg(t,n);return Bi.log("".concat(this._className,".getStatResult max:").concat(this._calcMax()," min:").concat(this._calcMin()," avg:").concat(r)),this.reset(),{totalCount:e,rttCount:n,successRateOfRequest:o,avgRTT:r}}},{key:"reset",value:function(){this._requestCount=0,this._rttArray.length=0}}]),e}(),VI=function(){function e(){Gn(this,e),this._map=new Map}return Un(e,[{key:"initMap",value:function(e){var t=this;e.forEach((function(e){t._map.set(e,{totalCount:0,successCount:0,failedCountOfUserSide:0,costArray:[],fileSizeArray:[]})}))}},{key:"addTotalCount",value:function(e){return!(Ji(e)||!this._map.has(e))&&(this._map.get(e).totalCount+=1,!0)}},{key:"addSuccessCount",value:function(e){return!(Ji(e)||!this._map.has(e))&&(this._map.get(e).successCount+=1,!0)}},{key:"addFailedCountOfUserSide",value:function(e){return!(Ji(e)||!this._map.has(e))&&(this._map.get(e).failedCountOfUserSide+=1,!0)}},{key:"addCost",value:function(e,t){return!(Ji(e)||!this._map.has(e))&&(this._map.get(e).costArray.push(t),!0)}},{key:"addFileSize",value:function(e,t){return!(Ji(e)||!this._map.has(e))&&(this._map.get(e).fileSizeArray.push(t),!0)}},{key:"_calcSuccessRateOfBusiness",value:function(e){if(Ji(e)||!this._map.has(e))return-1;var t=this._map.get(e),n=ku(t.successCount/t.totalCount*100,2);return n>100&&(n=100),n}},{key:"_calcSuccessRateOfPlatform",value:function(e){if(Ji(e)||!this._map.has(e))return-1;var t=this._map.get(e),n=this._calcSuccessCountOfPlatform(e)/t.totalCount*100;return(n=ku(n,2))>100&&(n=100),n}},{key:"_calcTotalCount",value:function(e){return Ji(e)||!this._map.has(e)?-1:this._map.get(e).totalCount}},{key:"_calcSuccessCountOfBusiness",value:function(e){return Ji(e)||!this._map.has(e)?-1:this._map.get(e).successCount}},{key:"_calcSuccessCountOfPlatform",value:function(e){if(Ji(e)||!this._map.has(e))return-1;var t=this._map.get(e);return t.successCount+t.failedCountOfUserSide}},{key:"_calcAvg",value:function(e){return Ji(e)||!this._map.has(e)?-1:e===zp?this._calcAvgSpeed(e):this._calcAvgCost(e)}},{key:"_calcAvgCost",value:function(e){var t=this._map.get(e).costArray.length;if(0===t)return 0;var n=0;return this._map.get(e).costArray.forEach((function(e){n+=e})),parseInt(n/t)}},{key:"_calcAvgSpeed",value:function(e){var t=0,n=0;return this._map.get(e).costArray.forEach((function(e){t+=e})),this._map.get(e).fileSizeArray.forEach((function(e){n+=e})),parseInt(1e3*n/t)}},{key:"getStatResult",value:function(e){var t=this._calcTotalCount(e);if(0===t)return null;var n=this._calcSuccessCountOfBusiness(e),o=this._calcSuccessRateOfBusiness(e),r=this._calcSuccessCountOfPlatform(e),a=this._calcSuccessRateOfPlatform(e),s=this._calcAvg(e);return this.reset(e),{totalCount:t,successCountOfBusiness:n,successRateOfBusiness:o,successCountOfPlatform:r,successRateOfPlatform:a,avgValue:s}}},{key:"reset",value:function(e){Ji(e)?this._map.clear():this._map.set(e,{totalCount:0,successCount:0,failedCountOfUserSide:0,costArray:[],fileSizeArray:[]})}}]),e}(),KI=function(){function e(){Gn(this,e),this._lastMap=new Map,this._currentMap=new Map}return Un(e,[{key:"initMap",value:function(e){var t=this;e.forEach((function(e){t._lastMap.set(e,new Map),t._currentMap.set(e,new Map)}))}},{key:"addMessageSequence",value:function(e){var t=e.key,n=e.message;if(Ji(t)||!this._lastMap.has(t)||!this._currentMap.has(t))return!1;var o=n.conversationID,r=n.sequence,a=o.replace(so.CONV_GROUP,"");if(0===this._lastMap.get(t).size)this._addCurrentMap(e);else if(this._lastMap.get(t).has(a)){var s=this._lastMap.get(t).get(a),i=s.length-1;r>s[0]&&r<s[i]?(s.push(r),s.sort(),this._lastMap.get(t).set(a,s)):this._addCurrentMap(e)}else this._addCurrentMap(e);return!0}},{key:"_addCurrentMap",value:function(e){var t=e.key,n=e.message,o=n.conversationID,r=n.sequence,a=o.replace(so.CONV_GROUP,"");this._currentMap.get(t).has(a)||this._currentMap.get(t).set(a,[]),this._currentMap.get(t).get(a).push(r)}},{key:"_copyData",value:function(e){if(!Ji(e)){this._lastMap.set(e,new Map);var t,n=this._lastMap.get(e),o=ro(this._currentMap.get(e));try{for(o.s();!(t=o.n()).done;){var r=Xn(t.value,2),a=r[0],s=r[1];n.set(a,s)}}catch(c){o.e(c)}finally{o.f()}n=null,this._currentMap.set(e,new Map)}}},{key:"getStatResult",value:function(e){if(Ji(this._currentMap.get(e))||Ji(this._lastMap.get(e)))return null;if(0===this._lastMap.get(e).size)return this._copyData(e),null;var t=0,n=0;if(this._lastMap.get(e).forEach((function(e,o){var r=Qn(e.values()),a=r.length,s=r[a-1]-r[0]+1;t+=s,n+=a})),0===t)return null;var o=ku(n/t*100,2);return o>100&&(o=100),this._copyData(e),{totalCount:t,successCountOfMessageReceived:n,successRateOfMessageReceived:o}}},{key:"reset",value:function(){this._currentMap.clear(),this._lastMap.clear()}}]),e}(),BI=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;Gn(this,n),(o=t.call(this,e))._className="QualityStatModule",o.TAG="im-ssolog-quality-stat",o.reportIndex=0,o.wholePeriod=!1,o._qualityItems=[Bp,Hp,jp,Wp,$p,Yp,zp,Jp,Xp,Qp],o._messageSentItems=[jp,Wp,$p,Yp,zp],o._messageReceivedItems=[Jp,Xp,Qp],o.REPORT_INTERVAL=120,o.REPORT_SDKAPPID_BLACKLIST=[],o.REPORT_TINYID_WHITELIST=[],o._statInfoArr=[],o._avgRTT=new xI,o._avgE2EDelay=new qI,o._rateMessageSent=new VI,o._rateMessageReceived=new KI;var r=o.getInnerEmitterInstance();return r.on(Gm,o._onLoginSuccess,Yn(o)),r.on(Pm,o._onCloudConfigUpdated,Yn(o)),o}return Un(n,[{key:"_onLoginSuccess",value:function(){var e=this;this._rateMessageSent.initMap(this._messageSentItems),this._rateMessageReceived.initMap(this._messageReceivedItems);var t=this.getModule(Qc),n=t.getItem(this.TAG,!1);!Lu(n)&&Qi(n.forEach)&&(Bi.log("".concat(this._className,"._onLoginSuccess.get quality stat log in storage, nums=").concat(n.length)),n.forEach((function(t){e._statInfoArr.push(t)})),t.removeItem(this.TAG,!1))}},{key:"_onCloudConfigUpdated",value:function(){var e=this.getCloudConfig("q_rpt_interval"),t=this.getCloudConfig("q_rpt_sdkappid_bl"),n=this.getCloudConfig("q_rpt_tinyid_wl");Ji(e)||(this.REPORT_INTERVAL=Number(e)),Ji(t)||(this.REPORT_SDKAPPID_BLACKLIST=t.split(",").map((function(e){return Number(e)}))),Ji(n)||(this.REPORT_TINYID_WHITELIST=n.split(","))}},{key:"onCheckTimer",value:function(e){this.isLoggedIn()&&e%this.REPORT_INTERVAL==0&&(this.wholePeriod=!0,this._report())}},{key:"addRequestCount",value:function(){this._avgRTT.addRequestCount()}},{key:"addRTT",value:function(e){this._avgRTT.addRTT(e)}},{key:"addMessageDelay",value:function(e){this._avgE2EDelay.addMessageDelay(e)}},{key:"addTotalCount",value:function(e){this._rateMessageSent.addTotalCount(e)||Bi.warn("".concat(this._className,".addTotalCount invalid key:"),e)}},{key:"addSuccessCount",value:function(e){this._rateMessageSent.addSuccessCount(e)||Bi.warn("".concat(this._className,".addSuccessCount invalid key:"),e)}},{key:"addFailedCountOfUserSide",value:function(e){this._rateMessageSent.addFailedCountOfUserSide(e)||Bi.warn("".concat(this._className,".addFailedCountOfUserSide invalid key:"),e)}},{key:"addCost",value:function(e,t){this._rateMessageSent.addCost(e,t)||Bi.warn("".concat(this._className,".addCost invalid key or cost:"),e,t)}},{key:"addFileSize",value:function(e,t){this._rateMessageSent.addFileSize(e,t)||Bi.warn("".concat(this._className,".addFileSize invalid key or size:"),e,t)}},{key:"addMessageSequence",value:function(e){this._rateMessageReceived.addMessageSequence(e)||Bi.warn("".concat(this._className,".addMessageSequence invalid key:"),e.key)}},{key:"_getQualityItem",value:function(e){var t={},n=tg[this.getNetworkType()];Ji(n)&&(n=8);var o={qualityType:Zp[e],timestamp:Fi(),networkType:n,extension:""};switch(e){case Bp:t=this._avgRTT.getStatResult();break;case Hp:t=this._avgE2EDelay.getStatResult();break;case jp:case Wp:case $p:case Yp:case zp:t=this._rateMessageSent.getStatResult(e);break;case Jp:case Xp:case Qp:t=this._rateMessageReceived.getStatResult(e)}return null===t?null:xn(xn({},o),t)}},{key:"_report",value:function(e){var t=this,n=[],o=null;Ji(e)?this._qualityItems.forEach((function(e){null!==(o=t._getQualityItem(e))&&(o.reportIndex=t.reportIndex,o.wholePeriod=t.wholePeriod,n.push(o))})):null!==(o=this._getQualityItem(e))&&(o.reportIndex=this.reportIndex,o.wholePeriod=this.wholePeriod,n.push(o)),Bi.debug("".concat(this._className,"._report"),n),this._statInfoArr.length>0&&(n=n.concat(this._statInfoArr),this._statInfoArr=[]);var r=this.getModule(Xc),a=r.getSDKAppID(),s=r.getTinyID();Du(this.REPORT_SDKAPPID_BLACKLIST,a)&&!Nu(this.REPORT_TINYID_WHITELIST,s)&&(n=[]),n.length>0&&this._doReport(n)}},{key:"_doReport",value:function(e){var t=this,n={header:Ev(this),quality:e};this.request({protocolName:Md,requestData:xn({},n)}).then((function(){t.reportIndex++,t.wholePeriod=!1})).catch((function(n){Bi.warn("".concat(t._className,"._doReport, online:").concat(t.getNetworkType()," error:"),n),t._statInfoArr=t._statInfoArr.concat(e),t._flushAtOnce()}))}},{key:"_flushAtOnce",value:function(){var e=this.getModule(Qc),t=e.getItem(this.TAG,!1),n=this._statInfoArr;if(Lu(t))Bi.log("".concat(this._className,"._flushAtOnce count:").concat(n.length)),e.setItem(this.TAG,n,!0,!1);else{var o=n.concat(t);o.length>10&&(o=o.slice(0,10)),Bi.log("".concat(this.className,"._flushAtOnce count:").concat(o.length)),e.setItem(this.TAG,o,!0,!1)}this._statInfoArr=[]}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._report(),this.reportIndex=0,this.wholePeriod=!1,this.REPORT_SDKAPPID_BLACKLIST=[],this.REPORT_TINYID_WHITELIST=[],this._avgRTT.reset(),this._avgE2EDelay.reset(),this._rateMessageSent.reset(),this._rateMessageReceived.reset()}}]),n}(gl),HI=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="WorkerModule",o._isWorkerEnabled=!1,o._workerTimer=null,o._init(),o.getInnerEmitterInstance().on(Pm,o._onCloudConfigUpdated,Yn(o)),o}return Un(n,[{key:"isWorkerEnabled",value:function(){return this._isWorkerEnabled&&Ai&&this._workerTimer}},{key:"startWorkerTimer",value:function(){Bi.log("".concat(this._className,".startWorkerTimer")),this._workerTimer&&this._workerTimer.postMessage("start")}},{key:"stopWorkerTimer",value:function(){Bi.log("".concat(this._className,".stopWorkerTimer")),this._workerTimer&&this._workerTimer.postMessage("stop")}},{key:"_init",value:function(){if(Ai){var e=URL.createObjectURL(new Blob(['let interval = -1;onmessage = function(event) {  if (event.data === "start") {    if (interval > 0) {      clearInterval(interval);    }    interval = setInterval(() => {      postMessage("");    }, 1000)  } else if (event.data === "stop") {    clearInterval(interval);    interval = -1;  }};'],{type:"application/javascript; charset=utf-8"}));this._workerTimer=new Worker(e);var t=this;this._workerTimer.onmessage=function(){t._moduleManager.onCheckTimer()}}}},{key:"_onCloudConfigUpdated",value:function(){"1"===this.getCloudConfig("enable_worker")?!this._isWorkerEnabled&&Ai&&(this._isWorkerEnabled=!0,this.startWorkerTimer(),this._moduleManager.onWorkerTimerEnabled()):this._isWorkerEnabled&&Ai&&(this._isWorkerEnabled=!1,this.stopWorkerTimer(),this._moduleManager.onWorkerTimerDisabled())}},{key:"terminate",value:function(){Bi.log("".concat(this._className,".terminate")),this._workerTimer&&(this._workerTimer.terminate(),this._workerTimer=null)}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset"))}}]),n}(gl),jI=function(){function e(){Gn(this,e),this._className="PurchasedFeatureHandler",this._purchasedFeatureMap=new Map}return Un(e,[{key:"isValidPurchaseBits",value:function(e){return e&&"string"==typeof e&&e.length>=1&&e.length<=64&&/[01]{1,64}/.test(e)}},{key:"parsePurchaseBits",value:function(e){var t="".concat(this._className,".parsePurchaseBits");if(this.isValidPurchaseBits(e)){this._purchasedFeatureMap.clear();for(var n=Object.values(Xs),o=null,r=e.length-1,a=0;r>=0;r--,a++)o=a<32?new qs(0,Math.pow(2,a)).toString():new qs(Math.pow(2,a-32),0).toString(),-1!==n.indexOf(o)&&("1"===e[r]?this._purchasedFeatureMap.set(o,!0):this._purchasedFeatureMap.set(o,!1))}else Bi.warn("".concat(t," invalid purchase bits:").concat(e))}},{key:"hasPurchasedFeature",value:function(e){return!!this._purchasedFeatureMap.get(e)}},{key:"clear",value:function(){this._purchasedFeatureMap.clear()}}]),e}(),WI=function(e){Vn(n,e);var t=Jn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="CommercialConfigModule",o._expiredTime=0,o._isFetching=!1,o._purchasedFeatureHandler=new jI,o}return Un(n,[{key:"_canFetch",value:function(){return this.isLoggedIn()?!this._isFetching&&Date.now()>=this._expiredTime:(this._expiredTime=Date.now()+2e3,!1)}},{key:"onCheckTimer",value:function(e){this._canFetch()&&this.fetchConfig()}},{key:"fetchConfig",value:function(){var e=this,t=this._canFetch(),n="".concat(this._className,".fetchConfig");if(Bi.log("".concat(n," canFetch:").concat(t)),t){var o=new og(kh);o.setNetworkType(this.getNetworkType());var r=this.getModule(Xc).getSDKAppID();this._isFetching=!0,this.request({protocolName:Dd,requestData:{SDKAppID:r}}).then((function(t){o.setMessage("purchaseBits:".concat(t.data.purchaseBits)).end(),Bi.log("".concat(n," ok.")),e._parseConfig(t.data),e._isFetching=!1})).catch((function(t){e.probeNetwork().then((function(e){var n=Xn(e,2),r=n[0],a=n[1];o.setError(t,r,a).end()})),e._isFetching=!1}))}}},{key:"onPushedConfig",value:function(e){var t="".concat(this._className,".onPushedConfig");Bi.log("".concat(t)),new og(Dh).setNetworkType(this.getNetworkType()).setMessage("purchaseBits:".concat(e.purchaseBits)).end(),this._parseConfig(e)}},{key:"_parseConfig",value:function(e){var t="".concat(this._className,"._parseConfig"),n=e.errorCode,o=e.errorMessage,r=e.purchaseBits,a=e.expiredTime;0===n?(this._purchasedFeatureHandler.parsePurchaseBits(r),this._expiredTime=Date.now()+1e3*a):Ji(n)?(Bi.log("".concat(t," failed. Invalid message format:"),e),this._setExpiredTimeOnResponseError(36e5)):(Bi.error("".concat(t," errorCode:").concat(n," errorMessage:").concat(o)),this._setExpiredTimeOnResponseError(12e4))}},{key:"_setExpiredTimeOnResponseError",value:function(e){this._expiredTime=Date.now()+e}},{key:"hasPurchasedFeature",value:function(e){return this._purchasedFeatureHandler.hasPurchasedFeature(e)}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._expiredTime=0,this._isFetching=!1,this._purchasedFeatureHandler.clear()}}]),n}(gl),$I=function(){function e(t){Gn(this,e);var n=new og(rg);this._className="ModuleManager",this._isReady=!1,this._startLoginTs=0,this._moduleMap=new Map,this._innerEmitter=null,this._outerEmitter=null,this._checkCount=0,this._checkTimer=-1,this._moduleMap.set(Xc,new Iv(this,t)),this._moduleMap.set(pl,new WI(this)),this._moduleMap.set(ul,new UI(this)),this._moduleMap.set(cl,new HI(this)),this._moduleMap.set(dl,new BI(this)),this._moduleMap.set(sl,new OI(this)),this._moduleMap.set(al,new GI(this)),this._moduleMap.set(Bc,new Tv(this)),this._moduleMap.set(Hc,new Uv(this)),this._moduleMap.set(jc,new yv(this)),this._moduleMap.set(Wc,new Am(this)),this._moduleMap.set(Jc,new ev(this)),this._moduleMap.set($c,new hv(this)),this._moduleMap.set(zc,new _v(this)),this._moduleMap.set(Qc,new Sv(this)),this._moduleMap.set(Zc,new kv(this)),this._moduleMap.set(el,new Ov(this)),this._moduleMap.set(tl,new Rv(this)),this._moduleMap.set(nl,new bv(this)),this._moduleMap.set(ol,new Fv(this)),this._moduleMap.set(rl,new qv(this)),this._moduleMap.set(il,new PI(this)),this._moduleMap.set(ll,new FI(this));var o=t.instanceID,r=t.oversea,a=t.SDKAppID,s="instanceID:".concat(o," SDKAppID:").concat(a," host:").concat(Cu()," oversea:").concat(r," inBrowser:").concat(ui," inMiniApp:").concat(ii)+" workerAvailable:".concat(Ai," UserAgent:").concat(di);og.bindEventStatModule(this._moduleMap.get(Zc)),n.setMessage("".concat(s," ").concat(function(){var e="";if(ii)try{var t=ci.getSystemInfoSync(),n=t.model,o=t.version,r=t.system,a=t.platform,s=t.SDKVersion;e="model:".concat(n," version:").concat(o," system:").concat(r," platform:").concat(a," SDKVersion:").concat(s)}catch(eT){e=""}return e}())).end(),Bi.info("SDK ".concat(s)),this._readyList=void 0,this._ssoLogForReady=null,this._initReadyList()}return Un(e,[{key:"_startTimer",value:function(){var e=this._moduleMap.get(cl).isWorkerEnabled();Bi.log("".concat(this._className,".startTimer isWorkerEnabled:").concat(e," seed:").concat(this._checkTimer)),e?this._moduleMap.get(cl).startWorkerTimer():this._startMainThreadTimer()}},{key:"_startMainThreadTimer",value:function(){Bi.log("".concat(this._className,"._startMainThreadTimer")),this._checkTimer<0&&(this._checkTimer=setInterval(this.onCheckTimer.bind(this),1e3))}},{key:"stopTimer",value:function(){var e=this._moduleMap.get(cl).isWorkerEnabled();Bi.log("".concat(this._className,".stopTimer isWorkerEnabled:").concat(e," seed:").concat(this._checkTimer)),e?this._moduleMap.get(cl).stopWorkerTimer():this._stopMainThreadTimer()}},{key:"_stopMainThreadTimer",value:function(){Bi.log("".concat(this._className,"._stopMainThreadTimer")),this._checkTimer>0&&(clearInterval(this._checkTimer),this._checkTimer=-1,this._checkCount=0)}},{key:"onWorkerTimerEnabled",value:function(){Bi.log("".concat(this._className,".onWorkerTimerEnabled, disable main thread timer")),this._stopMainThreadTimer()}},{key:"onWorkerTimerDisabled",value:function(){Bi.log("".concat(this._className,".onWorkerTimerDisabled, enable main thread timer")),this._startMainThreadTimer()}},{key:"onCheckTimer",value:function(){this._checkCount+=1;var e,t=ro(this._moduleMap);try{for(t.s();!(e=t.n()).done;){var n=Xn(e.value,2)[1];n.onCheckTimer&&n.onCheckTimer(this._checkCount)}}catch(r){t.e(r)}finally{t.f()}}},{key:"_initReadyList",value:function(){var e=this;this._readyList=[this._moduleMap.get(Bc),this._moduleMap.get(Jc)],this._readyList.forEach((function(t){t.ready((function(){return e._onModuleReady()}))}))}},{key:"_onModuleReady",value:function(){var e=!0;if(this._readyList.forEach((function(t){t.isReady()||(e=!1)})),e&&!this._isReady){this._isReady=!0,this._outerEmitter.emit(ao.SDK_READY);var t=Date.now()-this._startLoginTs;Bi.warn("SDK is ready. cost ".concat(t," ms")),this._startLoginTs=Date.now();var n=this._moduleMap.get(el).getNetworkType(),o=this._ssoLogForReady.getStartTs()+Ui;this._ssoLogForReady.setNetworkType(n).setMessage(t).start(o).end()}}},{key:"login",value:function(){0===this._startLoginTs&&(qi(),this._startLoginTs=Date.now(),this._startTimer(),this._moduleMap.get(el).start(),this._ssoLogForReady=new og(ag))}},{key:"onLoginFailed",value:function(){this._startLoginTs=0}},{key:"getOuterEmitterInstance",value:function(){return null===this._outerEmitter&&(this._outerEmitter=new Lv,Tm(this._outerEmitter),this._outerEmitter._emit=this._outerEmitter.emit,this._outerEmitter.emit=function(e,t){var n=arguments[0],o=[n,{name:arguments[0],data:arguments[1]}];this._outerEmitter._emit.apply(this._outerEmitter,o)}.bind(this)),this._outerEmitter}},{key:"getInnerEmitterInstance",value:function(){return null===this._innerEmitter&&(this._innerEmitter=new Lv,this._innerEmitter._emit=this._innerEmitter.emit,this._innerEmitter.emit=function(e,t){var n;Yi(arguments[1])&&arguments[1].data?(Bi.warn("inner eventData has data property, please check!"),n=[e,{name:arguments[0],data:arguments[1].data}]):n=[e,{name:arguments[0],data:arguments[1]}],this._innerEmitter._emit.apply(this._innerEmitter,n)}.bind(this)),this._innerEmitter}},{key:"hasModule",value:function(e){return this._moduleMap.has(e)}},{key:"getModule",value:function(e){return this._moduleMap.get(e)}},{key:"isReady",value:function(){return this._isReady}},{key:"onError",value:function(e){Bi.warn("Oops! code:".concat(e.code," message:").concat(e.message)),new og(Nh).setMessage("code:".concat(e.code," message:").concat(e.message)).setNetworkType(this.getModule(el).getNetworkType()).setLevel("error").end(),this.getOuterEmitterInstance().emit(ao.ERROR,e)}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),qi();var e,t=ro(this._moduleMap);try{for(t.s();!(e=t.n()).done;){var n=Xn(e.value,2)[1];n.reset&&n.reset()}}catch(r){t.e(r)}finally{t.f()}this._startLoginTs=0,this._initReadyList(),this._isReady=!1,this.stopTimer(),this._outerEmitter.emit(ao.SDK_NOT_READY)}}]),e}(),YI=function(){function e(){Gn(this,e),this._funcMap=new Map}return Un(e,[{key:"defense",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;if("string"!=typeof e)return null;if(0===e.length)return null;if("function"!=typeof t)return null;if(this._funcMap.has(e)&&this._funcMap.get(e).has(t))return this._funcMap.get(e).get(t);this._funcMap.has(e)||this._funcMap.set(e,new Map);var o=null;return this._funcMap.get(e).has(t)?o=this._funcMap.get(e).get(t):(o=this._pack(e,t,n),this._funcMap.get(e).set(t,o)),o}},{key:"defenseOnce",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;return"function"!=typeof t?null:this._pack(e,t,n)}},{key:"find",value:function(e,t){return"string"!=typeof e||0===e.length||"function"!=typeof t?null:this._funcMap.has(e)?this._funcMap.get(e).has(t)?this._funcMap.get(e).get(t):(Bi.log("SafetyCallback.find: 找不到 func —— ".concat(e,"/").concat(""!==t.name?t.name:"[anonymous]")),null):(Bi.log("SafetyCallback.find: 找不到 eventName-".concat(e," 对应的 func")),null)}},{key:"delete",value:function(e,t){return"function"==typeof t&&!!this._funcMap.has(e)&&!!this._funcMap.get(e).has(t)&&(this._funcMap.get(e).delete(t),0===this._funcMap.get(e).size&&this._funcMap.delete(e),!0)}},{key:"_pack",value:function(e,t,n){return function(){try{t.apply(n,Array.from(arguments))}catch(u){var o=Object.values(ao).indexOf(e);if(-1!==o){var r=Object.keys(ao)[o];Bi.warn("接入侧事件 TIM.EVENT.".concat(r," 对应的回调函数逻辑存在问题，请检查！"),u)}var a=new og(Sh);a.setMessage("eventName:".concat(e)).setMoreMessage(u.message).end()}}}}]),e}(),zI=function(){function e(t){Gn(this,e);var n={SDKAppID:t.SDKAppID,unlimitedAVChatRoom:t.unlimitedAVChatRoom||!1,scene:t.scene||"",oversea:t.oversea||!1,instanceID:Tu(),devMode:t.devMode||!1,proxyServer:t.proxyServer||void 0};this._moduleManager=new $I(n),this._safetyCallbackFactory=new YI}return Un(e,[{key:"isReady",value:function(){return this._moduleManager.isReady()}},{key:"onError",value:function(e){this._moduleManager.onError(e)}},{key:"login",value:function(e){return this._moduleManager.login(),this._moduleManager.getModule(Bc).login(e)}},{key:"logout",value:function(){var e=this;return this._moduleManager.getModule(Bc).logout().then((function(t){return e._moduleManager.reset(),t}))}},{key:"destroy",value:function(){var e=this;return this.logout().finally((function(){e._moduleManager.stopTimer(),e._moduleManager.getModule(cl).terminate(),e._moduleManager.getModule(sl).dealloc();var t=e._moduleManager.getOuterEmitterInstance(),n=e._moduleManager.getModule(Xc);t.emit(ao.SDK_DESTROY,{SDKAppID:n.getSDKAppID()})}))}},{key:"on",value:function(e,t,n){e===ao.GROUP_SYSTEM_NOTICE_RECEIVED&&Bi.warn("！！！TIM.EVENT.GROUP_SYSTEM_NOTICE_RECEIVED v2.6.0起弃用，为了更好的体验，请在 TIM.EVENT.MESSAGE_RECEIVED 事件回调内接收处理群系统通知，详细请参考：https://web.sdk.qcloud.com/im/doc/zh-cn/Message.html#.GroupSystemNoticePayload"),Bi.debug("on","eventName:".concat(e)),this._moduleManager.getOuterEmitterInstance().on(e,this._safetyCallbackFactory.defense(e,t,n),n)}},{key:"once",value:function(e,t,n){Bi.debug("once","eventName:".concat(e)),this._moduleManager.getOuterEmitterInstance().once(e,this._safetyCallbackFactory.defenseOnce(e,t,n),n||this)}},{key:"off",value:function(e,t,n,o){Bi.debug("off","eventName:".concat(e));var r=this._safetyCallbackFactory.find(e,t);null!==r&&(this._moduleManager.getOuterEmitterInstance().off(e,r,n,o),this._safetyCallbackFactory.delete(e,t))}},{key:"registerPlugin",value:function(e){this._moduleManager.getModule(ol).registerPlugin(e)}},{key:"setLogLevel",value:function(e){e<=0&&(console.log([""," ________  ______  __       __  __       __  ________  _______","|        \\|      \\|  \\     /  \\|  \\  _  |  \\|        \\|       \\"," \\$$$$$$$$ \\$$$$$$| $$\\   /  $$| $$ / \\ | $$| $$$$$$$$| $$$$$$$\\","   | $$     | $$  | $$$\\ /  $$$| $$/  $\\| $$| $$__    | $$__/ $$","   | $$     | $$  | $$$$\\  $$$$| $$  $$$\\ $$| $$  \\   | $$    $$","   | $$     | $$  | $$\\$$ $$ $$| $$ $$\\$$\\$$| $$$$$   | $$$$$$$\\","   | $$    _| $$_ | $$ \\$$$| $$| $$$$  \\$$$$| $$_____ | $$__/ $$","   | $$   |   $$ \\| $$  \\$ | $$| $$$    \\$$$| $$     \\| $$    $$","    \\$$    \\$$$$$$ \\$$      \\$$ \\$$      \\$$ \\$$$$$$$$ \\$$$$$$$","",""].join("\n")),console.log("%cIM 智能客服，随时随地解决您的问题 →_→ https://cloud.tencent.com/act/event/smarty-service?from=im-doc","color:#006eff"),console.log("%c从v2.11.2起，SDK 支持了 WebSocket，小程序需要添加受信域名！升级指引: https://web.sdk.qcloud.com/im/doc/zh-cn/tutorial-02-upgradeguideline.html","color:#ff0000"),console.log(["","参考以下文档，会更快解决问题哦！(#^.^#)\n","SDK 更新日志: https://cloud.tencent.com/document/product/269/38492\n","SDK 接口文档: https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html\n","常见问题: https://web.sdk.qcloud.com/im/doc/zh-cn/tutorial-01-faq.html\n","反馈问题？戳我提 issue: https://github.com/tencentyun/TIMSDK/issues\n","如果您需要在生产环境关闭上面的日志，请 tim.setLogLevel(1)\n"].join("\n"))),Bi.setLevel(e)}},{key:"createTextMessage",value:function(e){return this._moduleManager.getModule(Hc).createTextMessage(e)}},{key:"createTextAtMessage",value:function(e){return this._moduleManager.getModule(Hc).createTextMessage(e)}},{key:"createImageMessage",value:function(e){return this._moduleManager.getModule(Hc).createImageMessage(e)}},{key:"createAudioMessage",value:function(e){return this._moduleManager.getModule(Hc).createAudioMessage(e)}},{key:"createVideoMessage",value:function(e){return this._moduleManager.getModule(Hc).createVideoMessage(e)}},{key:"createCustomMessage",value:function(e){return this._moduleManager.getModule(Hc).createCustomMessage(e)}},{key:"createFaceMessage",value:function(e){return this._moduleManager.getModule(Hc).createFaceMessage(e)}},{key:"createFileMessage",value:function(e){return this._moduleManager.getModule(Hc).createFileMessage(e)}},{key:"createLocationMessage",value:function(e){return this._moduleManager.getModule(Hc).createLocationMessage(e)}},{key:"createMergerMessage",value:function(e){return this._moduleManager.getModule(Hc).createMergerMessage(e)}},{key:"downloadMergerMessage",value:function(e){return e.type!==so.MSG_MERGER?Sm(new ym({code:Ld.MESSAGE_MERGER_TYPE_INVALID,message:ip})):Lu(e.payload.downloadKey)?Sm(new ym({code:Ld.MESSAGE_MERGER_KEY_INVALID,message:up})):this._moduleManager.getModule(Hc).downloadMergerMessage(e).catch((function(e){return Sm(new ym({code:Ld.MESSAGE_MERGER_DOWNLOAD_FAIL,message:cp}))}))}},{key:"createForwardMessage",value:function(e){return this._moduleManager.getModule(Hc).createForwardMessage(e)}},{key:"sendMessage",value:function(e,t){return e instanceof gm?this._moduleManager.getModule(Hc).sendMessageInstance(e,t):Sm(new ym({code:Ld.MESSAGE_SEND_NEED_MESSAGE_INSTANCE,message:Kd}))}},{key:"callExperimentalAPI",value:function(e,t){return"handleGroupInvitation"===e?this._moduleManager.getModule($c).handleGroupInvitation(t):Sm(new ym({code:Ld.INVALID_OPERATION,message:Up}))}},{key:"revokeMessage",value:function(e){return this._moduleManager.getModule(Hc).revokeMessage(e)}},{key:"resendMessage",value:function(e){return this._moduleManager.getModule(Hc).resendMessage(e)}},{key:"deleteMessage",value:function(e){return this._moduleManager.getModule(Hc).deleteMessage(e)}},{key:"getMessageList",value:function(e){return this._moduleManager.getModule(Jc).getMessageList(e)}},{key:"setMessageRead",value:function(e){return this._moduleManager.getModule(Jc).setMessageRead(e)}},{key:"getConversationList",value:function(e){return this._moduleManager.getModule(Jc).getConversationList(e)}},{key:"getConversationProfile",value:function(e){return this._moduleManager.getModule(Jc).getConversationProfile(e)}},{key:"deleteConversation",value:function(e){return this._moduleManager.getModule(Jc).deleteConversation(e)}},{key:"pinConversation",value:function(e){return this._moduleManager.getModule(Jc).pinConversation(e)}},{key:"setAllMessageRead",value:function(e){return this._moduleManager.getModule(Jc).setAllMessageRead(e)}},{key:"setMessageRemindType",value:function(e){return this._moduleManager.getModule(Jc).setMessageRemindType(e)}},{key:"getMyProfile",value:function(){return this._moduleManager.getModule(jc).getMyProfile()}},{key:"getUserProfile",value:function(e){return this._moduleManager.getModule(jc).getUserProfile(e)}},{key:"updateMyProfile",value:function(e){return this._moduleManager.getModule(jc).updateMyProfile(e)}},{key:"getBlacklist",value:function(){return this._moduleManager.getModule(jc).getLocalBlacklist()}},{key:"addToBlacklist",value:function(e){return this._moduleManager.getModule(jc).addBlacklist(e)}},{key:"removeFromBlacklist",value:function(e){return this._moduleManager.getModule(jc).deleteBlacklist(e)}},{key:"getFriendList",value:function(){var e=this._moduleManager.getModule(Yc);return e?e.getLocalFriendList():Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"addFriend",value:function(e){var t=this._moduleManager.getModule(Yc);return t?t.addFriend(e):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"deleteFriend",value:function(e){var t=this._moduleManager.getModule(Yc);return t?t.deleteFriend(e):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"checkFriend",value:function(e){var t=this._moduleManager.getModule(Yc);return t?t.checkFriend(e):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"getFriendProfile",value:function(e){var t=this._moduleManager.getModule(Yc);return t?t.getFriendProfile(e):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"updateFriend",value:function(e){var t=this._moduleManager.getModule(Yc);return t?t.updateFriend(e):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"getFriendApplicationList",value:function(){var e=this._moduleManager.getModule(Yc);return e?e.getLocalFriendApplicationList():Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"acceptFriendApplication",value:function(e){var t=this._moduleManager.getModule(Yc);return t?t.acceptFriendApplication(e):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"refuseFriendApplication",value:function(e){var t=this._moduleManager.getModule(Yc);return t?t.refuseFriendApplication(e):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"deleteFriendApplication",value:function(e){var t=this._moduleManager.getModule(Yc);return t?t.deleteFriendApplication(e):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"setFriendApplicationRead",value:function(){var e=this._moduleManager.getModule(Yc);return e?e.setFriendApplicationRead():Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"getFriendGroupList",value:function(){var e=this._moduleManager.getModule(Yc);return e?e.getLocalFriendGroupList():Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"createFriendGroup",value:function(e){var t=this._moduleManager.getModule(Yc);return t?t.createFriendGroup(e):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"deleteFriendGroup",value:function(e){var t=this._moduleManager.getModule(Yc);return t?t.deleteFriendGroup(e):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"addToFriendGroup",value:function(e){var t=this._moduleManager.getModule(Yc);return t?t.addToFriendGroup(e):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"removeFromFriendGroup",value:function(e){var t=this._moduleManager.getModule(Yc);return t?t.removeFromFriendGroup(e):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"renameFriendGroup",value:function(e){var t=this._moduleManager.getModule(Yc);return t?t.renameFriendGroup(e):Sm({code:Ld.CANNOT_FIND_MODULE,message:qp})}},{key:"getGroupList",value:function(e){return this._moduleManager.getModule($c).getGroupList(e)}},{key:"getGroupProfile",value:function(e){return this._moduleManager.getModule($c).getGroupProfile(e)}},{key:"createGroup",value:function(e){return this._moduleManager.getModule($c).createGroup(e)}},{key:"dismissGroup",value:function(e){return this._moduleManager.getModule($c).dismissGroup(e)}},{key:"updateGroupProfile",value:function(e){return this._moduleManager.getModule($c).updateGroupProfile(e)}},{key:"joinGroup",value:function(e){return this._moduleManager.getModule($c).joinGroup(e)}},{key:"quitGroup",value:function(e){return this._moduleManager.getModule($c).quitGroup(e)}},{key:"searchGroupByID",value:function(e){return this._moduleManager.getModule($c).searchGroupByID(e)}},{key:"getGroupOnlineMemberCount",value:function(e){return this._moduleManager.getModule($c).getGroupOnlineMemberCount(e)}},{key:"changeGroupOwner",value:function(e){return this._moduleManager.getModule($c).changeGroupOwner(e)}},{key:"handleGroupApplication",value:function(e){return this._moduleManager.getModule($c).handleGroupApplication(e)}},{key:"initGroupAttributes",value:function(e){return this._moduleManager.getModule($c).initGroupAttributes(e)}},{key:"setGroupAttributes",value:function(e){return this._moduleManager.getModule($c).setGroupAttributes(e)}},{key:"deleteGroupAttributes",value:function(e){return this._moduleManager.getModule($c).deleteGroupAttributes(e)}},{key:"getGroupAttributes",value:function(e){return this._moduleManager.getModule($c).getGroupAttributes(e)}},{key:"getGroupMemberList",value:function(e){return this._moduleManager.getModule(zc).getGroupMemberList(e)}},{key:"getGroupMemberProfile",value:function(e){return this._moduleManager.getModule(zc).getGroupMemberProfile(e)}},{key:"addGroupMember",value:function(e){return this._moduleManager.getModule(zc).addGroupMember(e)}},{key:"deleteGroupMember",value:function(e){return this._moduleManager.getModule(zc).deleteGroupMember(e)}},{key:"setGroupMemberMuteTime",value:function(e){return this._moduleManager.getModule(zc).setGroupMemberMuteTime(e)}},{key:"setGroupMemberRole",value:function(e){return this._moduleManager.getModule(zc).setGroupMemberRole(e)}},{key:"setGroupMemberNameCard",value:function(e){return this._moduleManager.getModule(zc).setGroupMemberNameCard(e)}},{key:"setGroupMemberCustomField",value:function(e){return this._moduleManager.getModule(zc).setGroupMemberCustomField(e)}}]),e}(),JI={login:"login",logout:"logout",destroy:"destroy",on:"on",off:"off",ready:"ready",setLogLevel:"setLogLevel",joinGroup:"joinGroup",quitGroup:"quitGroup",registerPlugin:"registerPlugin",getGroupOnlineMemberCount:"getGroupOnlineMemberCount"};function XI(e,t){if(e.isReady()||void 0!==JI[t])return!0;var n=new ym({code:Ld.SDK_IS_NOT_READY,message:"".concat(t," ").concat(xp,"，请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/module-EVENT.html#.SDK_READY")});return e.onError(n),!1}var QI={},ZI={create:function(e){var t=0;if(ji(e.SDKAppID))t=e.SDKAppID;else if(Bi.warn("TIM.create SDKAppID 的类型应该为 Number，请修改！"),t=parseInt(e.SDKAppID),isNaN(t))return Bi.error("TIM.create failed. 解析 SDKAppID 失败，请检查传参！"),null;if(t&&QI[t])return QI[t];Bi.log("TIM.create");var n=new zI(xn(xn({},e),{},{SDKAppID:t}));n.on(ao.SDK_DESTROY,(function(e){QI[e.data.SDKAppID]=null,delete QI[e.data.SDKAppID]}));var o=function(e){var t=Object.create(null);return Object.keys(wc).forEach((function(n){if(e[n]){var o=wc[n],r=new fo;t[o]=function(){var t=Array.from(arguments);return r.use((function(t,o){return XI(e,n)?o():Sm(new ym({code:Ld.SDK_IS_NOT_READY,message:"".concat(n," ").concat(xp,"。")}))})).use((function(e,t){if(!0===Ru(e,bc[n],o))return t()})).use((function(t,o){return e[n].apply(e,t)})),r.run(t)}}})),t}(n);return QI[t]=o,Bi.log("TIM.create ok"),o}};return ZI.TYPES=so,ZI.EVENT=ao,ZI.VERSION="2.16.3",Bi.log("TIM.VERSION: ".concat(ZI.VERSION)),ZI}))}).call(this,n("c8ba"))}}]);
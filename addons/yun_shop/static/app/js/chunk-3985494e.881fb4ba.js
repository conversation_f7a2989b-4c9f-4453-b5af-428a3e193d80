(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3985494e"],{"22ed":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,"#high_light[data-v-d2084860]{position:relative;height:100vh}#high_light .goback[data-v-d2084860]{height:2rem;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;background:#fff}#high_light .goback .goback_icon[data-v-d2084860]{padding-left:1rem;color:#666;font-size:16px}#high_light .goback .high_light_withdrawal[data-v-d2084860]{margin:0 auto;color:#666;font-weight:700;font-size:14px}#high_light .star[data-v-d2084860]{color:#ff062e}#high_light .select_type[data-v-d2084860]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}#high_light .select_type select[data-v-d2084860]{border:none;width:100%}#high_light .select_type .type_name[data-v-d2084860]{font-weight:700;margin-right:1rem}#high_light .select_type .van-cell-group[data-v-d2084860]{width:60%}#high_light .top[data-v-d2084860]{background:#fff;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;text-align:left;padding:0 1rem 1rem 1rem}#high_light .top input[data-v-d2084860]{border:none;width:70%}#high_light .top .line[data-v-d2084860]{padding:.5rem 0}#high_light .top .line .common_style[data-v-d2084860]{font-weight:700}#high_light .middle[data-v-d2084860]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;text-align:left}#high_light .middle .bank_card_message[data-v-d2084860]{padding:.5rem 1rem}#high_light .middle .common_style[data-v-d2084860]{font-weight:700}#high_light .middle .line[data-v-d2084860]{padding:.5rem 1rem;background:#fff}#high_light .middle input[data-v-d2084860]{border:none;width:60%}#high_light .bottom[data-v-d2084860]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;text-align:left}#high_light .bottom .alipay_message[data-v-d2084860]{padding:.5rem 1rem}#high_light .bottom .common_style[data-v-d2084860]{font-weight:700}#high_light .bottom .line[data-v-d2084860]{padding:.5rem 1rem;background:#fff}#high_light .bottom input[data-v-d2084860]{border:none;width:60%}#high_light .van-button--normal[data-v-d2084860]{width:80%;background-color:#f15353;border-radius:2.86rem;margin-top:5rem}#high_light .van-inp.van-cell[data-v-d2084860]{font-size:16px}#high_light .van-inp.van-cell[data-v-d2084860] .van-field__label{color:#333;width:auto}#high_light .tip[data-v-d2084860]{background:#fff;padding:0 1rem 1rem 1rem;color:#ed6a6a;font-weight:700}",""]),t.exports=e},"5fde":function(t,e,a){"use strict";a("8f7ab")},"8f7ab":function(t,e,a){var i=a("22ed");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("499e").default;n("d7330260",i,!0,{sourceMap:!1,shadowMode:!1})},e9f7:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"high_light"}},[a("div",{staticClass:"goback"},[a("van-icon",{staticClass:"goback_icon",attrs:{name:"arrow-left"},on:{click:t.btnBackGo}}),a("span",{staticClass:"high_light_withdrawal"},[t._v(t._s(t.diyname)+"提现")])],1),a("div",{staticClass:"top"},[a("div",{staticClass:"line name"},[a("span",{staticClass:"star"},[t._v("*")]),a("span",{staticClass:"common_style"},[t._v("姓名")]),t._v("："),a("input",{directives:[{name:"model",rawName:"v-model",value:t.name,expression:"name"}],attrs:{type:"text",placeholder:"请输入您姓名"},domProps:{value:t.name},on:{input:function(e){e.target.composing||(t.name=e.target.value)}}})]),a("div",{staticClass:"select_type"},[t._m(0),a("van-cell-group",{staticClass:"city-text"},[a("van-cell",{staticStyle:{border:"1px solid #757575","border-radius":"0.3125rem",height:"2rem",padding:"0 6px",position:"relative"},attrs:{"title-style":"text-align:left",center:"",border:!1}},[a("select",{directives:[{name:"model",rawName:"v-model",value:t.certificate_type,expression:"certificate_type"}],staticStyle:{color:"#757575","font-size":"13px"},attrs:{slot:"default"},on:{change:[function(e){var a=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.certificate_type=e.target.multiple?a:a[0]},t.groupingSelect],click:t.groupingClick},slot:"default"},t._l(t.document_list,(function(e,i){return a("option",{key:i,domProps:{value:e.id}},[t._v(t._s(e.name))])})),0),t.grouping_info?t._e():a("van-icon",{staticStyle:{position:"absolute",top:"25%",right:"0"},attrs:{name:"arrow"}}),t.grouping_info?a("van-icon",{staticStyle:{position:"absolute",top:"25%",right:"0"},attrs:{name:"arrow-down"}}):t._e()],1)],1)],1),a("div",{staticClass:"line card"},[a("span",{staticClass:"star"},[t._v("*")]),a("span",{staticClass:"common_style"},[t._v("证件号")]),t._v("："),a("input",{directives:[{name:"model",rawName:"v-model",value:t.certificate_no,expression:"certificate_no"}],attrs:{type:"text",placeholder:"请输入您的证件号"},domProps:{value:t.certificate_no},on:{input:function(e){e.target.composing||(t.certificate_no=e.target.value)}}})]),a("div",{staticClass:"line mobile"},[a("span",{staticClass:"star"},[t._v("*")]),a("span",{staticClass:"common_style"},[t._v("手机号")]),t._v("："),a("input",{directives:[{name:"model",rawName:"v-model",value:t.phone_number,expression:"phone_number"}],attrs:{type:"number",maxlength:"11",placeholder:"请输入银行卡绑定的手机号"},domProps:{value:t.phone_number},on:{input:function(e){e.target.composing||(t.phone_number=e.target.value)}}})])]),a("div",{staticClass:"middle"},[a("span",{staticClass:"bank_card_message common_style"},[t._v("银行卡信息")]),a("div",{staticClass:"line bank_card"},[a("span",{staticClass:"common_style"},[t._v("银行卡号：")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.bankcard_num,expression:"bankcard_num"}],attrs:{type:"number",placeholder:"请输入您的银行卡号"},domProps:{value:t.bankcard_num},on:{input:function(e){e.target.composing||(t.bankcard_num=e.target.value)}}})]),a("div",{staticClass:"line name"},[a("span",{staticClass:"common_style"},[t._v("银行名称：")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.bank_name,expression:"bank_name"}],attrs:{placeholder:"请输入您的银行名称"},domProps:{value:t.bank_name},on:{input:function(e){e.target.composing||(t.bank_name=e.target.value)}}})]),a("span",{staticClass:"tip"},[t._v("注：若选择提现到银行卡-"+t._s(t.diyname)+"则必须填写银行卡信息")])]),a("div",{staticClass:"bottom"},[a("span",{staticClass:"alipay_message common_style"},[t._v("支付宝信息")]),a("div",{staticClass:"line alipay"},[a("span",{staticClass:"common_style"},[t._v("支付宝账户：")]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.payment_account,expression:"payment_account"}],attrs:{type:"number",placeholder:"请输入您的支付宝账户"},domProps:{value:t.payment_account},on:{input:function(e){e.target.composing||(t.payment_account=e.target.value)}}})]),a("span",{staticClass:"tip"},[t._v("注：若选择提现到支付宝-"+t._s(t.diyname)+"则必须填写支付宝信息")])]),a("van-button",{attrs:{type:"danger"},on:{click:t.submit}},[t._v("提交")])],1)},n=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"type_name"},[a("span",{staticClass:"star"},[t._v("*")]),t._v("请选择证件类型: ")])}],o=(a("e7e5"),a("d399")),l=(a("b0c0"),a("ac1f"),a("00b4"),{data:function(){return{name:"",certificate_no:"",phone_number:"",bankcard_num:"",bank_name:"",payment_account:"",grouping_info:!1,industry_info:!1,certificate_type:1,document_list:[{id:1,name:"居民身份证"},{id:3,name:"港澳居民来往内地通行证"},{id:4,name:"港澳居民居住证"},{id:5,name:"台湾居民来往大陆通行证"},{id:6,name:"台湾居民居住证"}],value:"",diyname:"高灯"}},activated:function(){this.value=this.$route.params.value,this.data_echo()},methods:{submit:function(){var t=this;if(this.name)if(this.certificate_type)if(this.certificate_no)if(this.phone_number){var e=/^1(3[0-9]|4[********]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/;if(""==this.phone_number||this.phone_number.length<=10||!e.test(this.phone_number))Object(o["a"])("手机号码格式错误");else if(this.bankcard_num||"high_light_bank"!==this.value)if(this.bank_name||"high_light_bank"!==this.value)if(this.payment_account||"high_light_alipay"!==this.value){var a={name:this.name,certificate_no:this.certificate_no,phone_number:this.phone_number,certificate_type:this.certificate_type,bank_name:this.bank_name,bankcard_num:this.bankcard_num,payment_account:this.payment_account};$http.get("plugin.high-light.frontend.index.edit-agreement-info",a).then((function(e){1==e.result?(Object(o["a"])("提交成功"),1===e.data.statue&&setTimeout((function(){window.location.href=e.data.url}),1e3),0===e.data.statue&&setTimeout((function(){t.$router.go(-1)}),1e3)):Object(o["a"])(e.msg)}),(function(t){console.log(t)}))}else Object(o["a"])("请输入您的支付宝账户");else Object(o["a"])("请输入您的银行名称");else Object(o["a"])("请输入您的银行卡号")}else Object(o["a"])("请输入您的手机号码");else Object(o["a"])("请输入您的证件号");else Object(o["a"])("请选择证件类型");else Object(o["a"])("请输入您的姓名")},data_echo:function(){var t=this;$http.get("plugin.high-light.frontend.index.get-info").then((function(e){if(1==e.result){console.log(e,"response"),t.name=e.data?e.data.name:"",t.certificate_no=e.data?e.data.certificate_no:"",t.phone_number=e.data?e.data.phone_number:"",t.certificate_type=e.data?e.data.certificate_type:1,t.bank_name=e.data?e.data.bank_name:"",t.bankcard_num=e.data?e.data.bankcard_num:"",t.payment_account=e.data?e.data.payment_account:"";var a=JSON.parse(localStorage.getItem("plugin_setting"));t.diyname=a&&a.high_light&&a.high_light.diy_name||"高灯",t.fun.setWXTitle("".concat(t.diyname,"代发薪"))}else Object(o["a"])(e.msg)}),(function(t){console.log(t)}))},groupingSelect:function(){this.grouping_info=!1},groupingClick:function(){this.grouping_info=!this.grouping_info},btnBackGo:function(){this.$router.push(this.fun.getUrl("withdrawal"))}}}),s=l,c=s,r=(a("5fde"),a("2877")),d=Object(r["a"])(c,i,n,!1,null,"d2084860",null);e["default"]=d.exports}}]);
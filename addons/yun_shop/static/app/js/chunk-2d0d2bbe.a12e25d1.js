(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0d2bbe"],{"5a84":function(A,e,t){var r,n;t("f3dd"),t("0a51"),t("9b11"),t("b4fb"),t("56ea"),t("dbb3"),t("fe59"),t("98e0"),t("ecb4"),t("9302"),t("2eeb"),t("1784"),t("77ad"),t("d497"),t("ea69"),t("b722"),t("3e54"),t("053b"),t("fe8a"),t("be1d"),t("e18c"),t("e35a"),t("340f"),t("1c2e"),t("d543"),t("96db"),t("f4e3"),t("5e9f"),t("6db4"),t("8256"),t("9119"),t("d838"),t("ab6e"),t("927c"),t("3c51"),t("db0a"),t("1cc1"),t("ab0f"),t("fc6e"),t("f30b"),t("cfd1"),t("d104"),t("f74a"),t("3598"),t("b497"),t("2909"),t("a7ef"),t("b523"),t("e671"),t("4140"),t("83db"),t("829d"),t("939f"),t("1bb1"),t("c3ba"),t("08ba"),t("af86");var B=t("2b9d");
/*!
 * html2canvas 1.0.0-rc.4 <https://html2canvas.hertzen.com>
 * Copyright (c) 2019 Niklas von Hertzen <https://hertzen.com>
 * Released under MIT License
 */(function(s,o){"object"===B(e)&&"undefined"!==typeof A?A.exports=o():(r=o,n="function"===typeof r?r.call(e,t,e,A):r,void 0===n||(A.exports=n))})(0,(function(){"use strict";
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation. All rights reserved.
  Licensed under the Apache License, Version 2.0 (the "License"); you may not use
  this file except in compliance with the License. You may obtain a copy of the
  License at http://www.apache.org/licenses/LICENSE-2.0
    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
  WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
  MERCHANTABLITY OR NON-INFRINGEMENT.
    See the Apache Version 2.0 License for specific language governing permissions
  and limitations under the License.
  ***************************************************************************** */var A=function(e,t){return A=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(A,e){A.__proto__=e}||function(A,e){for(var t in e)e.hasOwnProperty(t)&&(A[t]=e[t])},A(e,t)};function e(e,t){function r(){this.constructor=e}A(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var t=function(){return t=Object.assign||function(A){for(var e,t=1,r=arguments.length;t<r;t++)for(var n in e=arguments[t],e)Object.prototype.hasOwnProperty.call(e,n)&&(A[n]=e[n]);return A},t.apply(this,arguments)};function r(A,e,t,r){return new(t||(t=Promise))((function(n,B){function s(A){try{i(r.next(A))}catch(ye){B(ye)}}function o(A){try{i(r["throw"](A))}catch(ye){B(ye)}}function i(A){A.done?n(A.value):new t((function(e){e(A.value)})).then(s,o)}i((r=r.apply(A,e||[])).next())}))}function n(A,e){var t,r,n,B,s={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return B={next:o(0),throw:o(1),return:o(2)},"function"===typeof Symbol&&(B[Symbol.iterator]=function(){return this}),B;function o(A){return function(e){return i([A,e])}}function i(B){if(t)throw new TypeError("Generator is already executing.");while(s)try{if(t=1,r&&(n=2&B[0]?r["return"]:B[0]?r["throw"]||((n=r["return"])&&n.call(r),0):r.next)&&!(n=n.call(r,B[1])).done)return n;switch(r=0,n&&(B=[2&B[0],n.value]),B[0]){case 0:case 1:n=B;break;case 4:return s.label++,{value:B[1],done:!1};case 5:s.label++,r=B[1],B=[0];continue;case 7:B=s.ops.pop(),s.trys.pop();continue;default:if(n=s.trys,!(n=n.length>0&&n[n.length-1])&&(6===B[0]||2===B[0])){s=0;continue}if(3===B[0]&&(!n||B[1]>n[0]&&B[1]<n[3])){s.label=B[1];break}if(6===B[0]&&s.label<n[1]){s.label=n[1],n=B;break}if(n&&s.label<n[2]){s.label=n[2],s.ops.push(B);break}n[2]&&s.ops.pop(),s.trys.pop();continue}B=e.call(A,s)}catch(ye){B=[6,ye],r=0}finally{t=n=0}if(5&B[0])throw B[1];return{value:B[0]?B[1]:void 0,done:!0}}}for(var B=function(){function A(A,e,t,r){this.left=A,this.top=e,this.width=t,this.height=r}return A.prototype.add=function(e,t,r,n){return new A(this.left+e,this.top+t,this.width+r,this.height+n)},A.fromClientRect=function(e){return new A(e.left,e.top,e.width,e.height)},A}(),s=function(A){return B.fromClientRect(A.getBoundingClientRect())},o=function(A){var e=A.body,t=A.documentElement;if(!e||!t)throw new Error("Unable to get document size");var r=Math.max(Math.max(e.scrollWidth,t.scrollWidth),Math.max(e.offsetWidth,t.offsetWidth),Math.max(e.clientWidth,t.clientWidth)),n=Math.max(Math.max(e.scrollHeight,t.scrollHeight),Math.max(e.offsetHeight,t.offsetHeight),Math.max(e.clientHeight,t.clientHeight));return new B(0,0,r,n)},i=function(A){var e=[],t=0,r=A.length;while(t<r){var n=A.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var B=A.charCodeAt(t++);56320===(64512&B)?e.push(((1023&n)<<10)+(1023&B)+65536):(e.push(n),t--)}else e.push(n)}return e},a=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";var r=[],n=-1,B="";while(++n<t){var s=A[n];s<=65535?r.push(s):(s-=65536,r.push(55296+(s>>10),s%1024+56320)),(n+1===t||r.length>16384)&&(B+=String.fromCharCode.apply(String,r),r.length=0)}return B},c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Q="undefined"===typeof Uint8Array?[]:new Uint8Array(256),u=0;u<c.length;u++)Q[c.charCodeAt(u)]=u;var w,U=function(A){var e,t,r,n,B,s=.75*A.length,o=A.length,i=0;"="===A[A.length-1]&&(s--,"="===A[A.length-2]&&s--);var a="undefined"!==typeof ArrayBuffer&&"undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint8Array.prototype.slice?new ArrayBuffer(s):new Array(s),c=Array.isArray(a)?a:new Uint8Array(a);for(e=0;e<o;e+=4)t=Q[A.charCodeAt(e)],r=Q[A.charCodeAt(e+1)],n=Q[A.charCodeAt(e+2)],B=Q[A.charCodeAt(e+3)],c[i++]=t<<2|r>>4,c[i++]=(15&r)<<4|n>>2,c[i++]=(3&n)<<6|63&B;return a},l=function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t},C=function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t},g=5,E=11,F=2,h=E-g,H=65536>>g,d=1<<g,f=d-1,p=1024>>g,N=H+p,K=N,I=32,T=K+I,m=65536>>E,R=1<<h,L=R-1,O=function(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))},b=function(A,e,t){return A.slice?A.slice(e,t):new Uint32Array(Array.prototype.slice.call(A,e,t))},D=function(A){var e=U(A),t=Array.isArray(e)?C(e):new Uint32Array(e),r=Array.isArray(e)?l(e):new Uint16Array(e),n=24,B=O(r,n/2,t[4]/2),s=2===t[5]?O(r,(n+t[4])/2):b(t,Math.ceil((n+t[4])/4));return new v(t[0],t[1],t[2],t[3],B,s)},v=function(){function A(A,e,t,r,n,B){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=n,this.data=B}return A.prototype.get=function(A){var e;if(A>=0){if(A<55296||A>56319&&A<=65535)return e=this.index[A>>g],e=(e<<F)+(A&f),this.data[e];if(A<=65535)return e=this.index[H+(A-55296>>g)],e=(e<<F)+(A&f),this.data[e];if(A<this.highStart)return e=T-m+(A>>E),e=this.index[e],e+=A>>g&L,e=this.index[e],e=(e<<F)+(A&f),this.data[e];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),S="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",M=50,y=1,_=2,P=3,x=4,V=5,z=7,X=8,J=9,G=10,k=11,W=12,Y=13,q=14,Z=15,j=16,$=17,AA=18,eA=19,tA=20,rA=21,nA=22,BA=23,sA=24,oA=25,iA=26,aA=27,cA=28,QA=29,uA=30,wA=31,UA=32,lA=33,CA=34,gA=35,EA=36,FA=37,hA=38,HA=39,dA=40,fA=41,pA=42,NA=43,KA="!",IA="×",TA="÷",mA=D(S),RA=[uA,EA],LA=[y,_,P,V],OA=[G,X],bA=[aA,iA],DA=LA.concat(OA),vA=[hA,HA,dA,CA,gA],SA=[Z,Y],MA=function(A,e){void 0===e&&(e="strict");var t=[],r=[],n=[];return A.forEach((function(A,B){var s=mA.get(A);if(s>M?(n.push(!0),s-=M):n.push(!1),-1!==["normal","auto","loose"].indexOf(e)&&-1!==[8208,8211,12316,12448].indexOf(A))return r.push(B),t.push(j);if(s===x||s===k){if(0===B)return r.push(B),t.push(uA);var o=t[B-1];return-1===DA.indexOf(o)?(r.push(r[B-1]),t.push(o)):(r.push(B),t.push(uA))}return r.push(B),s===wA?t.push("strict"===e?rA:FA):s===pA||s===QA?t.push(uA):s===NA?A>=131072&&A<=196605||A>=196608&&A<=262141?t.push(FA):t.push(uA):void t.push(s)})),[r,t,n]},yA=function(A,e,t,r){var n=r[t];if(Array.isArray(A)?-1!==A.indexOf(n):A===n){var B=t;while(B<=r.length){B++;var s=r[B];if(s===e)return!0;if(s!==G)break}}if(n===G){B=t;while(B>0){B--;var o=r[B];if(Array.isArray(A)?-1!==A.indexOf(o):A===o){var i=t;while(i<=r.length){i++;s=r[i];if(s===e)return!0;if(s!==G)break}}if(o!==G)break}}return!1},_A=function(A,e){var t=A;while(t>=0){var r=e[t];if(r!==G)return r;t--}return 0},PA=function(A,e,t,r,n){if(0===t[r])return IA;var B=r-1;if(Array.isArray(n)&&!0===n[B])return IA;var s=B-1,o=B+1,i=e[B],a=s>=0?e[s]:0,c=e[o];if(i===_&&c===P)return IA;if(-1!==LA.indexOf(i))return KA;if(-1!==LA.indexOf(c))return IA;if(-1!==OA.indexOf(c))return IA;if(_A(B,e)===X)return TA;if(mA.get(A[B])===k&&(c===FA||c===UA||c===lA))return IA;if(i===z||c===z)return IA;if(i===J)return IA;if(-1===[G,Y,Z].indexOf(i)&&c===J)return IA;if(-1!==[$,AA,eA,sA,cA].indexOf(c))return IA;if(_A(B,e)===nA)return IA;if(yA(BA,nA,B,e))return IA;if(yA([$,AA],rA,B,e))return IA;if(yA(W,W,B,e))return IA;if(i===G)return TA;if(i===BA||c===BA)return IA;if(c===j||i===j)return TA;if(-1!==[Y,Z,rA].indexOf(c)||i===q)return IA;if(a===EA&&-1!==SA.indexOf(i))return IA;if(i===cA&&c===EA)return IA;if(c===tA&&-1!==RA.concat(tA,eA,oA,FA,UA,lA).indexOf(i))return IA;if(-1!==RA.indexOf(c)&&i===oA||-1!==RA.indexOf(i)&&c===oA)return IA;if(i===aA&&-1!==[FA,UA,lA].indexOf(c)||-1!==[FA,UA,lA].indexOf(i)&&c===iA)return IA;if(-1!==RA.indexOf(i)&&-1!==bA.indexOf(c)||-1!==bA.indexOf(i)&&-1!==RA.indexOf(c))return IA;if(-1!==[aA,iA].indexOf(i)&&(c===oA||-1!==[nA,Z].indexOf(c)&&e[o+1]===oA)||-1!==[nA,Z].indexOf(i)&&c===oA||i===oA&&-1!==[oA,cA,sA].indexOf(c))return IA;if(-1!==[oA,cA,sA,$,AA].indexOf(c)){var Q=B;while(Q>=0){var u=e[Q];if(u===oA)return IA;if(-1===[cA,sA].indexOf(u))break;Q--}}if(-1!==[aA,iA].indexOf(c)){Q=-1!==[$,AA].indexOf(i)?s:B;while(Q>=0){u=e[Q];if(u===oA)return IA;if(-1===[cA,sA].indexOf(u))break;Q--}}if(hA===i&&-1!==[hA,HA,CA,gA].indexOf(c)||-1!==[HA,CA].indexOf(i)&&-1!==[HA,dA].indexOf(c)||-1!==[dA,gA].indexOf(i)&&c===dA)return IA;if(-1!==vA.indexOf(i)&&-1!==[tA,iA].indexOf(c)||-1!==vA.indexOf(c)&&i===aA)return IA;if(-1!==RA.indexOf(i)&&-1!==RA.indexOf(c))return IA;if(i===sA&&-1!==RA.indexOf(c))return IA;if(-1!==RA.concat(oA).indexOf(i)&&c===nA||-1!==RA.concat(oA).indexOf(c)&&i===AA)return IA;if(i===fA&&c===fA){var w=t[B],U=1;while(w>0){if(w--,e[w]!==fA)break;U++}if(U%2!==0)return IA}return i===UA&&c===lA?IA:TA},xA=function(A,e){e||(e={lineBreak:"normal",wordBreak:"normal"});var t=MA(A,e.lineBreak),r=t[0],n=t[1],B=t[2];"break-all"!==e.wordBreak&&"break-word"!==e.wordBreak||(n=n.map((function(A){return-1!==[oA,uA,pA].indexOf(A)?FA:A})));var s="keep-all"===e.wordBreak?B.map((function(e,t){return e&&A[t]>=19968&&A[t]<=40959})):void 0;return[r,n,s]},VA=function(){function A(A,e,t,r){this.codePoints=A,this.required=e===KA,this.start=t,this.end=r}return A.prototype.slice=function(){return a.apply(void 0,this.codePoints.slice(this.start,this.end))},A}(),zA=function(A,e){var t=i(A),r=xA(t,e),n=r[0],B=r[1],s=r[2],o=t.length,a=0,c=0;return{next:function(){if(c>=o)return{done:!0,value:null};var A=IA;while(c<o&&(A=PA(t,B,n,++c,s))===IA);if(A!==IA||c===o){var e=new VA(t,A,a,c);return a=c,{value:e,done:!1}}return{done:!0,value:null}}}};(function(A){A[A["STRING_TOKEN"]=0]="STRING_TOKEN",A[A["BAD_STRING_TOKEN"]=1]="BAD_STRING_TOKEN",A[A["LEFT_PARENTHESIS_TOKEN"]=2]="LEFT_PARENTHESIS_TOKEN",A[A["RIGHT_PARENTHESIS_TOKEN"]=3]="RIGHT_PARENTHESIS_TOKEN",A[A["COMMA_TOKEN"]=4]="COMMA_TOKEN",A[A["HASH_TOKEN"]=5]="HASH_TOKEN",A[A["DELIM_TOKEN"]=6]="DELIM_TOKEN",A[A["AT_KEYWORD_TOKEN"]=7]="AT_KEYWORD_TOKEN",A[A["PREFIX_MATCH_TOKEN"]=8]="PREFIX_MATCH_TOKEN",A[A["DASH_MATCH_TOKEN"]=9]="DASH_MATCH_TOKEN",A[A["INCLUDE_MATCH_TOKEN"]=10]="INCLUDE_MATCH_TOKEN",A[A["LEFT_CURLY_BRACKET_TOKEN"]=11]="LEFT_CURLY_BRACKET_TOKEN",A[A["RIGHT_CURLY_BRACKET_TOKEN"]=12]="RIGHT_CURLY_BRACKET_TOKEN",A[A["SUFFIX_MATCH_TOKEN"]=13]="SUFFIX_MATCH_TOKEN",A[A["SUBSTRING_MATCH_TOKEN"]=14]="SUBSTRING_MATCH_TOKEN",A[A["DIMENSION_TOKEN"]=15]="DIMENSION_TOKEN",A[A["PERCENTAGE_TOKEN"]=16]="PERCENTAGE_TOKEN",A[A["NUMBER_TOKEN"]=17]="NUMBER_TOKEN",A[A["FUNCTION"]=18]="FUNCTION",A[A["FUNCTION_TOKEN"]=19]="FUNCTION_TOKEN",A[A["IDENT_TOKEN"]=20]="IDENT_TOKEN",A[A["COLUMN_TOKEN"]=21]="COLUMN_TOKEN",A[A["URL_TOKEN"]=22]="URL_TOKEN",A[A["BAD_URL_TOKEN"]=23]="BAD_URL_TOKEN",A[A["CDC_TOKEN"]=24]="CDC_TOKEN",A[A["CDO_TOKEN"]=25]="CDO_TOKEN",A[A["COLON_TOKEN"]=26]="COLON_TOKEN",A[A["SEMICOLON_TOKEN"]=27]="SEMICOLON_TOKEN",A[A["LEFT_SQUARE_BRACKET_TOKEN"]=28]="LEFT_SQUARE_BRACKET_TOKEN",A[A["RIGHT_SQUARE_BRACKET_TOKEN"]=29]="RIGHT_SQUARE_BRACKET_TOKEN",A[A["UNICODE_RANGE_TOKEN"]=30]="UNICODE_RANGE_TOKEN",A[A["WHITESPACE_TOKEN"]=31]="WHITESPACE_TOKEN",A[A["EOF_TOKEN"]=32]="EOF_TOKEN"})(w||(w={}));var XA=1,JA=2,GA=4,kA=8,WA=10,YA=47,qA=92,ZA=9,jA=32,$A=34,Ae=61,ee=35,te=36,re=37,ne=39,Be=40,se=41,oe=95,ie=45,ae=33,ce=60,Qe=62,ue=64,we=91,Ue=93,le=61,Ce=123,ge=63,Ee=125,Fe=124,he=126,He=128,de=65533,fe=42,pe=43,Ne=44,Ke=58,Ie=59,Te=46,me=0,Re=8,Le=11,Oe=14,be=31,De=127,ve=-1,Se=48,Me=97,ye=101,_e=102,Pe=117,xe=122,Ve=65,ze=69,Xe=70,Je=85,Ge=90,ke=function(A){return A>=Se&&A<=57},We=function(A){return A>=55296&&A<=57343},Ye=function(A){return ke(A)||A>=Ve&&A<=Xe||A>=Me&&A<=_e},qe=function(A){return A>=Me&&A<=xe},Ze=function(A){return A>=Ve&&A<=Ge},je=function(A){return qe(A)||Ze(A)},$e=function(A){return A>=He},At=function(A){return A===WA||A===ZA||A===jA},et=function(A){return je(A)||$e(A)||A===oe},tt=function(A){return et(A)||ke(A)||A===ie},rt=function(A){return A>=me&&A<=Re||A===Le||A>=Oe&&A<=be||A===De},nt=function(A,e){return A===qA&&e!==WA},Bt=function(A,e,t){return A===ie?et(e)||nt(e,t):!!et(A)||!(A!==qA||!nt(A,e))},st=function(A,e,t){return A===pe||A===ie?!!ke(e)||e===Te&&ke(t):ke(A===Te?e:A)},ot=function(A){var e=0,t=1;A[e]!==pe&&A[e]!==ie||(A[e]===ie&&(t=-1),e++);var r=[];while(ke(A[e]))r.push(A[e++]);var n=r.length?parseInt(a.apply(void 0,r),10):0;A[e]===Te&&e++;var B=[];while(ke(A[e]))B.push(A[e++]);var s=B.length,o=s?parseInt(a.apply(void 0,B),10):0;A[e]!==ze&&A[e]!==ye||e++;var i=1;A[e]!==pe&&A[e]!==ie||(A[e]===ie&&(i=-1),e++);var c=[];while(ke(A[e]))c.push(A[e++]);var Q=c.length?parseInt(a.apply(void 0,c),10):0;return t*(n+o*Math.pow(10,-s))*Math.pow(10,i*Q)},it={type:w.LEFT_PARENTHESIS_TOKEN},at={type:w.RIGHT_PARENTHESIS_TOKEN},ct={type:w.COMMA_TOKEN},Qt={type:w.SUFFIX_MATCH_TOKEN},ut={type:w.PREFIX_MATCH_TOKEN},wt={type:w.COLUMN_TOKEN},Ut={type:w.DASH_MATCH_TOKEN},lt={type:w.INCLUDE_MATCH_TOKEN},Ct={type:w.LEFT_CURLY_BRACKET_TOKEN},gt={type:w.RIGHT_CURLY_BRACKET_TOKEN},Et={type:w.SUBSTRING_MATCH_TOKEN},Ft={type:w.BAD_URL_TOKEN},ht={type:w.BAD_STRING_TOKEN},Ht={type:w.CDO_TOKEN},dt={type:w.CDC_TOKEN},ft={type:w.COLON_TOKEN},pt={type:w.SEMICOLON_TOKEN},Nt={type:w.LEFT_SQUARE_BRACKET_TOKEN},Kt={type:w.RIGHT_SQUARE_BRACKET_TOKEN},It={type:w.WHITESPACE_TOKEN},Tt={type:w.EOF_TOKEN},mt=function(){function A(){this._value=[]}return A.prototype.write=function(A){this._value=this._value.concat(i(A))},A.prototype.read=function(){var A=[],e=this.consumeToken();while(e!==Tt)A.push(e),e=this.consumeToken();return A},A.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case $A:return this.consumeStringToken($A);case ee:var e=this.peekCodePoint(0),t=this.peekCodePoint(1),r=this.peekCodePoint(2);if(tt(e)||nt(t,r)){var n=Bt(e,t,r)?JA:XA,B=this.consumeName();return{type:w.HASH_TOKEN,value:B,flags:n}}break;case te:if(this.peekCodePoint(0)===Ae)return this.consumeCodePoint(),Qt;break;case ne:return this.consumeStringToken(ne);case Be:return it;case se:return at;case fe:if(this.peekCodePoint(0)===Ae)return this.consumeCodePoint(),Et;break;case pe:if(st(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case Ne:return ct;case ie:var s=A,o=this.peekCodePoint(0),i=this.peekCodePoint(1);if(st(s,o,i))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(Bt(s,o,i))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(o===ie&&i===Qe)return this.consumeCodePoint(),this.consumeCodePoint(),dt;break;case Te:if(st(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case YA:if(this.peekCodePoint(0)===fe){this.consumeCodePoint();while(1){var c=this.consumeCodePoint();if(c===fe&&(c=this.consumeCodePoint(),c===YA))return this.consumeToken();if(c===ve)return this.consumeToken()}}break;case Ke:return ft;case Ie:return pt;case ce:if(this.peekCodePoint(0)===ae&&this.peekCodePoint(1)===ie&&this.peekCodePoint(2)===ie)return this.consumeCodePoint(),this.consumeCodePoint(),Ht;break;case ue:var Q=this.peekCodePoint(0),u=this.peekCodePoint(1),U=this.peekCodePoint(2);if(Bt(Q,u,U)){B=this.consumeName();return{type:w.AT_KEYWORD_TOKEN,value:B}}break;case we:return Nt;case qA:if(nt(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case Ue:return Kt;case le:if(this.peekCodePoint(0)===Ae)return this.consumeCodePoint(),ut;break;case Ce:return Ct;case Ee:return gt;case Pe:case Je:var l=this.peekCodePoint(0),C=this.peekCodePoint(1);return l!==pe||!Ye(C)&&C!==ge||(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case Fe:if(this.peekCodePoint(0)===Ae)return this.consumeCodePoint(),Ut;if(this.peekCodePoint(0)===Fe)return this.consumeCodePoint(),wt;break;case he:if(this.peekCodePoint(0)===Ae)return this.consumeCodePoint(),lt;break;case ve:return Tt}return At(A)?(this.consumeWhiteSpace(),It):ke(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):et(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:w.DELIM_TOKEN,value:a(A)}},A.prototype.consumeCodePoint=function(){var A=this._value.shift();return"undefined"===typeof A?-1:A},A.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},A.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},A.prototype.consumeUnicodeRangeToken=function(){var A=[],e=this.consumeCodePoint();while(Ye(e)&&A.length<6)A.push(e),e=this.consumeCodePoint();var t=!1;while(e===ge&&A.length<6)A.push(e),e=this.consumeCodePoint(),t=!0;if(t){var r=parseInt(a.apply(void 0,A.map((function(A){return A===ge?Se:A}))),16),n=parseInt(a.apply(void 0,A.map((function(A){return A===ge?Xe:A}))),16);return{type:w.UNICODE_RANGE_TOKEN,start:r,end:n}}var B=parseInt(a.apply(void 0,A),16);if(this.peekCodePoint(0)===ie&&Ye(this.peekCodePoint(1))){this.consumeCodePoint(),e=this.consumeCodePoint();var s=[];while(Ye(e)&&s.length<6)s.push(e),e=this.consumeCodePoint();n=parseInt(a.apply(void 0,s),16);return{type:w.UNICODE_RANGE_TOKEN,start:B,end:n}}return{type:w.UNICODE_RANGE_TOKEN,start:B,end:B}},A.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return"url"===A.toLowerCase()&&this.peekCodePoint(0)===Be?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===Be?(this.consumeCodePoint(),{type:w.FUNCTION_TOKEN,value:A}):{type:w.IDENT_TOKEN,value:A}},A.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===ve)return{type:w.URL_TOKEN,value:""};var e=this.peekCodePoint(0);if(e===ne||e===$A){var t=this.consumeStringToken(this.consumeCodePoint());return t.type===w.STRING_TOKEN&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===ve||this.peekCodePoint(0)===se)?(this.consumeCodePoint(),{type:w.URL_TOKEN,value:t.value}):(this.consumeBadUrlRemnants(),Ft)}while(1){var r=this.consumeCodePoint();if(r===ve||r===se)return{type:w.URL_TOKEN,value:a.apply(void 0,A)};if(At(r))return this.consumeWhiteSpace(),this.peekCodePoint(0)===ve||this.peekCodePoint(0)===se?(this.consumeCodePoint(),{type:w.URL_TOKEN,value:a.apply(void 0,A)}):(this.consumeBadUrlRemnants(),Ft);if(r===$A||r===ne||r===Be||rt(r))return this.consumeBadUrlRemnants(),Ft;if(r===qA){if(!nt(r,this.peekCodePoint(0)))return this.consumeBadUrlRemnants(),Ft;A.push(this.consumeEscapedCodePoint())}else A.push(r)}},A.prototype.consumeWhiteSpace=function(){while(At(this.peekCodePoint(0)))this.consumeCodePoint()},A.prototype.consumeBadUrlRemnants=function(){while(1){var A=this.consumeCodePoint();if(A===se||A===ve)return;nt(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},A.prototype.consumeStringSlice=function(A){var e=6e4,t="";while(A>0){var r=Math.min(e,A);t+=a.apply(void 0,this._value.splice(0,r)),A-=r}return this._value.shift(),t},A.prototype.consumeStringToken=function(A){var e="",t=0;do{var r=this._value[t];if(r===ve||void 0===r||r===A)return e+=this.consumeStringSlice(t),{type:w.STRING_TOKEN,value:e};if(r===WA)return this._value.splice(0,t),ht;if(r===qA){var n=this._value[t+1];n!==ve&&void 0!==n&&(n===WA?(e+=this.consumeStringSlice(t),t=-1,this._value.shift()):nt(r,n)&&(e+=this.consumeStringSlice(t),e+=a(this.consumeEscapedCodePoint()),t=-1))}t++}while(1)},A.prototype.consumeNumber=function(){var A=[],e=GA,t=this.peekCodePoint(0);t!==pe&&t!==ie||A.push(this.consumeCodePoint());while(ke(this.peekCodePoint(0)))A.push(this.consumeCodePoint());t=this.peekCodePoint(0);var r=this.peekCodePoint(1);if(t===Te&&ke(r)){A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=kA;while(ke(this.peekCodePoint(0)))A.push(this.consumeCodePoint())}t=this.peekCodePoint(0),r=this.peekCodePoint(1);var n=this.peekCodePoint(2);if((t===ze||t===ye)&&((r===pe||r===ie)&&ke(n)||ke(r))){A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=kA;while(ke(this.peekCodePoint(0)))A.push(this.consumeCodePoint())}return[ot(A),e]},A.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),e=A[0],t=A[1],r=this.peekCodePoint(0),n=this.peekCodePoint(1),B=this.peekCodePoint(2);if(Bt(r,n,B)){var s=this.consumeName();return{type:w.DIMENSION_TOKEN,number:e,flags:t,unit:s}}return r===re?(this.consumeCodePoint(),{type:w.PERCENTAGE_TOKEN,number:e,flags:t}):{type:w.NUMBER_TOKEN,number:e,flags:t}},A.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(Ye(A)){var e=a(A);while(Ye(this.peekCodePoint(0))&&e.length<6)e+=a(this.consumeCodePoint());At(this.peekCodePoint(0))&&this.consumeCodePoint();var t=parseInt(e,16);return 0===t||We(t)||t>1114111?de:t}return A===ve?de:A},A.prototype.consumeName=function(){var A="";while(1){var e=this.consumeCodePoint();if(tt(e))A+=a(e);else{if(!nt(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),A;A+=a(this.consumeEscapedCodePoint())}}},A}(),Rt=function(){function A(A){this._tokens=A}return A.create=function(e){var t=new mt;return t.write(e),new A(t.read())},A.parseValue=function(e){return A.create(e).parseComponentValue()},A.parseValues=function(e){return A.create(e).parseComponentValues()},A.prototype.parseComponentValue=function(){var A=this.consumeToken();while(A.type===w.WHITESPACE_TOKEN)A=this.consumeToken();if(A.type===w.EOF_TOKEN)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var e=this.consumeComponentValue();do{A=this.consumeToken()}while(A.type===w.WHITESPACE_TOKEN);if(A.type===w.EOF_TOKEN)return e;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},A.prototype.parseComponentValues=function(){var A=[];while(1){var e=this.consumeComponentValue();if(e.type===w.EOF_TOKEN)return A;A.push(e),A.push()}},A.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case w.LEFT_CURLY_BRACKET_TOKEN:case w.LEFT_SQUARE_BRACKET_TOKEN:case w.LEFT_PARENTHESIS_TOKEN:return this.consumeSimpleBlock(A.type);case w.FUNCTION_TOKEN:return this.consumeFunction(A)}return A},A.prototype.consumeSimpleBlock=function(A){var e={type:A,values:[]},t=this.consumeToken();while(1){if(t.type===w.EOF_TOKEN||_t(t,A))return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue()),t=this.consumeToken()}},A.prototype.consumeFunction=function(A){var e={name:A.value,values:[],type:w.FUNCTION};while(1){var t=this.consumeToken();if(t.type===w.EOF_TOKEN||t.type===w.RIGHT_PARENTHESIS_TOKEN)return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue())}},A.prototype.consumeToken=function(){var A=this._tokens.shift();return"undefined"===typeof A?Tt:A},A.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},A}(),Lt=function(A){return A.type===w.DIMENSION_TOKEN},Ot=function(A){return A.type===w.NUMBER_TOKEN},bt=function(A){return A.type===w.IDENT_TOKEN},Dt=function(A){return A.type===w.STRING_TOKEN},vt=function(A,e){return bt(A)&&A.value===e},St=function(A){return A.type!==w.WHITESPACE_TOKEN},Mt=function(A){return A.type!==w.WHITESPACE_TOKEN&&A.type!==w.COMMA_TOKEN},yt=function(A){var e=[],t=[];return A.forEach((function(A){if(A.type===w.COMMA_TOKEN){if(0===t.length)throw new Error("Error parsing function args, zero tokens for arg");return e.push(t),void(t=[])}A.type!==w.WHITESPACE_TOKEN&&t.push(A)})),t.length&&e.push(t),e},_t=function(A,e){return e===w.LEFT_CURLY_BRACKET_TOKEN&&A.type===w.RIGHT_CURLY_BRACKET_TOKEN||(e===w.LEFT_SQUARE_BRACKET_TOKEN&&A.type===w.RIGHT_SQUARE_BRACKET_TOKEN||e===w.LEFT_PARENTHESIS_TOKEN&&A.type===w.RIGHT_PARENTHESIS_TOKEN)},Pt=function(A){return A.type===w.NUMBER_TOKEN||A.type===w.DIMENSION_TOKEN},xt=function(A){return A.type===w.PERCENTAGE_TOKEN||Pt(A)},Vt=function(A){return A.length>1?[A[0],A[1]]:[A[0]]},zt={type:w.NUMBER_TOKEN,number:0,flags:GA},Xt={type:w.PERCENTAGE_TOKEN,number:50,flags:GA},Jt={type:w.PERCENTAGE_TOKEN,number:100,flags:GA},Gt=function(A,e,t){var r=A[0],n=A[1];return[kt(r,e),kt("undefined"!==typeof n?n:r,t)]},kt=function(A,e){if(A.type===w.PERCENTAGE_TOKEN)return A.number/100*e;if(Lt(A))switch(A.unit){case"rem":case"em":return 16*A.number;case"px":default:return A.number}return A.number},Wt="deg",Yt="grad",qt="rad",Zt="turn",jt={name:"angle",parse:function(A){if(A.type===w.DIMENSION_TOKEN)switch(A.unit){case Wt:return Math.PI*A.number/180;case Yt:return Math.PI/200*A.number;case qt:return A.number;case Zt:return 2*Math.PI*A.number}throw new Error("Unsupported angle type")}},$t=function(A){return A.type===w.DIMENSION_TOKEN&&(A.unit===Wt||A.unit===Yt||A.unit===qt||A.unit===Zt)},Ar=function(A){var e=A.filter(bt).map((function(A){return A.value})).join(" ");switch(e){case"to bottom right":case"to right bottom":case"left top":case"top left":return[zt,zt];case"to top":case"bottom":return er(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[zt,Jt];case"to right":case"left":return er(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[Jt,Jt];case"to bottom":case"top":return er(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[Jt,zt];case"to left":case"right":return er(270)}return 0},er=function(A){return Math.PI*A/180},tr={name:"color",parse:function(A){if(A.type===w.FUNCTION){var e=ur[A.name];if("undefined"===typeof e)throw new Error('Attempting to parse an unsupported color function "'+A.name+'"');return e(A.values)}if(A.type===w.HASH_TOKEN){if(3===A.value.length){var t=A.value.substring(0,1),r=A.value.substring(1,2),n=A.value.substring(2,3);return Br(parseInt(t+t,16),parseInt(r+r,16),parseInt(n+n,16),1)}if(4===A.value.length){t=A.value.substring(0,1),r=A.value.substring(1,2),n=A.value.substring(2,3);var B=A.value.substring(3,4);return Br(parseInt(t+t,16),parseInt(r+r,16),parseInt(n+n,16),parseInt(B+B,16)/255)}if(6===A.value.length){t=A.value.substring(0,2),r=A.value.substring(2,4),n=A.value.substring(4,6);return Br(parseInt(t,16),parseInt(r,16),parseInt(n,16),1)}if(8===A.value.length){t=A.value.substring(0,2),r=A.value.substring(2,4),n=A.value.substring(4,6),B=A.value.substring(6,8);return Br(parseInt(t,16),parseInt(r,16),parseInt(n,16),parseInt(B,16)/255)}}if(A.type===w.IDENT_TOKEN){var s=wr[A.value.toUpperCase()];if("undefined"!==typeof s)return s}return wr.TRANSPARENT}},rr=function(A){return 0===(255&A)},nr=function(A){var e=255&A,t=255&A>>8,r=255&A>>16,n=255&A>>24;return e<255?"rgba("+n+","+r+","+t+","+e/255+")":"rgb("+n+","+r+","+t+")"},Br=function(A,e,t,r){return(A<<24|e<<16|t<<8|Math.round(255*r)<<0)>>>0},sr=function(A,e){if(A.type===w.NUMBER_TOKEN)return A.number;if(A.type===w.PERCENTAGE_TOKEN){var t=3===e?1:255;return 3===e?A.number/100*t:Math.round(A.number/100*t)}return 0},or=function(A){var e=A.filter(Mt);if(3===e.length){var t=e.map(sr),r=t[0],n=t[1],B=t[2];return Br(r,n,B,1)}if(4===e.length){var s=e.map(sr),o=(r=s[0],n=s[1],B=s[2],s[3]);return Br(r,n,B,o)}return 0};function ir(A,e,t){return t<0&&(t+=1),t>=1&&(t-=1),t<1/6?(e-A)*t*6+A:t<.5?e:t<2/3?6*(e-A)*(2/3-t)+A:A}var ar,cr,Qr=function(A){var e=A.filter(Mt),t=e[0],r=e[1],n=e[2],B=e[3],s=(t.type===w.NUMBER_TOKEN?er(t.number):jt.parse(t))/(2*Math.PI),o=xt(r)?r.number/100:0,i=xt(n)?n.number/100:0,a="undefined"!==typeof B&&xt(B)?kt(B,1):1;if(0===o)return Br(255*i,255*i,255*i,1);var c=i<=.5?i*(o+1):i+o-i*o,Q=2*i-c,u=ir(Q,c,s+1/3),U=ir(Q,c,s),l=ir(Q,c,s-1/3);return Br(255*u,255*U,255*l,a)},ur={hsl:Qr,hsla:Qr,rgb:or,rgba:or},wr={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199};(function(A){A[A["VALUE"]=0]="VALUE",A[A["LIST"]=1]="LIST",A[A["IDENT_VALUE"]=2]="IDENT_VALUE",A[A["TYPE_VALUE"]=3]="TYPE_VALUE",A[A["TOKEN_VALUE"]=4]="TOKEN_VALUE"})(ar||(ar={})),function(A){A[A["BORDER_BOX"]=0]="BORDER_BOX",A[A["PADDING_BOX"]=1]="PADDING_BOX",A[A["CONTENT_BOX"]=2]="CONTENT_BOX"}(cr||(cr={}));var Ur,lr={name:"background-clip",initialValue:"border-box",prefix:!1,type:ar.LIST,parse:function(A){return A.map((function(A){if(bt(A))switch(A.value){case"padding-box":return cr.PADDING_BOX;case"content-box":return cr.CONTENT_BOX}return cr.BORDER_BOX}))}},Cr={name:"background-color",initialValue:"transparent",prefix:!1,type:ar.TYPE_VALUE,format:"color"},gr=function(A){var e=tr.parse(A[0]),t=A[1];return t&&xt(t)?{color:e,stop:t}:{color:e,stop:null}},Er=function(A,e){var t=A[0],r=A[A.length-1];null===t.stop&&(t.stop=zt),null===r.stop&&(r.stop=Jt);for(var n=[],B=0,s=0;s<A.length;s++){var o=A[s].stop;if(null!==o){var i=kt(o,e);i>B?n.push(i):n.push(B),B=i}else n.push(null)}var a=null;for(s=0;s<n.length;s++){var c=n[s];if(null===c)null===a&&(a=s);else if(null!==a){for(var Q=s-a,u=n[a-1],w=(c-u)/(Q+1),U=1;U<=Q;U++)n[a+U-1]=w*U;a=null}}return A.map((function(A,t){var r=A.color;return{color:r,stop:Math.max(Math.min(1,n[t]/e),0)}}))},Fr=function(A,e,t){var r=e/2,n=t/2,B=kt(A[0],e)-r,s=n-kt(A[1],t);return(Math.atan2(s,B)+2*Math.PI)%(2*Math.PI)},hr=function(A,e,t){var r="number"===typeof A?A:Fr(A,e,t),n=Math.abs(e*Math.sin(r))+Math.abs(t*Math.cos(r)),B=e/2,s=t/2,o=n/2,i=Math.sin(r-Math.PI/2)*o,a=Math.cos(r-Math.PI/2)*o;return[n,B-a,B+a,s-i,s+i]},Hr=function(A,e){return Math.sqrt(A*A+e*e)},dr=function(A,e,t,r,n){var B=[[0,0],[0,e],[A,0],[A,e]];return B.reduce((function(A,e){var B=e[0],s=e[1],o=Hr(t-B,r-s);return(n?o<A.optimumDistance:o>A.optimumDistance)?{optimumCorner:e,optimumDistance:o}:A}),{optimumDistance:n?1/0:-1/0,optimumCorner:null}).optimumCorner},fr=function(A,e,t,r,n){var B=0,s=0;switch(A.size){case nn.CLOSEST_SIDE:A.shape===rn.CIRCLE?B=s=Math.min(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):A.shape===rn.ELLIPSE&&(B=Math.min(Math.abs(e),Math.abs(e-r)),s=Math.min(Math.abs(t),Math.abs(t-n)));break;case nn.CLOSEST_CORNER:if(A.shape===rn.CIRCLE)B=s=Math.min(Hr(e,t),Hr(e,t-n),Hr(e-r,t),Hr(e-r,t-n));else if(A.shape===rn.ELLIPSE){var o=Math.min(Math.abs(t),Math.abs(t-n))/Math.min(Math.abs(e),Math.abs(e-r)),i=dr(r,n,e,t,!0),a=i[0],c=i[1];B=Hr(a-e,(c-t)/o),s=o*B}break;case nn.FARTHEST_SIDE:A.shape===rn.CIRCLE?B=s=Math.max(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):A.shape===rn.ELLIPSE&&(B=Math.max(Math.abs(e),Math.abs(e-r)),s=Math.max(Math.abs(t),Math.abs(t-n)));break;case nn.FARTHEST_CORNER:if(A.shape===rn.CIRCLE)B=s=Math.max(Hr(e,t),Hr(e,t-n),Hr(e-r,t),Hr(e-r,t-n));else if(A.shape===rn.ELLIPSE){o=Math.max(Math.abs(t),Math.abs(t-n))/Math.max(Math.abs(e),Math.abs(e-r));var Q=dr(r,n,e,t,!1);a=Q[0],c=Q[1];B=Hr(a-e,(c-t)/o),s=o*B}break}return Array.isArray(A.size)&&(B=kt(A.size[0],r),s=2===A.size.length?kt(A.size[1],n):B),[B,s]},pr=function(A){var e=er(180),t=[];return yt(A).forEach((function(A,r){if(0===r){var n=A[0];if(n.type===w.IDENT_TOKEN&&"to"===n.value)return void(e=Ar(A));if($t(n))return void(e=jt.parse(n))}var B=gr(A);t.push(B)})),{angle:e,stops:t,type:Ur.LINEAR_GRADIENT}},Nr=function(A){var e=er(180),t=[];return yt(A).forEach((function(A,r){if(0===r){var n=A[0];if(n.type===w.IDENT_TOKEN&&-1!==["top","left","right","bottom"].indexOf(n.value))return void(e=Ar(A));if($t(n))return void(e=(jt.parse(n)+er(270))%er(360))}var B=gr(A);t.push(B)})),{angle:e,stops:t,type:Ur.LINEAR_GRADIENT}},Kr=function(A){var e=123;if(A.createRange){var t=A.createRange();if(t.getBoundingClientRect){var r=A.createElement("boundtest");r.style.height=e+"px",r.style.display="block",A.body.appendChild(r),t.selectNode(r);var n=t.getBoundingClientRect(),B=Math.round(n.height);if(A.body.removeChild(r),B===e)return!0}}return!1},Ir=function(){return"undefined"!==typeof(new Image).crossOrigin},Tr=function(){return"string"===typeof(new XMLHttpRequest).responseType},mr=function(A){var e=new Image,t=A.createElement("canvas"),r=t.getContext("2d");if(!r)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{r.drawImage(e,0,0),t.toDataURL()}catch(ye){return!1}return!0},Rr=function(A){return 0===A[0]&&255===A[1]&&0===A[2]&&255===A[3]},Lr=function(A){var e=A.createElement("canvas"),t=100;e.width=t,e.height=t;var r=e.getContext("2d");if(!r)return Promise.reject(!1);r.fillStyle="rgb(0, 255, 0)",r.fillRect(0,0,t,t);var n=new Image,B=e.toDataURL();n.src=B;var s=Or(t,t,0,0,n);return r.fillStyle="red",r.fillRect(0,0,t,t),br(s).then((function(e){r.drawImage(e,0,0);var n=r.getImageData(0,0,t,t).data;r.fillStyle="red",r.fillRect(0,0,t,t);var s=A.createElement("div");return s.style.backgroundImage="url("+B+")",s.style.height=t+"px",Rr(n)?br(Or(t,t,0,0,s)):Promise.reject(!1)})).then((function(A){return r.drawImage(A,0,0),Rr(r.getImageData(0,0,t,t).data)})).catch((function(){return!1}))},Or=function(A,e,t,r,n){var B="http://www.w3.org/2000/svg",s=document.createElementNS(B,"svg"),o=document.createElementNS(B,"foreignObject");return s.setAttributeNS(null,"width",A.toString()),s.setAttributeNS(null,"height",e.toString()),o.setAttributeNS(null,"width","100%"),o.setAttributeNS(null,"height","100%"),o.setAttributeNS(null,"x",t.toString()),o.setAttributeNS(null,"y",r.toString()),o.setAttributeNS(null,"externalResourcesRequired","true"),s.appendChild(o),o.appendChild(n),s},br=function(A){return new Promise((function(e,t){var r=new Image;r.onload=function(){return e(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},Dr={get SUPPORT_RANGE_BOUNDS(){var A=Kr(document);return Object.defineProperty(Dr,"SUPPORT_RANGE_BOUNDS",{value:A}),A},get SUPPORT_SVG_DRAWING(){var A=mr(document);return Object.defineProperty(Dr,"SUPPORT_SVG_DRAWING",{value:A}),A},get SUPPORT_FOREIGNOBJECT_DRAWING(){var A="function"===typeof Array.from&&"function"===typeof window.fetch?Lr(document):Promise.resolve(!1);return Object.defineProperty(Dr,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:A}),A},get SUPPORT_CORS_IMAGES(){var A=Ir();return Object.defineProperty(Dr,"SUPPORT_CORS_IMAGES",{value:A}),A},get SUPPORT_RESPONSE_TYPE(){var A=Tr();return Object.defineProperty(Dr,"SUPPORT_RESPONSE_TYPE",{value:A}),A},get SUPPORT_CORS_XHR(){var A="withCredentials"in new XMLHttpRequest;return Object.defineProperty(Dr,"SUPPORT_CORS_XHR",{value:A}),A}},vr=function(){function A(A){this.id=A,this.start=Date.now()}return A.prototype.debug=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];"undefined"!==typeof window&&window.console&&"function"===typeof function(){}||this.info.apply(this,A)},A.prototype.getTime=function(){return Date.now()-this.start},A.create=function(e){A.instances[e]=new A(e)},A.destroy=function(e){delete A.instances[e]},A.getInstance=function(e){var t=A.instances[e];if("undefined"===typeof t)throw new Error("No logger instance found with id "+e);return t},A.prototype.info=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];"undefined"!==typeof window&&window.console},A.prototype.error=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];"undefined"!==typeof window&&window.console&&"function"===typeof function(){}||this.info.apply(this,A)},A.instances={},A}(),Sr=function(){function A(){}return A.create=function(e,t){return A._caches[e]=new Mr(e,t)},A.destroy=function(e){delete A._caches[e]},A.open=function(e){var t=A._caches[e];if("undefined"!==typeof t)return t;throw new Error('Cache with key "'+e+'" not found')},A.getOrigin=function(e){var t=A._link;return t?(t.href=e,t.href=t.href,t.protocol+t.hostname+t.port):"about:blank"},A.isSameOrigin=function(e){return A.getOrigin(e)===A._origin},A.setContext=function(e){A._link=e.document.createElement("a"),A._origin=A.getOrigin(e.location.href)},A.getInstance=function(){var e=A._current;if(null===e)throw new Error("No cache instance attached");return e},A.attachInstance=function(e){A._current=e},A.detachInstance=function(){A._current=null},A._caches={},A._origin="about:blank",A._current=null,A}(),Mr=function(){function A(A,e){this.id=A,this._options=e,this._cache={}}return A.prototype.addImage=function(A){var e=Promise.resolve();return this.has(A)?e:Xr(A)||xr(A)?(this._cache[A]=this.loadImage(A),e):e},A.prototype.match=function(A){return this._cache[A]},A.prototype.loadImage=function(A){return r(this,void 0,void 0,(function(){var e,t,r,B,s=this;return n(this,(function(n){switch(n.label){case 0:return e=Sr.isSameOrigin(A),t=!Vr(A)&&!0===this._options.useCORS&&Dr.SUPPORT_CORS_IMAGES&&!e,r=!Vr(A)&&!e&&"string"===typeof this._options.proxy&&Dr.SUPPORT_CORS_XHR&&!t,e||!1!==this._options.allowTaint||Vr(A)||r||t?(B=A,r?[4,this.proxy(B)]:[3,2]):[2];case 1:B=n.sent(),n.label=2;case 2:return vr.getInstance(this.id).debug("Added image "+A.substring(0,256)),[4,new Promise((function(A,e){var r=new Image;r.onload=function(){return A(r)},r.onerror=e,(zr(B)||t)&&(r.crossOrigin="anonymous"),r.src=B,!0===r.complete&&setTimeout((function(){return A(r)}),500),s._options.imageTimeout>0&&setTimeout((function(){return e("Timed out ("+s._options.imageTimeout+"ms) loading image")}),s._options.imageTimeout)}))];case 3:return[2,n.sent()]}}))}))},A.prototype.has=function(A){return"undefined"!==typeof this._cache[A]},A.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},A.prototype.proxy=function(A){var e=this,t=this._options.proxy;if(!t)throw new Error("No proxy defined");var r=A.substring(0,256);return new Promise((function(n,B){var s=Dr.SUPPORT_RESPONSE_TYPE?"blob":"text",o=new XMLHttpRequest;if(o.onload=function(){if(200===o.status)if("text"===s)n(o.response);else{var A=new FileReader;A.addEventListener("load",(function(){return n(A.result)}),!1),A.addEventListener("error",(function(A){return B(A)}),!1),A.readAsDataURL(o.response)}else B("Failed to proxy resource "+r+" with status code "+o.status)},o.onerror=B,o.open("GET",t+"?url="+encodeURIComponent(A)+"&responseType="+s),"text"!==s&&o instanceof XMLHttpRequest&&(o.responseType=s),e._options.imageTimeout){var i=e._options.imageTimeout;o.timeout=i,o.ontimeout=function(){return B("Timed out ("+i+"ms) proxying "+r)}}o.send()}))},A}(),yr=/^data:image\/svg\+xml/i,_r=/^data:image\/.*;base64,/i,Pr=/^data:image\/.*/i,xr=function(A){return Dr.SUPPORT_SVG_DRAWING||!Jr(A)},Vr=function(A){return Pr.test(A)},zr=function(A){return _r.test(A)},Xr=function(A){return"blob"===A.substr(0,4)},Jr=function(A){return"svg"===A.substr(-3).toLowerCase()||yr.test(A)},Gr=function(A){var e=er(180),t=[],r=Ur.LINEAR_GRADIENT,n=rn.CIRCLE,B=nn.FARTHEST_CORNER,s=[];return yt(A).forEach((function(A,e){var n=A[0];if(0===e){if(bt(n)&&"linear"===n.value)return void(r=Ur.LINEAR_GRADIENT);if(bt(n)&&"radial"===n.value)return void(r=Ur.RADIAL_GRADIENT)}if(n.type===w.FUNCTION)if("from"===n.name){var B=tr.parse(n.values[0]);t.push({stop:zt,color:B})}else if("to"===n.name){B=tr.parse(n.values[0]);t.push({stop:Jt,color:B})}else if("color-stop"===n.name){var s=n.values.filter(Mt);if(2===s.length){B=tr.parse(s[1]);var o=s[0];Ot(o)&&t.push({stop:{type:w.PERCENTAGE_TOKEN,number:100*o.number,flags:o.flags},color:B})}}})),r===Ur.LINEAR_GRADIENT?{angle:(e+er(180))%er(360),stops:t,type:r}:{size:B,shape:n,stops:t,position:s,type:r}},kr="closest-side",Wr="farthest-side",Yr="closest-corner",qr="farthest-corner",Zr="circle",jr="ellipse",$r="cover",An="contain",en=function(A){var e=rn.CIRCLE,t=nn.FARTHEST_CORNER,r=[],n=[];return yt(A).forEach((function(A,B){var s=!0;if(0===B){var o=!1;s=A.reduce((function(A,r){if(o)if(bt(r))switch(r.value){case"center":return n.push(Xt),A;case"top":case"left":return n.push(zt),A;case"right":case"bottom":return n.push(Jt),A}else(xt(r)||Pt(r))&&n.push(r);else if(bt(r))switch(r.value){case Zr:return e=rn.CIRCLE,!1;case jr:return e=rn.ELLIPSE,!1;case"at":return o=!0,!1;case kr:return t=nn.CLOSEST_SIDE,!1;case $r:case Wr:return t=nn.FARTHEST_SIDE,!1;case An:case Yr:return t=nn.CLOSEST_CORNER,!1;case qr:return t=nn.FARTHEST_CORNER,!1}else if(Pt(r)||xt(r))return Array.isArray(t)||(t=[]),t.push(r),!1;return A}),s)}if(s){var i=gr(A);r.push(i)}})),{size:t,shape:e,stops:r,position:n,type:Ur.RADIAL_GRADIENT}},tn=function(A){var e=rn.CIRCLE,t=nn.FARTHEST_CORNER,r=[],n=[];return yt(A).forEach((function(A,B){var s=!0;if(0===B?s=A.reduce((function(A,e){if(bt(e))switch(e.value){case"center":return n.push(Xt),!1;case"top":case"left":return n.push(zt),!1;case"right":case"bottom":return n.push(Jt),!1}else if(xt(e)||Pt(e))return n.push(e),!1;return A}),s):1===B&&(s=A.reduce((function(A,r){if(bt(r))switch(r.value){case Zr:return e=rn.CIRCLE,!1;case jr:return e=rn.ELLIPSE,!1;case An:case kr:return t=nn.CLOSEST_SIDE,!1;case Wr:return t=nn.FARTHEST_SIDE,!1;case Yr:return t=nn.CLOSEST_CORNER,!1;case $r:case qr:return t=nn.FARTHEST_CORNER,!1}else if(Pt(r)||xt(r))return Array.isArray(t)||(t=[]),t.push(r),!1;return A}),s)),s){var o=gr(A);r.push(o)}})),{size:t,shape:e,stops:r,position:n,type:Ur.RADIAL_GRADIENT}};(function(A){A[A["URL"]=0]="URL",A[A["LINEAR_GRADIENT"]=1]="LINEAR_GRADIENT",A[A["RADIAL_GRADIENT"]=2]="RADIAL_GRADIENT"})(Ur||(Ur={}));var rn,nn,Bn=function(A){return A.type===Ur.LINEAR_GRADIENT},sn=function(A){return A.type===Ur.RADIAL_GRADIENT};(function(A){A[A["CIRCLE"]=0]="CIRCLE",A[A["ELLIPSE"]=1]="ELLIPSE"})(rn||(rn={})),function(A){A[A["CLOSEST_SIDE"]=0]="CLOSEST_SIDE",A[A["FARTHEST_SIDE"]=1]="FARTHEST_SIDE",A[A["CLOSEST_CORNER"]=2]="CLOSEST_CORNER",A[A["FARTHEST_CORNER"]=3]="FARTHEST_CORNER"}(nn||(nn={}));var on={name:"image",parse:function(A){if(A.type===w.URL_TOKEN){var e={url:A.value,type:Ur.URL};return Sr.getInstance().addImage(A.value),e}if(A.type===w.FUNCTION){var t=Qn[A.name];if("undefined"===typeof t)throw new Error('Attempting to parse an unsupported image function "'+A.name+'"');return t(A.values)}throw new Error("Unsupported image type")}};function an(A){return A.type!==w.FUNCTION||Qn[A.name]}var cn,Qn={"linear-gradient":pr,"-moz-linear-gradient":Nr,"-ms-linear-gradient":Nr,"-o-linear-gradient":Nr,"-webkit-linear-gradient":Nr,"radial-gradient":en,"-moz-radial-gradient":tn,"-ms-radial-gradient":tn,"-o-radial-gradient":tn,"-webkit-radial-gradient":tn,"-webkit-gradient":Gr},un={name:"background-image",initialValue:"none",type:ar.LIST,prefix:!1,parse:function(A){if(0===A.length)return[];var e=A[0];return e.type===w.IDENT_TOKEN&&"none"===e.value?[]:A.filter((function(A){return Mt(A)&&an(A)})).map(on.parse)}},wn={name:"background-origin",initialValue:"border-box",prefix:!1,type:ar.LIST,parse:function(A){return A.map((function(A){if(bt(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},Un={name:"background-position",initialValue:"0% 0%",type:ar.LIST,prefix:!1,parse:function(A){return yt(A).map((function(A){return A.filter(xt)})).map(Vt)}};(function(A){A[A["REPEAT"]=0]="REPEAT",A[A["NO_REPEAT"]=1]="NO_REPEAT",A[A["REPEAT_X"]=2]="REPEAT_X",A[A["REPEAT_Y"]=3]="REPEAT_Y"})(cn||(cn={}));var ln,Cn={name:"background-repeat",initialValue:"repeat",prefix:!1,type:ar.LIST,parse:function(A){return yt(A).map((function(A){return A.filter(bt).map((function(A){return A.value})).join(" ")})).map(gn)}},gn=function(A){switch(A){case"no-repeat":return cn.NO_REPEAT;case"repeat-x":case"repeat no-repeat":return cn.REPEAT_X;case"repeat-y":case"no-repeat repeat":return cn.REPEAT_Y;case"repeat":default:return cn.REPEAT}};(function(A){A["AUTO"]="auto",A["CONTAIN"]="contain",A["COVER"]="cover"})(ln||(ln={}));var En,Fn={name:"background-size",initialValue:"0",prefix:!1,type:ar.LIST,parse:function(A){return yt(A).map((function(A){return A.filter(hn)}))}},hn=function(A){return bt(A)||xt(A)},Hn=function(A){return{name:"border-"+A+"-color",initialValue:"transparent",prefix:!1,type:ar.TYPE_VALUE,format:"color"}},dn=Hn("top"),fn=Hn("right"),pn=Hn("bottom"),Nn=Hn("left"),Kn=function(A){return{name:"border-radius-"+A,initialValue:"0 0",prefix:!1,type:ar.LIST,parse:function(A){return Vt(A.filter(xt))}}},In=Kn("top-left"),Tn=Kn("top-right"),mn=Kn("bottom-right"),Rn=Kn("bottom-left");(function(A){A[A["NONE"]=0]="NONE",A[A["SOLID"]=1]="SOLID"})(En||(En={}));var Ln,On=function(A){return{name:"border-"+A+"-style",initialValue:"solid",prefix:!1,type:ar.IDENT_VALUE,parse:function(A){switch(A){case"none":return En.NONE}return En.SOLID}}},bn=On("top"),Dn=On("right"),vn=On("bottom"),Sn=On("left"),Mn=function(A){return{name:"border-"+A+"-width",initialValue:"0",type:ar.VALUE,prefix:!1,parse:function(A){return Lt(A)?A.number:0}}},yn=Mn("top"),_n=Mn("right"),Pn=Mn("bottom"),xn=Mn("left"),Vn={name:"color",initialValue:"transparent",prefix:!1,type:ar.TYPE_VALUE,format:"color"},zn={name:"display",initialValue:"inline-block",prefix:!1,type:ar.LIST,parse:function(A){return A.filter(bt).reduce((function(A,e){return A|Xn(e.value)}),0)}},Xn=function(A){switch(A){case"block":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0};(function(A){A[A["NONE"]=0]="NONE",A[A["LEFT"]=1]="LEFT",A[A["RIGHT"]=2]="RIGHT",A[A["INLINE_START"]=3]="INLINE_START",A[A["INLINE_END"]=4]="INLINE_END"})(Ln||(Ln={}));var Jn,Gn={name:"float",initialValue:"none",prefix:!1,type:ar.IDENT_VALUE,parse:function(A){switch(A){case"left":return Ln.LEFT;case"right":return Ln.RIGHT;case"inline-start":return Ln.INLINE_START;case"inline-end":return Ln.INLINE_END}return Ln.NONE}},kn={name:"letter-spacing",initialValue:"0",prefix:!1,type:ar.VALUE,parse:function(A){return A.type===w.IDENT_TOKEN&&"normal"===A.value?0:A.type===w.NUMBER_TOKEN||A.type===w.DIMENSION_TOKEN?A.number:0}};(function(A){A["NORMAL"]="normal",A["STRICT"]="strict"})(Jn||(Jn={}));var Wn,Yn={name:"line-break",initialValue:"normal",prefix:!1,type:ar.IDENT_VALUE,parse:function(A){switch(A){case"strict":return Jn.STRICT;case"normal":default:return Jn.NORMAL}}},qn={name:"line-height",initialValue:"normal",prefix:!1,type:ar.TOKEN_VALUE},Zn=function(A,e){return bt(A)&&"normal"===A.value?1.2*e:A.type===w.NUMBER_TOKEN?e*A.number:xt(A)?kt(A,e):e},jn={name:"list-style-image",initialValue:"none",type:ar.VALUE,prefix:!1,parse:function(A){return A.type===w.IDENT_TOKEN&&"none"===A.value?null:on.parse(A)}};(function(A){A[A["INSIDE"]=0]="INSIDE",A[A["OUTSIDE"]=1]="OUTSIDE"})(Wn||(Wn={}));var $n,AB={name:"list-style-position",initialValue:"outside",prefix:!1,type:ar.IDENT_VALUE,parse:function(A){switch(A){case"inside":return Wn.INSIDE;case"outside":default:return Wn.OUTSIDE}}};(function(A){A[A["NONE"]=-1]="NONE",A[A["DISC"]=0]="DISC",A[A["CIRCLE"]=1]="CIRCLE",A[A["SQUARE"]=2]="SQUARE",A[A["DECIMAL"]=3]="DECIMAL",A[A["CJK_DECIMAL"]=4]="CJK_DECIMAL",A[A["DECIMAL_LEADING_ZERO"]=5]="DECIMAL_LEADING_ZERO",A[A["LOWER_ROMAN"]=6]="LOWER_ROMAN",A[A["UPPER_ROMAN"]=7]="UPPER_ROMAN",A[A["LOWER_GREEK"]=8]="LOWER_GREEK",A[A["LOWER_ALPHA"]=9]="LOWER_ALPHA",A[A["UPPER_ALPHA"]=10]="UPPER_ALPHA",A[A["ARABIC_INDIC"]=11]="ARABIC_INDIC",A[A["ARMENIAN"]=12]="ARMENIAN",A[A["BENGALI"]=13]="BENGALI",A[A["CAMBODIAN"]=14]="CAMBODIAN",A[A["CJK_EARTHLY_BRANCH"]=15]="CJK_EARTHLY_BRANCH",A[A["CJK_HEAVENLY_STEM"]=16]="CJK_HEAVENLY_STEM",A[A["CJK_IDEOGRAPHIC"]=17]="CJK_IDEOGRAPHIC",A[A["DEVANAGARI"]=18]="DEVANAGARI",A[A["ETHIOPIC_NUMERIC"]=19]="ETHIOPIC_NUMERIC",A[A["GEORGIAN"]=20]="GEORGIAN",A[A["GUJARATI"]=21]="GUJARATI",A[A["GURMUKHI"]=22]="GURMUKHI",A[A["HEBREW"]=22]="HEBREW",A[A["HIRAGANA"]=23]="HIRAGANA",A[A["HIRAGANA_IROHA"]=24]="HIRAGANA_IROHA",A[A["JAPANESE_FORMAL"]=25]="JAPANESE_FORMAL",A[A["JAPANESE_INFORMAL"]=26]="JAPANESE_INFORMAL",A[A["KANNADA"]=27]="KANNADA",A[A["KATAKANA"]=28]="KATAKANA",A[A["KATAKANA_IROHA"]=29]="KATAKANA_IROHA",A[A["KHMER"]=30]="KHMER",A[A["KOREAN_HANGUL_FORMAL"]=31]="KOREAN_HANGUL_FORMAL",A[A["KOREAN_HANJA_FORMAL"]=32]="KOREAN_HANJA_FORMAL",A[A["KOREAN_HANJA_INFORMAL"]=33]="KOREAN_HANJA_INFORMAL",A[A["LAO"]=34]="LAO",A[A["LOWER_ARMENIAN"]=35]="LOWER_ARMENIAN",A[A["MALAYALAM"]=36]="MALAYALAM",A[A["MONGOLIAN"]=37]="MONGOLIAN",A[A["MYANMAR"]=38]="MYANMAR",A[A["ORIYA"]=39]="ORIYA",A[A["PERSIAN"]=40]="PERSIAN",A[A["SIMP_CHINESE_FORMAL"]=41]="SIMP_CHINESE_FORMAL",A[A["SIMP_CHINESE_INFORMAL"]=42]="SIMP_CHINESE_INFORMAL",A[A["TAMIL"]=43]="TAMIL",A[A["TELUGU"]=44]="TELUGU",A[A["THAI"]=45]="THAI",A[A["TIBETAN"]=46]="TIBETAN",A[A["TRAD_CHINESE_FORMAL"]=47]="TRAD_CHINESE_FORMAL",A[A["TRAD_CHINESE_INFORMAL"]=48]="TRAD_CHINESE_INFORMAL",A[A["UPPER_ARMENIAN"]=49]="UPPER_ARMENIAN",A[A["DISCLOSURE_OPEN"]=50]="DISCLOSURE_OPEN",A[A["DISCLOSURE_CLOSED"]=51]="DISCLOSURE_CLOSED"})($n||($n={}));var eB,tB={name:"list-style-type",initialValue:"none",prefix:!1,type:ar.IDENT_VALUE,parse:function(A){switch(A){case"disc":return $n.DISC;case"circle":return $n.CIRCLE;case"square":return $n.SQUARE;case"decimal":return $n.DECIMAL;case"cjk-decimal":return $n.CJK_DECIMAL;case"decimal-leading-zero":return $n.DECIMAL_LEADING_ZERO;case"lower-roman":return $n.LOWER_ROMAN;case"upper-roman":return $n.UPPER_ROMAN;case"lower-greek":return $n.LOWER_GREEK;case"lower-alpha":return $n.LOWER_ALPHA;case"upper-alpha":return $n.UPPER_ALPHA;case"arabic-indic":return $n.ARABIC_INDIC;case"armenian":return $n.ARMENIAN;case"bengali":return $n.BENGALI;case"cambodian":return $n.CAMBODIAN;case"cjk-earthly-branch":return $n.CJK_EARTHLY_BRANCH;case"cjk-heavenly-stem":return $n.CJK_HEAVENLY_STEM;case"cjk-ideographic":return $n.CJK_IDEOGRAPHIC;case"devanagari":return $n.DEVANAGARI;case"ethiopic-numeric":return $n.ETHIOPIC_NUMERIC;case"georgian":return $n.GEORGIAN;case"gujarati":return $n.GUJARATI;case"gurmukhi":return $n.GURMUKHI;case"hebrew":return $n.HEBREW;case"hiragana":return $n.HIRAGANA;case"hiragana-iroha":return $n.HIRAGANA_IROHA;case"japanese-formal":return $n.JAPANESE_FORMAL;case"japanese-informal":return $n.JAPANESE_INFORMAL;case"kannada":return $n.KANNADA;case"katakana":return $n.KATAKANA;case"katakana-iroha":return $n.KATAKANA_IROHA;case"khmer":return $n.KHMER;case"korean-hangul-formal":return $n.KOREAN_HANGUL_FORMAL;case"korean-hanja-formal":return $n.KOREAN_HANJA_FORMAL;case"korean-hanja-informal":return $n.KOREAN_HANJA_INFORMAL;case"lao":return $n.LAO;case"lower-armenian":return $n.LOWER_ARMENIAN;case"malayalam":return $n.MALAYALAM;case"mongolian":return $n.MONGOLIAN;case"myanmar":return $n.MYANMAR;case"oriya":return $n.ORIYA;case"persian":return $n.PERSIAN;case"simp-chinese-formal":return $n.SIMP_CHINESE_FORMAL;case"simp-chinese-informal":return $n.SIMP_CHINESE_INFORMAL;case"tamil":return $n.TAMIL;case"telugu":return $n.TELUGU;case"thai":return $n.THAI;case"tibetan":return $n.TIBETAN;case"trad-chinese-formal":return $n.TRAD_CHINESE_FORMAL;case"trad-chinese-informal":return $n.TRAD_CHINESE_INFORMAL;case"upper-armenian":return $n.UPPER_ARMENIAN;case"disclosure-open":return $n.DISCLOSURE_OPEN;case"disclosure-closed":return $n.DISCLOSURE_CLOSED;case"none":default:return $n.NONE}}},rB=function(A){return{name:"margin-"+A,initialValue:"0",prefix:!1,type:ar.TOKEN_VALUE}},nB=rB("top"),BB=rB("right"),sB=rB("bottom"),oB=rB("left");(function(A){A[A["VISIBLE"]=0]="VISIBLE",A[A["HIDDEN"]=1]="HIDDEN",A[A["SCROLL"]=2]="SCROLL",A[A["AUTO"]=3]="AUTO"})(eB||(eB={}));var iB,aB={name:"overflow",initialValue:"visible",prefix:!1,type:ar.LIST,parse:function(A){return A.filter(bt).map((function(A){switch(A.value){case"hidden":return eB.HIDDEN;case"scroll":return eB.SCROLL;case"auto":return eB.AUTO;case"visible":default:return eB.VISIBLE}}))}};(function(A){A["NORMAL"]="normal",A["BREAK_WORD"]="break-word"})(iB||(iB={}));var cB,QB={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:ar.IDENT_VALUE,parse:function(A){switch(A){case"break-word":return iB.BREAK_WORD;case"normal":default:return iB.NORMAL}}},uB=function(A){return{name:"padding-"+A,initialValue:"0",prefix:!1,type:ar.TYPE_VALUE,format:"length-percentage"}},wB=uB("top"),UB=uB("right"),lB=uB("bottom"),CB=uB("left");(function(A){A[A["LEFT"]=0]="LEFT",A[A["CENTER"]=1]="CENTER",A[A["RIGHT"]=2]="RIGHT"})(cB||(cB={}));var gB,EB={name:"text-align",initialValue:"left",prefix:!1,type:ar.IDENT_VALUE,parse:function(A){switch(A){case"right":return cB.RIGHT;case"center":case"justify":return cB.CENTER;case"left":default:return cB.LEFT}}};(function(A){A[A["STATIC"]=0]="STATIC",A[A["RELATIVE"]=1]="RELATIVE",A[A["ABSOLUTE"]=2]="ABSOLUTE",A[A["FIXED"]=3]="FIXED",A[A["STICKY"]=4]="STICKY"})(gB||(gB={}));var FB,hB={name:"position",initialValue:"static",prefix:!1,type:ar.IDENT_VALUE,parse:function(A){switch(A){case"relative":return gB.RELATIVE;case"absolute":return gB.ABSOLUTE;case"fixed":return gB.FIXED;case"sticky":return gB.STICKY}return gB.STATIC}},HB={name:"text-shadow",initialValue:"none",type:ar.LIST,prefix:!1,parse:function(A){return 1===A.length&&vt(A[0],"none")?[]:yt(A).map((function(A){for(var e={color:wr.TRANSPARENT,offsetX:zt,offsetY:zt,blur:zt},t=0,r=0;r<A.length;r++){var n=A[r];Pt(n)?(0===t?e.offsetX=n:1===t?e.offsetY=n:e.blur=n,t++):e.color=tr.parse(n)}return e}))}};(function(A){A[A["NONE"]=0]="NONE",A[A["LOWERCASE"]=1]="LOWERCASE",A[A["UPPERCASE"]=2]="UPPERCASE",A[A["CAPITALIZE"]=3]="CAPITALIZE"})(FB||(FB={}));var dB,fB={name:"text-transform",initialValue:"none",prefix:!1,type:ar.IDENT_VALUE,parse:function(A){switch(A){case"uppercase":return FB.UPPERCASE;case"lowercase":return FB.LOWERCASE;case"capitalize":return FB.CAPITALIZE}return FB.NONE}},pB={name:"transform",initialValue:"none",prefix:!0,type:ar.VALUE,parse:function(A){if(A.type===w.IDENT_TOKEN&&"none"===A.value)return null;if(A.type===w.FUNCTION){var e=IB[A.name];if("undefined"===typeof e)throw new Error('Attempting to parse an unsupported transform function "'+A.name+'"');return e(A.values)}return null}},NB=function(A){var e=A.filter((function(A){return A.type===w.NUMBER_TOKEN})).map((function(A){return A.number}));return 6===e.length?e:null},KB=function(A){var e=A.filter((function(A){return A.type===w.NUMBER_TOKEN})).map((function(A){return A.number})),t=e[0],r=e[1],n=(e[2],e[3],e[4]),B=e[5],s=(e[6],e[7],e[8],e[9],e[10],e[11],e[12]),o=e[13];e[14],e[15];return 16===e.length?[t,r,n,B,s,o]:null},IB={matrix:NB,matrix3d:KB},TB={type:w.PERCENTAGE_TOKEN,number:50,flags:GA},mB=[TB,TB],RB={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:ar.LIST,parse:function(A){var e=A.filter(xt);return 2!==e.length?mB:[e[0],e[1]]}};(function(A){A[A["VISIBLE"]=0]="VISIBLE",A[A["HIDDEN"]=1]="HIDDEN",A[A["COLLAPSE"]=2]="COLLAPSE"})(dB||(dB={}));var LB,OB={name:"visible",initialValue:"none",prefix:!1,type:ar.IDENT_VALUE,parse:function(A){switch(A){case"hidden":return dB.HIDDEN;case"collapse":return dB.COLLAPSE;case"visible":default:return dB.VISIBLE}}};(function(A){A["NORMAL"]="normal",A["BREAK_ALL"]="break-all",A["KEEP_ALL"]="keep-all"})(LB||(LB={}));var bB,DB={name:"word-break",initialValue:"normal",prefix:!1,type:ar.IDENT_VALUE,parse:function(A){switch(A){case"break-all":return LB.BREAK_ALL;case"keep-all":return LB.KEEP_ALL;case"normal":default:return LB.NORMAL}}},vB={name:"z-index",initialValue:"auto",prefix:!1,type:ar.VALUE,parse:function(A){if(A.type===w.IDENT_TOKEN)return{auto:!0,order:0};if(Ot(A))return{auto:!1,order:A.number};throw new Error("Invalid z-index number parsed")}},SB={name:"opacity",initialValue:"1",type:ar.VALUE,prefix:!1,parse:function(A){return Ot(A)?A.number:1}},MB={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:ar.TYPE_VALUE,format:"color"},yB={name:"text-decoration-line",initialValue:"none",prefix:!1,type:ar.LIST,parse:function(A){return A.filter(bt).map((function(A){switch(A.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0})).filter((function(A){return 0!==A}))}},_B={name:"font-family",initialValue:"",prefix:!1,type:ar.LIST,parse:function(A){return A.filter(PB).map((function(A){return A.value}))}},PB=function(A){return A.type===w.STRING_TOKEN||A.type===w.IDENT_TOKEN},xB={name:"font-size",initialValue:"0",prefix:!1,type:ar.TYPE_VALUE,format:"length"},VB={name:"font-weight",initialValue:"normal",type:ar.VALUE,prefix:!1,parse:function(A){if(Ot(A))return A.number;if(bt(A))switch(A.value){case"bold":return 700;case"normal":default:return 400}return 400}},zB={name:"font-variant",initialValue:"none",type:ar.LIST,prefix:!1,parse:function(A){return A.filter(bt).map((function(A){return A.value}))}};(function(A){A["NORMAL"]="normal",A["ITALIC"]="italic",A["OBLIQUE"]="oblique"})(bB||(bB={}));var XB,JB={name:"font-style",initialValue:"normal",prefix:!1,type:ar.IDENT_VALUE,parse:function(A){switch(A){case"oblique":return bB.OBLIQUE;case"italic":return bB.ITALIC;case"normal":default:return bB.NORMAL}}},GB=function(A,e){return 0!==(A&e)},kB={name:"content",initialValue:"none",type:ar.LIST,prefix:!1,parse:function(A){if(0===A.length)return[];var e=A[0];return e.type===w.IDENT_TOKEN&&"none"===e.value?[]:A}},WB={name:"counter-increment",initialValue:"none",prefix:!0,type:ar.LIST,parse:function(A){if(0===A.length)return null;var e=A[0];if(e.type===w.IDENT_TOKEN&&"none"===e.value)return null;for(var t=[],r=A.filter(St),n=0;n<r.length;n++){var B=r[n],s=r[n+1];if(B.type===w.IDENT_TOKEN){var o=s&&Ot(s)?s.number:1;t.push({counter:B.value,increment:o})}}return t}},YB={name:"counter-reset",initialValue:"none",prefix:!0,type:ar.LIST,parse:function(A){if(0===A.length)return[];for(var e=[],t=A.filter(St),r=0;r<t.length;r++){var n=t[r],B=t[r+1];if(bt(n)&&"none"!==n.value){var s=B&&Ot(B)?B.number:0;e.push({counter:n.value,reset:s})}}return e}},qB={name:"quotes",initialValue:"none",prefix:!0,type:ar.LIST,parse:function(A){if(0===A.length)return null;var e=A[0];if(e.type===w.IDENT_TOKEN&&"none"===e.value)return null;var t=[],r=A.filter(Dt);if(r.length%2!==0)return null;for(var n=0;n<r.length;n+=2){var B=r[n].value,s=r[n+1].value;t.push({open:B,close:s})}return t}},ZB=function(A,e,t){if(!A)return"";var r=A[Math.min(e,A.length-1)];return r?t?r.open:r.close:""},jB={name:"box-shadow",initialValue:"none",type:ar.LIST,prefix:!1,parse:function(A){return 1===A.length&&vt(A[0],"none")?[]:yt(A).map((function(A){for(var e={color:255,offsetX:zt,offsetY:zt,blur:zt,spread:zt,inset:!1},t=0,r=0;r<A.length;r++){var n=A[r];vt(n,"inset")?e.inset=!0:Pt(n)?(0===t?e.offsetX=n:1===t?e.offsetY=n:2===t?e.blur=n:e.spread=n,t++):e.color=tr.parse(n)}return e}))}},$B=function(){function A(A){this.backgroundClip=ts(lr,A.backgroundClip),this.backgroundColor=ts(Cr,A.backgroundColor),this.backgroundImage=ts(un,A.backgroundImage),this.backgroundOrigin=ts(wn,A.backgroundOrigin),this.backgroundPosition=ts(Un,A.backgroundPosition),this.backgroundRepeat=ts(Cn,A.backgroundRepeat),this.backgroundSize=ts(Fn,A.backgroundSize),this.borderTopColor=ts(dn,A.borderTopColor),this.borderRightColor=ts(fn,A.borderRightColor),this.borderBottomColor=ts(pn,A.borderBottomColor),this.borderLeftColor=ts(Nn,A.borderLeftColor),this.borderTopLeftRadius=ts(In,A.borderTopLeftRadius),this.borderTopRightRadius=ts(Tn,A.borderTopRightRadius),this.borderBottomRightRadius=ts(mn,A.borderBottomRightRadius),this.borderBottomLeftRadius=ts(Rn,A.borderBottomLeftRadius),this.borderTopStyle=ts(bn,A.borderTopStyle),this.borderRightStyle=ts(Dn,A.borderRightStyle),this.borderBottomStyle=ts(vn,A.borderBottomStyle),this.borderLeftStyle=ts(Sn,A.borderLeftStyle),this.borderTopWidth=ts(yn,A.borderTopWidth),this.borderRightWidth=ts(_n,A.borderRightWidth),this.borderBottomWidth=ts(Pn,A.borderBottomWidth),this.borderLeftWidth=ts(xn,A.borderLeftWidth),this.boxShadow=ts(jB,A.boxShadow),this.color=ts(Vn,A.color),this.display=ts(zn,A.display),this.float=ts(Gn,A.cssFloat),this.fontFamily=ts(_B,A.fontFamily),this.fontSize=ts(xB,A.fontSize),this.fontStyle=ts(JB,A.fontStyle),this.fontVariant=ts(zB,A.fontVariant),this.fontWeight=ts(VB,A.fontWeight),this.letterSpacing=ts(kn,A.letterSpacing),this.lineBreak=ts(Yn,A.lineBreak),this.lineHeight=ts(qn,A.lineHeight),this.listStyleImage=ts(jn,A.listStyleImage),this.listStylePosition=ts(AB,A.listStylePosition),this.listStyleType=ts(tB,A.listStyleType),this.marginTop=ts(nB,A.marginTop),this.marginRight=ts(BB,A.marginRight),this.marginBottom=ts(sB,A.marginBottom),this.marginLeft=ts(oB,A.marginLeft),this.opacity=ts(SB,A.opacity);var e=ts(aB,A.overflow);this.overflowX=e[0],this.overflowY=e[e.length>1?1:0],this.overflowWrap=ts(QB,A.overflowWrap),this.paddingTop=ts(wB,A.paddingTop),this.paddingRight=ts(UB,A.paddingRight),this.paddingBottom=ts(lB,A.paddingBottom),this.paddingLeft=ts(CB,A.paddingLeft),this.position=ts(hB,A.position),this.textAlign=ts(EB,A.textAlign),this.textDecorationColor=ts(MB,A.textDecorationColor||A.color),this.textDecorationLine=ts(yB,A.textDecorationLine),this.textShadow=ts(HB,A.textShadow),this.textTransform=ts(fB,A.textTransform),this.transform=ts(pB,A.transform),this.transformOrigin=ts(RB,A.transformOrigin),this.visibility=ts(OB,A.visibility),this.wordBreak=ts(DB,A.wordBreak),this.zIndex=ts(vB,A.zIndex)}return A.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===dB.VISIBLE},A.prototype.isTransparent=function(){return rr(this.backgroundColor)},A.prototype.isTransformed=function(){return null!==this.transform},A.prototype.isPositioned=function(){return this.position!==gB.STATIC},A.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},A.prototype.isFloating=function(){return this.float!==Ln.NONE},A.prototype.isInlineLevel=function(){return GB(this.display,4)||GB(this.display,33554432)||GB(this.display,268435456)||GB(this.display,536870912)||GB(this.display,67108864)||GB(this.display,134217728)},A}(),As=function(){function A(A){this.content=ts(kB,A.content),this.quotes=ts(qB,A.quotes)}return A}(),es=function(){function A(A){this.counterIncrement=ts(WB,A.counterIncrement),this.counterReset=ts(YB,A.counterReset)}return A}(),ts=function(A,e){var t=new mt,r=null!==e&&"undefined"!==typeof e?e.toString():A.initialValue;t.write(r);var n=new Rt(t.read());switch(A.type){case ar.IDENT_VALUE:var B=n.parseComponentValue();return A.parse(bt(B)?B.value:A.initialValue);case ar.VALUE:return A.parse(n.parseComponentValue());case ar.LIST:return A.parse(n.parseComponentValues());case ar.TOKEN_VALUE:return n.parseComponentValue();case ar.TYPE_VALUE:switch(A.format){case"angle":return jt.parse(n.parseComponentValue());case"color":return tr.parse(n.parseComponentValue());case"image":return on.parse(n.parseComponentValue());case"length":var s=n.parseComponentValue();return Pt(s)?s:zt;case"length-percentage":var o=n.parseComponentValue();return xt(o)?o:zt}}throw new Error("Attempting to parse unsupported css format type "+A.format)},rs=function(){function A(A){this.styles=new $B(window.getComputedStyle(A,null)),this.textNodes=[],this.elements=[],null!==this.styles.transform&&Ps(A)&&(A.style.transform="none"),this.bounds=s(A),this.flags=0}return A}(),ns=function(){function A(A,e){this.text=A,this.bounds=e}return A}(),Bs=function(A,e,t){var r=is(A,e),n=[],B=0;return r.forEach((function(A){if(e.textDecorationLine.length||A.trim().length>0)if(Dr.SUPPORT_RANGE_BOUNDS)n.push(new ns(A,os(t,B,A.length)));else{var r=t.splitText(A.length);n.push(new ns(A,ss(t))),t=r}else Dr.SUPPORT_RANGE_BOUNDS||(t=t.splitText(A.length));B+=A.length})),n},ss=function(A){var e=A.ownerDocument;if(e){var t=e.createElement("html2canvaswrapper");t.appendChild(A.cloneNode(!0));var r=A.parentNode;if(r){r.replaceChild(t,A);var n=s(t);return t.firstChild&&r.replaceChild(t.firstChild,t),n}}return new B(0,0,0,0)},os=function(A,e,t){var r=A.ownerDocument;if(!r)throw new Error("Node has no owner document");var n=r.createRange();return n.setStart(A,e),n.setEnd(A,e+t),B.fromClientRect(n.getBoundingClientRect())},is=function(A,e){return 0!==e.letterSpacing?i(A).map((function(A){return a(A)})):as(A,e)},as=function(A,e){var t,r=zA(A,{lineBreak:e.lineBreak,wordBreak:e.overflowWrap===iB.BREAK_WORD?"break-word":e.wordBreak}),n=[];while(!(t=r.next()).done)t.value&&n.push(t.value.slice());return n},cs=function(){function A(A,e){this.text=Qs(A.data,e.textTransform),this.textBounds=Bs(this.text,e,A)}return A}(),Qs=function(A,e){switch(e){case FB.LOWERCASE:return A.toLowerCase();case FB.CAPITALIZE:return A.replace(us,ws);case FB.UPPERCASE:return A.toUpperCase();default:return A}},us=/(^|\s|:|-|\(|\))([a-z])/g,ws=function(A,e,t){return A.length>0?e+t.toUpperCase():A},Us=function(A){function t(e){var t=A.call(this,e)||this;return t.src=e.currentSrc||e.src,t.intrinsicWidth=e.naturalWidth,t.intrinsicHeight=e.naturalHeight,Sr.getInstance().addImage(t.src),t}return e(t,A),t}(rs),ls=function(A){function t(e){var t=A.call(this,e)||this;return t.canvas=e,t.intrinsicWidth=e.width,t.intrinsicHeight=e.height,t}return e(t,A),t}(rs),Cs=function(A){function t(e){var t=A.call(this,e)||this,r=new XMLSerializer;return t.svg="data:image/svg+xml,"+encodeURIComponent(r.serializeToString(e)),t.intrinsicWidth=e.width.baseVal.value,t.intrinsicHeight=e.height.baseVal.value,Sr.getInstance().addImage(t.svg),t}return e(t,A),t}(rs),gs=function(A){function t(e){var t=A.call(this,e)||this;return t.value=e.value,t}return e(t,A),t}(rs),Es=function(A){function t(e){var t=A.call(this,e)||this;return t.start=e.start,t.reversed="boolean"===typeof e.reversed&&!0===e.reversed,t}return e(t,A),t}(rs),Fs=[{type:w.DIMENSION_TOKEN,flags:0,unit:"px",number:3}],hs=[{type:w.PERCENTAGE_TOKEN,flags:0,number:50}],Hs=function(A){return A.width>A.height?new B(A.left+(A.width-A.height)/2,A.top,A.height,A.height):A.width<A.height?new B(A.left,A.top+(A.height-A.width)/2,A.width,A.width):A},ds=function(A){var e=A.type===Ns?new Array(A.value.length+1).join("•"):A.value;return 0===e.length?A.placeholder||"":e},fs="checkbox",ps="radio",Ns="password",Ks=707406591,Is=function(A){function t(e){var t=A.call(this,e)||this;switch(t.type=e.type.toLowerCase(),t.checked=e.checked,t.value=ds(e),t.type!==fs&&t.type!==ps||(t.styles.backgroundColor=3739148031,t.styles.borderTopColor=t.styles.borderRightColor=t.styles.borderBottomColor=t.styles.borderLeftColor=2779096575,t.styles.borderTopWidth=t.styles.borderRightWidth=t.styles.borderBottomWidth=t.styles.borderLeftWidth=1,t.styles.borderTopStyle=t.styles.borderRightStyle=t.styles.borderBottomStyle=t.styles.borderLeftStyle=En.SOLID,t.styles.backgroundClip=[cr.BORDER_BOX],t.styles.backgroundOrigin=[0],t.bounds=Hs(t.bounds)),t.type){case fs:t.styles.borderTopRightRadius=t.styles.borderTopLeftRadius=t.styles.borderBottomRightRadius=t.styles.borderBottomLeftRadius=Fs;break;case ps:t.styles.borderTopRightRadius=t.styles.borderTopLeftRadius=t.styles.borderBottomRightRadius=t.styles.borderBottomLeftRadius=hs;break}return t}return e(t,A),t}(rs),Ts=function(A){function t(e){var t=A.call(this,e)||this,r=e.options[e.selectedIndex||0];return t.value=r&&r.text||"",t}return e(t,A),t}(rs),ms=function(A){function t(e){var t=A.call(this,e)||this;return t.value=e.value,t}return e(t,A),t}(rs),Rs=function(A){return tr.parse(Rt.create(A).parseComponentValue())},Ls=function(A){function t(e){var t=A.call(this,e)||this;t.src=e.src,t.width=parseInt(e.width,10)||0,t.height=parseInt(e.height,10)||0,t.backgroundColor=t.styles.backgroundColor;try{if(e.contentWindow&&e.contentWindow.document&&e.contentWindow.document.documentElement){t.tree=vs(e.contentWindow.document.documentElement);var r=e.contentWindow.document.documentElement?Rs(getComputedStyle(e.contentWindow.document.documentElement).backgroundColor):wr.TRANSPARENT,n=e.contentWindow.document.body?Rs(getComputedStyle(e.contentWindow.document.body).backgroundColor):wr.TRANSPARENT;t.backgroundColor=rr(r)?rr(n)?t.styles.backgroundColor:n:r}}catch(ye){}return t}return e(t,A),t}(rs),Os=["OL","UL","MENU"],bs=function A(e,t,r){for(var n=e.firstChild,B=void 0;n;n=B)if(B=n.nextSibling,ys(n)&&n.data.trim().length>0)t.textNodes.push(new cs(n,t.styles));else if(_s(n)){var s=Ds(n);s.styles.isVisible()&&(Ss(n,s,r)?s.flags|=4:Ms(s.styles)&&(s.flags|=2),-1!==Os.indexOf(n.tagName)&&(s.flags|=8),t.elements.push(s),js(n)||Js(n)||$s(n)||A(n,s,r))}},Ds=function(A){return Ws(A)?new Us(A):ks(A)?new ls(A):Js(A)?new Cs(A):xs(A)?new gs(A):Vs(A)?new Es(A):zs(A)?new Is(A):$s(A)?new Ts(A):js(A)?new ms(A):Ys(A)?new Ls(A):new rs(A)},vs=function(A){var e=Ds(A);return e.flags|=4,bs(A,e,e),e},Ss=function(A,e,t){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||Gs(A)&&t.styles.isTransparent()},Ms=function(A){return A.isPositioned()||A.isFloating()},ys=function(A){return A.nodeType===Node.TEXT_NODE},_s=function(A){return A.nodeType===Node.ELEMENT_NODE},Ps=function(A){return"undefined"!==typeof A.style},xs=function(A){return"LI"===A.tagName},Vs=function(A){return"OL"===A.tagName},zs=function(A){return"INPUT"===A.tagName},Xs=function(A){return"HTML"===A.tagName},Js=function(A){return"svg"===A.tagName},Gs=function(A){return"BODY"===A.tagName},ks=function(A){return"CANVAS"===A.tagName},Ws=function(A){return"IMG"===A.tagName},Ys=function(A){return"IFRAME"===A.tagName},qs=function(A){return"STYLE"===A.tagName},Zs=function(A){return"SCRIPT"===A.tagName},js=function(A){return"TEXTAREA"===A.tagName},$s=function(A){return"SELECT"===A.tagName},Ao=function(){function A(){this.counters={}}return A.prototype.getCounterValue=function(A){var e=this.counters[A];return e&&e.length?e[e.length-1]:1},A.prototype.getCounterValues=function(A){var e=this.counters[A];return e||[]},A.prototype.pop=function(A){var e=this;A.forEach((function(A){return e.counters[A].pop()}))},A.prototype.parse=function(A){var e=this,t=A.counterIncrement,r=A.counterReset;null!==t&&t.forEach((function(A){var t=e.counters[A.counter];t&&(t[Math.max(0,t.length-1)]+=A.increment)}));var n=[];return r.forEach((function(A){var t=e.counters[A.counter];n.push(A.counter),t||(t=e.counters[A.counter]=[]),t.push(A.reset)})),n},A}(),eo={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},to={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},ro={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},no={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},Bo=function(A,e,t,r,n,B){return A<e||A>t?Eo(A,n,B.length>0):r.integers.reduce((function(e,t,n){while(A>=t)A-=t,e+=r.values[n];return e}),"")+B},so=function(A,e,t,r){var n="";do{t||A--,n=r(A)+n,A/=e}while(A*e>=e);return n},oo=function(A,e,t,r,n){var B=t-e+1;return(A<0?"-":"")+(so(Math.abs(A),B,r,(function(A){return a(Math.floor(A%B)+e)}))+n)},io=function(A,e,t){void 0===t&&(t=". ");var r=e.length;return so(Math.abs(A),r,!1,(function(A){return e[Math.floor(A%r)]}))+t},ao=1,co=2,Qo=4,uo=8,wo=function(A,e,t,r,n,B){if(A<-9999||A>9999)return Eo(A,$n.CJK_DECIMAL,n.length>0);var s=Math.abs(A),o=n;if(0===s)return e[0]+o;for(var i=0;s>0&&i<=4;i++){var a=s%10;0===a&&GB(B,ao)&&""!==o?o=e[a]+o:a>1||1===a&&0===i||1===a&&1===i&&GB(B,co)||1===a&&1===i&&GB(B,Qo)&&A>100||1===a&&i>1&&GB(B,uo)?o=e[a]+(i>0?t[i-1]:"")+o:1===a&&i>0&&(o=t[i-1]+o),s=Math.floor(s/10)}return(A<0?r:"")+o},Uo="十百千萬",lo="拾佰仟萬",Co="マイナス",go="마이너스",Eo=function(A,e,t){var r=t?". ":"",n=t?"、":"",B=t?", ":"",s=t?" ":"";switch(e){case $n.DISC:return"•"+s;case $n.CIRCLE:return"◦"+s;case $n.SQUARE:return"◾"+s;case $n.DECIMAL_LEADING_ZERO:var o=oo(A,48,57,!0,r);return o.length<4?"0"+o:o;case $n.CJK_DECIMAL:return io(A,"〇一二三四五六七八九",n);case $n.LOWER_ROMAN:return Bo(A,1,3999,eo,$n.DECIMAL,r).toLowerCase();case $n.UPPER_ROMAN:return Bo(A,1,3999,eo,$n.DECIMAL,r);case $n.LOWER_GREEK:return oo(A,945,969,!1,r);case $n.LOWER_ALPHA:return oo(A,97,122,!1,r);case $n.UPPER_ALPHA:return oo(A,65,90,!1,r);case $n.ARABIC_INDIC:return oo(A,1632,1641,!0,r);case $n.ARMENIAN:case $n.UPPER_ARMENIAN:return Bo(A,1,9999,to,$n.DECIMAL,r);case $n.LOWER_ARMENIAN:return Bo(A,1,9999,to,$n.DECIMAL,r).toLowerCase();case $n.BENGALI:return oo(A,2534,2543,!0,r);case $n.CAMBODIAN:case $n.KHMER:return oo(A,6112,6121,!0,r);case $n.CJK_EARTHLY_BRANCH:return io(A,"子丑寅卯辰巳午未申酉戌亥",n);case $n.CJK_HEAVENLY_STEM:return io(A,"甲乙丙丁戊己庚辛壬癸",n);case $n.CJK_IDEOGRAPHIC:case $n.TRAD_CHINESE_INFORMAL:return wo(A,"零一二三四五六七八九",Uo,"負",n,co|Qo|uo);case $n.TRAD_CHINESE_FORMAL:return wo(A,"零壹貳參肆伍陸柒捌玖",lo,"負",n,ao|co|Qo|uo);case $n.SIMP_CHINESE_INFORMAL:return wo(A,"零一二三四五六七八九",Uo,"负",n,co|Qo|uo);case $n.SIMP_CHINESE_FORMAL:return wo(A,"零壹贰叁肆伍陆柒捌玖",lo,"负",n,ao|co|Qo|uo);case $n.JAPANESE_INFORMAL:return wo(A,"〇一二三四五六七八九","十百千万",Co,n,0);case $n.JAPANESE_FORMAL:return wo(A,"零壱弐参四伍六七八九","拾百千万",Co,n,ao|co|Qo);case $n.KOREAN_HANGUL_FORMAL:return wo(A,"영일이삼사오육칠팔구","십백천만",go,B,ao|co|Qo);case $n.KOREAN_HANJA_INFORMAL:return wo(A,"零一二三四五六七八九","十百千萬",go,B,0);case $n.KOREAN_HANJA_FORMAL:return wo(A,"零壹貳參四五六七八九","拾百千",go,B,ao|co|Qo);case $n.DEVANAGARI:return oo(A,2406,2415,!0,r);case $n.GEORGIAN:return Bo(A,1,19999,no,$n.DECIMAL,r);case $n.GUJARATI:return oo(A,2790,2799,!0,r);case $n.GURMUKHI:return oo(A,2662,2671,!0,r);case $n.HEBREW:return Bo(A,1,10999,ro,$n.DECIMAL,r);case $n.HIRAGANA:return io(A,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case $n.HIRAGANA_IROHA:return io(A,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case $n.KANNADA:return oo(A,3302,3311,!0,r);case $n.KATAKANA:return io(A,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",n);case $n.KATAKANA_IROHA:return io(A,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",n);case $n.LAO:return oo(A,3792,3801,!0,r);case $n.MONGOLIAN:return oo(A,6160,6169,!0,r);case $n.MYANMAR:return oo(A,4160,4169,!0,r);case $n.ORIYA:return oo(A,2918,2927,!0,r);case $n.PERSIAN:return oo(A,1776,1785,!0,r);case $n.TAMIL:return oo(A,3046,3055,!0,r);case $n.TELUGU:return oo(A,3174,3183,!0,r);case $n.THAI:return oo(A,3664,3673,!0,r);case $n.TIBETAN:return oo(A,3872,3881,!0,r);case $n.DECIMAL:default:return oo(A,48,57,!0,r)}},Fo="data-html2canvas-ignore",ho=function(){function A(A,e){if(this.options=e,this.scrolledElements=[],this.referenceElement=A,this.counters=new Ao,this.quoteDepth=0,!A.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(A.ownerDocument.documentElement)}return A.prototype.toIFrame=function(A,e){var t=this,r=fo(A,e);if(!r.contentWindow)return Promise.reject("Unable to find iframe window");var n=A.defaultView.pageXOffset,B=A.defaultView.pageYOffset,s=r.contentWindow,o=s.document,i=po(r).then((function(){t.scrolledElements.forEach(To),s&&(s.scrollTo(e.left,e.top),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||s.scrollY===e.top&&s.scrollX===e.left||(o.documentElement.style.top=-e.top+"px",o.documentElement.style.left=-e.left+"px",o.documentElement.style.position="absolute"));var A=t.options.onclone;return"undefined"===typeof t.clonedReferenceElement?Promise.reject("Error finding the "+t.referenceElement.nodeName+" in the cloned document"):"function"===typeof A?Promise.resolve().then((function(){return A(o)})).then((function(){return r})):r}));return o.open(),o.write(Ko(document.doctype)+"<html></html>"),Io(this.referenceElement.ownerDocument,n,B),o.replaceChild(o.adoptNode(this.documentElement),o.documentElement),o.close(),i},A.prototype.createElementClone=function(A){return ks(A)?this.createCanvasClone(A):qs(A)?this.createStyleClone(A):A.cloneNode(!1)},A.prototype.createStyleClone=function(A){try{var e=A.sheet;if(e&&e.cssRules){var t=[].slice.call(e.cssRules,0).reduce((function(A,e){return e&&"string"===typeof e.cssText?A+e.cssText:A}),""),r=A.cloneNode(!1);return r.textContent=t,r}}catch(ye){if(vr.getInstance(this.options.id).error("Unable to access cssRules property",ye),"SecurityError"!==ye.name)throw ye}return A.cloneNode(!1)},A.prototype.createCanvasClone=function(A){if(this.options.inlineImages&&A.ownerDocument){var e=A.ownerDocument.createElement("img");try{return e.src=A.toDataURL(),e}catch(ye){vr.getInstance(this.options.id).info("Unable to clone canvas contents, canvas is tainted")}}var t=A.cloneNode(!1);try{t.width=A.width,t.height=A.height;var r=A.getContext("2d"),n=t.getContext("2d");return n&&(r?n.putImageData(r.getImageData(0,0,A.width,A.height),0,0):n.drawImage(A,0,0)),t}catch(ye){}return t},A.prototype.cloneNode=function(A){if(ys(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var e=A.ownerDocument.defaultView;if(Ps(A)&&e){var t=this.createElementClone(A),r=e.getComputedStyle(A),n=e.getComputedStyle(A,":before"),B=e.getComputedStyle(A,":after");this.referenceElement===A&&(this.clonedReferenceElement=t),Gs(t)&&Do(t);for(var s=this.counters.parse(new es(r)),o=this.resolvePseudoContent(A,t,n,XB.BEFORE),i=A.firstChild;i;i=i.nextSibling)_s(i)&&(Zs(i)||i.hasAttribute(Fo)||"function"===typeof this.options.ignoreElements&&this.options.ignoreElements(i))||this.options.copyStyles&&_s(i)&&qs(i)||t.appendChild(this.cloneNode(i));o&&t.insertBefore(o,t.firstChild);var a=this.resolvePseudoContent(A,t,B,XB.AFTER);return a&&t.appendChild(a),this.counters.pop(s),r&&this.options.copyStyles&&!Ys(A)&&No(r,t),0===A.scrollTop&&0===A.scrollLeft||this.scrolledElements.push([t,A.scrollLeft,A.scrollTop]),(js(A)||$s(A))&&(js(t)||$s(t))&&(t.value=A.value),t}return A.cloneNode(!1)},A.prototype.resolvePseudoContent=function(A,e,t,r){var n=this;if(t){var B=t.content,s=e.ownerDocument;if(s&&B&&"none"!==B&&"-moz-alt-content"!==B&&"none"!==t.display){this.counters.parse(new es(t));var o=new As(t),i=s.createElement("html2canvaspseudoelement");return No(t,i),o.content.forEach((function(e){if(e.type===w.STRING_TOKEN)i.appendChild(s.createTextNode(e.value));else if(e.type===w.URL_TOKEN){var t=s.createElement("img");t.src=e.value,t.style.opacity="1",i.appendChild(t)}else if(e.type===w.FUNCTION){if("attr"===e.name){var r=e.values.filter(bt);r.length&&i.appendChild(s.createTextNode(A.getAttribute(r[0].value)||""))}else if("counter"===e.name){var B=e.values.filter(Mt),a=B[0],c=B[1];if(a&&bt(a)){var Q=n.counters.getCounterValue(a.value),u=c&&bt(c)?tB.parse(c.value):$n.DECIMAL;i.appendChild(s.createTextNode(Eo(Q,u,!1)))}}else if("counters"===e.name){var U=e.values.filter(Mt),l=(a=U[0],U[1]);c=U[2];if(a&&bt(a)){var C=n.counters.getCounterValues(a.value),g=c&&bt(c)?tB.parse(c.value):$n.DECIMAL,E=l&&l.type===w.STRING_TOKEN?l.value:"",F=C.map((function(A){return Eo(A,g,!1)})).join(E);i.appendChild(s.createTextNode(F))}}}else if(e.type===w.IDENT_TOKEN)switch(e.value){case"open-quote":i.appendChild(s.createTextNode(ZB(o.quotes,n.quoteDepth++,!0)));break;case"close-quote":i.appendChild(s.createTextNode(ZB(o.quotes,--n.quoteDepth,!1)));break;default:}})),i.className=Lo+" "+Oo,e.className+=r===XB.BEFORE?" "+Lo:" "+Oo,i}}},A}();(function(A){A[A["BEFORE"]=0]="BEFORE",A[A["AFTER"]=1]="AFTER"})(XB||(XB={}));var Ho,fo=function(A,e){var t=A.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=e.width.toString(),t.height=e.height.toString(),t.scrolling="no",t.setAttribute(Fo,"true"),A.body.appendChild(t),t},po=function(A){return new Promise((function(e,t){var r=A.contentWindow;if(!r)return t("No window assigned for iframe");var n=r.document;r.onload=A.onload=n.onreadystatechange=function(){r.onload=A.onload=n.onreadystatechange=null;var t=setInterval((function(){n.body.childNodes.length>0&&"complete"===n.readyState&&(clearInterval(t),e(A))}),50)}}))},No=function(A,e){for(var t=A.length-1;t>=0;t--){var r=A.item(t);"content"!==r&&e.style.setProperty(r,A.getPropertyValue(r))}return e},Ko=function(A){var e="";return A&&(e+="<!DOCTYPE ",A.name&&(e+=A.name),A.internalSubset&&(e+=A.internalSubset),A.publicId&&(e+='"'+A.publicId+'"'),A.systemId&&(e+='"'+A.systemId+'"'),e+=">"),e},Io=function(A,e,t){A&&A.defaultView&&(e!==A.defaultView.pageXOffset||t!==A.defaultView.pageYOffset)&&A.defaultView.scrollTo(e,t)},To=function(A){var e=A[0],t=A[1],r=A[2];e.scrollLeft=t,e.scrollTop=r},mo=":before",Ro=":after",Lo="___html2canvas___pseudoelement_before",Oo="___html2canvas___pseudoelement_after",bo='{\n    content: "" !important;\n    display: none !important;\n}',Do=function(A){vo(A,"."+Lo+mo+bo+"\n         ."+Oo+Ro+bo)},vo=function(A,e){var t=A.ownerDocument;if(t){var r=t.createElement("style");r.textContent=e,A.appendChild(r)}};(function(A){A[A["VECTOR"]=0]="VECTOR",A[A["BEZIER_CURVE"]=1]="BEZIER_CURVE"})(Ho||(Ho={}));var So,Mo=function(A,e){return A.length===e.length&&A.some((function(A,t){return A===e[t]}))},yo=function(A,e,t,r,n){return A.map((function(A,B){switch(B){case 0:return A.add(e,t);case 1:return A.add(e+r,t);case 2:return A.add(e+r,t+n);case 3:return A.add(e,t+n)}return A}))},_o=function(){function A(A,e){this.type=Ho.VECTOR,this.x=A,this.y=e}return A.prototype.add=function(e,t){return new A(this.x+e,this.y+t)},A}(),Po=function(A,e,t){return new _o(A.x+(e.x-A.x)*t,A.y+(e.y-A.y)*t)},xo=function(){function A(A,e,t,r){this.type=Ho.BEZIER_CURVE,this.start=A,this.startControl=e,this.endControl=t,this.end=r}return A.prototype.subdivide=function(e,t){var r=Po(this.start,this.startControl,e),n=Po(this.startControl,this.endControl,e),B=Po(this.endControl,this.end,e),s=Po(r,n,e),o=Po(n,B,e),i=Po(s,o,e);return t?new A(this.start,r,s,i):new A(i,o,B,this.end)},A.prototype.add=function(e,t){return new A(this.start.add(e,t),this.startControl.add(e,t),this.endControl.add(e,t),this.end.add(e,t))},A.prototype.reverse=function(){return new A(this.end,this.endControl,this.startControl,this.start)},A}(),Vo=function(A){return A.type===Ho.BEZIER_CURVE},zo=function(){function A(A){var e=A.styles,t=A.bounds,r=Gt(e.borderTopLeftRadius,t.width,t.height),n=r[0],B=r[1],s=Gt(e.borderTopRightRadius,t.width,t.height),o=s[0],i=s[1],a=Gt(e.borderBottomRightRadius,t.width,t.height),c=a[0],Q=a[1],u=Gt(e.borderBottomLeftRadius,t.width,t.height),w=u[0],U=u[1],l=[];l.push((n+o)/t.width),l.push((w+c)/t.width),l.push((B+U)/t.height),l.push((i+Q)/t.height);var C=Math.max.apply(Math,l);C>1&&(n/=C,B/=C,o/=C,i/=C,c/=C,Q/=C,w/=C,U/=C);var g=t.width-o,E=t.height-Q,F=t.width-c,h=t.height-U,H=e.borderTopWidth,d=e.borderRightWidth,f=e.borderBottomWidth,p=e.borderLeftWidth,N=kt(e.paddingTop,A.bounds.width),K=kt(e.paddingRight,A.bounds.width),I=kt(e.paddingBottom,A.bounds.width),T=kt(e.paddingLeft,A.bounds.width);this.topLeftBorderBox=n>0||B>0?Xo(t.left,t.top,n,B,So.TOP_LEFT):new _o(t.left,t.top),this.topRightBorderBox=o>0||i>0?Xo(t.left+g,t.top,o,i,So.TOP_RIGHT):new _o(t.left+t.width,t.top),this.bottomRightBorderBox=c>0||Q>0?Xo(t.left+F,t.top+E,c,Q,So.BOTTOM_RIGHT):new _o(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=w>0||U>0?Xo(t.left,t.top+h,w,U,So.BOTTOM_LEFT):new _o(t.left,t.top+t.height),this.topLeftPaddingBox=n>0||B>0?Xo(t.left+p,t.top+H,Math.max(0,n-p),Math.max(0,B-H),So.TOP_LEFT):new _o(t.left+p,t.top+H),this.topRightPaddingBox=o>0||i>0?Xo(t.left+Math.min(g,t.width+p),t.top+H,g>t.width+p?0:o-p,i-H,So.TOP_RIGHT):new _o(t.left+t.width-d,t.top+H),this.bottomRightPaddingBox=c>0||Q>0?Xo(t.left+Math.min(F,t.width-p),t.top+Math.min(E,t.height+H),Math.max(0,c-d),Q-f,So.BOTTOM_RIGHT):new _o(t.left+t.width-d,t.top+t.height-f),this.bottomLeftPaddingBox=w>0||U>0?Xo(t.left+p,t.top+h,Math.max(0,w-p),U-f,So.BOTTOM_LEFT):new _o(t.left+p,t.top+t.height-f),this.topLeftContentBox=n>0||B>0?Xo(t.left+p+T,t.top+H+N,Math.max(0,n-(p+T)),Math.max(0,B-(H+N)),So.TOP_LEFT):new _o(t.left+p+T,t.top+H+N),this.topRightContentBox=o>0||i>0?Xo(t.left+Math.min(g,t.width+p+T),t.top+H+N,g>t.width+p+T?0:o-p+T,i-(H+N),So.TOP_RIGHT):new _o(t.left+t.width-(d+K),t.top+H+N),this.bottomRightContentBox=c>0||Q>0?Xo(t.left+Math.min(F,t.width-(p+T)),t.top+Math.min(E,t.height+H+N),Math.max(0,c-(d+K)),Q-(f+I),So.BOTTOM_RIGHT):new _o(t.left+t.width-(d+K),t.top+t.height-(f+I)),this.bottomLeftContentBox=w>0||U>0?Xo(t.left+p+T,t.top+h,Math.max(0,w-(p+T)),U-(f+I),So.BOTTOM_LEFT):new _o(t.left+p+T,t.top+t.height-(f+I))}return A}();(function(A){A[A["TOP_LEFT"]=0]="TOP_LEFT",A[A["TOP_RIGHT"]=1]="TOP_RIGHT",A[A["BOTTOM_RIGHT"]=2]="BOTTOM_RIGHT",A[A["BOTTOM_LEFT"]=3]="BOTTOM_LEFT"})(So||(So={}));var Xo=function(A,e,t,r,n){var B=(Math.sqrt(2)-1)/3*4,s=t*B,o=r*B,i=A+t,a=e+r;switch(n){case So.TOP_LEFT:return new xo(new _o(A,a),new _o(A,a-o),new _o(i-s,e),new _o(i,e));case So.TOP_RIGHT:return new xo(new _o(A,e),new _o(A+s,e),new _o(i,a-o),new _o(i,a));case So.BOTTOM_RIGHT:return new xo(new _o(i,e),new _o(i,e+o),new _o(A+s,a),new _o(A,a));case So.BOTTOM_LEFT:default:return new xo(new _o(i,a),new _o(i-s,a),new _o(A,e+o),new _o(A,e))}},Jo=function(A){return[A.topLeftBorderBox,A.topRightBorderBox,A.bottomRightBorderBox,A.bottomLeftBorderBox]},Go=function(A){return[A.topLeftContentBox,A.topRightContentBox,A.bottomRightContentBox,A.bottomLeftContentBox]},ko=function(A){return[A.topLeftPaddingBox,A.topRightPaddingBox,A.bottomRightPaddingBox,A.bottomLeftPaddingBox]},Wo=function(){function A(A,e,t){this.type=0,this.offsetX=A,this.offsetY=e,this.matrix=t,this.target=6}return A}(),Yo=function(){function A(A,e){this.type=1,this.target=e,this.path=A}return A}(),qo=function(A){return 0===A.type},Zo=function(A){return 1===A.type},jo=function(){function A(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return A}(),$o=function(){function A(A,e){if(this.container=A,this.effects=e.slice(0),this.curves=new zo(A),null!==A.styles.transform){var t=A.bounds.left+A.styles.transformOrigin[0].number,r=A.bounds.top+A.styles.transformOrigin[1].number,n=A.styles.transform;this.effects.push(new Wo(t,r,n))}if(A.styles.overflowX!==eB.VISIBLE){var B=Jo(this.curves),s=ko(this.curves);Mo(B,s)?this.effects.push(new Yo(B,6)):(this.effects.push(new Yo(B,2)),this.effects.push(new Yo(s,4)))}}return A.prototype.getParentEffects=function(){var A=this.effects.slice(0);if(this.container.styles.overflowX!==eB.VISIBLE){var e=Jo(this.curves),t=ko(this.curves);Mo(e,t)||A.push(new Yo(t,6))}return A},A}(),Ai=function A(e,t,r,n){e.container.elements.forEach((function(B){var s=GB(B.flags,4),o=GB(B.flags,2),i=new $o(B,e.getParentEffects());GB(B.styles.display,2048)&&n.push(i);var a=GB(B.flags,8)?[]:n;if(s||o){var c=s||B.styles.isPositioned()?r:t,Q=new jo(i);if(B.styles.isPositioned()||B.styles.opacity<1||B.styles.isTransformed()){var u=B.styles.zIndex.order;if(u<0){var w=0;c.negativeZIndex.some((function(A,e){return u>A.element.container.styles.zIndex.order?(w=e,!1):w>0})),c.negativeZIndex.splice(w,0,Q)}else if(u>0){var U=0;c.positiveZIndex.some((function(A,e){return u>A.element.container.styles.zIndex.order?(U=e+1,!1):U>0})),c.positiveZIndex.splice(U,0,Q)}else c.zeroOrAutoZIndexOrTransformedOrOpacity.push(Q)}else B.styles.isFloating()?c.nonPositionedFloats.push(Q):c.nonPositionedInlineLevel.push(Q);A(i,Q,s?Q:r,a)}else B.styles.isInlineLevel()?t.inlineLevel.push(i):t.nonInlineLevel.push(i),A(i,t,r,a);GB(B.flags,8)&&ei(B,a)}))},ei=function(A,e){for(var t=A instanceof Es?A.start:1,r=A instanceof Es&&A.reversed,n=0;n<e.length;n++){var B=e[n];B.container instanceof gs&&"number"===typeof B.container.value&&0!==B.container.value&&(t=B.container.value),B.listValue=Eo(t,B.container.styles.listStyleType,!0),t+=r?-1:1}},ti=function(A){var e=new $o(A,[]),t=new jo(e),r=[];return Ai(e,t,t,r),ei(e.container,r),t},ri=function(A,e){switch(e){case 0:return ni(A.topLeftBorderBox,A.topLeftPaddingBox,A.topRightBorderBox,A.topRightPaddingBox);case 1:return ni(A.topRightBorderBox,A.topRightPaddingBox,A.bottomRightBorderBox,A.bottomRightPaddingBox);case 2:return ni(A.bottomRightBorderBox,A.bottomRightPaddingBox,A.bottomLeftBorderBox,A.bottomLeftPaddingBox);case 3:default:return ni(A.bottomLeftBorderBox,A.bottomLeftPaddingBox,A.topLeftBorderBox,A.topLeftPaddingBox)}},ni=function(A,e,t,r){var n=[];return Vo(A)?n.push(A.subdivide(.5,!1)):n.push(A),Vo(t)?n.push(t.subdivide(.5,!0)):n.push(t),Vo(r)?n.push(r.subdivide(.5,!0).reverse()):n.push(r),Vo(e)?n.push(e.subdivide(.5,!1).reverse()):n.push(e),n},Bi=function(A){var e=A.bounds,t=A.styles;return e.add(t.borderLeftWidth,t.borderTopWidth,-(t.borderRightWidth+t.borderLeftWidth),-(t.borderTopWidth+t.borderBottomWidth))},si=function(A){var e=A.styles,t=A.bounds,r=kt(e.paddingLeft,t.width),n=kt(e.paddingRight,t.width),B=kt(e.paddingTop,t.width),s=kt(e.paddingBottom,t.width);return t.add(r+e.borderLeftWidth,B+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+r+n),-(e.borderTopWidth+e.borderBottomWidth+B+s))},oi=function(A,e){return 0===A?e.bounds:2===A?si(e):Bi(e)},ii=function(A,e){return A===cr.BORDER_BOX?e.bounds:A===cr.CONTENT_BOX?si(e):Bi(e)},ai=function(A,e,t){var r=oi(wi(A.styles.backgroundOrigin,e),A),n=ii(wi(A.styles.backgroundClip,e),A),B=ui(wi(A.styles.backgroundSize,e),t,r),s=B[0],o=B[1],i=Gt(wi(A.styles.backgroundPosition,e),r.width-s,r.height-o),a=Ui(wi(A.styles.backgroundRepeat,e),i,B,r,n),c=Math.round(r.left+i[0]),Q=Math.round(r.top+i[1]);return[a,c,Q,s,o]},ci=function(A){return bt(A)&&A.value===ln.AUTO},Qi=function(A){return"number"===typeof A},ui=function(A,e,t){var r=e[0],n=e[1],B=e[2],s=A[0],o=A[1];if(xt(s)&&o&&xt(o))return[kt(s,t.width),kt(o,t.height)];var i=Qi(B);if(bt(s)&&(s.value===ln.CONTAIN||s.value===ln.COVER)){if(Qi(B)){var a=t.width/t.height;return a<B!==(s.value===ln.COVER)?[t.width,t.width/B]:[t.height*B,t.height]}return[t.width,t.height]}var c=Qi(r),Q=Qi(n),u=c||Q;if(ci(s)&&(!o||ci(o))){if(c&&Q)return[r,n];if(!i&&!u)return[t.width,t.height];if(u&&i){var w=c?r:n*B,U=Q?n:r/B;return[w,U]}var l=c?r:t.width,C=Q?n:t.height;return[l,C]}if(i){var g=0,E=0;return xt(s)?g=kt(s,t.width):xt(o)&&(E=kt(o,t.height)),ci(s)?g=E*B:o&&!ci(o)||(E=g/B),[g,E]}var F=null,h=null;if(xt(s)?F=kt(s,t.width):o&&xt(o)&&(h=kt(o,t.height)),null===F||o&&!ci(o)||(h=c&&Q?F/r*n:t.height),null!==h&&ci(s)&&(F=c&&Q?h/n*r:t.width),null!==F&&null!==h)return[F,h];throw new Error("Unable to calculate background-size for element")},wi=function(A,e){var t=A[e];return"undefined"===typeof t?A[0]:t},Ui=function(A,e,t,r,n){var B=e[0],s=e[1],o=t[0],i=t[1];switch(A){case cn.REPEAT_X:return[new _o(Math.round(r.left),Math.round(r.top+s)),new _o(Math.round(r.left+r.width),Math.round(r.top+s)),new _o(Math.round(r.left+r.width),Math.round(i+r.top+s)),new _o(Math.round(r.left),Math.round(i+r.top+s))];case cn.REPEAT_Y:return[new _o(Math.round(r.left+B),Math.round(r.top)),new _o(Math.round(r.left+B+o),Math.round(r.top)),new _o(Math.round(r.left+B+o),Math.round(r.height+r.top)),new _o(Math.round(r.left+B),Math.round(r.height+r.top))];case cn.NO_REPEAT:return[new _o(Math.round(r.left+B),Math.round(r.top+s)),new _o(Math.round(r.left+B+o),Math.round(r.top+s)),new _o(Math.round(r.left+B+o),Math.round(r.top+s+i)),new _o(Math.round(r.left+B),Math.round(r.top+s+i))];default:return[new _o(Math.round(n.left),Math.round(n.top)),new _o(Math.round(n.left+n.width),Math.round(n.top)),new _o(Math.round(n.left+n.width),Math.round(n.height+n.top)),new _o(Math.round(n.left),Math.round(n.height+n.top))]}},li="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",Ci="Hidden Text",gi=function(){function A(A){this._data={},this._document=A}return A.prototype.parseMetrics=function(A,e){var t=this._document.createElement("div"),r=this._document.createElement("img"),n=this._document.createElement("span"),B=this._document.body;t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=e,t.style.margin="0",t.style.padding="0",B.appendChild(t),r.src=li,r.width=1,r.height=1,r.style.margin="0",r.style.padding="0",r.style.verticalAlign="baseline",n.style.fontFamily=A,n.style.fontSize=e,n.style.margin="0",n.style.padding="0",n.appendChild(this._document.createTextNode(Ci)),t.appendChild(n),t.appendChild(r);var s=r.offsetTop-n.offsetTop+2;t.removeChild(n),t.appendChild(this._document.createTextNode(Ci)),t.style.lineHeight="normal",r.style.verticalAlign="super";var o=r.offsetTop-t.offsetTop+2;return B.removeChild(t),{baseline:s,middle:o}},A.prototype.getMetrics=function(A,e){var t=A+" "+e;return"undefined"===typeof this._data[t]&&(this._data[t]=this.parseMetrics(A,e)),this._data[t]},A}(),Ei=1e4,Fi=function(){function A(A){this._activeEffects=[],this.canvas=A.canvas?A.canvas:document.createElement("canvas"),this.ctx=this.canvas.getContext("2d"),this.options=A,this.canvas.width=Math.floor(A.width*A.scale),this.canvas.height=Math.floor(A.height*A.scale),this.canvas.style.width=A.width+"px",this.canvas.style.height=A.height+"px",this.fontMetrics=new gi(document),this.ctx.scale(this.options.scale,this.options.scale),this.ctx.translate(-A.x+A.scrollX,-A.y+A.scrollY),this.ctx.textBaseline="bottom",this._activeEffects=[],vr.getInstance(A.id).debug("Canvas renderer initialized ("+A.width+"x"+A.height+" at "+A.x+","+A.y+") with scale "+A.scale)}return A.prototype.applyEffects=function(A,e){var t=this;while(this._activeEffects.length)this.popEffect();A.filter((function(A){return GB(A.target,e)})).forEach((function(A){return t.applyEffect(A)}))},A.prototype.applyEffect=function(A){this.ctx.save(),qo(A)&&(this.ctx.translate(A.offsetX,A.offsetY),this.ctx.transform(A.matrix[0],A.matrix[1],A.matrix[2],A.matrix[3],A.matrix[4],A.matrix[5]),this.ctx.translate(-A.offsetX,-A.offsetY)),Zo(A)&&(this.path(A.path),this.ctx.clip()),this._activeEffects.push(A)},A.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},A.prototype.renderStack=function(A){return r(this,void 0,void 0,(function(){var e;return n(this,(function(t){switch(t.label){case 0:return e=A.element.container.styles,e.isVisible()?(this.ctx.globalAlpha=e.opacity,[4,this.renderStackContent(A)]):[3,2];case 1:t.sent(),t.label=2;case 2:return[2]}}))}))},A.prototype.renderNode=function(A){return r(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return A.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(A)]:[3,3];case 1:return e.sent(),[4,this.renderNodeContent(A)];case 2:e.sent(),e.label=3;case 3:return[2]}}))}))},A.prototype.renderTextWithLetterSpacing=function(A,e){var t=this;if(0===e)this.ctx.fillText(A.text,A.bounds.left,A.bounds.top+A.bounds.height);else{var r=i(A.text).map((function(A){return a(A)}));r.reduce((function(e,r){return t.ctx.fillText(r,e,A.bounds.top+A.bounds.height),e+t.ctx.measureText(r).width}),A.bounds.left)}},A.prototype.createFontStyle=function(A){var e=A.fontVariant.filter((function(A){return"normal"===A||"small-caps"===A})).join(""),t=A.fontFamily.join(", "),r=Lt(A.fontSize)?""+A.fontSize.number+A.fontSize.unit:A.fontSize.number+"px";return[[A.fontStyle,e,A.fontWeight,r,t].join(" "),t,r]},A.prototype.renderTextNode=function(A,e){return r(this,void 0,void 0,(function(){var t,r,B,s,o=this;return n(this,(function(n){return t=this.createFontStyle(e),r=t[0],B=t[1],s=t[2],this.ctx.font=r,A.textBounds.forEach((function(A){o.ctx.fillStyle=nr(e.color),o.renderTextWithLetterSpacing(A,e.letterSpacing);var t=e.textShadow;t.length&&A.text.trim().length&&(t.slice(0).reverse().forEach((function(e){o.ctx.shadowColor=nr(e.color),o.ctx.shadowOffsetX=e.offsetX.number*o.options.scale,o.ctx.shadowOffsetY=e.offsetY.number*o.options.scale,o.ctx.shadowBlur=e.blur.number,o.ctx.fillText(A.text,A.bounds.left,A.bounds.top+A.bounds.height)})),o.ctx.shadowColor="",o.ctx.shadowOffsetX=0,o.ctx.shadowOffsetY=0,o.ctx.shadowBlur=0),e.textDecorationLine.length&&(o.ctx.fillStyle=nr(e.textDecorationColor||e.color),e.textDecorationLine.forEach((function(e){switch(e){case 1:var t=o.fontMetrics.getMetrics(B,s).baseline;o.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top+t),A.bounds.width,1);break;case 2:o.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top),A.bounds.width,1);break;case 3:var r=o.fontMetrics.getMetrics(B,s).middle;o.ctx.fillRect(A.bounds.left,Math.ceil(A.bounds.top+r),A.bounds.width,1);break}})))})),[2]}))}))},A.prototype.renderReplacedElement=function(A,e,t){if(t&&A.intrinsicWidth>0&&A.intrinsicHeight>0){var r=si(A),n=ko(e);this.path(n),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(t,0,0,A.intrinsicWidth,A.intrinsicHeight,r.left,r.top,r.width,r.height),this.ctx.restore()}},A.prototype.renderNodeContent=function(e){return r(this,void 0,void 0,(function(){var t,r,s,o,i,a,c,Q,u,w,U,l,C,g,E;return n(this,(function(n){switch(n.label){case 0:this.applyEffects(e.effects,4),t=e.container,r=e.curves,s=t.styles,o=0,i=t.textNodes,n.label=1;case 1:return o<i.length?(a=i[o],[4,this.renderTextNode(a,s)]):[3,4];case 2:n.sent(),n.label=3;case 3:return o++,[3,1];case 4:if(!(t instanceof Us))return[3,8];n.label=5;case 5:return n.trys.push([5,7,,8]),[4,this.options.cache.match(t.src)];case 6:return C=n.sent(),this.renderReplacedElement(t,r,C),[3,8];case 7:return n.sent(),vr.getInstance(this.options.id).error("Error loading image "+t.src),[3,8];case 8:if(t instanceof ls&&this.renderReplacedElement(t,r,t.canvas),!(t instanceof Cs))return[3,12];n.label=9;case 9:return n.trys.push([9,11,,12]),[4,this.options.cache.match(t.svg)];case 10:return C=n.sent(),this.renderReplacedElement(t,r,C),[3,12];case 11:return n.sent(),vr.getInstance(this.options.id).error("Error loading svg "+t.svg.substring(0,255)),[3,12];case 12:return t instanceof Ls&&t.tree?(c=new A({id:this.options.id,scale:this.options.scale,backgroundColor:t.backgroundColor,x:0,y:0,scrollX:0,scrollY:0,width:t.width,height:t.height,cache:this.options.cache,windowWidth:t.width,windowHeight:t.height}),[4,c.render(t.tree)]):[3,14];case 13:Q=n.sent(),t.width&&t.height&&this.ctx.drawImage(Q,0,0,t.width,t.height,t.bounds.left,t.bounds.top,t.bounds.width,t.bounds.height),n.label=14;case 14:if(t instanceof Is&&(u=Math.min(t.bounds.width,t.bounds.height),t.type===fs?t.checked&&(this.ctx.save(),this.path([new _o(t.bounds.left+.39363*u,t.bounds.top+.79*u),new _o(t.bounds.left+.16*u,t.bounds.top+.5549*u),new _o(t.bounds.left+.27347*u,t.bounds.top+.44071*u),new _o(t.bounds.left+.39694*u,t.bounds.top+.5649*u),new _o(t.bounds.left+.72983*u,t.bounds.top+.23*u),new _o(t.bounds.left+.84*u,t.bounds.top+.34085*u),new _o(t.bounds.left+.39363*u,t.bounds.top+.79*u)]),this.ctx.fillStyle=nr(Ks),this.ctx.fill(),this.ctx.restore()):t.type===ps&&t.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(t.bounds.left+u/2,t.bounds.top+u/2,u/4,0,2*Math.PI,!0),this.ctx.fillStyle=nr(Ks),this.ctx.fill(),this.ctx.restore())),hi(t)&&t.value.length){switch(this.ctx.font=this.createFontStyle(s)[0],this.ctx.fillStyle=nr(s.color),this.ctx.textBaseline="middle",this.ctx.textAlign=di(t.styles.textAlign),E=si(t),w=0,t.styles.textAlign){case cB.CENTER:w+=E.width/2;break;case cB.RIGHT:w+=E.width;break}U=E.add(w,0,0,-E.height/2+1),this.ctx.save(),this.path([new _o(E.left,E.top),new _o(E.left+E.width,E.top),new _o(E.left+E.width,E.top+E.height),new _o(E.left,E.top+E.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new ns(t.value,U),s.letterSpacing),this.ctx.restore(),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"}if(!GB(t.styles.display,2048))return[3,20];if(null===t.styles.listStyleImage)return[3,19];if(l=t.styles.listStyleImage,l.type!==Ur.URL)return[3,18];C=void 0,g=l.url,n.label=15;case 15:return n.trys.push([15,17,,18]),[4,this.options.cache.match(g)];case 16:return C=n.sent(),this.ctx.drawImage(C,t.bounds.left-(C.width+10),t.bounds.top),[3,18];case 17:return n.sent(),vr.getInstance(this.options.id).error("Error loading list-style-image "+g),[3,18];case 18:return[3,20];case 19:e.listValue&&t.styles.listStyleType!==$n.NONE&&(this.ctx.font=this.createFontStyle(s)[0],this.ctx.fillStyle=nr(s.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",E=new B(t.bounds.left,t.bounds.top+kt(t.styles.paddingTop,t.bounds.width),t.bounds.width,Zn(s.lineHeight,s.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new ns(e.listValue,E),s.letterSpacing),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),n.label=20;case 20:return[2]}}))}))},A.prototype.renderStackContent=function(A){return r(this,void 0,void 0,(function(){var e,t,r,B,s,o,i,a,c,Q,u,w,U,l,C;return n(this,(function(n){switch(n.label){case 0:return[4,this.renderNodeBackgroundAndBorders(A.element)];case 1:n.sent(),e=0,t=A.negativeZIndex,n.label=2;case 2:return e<t.length?(C=t[e],[4,this.renderStack(C)]):[3,5];case 3:n.sent(),n.label=4;case 4:return e++,[3,2];case 5:return[4,this.renderNodeContent(A.element)];case 6:n.sent(),r=0,B=A.nonInlineLevel,n.label=7;case 7:return r<B.length?(C=B[r],[4,this.renderNode(C)]):[3,10];case 8:n.sent(),n.label=9;case 9:return r++,[3,7];case 10:s=0,o=A.nonPositionedFloats,n.label=11;case 11:return s<o.length?(C=o[s],[4,this.renderStack(C)]):[3,14];case 12:n.sent(),n.label=13;case 13:return s++,[3,11];case 14:i=0,a=A.nonPositionedInlineLevel,n.label=15;case 15:return i<a.length?(C=a[i],[4,this.renderStack(C)]):[3,18];case 16:n.sent(),n.label=17;case 17:return i++,[3,15];case 18:c=0,Q=A.inlineLevel,n.label=19;case 19:return c<Q.length?(C=Q[c],[4,this.renderNode(C)]):[3,22];case 20:n.sent(),n.label=21;case 21:return c++,[3,19];case 22:u=0,w=A.zeroOrAutoZIndexOrTransformedOrOpacity,n.label=23;case 23:return u<w.length?(C=w[u],[4,this.renderStack(C)]):[3,26];case 24:n.sent(),n.label=25;case 25:return u++,[3,23];case 26:U=0,l=A.positiveZIndex,n.label=27;case 27:return U<l.length?(C=l[U],[4,this.renderStack(C)]):[3,30];case 28:n.sent(),n.label=29;case 29:return U++,[3,27];case 30:return[2]}}))}))},A.prototype.mask=function(A){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(A.slice(0).reverse()),this.ctx.closePath()},A.prototype.path=function(A){this.ctx.beginPath(),this.formatPath(A),this.ctx.closePath()},A.prototype.formatPath=function(A){var e=this;A.forEach((function(A,t){var r=Vo(A)?A.start:A;0===t?e.ctx.moveTo(r.x,r.y):e.ctx.lineTo(r.x,r.y),Vo(A)&&e.ctx.bezierCurveTo(A.startControl.x,A.startControl.y,A.endControl.x,A.endControl.y,A.end.x,A.end.y)}))},A.prototype.renderRepeat=function(A,e,t,r){this.path(A),this.ctx.fillStyle=e,this.ctx.translate(t,r),this.ctx.fill(),this.ctx.translate(-t,-r)},A.prototype.resizeImage=function(A,e,t){if(A.width===e&&A.height===t)return A;var r=this.canvas.ownerDocument.createElement("canvas");r.width=e,r.height=t;var n=r.getContext("2d");return n.drawImage(A,0,0,A.width,A.height,0,0,e,t),r},A.prototype.renderBackgroundImage=function(A){return r(this,void 0,void 0,(function(){var e,t,r,B,s,o;return n(this,(function(i){switch(i.label){case 0:e=A.styles.backgroundImage.length-1,t=function(t){var B,s,o,i,a,c,Q,u,w,U,l,C,g,E,F,h,H,d,f,p,N,K,I,T,m,R,L,O,b,D,v;return n(this,(function(n){switch(n.label){case 0:if(t.type!==Ur.URL)return[3,5];B=void 0,s=t.url,n.label=1;case 1:return n.trys.push([1,3,,4]),[4,r.options.cache.match(s)];case 2:return B=n.sent(),[3,4];case 3:return n.sent(),vr.getInstance(r.options.id).error("Error loading background-image "+s),[3,4];case 4:return B&&(o=ai(A,e,[B.width,B.height,B.width/B.height]),h=o[0],K=o[1],I=o[2],f=o[3],p=o[4],E=r.ctx.createPattern(r.resizeImage(B,f,p),"repeat"),r.renderRepeat(h,E,K,I)),[3,6];case 5:Bn(t)?(i=ai(A,e,[null,null,null]),h=i[0],K=i[1],I=i[2],f=i[3],p=i[4],a=hr(t.angle,f,p),c=a[0],Q=a[1],u=a[2],w=a[3],U=a[4],l=document.createElement("canvas"),l.width=f,l.height=p,C=l.getContext("2d"),g=C.createLinearGradient(Q,w,u,U),Er(t.stops,c).forEach((function(A){return g.addColorStop(A.stop,nr(A.color))})),C.fillStyle=g,C.fillRect(0,0,f,p),f>0&&p>0&&(E=r.ctx.createPattern(l,"repeat"),r.renderRepeat(h,E,K,I))):sn(t)&&(F=ai(A,e,[null,null,null]),h=F[0],H=F[1],d=F[2],f=F[3],p=F[4],N=0===t.position.length?[Xt]:t.position,K=kt(N[0],f),I=kt(N[N.length-1],p),T=fr(t,K,I,f,p),m=T[0],R=T[1],m>0&&m>0&&(L=r.ctx.createRadialGradient(H+K,d+I,0,H+K,d+I,m),Er(t.stops,2*m).forEach((function(A){return L.addColorStop(A.stop,nr(A.color))})),r.path(h),r.ctx.fillStyle=L,m!==R?(O=A.bounds.left+.5*A.bounds.width,b=A.bounds.top+.5*A.bounds.height,D=R/m,v=1/D,r.ctx.save(),r.ctx.translate(O,b),r.ctx.transform(1,0,0,D,0,0),r.ctx.translate(-O,-b),r.ctx.fillRect(H,v*(d-b)+b,f,p*v),r.ctx.restore()):r.ctx.fill())),n.label=6;case 6:return e--,[2]}}))},r=this,B=0,s=A.styles.backgroundImage.slice(0).reverse(),i.label=1;case 1:return B<s.length?(o=s[B],[5,t(o)]):[3,4];case 2:i.sent(),i.label=3;case 3:return B++,[3,1];case 4:return[2]}}))}))},A.prototype.renderBorder=function(A,e,t){return r(this,void 0,void 0,(function(){return n(this,(function(r){return this.path(ri(t,e)),this.ctx.fillStyle=nr(A),this.ctx.fill(),[2]}))}))},A.prototype.renderNodeBackgroundAndBorders=function(A){return r(this,void 0,void 0,(function(){var e,t,r,B,s,o,i,a,c=this;return n(this,(function(n){switch(n.label){case 0:return this.applyEffects(A.effects,2),e=A.container.styles,t=!rr(e.backgroundColor)||e.backgroundImage.length,r=[{style:e.borderTopStyle,color:e.borderTopColor},{style:e.borderRightStyle,color:e.borderRightColor},{style:e.borderBottomStyle,color:e.borderBottomColor},{style:e.borderLeftStyle,color:e.borderLeftColor}],B=Hi(wi(e.backgroundClip,0),A.curves),t||e.boxShadow.length?(this.ctx.save(),this.path(B),this.ctx.clip(),rr(e.backgroundColor)||(this.ctx.fillStyle=nr(e.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(A.container)]):[3,2];case 1:n.sent(),this.ctx.restore(),e.boxShadow.slice(0).reverse().forEach((function(e){c.ctx.save();var t=Jo(A.curves),r=e.inset?0:Ei,n=yo(t,-r+(e.inset?1:-1)*e.spread.number,(e.inset?1:-1)*e.spread.number,e.spread.number*(e.inset?-2:2),e.spread.number*(e.inset?-2:2));e.inset?(c.path(t),c.ctx.clip(),c.mask(n)):(c.mask(t),c.ctx.clip(),c.path(n)),c.ctx.shadowOffsetX=e.offsetX.number+r,c.ctx.shadowOffsetY=e.offsetY.number,c.ctx.shadowColor=nr(e.color),c.ctx.shadowBlur=e.blur.number,c.ctx.fillStyle=e.inset?nr(e.color):"rgba(0,0,0,1)",c.ctx.fill(),c.ctx.restore()})),n.label=2;case 2:s=0,o=0,i=r,n.label=3;case 3:return o<i.length?(a=i[o],a.style===En.NONE||rr(a.color)?[3,5]:[4,this.renderBorder(a.color,s,A.curves)]):[3,7];case 4:n.sent(),n.label=5;case 5:s++,n.label=6;case 6:return o++,[3,3];case 7:return[2]}}))}))},A.prototype.render=function(A){return r(this,void 0,void 0,(function(){var e;return n(this,(function(t){switch(t.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=nr(this.options.backgroundColor),this.ctx.fillRect(this.options.x-this.options.scrollX,this.options.y-this.options.scrollY,this.options.width,this.options.height)),e=ti(A),[4,this.renderStack(e)];case 1:return t.sent(),this.applyEffects([],2),[2,this.canvas]}}))}))},A}(),hi=function(A){return A instanceof ms||(A instanceof Ts||A instanceof Is&&A.type!==ps&&A.type!==fs)},Hi=function(A,e){switch(A){case cr.BORDER_BOX:return Jo(e);case cr.CONTENT_BOX:return Go(e);case cr.PADDING_BOX:default:return ko(e)}},di=function(A){switch(A){case cB.CENTER:return"center";case cB.RIGHT:return"right";case cB.LEFT:default:return"left"}},fi=function(){function A(A){this.canvas=A.canvas?A.canvas:document.createElement("canvas"),this.ctx=this.canvas.getContext("2d"),this.options=A,this.canvas.width=Math.floor(A.width*A.scale),this.canvas.height=Math.floor(A.height*A.scale),this.canvas.style.width=A.width+"px",this.canvas.style.height=A.height+"px",this.ctx.scale(this.options.scale,this.options.scale),this.ctx.translate(-A.x+A.scrollX,-A.y+A.scrollY),vr.getInstance(A.id).debug("EXPERIMENTAL ForeignObject renderer initialized ("+A.width+"x"+A.height+" at "+A.x+","+A.y+") with scale "+A.scale)}return A.prototype.render=function(A){return r(this,void 0,void 0,(function(){var e,t;return n(this,(function(r){switch(r.label){case 0:return e=Or(Math.max(this.options.windowWidth,this.options.width)*this.options.scale,Math.max(this.options.windowHeight,this.options.height)*this.options.scale,this.options.scrollX*this.options.scale,this.options.scrollY*this.options.scale,A),[4,pi(e)];case 1:return t=r.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=nr(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(t,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}}))}))},A}(),pi=function(A){return new Promise((function(e,t){var r=new Image;r.onload=function(){e(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},Ni=void 0,Ki=function(A){return tr.parse(Rt.create(A).parseComponentValue())},Ii=function(A,e){return void 0===e&&(e={}),Ti(A,e)};Sr.setContext(window);var Ti=function(A,e){return r(Ni,void 0,void 0,(function(){var r,i,a,c,Q,u,w,U,l,C,g,E,F,h,H,d,f,p,N,K,I,T,m,R,L;return n(this,(function(n){switch(n.label){case 0:if(r=A.ownerDocument,!r)throw new Error("Element is not attached to a Document");if(i=r.defaultView,!i)throw new Error("Document is not attached to a Window");return a=(Math.round(1e3*Math.random())+Date.now()).toString(16),c=Gs(A)||Xs(A)?o(r):s(A),Q=c.width,u=c.height,w=c.left,U=c.top,l={allowTaint:!1,imageTimeout:15e3,proxy:void 0,useCORS:!1},C=t({},l,e),g={backgroundColor:"#ffffff",cache:e.cache?e.cache:Sr.create(a,C),logging:!0,removeContainer:!0,foreignObjectRendering:!1,scale:i.devicePixelRatio||1,windowWidth:i.innerWidth,windowHeight:i.innerHeight,scrollX:i.pageXOffset,scrollY:i.pageYOffset,x:w,y:U,width:Math.ceil(Q),height:Math.ceil(u),id:a},E=t({},g,C,e),F=new B(E.scrollX,E.scrollY,E.windowWidth,E.windowHeight),vr.create(a),vr.getInstance(a).debug("Starting document clone"),h=new ho(A,{id:a,onclone:E.onclone,ignoreElements:E.ignoreElements,inlineImages:E.foreignObjectRendering,copyStyles:E.foreignObjectRendering}),H=h.clonedReferenceElement,H?[4,h.toIFrame(r,F)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return d=n.sent(),f=r.documentElement?Ki(getComputedStyle(r.documentElement).backgroundColor):wr.TRANSPARENT,p=r.body?Ki(getComputedStyle(r.body).backgroundColor):wr.TRANSPARENT,N=e.backgroundColor,K="string"===typeof N?Ki(N):null===N?wr.TRANSPARENT:4294967295,I=A===r.documentElement?rr(f)?rr(p)?K:p:f:K,T={id:a,cache:E.cache,backgroundColor:I,scale:E.scale,x:E.x,y:E.y,scrollX:E.scrollX,scrollY:E.scrollY,width:E.width,height:E.height,windowWidth:E.windowWidth,windowHeight:E.windowHeight},E.foreignObjectRendering?(vr.getInstance(a).debug("Document cloned, using foreign object rendering"),L=new fi(T),[4,L.render(H)]):[3,3];case 2:return m=n.sent(),[3,5];case 3:return vr.getInstance(a).debug("Document cloned, using computed rendering"),Sr.attachInstance(E.cache),vr.getInstance(a).debug("Starting DOM parsing"),R=vs(H),Sr.detachInstance(),I===R.styles.backgroundColor&&(R.styles.backgroundColor=wr.TRANSPARENT),vr.getInstance(a).debug("Starting renderer"),L=new Fi(T),[4,L.render(R)];case 4:m=n.sent(),n.label=5;case 5:return!0===E.removeContainer&&(mi(d)||vr.getInstance(a).error("Cannot detach cloned iframe as it is not in the DOM anymore")),vr.getInstance(a).debug("Finished rendering"),vr.destroy(a),Sr.destroy(a),[2,m]}}))}))},mi=function(A){return!!A.parentNode&&(A.parentNode.removeChild(A),!0)};return Ii}))}}]);
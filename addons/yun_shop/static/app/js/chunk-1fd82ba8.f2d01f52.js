(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1fd82ba8","chunk-2d0b235b"],{"078a":function(e,t,i){var a=i("a1a8");t=a(!1),t.push([e.i,'#orderList .blank[data-v-14f5a022]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;position:fixed;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}#orderList .blank img[data-v-14f5a022]{width:6rem;height:6rem}#orderList .blank span[data-v-14f5a022]{margin-top:15px;color:#ccc;font-size:14px}#orderList .mint-navbar .mint-tab-item[data-v-14f5a022]{padding:.875rem 0}#orderList .van-dialog[data-v-14f5a022]{width:80%;height:25rem;padding-bottom:3.125rem;padding-top:3.125rem}#orderList .van-dialog .van-dialog__content[data-v-14f5a022]{padding-bottom:1.25rem}#orderList .van-dialog .van-dialog__content section[data-v-14f5a022]{height:16rem;padding:0 .75rem;font-size:14px;overflow-y:scroll}#orderList .van-dialog .van-dialog__content section .van-button--plain.van-button--primary[data-v-14f5a022]{color:#1989fa;width:100%;text-align:right}#orderList .van-dialog .van-dialog__content section .van-button--normal[data-v-14f5a022]{padding:0}#orderList .van-dialog .van-dialog__content section .van-cell[data-v-14f5a022]{padding:.875rem 0}#orderList .van-dialog .van-dialog__content section .van-hairline--top-bottom[data-v-14f5a022]:after{border-width:0}#orderList .van-dialog .van-dialog__content .title[data-v-14f5a022]{line-height:3.125rem;font-weight:700;padding:0 .75rem 0 .75rem;position:fixed;top:0;width:100%;background:#fff;border-bottom:1px solid #ebebeb}#orderList .van-dialog .van-dialog__content .addressee[data-v-14f5a022]{font-size:15px;font-weight:700;min-height:1.875rem;line-height:1.875rem}#orderList .van-dialog .van-dialog__content .user_name[data-v-14f5a022]{width:100%;min-height:1.5rem;line-height:1.5rem;overflow:visible;color:#8c8c8c}#orderList .van-dialog .van-dialog__content .user_city[data-v-14f5a022]{width:100%;overflow:visible;line-height:1.5rem;color:#8c8c8c;margin-bottom:.625rem}#orderList .van-dialog .van-dialog__content .input_number[data-v-14f5a022],#orderList .van-dialog .van-dialog__content .select_company[data-v-14f5a022]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;width:100%;height:2.875rem;line-height:2.8125rem}#orderList .van-dialog .van-dialog__content .input_number .company_right[data-v-14f5a022],#orderList .van-dialog .van-dialog__content .input_number .number_right[data-v-14f5a022],#orderList .van-dialog .van-dialog__content .select_company .company_right[data-v-14f5a022],#orderList .van-dialog .van-dialog__content .select_company .number_right[data-v-14f5a022]{width:60%;text-align:right;height:2.875rem;border-bottom:.0625rem solid #ebebeb}#orderList .van-dialog .van-dialog__content .store_foot[data-v-14f5a022]{width:100%;position:fixed;bottom:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}#orderList .van-dialog .van-dialog__content .store_foot .van-button[data-v-14f5a022]{width:50%;height:3.125rem;background:#fff;font-size:16px}#orderList .van-dialog .van-dialog__content .store_foot .btn_b[data-v-14f5a022]{color:#1989fa;border-left:.0625rem solid #ebebeb;border-top:.0625rem solid #ebebeb}#orderList .van-dialog .van-dialog__content .store_foot .btn_a[data-v-14f5a022]{border-top:.0625rem solid #ebebeb}.olis[data-v-14f5a022]{height:100vh}.Margintop[data-v-14f5a022]{margin-bottom:3.75rem}.shop[data-v-14f5a022]{background:#fff;margin-top:.625rem;border-bottom:.0625rem solid #ebebeb;border-top:.0625rem solid #ebebeb}.shop a[data-v-14f5a022]{color:#000}.shop .title[data-v-14f5a022]:after{content:"";display:block;clear:both}.shop .title[data-v-14f5a022]{padding:0 .875rem;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.shop .title h4[data-v-14f5a022]{text-align:left;font-weight:400;line-height:2.25rem;-webkit-box-flex:8;-webkit-flex:8;-ms-flex:8;flex:8;font-size:14px}.shop .title h4 b[data-v-14f5a022]{font-size:18px}.shop .title span[data-v-14f5a022]{color:#f15353;text-align:right;margin-right:.0625rem;-webkit-box-flex:3;-webkit-flex:3;-ms-flex:3;flex:3;font-size:14px}.shop span.del[data-v-14f5a022]{border-radius:.875rem;border:.0625rem solid #f15353;text-align:center;padding:.25rem .625rem;font-size:12px}.shop .allbt[data-v-14f5a022]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:end;-webkit-justify-content:flex-end;-ms-flex-pack:end;justify-content:flex-end;border-top:.0625rem solid #ebebeb;padding-right:.875rem;padding-top:.5rem;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap}.shop .allbt span[data-v-14f5a022]{border-radius:.1875rem;border:.0625rem solid #666;margin-left:.625rem;padding:.25rem .625rem;text-align:center;font-size:12px;margin-bottom:.5rem}.shop .goods[data-v-14f5a022]:after{content:"";display:block;clear:both}.shop .goods[data-v-14f5a022]{width:100%;-webkit-box-sizing:border-box;box-sizing:border-box;background:#fafafa}.shop .goods .img[data-v-14f5a022]{padding:.625rem 1rem;width:26%;float:left;display:inline-block}.shop .goods .img img[data-v-14f5a022]{width:100%}.shop .goods .warp[data-v-14f5a022]{width:74%;padding:.625rem 1rem .625rem 0;float:left;display:inline-block}.shop .goods .warp .inner[data-v-14f5a022]{width:70%;float:left;-webkit-box-sizing:border-box;box-sizing:border-box;text-align:left}.shop .goods .warp .inner .name[data-v-14f5a022]{font-size:14px;text-align:left;color:#333;margin-bottom:.625rem;height:2.5rem;line-height:1.25rem;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.shop .goods .warp .price[data-v-14f5a022]{font-size:14px;width:30%;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;text-align:right;color:#333;-webkit-box-sizing:border-box;box-sizing:border-box;float:left}.shop .goods .warp .price .money[data-v-14f5a022]{margin-bottom:.625rem}.shop .goods .warp .price small[data-v-14f5a022]{font-size:12px}.shop .goods .option[data-v-14f5a022]{color:#888;font-size:12px;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.shop #tbs[data-v-14f5a022]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;background:#fff;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-webkit-flex-flow:row wrap;-ms-flex-flow:row wrap;flex-flow:row wrap;padding:.625rem;line-height:1.5rem;border-bottom:.0625rem solid #d9d9d9}.shop #tbs .left[data-v-14f5a022]{text-align:left;color:#858585}.shop #tbs .left[data-v-14f5a022],.shop #tbs .right[data-v-14f5a022]{-webkit-box-flex:50%;-webkit-flex:50%;-ms-flex:50%;flex:50%}.shop #tbs .right[data-v-14f5a022]{text-align:right}.shop #tbs .add[data-v-14f5a022]{color:#259b24}.shop #tbs .reduce[data-v-14f5a022]{color:#e51c23}',""]),e.exports=t},"22af":function(e,t){e.exports="data:image/png;base64,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"},"2ea27":function(e,t,i){var a=i("751f");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var o=i("5925").default;o("cc1dd9fa",a,!0,{sourceMap:!1,shadowMode:!1})},"751f":function(e,t,i){var a=i("a1a8");t=a(!1),t.push([e.i,".osll[data-v-246d1e1c]{min-height:100vh}.contes[data-v-246d1e1c]{z-index:98;position:fixed;bottom:0;width:100%;height:2.8125rem;background:#fff;line-height:2.8125rem;border-top:.0625rem solid #e2e2e2;text-align:right}.contes button[data-v-246d1e1c]{border:.0625rem solid #f15353;background:#f15353;border-radius:.1875rem;margin-right:.875rem;color:#fff;padding:.25rem .625rem;height:2rem;line-height:1rem;margin-top:.4rem}.loadNomore img[data-v-246d1e1c]{width:20%}#payment .mint-navbar.is-fixed[data-v-246d1e1c]{top:40px}",""]),e.exports=t},acd79:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{attrs:{id:"payment"}},[i("c-title",{attrs:{hide:!1,text:e.titleText}}),i("van-sticky",{attrs:{"offset-top":"2.75rem"}},[i("van-tabs",{staticStyle:{"z-index":"98",height:"2.75rem","line-height":"2.75rem"},on:{click:e.swichTabTItem},model:{value:e.selected,callback:function(t){e.selected=t},expression:"selected"}},e._l(e.setNavList,(function(e,t){return i("van-tab",{key:t,attrs:{name:t.toString(),title:e}})})),1)],1),i("van-pull-refresh",{attrs:{"success-text":"刷新成功"},on:{refresh:e.loadTop},model:{value:e.isLoading,callback:function(t){e.isLoading=t},expression:"isLoading"}},[i("div",e._l(e.dataList,(function(t,a,o){return i("div",{directives:[{name:"show",rawName:"v-show",value:e.selected==o.toString(),expression:"selected==indexs.toString()"}],key:o},[i("cOrderList",{ref:"mychild",refInFor:!0,staticClass:"osll",attrs:{datasource:t,orderType:e.orderType,detailUrl:e.detailUrl,status:0},on:{ConfrimOrderNotification:e.cofirmOrderAction,MultiplePayNotification:e.multiplePayAction}})],1)})),0)]),e.isMultiplePay?i("div",{staticClass:"contes"},[i("button",{on:{click:e.toMultiplePay}},[e._v("合并支付")])]):e._e()],1)},o=[],r=(i("8d1b"),i("44ad"),i("6968")),n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{attrs:{id:"orderList"}},[a("van-checkbox-group",{on:{change:e.multiplePayChange},model:{value:e.checkList,callback:function(t){e.checkList=t},expression:"checkList"}},e._l(e.datasource,(function(t,i){return a("div",{key:i,class:"lease_toy"==e.orderType&&i==e.datasource.length-1?"Margintop shop":"shop"},[a("div",{staticClass:"title"},[1==e.status?a("van-checkbox",{attrs:{"checked-color":"#f15353",shape:"square",name:t.id}},[e._v("  ")]):e._e(),a("h4",[e._v("订单号："+e._s(t.order_sn))]),a("span",[e._v(e._s(t.status_name))])],1),e._l(t.has_many_order_goods,(function(i,o){return a("router-link",{key:o,attrs:{to:e.fun.getUrl(e.detailUrl,{order_id:t.id,orderType:e.orderType})}},[a("div",{staticClass:"goods"},[a("div",{staticClass:"img"},[a("img",{attrs:{src:i.thumb}})]),a("div",{staticClass:"warp"},[a("div",{staticClass:"inner"},[a("div",{staticClass:"name",staticStyle:{"-webkit-box-orient":"vertical"}},[e._v(" "+e._s(e._f("escapeTitle")(i.title))+" ")]),a("div",{staticClass:"option"},[e._v("规格: "+e._s(i.goods_option_title))])]),a("div",{staticClass:"price"},[a("div",{staticClass:"money"},[a("small",[e._v(e._s(e.$i18n.t("money")))]),e._v(" "+e._s("lease_toy"==e.orderType?i.price/t.has_one_lease_toy_order.return_days/i.total:i.price)+" "),40==t.plugin_id?a("span",[e._v("/天")]):e._e()]),"HotelOrderDetail"!=e.detailUrl?a("div",[e._v("×"+e._s(i.total))]):e._e(),"HotelOrderDetail"==e.detailUrl?a("div",[e._v("×"+e._s(e.getDays(t.hotel_order.enter_at,t.hotel_order.leave_at))+"晚"+e._s(i.total)+"间")]):e._e()])])])])})),a("div",{staticClass:"title"},[a("h4",{staticStyle:{"text-align":"right"}},[e._v(" 共 "+e._s(t.goods_total)+" "+e._s("HotelOrderDetail"==e.detailUrl?"个房间":"件商品")+" 实付："+e._s(e.$i18n.t("money"))),a("b",[e._v(e._s(t.price))])])]),t.button_models.length>0||!e.fun.isTextEmpty(t.replenishment_button)?a("div",{staticClass:"allbt"},[e.fun.isTextEmpty(t.replenishment_button)?e._e():a("span",{on:{click:function(i){return e.channelReplenishment(t.replenishment_button.order_id)}}},[e._v(e._s(t.replenishment_button.botton_name))]),t.button_models.length>0?e._l(t.button_models,(function(i,o){return a("span",{key:o,on:{click:function(a){return e.operation(i,t)}}},[e._v(e._s(i.name))])})):e._e()],2):e._e()],2)})),0),a("div",{staticClass:"qrcode"},[a("van-popup",{attrs:{position:"right",overlay:!1},model:{value:e.showQrcode,callback:function(t){e.showQrcode=t},expression:"showQrcode"}},[a("div",[a("img",{staticStyle:{width:"100%"},attrs:{src:e.qrcode}}),a("div",{staticStyle:{height:"1.875rem"}},[e._v("请核销员扫码")])])])],1),a("van-dialog",{staticStyle:{},attrs:{showConfirmButton:!1},nativeOn:{touchmove:function(t){return e.stopmove.apply(null,arguments)},mousemove:function(t){return e.stopmove.apply(null,arguments)}},model:{value:e.dialogVisible,callback:function(t){e.dialogVisible=t},expression:"dialogVisible"}},[a("div",{staticClass:"title"},[e._v("确认发货")]),a("van-tabs",{model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("van-tab",{attrs:{name:"first",title:"快递配送"}}),e.isShowDri?a("van-tab",{attrs:{name:"second",title:"司机配送"}}):e._e()],1),a("div",[a("div",{directives:[{name:"show",rawName:"v-show",value:"first"==e.activeName,expression:"activeName == 'first'"}]},[a("section",{staticStyle:{width:"100%","text-align":"left","padding-bottom":"2.5rem","padding-top":"0.625rem"}},[e.addresseeInfo?a("div",{staticStyle:{width:"100%"}},[a("div",{staticClass:"addressee"},[e._v(" 收件人信息: ")]),a("div",[a("div",{staticClass:"user_name"},[a("span",[e._v(e._s(e.addresseeInfo.realname))]),a("span",[e._v("("+e._s(e.addresseeInfo.mobile)+")")])]),a("div",{staticClass:"user_city"},[e._v(" "+e._s(e.addresseeInfo.address)+" ")])])]):e._e(),a("div",{staticClass:"select_company"},[a("div",{staticStyle:{"font-weight":"bold","font-size":"15px"}},[e._v("快递公司:")]),a("div",{staticClass:"company_right"},[a("van-button",{attrs:{plain:"",type:"primary"},on:{click:e.seleRegional}},[e._v(" "+e._s(e.regional)+" ")])],1)]),a("div",{staticClass:"input_number"},[a("div",{staticStyle:{"font-weight":"bold","font-size":"15px"}},[e._v("快递单号:")]),a("div",{staticClass:"number_right"},[a("van-cell-group",[a("van-field",{attrs:{placeholder:"请输入快递单号"},model:{value:e.numberName,callback:function(t){e.numberName=t},expression:"numberName"}})],1)],1)])])]),a("div",{directives:[{name:"show",rawName:"v-show",value:"second"==e.activeName,expression:"activeName == 'second'"}]},[a("section",{staticStyle:{width:"100%","text-align":"left","padding-bottom":"2.5rem","padding-top":"0.625rem"}},[e.addresseeInfo?a("div",{staticStyle:{width:"100%"}},[a("div",{staticClass:"addressee"},[e._v(" 收件人信息: ")]),a("div",[a("div",{staticClass:"user_name"},[a("span",[e._v(e._s(e.addresseeInfo.realname))]),a("span",[e._v("("+e._s(e.addresseeInfo.mobile)+")")])]),a("div",{staticClass:"user_city"},[e._v(" "+e._s(e.addresseeInfo.address)+" ")])])]):e._e(),a("div",{staticClass:"select_company",staticStyle:{"justify-content":"flex-start"}},[a("div",{staticStyle:{"font-weight":"bold","font-size":"15px",width:"5rem"}},[e._v(" 配送司机: ")]),a("div",{staticClass:"company_right",staticStyle:{"text-align":"left","margin-left":"0.5rem"}},[a("van-button",{staticStyle:{"text-align":"left"},attrs:{plain:"",type:"primary"},on:{click:e.driverSeleRegional}},[e._v(" "+e._s(e.driverRegional)+" ")])],1)]),a("div",{staticClass:"select_company",staticStyle:{"justify-content":"flex-start"}},[a("div",{staticStyle:{"font-weight":"bold","font-size":"15px",width:"5rem"}},[e._v(" 车牌号: ")]),a("div",{staticClass:"company_right",staticStyle:{"text-align":"left","margin-left":"0.5rem"}},[e._v(" "+e._s(e.driverNumber)+" ")])])])])]),a("div",{staticClass:"store_foot"},[a("van-button",{staticClass:"btn_a",attrs:{type:"default"},on:{click:e.sendCancel}},[e._v("取 消")]),a("van-button",{staticClass:"btn_b",attrs:{type:"primary"},on:{click:e.sendGoogs}},[e._v("确 定")])],1)],1),a("van-popup",{attrs:{position:"bottom",overlay:!0},model:{value:e.dateshow_1,callback:function(t){e.dateshow_1=t},expression:"dateshow_1"}},[a("van-picker",{attrs:{columns:e.columns,"show-toolbar":""},on:{change:e.onChange,cancel:e.onCancelbtn,confirm:e.onConfirmbtn},nativeOn:{touchmove:function(t){return e.stoppao.apply(null,arguments)},mousemove:function(t){return e.stoppao.apply(null,arguments)}}})],1),a("van-popup",{attrs:{position:"bottom",overlay:!0},model:{value:e.driverDateshow_1,callback:function(t){e.driverDateshow_1=t},expression:"driverDateshow_1"}},[a("van-picker",{attrs:{columns:e.driverColumns,"show-toolbar":""},on:{change:e.onDriverChange,cancel:e.onDriverCancelbtn,confirm:e.onDriverConfirmbtn},nativeOn:{touchmove:function(t){return e.stoppao.apply(null,arguments)},mousemove:function(t){return e.stoppao.apply(null,arguments)}}})],1),a("van-popup",{attrs:{position:"bottom",overlay:!0},model:{value:e.showWriteOff,callback:function(t){e.showWriteOff=t},expression:"showWriteOff"}},[a("img",{staticStyle:{width:"11.25rem",height:"11.25rem",margin:"2rem 0 1rem 0"},attrs:{src:e.writeOffQR}}),a("div",{staticStyle:{"font-size":"18px","font-weight":"bold","margin-bottom":"1rem",color:"#f15353"}},[e._v(" 请核销员扫码 ")])]),e.datasource.length<=0?a("div",{staticClass:"blank"},[a("img",{attrs:{src:i("22af")}}),a("span",[e._v("还没有订单记录哦")])]):e._e()],1)},s=[],d=(i("59d7"),i("3e22"),i("3fab"),i("89cf"),{props:["datasource","status","getAllLoaded","orderType","detailUrl"],data:function(){return{regional:"请选择快递公司",dateshow_1:!1,columns:[],isShow:!1,toi:this.fun.getKeyByI(),checkList:[],loading:!1,allLoaded:!1,goload:!0,qrcode:"",showQrcode:!1,dialogVisible:!1,expressName:"",numberName:"",addresseeInfo:"",options:[],orderPayId:"",showWriteOff:!1,writeOffQR:"",isShowDri:!1,driverDialogVisible:!1,driverId:"",driverOptions:[],driverColumns:[],driverRegional:"请选择配送司机",driverDateshow_1:!1,driverApi:"",driverNumber:"",activeName:"first"}},mounted:function(){},methods:{setCheckList:function(){this.checkList=[]},operation:function(e,t){8==e.value?this.checklogistics(t,e):12==e.value?this.deleteOrder(t,e):10==e.value?this.comment(t,e):1==e.value?this.toPay(t,e):13==e.value?this.toRefund(t,e):9==e.value?this.cancleOrder(t,e):5==e.value?this.confirmOrder(t,e):99==e.value?this.confirmOrder_dragon(t,e):18==e.value?this.toRefundDetail(t,e):"cashierPay"==e.value?this.cashPayOrder(t,e):"verification_code"==e.value?this.verification(t,e):20==e.value?this.verificationCash(t,e):21==e.value?this.$router.push(this.fun.getUrl("OrderRecord",{order_id:t.id})):22==e.value?(this.orderPayId=t.id,this.payMoney(t,e)):23==e.value?(this.orderPayId=t.id,3==t.dispatch_type_id?this.sendGoogs():(this.getCompany(),this.dialogVisible=!0),this.expressName="",this.numberName="","1"!=JSON.parse(window.localStorage.getItem("globalParameter")).delivery_driver_open||"supplier"!=this.orderType&&"store"!=this.orderType||(this.isShowDri=!0,this.driverId="",this.driverNumber="",this.driverRegional="请选择配送司机",this.driverApi=e.api,this.supplierDriverSend())):24==e.value?(this.orderPayId=t.id,this.deliverGoodsCancel(t,e)):25==e.value?(this.orderPayId=t.id,this.collectGoods(t,e)):26==e.value?(this.orderPayId=t.id,this.closeOrderPay(t,e)):27==e.value?(this.orderPayId=t.id,this.evaluate(t,e)):28==e.value?this.truckList(e,t):41==e.value?window.location.href=e.api:50==e.value?this.checkInvoice(e,t):11==e.value?this.confirmHotelOrder(e.api,t.id):"coupon"==e.value?this.$router.push(this.fun.getUrl("couponShare",{order_id:t.id})):"check_out"===e.value?this.sureOut(t,e):"lease_toy_refund"==e.value||"package_deliver_clerk"==e.value?this.getReturn(e,t):"lease_toy_apply_adopt"==e.value?this.$router.push(this.fun.getUrl("OrderReturn",{id:t.id})):"lease_toy_return_info"==e.value?this.$router.push(this.fun.getUrl("orderruturninfo",{id:t.id})):144==e.value?this.publicMessage(t,e,"接单"):145==e.value?this.publicMessage(t,e,"放弃接单"):this.publicMessage(t,e,"操作")},publicMessage:function(e,t,i){var a=this,o=i||"";this.orderPayId=e.id,t.api&&this.$dialog.confirm({message:"确定".concat(o,"?")}).then((function(){var e=a,i={order_id:e.orderPayId},r=t.api||"";$http.get(r,i).then((function(t){1==t.result?(e.$emit("ConfrimOrderNotification","pay"),e.$notify({background:"#f0f9eb",message:"".concat(o,"成功"),color:"#67c23a"})):e.$dialog.alert({message:t.msg})}),(function(e){}))})).catch((function(){}))},supplierDriverSend:function(){var e=this,t="plugin.delivery-driver.home.delivery-info.get-driver-list";$http.get(t,{}).then((function(t){if(1==t.result){e.driverOptions=[],e.driverOptions=t.data;var i=e.driverOptions.map((function(e){return e.driver_name+"("+e.user_phone+")"}));e.driverColumns=i}else e.$dialog.alert({message:t.msg})}),(function(e){}))},driverSeleRegional:function(){this.driverDateshow_1=!0},onDriverChange:function(e,t,i){},onDriverCancelbtn:function(e,t){this.driverDateshow_1=!1},onDriverConfirmbtn:function(e,t){var i=this.driverOptions;this.driverDateshow_1=!1,this.driverRegional=e,this.driverNumber=i[t].license_number,this.driverId=i[t].id},driverSendCancel:function(){this.driverDialogVisible=!1},checkInvoice:function(e,t){this.$router.push(this.fun.getUrl("Invoice",{order_id:t.id}))},truckList:function(e,t){this.$router.push(this.fun.getUrl("TruckList",{order_id:t.id}))},evaluate:function(e,t){this.$router.push(this.fun.getUrl("DeliveryEvaluate",{order_id:e.id,api:t.api,name:"distributor"}))},getReturn:function(e,t){var i=this;$http.get(e.api,{order_id:t.id}).then((function(e){1==e.result?(i.$dialog.alert({message:e.msg}),i.$emit("ConfrimOrderNotification")):i.$dialog.alert({message:e.msg})}))},payMoney:function(e,t){var i=this;this.$dialog.confirm({message:"确认支付该订单?"}).then((function(){var e=i,a={order_id:e.orderPayId},o="";"supplier"==i.orderType||"DeliveryS"==i.orderType||"serviceReplenish"==i.orderType?o=t.api:(o="plugin.store-cashier.frontend.store.center.order-operation.pay",a={order_id:e.orderPayId}),$http.get(o,a).then((function(t){1==t.result?(e.$emit("ConfrimOrderNotification","pay"),e.$notify({background:"#f0f9eb",message:"支付成功",color:"#67c23a"})):e.$dialog.alert({message:t.msg})}),(function(e){}))})).catch((function(){}))},orderReceiving:function(e,t){var i=this;this.$dialog.confirm({message:"确定接收此单配送任务?"}).then((function(){var e=i,t={order_id:e.orderPayId},a="plugin.delivery-station.frontend.order.operation.accept-order";$http.get(a,t).then((function(t){1==t.result?(e.$emit("ConfrimOrderNotification","pay"),e.$notify({background:"#f0f9eb",message:"接单成功",color:"#67c23a"})):e.$dialog.alert({message:t.msg})}),(function(e){}))})).catch((function(){}))},confirmDelivery:function(e,t){var i=this;this.$dialog.confirm({message:"确定配送此单?"}).then((function(){var e=i,t={order_id:e.orderPayId},a="plugin.delivery-station.frontend.order.operation.send";$http.get(a,t).then((function(t){1==t.result?(e.$emit("ConfrimOrderNotification","pay"),e.$notify({background:"#f0f9eb",message:"配送成功",color:"#67c23a"})):e.$dialog.alert({message:t.msg})}),(function(e){}))})).catch((function(){}))},cancelDelivery:function(){var e=this;this.$dialog.confirm({message:"确定取消配送此单?"}).then((function(){var t=e,i={order_id:t.orderPayId},a="plugin.delivery-station.frontend.order.operation.cancel-send";$http.get(a,i).then((function(e){1==e.result?(t.$emit("ConfrimOrderNotification","pay"),t.$notify({background:"#f0f9eb",message:"取消配送成功",color:"#67c23a"})):t.$dialog.alert({message:e.msg})}),(function(e){}))})).catch((function(){}))},closeOrderPay:function(e,t){var i=this;this.$dialog.confirm({message:"确定关闭订单?"}).then((function(){var e=i,a={order_id:e.orderPayId},o="";o="supplier"==i.orderType?t.api:"DeliveryS"==i.orderType?"plugin.delivery-station.frontend.order.operation.close":"serviceReplenish"==i.orderType?t.api:"plugin.store-cashier.frontend.store.center.order-operation.close",$http.get(o,a).then((function(t){1==t.result?(e.$emit("ConfrimOrderNotification","pay"),e.$notify({background:"#f0f9eb",message:"关闭订单成功",color:"#67c23a"})):e.$dialog.alert({message:t.msg})}),(function(e){}))})).catch((function(){}))},deliverGoodsCancel:function(e,t){var i=this;this.$dialog.confirm({message:"确定取消发货?"}).then((function(){var e=i,a={order_id:e.orderPayId},o="";o="supplier"==i.orderType||"serviceReplenish"==i.orderType?t.api:"plugin.store-cashier.frontend.store.center.order-operation.cancel-send",$http.get(o,a).then((function(t){1==t.result?(e.$emit("ConfrimOrderNotification","pay"),e.$notify({background:"#f0f9eb",message:"取消发货成功",color:"#67c23a"})):e.$dialog.alert({message:t.msg})}),(function(e){}))})).catch((function(){}))},sureOut:function(e,t){var i=this;this.$dialog.confirm({message:"确认退房?"}).then((function(){var a=i,o={order_id:e.id};$http.get(t.api,o).then((function(e){1===e.result?(a.$emit("ConfrimOrderNotification","pay"),a.$notify({background:"#f0f9eb",message:"退房成功",color:"#67c23a"})):a.$dialog.alert({message:e.msg})}),(function(e){}))})).catch((function(){}))},collectGoods:function(e,t){var i=this;this.$dialog.confirm({message:"确认收货?"}).then((function(){var e=i,a={order_id:e.orderPayId},o="";o="supplier"==i.orderType?t.api:"DeliveryS"==i.orderType?"plugin.delivery-station.frontend.order.operation.receive":"serviceReplenish"==i.orderType?t.api:"plugin.store-cashier.frontend.store.center.order-operation.receive",$http.get(o,a).then((function(t){1==t.result?(e.$emit("ConfrimOrderNotification","pay"),e.$notify({background:"#f0f9eb",message:"收货成功",color:"#67c23a"})):e.$dialog.alert({message:t.msg})}),(function(e){}))})).catch((function(){}))},confirmWriteOff:function(e,t){var i=this,a="";"clerk_code"==t.value?a="plugin.delivery-station.frontend.order.detail.qr-code-url":"package_deliver"==t.value&&(a=t.api),$http.get(a,{order_id:e.id},"正在获取核销码").then((function(e){1==e.result?("clerk_code"==t.value?i.writeOffQR=e.data.qrcode_url:"package_deliver"==t.value&&(i.writeOffQR=e.data.qr_code_url),i.showWriteOff=!0):i.$dialog.alert({message:e.msg})}),(function(e){}))},getCompany:function(){var e=this,t="";t="supplier"==this.orderType?"plugin.supplier.frontend.order.express-company":"serviceReplenish"==this.orderType?"plugin.service-station.frontend.order-list.express":"plugin.store-cashier.frontend.store.center.sendOrder",$http.get(t,{order_id:e.orderPayId}).then((function(t){if(1==t.result){e.options=[],e.options=t.data.express_companies;var i=e.options.map((function(e){return e.name}));e.columns=i,e.addresseeInfo=t.data.address}else e.$dialog.alert({message:t.msg})}),(function(e){}))},selectCompany:function(e){},sendCancel:function(){this.dialogVisible=!1},sendGoogs:function(){var e=this,t={};this.isShowDri&&"second"==e.activeName?(t.order_id=e.orderPayId,t.dispatch_type_id=7,t.driver_id=e.driverId):(t.order_id=e.orderPayId,t.express_code=e.expressName.value,t.express_company_name=e.expressName.name,t.express_sn=e.numberName),"supplier"==this.orderType?$http.post("plugin.supplier.frontend.order.send",t).then((function(t){1==t.result?(e.dialogVisible=!1,e.$emit("ConfrimOrderNotification","pay"),e.$notify({background:"#f0f9eb",message:"发货成功",color:"#67c23a"})):e.$dialog.alert({message:t.msg})}),(function(e){})):"serviceReplenish"==this.orderType?$http.post("plugin.service-station.frontend.order.order-operation.send",t).then((function(t){1==t.result?(e.dialogVisible=!1,e.$emit("ConfrimOrderNotification","pay"),e.$notify({background:"#f0f9eb",message:"发货成功",color:"#67c23a"})):e.$dialog.alert({message:t.msg})}),(function(e){})):$http.get("plugin.store-cashier.frontend.store.center.order-operation.send",t).then((function(t){1==t.result?(e.dialogVisible=!1,e.$emit("ConfrimOrderNotification","pay"),e.$notify({background:"#f0f9eb",message:"发货成功",color:"#67c23a"})):e.$dialog.alert({message:t.msg})}),(function(e){}))},multiplePayChange:function(e){this.$emit("MultiplePayNotification",this.checkList)},checklogistics:function(e,t){7==e.dispatch_type_id&&"1"==JSON.parse(window.localStorage.getItem("globalParameter")).delivery_driver_open?this.$router.push(this.fun.getUrl("track",{id:e.id})):this.$router.push(this.fun.getUrl("logistics",{id:e.id,fromGrab:1,api:t.api}))},cashPayOrder:function(e,t){var i=this;this.$dialog.confirm({message:"确定此订单已付款?"}).then((function(){i.sentRequest(t.api,{order_id:e.id,order_pay_id:i.order_pay_id,i:i.fun.getKeyByI(),type:i.fun.getTyep()},!1)})).catch((function(){}))},verification:function(e,t){this.$router.push(this.fun.getUrl("orderVerification",{order_id:e.id,api:t.api}))},verificationCash:function(e,t){var i=this;this.$dialog.confirm({message:"确定此订单已付款?"}).then((function(){i.sentRequest(t.api,{order_id:e.id,order_pay_id:i.order_pay_id,i:i.fun.getKeyByI(),type:i.fun.getTyep()},!1)})).catch((function(){}))},cancleOrder:function(e,t){var i=this;this.$dialog.confirm({message:"确定取消此订单?"}).then((function(){i.datasource.splice(i.datasource.indexOf(e),1),i.sentRequest(t.api,{order_id:e.id},!1)})).catch((function(){}))},deleteOrder:function(e,t){var i=this;this.$dialog.confirm({message:"确定删除此订单?"}).then((function(){i.datasource.splice(i.datasource.indexOf(e),1),i.sentRequest(t.api,{order_id:e.id},!1)})).catch((function(){}))},toPay:function(e,t){this.$router.push(this.fun.getUrl("orderpay",{status:2,order_ids:e.id}))},comment:function(e,t){this.$router.push(this.fun.getUrl("comment",{order:e}))},confirmOrder:function(e,t){var i=this;this.$dialog.confirm({message:t.name}).then((function(){var a=i;$http.get(t.api,{order_id:e.id},"操作中...").then((function(t){1==t.result?a.$emit("ConfrimOrderNotification",e):a.$dialog.alert({message:t.msg})}),(function(e){}))})).catch((function(){}))},dragonPay:function(e){var t=e.form_data,i=document.createElement("form");i.method="post",i.setAttribute("action",e.action_url);for(var a=["INFO","BODY","SIGN","CONTENTTYPE"],o=0,r=a;o<r.length;o++){var n=r[o],s=document.createElement("input");s.setAttribute("name",n),s.setAttribute("value",t[n]),i.appendChild(s)}return document.body.appendChild(i),i.style.display="none",i.submit(),i},confirmOrder_dragon:function(e,t){var i=this;this.$dialog.confirm({message:t.name}).then((function(){var a=i;$http.get(t.api,{order_id:e.id},"操作中...").then((function(t){1==t.result?(a.dragonPay(t.data),a.$emit("ConfrimOrderNotification",e)):a.$dialog.alert({message:t.msg})}),(function(e){}))})).catch((function(){}))},reCommend:function(e,t){},toRefundDetail:function(e,t){this.$router.push(this.fun.getUrl("aftersales",{refund_id:e.refund_id}))},toRefund:function(e,t){this.$router.push(this.fun.getUrl("refund",{order_id:e.id,order_type:1}))},sentRequest:function(e,t,i){var a=this;$http.get(e,t,"").then((function(e){1==e.result?(a.$dialog.alert({message:e.msg}),window.history.length<=1?a.$router.push(a.fun.getUrl("home",{})):a.$router.go(-1)):a.$dialog.alert({message:e.msg})}),(function(e){}))},sheetAction:function(){},loadTop:function(){alert("刷新"),this.$refs.loadmore.onTopLoaded()},loadBottom:function(){this.$refs.loadmore.onBottomLoaded(),this.getAllLoaded?this.allLoaded=!1:this.allLoaded=!0},seleRegional:function(){this.dateshow_1=!0},onChange:function(e,t,i){},onCancelbtn:function(e,t){this.dateshow_1=!1},onConfirmbtn:function(e,t){this.dateshow_1=!1,this.regional=e;var i=this.options;this.expressName=i[t]},stopmove:function(e){e.preventDefault(),e.stopPropagation()},stoppao:function(e){e.stopPropagation()},getDays:function(e,t){var i,a,o,r=e.split(" "),n=t.split(" "),s="-";i=r[0].split(s),a=n[0].split(s);var d=new Date(i[0],i[1]-1,i[2]),l=new Date(a[0],a[1]-1,a[2]);return o=parseInt(Math.abs(d-l)/1e3/60/60/24),o},confirmHotelOrder:function(e,t){var i=this;$http.get(e,{order_id:t},"处理中...").then((function(e){1==e.result?(i.$dialog.alert({message:e.msg}),i.reload()):i.$dialog.alert({message:e.msg})})).catch((function(e){}))},channelReplenishment:function(e){this.$router.push(this.fun.getUrl("bookingChange",{order_id:e}))}},inject:["reload"]}),l=d,c=(i("be58"),i("cba8")),h=Object(c["a"])(l,n,s,!1,null,"14f5a022",null),p=h.exports,f=document.documentElement,u=document.body,g={data:function(){return{titleText:"我的订单",selected:0,last_status:0,dataList:{orderList:[],waitPayList:[],waitSendList:[],waitReceiveList:[],waitCompleteList:[],waitRefund:[],alreadyComplete:[]},action:"",order_ids:[],checkList:[],isMultiplePay:!1,detailUrl:"orderdetail",orderType:"shop",NavList:[],overSelected:[],page0:1,total_page0:0,isLoadMore0:!0,page1:1,total_page1:0,isLoadMore1:!0,page2:1,total_page2:0,isLoadMore2:!0,page3:1,total_page3:0,isLoadMore3:!0,page4:1,total_page4:0,isLoadMore4:!0,page5:1,total_page5:0,isLoadMore5:!0,page6:1,total_page6:0,isLoadMore6:!0,isLoading:!1}},methods:{swichTabTItem:function(){window.scrollTo(0,0),this.getOrderList(this.selected)},initData:function(){this.overSelected=[],this.page0=1,this.total_page0=0,this.isLoadMore0=!0,this.page1=1,this.total_page1=0,this.isLoadMore1=!0,this.page2=1,this.total_page2=0,this.isLoadMore3=!0,this.page3=1,this.total_page3=0,this.isLoadMore3=!0,this.page4=1,this.total_page4=0,this.isLoadMore4=!0,this.page5=1,this.total_page5=0,this.isLoadMore5=!0,this.page6=1,this.total_page6=0,this.isLoadMore6=!0,this.dataList={orderList:[],waitPayList:[],waitSendList:[],waitReceiveList:[],waitCompleteList:[],waitRefund:[],alreadyComplete:[]}},loadTop:function(){this.initData(),this.getOrderList(this.selected)},getOrderList:function(e,t){this.$route.params.orderType&&"myDeliver"==this.$route.params.orderType?(this.orderType="myDeliver",this.detailUrl="DisOrderDetail"):this.$route.params.orderType&&"dismyReplenishment"==this.$route.params.orderType&&(this.orderType="dismyReplenishment",this.detailUrl="DisOrderDetail"),0==e?(this.action="plugin.channel.frontend.order-list.index","myDeliver"==this.orderType?this.action="plugin.channel.frontend.freedom-order-list.index":"dismyReplenishment"==this.orderType&&(this.action="plugin.channel.frontend.replenishment-order-list.index")):1==e?(this.action="plugin.channel.frontend.order-list.waitReceiving","myDeliver"==this.orderType?this.action="plugin.channel.frontend.freedom-order-list.waitSend":"dismyReplenishment"==this.orderType&&(this.action="plugin.channel.frontend.replenishment-order-list.waitPay")):2==e?(this.action="plugin.channel.frontend.order-list.waitReplenishment","myDeliver"==this.orderType?this.action="plugin.channel.frontend.freedom-order-list.waitReceive":"dismyReplenishment"==this.orderType&&(this.action="plugin.channel.frontend.replenishment-order-list.waitSend")):3==e?(this.action="plugin.channel.frontend.order-list.waitSend","myDeliver"==this.orderType?this.action="plugin.channel.frontend.freedom-order-list.Completed":"dismyReplenishment"==this.orderType&&(this.action="plugin.channel.frontend.replenishment-order-list.waitReceive")):4==e?(this.action="plugin.channel.frontend.order-list.waitReceive","dismyReplenishment"==this.orderType&&(this.action="plugin.channel.frontend.replenishment-order-list.Completed")):5==e&&(this.action="plugin.channel.frontend.order-list.Completed"),this.overSelected.indexOf(this.selected)<0&&(this.overSelected.push(this.selected),this.getNetData(this.action,e)),"refresh"===t&&this.getNetData(this.action,e)},cofirmOrderAction:function(e){0==this.selected||"pay"==e?this.getOrderList(this.selected,"refresh"):this.dataList.waitReceiveList.splice(this.dataList.waitReceiveList.indexOf(e),1)},toMultiplePay:function(){this.$router.push(this.fun.getUrl("orderpay",{status:2,order_ids:encodeURI(this.order_ids)}));var e=this.$refs.mychild;e.setCheckList(),this.isMultiplePay=!1},multiplePayAction:function(e){this.order_ids=e,e.length>0?this.isMultiplePay=!0:this.isMultiplePay=!1},getNetData:function(e,t){var i=this;$http.get(e,{page:1},"正在获取订单").then((function(e){if(1===e.result){i["isLoadMore".concat(t)]=!0;var a=e.data;i["total_page".concat(t)]=e.data.last_page,i["total_page".concat(t)]||(i["total_page".concat(t)]=0),i.selectIndexData(t,a.data),i.isLoading=!1}}),(function(e){}))},selectIndexData:function(e,t){var i=this;0==e?i.dataList.orderList=t:1==e?i.dataList.waitPayList=t:2==e?i.dataList.waitSendList=t:3==e?i.dataList.waitReceiveList=t:4==e?i.dataList.waitCompleteList=t:5==e?i.dataList.waitRefund=t:6==e&&(i.dataList.alreadyComplete=t)},getMoreData:function(){var e=this;e["isLoadMore".concat(e.selected)]=!1,e["page".concat(e.selected)]>=e["total_page".concat(e.selected)]||(e["page".concat(e.selected)]=e["page".concat(e.selected)]+1,$http.get(this.action,{page:e["page".concat(e.selected)]},"正在获取更多订单").then((function(t){if(e["isLoadMore".concat(e.selected)]=!0,1!=t.result)return e["page".concat(e.selected)]=e["page".concat(e.selected)]-1,void(e["isLoadMore".concat(e.selected)]=!1);var i=t.data;0==e.selected?e.dataList.orderList=e.dataList.orderList.concat(i.data):1==e.selected?e.dataList.waitPayList=e.dataList.waitPayList.concat(i.data):2==e.selected?e.dataList.waitSendList=e.dataList.waitSendList.concat(i.data):3==e.selected?e.dataList.waitReceiveList=e.dataList.waitReceiveList.concat(i.data):4==e.selected?e.dataList.waitCompleteList=e.dataList.waitCompleteList.concat(i.data):5==e.selected?e.dataList.waitRefund=e.dataList.waitRefund.concat(i.data):6==e.selected&&(e.dataList.alreadyComplete=e.dataList.alreadyComplete.concat(i.data))}),(function(e){})))},getScrollTop:function(){var e=0;return f&&f.scrollTop?e=f.scrollTop:u&&(e=u.scrollTop),e},getClientHeight:function(){var e=0;return e=u.clientHeight&&f.clientHeight?Math.min(u.clientHeight,f.clientHeight):Math.max(u.clientHeight,f.clientHeight),e},getScrollHeight:function(){return Math.max(u.scrollHeight,f.scrollHeight)},handleScroll:function(){this.getScrollTop()+this.getClientHeight()+105>this.getScrollHeight()&&this["isLoadMore".concat(this.selected)]&&this.getMoreData()}},computed:{setNavList:function(){return"myDeliver"==this.orderType?this.NavList=["全部","待发货","待收货","已完成"]:"dismyReplenishment"==this.orderType?this.NavList=["全部","待付款","待发货","待收货","已完成"]:this.NavList=["全部","待接单","待补货","待发货","待收货","已完成"],this.NavList}},created:function(){this.last_status=this.selected},mounted:function(){},updated:function(){},deactivated:function(){window.removeEventListener("scroll",this.handleScroll)},activated:function(){window.addEventListener("scroll",this.handleScroll),this.$route.params.orderType&&"myDeliver"==this.$route.params.orderType?(this.titleText="我的发货",this.orderType="myDeliver",this.detailUrl="DisOrderDetail"):this.$route.params.orderType&&"dismyReplenishment"==this.$route.params.orderType?(this.titleText="我的补货",this.orderType="dismyReplenishment",this.detailUrl="DisOrderDetail"):(this.titleText="客户订单",this.orderType="shop",this.detailUrl="DisOrderDetail"),this.initData(),window.scrollTo(0,0),this.selected=this.$route.params.status,this.getOrderList(this.selected)},components:{cTitle:r["a"],cOrderList:p}},m=g,v=m,b=(i("bd78"),Object(c["a"])(v,a,o,!1,null,"246d1e1c",null));t["default"]=b.exports},bc19:function(e,t,i){var a=i("078a");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var o=i("5925").default;o("7eec43a3",a,!0,{sourceMap:!1,shadowMode:!1})},bd78:function(e,t,i){"use strict";i("2ea27")},be58:function(e,t,i){"use strict";i("bc19")}}]);
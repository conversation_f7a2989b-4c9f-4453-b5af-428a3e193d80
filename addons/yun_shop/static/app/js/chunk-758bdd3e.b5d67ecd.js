(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-758bdd3e","chunk-2d0b235b"],{"22af":function(t,o){t.exports="data:image/png;base64,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"},"24ea":function(t,o,e){var s=e("24fb");o=s(!1),o.push([t.i,".activity-goods-details[data-v-494788ee]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;background:#fff;margin:0 .5rem .75rem;padding:.75rem;text-align:left;border-radius:4px;-webkit-box-shadow:0 0 .56rem 0 hsla(0,0%,66.3%,.24);box-shadow:0 0 .56rem 0 hsla(0,0%,66.3%,.24)}.activity-goods-cover[data-v-494788ee]{-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.activity-goods-info[data-v-494788ee]{width:100%;margin-left:.63rem}.activity-goods-title[data-v-494788ee]{font-size:14px;height:2.5rem;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;color:#333;font-weight:700}.activity-goods-sku[data-v-494788ee]{margin-top:4px;line-height:.75rem;color:#f14e4e;font-size:12px}.activity-goods-sku i[data-v-494788ee]{padding-right:4px}.activity-goods-price[data-v-494788ee]{margin-top:8px;line-height:14px;font-size:14px;color:#202020;font-weight:700}.activity-goods-footer[data-v-494788ee]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:end;-webkit-align-items:flex-end;-ms-flex-align:end;align-items:flex-end}.activity-goods-deposit[data-v-494788ee]{font-size:.88rem;color:#f14e4e;font-weight:700}",""]),t.exports=o},"36f6":function(t,o,e){var s=e("24fb");o=s(!1),o.push([t.i,".group-box .goods_box[data-v-3ca2deca]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;padding:.625rem;border-bottom:.0625rem solid #ebebeb}.group-box .goods_box .goods_img[data-v-3ca2deca]{width:7.5rem;height:7.5rem;background:#f2f2f2;overflow:hidden;border-radius:.25rem;margin-right:.625rem}.group-box .goods_box .goods_img img[data-v-3ca2deca]{width:100%}.group-box .goods_box .goods_info[data-v-3ca2deca]{width:14.0625rem;text-align:left}.group-box .goods_box .goods_info .name[data-v-3ca2deca]{font-size:14px;line-height:1.25rem;height:2.5rem;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}.group-box .goods_box .goods_info .price[data-v-3ca2deca]{font-size:16px;margin-top:.375rem;color:#f15353}.group-box .goods_box .goods_info .price span[data-v-3ca2deca]{font-size:12px}.group-box .goods_box .goods_info .price span[data-v-3ca2deca]:last-child{color:#8c8c8c;margin-left:.625rem}.group-box .goods_box .goods_info .number[data-v-3ca2deca]{-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;margin-top:1.625rem}.group-box .goods_box .goods_info .number[data-v-3ca2deca],.group-box .goods_box .goods_info .number .left[data-v-3ca2deca]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.group-box .goods_box .goods_info .number .left .iconfont[data-v-3ca2deca]{font-size:1.5rem;color:#f15353;margin-right:.375rem}.group-box .goods_box .goods_info .number .left span[data-v-3ca2deca]{color:#f15353;font-size:14px;border-left:.0625rem solid #ebebeb;padding-left:.375rem}.group-box .goods_box .goods_info .number .go_group[data-v-3ca2deca]{width:5rem;height:1.75rem;border-radius:.25rem;background:#f15353;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.group-box .goods_box .goods_info .number .go_group span[data-v-3ca2deca]{color:#fff;font-size:14px}.group-box .goods_box .goods_info .number .go_group .fa[data-v-3ca2deca]{color:#fff;font-size:1rem;margin-left:.625rem}",""]),t.exports=o},4379:function(t,o,e){t.exports=e.p+"static/app/img/video-loading.c4a4193d.gif"},5794:function(t,o){t.exports="data:image/png;base64,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"},"6f8a":function(t,o,e){var s=e("24ea");s.__esModule&&(s=s.default),"string"===typeof s&&(s=[[t.i,s,""]]),s.locals&&(t.exports=s.locals);var i=e("499e").default;i("890450a8",s,!0,{sourceMap:!1,shadowMode:!1})},8798:function(t,o,e){t.exports=e.p+"static/app/img/no-more-product.f4632785.png"},a604:function(t,o,e){"use strict";var s=function(){var t=this,o=t.$createElement,e=t._self._c||o;return e("div",{staticClass:"group-box"},t._l(t.list,(function(o,s){return e("div",{key:s,staticClass:"goods_box"},[e("div",{staticClass:"goods_img"},[e("img",{attrs:{src:o.has_one_goods.thumb}})]),e("ul",{staticClass:"goods_info"},[e("li",{staticClass:"name",staticStyle:{"-webkit-box-orient":"vertical"}},[t._v(" "+t._s(o.title)+" ")]),e("li",{staticClass:"price"},[e("span",[t._v(t._s(t.$i18n.t("money")))]),t._v(t._s(o.has_many_option_levels[0].group_price)+" "),e("span",[t._v(t._s(t.$i18n.t("money"))+t._s(o.has_many_option_levels[0].has_one_fight_groups_option.option_price))])]),e("li",{staticClass:"number"},[e("div",{staticClass:"left"},[e("i",{staticClass:"iconfont icon-group_list"}),e("span",[t._v(t._s(o.has_many_option_levels[0].member_num)+"人")])]),e("div",{staticClass:"go_group",on:{click:function(e){return t.goGroup(o.id)}}},[e("span",[t._v("去开团")]),e("i",{staticClass:"fa fa-angle-right"})])])])])})),0)},i=[],a=(e("a9e3"),{props:{list:{type:Array,default:function(){return[]}},store_id:{type:[String,Number]}},data:function(){return{}},components:{},computed:{},methods:{goGroup:function(t){this.$router.push(this.fun.getUrl("GroupGoods",{id:t,store_id:this.store_id}))}}}),n=a,r=(e("f343"),e("2877")),d=Object(r["a"])(n,s,i,!1,null,"3ca2deca",null);o["a"]=d.exports},ac0d:function(t,o,e){"use strict";e.d(o,"a",(function(){return a}));var s=document.documentElement,i=document.body,a={data:function(){return{}},activated:function(){window.addEventListener("scroll",this.handleScroll)},deactivated:function(){window.removeEventListener("scroll",this.handleScroll)},methods:{getScrollTop:function(){var t=0;return s&&s.scrollTop?t=s.scrollTop:i&&(t=i.scrollTop),t},getClientHeight:function(){var t=0;return t=i.clientHeight&&s.clientHeight?Math.min(i.clientHeight,s.clientHeight):Math.max(i.clientHeight,s.clientHeight),t},getScrollHeight:function(){return Math.max(i.scrollHeight,s.scrollHeight)},handleScroll:function(){this.otherScroll();var t=document.documentElement.scrollTop||document.body.scrollTop;this.btnFlag=t>60,this.getScrollTop()+this.getClientHeight()+105>this.getScrollHeight()?(this.isLoadMore&&this.getMoreData(),this.isBottom=!0):this.isBottom=!1},otherScroll:function(){}}}},be11:function(t,o,e){"use strict";e("6f8a")},ca3f:function(t,o,e){var s=e("36f6");s.__esModule&&(s=s.default),"string"===typeof s&&(s=[[t.i,s,""]]),s.locals&&(t.exports=s.locals);var i=e("499e").default;i("4e8ed161",s,!0,{sourceMap:!1,shadowMode:!1})},e4de:function(t,o,e){"use strict";var s=function(){var t=this,o=t.$createElement,e=t._self._c||o;return e("div",{attrs:{id:"deposit-ladder-cell"}},[e("div",{staticClass:"activity-goods-details",on:{click:t.getGoodDetail}},[e("van-image",{staticClass:"activity-goods-cover",attrs:{width:"6.25rem",height:"6.25rem",src:t.thumb||"",radius:"0.31rem"}}),e("div",{staticClass:"activity-goods-info"},[e("div",{staticClass:"activity-goods-title"},[t._v(t._s(t.title))]),e("div",{staticClass:"activity-goods-sku"},[e("i",{staticClass:"iconfont icon-group_list"}),t._v(t._s(t.count?t.count+"人":"--"))]),e("div",{staticClass:"activity-goods-price"},[t._v("￥"+t._s(t.price))]),e("div",{staticClass:"activity-goods-footer",on:{click:function(t){t.stopPropagation()}}},[e("span",{staticClass:"activity-goods-deposit"},[t._v("定金：￥"+t._s(t.deposit))]),e("van-button",{attrs:{type:"primary",size:"mini",color:"#f14e4e"},on:{click:function(o){return o.stopPropagation(),t.toDetail.apply(null,arguments)}}},[t._v("去下定"),e("i",{staticClass:"fa fa-angle-right",staticStyle:{"margin-left":"6px"}})])],1)])],1)])},i=[],a=(e("4056"),e("44bf")),n=(e("a9e3"),{props:{thumb:{type:String,default:null},title:{type:String,default:"暂无信息"},option:{type:String,default:null},price:{type:String,default:""},deposit:{type:String,default:null},store_id:{type:[String,Number],default:null},id:{type:[String,Number],default:null},count:{type:[String,Number],default:null}},data:function(){return{}},activated:function(){},components:{VanImage:a["a"]},computed:{},mounted:function(){},methods:{toDetail:function(){var t={id:this.id};this.store_id&&(t.store_id=this.store_id),this.id&&this.$router.push(this.fun.getUrl("depositGroupActivity",{},t))}}}),r=n,d=(e("be11"),e("2877")),c=Object(d["a"])(r,s,i,!1,null,"494788ee",null);o["a"]=c.exports},ef1d:function(t,o,e){"use strict";e("e17f");var s=e("2241"),i=(e("e7e5"),e("d399")),a=(e("b0c0"),e("d81d"),e("99af"),e("a9e3"),e("d3b7"),e("159b"),e("ac1f"),e("5319"),e("b680"),e("4e82"),e("1276"),e("a434"),e("4b18")),n=e("1fba"),r=e("df81"),d=e("ac0d"),c=e("d938"),l=e("9958"),h=e("a604"),g=e("fb0a"),u=e("e4de"),f=[],p="",m=1,_=0,b=window.screen.height;o["a"]={mixins:[d["a"]],data:function(){return{showLength:0,isLoadMore:!0,cisLoadMore:!0,get_coupon:[],micro_communities:[],get_recommend_goods:[],store_member:[],store_room:[],nearbyClassification:[],delivery_note:"",delivery_area:[[116.403322,39.920255]],locations:[116.403322,39.920255],is_open_store_delivery:!1,checked:!0,value:3,follow_mode:{},isfollow:!1,store_id:"",order_id:"",show8:!1,new_comment:[],store:{comment:{average_score:0,comment_total:0},goods_total:0,order_count:0,business_hours:"9:00-22:00",store_address:"",store_mobile:"",operating_state:0},activeName:[],secondaryId:"",category_id:"",category_loading:!1,category:[],secondCategory:[],goodsList:[],goodsCount:0,currentCategory:"",toolbar:!1,showGoods:!0,showStoreInfo:!1,showCart:!1,hasCart:!1,carts:[],cartsTotal:0,cartsNum:0,goodsCarts:[],specid:"",cartIDs:[],goodsCartsTotal:[],pageCount:1,catesId:"",loading:!1,show1:!1,show2:!1,goodsInfo:{},secondTitle:[],goods:[],listHeight:[],listCategoryHeight:[],scrollY:0,scrollMargin:0,Index:0,showDis:!0,tabIndex:0,get_store_full_package:"",get_store_full_reduction:[],goodsCartsOld:"",item:"",tab:["商品","评价","商家"],info:"",amout:!1,loadingPopup:!0,goodPage:1,goodIsLoadMore:!0,goodTotal_page:0,delivery_amount:0,isCreate:!0,tabIndex2:0,tabList:[],menuList:[],tabName:"店铺详情",isLoading:!1}},created:function(){1!=this.$route.params.fromHome&&(this.show=!1,this.showCart=!1,this.fun.setWXTitle(this.$store.state.temp.item.janst||"商家"),this.store_id=this.$route.params.store_id,this.initData(),this.getStoresDetails(),this.isCreate=!0),1==this.$route.query.fromOrder&&(this.order_id=this.$route.params.store_id,this.store_id="")},activated:function(){var t=this;this.show=!1,this.showCart=!1,this.fun.setWXTitle(this.$store.state.temp.item.janst||"商家"),this.store_id=this.$route.params.store_id,1==this.$route.query.fromOrder&&(this.order_id=this.$route.params.store_id,this.store_id=""),1==this.$route.params.fromHome?(this.initData(),this.getStoresDetails()):this.isCreate||(this.updateshoppingcart(),this.$nextTick((function(){t._initScroll()}))),this.isCreate=!1,"comment"===this.$route.params.activeType&&(this.tabIndex=1),window.scrollTo(0,0)},mounted:function(){window.storeV2ScrollTop=0},deactivated:function(){},methods:{toCard:function(t){t&&this.$router.push(this.fun.getUrl("BusinessCard",{},{mark_id:t,mark:"card"}))},onCopy:function(t){Object(i["a"])({message:"复制成功",duration:1e3})},onError:function(t){Object(i["a"])({message:"复制失败",duration:1e3})},toGood:function(t){this.$router.push(this.fun.getUrl("goods",{id:t}))},chooseTab:function(t,o){this.tabName=t.name,this.tabIndex2=o,this.getTabsData()},getTabsData:function(){var t=this;if(this.menuList[this.tabIndex2].url)if(this.fun.isTextEmpty(this.menuList[this.tabIndex2].list)){var o=1,e=[];this.isLoading=!0,$http.post(this.menuList[this.tabIndex2].url,{page:1,store_id:this.$route.params.store_id},"").then((function(s){var i;(t.isLoading=!1,1===s.result)?("门店微贴"==t.tabName?(o=s.data.last_page,e=t.setCommunities(s.data.data)):"门店员工"==t.tabName?e=s.data:null!==(i=s.data)&&void 0!==i&&i.data&&(o=s.data.last_page,e=s.data.data),t.isLoadMore=!0,t.menuList[t.tabIndex2].isLoadMore=!0,t.menuList[t.tabIndex2].total_page=o,t.menuList[t.tabIndex2].total_page||(t.menuList[t.tabIndex2].total_page=0),t.$set(t.menuList[t.tabIndex2],"list",e)):t.$dialog.alert({message:s.msg})})).catch((function(t){}))}else this.isLoadMore=this.menuList[this.tabIndex2].isLoadMore},setCommunities:function(t){var o=[];return t.map((function(t){o.push({id:t.id,title:t.title,praise_num:t.praise_num,has_one_stick_user:{avatar:t.user_avatar,nickname:t.user_nickname},has_many_image:[{url:t.image,stick_id:t.id}]})})),o},getMoreData:function(){var t=this,o=this;if(o.menuList[o.tabIndex2].isLoadMore=!1,o.isLoadMore=!1,!(o.menuList[o.tabIndex2].page>=o.menuList[o.tabIndex2].total_page)){var e=[],s={store_id:this.$route.params.store_id};o.menuList[o.tabIndex2].page=o.menuList[o.tabIndex2].page+1,s.page=o.menuList[o.tabIndex2].page,$http.get(o.menuList[o.tabIndex2].url,s,"加载中").then((function(s){var i;(o.isLoadMore=!0,o.menuList[o.tabIndex2].isLoadMore=!0,1===s.result)?("门店微贴"==t.tabName?e=t.setCommunities(s.data.data):"门店员工"==t.tabName?e=s.data:null!==(i=s.data)&&void 0!==i&&i.data&&(e=s.data.data),o.menuList[o.tabIndex2].list=o.menuList[o.tabIndex2].list.concat(e)):(o.menuList[o.tabIndex2].page=o.menuList[o.tabIndex2].page-1,o.menuList[o.tabIndex2].isLoadMore=!1,o.isLoadMore=!1)}),(function(t){}))}},loadMore:function(){this.cisLoadMore=!0},trggleCart:function(){0!==this.carts.length&&(this.showCart=!this.showCart)},changeTab:function(t){var o=this;this.tabIndex=t,this.scrollMargin=0,0==this.tabIndex&&this.$nextTick((function(){o._initScroll()})),document.documentElement.scrollTop=0,this.amout=!1},goback:function(){this.$router.go(-1)},goToAdress:function(){var t=this.fun.bd_decrypt(this.store.lng,this.store.lat);this.fun.goToWXAdress(t,this.store.store_name,"门店地址")},clickCount:function(t){this.goodsCartsOld=t.total,this.changeCount(t)},changeCount:function(t){var o=this;if(t.total<0)return Object(i["a"])("商品数量不能为负数"),void(t.total=o.goodsCartsOld);if(t.total<o.goodsCartsOld){var e=o.goodsCartsOld-t.total;o.updateCart(t.id,-e)}else if(t.total>o.goodsCartsOld){var s=t.total-o.goodsCartsOld;o.updateCart(t.id,s)}else if(t.total=o.goodsCartsOld)return},numberRight:function(t,o){if(0!=t.stock)if(o&&o.total>=t.stock)Object(i["a"])("库存不足！");else{if(t.vip_level_status&&1==t.vip_level_status.status)return Object(i["a"])(t.vip_level_status.tips),!1;if(this.fun.isTextEmpty(o))this.addCart(t);else{var e="goodsInputs".concat(t.id),s=document.getElementById(e);s.blur(),this.goodsCartsOld=o.total,this.goodsCarts[t.id].total=Number(this.goodsCarts[t.id].total)+1,this.addCart(t)}}else Object(i["a"])("库存不足！")},numberLeft:function(t,o){if(this.fun.isTextEmpty(o))this.updateCart(o.id,-1);else{var e="goodsInputs".concat(t.id),s=document.getElementById(e);s.blur(),this.goodsCartsOld=o.total,this.goodsCarts[t.id].total=Number(this.goodsCarts[t.id].total)-1,_=0,this.changeCount(this.goodsCarts[t.id])}},numberRight1:function(t,o){if(this.fun.isTextEmpty(t))this.addCart(t,"cart");else{var e="itemIputs".concat(o),s=document.getElementById(e);s.blur(),this.goodsCartsOld=t.total,this.addCart(t,"cart",o)}},numberLeft1:function(t,o){if(this.fun.isTextEmpty(t))this.updateCart(t.id,-1);else{var e="itemIputs".concat(o),s=document.getElementById(e);s.blur(),this.goodsCartsOld=t.total,this.carts[o].total=Number(t.total)-1,_=0,this.changeCount(this.carts[o])}},getStoresDetails:function(){var t=this,o=this;$http.post("plugin.store-cashier.frontend.store.get-store-info.get-stores-details",{store_id:this.store_id,order_id:this.order_id,url:"android"===this.fun.isIosOrAndroid()?window.location.href:window.initUrl},"...").then((function(e){if(t.loadingPopup=!1,t.$nextTick((function(){t.Height=-(t.$refs.storeInfo_box.clientHeight+t.$refs.storeInfo_box.offsetTop-44)})),"该商家已过期,去看看其他的吧"===e.msg)return t.showDis=!1,void(t.show8=!0);if(1===e.result){if(e.basic_info&&e.basic_info.home&&t.fun.setWXTitle(e.basic_info.home.mailInfo.name||"商家"),t.store=e.data.store_info,t.store_id=t.store.store_id,t.new_comment=e.data.comment.new_comment,t.info=e.data.store_information,t.get_store_full_package=e.data.get_store_full_package,t.get_store_full_reduction=e.data.get_store_full_reduction,1===t.store.operating_state&&(t.show1=!0),t.get_coupon=e.data.get_coupon,t.get_recommend_goods=e.data.get_recommend_goods,t.tabList=[{name:"店铺详情",api:""},{name:"门店员工",api:"plugin.store-cashier.frontend.store.get-store-info.get-store-staff"}],t.tabName="店铺详情",t.tabIndex2=0,1==e.data.is_micro_communities&&t.tabList.push({name:"门店微贴",api:"plugin.store-cashier.frontend.store.get-store-info.get-micro-communities"}),1==e.data.is_room&&t.tabList.push({name:"门店直播",api:"plugin.store-cashier.frontend.store.get-store-info.get-room"}),1==e.data.is_fight_groups&&t.tabList.push({name:"门店拼团",api:"plugin.fight-groups.frontend.store.frontend-fight-groups.index"}),1==e.data.is_store_deposit_ladder&&t.tabList.push({name:"定金阶梯团",api:"plugin.deposit-ladder.frontend.store.index.activities"}),t.menuList=[],t.tabList.forEach((function(o,e){t.menuList.push({isLoadMore:!0,page:1,total_page:0,list:[],url:o.api})})),-1!=t.store.dispatch_type.indexOf(3)&&t.store.store_delivery&&1==t.store.store_delivery.delivery_status){t.is_open_store_delivery=!0,t.delivery_amount=t.store.store_delivery.delivery_amount,t.delivery_note=t.store.store_delivery?t.store.store_delivery.delivery_note:"",t.delivery_note=t.delivery_note.replace(/\n|\r/g,"<br/>");var s=t.store.store_delivery&&t.store.store_delivery.delivery_area?t.store.store_delivery.delivery_area:[],a=t.store.store_delivery?t.store.store_delivery.locations:[],n=[];s.forEach((function(t,o){n.push([Number(t.R),Number(t.Q)])})),t.delivery_area=n,t.locations=[Number(a.longitude).toFixed(6),Number(a.latitude).toFixed(6)]}t.getAllCategory(e.data.first_category),t.getFirstCart(e.data.member_cart),t.fun.wxShare("",{},{title:o.store.store_name,imgUrl:o.store.store_thumb,description:o.store.store_introduce})}else Object(i["a"])({message:e.msg,position:"middle",duration:1500})}),(function(t){}))},_calculateCategoryHeight:function(){var t=this.$refs.foodsWrapper.getElementsByClassName("food-list"),o=0;this.listCategoryHeight.push(o);for(var e=0;e<t.length;e++){var s=t[e];o+=s.clientHeight,this.listCategoryHeight.push(o)}},_calculateHeight:function(){var t=this.$refs.foodsWrapper.getElementsByClassName("food-list-hook"),o=0;this.listHeight.push(o);for(var e=0;e<t.length;e++){var s=t[e];o+=s.clientHeight,this.listHeight.push(o)}},selectCategoryMenu:function(t,o){o._constructed&&(t.childrens[0]?this.selectMenu(t.childrens[0].id,o):Object(i["a"])({message:"该分类暂无商品",duration:1e3}))},selectMenu:function(t,o){var e=this;if(o._constructed){this.meunScroll?this.meunScroll.refresh():this.meunScroll=new n["a"](this.$refs.menuWrapper,{click:!0}),this.secondTitle.forEach((function(o,s){if(o.id===t)return e.Index=s,s}));var s=this.$refs.foodsWrapper.getElementsByClassName("food-list-hook"),i=s[this.Index];this.foodsScroll.scrollToElement(i,100)}},initData:function(){this.showLength=0,this.category_loading=!1,this.loadingPopup=!0,this.tabIndex=0,this.info="",this.new_comment=[],this.category=[],this.goodsList=[],this.currentCategory="",this.goodsCount=0,this.pageCount=1,this.cartsNum=0,this.cartsTotal=0,this.carts=[],this.goodsCarts=[],this.specid="",this.secondTitle=[],this.get_store_full_package="",this.get_store_full_reduction=[],f=[],p="",m=1},searchJump:function(){this.$router.push(this.fun.getUrl("StoreSearch",{store_id:this.store_id,fromHome:1}))},showStore:function(){this.showStoreInfo?(this.showStoreInfo=!1,this.showGoods=!0):(this.showStoreInfo=!0,this.showGoods=!1)},goBuy:function(){this.$router.push(this.fun.getUrl("cashier_pay",{store_id:this.store_id}))},showGoodsInfo:function(){this.showGoods?(this.showStoreInfo=!0,this.showGoods=!1):(this.showStoreInfo=!1,this.showGoods=!0)},_initScroll:function(){var t=this;this.meunScroll?this.meunScroll.refresh():this.meunScroll=new n["a"](this.$refs.menuWrapper,{click:!0}),this.foodsScroll?(this.foodsScroll.refresh(),this.foodsScroll.scrollTo(0,window.storeV2ScrollTop,100)):this.foodsScroll=new n["a"](this.$refs.foodsWrapper,{click:!0,probeType:3}),this.foodsScroll.on("scroll",(function(o){t.scrollY=Math.abs(Math.round(o.y)),t.scrollMargin=o.y,window.storeV2ScrollTop=o.y;var e=document.getElementById("loadMore");(e?e.offsetTop+o.y:o.y)<t.$refs.foodsWrapper.offsetHeight&&t.goodIsLoadMore&&t.getMoreGood()})),this.foodsScroll.on("touchEnd",(function(){var o=document.getElementById("loadMore");if(t.scrollMargin>50&&!t.category_loading){for(var e="",s=0;s<t.category.length;s++)if(t.category_id==t.category[s].id&&t.category[s-1]){e=t.category[s-1].id;break}e&&(t.category_id=e,t.getSecondCategory(t.category_id))}if((o?o.offsetTop+t.scrollMargin:t.scrollMargin)<t.$refs.foodsWrapper.offsetHeight-170&&t.scrollMargin<0&&!t.goodIsLoadMore&&!t.category_loading){for(var i="",a=0;a<t.category.length;a++)if(t.category_id==t.category[a].id&&t.category[a+1]){i=t.category[a+1].id;break}i&&(t.category_id=i,t.getSecondCategory(t.category_id))}}))},getAllCategory:function(t){var o=this;this.category=t.first_category||[],this.category&&this.category.length>0&&(this.category_id=this.category[0].id),this.secondCategory=t.second_category,this.secondCategory.length>0&&this.secondCategory.unshift({name:"全部",id:""}),this.secondaryId="",this.goods=t.goods.data,this.showLength=Number(6-this.goods.length)>0?Number(6-this.goods.length):1,this.goodTotal_page=t.goods.last_page,this.$nextTick((function(){o._initScroll()}))},getSecondCategory:function(t){var o=this;this.category_id=t,this.category_loading=!0,this.foodsScroll&&this.foodsScroll.scrollTo(0,0),$http.post("plugin.store-cashier.frontend.store.get-store-info.get-second-category",{store_id:this.store_id,category_id:t},"...").then((function(t){1===t.result?(o.secondCategory=t.data.second_category,o.secondCategory.unshift({name:"全部",id:""}),o.secondaryId="",o.goods=t.data.goods.data,o.showLength=Number(6-o.goods.length)>0?Number(6-o.goods.length):1,o.goodPage=1,o.goodIsLoadMore=!0,o.goodTotal_page=t.data.goods.last_page,o.$nextTick((function(){o._initScroll()}))):Object(i["a"])({message:t.msg,position:"middle",duration:1500}),setTimeout((function(){o.category_loading=!1}),1500)}),(function(t){}))},getSecondGoods:function(t){var o=this;this.secondaryId=t,$http.post("plugin.store-cashier.frontend.store.get-store-info.get-goods",{store_id:this.store_id,category_id:this.category_id,second_category_id:t||0},"...").then((function(t){1===t.result?(o.goods=t.data.goods.data,o.showLength=Number(6-o.goods.length)>0?Number(6-o.goods.length):1,o.goodPage=1,o.goodIsLoadMore=!0,o.goodTotal_page=t.data.goods.last_page,o.$nextTick((function(){o._initScroll()}))):Object(i["a"])({message:t.msg,position:"middle",duration:1500})}),(function(t){}))},getMoreGood:function(){var t=this;this.goodIsLoadMore=!1,this.goodPage>=this.goodTotal_page||(this.goodPage+=1,$http.post("plugin.store-cashier.frontend.store.get-store-info.get-goods",{store_id:this.store_id,category_id:this.category_id,second_category_id:this.secondaryId||0,page:this.goodPage},"...").then((function(o){1===o.result?(t.goodIsLoadMore=!0,t.goods=t.goods.concat(o.data.goods.data),t.goodPage>=t.goodTotal_page&&(t.goodIsLoadMore=!1),t.$nextTick((function(){t._initScroll()}))):(t.goodPage=t.goodPage-1,t.goodIsLoadMore=!1,Object(i["a"])({message:o.msg,position:"middle",duration:1500}))}),(function(t){})))},getCategory:function(t){var o=this;if(t.list&&(this.category=t.list||[],this.category.length>0)){this.category[0].active=!0,this.category[0].childrens.length>0&&(this.category[0].childrens[0].active=!0,this.currentCategory=this.category[0].childrens[0].name,this.catesId=this.category[0].childrens[0].id);var e=[],s=[];this.category.forEach((function(t,o){t.childrens.length>0?t.childrens&&t.childrens[0].goods.length>=0&&e.push(t.childrens):e.push([])})),e.forEach((function(t){t.length>0&&t.forEach((function(t){s.push({id:t.id,name:t.name})}))})),this.secondTitle=s,this.goods=e,this.showLength=Number(6-this.goods.length)>0?Number(6-this.goods.length):1,this.$nextTick((function(){o._initScroll(),o._calculateCategoryHeight(),o._calculateHeight()}))}},selectCategory:function(t){var o=this;this.category.forEach((function(t){o.$set(t,"active",!1)})),this.$set(t,"active",!0),t.childrens.length>0&&(t.childrens.forEach((function(t){o.$set(t,"active",!1)})),this.$set(t.childrens[0],"active",!0),this.currentCategory=t.childrens[0].name,this.pageCount=1,this.catesId=t.childrens[0].id,this.getGoodsList(t.childrens[0].id),this.goodsList=[])},selectChildCategory:function(t,o){var e=this;t.forEach((function(t){e.$set(t,"active",!1)})),this.$set(o,"active",!0),this.currentCategory=o.name,this.pageCount=1,this.catesId=o.id,this.getGoodsList(o.id),this.goodsList=[]},getGoodsList:function(t){var o=this;$http.get("plugin.store-cashier.frontend.store.goods.get-goods-to-page",{store_id:this.store_id,category_id:t,page:o.pageCount},"加载中").then((function(t){if(1===t.result){if(1==o.pageCount&&(o.goodsList=[]),o.pageCount=Number(o.pageCount)+1,o.loading=!1,t.data.total<=0)return void(o.loading=!0);if(o.goodsList=o.goodsList.concat(t.data.data),o.goodsCount=t.data.total,t.data.current_page==t.data.last_page)return void(o.loading=!0)}else o.goodsList=[],o.goodsCount=o.goodsList.length}),(function(t){}))},addGood:function(t){if(t.vip_level_status&&1==t.vip_level_status.status)return Object(i["a"])(t.vip_level_status.tips),!1;this.show2=!0,this.goodsInfo=t,this.initPopView()},addCart:function(t,o,e){var s=this,i="";i="cart"===o?t.goods_id:t.id,$http.get("plugin.store-cashier.frontend.shoppingCart.member-cart.store",{goods_id:i,total:1,store_id:this.store_id},"添加中").then((function(i){1===i.result?("cart"==o&&(s.carts[e].total=Number(t.total)+1),s.getCart(t)):s.$dialog.alert({message:i.msg})}),(function(t){}))},updateCart:function(t,o){if(o<0){var e=0;if(this.carts.forEach((function(o){o.id==t&&(e=o.total)})),e+o<0)return void(0===_&&(_=1,this.delItemByCart(t),setTimeout((function(){_=0}),500)));0===_&&(_=1,this.updateCartRequest(t,o),setTimeout((function(){_=0}),500))}else 0===_&&(_=1,this.updateCartRequest(t,o),setTimeout((function(){_=0}),500))},updateCartRequest:function(t,o){var e=this;$http.get("plugin.store-cashier.frontend.shoppingCart.member-cart.updateNum",{id:t,num:o,store_id:this.store_id},"加载中").then((function(t){1==t.result?(e.popNum=e.popNum+o,e.getCart()):(e.$dialog.alert({message:t.msg}),e.getCart())}),(function(t){}))},delItemByCart:function(t){var o=this;$http.get("plugin.store-cashier.frontend.shoppingCart.member-cart.destroy",{store_id:this.store_id,ids:t}).then((function(t){1==t.result?(o.getCart(),o.popNum--,o.showCart=!1):o.$dialog.alert({message:t.msg})}),(function(t){}))},clearCart:function(){var t=this,o=[];this.carts.forEach((function(t){o.push(t.id)})),$http.get("plugin.store-cashier.frontend.shoppingCart.member-cart.destroy",{store_id:this.store_id,ids:o}).then((function(o){1==o.result?(t.getCart(),t.showCart=!1,t.popNum=0):t.$dialog.alert({message:o.msg})}),(function(t){}))},isHasDifferType:function(t){var o=this,e=0,i=0,a="";return t.forEach((function(t){t.live_install&&1==t.live_install.open_state?(e+=1,a=t.live_install.plugin_name):i+=1})),0==e||0==i||(s["a"].confirm({title:"提示",message:"普通商品与".concat(a,"商品一同下单将无法享受").concat(a,"，是否继续下单？")}).then((function(){o.goodsOrder(t,!0)})).catch((function(){})),!1)},goodsOrder:function(t,o){var e=0;(o||this.isHasDifferType(t))&&(this.carts.forEach((function(t){e+=t.total})),0!=e&&0!=t.length&&0!=this.carts.length&&this.$router.push(this.fun.getUrl("goodsorder",{},{store_id:this.store_id,tag:"store"})))},getFirstCart:function(t){var o=this;this.carts=t,this.goodsCarts=[],this.cartsNum=0,this.cartsTotal=0,this.carts.forEach((function(t){o.$set(o.goodsCarts,t.goods_id,t),t.goods_id===o.goodsInfo.id&&t.option_id===p&&(o.popNum=t.total,o.popCard=t),o.cartsNum+=t.total,o.cartsTotal+=t.total*t.goods.price})),this.goodsCartsTotal=this.calculateTotal(this.carts),this.cartsTotal=parseFloat(this.cartsTotal).toFixed(2)},getCart:function(t){var o=this,e=this;$http.get("plugin.store-cashier.frontend.shoppingCart.member-cart.index",{store_id:this.store_id}).then((function(s){1===s.result&&(e.carts=s.data,e.goodsCarts=[],e.cartsNum=0,e.cartsTotal=0,e.carts.forEach((function(s){o.$set(e.goodsCarts,s.goods_id,s),t&&t.id==s.goods_id&&(t.buyNum=s.total),s.goods_id===e.goodsInfo.id&&s.option_id===p&&(e.popNum=s.total,e.popCard=s),e.cartsNum+=s.total,e.cartsTotal+=s.total*s.goods.price})),e.goodsCartsTotal=e.calculateTotal(e.carts),e.cartsTotal=parseFloat(e.cartsTotal).toFixed(2))}),(function(t){})).catch((function(t){}))},goShowCart:function(){!this.showCart&&this.cartsNum>0?(this.cartScroll?this.cartScroll.refresh():this.cartScroll=new n["a"](this.$refs.cartWrapper,{click:!0}),this.showCart=!0):this.showCart=!1},showToolbar:function(){this.toolbar=!this.toolbar},tel:function(){window.location.href="tel://"+this.store.store_mobile},tosearch:function(){this.$router.push(this.fun.getUrl("search",{fromHome:1}))},goToGoodsO2O:function(t){1!==this.store.operating_state&&this.showDis&&this.$router.push(this.fun.getUrl("goods",{id:t.goods_id}))},gotoGoodDetail:function(t){this.$router.push(this.fun.getUrl("goodsO2O",{id:t,tag:"o2o",store_id:this.store_id}))},goTOHome:function(){this.show8=!1,this.$router.push(this.fun.getUrl("o2oHome"))},calculateTotal:function(t){var o=this,e=[],s={},i=[];for(var a in t){var n=t[a].goods_id;s[n]?s[n].total=s[n].total+t[a].total:(s[n]={},s[n].goods_id=t[a].goods_id,s[n].total=t[a].total)}for(var r in s)e.push(s[r]);return e.forEach((function(t){o.$set(i,t.goods_id,t)})),i},close:function(){this.show2=!1,p="",f=[],this.specid="",this.popNum=0},submitAction:function(){if(f.length<this.goodsInfo.has_many_specs.length)return this.show2=!1,void Object(i["a"])(this.goodsDescription);this.addCartRequest(this.goodsInfo.id,p)},addCartRequest:function(t,o){if(0!==m){var e=this,s={goods_id:t,total:1,option_id:o,store_id:this.store_id};$http.get("plugin.store-cashier.frontend.shoppingCart.member-cart.store",s,"添加中").then((function(t){1===t.result?(e.popNum++,e.getCart(),Object(i["a"])(t.msg)):Object(i["a"])(t.msg)})).catch((function(t){}))}else Object(i["a"])("商品库存不足")},initPopView:function(){if(p="",f=[],this.specid="",this.popNum=0,1===this.goodsInfo.has_option){this.popTitle=this.goodsInfo.title,this.popThumb=this.goodsInfo.thumb,this.popStock=this.goodsInfo.stock,this.popCard={},this.specid.length||(this.popPrice=this.goodsInfo.min_price+"-"+this.goodsInfo.max_price),this.goodsDescription="请选择";for(var t=0;t<this.goodsInfo.has_many_specs.length;t++)this.goodsDescription+=" "+this.goodsInfo.has_many_specs[t].title;if(!p)for(var o=0;o<this.goodsInfo.has_many_specs.length;o++)this.selectSpecs(this.goodsInfo.has_many_specs[o].specitem[0],this.goodsInfo.has_many_specs[o].specitem[0].id)}else this.goodsDescription="",m=this.goodsInfo.stock},selectSpecs:function(t,o){this.manageSpecs(t),this.setGoodsSpecs(t),this.setGoodsSpecsChangeInfo(o),this.getMaxCount()},getMaxCount:function(){f.length==this.goodsInfo.has_many_specs.length&&(0==m&&(this.popNum=0),this.popNum>m&&(this.popNum=m))},setGoodsSpecsChangeInfo:function(t){if(f.sort((function(t,o){return t.id-o.id})),f.length===this.goodsInfo.has_many_specs.length){for(var o="",e=0;e<f.length;e++)o+=f[e].id+"_";o=o.substring(0,o.length-1),this.specid=o;for(var s=0;s<this.goodsInfo.has_many_options.length;s++)if(o===this.setGoodsSpecsBySort(this.goodsInfo.has_many_options[s].specs)){this.popmPrice=this.goodsInfo.has_many_options[s].market_price,this.popPrice=this.goodsInfo.has_many_options[s].product_price,this.popThumb=this.fun.isTextEmpty(this.goodsInfo.has_many_options[s].thumb)?this.goodsInfo.thumb:this.goodsInfo.has_many_options[s].thumb,this.popStock=this.goodsInfo.has_many_options[s].stock,p=this.goodsInfo.has_many_options[s].id,m=this.goodsInfo.has_many_options[s].stock,m>0&&(this.popNum=0);break}for(var i=0;i<this.carts.length;i++){if(this.carts[i].goods_id===this.goodsInfo.id&&this.carts[i].option_id===p){this.popNum=this.carts[i].total,this.popCard=this.carts[i];break}this.popNum=0,this.popCard={}}}else{for(var a="",n=0;n<f.length;n++)a+=f[n].id+"_";this.specid=a}},setGoodsSpecsBySort:function(t){var o=t.split("_");o.sort((function(t,o){return t-o}));for(var e="",s=0;s<o.length;s++)e+=o[s]+"_";return e=e.substring(0,e.length-1),e},manageSpecs:function(t){var o=new Object;if(o.id=t.id,o.specid=t.specid,o.title=t.title,f.length>0){for(var e=0;e<f.length;e++)f[e].specid==o.specid&&f.splice(e,1);f.push(o)}else f.push(o);if(f.length==this.goodsInfo.has_many_specs.length){for(var s=[],i=0;i<this.goodsInfo.has_many_specs.length;i++)for(var a=0;a<f.length;a++)if(this.goodsInfo.has_many_specs[i].id==f[a].specid){s.push(f[a]);break}f=s}this.setGoodsDescription()},setGoodsDescription:function(){var t="";if(f.length==this.goodsInfo.has_many_specs.length){t="已选择 ";for(var o=0;o<f.length;o++)t+=f[o].title+" ";this.goodsDescription=t}else{t="请选择 ";for(var e=0;e<this.goodsInfo.has_many_specs.length;e++)for(var s=0;s<f.length;s++)if(this.goodsInfo.has_many_specs[e].id!=f[s].specid){t+=this.goodsInfo.has_many_specs[e].title+" ";break}this.goodsDescription=t}},setGoodsSpecs:function(t){for(var o=0;o<this.goodsInfo.has_many_specs.length;o++)t.specid!=this.goodsInfo.has_many_specs[o].id&&this.setGoodsSpecsStatus(this.goodsInfo.has_many_specs[o].specitem,t.id)},setGoodsSpecsStatus:function(t,o){for(var e=[],s=0;s<this.goodsInfo.has_many_options.length;s++)for(var i=this.goodsInfo.has_many_options[s].specs.split("_"),a=0;a<i.length;a++)i[a]==o&&e.push(this.goodsInfo.has_many_options[s]);for(var n=0;n<e.length;n++)for(var r=e[n].specs.split("_"),d=0;d<r.length;d++)if(r[d]!=o&&0==e[n].stock)for(var c=0;c<t.length;c++)r[d]==t[c].id&&(t[c].c=!0);else if(r[d]!=o&&e[n].stock>0)for(var l=0;l<t.length;l++)r[d]==t[l].id&&(t[l].c=!1)},initShare:function(){var t=this,o={url:"android"===this.fun.isIosOrAndroid()?window.location.href:window.initUrl};$http.post("member.member.wxJsSdkConfig",o).then((function(o){1===o.result&&o.data.config&&t.share(o.data)})).catch((function(t){}))},share:function(t){var o=this;wx.config(t.config),wx.ready((function(){var e=o.fun.isTextEmpty(o.store.store_name)?t.share.title:o.store.store_name,s=document.location.href+"&share_tag=2";s=o.fun.isMid(s,t.info.uid);var a=o.fun.isTextEmpty(o.store.store_thumb)?t.share.icon:o.store.store_thumb,n=o.fun.isTextEmpty(o.store.store_introduce)?t.share.desc:o.store.store_introduce;wx.showOptionMenu(),wx.onMenuShareTimeline({title:e,link:s,imgUrl:a,success:function(){Object(i["a"])("分享成功")},cancel:function(){Object(i["a"])("取消分享")}}),wx.onMenuShareAppMessage({title:e,desc:n,link:s,imgUrl:a,type:"link",dataUrl:"",success:function(){Object(i["a"])("分享成功")},cancel:function(){Object(i["a"])("取消分享")}})}))},gotoCupcon:function(){this.$router.push(this.fun.getUrl("StoreCoupon",{id:this.store_id}))},updateshoppingcart:function(){var t=this;$http.post("plugin.store-cashier.frontend.store.get-store-info.get-stores-details",{store_id:this.store_id,order_id:this.order_id,url:"android"===this.fun.isIosOrAndroid()?window.location.href:window.initUrl}).then((function(o){"该商家已过期,去看看其他的吧"!==o.msg&&1===o.result&&t.getFirstCart(o.data.member_cart)}),(function(t){}))}},components:{cStar:a["a"],cFlow:r["a"],cLive:c["a"],deliveryMap:l["a"],cGrouplist:h["a"],yz_home_button:g["a"],depositLadderCell:u["a"]},watch:{scrollMargin:function(t){b<=670&&(t<this.Height?this.amout=!0:this.amout=!1)}},computed:{minicartStyle:function(){return this.showCart?"display: none;transform: translateY(-100%)":"display: block;transform: translateY(0)"},leftPrice:function(){if(this.showCart)return"transform: translateX(-60px);"},currentCategoryIndex:function(){for(var t=0;t<this.listCategoryHeight.length;t++){var o=this.listCategoryHeight[t],e=this.listCategoryHeight[t+1];if(!e||this.scrollY>=o&&this.scrollY<e)return t}return 0},currentIndex:function(){for(var t=0;t<this.listHeight.length;t++){var o=this.listHeight[t],e=this.listHeight[t+1];if(!e||this.scrollY>=o&&this.scrollY<e)return this.secondTitle[t].id}return 0}}}},f343:function(t,o,e){"use strict";e("ca3f")}}]);
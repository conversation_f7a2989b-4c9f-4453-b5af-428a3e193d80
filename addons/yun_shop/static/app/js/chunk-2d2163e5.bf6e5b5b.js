(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d2163e5"],{c244:function(e,t,n){"use strict";(function(o){var r,a,s=n("f2a9").default;n("b942"),n("a8b2"),n("2c45"),n("ca52"),n("abd8"),n("2261"),n("c24b"),n("c917"),n("7116"),n("2283"),n("f95d"),n("6912"),n("237e"),n("c12c"),n("5ee8"),n("e9bb"),n("b0a9"),n("2ee7"),n("ad3f"),n("1fbe"),n("685e"),n("f6be"),n("5de7"),n("4278"),n("6c2e"),n("592d"),n("9b31"),n("873f"),n("fd7d"),n("b101"),n("b232"),n("71de"),n("5cae"),n("ea92"),n("1248"),n("3da3"),n("bcfa"),n("1eca"),n("b034"),n("193e"),n("07d1"),n("1bf1"),n("6e63"),n("f5a1"),n("7a13"),n("4409"),n("326e"),n("6351"),n("e21e"),n("f252"),n("390f"),n("7d49"),n("ad9c"),n("941b"),n("0baa"),n("3d7e"),n("b7bb"),n("ac4f"),n("b3f9"),n("7499"),n("5cac"),n("6b9f"),n("04b7"),n("0b5c"),n("1fe7"),n("50fd"),n("0bb9"),n("593e"),n("4b8e"),n("277f"),n("5999"),n("4c75"),n("ff35"),n("7f80"),n("f31f"),n("43bf"),n("cd04"),n("d7ef"),n("b4d2"),n("7f97"),n("5e1f"),n("b906"),n("e6b5"),n("bb33"),n("640a"),n("fe7a"),n("bc9c"),n("7519"),function(o,i){"object"==s(t)&&"undefined"!=typeof e?e.exports=i():(r=i,a="function"===typeof r?r.call(t,n,t,e):r,void 0===a||(e.exports=a))}(0,(function(){var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof o?o:"undefined"!=typeof self?self:{};function t(e,t){return e(t={exports:{}},t.exports),t.exports}var n=function(e){return e&&e.Math==Math&&e},r=n("object"==("undefined"===typeof globalThis?"undefined":s(globalThis))&&globalThis)||n("object"==("undefined"===typeof window?"undefined":s(window))&&window)||n("object"==("undefined"===typeof self?"undefined":s(self))&&self)||n("object"==s(e)&&e)||Function("return this")(),a=function(e){try{return!!e()}catch(t){return!0}},i=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),u={}.propertyIsEnumerable,c=Object.getOwnPropertyDescriptor,l={f:c&&!u.call({1:2},1)?function(e){var t=c(this,e);return!!t&&t.enumerable}:u},d=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},p={}.toString,g=function(e){return p.call(e).slice(8,-1)},h="".split,f=a((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==g(e)?h.call(e,""):Object(e)}:Object,_=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},m=function(e){return f(_(e))},v=function(e){return"object"==s(e)?null!==e:"function"==typeof e},M=function(e,t){if(!v(e))return e;var n,o;if(t&&"function"==typeof(n=e.toString)&&!v(o=n.call(e)))return o;if("function"==typeof(n=e.valueOf)&&!v(o=n.call(e)))return o;if(!t&&"function"==typeof(n=e.toString)&&!v(o=n.call(e)))return o;throw TypeError("Can't convert object to primitive value")},y={}.hasOwnProperty,I=function(e,t){return y.call(e,t)},T=r.document,C=v(T)&&v(T.createElement),S=function(e){return C?T.createElement(e):{}},A=!i&&!a((function(){return 7!=Object.defineProperty(S("div"),"a",{get:function(){return 7}}).a})),E=Object.getOwnPropertyDescriptor,k={f:i?E:function(e,t){if(e=m(e),t=M(t,!0),A)try{return E(e,t)}catch(n){}if(I(e,t))return d(!l.f.call(e,t),e[t])}},D=function(e){if(!v(e))throw TypeError(String(e)+" is not an object");return e},N=Object.defineProperty,O={f:i?N:function(e,t,n){if(D(e),t=M(t,!0),D(n),A)try{return N(e,t,n)}catch(r){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},R=i?function(e,t,n){return O.f(e,t,d(1,n))}:function(e,t,n){return e[t]=n,e},L=function(e,t){try{R(r,e,t)}catch(n){r[e]=t}return t},b=r["__core-js_shared__"]||L("__core-js_shared__",{}),w=Function.toString;"function"!=typeof b.inspectSource&&(b.inspectSource=function(e){return w.call(e)});var G,P,U,F=b.inspectSource,q=r.WeakMap,x="function"==typeof q&&/native code/.test(F(q)),V=t((function(e){(e.exports=function(e,t){return b[e]||(b[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.6.5",mode:"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})})),B=0,K=Math.random(),H=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++B+K).toString(36)},j=V("keys"),W=function(e){return j[e]||(j[e]=H(e))},Y={},z=r.WeakMap;if(x){var J=new z,X=J.get,Q=J.has,$=J.set;G=function(e,t){return $.call(J,e,t),t},P=function(e){return X.call(J,e)||{}},U=function(e){return Q.call(J,e)}}else{var Z=W("state");Y[Z]=!0,G=function(e,t){return R(e,Z,t),t},P=function(e){return I(e,Z)?e[Z]:{}},U=function(e){return I(e,Z)}}var ee,te,ne={set:G,get:P,has:U,enforce:function(e){return U(e)?P(e):G(e,{})},getterFor:function(e){return function(t){var n;if(!v(t)||(n=P(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return n}}},oe=t((function(e){var t=ne.get,n=ne.enforce,o=String(String).split("String");(e.exports=function(e,t,a,s){var i=!!s&&!!s.unsafe,u=!!s&&!!s.enumerable,c=!!s&&!!s.noTargetGet;"function"==typeof a&&("string"!=typeof t||I(a,"name")||R(a,"name",t),n(a).source=o.join("string"==typeof t?t:"")),e!==r?(i?!c&&e[t]&&(u=!0):delete e[t],u?e[t]=a:R(e,t,a)):u?e[t]=a:L(t,a)})(Function.prototype,"toString",(function(){return"function"==typeof this&&t(this).source||F(this)}))})),re=r,ae=function(e){return"function"==typeof e?e:void 0},se=function(e,t){return arguments.length<2?ae(re[e])||ae(r[e]):re[e]&&re[e][t]||r[e]&&r[e][t]},ie=Math.ceil,ue=Math.floor,ce=function(e){return isNaN(e=+e)?0:(e>0?ue:ie)(e)},le=Math.min,de=function(e){return e>0?le(ce(e),9007199254740991):0},pe=Math.max,ge=Math.min,he=function(e,t){var n=ce(e);return n<0?pe(n+t,0):ge(n,t)},fe=function(e){return function(t,n,o){var r,a=m(t),s=de(a.length),i=he(o,s);if(e&&n!=n){for(;s>i;)if((r=a[i++])!=r)return!0}else for(;s>i;i++)if((e||i in a)&&a[i]===n)return e||i||0;return!e&&-1}},_e={includes:fe(!0),indexOf:fe(!1)},me=_e.indexOf,ve=function(e,t){var n,o=m(e),r=0,a=[];for(n in o)!I(Y,n)&&I(o,n)&&a.push(n);for(;t.length>r;)I(o,n=t[r++])&&(~me(a,n)||a.push(n));return a},Me=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],ye=Me.concat("length","prototype"),Ie={f:Object.getOwnPropertyNames||function(e){return ve(e,ye)}},Te={f:Object.getOwnPropertySymbols},Ce=se("Reflect","ownKeys")||function(e){var t=Ie.f(D(e)),n=Te.f;return n?t.concat(n(e)):t},Se=function(e,t){for(var n=Ce(t),o=O.f,r=k.f,a=0;a<n.length;a++){var s=n[a];I(e,s)||o(e,s,r(t,s))}},Ae=/#|\.prototype\./,Ee=function(e,t){var n=De[ke(e)];return n==Oe||n!=Ne&&("function"==typeof t?a(t):!!t)},ke=Ee.normalize=function(e){return String(e).replace(Ae,".").toLowerCase()},De=Ee.data={},Ne=Ee.NATIVE="N",Oe=Ee.POLYFILL="P",Re=Ee,Le=k.f,be=function(e,t){var n,o,a,i,u,c=e.target,l=e.global,d=e.stat;if(n=l?r:d?r[c]||L(c,{}):(r[c]||{}).prototype)for(o in t){if(i=t[o],a=e.noTargetGet?(u=Le(n,o))&&u.value:n[o],!Re(l?o:c+(d?".":"#")+o,e.forced)&&void 0!==a){if(s(i)==s(a))continue;Se(i,a)}(e.sham||a&&a.sham)&&R(i,"sham",!0),oe(n,o,i,e)}},we=Array.isArray||function(e){return"Array"==g(e)},Ge=function(e){return Object(_(e))},Pe=function(e,t,n){var o=M(t);o in e?O.f(e,o,d(0,n)):e[o]=n},Ue=!!Object.getOwnPropertySymbols&&!a((function(){return!String(Symbol())})),Fe=Ue&&!Symbol.sham&&"symbol"==s(Symbol.iterator),qe=V("wks"),xe=r.Symbol,Ve=Fe?xe:xe&&xe.withoutSetter||H,Be=function(e){return I(qe,e)||(Ue&&I(xe,e)?qe[e]=xe[e]:qe[e]=Ve("Symbol."+e)),qe[e]},Ke=Be("species"),He=function(e,t){var n;return we(e)&&("function"!=typeof(n=e.constructor)||n!==Array&&!we(n.prototype)?v(n)&&null===(n=n[Ke])&&(n=void 0):n=void 0),new(void 0===n?Array:n)(0===t?0:t)},je=se("navigator","userAgent")||"",We=r.process,Ye=We&&We.versions,ze=Ye&&Ye.v8;ze?te=(ee=ze.split("."))[0]+ee[1]:je&&(!(ee=je.match(/Edge\/(\d+)/))||ee[1]>=74)&&(ee=je.match(/Chrome\/(\d+)/))&&(te=ee[1]);var Je=te&&+te,Xe=Be("species"),Qe=function(e){return Je>=51||!a((function(){var t=[];return(t.constructor={})[Xe]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},$e=Be("isConcatSpreadable"),Ze=Je>=51||!a((function(){var e=[];return e[$e]=!1,e.concat()[0]!==e})),et=Qe("concat"),nt=function(e){if(!v(e))return!1;var t=e[$e];return void 0!==t?!!t:we(e)};be({target:"Array",proto:!0,forced:!Ze||!et},{concat:function(e){var t,n,o,r,a,s=Ge(this),i=He(s,0),u=0;for(t=-1,o=arguments.length;t<o;t++)if(nt(a=-1===t?s:arguments[t])){if(u+(r=de(a.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(n=0;n<r;n++,u++)n in a&&Pe(i,u,a[n])}else{if(u>=9007199254740991)throw TypeError("Maximum allowed index exceeded");Pe(i,u++,a)}return i.length=u,i}});var ot=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e},rt=function(e,t,n){if(ot(e),void 0===t)return e;switch(n){case 0:return function(){return e.call(t)};case 1:return function(n){return e.call(t,n)};case 2:return function(n,o){return e.call(t,n,o)};case 3:return function(n,o,r){return e.call(t,n,o,r)}}return function(){return e.apply(t,arguments)}},at=[].push,st=function(e){var t=1==e,n=2==e,o=3==e,r=4==e,a=6==e,s=5==e||a;return function(i,u,c,l){for(var d,p,g=Ge(i),h=f(g),_=rt(u,c,3),m=de(h.length),v=0,M=l||He,y=t?M(i,m):n?M(i,0):void 0;m>v;v++)if((s||v in h)&&(p=_(d=h[v],v,g),e))if(t)y[v]=p;else if(p)switch(e){case 3:return!0;case 5:return d;case 6:return v;case 2:at.call(y,d)}else if(r)return!1;return a?-1:o||r?r:y}},it={forEach:st(0),map:st(1),filter:st(2),some:st(3),every:st(4),find:st(5),findIndex:st(6)},ut=function(e,t){var n=[][e];return!!n&&a((function(){n.call(null,t||function(){throw 1},1)}))},ct=Object.defineProperty,lt={},dt=function(e){throw e},pt=function(e,t){if(I(lt,e))return lt[e];t||(t={});var n=[][e],o=!!I(t,"ACCESSORS")&&t.ACCESSORS,r=I(t,0)?t[0]:dt,s=I(t,1)?t[1]:void 0;return lt[e]=!!n&&!a((function(){if(o&&!i)return!0;var e={length:-1};o?ct(e,1,{enumerable:!0,get:dt}):e[1]=1,n.call(e,r,s)}))},gt=it.forEach,ht=ut("forEach"),ft=pt("forEach"),_t=ht&&ft?[].forEach:function(e){return gt(this,e,arguments.length>1?arguments[1]:void 0)};be({target:"Array",proto:!0,forced:[].forEach!=_t},{forEach:_t});var mt=function(e,t,n,o){try{return o?t(D(n)[0],n[1]):t(n)}catch(i){var r=e.return;throw void 0!==r&&D(r.call(e)),i}},vt={},Mt=Be("iterator"),yt=Array.prototype,It=function(e){return void 0!==e&&(vt.Array===e||yt[Mt]===e)},Tt={};Tt[Be("toStringTag")]="z";var Ct="[object z]"===String(Tt),St=Be("toStringTag"),At="Arguments"==g(function(){return arguments}()),Et=Ct?g:function(e){var t,n,o;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(n){}}(t=Object(e),St))?n:At?g(t):"Object"==(o=g(t))&&"function"==typeof t.callee?"Arguments":o},kt=Be("iterator"),Dt=function(e){if(null!=e)return e[kt]||e["@@iterator"]||vt[Et(e)]},Nt=function(e){var t,n,o,r,a,s,i=Ge(e),u="function"==typeof this?this:Array,c=arguments.length,l=c>1?arguments[1]:void 0,d=void 0!==l,p=Dt(i),g=0;if(d&&(l=rt(l,c>2?arguments[2]:void 0,2)),null==p||u==Array&&It(p))for(n=new u(t=de(i.length));t>g;g++)s=d?l(i[g],g):i[g],Pe(n,g,s);else for(a=(r=p.call(i)).next,n=new u;!(o=a.call(r)).done;g++)s=d?mt(r,l,[o.value,g],!0):o.value,Pe(n,g,s);return n.length=g,n},Ot=Be("iterator"),Rt=!1;try{var Lt=0,bt={next:function(){return{done:!!Lt++}},return:function(){Rt=!0}};bt[Ot]=function(){return this},Array.from(bt,(function(){throw 2}))}catch(YI){}var wt=function(e,t){if(!t&&!Rt)return!1;var n=!1;try{var o={};o[Ot]=function(){return{next:function(){return{done:n=!0}}}},e(o)}catch(YI){}return n},Gt=!wt((function(e){Array.from(e)}));be({target:"Array",stat:!0,forced:Gt},{from:Nt});var Pt,Ut=Object.keys||function(e){return ve(e,Me)},Ft=i?Object.defineProperties:function(e,t){D(e);for(var n,o=Ut(t),r=o.length,a=0;r>a;)O.f(e,n=o[a++],t[n]);return e},qt=se("document","documentElement"),xt=W("IE_PROTO"),Vt=function(){},Bt=function(e){return"<script>"+e+"<\/script>"},Kt=function(){try{Pt=document.domain&&new ActiveXObject("htmlfile")}catch(YI){}var e,t;Kt=Pt?function(e){e.write(Bt("")),e.close();var t=e.parentWindow.Object;return e=null,t}(Pt):((t=S("iframe")).style.display="none",qt.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(Bt("document.F=Object")),e.close(),e.F);for(var n=Me.length;n--;)delete Kt.prototype[Me[n]];return Kt()};Y[xt]=!0;var Ht=Object.create||function(e,t){var n;return null!==e?(Vt.prototype=D(e),n=new Vt,Vt.prototype=null,n[xt]=e):n=Kt(),void 0===t?n:Ft(n,t)};be({target:"Object",stat:!0,sham:!i},{create:Ht});var jt=a((function(){Ut(1)}));be({target:"Object",stat:!0,forced:jt},{keys:function(e){return Ut(Ge(e))}});var Wt="\t\n\v\f\r                　\u2028\u2029\ufeff",Yt="["+Wt+"]",zt=RegExp("^"+Yt+Yt+"*"),Jt=RegExp(Yt+Yt+"*$"),Xt=function(e){return function(t){var n=String(_(t));return 1&e&&(n=n.replace(zt,"")),2&e&&(n=n.replace(Jt,"")),n}},Qt={start:Xt(1),end:Xt(2),trim:Xt(3)},$t=Qt.trim,Zt=r.parseInt,en=/^[+-]?0[Xx]/,tn=8!==Zt(Wt+"08")||22!==Zt(Wt+"0x16")?function(e,t){var n=$t(String(e));return Zt(n,t>>>0||(en.test(n)?16:10))}:Zt;be({global:!0,forced:parseInt!=tn},{parseInt:tn});var nn,on,rn,an=function(e){return function(t,n){var o,r,a=String(_(t)),s=ce(n),i=a.length;return s<0||s>=i?e?"":void 0:(o=a.charCodeAt(s))<55296||o>56319||s+1===i||(r=a.charCodeAt(s+1))<56320||r>57343?e?a.charAt(s):o:e?a.slice(s,s+2):r-56320+(o-55296<<10)+65536}},sn={codeAt:an(!1),charAt:an(!0)},un=!a((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),cn=W("IE_PROTO"),ln=Object.prototype,dn=un?Object.getPrototypeOf:function(e){return e=Ge(e),I(e,cn)?e[cn]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?ln:null},pn=Be("iterator"),gn=!1;[].keys&&("next"in(rn=[].keys())?(on=dn(dn(rn)))!==Object.prototype&&(nn=on):gn=!0),null==nn&&(nn={}),I(nn,pn)||R(nn,pn,(function(){return this}));var hn={IteratorPrototype:nn,BUGGY_SAFARI_ITERATORS:gn},fn=O.f,_n=Be("toStringTag"),mn=function(e,t,n){e&&!I(e=n?e:e.prototype,_n)&&fn(e,_n,{configurable:!0,value:t})},vn=hn.IteratorPrototype,Mn=function(){return this},yn=function(e,t,n){var o=t+" Iterator";return e.prototype=Ht(vn,{next:d(1,n)}),mn(e,o,!1),vt[o]=Mn,e},In=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(n,[]),t=n instanceof Array}catch(YI){}return function(n,o){return D(n),function(e){if(!v(e)&&null!==e)throw TypeError("Can't set "+String(e)+" as a prototype")}(o),t?e.call(n,o):n.__proto__=o,n}}():void 0),Tn=hn.IteratorPrototype,Cn=hn.BUGGY_SAFARI_ITERATORS,Sn=Be("iterator"),An=function(){return this},En=function(e,t,n,o,r,a,s){yn(n,t,o);var i,u,c,l=function(e){if(e===r&&f)return f;if(!Cn&&e in g)return g[e];switch(e){case"keys":case"values":case"entries":return function(){return new n(this,e)}}return function(){return new n(this)}},d=t+" Iterator",p=!1,g=e.prototype,h=g[Sn]||g["@@iterator"]||r&&g[r],f=!Cn&&h||l(r),_="Array"==t&&g.entries||h;if(_&&(i=dn(_.call(new e)),Tn!==Object.prototype&&i.next&&(dn(i)!==Tn&&(In?In(i,Tn):"function"!=typeof i[Sn]&&R(i,Sn,An)),mn(i,d,!0))),"values"==r&&h&&"values"!==h.name&&(p=!0,f=function(){return h.call(this)}),g[Sn]!==f&&R(g,Sn,f),vt[t]=f,r)if(u={values:l("values"),keys:a?f:l("keys"),entries:l("entries")},s)for(c in u)(Cn||p||!(c in g))&&oe(g,c,u[c]);else be({target:t,proto:!0,forced:Cn||p},u);return u},kn=sn.charAt,Dn=ne.set,Nn=ne.getterFor("String Iterator");En(String,"String",(function(e){Dn(this,{type:"String Iterator",string:String(e),index:0})}),(function(){var e,t=Nn(this),n=t.string,o=t.index;return o>=n.length?{value:void 0,done:!0}:(e=kn(n,o),t.index+=e.length,{value:e,done:!1})}));var On={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0};for(var Rn in On){var Ln=r[Rn],bn=Ln&&Ln.prototype;if(bn&&bn.forEach!==_t)try{R(bn,"forEach",_t)}catch(YI){bn.forEach=_t}}function wn(e){return(wn="function"==typeof Symbol&&"symbol"==s(Symbol.iterator)?function(e){return s(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":s(e)})(e)}function Gn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Pn(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function Un(e,t,n){return t&&Pn(e.prototype,t),n&&Pn(e,n),e}function Fn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function qn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function xn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?qn(Object(n),!0).forEach((function(t){Fn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):qn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Vn(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Kn(e,t)}function Bn(e){return(Bn=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Kn(e,t){return(Kn=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Hn(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function jn(e,t,n){return(jn=Hn()?Reflect.construct:function(e,t,n){var o=[null];o.push.apply(o,t);var r=new(Function.bind.apply(e,o));return n&&Kn(r,n.prototype),r}).apply(null,arguments)}function Wn(e){var t="function"==typeof Map?new Map:void 0;return(Wn=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,o)}function o(){return jn(e,arguments,Bn(this).constructor)}return o.prototype=Object.create(e.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),Kn(o,e)})(e)}function Yn(e,t){if(null==e)return{};var n,o,r=function(e,t){if(null==e)return{};var n,o,r={},a=Object.keys(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function zn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Jn(e,t){return!t||"object"!=s(t)&&"function"!=typeof t?zn(e):t}function Xn(e){var t=Hn();return function(){var n,o=Bn(e);if(t){var r=Bn(this).constructor;n=Reflect.construct(o,arguments,r)}else n=o.apply(this,arguments);return Jn(this,n)}}function Qn(e,t){return Zn(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var n=[],o=!0,r=!1,a=void 0;try{for(var s,i=e[Symbol.iterator]();!(o=(s=i.next()).done)&&(n.push(s.value),!t||n.length!==t);o=!0);}catch(l){r=!0,a=l}finally{try{o||null==i.return||i.return()}finally{if(r)throw a}}return n}}(e,t)||to(e,t)||oo()}function $n(e){return function(e){if(Array.isArray(e))return no(e)}(e)||eo(e)||to(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Zn(e){if(Array.isArray(e))return e}function eo(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function to(e,t){if(e){if("string"==typeof e)return no(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?no(e,t):void 0}}function no(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function oo(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ro(e,t){var n;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=to(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var o=0,r=function(){};return{s:r,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,i=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return s=e.done,e},e:function(e){i=!0,a=e},f:function(){try{s||null==n.return||n.return()}finally{if(i)throw a}}}}var ao={SDK_READY:"sdkStateReady",SDK_NOT_READY:"sdkStateNotReady",SDK_DESTROY:"sdkDestroy",MESSAGE_RECEIVED:"onMessageReceived",MESSAGE_MODIFIED:"onMessageModified",MESSAGE_REVOKED:"onMessageRevoked",MESSAGE_READ_BY_PEER:"onMessageReadByPeer",CONVERSATION_LIST_UPDATED:"onConversationListUpdated",GROUP_LIST_UPDATED:"onGroupListUpdated",GROUP_SYSTEM_NOTICE_RECEIVED:"receiveGroupSystemNotice",GROUP_ATTRIBUTES_UPDATED:"groupAttributesUpdated",PROFILE_UPDATED:"onProfileUpdated",BLACKLIST_UPDATED:"blacklistUpdated",FRIEND_LIST_UPDATED:"onFriendListUpdated",FRIEND_GROUP_LIST_UPDATED:"onFriendGroupListUpdated",FRIEND_APPLICATION_LIST_UPDATED:"onFriendApplicationListUpdated",KICKED_OUT:"kickedOut",ERROR:"error",NET_STATE_CHANGE:"netStateChange",SDK_RELOAD:"sdkReload"},so={MSG_TEXT:"TIMTextElem",MSG_IMAGE:"TIMImageElem",MSG_SOUND:"TIMSoundElem",MSG_AUDIO:"TIMSoundElem",MSG_FILE:"TIMFileElem",MSG_FACE:"TIMFaceElem",MSG_VIDEO:"TIMVideoFileElem",MSG_GEO:"TIMLocationElem",MSG_LOCATION:"TIMLocationElem",MSG_GRP_TIP:"TIMGroupTipElem",MSG_GRP_SYS_NOTICE:"TIMGroupSystemNoticeElem",MSG_CUSTOM:"TIMCustomElem",MSG_MERGER:"TIMRelayElem",MSG_PRIORITY_HIGH:"High",MSG_PRIORITY_NORMAL:"Normal",MSG_PRIORITY_LOW:"Low",MSG_PRIORITY_LOWEST:"Lowest",CONV_C2C:"C2C",CONV_GROUP:"GROUP",CONV_SYSTEM:"@TIM#SYSTEM",CONV_AT_ME:1,CONV_AT_ALL:2,CONV_AT_ALL_AT_ME:3,GRP_PRIVATE:"Private",GRP_WORK:"Private",GRP_PUBLIC:"Public",GRP_CHATROOM:"ChatRoom",GRP_MEETING:"ChatRoom",GRP_AVCHATROOM:"AVChatRoom",GRP_MBR_ROLE_OWNER:"Owner",GRP_MBR_ROLE_ADMIN:"Admin",GRP_MBR_ROLE_MEMBER:"Member",GRP_TIP_MBR_JOIN:1,GRP_TIP_MBR_QUIT:2,GRP_TIP_MBR_KICKED_OUT:3,GRP_TIP_MBR_SET_ADMIN:4,GRP_TIP_MBR_CANCELED_ADMIN:5,GRP_TIP_GRP_PROFILE_UPDATED:6,GRP_TIP_MBR_PROFILE_UPDATED:7,MSG_REMIND_ACPT_AND_NOTE:"AcceptAndNotify",MSG_REMIND_ACPT_NOT_NOTE:"AcceptNotNotify",MSG_REMIND_DISCARD:"Discard",GENDER_UNKNOWN:"Gender_Type_Unknown",GENDER_FEMALE:"Gender_Type_Female",GENDER_MALE:"Gender_Type_Male",KICKED_OUT_MULT_ACCOUNT:"multipleAccount",KICKED_OUT_MULT_DEVICE:"multipleDevice",KICKED_OUT_USERSIG_EXPIRED:"userSigExpired",ALLOW_TYPE_ALLOW_ANY:"AllowType_Type_AllowAny",ALLOW_TYPE_NEED_CONFIRM:"AllowType_Type_NeedConfirm",ALLOW_TYPE_DENY_ANY:"AllowType_Type_DenyAny",FORBID_TYPE_NONE:"AdminForbid_Type_None",FORBID_TYPE_SEND_OUT:"AdminForbid_Type_SendOut",JOIN_OPTIONS_FREE_ACCESS:"FreeAccess",JOIN_OPTIONS_NEED_PERMISSION:"NeedPermission",JOIN_OPTIONS_DISABLE_APPLY:"DisableApply",JOIN_STATUS_SUCCESS:"JoinedSuccess",JOIN_STATUS_ALREADY_IN_GROUP:"AlreadyInGroup",JOIN_STATUS_WAIT_APPROVAL:"WaitAdminApproval",GRP_PROFILE_OWNER_ID:"ownerID",GRP_PROFILE_CREATE_TIME:"createTime",GRP_PROFILE_LAST_INFO_TIME:"lastInfoTime",GRP_PROFILE_MEMBER_NUM:"memberNum",GRP_PROFILE_MAX_MEMBER_NUM:"maxMemberNum",GRP_PROFILE_JOIN_OPTION:"joinOption",GRP_PROFILE_INTRODUCTION:"introduction",GRP_PROFILE_NOTIFICATION:"notification",GRP_PROFILE_MUTE_ALL_MBRS:"muteAllMembers",SNS_ADD_TYPE_SINGLE:"Add_Type_Single",SNS_ADD_TYPE_BOTH:"Add_Type_Both",SNS_DELETE_TYPE_SINGLE:"Delete_Type_Single",SNS_DELETE_TYPE_BOTH:"Delete_Type_Both",SNS_APPLICATION_TYPE_BOTH:"Pendency_Type_Both",SNS_APPLICATION_SENT_TO_ME:"Pendency_Type_ComeIn",SNS_APPLICATION_SENT_BY_ME:"Pendency_Type_SendOut",SNS_APPLICATION_AGREE:"Response_Action_Agree",SNS_APPLICATION_AGREE_AND_ADD:"Response_Action_AgreeAndAdd",SNS_CHECK_TYPE_BOTH:"CheckResult_Type_Both",SNS_CHECK_TYPE_SINGLE:"CheckResult_Type_Single",SNS_TYPE_NO_RELATION:"CheckResult_Type_NoRelation",SNS_TYPE_A_WITH_B:"CheckResult_Type_AWithB",SNS_TYPE_B_WITH_A:"CheckResult_Type_BWithA",SNS_TYPE_BOTH_WAY:"CheckResult_Type_BothWay",NET_STATE_CONNECTED:"connected",NET_STATE_CONNECTING:"connecting",NET_STATE_DISCONNECTED:"disconnected",MSG_AT_ALL:"__kImSDK_MesssageAtALL__",READ_ALL_C2C_MSG:"readAllC2CMessage",READ_ALL_GROUP_MSG:"readAllGroupMessage",READ_ALL_MSG:"readAllMessage"},io=it.map,uo=Qe("map"),co=pt("map");be({target:"Array",proto:!0,forced:!uo||!co},{map:function(e){return io(this,e,arguments.length>1?arguments[1]:void 0)}});var lo=[].slice,po={},go=function(e,t,n){if(!(t in po)){for(var o=[],r=0;r<t;r++)o[r]="a["+r+"]";po[t]=Function("C,a","return new C("+o.join(",")+")")}return po[t](e,n)},ho=Function.bind||function(e){var t=ot(this),n=lo.call(arguments,1),o=function o(){var r=n.concat(lo.call(arguments));return this instanceof o?go(t,r.length,r):t.apply(e,r)};return v(t.prototype)&&(o.prototype=t.prototype),o};be({target:"Function",proto:!0},{bind:ho});var fo=function(){function e(){Gn(this,e),this.cache=[],this.options=null}return Un(e,[{key:"use",value:function(e){if("function"!=typeof e)throw"middleware must be a function";return this.cache.push(e),this}},{key:"next",value:function(e){if(this.middlewares&&this.middlewares.length>0)return this.middlewares.shift().call(this,this.options,this.next.bind(this))}},{key:"run",value:function(e){return this.middlewares=this.cache.map((function(e){return e})),this.options=e,this.next()}}]),e}(),_o=O.f,mo=Function.prototype,vo=mo.toString,Mo=/^\s*function ([^ (]*)/;i&&!("name"in mo)&&_o(mo,"name",{configurable:!0,get:function(){try{return vo.call(this).match(Mo)[1]}catch(YI){return""}}});var yo=t((function(t,n){var o,r,a,i,u,c,l,d,p,g,h,f,_,m,v,M,y,I;t.exports=(o="function"==typeof Promise,r="object"==("undefined"===typeof self?"undefined":s(self))?self:e,a="undefined"!=typeof Symbol,i="undefined"!=typeof Map,u="undefined"!=typeof Set,c="undefined"!=typeof WeakMap,l="undefined"!=typeof WeakSet,d="undefined"!=typeof DataView,p=a&&void 0!==Symbol.iterator,g=a&&void 0!==Symbol.toStringTag,h=u&&"function"==typeof Set.prototype.entries,f=i&&"function"==typeof Map.prototype.entries,_=h&&Object.getPrototypeOf((new Set).entries()),m=f&&Object.getPrototypeOf((new Map).entries()),v=p&&"function"==typeof Array.prototype[Symbol.iterator],M=v&&Object.getPrototypeOf([][Symbol.iterator]()),y=p&&"function"==typeof String.prototype[Symbol.iterator],I=y&&Object.getPrototypeOf(""[Symbol.iterator]()),function(e){var t=s(e);if("object"!==t)return t;if(null===e)return"null";if(e===r)return"global";if(Array.isArray(e)&&(!1===g||!(Symbol.toStringTag in e)))return"Array";if("object"==("undefined"===typeof window?"undefined":s(window))&&null!==window){if("object"==s(window.location)&&e===window.location)return"Location";if("object"==s(window.document)&&e===window.document)return"Document";if("object"==s(window.navigator)){if("object"==s(window.navigator.mimeTypes)&&e===window.navigator.mimeTypes)return"MimeTypeArray";if("object"==s(window.navigator.plugins)&&e===window.navigator.plugins)return"PluginArray"}if(("function"==typeof window.HTMLElement||"object"==s(window.HTMLElement))&&e instanceof window.HTMLElement){if("BLOCKQUOTE"===e.tagName)return"HTMLQuoteElement";if("TD"===e.tagName)return"HTMLTableDataCellElement";if("TH"===e.tagName)return"HTMLTableHeaderCellElement"}}var n=g&&e[Symbol.toStringTag];if("string"==typeof n)return n;var a=Object.getPrototypeOf(e);return a===RegExp.prototype?"RegExp":a===Date.prototype?"Date":o&&a===Promise.prototype?"Promise":u&&a===Set.prototype?"Set":i&&a===Map.prototype?"Map":l&&a===WeakSet.prototype?"WeakSet":c&&a===WeakMap.prototype?"WeakMap":d&&a===DataView.prototype?"DataView":i&&a===m?"Map Iterator":u&&a===_?"Set Iterator":v&&a===M?"Array Iterator":y&&a===I?"String Iterator":null===a?"Object":Object.prototype.toString.call(e).slice(8,-1)})}));be({target:"Array",stat:!0},{isArray:we});var Io=Be("unscopables"),To=Array.prototype;null==To[Io]&&O.f(To,Io,{configurable:!0,value:Ht(null)});var Co=function(e){To[Io][e]=!0},So=it.find,Ao=!0,Eo=pt("find");"find"in[]&&Array(1).find((function(){Ao=!1})),be({target:"Array",proto:!0,forced:Ao||!Eo},{find:function(e){return So(this,e,arguments.length>1?arguments[1]:void 0)}}),Co("find");var ko=_e.includes,Do=pt("indexOf",{ACCESSORS:!0,1:0});be({target:"Array",proto:!0,forced:!Do},{includes:function(e){return ko(this,e,arguments.length>1?arguments[1]:void 0)}}),Co("includes");var No=_e.indexOf,Oo=[].indexOf,Ro=!!Oo&&1/[1].indexOf(1,-0)<0,Lo=ut("indexOf"),bo=pt("indexOf",{ACCESSORS:!0,1:0});be({target:"Array",proto:!0,forced:Ro||!Lo||!bo},{indexOf:function(e){return Ro?Oo.apply(this,arguments)||0:No(this,e,arguments.length>1?arguments[1]:void 0)}});var wo=ne.set,Go=ne.getterFor("Array Iterator"),Po=En(Array,"Array",(function(e,t){wo(this,{type:"Array Iterator",target:m(e),index:0,kind:t})}),(function(){var e=Go(this),t=e.target,n=e.kind,o=e.index++;return!t||o>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:o,done:!1}:"values"==n?{value:t[o],done:!1}:{value:[o,t[o]],done:!1}}),"values");vt.Arguments=vt.Array,Co("keys"),Co("values"),Co("entries");var Uo=[].join,Fo=f!=Object,qo=ut("join",",");be({target:"Array",proto:!0,forced:Fo||!qo},{join:function(e){return Uo.call(m(this),void 0===e?",":e)}});var xo=Qe("slice"),Vo=pt("slice",{ACCESSORS:!0,0:0,1:2}),Bo=Be("species"),Ko=[].slice,Ho=Math.max;be({target:"Array",proto:!0,forced:!xo||!Vo},{slice:function(e,t){var n,o,r,a=m(this),s=de(a.length),i=he(e,s),u=he(void 0===t?s:t,s);if(we(a)&&("function"!=typeof(n=a.constructor)||n!==Array&&!we(n.prototype)?v(n)&&null===(n=n[Bo])&&(n=void 0):n=void 0,n===Array||void 0===n))return Ko.call(a,i,u);for(o=new(void 0===n?Array:n)(Ho(u-i,0)),r=0;i<u;i++,r++)i in a&&Pe(o,r,a[i]);return o.length=r,o}}),be({target:"Date",stat:!0},{now:function(){return(new Date).getTime()}});var jo="".repeat||function(e){var t=String(_(this)),n="",o=ce(e);if(o<0||1/0==o)throw RangeError("Wrong number of repetitions");for(;o>0;(o>>>=1)&&(t+=t))1&o&&(n+=t);return n},Wo=Math.ceil,Yo=function(e){return function(t,n,o){var r,a,s=String(_(t)),i=s.length,u=void 0===o?" ":String(o),c=de(n);return c<=i||""==u?s:(r=c-i,(a=jo.call(u,Wo(r/u.length))).length>r&&(a=a.slice(0,r)),e?s+a:a+s)}},zo={start:Yo(!1),end:Yo(!0)}.start,Jo=Math.abs,Xo=Date.prototype,Qo=Xo.getTime,$o=Xo.toISOString,Zo=a((function(){return"0385-07-25T07:06:39.999Z"!=$o.call(new Date(-50000000000001))}))||!a((function(){$o.call(new Date(NaN))}))?function(){if(!isFinite(Qo.call(this)))throw RangeError("Invalid time value");var e=this.getUTCFullYear(),t=this.getUTCMilliseconds(),n=e<0?"-":e>9999?"+":"";return n+zo(Jo(e),n?6:4,0)+"-"+zo(this.getUTCMonth()+1,2,0)+"-"+zo(this.getUTCDate(),2,0)+"T"+zo(this.getUTCHours(),2,0)+":"+zo(this.getUTCMinutes(),2,0)+":"+zo(this.getUTCSeconds(),2,0)+"."+zo(t,3,0)+"Z"}:$o;be({target:"Date",proto:!0,forced:Date.prototype.toISOString!==Zo},{toISOString:Zo});var er=Date.prototype,tr=er.toString,nr=er.getTime;new Date(NaN)+""!="Invalid Date"&&oe(er,"toString",(function(){var e=nr.call(this);return e==e?tr.call(this):"Invalid Date"}));var or=function(e,t,n){var o,r;return In&&"function"==typeof(o=t.constructor)&&o!==n&&v(r=o.prototype)&&r!==n.prototype&&In(e,r),e},rr=Ie.f,ar=k.f,sr=O.f,ir=Qt.trim,ur=r.Number,cr=ur.prototype,lr="Number"==g(Ht(cr)),dr=function(e){var t,n,o,r,a,s,i,u,c=M(e,!1);if("string"==typeof c&&c.length>2)if(43===(t=(c=ir(c)).charCodeAt(0))||45===t){if(88===(n=c.charCodeAt(2))||120===n)return NaN}else if(48===t){switch(c.charCodeAt(1)){case 66:case 98:o=2,r=49;break;case 79:case 111:o=8,r=55;break;default:return+c}for(s=(a=c.slice(2)).length,i=0;i<s;i++)if((u=a.charCodeAt(i))<48||u>r)return NaN;return parseInt(a,o)}return+c};if(Re("Number",!ur(" 0o1")||!ur("0b1")||ur("+0x1"))){for(var pr,gr=function e(t){var n=arguments.length<1?0:t,o=this;return o instanceof e&&(lr?a((function(){cr.valueOf.call(o)})):"Number"!=g(o))?or(new ur(dr(n)),o,e):dr(n)},hr=i?rr(ur):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),fr=0;hr.length>fr;fr++)I(ur,pr=hr[fr])&&!I(gr,pr)&&sr(gr,pr,ar(ur,pr));gr.prototype=cr,cr.constructor=gr,oe(r,"Number",gr)}var _r=Ie.f,mr={}.toString,vr="object"==("undefined"===typeof window?"undefined":s(window))&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],Mr={f:function(e){return vr&&"[object Window]"==mr.call(e)?function(e){try{return _r(e)}catch(YI){return vr.slice()}}(e):_r(m(e))}},yr=Mr.f,Ir=a((function(){return!Object.getOwnPropertyNames(1)}));be({target:"Object",stat:!0,forced:Ir},{getOwnPropertyNames:yr});var Tr=a((function(){dn(1)}));be({target:"Object",stat:!0,forced:Tr,sham:!un},{getPrototypeOf:function(e){return dn(Ge(e))}});var Cr=Ct?{}.toString:function(){return"[object "+Et(this)+"]"};Ct||oe(Object.prototype,"toString",Cr,{unsafe:!0});var Sr,Ar,Er,kr=r.Promise,Dr=function(e,t,n){for(var o in t)oe(e,o,t[o],n);return e},Nr=Be("species"),Or=function(e){var t=se(e),n=O.f;i&&t&&!t[Nr]&&n(t,Nr,{configurable:!0,get:function(){return this}})},Rr=function(e,t,n){if(!(e instanceof t))throw TypeError("Incorrect "+(n?n+" ":"")+"invocation");return e},Lr=t((function(e){var t=function(e,t){this.stopped=e,this.result=t};(e.exports=function(e,n,o,r,a){var i,u,c,l,d,p,g,h=rt(n,o,r?2:1);if(a)i=e;else{if("function"!=typeof(u=Dt(e)))throw TypeError("Target is not iterable");if(It(u)){for(c=0,l=de(e.length);l>c;c++)if((d=r?h(D(g=e[c])[0],g[1]):h(e[c]))&&d instanceof t)return d;return new t(!1)}i=u.call(e)}for(p=i.next;!(g=p.call(i)).done;)if("object"==s(d=mt(i,h,g.value,r))&&d&&d instanceof t)return d;return new t(!1)}).stop=function(e){return new t(!0,e)}})),br=Be("species"),wr=function(e,t){var n,o=D(e).constructor;return void 0===o||null==(n=D(o)[br])?t:ot(n)},Gr=/(iphone|ipod|ipad).*applewebkit/i.test(je),Pr=r.location,Ur=r.setImmediate,Fr=r.clearImmediate,qr=r.process,xr=r.MessageChannel,Vr=r.Dispatch,Br=0,Kr={},Hr=function(e){if(Kr.hasOwnProperty(e)){var t=Kr[e];delete Kr[e],t()}},jr=function(e){return function(){Hr(e)}},Wr=function(e){Hr(e.data)},Yr=function(e){r.postMessage(e+"",Pr.protocol+"//"+Pr.host)};Ur&&Fr||(Ur=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return Kr[++Br]=function(){("function"==typeof e?e:Function(e)).apply(void 0,t)},Sr(Br),Br},Fr=function(e){delete Kr[e]},"process"==g(qr)?Sr=function(e){qr.nextTick(jr(e))}:Vr&&Vr.now?Sr=function(e){Vr.now(jr(e))}:xr&&!Gr?(Er=(Ar=new xr).port2,Ar.port1.onmessage=Wr,Sr=rt(Er.postMessage,Er,1)):!r.addEventListener||"function"!=typeof postMessage||r.importScripts||a(Yr)||"file:"===Pr.protocol?Sr="onreadystatechange"in S("script")?function(e){qt.appendChild(S("script")).onreadystatechange=function(){qt.removeChild(this),Hr(e)}}:function(e){setTimeout(jr(e),0)}:(Sr=Yr,r.addEventListener("message",Wr,!1)));var zr,Jr,Xr,Qr,$r,Zr,ea,ta,na={set:Ur,clear:Fr},oa=k.f,ra=na.set,aa=r.MutationObserver||r.WebKitMutationObserver,sa=r.process,ia=r.Promise,ua="process"==g(sa),ca=oa(r,"queueMicrotask"),la=ca&&ca.value;la||(zr=function(){var e,t;for(ua&&(e=sa.domain)&&e.exit();Jr;){t=Jr.fn,Jr=Jr.next;try{t()}catch(YI){throw Jr?Qr():Xr=void 0,YI}}Xr=void 0,e&&e.enter()},ua?Qr=function(){sa.nextTick(zr)}:aa&&!Gr?($r=!0,Zr=document.createTextNode(""),new aa(zr).observe(Zr,{characterData:!0}),Qr=function(){Zr.data=$r=!$r}):ia&&ia.resolve?(ea=ia.resolve(void 0),ta=ea.then,Qr=function(){ta.call(ea,zr)}):Qr=function(){ra.call(r,zr)});var da,pa,ga,ha,fa=la||function(e){var t={fn:e,next:void 0};Xr&&(Xr.next=t),Jr||(Jr=t,Qr()),Xr=t},_a=function(e){var t,n;this.promise=new e((function(e,o){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=o})),this.resolve=ot(t),this.reject=ot(n)},ma={f:function(e){return new _a(e)}},va=function(e,t){if(D(e),v(t)&&t.constructor===e)return t;var n=ma.f(e);return(0,n.resolve)(t),n.promise},Ma=function(e){try{return{error:!1,value:e()}}catch(YI){return{error:!0,value:YI}}},ya=na.set,Ia=Be("species"),Ta="Promise",Ca=ne.get,Sa=ne.set,Aa=ne.getterFor(Ta),Ea=kr,ka=r.TypeError,Da=r.document,Na=r.process,Oa=se("fetch"),Ra=ma.f,La=Ra,ba="process"==g(Na),wa=!!(Da&&Da.createEvent&&r.dispatchEvent),Ga=Re(Ta,(function(){if(F(Ea)===String(Ea)){if(66===Je)return!0;if(!ba&&"function"!=typeof PromiseRejectionEvent)return!0}if(Je>=51&&/native code/.test(Ea))return!1;var e=Ea.resolve(1),t=function(e){e((function(){}),(function(){}))};return(e.constructor={})[Ia]=t,!(e.then((function(){}))instanceof t)})),Pa=Ga||!wt((function(e){Ea.all(e).catch((function(){}))})),Ua=function(e){var t;return!(!v(e)||"function"!=typeof(t=e.then))&&t},Fa=function(e,t,n){if(!t.notified){t.notified=!0;var o=t.reactions;fa((function(){for(var r=t.value,a=1==t.state,s=0;o.length>s;){var i,u,c,l=o[s++],d=a?l.ok:l.fail,p=l.resolve,g=l.reject,h=l.domain;try{d?(a||(2===t.rejection&&Ba(e,t),t.rejection=1),!0===d?i=r:(h&&h.enter(),i=d(r),h&&(h.exit(),c=!0)),i===l.promise?g(ka("Promise-chain cycle")):(u=Ua(i))?u.call(i,p,g):p(i)):g(r)}catch(YI){h&&!c&&h.exit(),g(YI)}}t.reactions=[],t.notified=!1,n&&!t.rejection&&xa(e,t)}))}},qa=function(e,t,n){var o,a;wa?((o=Da.createEvent("Event")).promise=t,o.reason=n,o.initEvent(e,!1,!0),r.dispatchEvent(o)):o={promise:t,reason:n},(a=r["on"+e])?a(o):"unhandledrejection"===e&&function(e,t){var n=r.console;n&&n.error&&(1===arguments.length?n.error(e):n.error(e,t))}("Unhandled promise rejection",n)},xa=function(e,t){ya.call(r,(function(){var n,o=t.value;if(Va(t)&&(n=Ma((function(){ba?Na.emit("unhandledRejection",o,e):qa("unhandledrejection",e,o)})),t.rejection=ba||Va(t)?2:1,n.error))throw n.value}))},Va=function(e){return 1!==e.rejection&&!e.parent},Ba=function(e,t){ya.call(r,(function(){ba?Na.emit("rejectionHandled",e):qa("rejectionhandled",e,t.value)}))},Ka=function(e,t,n,o){return function(r){e(t,n,r,o)}},Ha=function(e,t,n,o){t.done||(t.done=!0,o&&(t=o),t.value=n,t.state=2,Fa(e,t,!0))},ja=function e(t,n,o,r){if(!n.done){n.done=!0,r&&(n=r);try{if(t===o)throw ka("Promise can't be resolved itself");var a=Ua(o);a?fa((function(){var r={done:!1};try{a.call(o,Ka(e,t,r,n),Ka(Ha,t,r,n))}catch(YI){Ha(t,r,YI,n)}})):(n.value=o,n.state=1,Fa(t,n,!1))}catch(YI){Ha(t,{done:!1},YI,n)}}};Ga&&(Ea=function(e){Rr(this,Ea,Ta),ot(e),da.call(this);var t=Ca(this);try{e(Ka(ja,this,t),Ka(Ha,this,t))}catch(YI){Ha(this,t,YI)}},(da=function(e){Sa(this,{type:Ta,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=Dr(Ea.prototype,{then:function(e,t){var n=Aa(this),o=Ra(wr(this,Ea));return o.ok="function"!=typeof e||e,o.fail="function"==typeof t&&t,o.domain=ba?Na.domain:void 0,n.parent=!0,n.reactions.push(o),0!=n.state&&Fa(this,n,!1),o.promise},catch:function(e){return this.then(void 0,e)}}),pa=function(){var e=new da,t=Ca(e);this.promise=e,this.resolve=Ka(ja,e,t),this.reject=Ka(Ha,e,t)},ma.f=Ra=function(e){return e===Ea||e===ga?new pa(e):La(e)},"function"==typeof kr&&(ha=kr.prototype.then,oe(kr.prototype,"then",(function(e,t){var n=this;return new Ea((function(e,t){ha.call(n,e,t)})).then(e,t)}),{unsafe:!0}),"function"==typeof Oa&&be({global:!0,enumerable:!0,forced:!0},{fetch:function(e){return va(Ea,Oa.apply(r,arguments))}}))),be({global:!0,wrap:!0,forced:Ga},{Promise:Ea}),mn(Ea,Ta,!1),Or(Ta),ga=se(Ta),be({target:Ta,stat:!0,forced:Ga},{reject:function(e){var t=Ra(this);return t.reject.call(void 0,e),t.promise}}),be({target:Ta,stat:!0,forced:Ga},{resolve:function(e){return va(this,e)}}),be({target:Ta,stat:!0,forced:Pa},{all:function(e){var t=this,n=Ra(t),o=n.resolve,r=n.reject,a=Ma((function(){var n=ot(t.resolve),a=[],s=0,i=1;Lr(e,(function(e){var u=s++,c=!1;a.push(void 0),i++,n.call(t,e).then((function(e){c||(c=!0,a[u]=e,--i||o(a))}),r)})),--i||o(a)}));return a.error&&r(a.value),n.promise},race:function(e){var t=this,n=Ra(t),o=n.reject,r=Ma((function(){var r=ot(t.resolve);Lr(e,(function(e){r.call(t,e).then(n.resolve,o)}))}));return r.error&&o(r.value),n.promise}});var Wa=function(){var e=D(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t};function Ya(e,t){return RegExp(e,t)}var za,Ja,Xa={UNSUPPORTED_Y:a((function(){var e=Ya("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),BROKEN_CARET:a((function(){var e=Ya("^r","gy");return e.lastIndex=2,null!=e.exec("str")}))},Qa=RegExp.prototype.exec,$a=String.prototype.replace,Za=Qa,es=(za=/a/,Ja=/b*/g,Qa.call(za,"a"),Qa.call(Ja,"a"),0!==za.lastIndex||0!==Ja.lastIndex),ts=Xa.UNSUPPORTED_Y||Xa.BROKEN_CARET,ns=void 0!==/()??/.exec("")[1];(es||ns||ts)&&(Za=function(e){var t,n,o,r,a=this,s=ts&&a.sticky,i=Wa.call(a),u=a.source,c=0,l=e;return s&&(-1===(i=i.replace("y","")).indexOf("g")&&(i+="g"),l=String(e).slice(a.lastIndex),a.lastIndex>0&&(!a.multiline||a.multiline&&"\n"!==e[a.lastIndex-1])&&(u="(?: "+u+")",l=" "+l,c++),n=new RegExp("^(?:"+u+")",i)),ns&&(n=new RegExp("^"+u+"$(?!\\s)",i)),es&&(t=a.lastIndex),o=Qa.call(s?n:a,l),s?o?(o.input=o.input.slice(c),o[0]=o[0].slice(c),o.index=a.lastIndex,a.lastIndex+=o[0].length):a.lastIndex=0:es&&o&&(a.lastIndex=a.global?o.index+o[0].length:t),ns&&o&&o.length>1&&$a.call(o[0],n,(function(){for(r=1;r<arguments.length-2;r++)void 0===arguments[r]&&(o[r]=void 0)})),o});var os=Za;be({target:"RegExp",proto:!0,forced:/./.exec!==os},{exec:os});var rs=RegExp.prototype,as=rs.toString,ss=a((function(){return"/a/b"!=as.call({source:"a",flags:"b"})})),is="toString"!=as.name;(ss||is)&&oe(RegExp.prototype,"toString",(function(){var e=D(this),t=String(e.source),n=e.flags;return"/"+t+"/"+String(void 0===n&&e instanceof RegExp&&!("flags"in rs)?Wa.call(e):n)}),{unsafe:!0});var us=sn.codeAt;be({target:"String",proto:!0},{codePointAt:function(e){return us(this,e)}});var cs=Be("match"),ls=function(e){var t;return v(e)&&(void 0!==(t=e[cs])?!!t:"RegExp"==g(e))},ds=function(e){if(ls(e))throw TypeError("The method doesn't accept regular expressions");return e},ps=Be("match"),gs=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[ps]=!1,"/./"[e](t)}catch(r){}}return!1};be({target:"String",proto:!0,forced:!gs("includes")},{includes:function(e){return!!~String(_(this)).indexOf(ds(e),arguments.length>1?arguments[1]:void 0)}});var hs=Be("species"),fs=!a((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),_s="$0"==="a".replace(/./,"$0"),ms=Be("replace"),vs=!!/./[ms]&&""===/./[ms]("a","$0"),Ms=!a((function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var n="ab".split(e);return 2!==n.length||"a"!==n[0]||"b"!==n[1]})),ys=function(e,t,n,o){var r=Be(e),s=!a((function(){var t={};return t[r]=function(){return 7},7!=""[e](t)})),i=s&&!a((function(){var t=!1,n=/a/;return"split"===e&&((n={}).constructor={},n.constructor[hs]=function(){return n},n.flags="",n[r]=/./[r]),n.exec=function(){return t=!0,null},n[r](""),!t}));if(!s||!i||"replace"===e&&(!fs||!_s||vs)||"split"===e&&!Ms){var u=/./[r],c=n(r,""[e],(function(e,t,n,o,r){return t.exec===os?s&&!r?{done:!0,value:u.call(t,n,o)}:{done:!0,value:e.call(n,t,o)}:{done:!1}}),{REPLACE_KEEPS_$0:_s,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:vs}),l=c[0],d=c[1];oe(String.prototype,e,l),oe(RegExp.prototype,r,2==t?function(e,t){return d.call(e,this,t)}:function(e){return d.call(e,this)})}o&&R(RegExp.prototype[r],"sham",!0)},Is=sn.charAt,Ts=function(e,t,n){return t+(n?Is(e,t).length:1)},Cs=function(e,t){var n=e.exec;if("function"==typeof n){var o=n.call(e,t);if("object"!=s(o))throw TypeError("RegExp exec method returned something other than an Object or null");return o}if("RegExp"!==g(e))throw TypeError("RegExp#exec called on incompatible receiver");return os.call(e,t)};ys("match",1,(function(e,t,n){return[function(t){var n=_(this),o=null==t?void 0:t[e];return void 0!==o?o.call(t,n):new RegExp(t)[e](String(n))},function(e){var o=n(t,e,this);if(o.done)return o.value;var r=D(e),a=String(this);if(!r.global)return Cs(r,a);var s=r.unicode;r.lastIndex=0;for(var i,u=[],c=0;null!==(i=Cs(r,a));){var l=String(i[0]);u[c]=l,""===l&&(r.lastIndex=Ts(a,de(r.lastIndex),s)),c++}return 0===c?null:u}]}));var Ss=Math.max,As=Math.min,Es=Math.floor,ks=/\$([$&'`]|\d\d?|<[^>]*>)/g,Ds=/\$([$&'`]|\d\d?)/g;ys("replace",2,(function(e,t,n,o){var r=o.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,a=o.REPLACE_KEEPS_$0,s=r?"$":"$0";return[function(n,o){var r=_(this),a=null==n?void 0:n[e];return void 0!==a?a.call(n,r,o):t.call(String(r),n,o)},function(e,o){if(!r&&a||"string"==typeof o&&-1===o.indexOf(s)){var u=n(t,e,this,o);if(u.done)return u.value}var c=D(e),l=String(this),d="function"==typeof o;d||(o=String(o));var p=c.global;if(p){var g=c.unicode;c.lastIndex=0}for(var h=[];;){var f=Cs(c,l);if(null===f)break;if(h.push(f),!p)break;""===String(f[0])&&(c.lastIndex=Ts(l,de(c.lastIndex),g))}for(var _,m="",v=0,M=0;M<h.length;M++){f=h[M];for(var y=String(f[0]),I=Ss(As(ce(f.index),l.length),0),T=[],C=1;C<f.length;C++)T.push(void 0===(_=f[C])?_:String(_));var S=f.groups;if(d){var A=[y].concat(T,I,l);void 0!==S&&A.push(S);var E=String(o.apply(void 0,A))}else E=i(y,l,I,T,S,o);I>=v&&(m+=l.slice(v,I)+E,v=I+y.length)}return m+l.slice(v)}];function i(e,n,o,r,a,s){var i=o+e.length,u=r.length,c=Ds;return void 0!==a&&(a=Ge(a),c=ks),t.call(s,c,(function(t,s){var c;switch(s.charAt(0)){case"$":return"$";case"&":return e;case"`":return n.slice(0,o);case"'":return n.slice(i);case"<":c=a[s.slice(1,-1)];break;default:var l=+s;if(0===l)return t;if(l>u){var d=Es(l/10);return 0===d?t:d<=u?void 0===r[d-1]?s.charAt(1):r[d-1]+s.charAt(1):t}c=r[l-1]}return void 0===c?"":c}))}}));var Ns=Be("iterator"),Os=Be("toStringTag"),Rs=Po.values;for(var Ls in On){var bs=r[Ls],ws=bs&&bs.prototype;if(ws){if(ws[Ns]!==Rs)try{R(ws,Ns,Rs)}catch(YI){ws[Ns]=Rs}if(ws[Os]||R(ws,Os,Ls),On[Ls])for(var Gs in Po)if(ws[Gs]!==Po[Gs])try{R(ws,Gs,Po[Gs])}catch(YI){ws[Gs]=Po[Gs]}}}var Ps=Qt.trim,Us=r.parseFloat,Fs=1/Us(Wt+"-0")!=-1/0?function(e){var t=Ps(String(e)),n=Us(t);return 0===n&&"-"==t.charAt(0)?-0:n}:Us;be({global:!0,forced:parseFloat!=Fs},{parseFloat:Fs});var qs=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;Gn(this,e),this.high=t,this.low=n}return Un(e,[{key:"equal",value:function(e){return null!==e&&this.low===e.low&&this.high===e.high}},{key:"toString",value:function(){var e=Number(this.high).toString(16),t=Number(this.low).toString(16);if(t.length<8)for(var n=8-t.length;n;)t="0"+t,n--;return e+t}}]),e}(),xs={TEST:{CHINA:{DEFAULT:"wss://wss-dev.tim.qq.com"},OVERSEA:{DEFAULT:"wss://wss-dev.tim.qq.com"},SINGAPORE:{DEFAULT:"wss://wsssgp-dev.im.qcloud.com"},KOREA:{DEFAULT:"wss://wsskr-dev.im.qcloud.com"},GERMANY:{DEFAULT:"wss://wssger-dev.im.qcloud.com"}},PRODUCTION:{CHINA:{DEFAULT:"wss://wss.im.qcloud.com",BACKUP:"wss://wss.tim.qq.com"},OVERSEA:{DEFAULT:"wss://wss.im.qcloud.com"},SINGAPORE:{DEFAULT:"wss://wsssgp.im.qcloud.com"},KOREA:{DEFAULT:"wss://wsskr.im.qcloud.com"},GERMANY:{DEFAULT:"wss://wssger.im.qcloud.com"}}},Vs={WEB:7,WX_MP:8,QQ_MP:9,TT_MP:10,BAIDU_MP:11,ALI_MP:12,UNI_NATIVE_APP:15},Bs="1.7.3",Ks=537048168,Hs="CHINA",js="OVERSEA",Ws="SINGAPORE",Ys="KOREA",zs="GERMANY",Js={HOST:{CURRENT:{DEFAULT:"wss://wss.im.qcloud.com"},setCurrent:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Hs;this.CURRENT=xs.PRODUCTION[e]}},NAME:{OPEN_IM:"openim",GROUP:"group_open_http_svc",GROUP_ATTR:"group_open_attr_http_svc",FRIEND:"sns",PROFILE:"profile",RECENT_CONTACT:"recentcontact",PIC:"openpic",BIG_GROUP_NO_AUTH:"group_open_http_noauth_svc",BIG_GROUP_LONG_POLLING:"group_open_long_polling_http_svc",BIG_GROUP_LONG_POLLING_NO_AUTH:"group_open_long_polling_http_noauth_svc",IM_OPEN_STAT:"imopenstat",WEB_IM:"webim",IM_COS_SIGN:"im_cos_sign_svr",CUSTOM_UPLOAD:"im_cos_msg",HEARTBEAT:"heartbeat",IM_OPEN_PUSH:"im_open_push",IM_OPEN_STATUS:"im_open_status",IM_LONG_MESSAGE:"im_long_msg",IM_CONFIG_MANAGER:"im_sdk_config_mgr",STAT_SERVICE:"StatSvc",OVERLOAD_PUSH:"OverLoadPush"},CMD:{ACCESS_LAYER:"accesslayer",LOGIN:"wslogin",LOGOUT_LONG_POLL:"longpollinglogout",LOGOUT:"wslogout",HELLO:"wshello",PORTRAIT_GET:"portrait_get_all",PORTRAIT_SET:"portrait_set",GET_LONG_POLL_ID:"getlongpollingid",LONG_POLL:"longpolling",AVCHATROOM_LONG_POLL:"get_msg",ADD_FRIEND:"friend_add",UPDATE_FRIEND:"friend_update",GET_FRIEND_LIST:"friend_get",GET_FRIEND_PROFILE:"friend_get_list",DELETE_FRIEND:"friend_delete",CHECK_FRIEND:"friend_check",GET_FRIEND_GROUP_LIST:"group_get",RESPOND_FRIEND_APPLICATION:"friend_response",GET_FRIEND_APPLICATION_LIST:"pendency_get",DELETE_FRIEND_APPLICATION:"pendency_delete",REPORT_FRIEND_APPLICATION:"pendency_report",GET_GROUP_APPLICATION:"get_pendency",CREATE_FRIEND_GROUP:"group_add",DELETE_FRIEND_GROUP:"group_delete",UPDATE_FRIEND_GROUP:"group_update",GET_BLACKLIST:"black_list_get",ADD_BLACKLIST:"black_list_add",DELETE_BLACKLIST:"black_list_delete",CREATE_GROUP:"create_group",GET_JOINED_GROUPS:"get_joined_group_list",SET_GROUP_ATTRIBUTES:"set_group_attr",MODIFY_GROUP_ATTRIBUTES:"modify_group_attr",DELETE_GROUP_ATTRIBUTES:"delete_group_attr",CLEAR_GROUP_ATTRIBUTES:"clear_group_attr",GET_GROUP_ATTRIBUTES:"get_group_attr",SEND_MESSAGE:"sendmsg",REVOKE_C2C_MESSAGE:"msgwithdraw",DELETE_C2C_MESSAGE:"delete_c2c_msg_ramble",SEND_GROUP_MESSAGE:"send_group_msg",REVOKE_GROUP_MESSAGE:"group_msg_recall",DELETE_GROUP_MESSAGE:"delete_group_ramble_msg_by_seq",GET_GROUP_INFO:"get_group_self_member_info",GET_GROUP_MEMBER_INFO:"get_specified_group_member_info",GET_GROUP_MEMBER_LIST:"get_group_member_info",QUIT_GROUP:"quit_group",CHANGE_GROUP_OWNER:"change_group_owner",DESTROY_GROUP:"destroy_group",ADD_GROUP_MEMBER:"add_group_member",DELETE_GROUP_MEMBER:"delete_group_member",SEARCH_GROUP_BY_ID:"get_group_public_info",APPLY_JOIN_GROUP:"apply_join_group",HANDLE_APPLY_JOIN_GROUP:"handle_apply_join_group",HANDLE_GROUP_INVITATION:"handle_invite_join_group",MODIFY_GROUP_INFO:"modify_group_base_info",MODIFY_GROUP_MEMBER_INFO:"modify_group_member_info",DELETE_GROUP_SYSTEM_MESSAGE:"deletemsg",DELETE_GROUP_AT_TIPS:"deletemsg",GET_CONVERSATION_LIST:"get",PAGING_GET_CONVERSATION_LIST:"page_get",DELETE_CONVERSATION:"delete",PIN_CONVERSATION:"top",GET_MESSAGES:"getmsg",GET_C2C_ROAM_MESSAGES:"getroammsg",SET_C2C_PEER_MUTE_NOTIFICATIONS:"set_c2c_peer_mute_notifications",GET_C2C_PEER_MUTE_NOTIFICATIONS:"get_c2c_peer_mute_notifications",GET_GROUP_ROAM_MESSAGES:"group_msg_get",SET_C2C_MESSAGE_READ:"msgreaded",GET_PEER_READ_TIME:"get_peer_read_time",SET_GROUP_MESSAGE_READ:"msg_read_report",FILE_READ_AND_WRITE_AUTHKEY:"authkey",FILE_UPLOAD:"pic_up",COS_SIGN:"cos",COS_PRE_SIG:"pre_sig",TIM_WEB_REPORT_V2:"tim_web_report_v2",BIG_DATA_HALLWAY_AUTH_KEY:"authkey",GET_ONLINE_MEMBER_NUM:"get_online_member_num",ALIVE:"alive",MESSAGE_PUSH:"msg_push",MESSAGE_PUSH_ACK:"ws_msg_push_ack",STATUS_FORCEOFFLINE:"stat_forceoffline",DOWNLOAD_MERGER_MESSAGE:"get_relay_json_msg",UPLOAD_MERGER_MESSAGE:"save_relay_json_msg",FETCH_CLOUD_CONTROL_CONFIG:"fetch_config",PUSHED_CLOUD_CONTROL_CONFIG:"push_configv2",FETCH_COMMERCIAL_CONFIG:"fetch_imsdk_purchase_bitsv2",PUSHED_COMMERCIAL_CONFIG:"push_imsdk_purchase_bitsv2",KICK_OTHER:"KickOther",OVERLOAD_NOTIFY:"notify2",SET_ALL_MESSAGE_READ:"read_all_unread_msg"},CHANNEL:{SOCKET:1,XHR:2,AUTO:0},NAME_VERSION:{openim:"v4",group_open_http_svc:"v4",sns:"v4",profile:"v4",recentcontact:"v4",openpic:"v4",group_open_http_noauth_svc:"v4",group_open_long_polling_http_svc:"v4",group_open_long_polling_http_noauth_svc:"v4",imopenstat:"v4",im_cos_sign_svr:"v4",im_cos_msg:"v4",webim:"v4",im_open_push:"v4",im_open_status:"v4"}},Xs={SEARCH_MSG:new qs(0,Math.pow(2,0)).toString(),SEARCH_GRP_SNS:new qs(0,Math.pow(2,1)).toString(),AVCHATROOM_HISTORY_MSG:new qs(0,Math.pow(2,2)).toString(),GRP_COMMUNITY:new qs(0,Math.pow(2,3)).toString(),MSG_TO_SPECIFIED_GRP_MBR:new qs(0,Math.pow(2,4)).toString()};Js.HOST.setCurrent(Hs);var Qs,$s,Zs,ei="undefined"!=typeof wx&&"function"==typeof wx.getSystemInfoSync&&Boolean(wx.getSystemInfoSync().fontSizeSetting),ti="undefined"!=typeof qq&&"function"==typeof qq.getSystemInfoSync&&Boolean(qq.getSystemInfoSync().fontSizeSetting),ni="undefined"!=typeof tt&&"function"==typeof tt.getSystemInfoSync&&Boolean(tt.getSystemInfoSync().fontSizeSetting),oi="undefined"!=typeof swan&&"function"==typeof swan.getSystemInfoSync&&Boolean(swan.getSystemInfoSync().fontSizeSetting),ri="undefined"!=typeof my&&"function"==typeof my.getSystemInfoSync&&Boolean(my.getSystemInfoSync().fontSizeSetting),ai="undefined"!=typeof uni&&"undefined"==typeof window,si=ei||ti||ni||oi||ri||ai,ii=("undefined"!=typeof uni||"undefined"!=typeof window)&&!si,ui=ti?qq:ni?tt:oi?swan:ri?my:ei?wx:ai?uni:{},ci=(Qs="WEB",Ii?Qs="WEB":ti?Qs="QQ_MP":ni?Qs="TT_MP":oi?Qs="BAIDU_MP":ri?Qs="ALI_MP":ei?Qs="WX_MP":ai&&(Qs="UNI_NATIVE_APP"),Vs[Qs]),li=ii&&window&&window.navigator&&window.navigator.userAgent||"",di=/AppleWebKit\/([\d.]+)/i.exec(li),pi=(di&&parseFloat(di.pop()),/iPad/i.test(li)),gi=/iPhone/i.test(li)&&!pi,hi=/iPod/i.test(li),fi=gi||pi||hi,_i=(function(){var e=li.match(/OS (\d+)_/i);e&&e[1]&&e[1]}(),/Android/i.test(li)),mi=(function(){var e=li.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;var t=e[1]&&parseFloat(e[1]),n=e[2]&&parseFloat(e[2]);t&&n&&parseFloat(e[1]+"."+e[2])}(),_i&&/webkit/i.test(li),/Firefox/i.test(li),/Edge/i.test(li)),vi=(!mi&&/Chrome/i.test(li),function(){var e=li.match(/Chrome\/(\d+)/);e&&e[1]&&parseFloat(e[1])}(),/MSIE/.test(li)),Mi=(/MSIE\s8\.0/.test(li),function(){var e=/MSIE\s(\d+)\.\d/.exec(li),t=e&&parseFloat(e[1]);return!t&&/Trident\/7.0/i.test(li)&&/rv:11.0/.test(li)&&(t=11),t}()),yi=(/Safari/i.test(li),/TBS\/\d+/i.test(li)),Ii=(function(){var e=li.match(/TBS\/(\d+)/i);e&&e[1]&&e[1]}(),!yi&&/MQQBrowser\/\d+/i.test(li),!yi&&/ QQBrowser\/\d+/i.test(li),/(micromessenger|webbrowser)/i.test(li)),Ti=/Windows/i.test(li),Ci=/MAC OS X/i.test(li),Si=(/MicroMessenger/i.test(li),ii&&"undefined"!=typeof Worker),Ai=!a((function(){return Object.isExtensible(Object.preventExtensions({}))})),Ei=t((function(e){var t=O.f,n=H("meta"),o=0,r=Object.isExtensible||function(){return!0},a=function(e){t(e,n,{value:{objectID:"O"+ ++o,weakData:{}}})},i=e.exports={REQUIRED:!1,fastKey:function(e,t){if(!v(e))return"symbol"==s(e)?e:("string"==typeof e?"S":"P")+e;if(!I(e,n)){if(!r(e))return"F";if(!t)return"E";a(e)}return e[n].objectID},getWeakData:function(e,t){if(!I(e,n)){if(!r(e))return!0;if(!t)return!1;a(e)}return e[n].weakData},onFreeze:function(e){return Ai&&i.REQUIRED&&r(e)&&!I(e,n)&&a(e),e}};Y[n]=!0})),ki=(Ei.REQUIRED,Ei.fastKey,Ei.getWeakData,Ei.onFreeze,O.f),Di=Ei.fastKey,Ni=ne.set,Oi=ne.getterFor,Ri=(function(e,t,n){var o=-1!==e.indexOf("Map"),s=-1!==e.indexOf("Weak"),i=o?"set":"add",u=r[e],c=u&&u.prototype,l=u,d={},p=function(e){var t=c[e];oe(c,e,"add"==e?function(e){return t.call(this,0===e?0:e),this}:"delete"==e?function(e){return!(s&&!v(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return s&&!v(e)?void 0:t.call(this,0===e?0:e)}:"has"==e?function(e){return!(s&&!v(e))&&t.call(this,0===e?0:e)}:function(e,n){return t.call(this,0===e?0:e,n),this})};if(Re(e,"function"!=typeof u||!(s||c.forEach&&!a((function(){(new u).entries().next()})))))l=n.getConstructor(t,e,o,i),Ei.REQUIRED=!0;else if(Re(e,!0)){var g=new l,h=g[i](s?{}:-0,1)!=g,f=a((function(){g.has(1)})),_=wt((function(e){new u(e)})),m=!s&&a((function(){for(var e=new u,t=5;t--;)e[i](t,t);return!e.has(-0)}));_||((l=t((function(t,n){Rr(t,l,e);var r=or(new u,t,l);return null!=n&&Lr(n,r[i],r,o),r}))).prototype=c,c.constructor=l),(f||m)&&(p("delete"),p("has"),o&&p("get")),(m||h)&&p(i),s&&c.clear&&delete c.clear}d[e]=l,be({global:!0,forced:l!=u},d),mn(l,e),s||n.setStrong(l,e,o)}("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),{getConstructor:function(e,t,n,o){var r=e((function(e,a){Rr(e,r,t),Ni(e,{type:t,index:Ht(null),first:void 0,last:void 0,size:0}),i||(e.size=0),null!=a&&Lr(a,e[o],e,n)})),a=Oi(t),s=function(e,t,n){var o,r,s=a(e),c=u(e,t);return c?c.value=n:(s.last=c={index:r=Di(t,!0),key:t,value:n,previous:o=s.last,next:void 0,removed:!1},s.first||(s.first=c),o&&(o.next=c),i?s.size++:e.size++,"F"!==r&&(s.index[r]=c)),e},u=function(e,t){var n,o=a(e),r=Di(t);if("F"!==r)return o.index[r];for(n=o.first;n;n=n.next)if(n.key==t)return n};return Dr(r.prototype,{clear:function(){for(var e=a(this),t=e.index,n=e.first;n;)n.removed=!0,n.previous&&(n.previous=n.previous.next=void 0),delete t[n.index],n=n.next;e.first=e.last=void 0,i?e.size=0:this.size=0},delete:function(e){var t=a(this),n=u(this,e);if(n){var o=n.next,r=n.previous;delete t.index[n.index],n.removed=!0,r&&(r.next=o),o&&(o.previous=r),t.first==n&&(t.first=o),t.last==n&&(t.last=r),i?t.size--:this.size--}return!!n},forEach:function(e){for(var t,n=a(this),o=rt(e,arguments.length>1?arguments[1]:void 0,3);t=t?t.next:n.first;)for(o(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!u(this,e)}}),Dr(r.prototype,n?{get:function(e){var t=u(this,e);return t&&t.value},set:function(e,t){return s(this,0===e?0:e,t)}}:{add:function(e){return s(this,e=0===e?0:e,e)}}),i&&ki(r.prototype,"size",{get:function(){return a(this).size}}),r},setStrong:function(e,t,n){var o=t+" Iterator",r=Oi(t),a=Oi(o);En(e,t,(function(e,t){Ni(this,{type:o,target:e,state:r(e),kind:t,last:void 0})}),(function(){for(var e=a(this),t=e.kind,n=e.last;n&&n.removed;)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?"keys"==t?{value:n.key,done:!1}:"values"==t?{value:n.value,done:!1}:{value:[n.key,n.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),n?"entries":"values",!n,!0),Or(t)}}),"undefined"!=typeof o?o:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});$s="undefined"!=typeof console?console:void 0!==Ri&&Ri.console?Ri.console:"undefined"!=typeof window&&window.console?window.console:{};for(var Li=function(){},bi=["assert","clear","count","debug","dir","dirxml","error","exception","group","groupCollapsed","groupEnd","info","log","markTimeline","profile","profileEnd","table","time","timeEnd","timeStamp","trace","warn"],wi=bi.length;wi--;)Zs=bi[wi],function(){}||($s[Zs]=Li);$s.methods=bi;var Gi=$s,Pi=0,Ui=function(){return(new Date).getTime()+Pi},Fi=function(){Pi=0},qi=0,xi=new Map;function Vi(){var e,t=((e=new Date).setTime(Ui()),e);return"TIM "+t.toLocaleTimeString("en-US",{hour12:!1})+"."+function(e){var t;switch(e.toString().length){case 1:t="00"+e;break;case 2:t="0"+e;break;default:t=e}return t}(t.getMilliseconds())+":"}var Bi={arguments2String:function(e){var t;if(1===e.length)t=Vi()+e[0];else{t=Vi();for(var n=0,o=e.length;n<o;n++)Xi(e[n])?$i(e[n])?t+=ru(e[n]):t+=JSON.stringify(e[n]):t+=e[n],t+=" "}return t},debug:function(){if(qi<=-1){var e=this.arguments2String(arguments);Gi.debug(e)}},log:function(){if(qi<=0){var e=this.arguments2String(arguments);Gi.log(e)}},info:function(){if(qi<=1){var e=this.arguments2String(arguments);Gi.info(e)}},warn:function(){if(qi<=2){var e=this.arguments2String(arguments);Gi.warn(e)}},error:function(){if(qi<=3){var e=this.arguments2String(arguments);Gi.error(e)}},time:function(e){xi.set(e,nu.now())},timeEnd:function(e){if(xi.has(e)){var t=nu.now()-xi.get(e);return xi.delete(e),t}return Gi.warn("未找到对应label: ".concat(e,", 请在调用 logger.timeEnd 前，调用 logger.time")),0},setLevel:function(e){e<4&&Gi.log(Vi()+"set level from "+qi+" to "+e),qi=e},getLevel:function(){return qi}},Ki=function(e){return"file"===Zi(e)},Hi=function(e){return null!==e&&("number"==typeof e&&!isNaN(e-0)||"object"===wn(e)&&e.constructor===Number)},ji=function(e){return"string"==typeof e},Wi=function(e){return null!==e&&"object"===wn(e)},Yi=function(e){if("object"!==wn(e)||null===e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return t===n},zi=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"array"===Zi(e)},Ji=function(e){return void 0===e},Xi=function(e){return zi(e)||Wi(e)},Qi=function(e){return"function"==typeof e},$i=function(e){return e instanceof Error},Zi=function(e){return Object.prototype.toString.call(e).match(/^\[object (.*)\]$/)[1].toLowerCase()},eu=function(e){if("string"!=typeof e)return!1;var t=e[0];return!/[^a-zA-Z0-9]/.test(t)},tu=0;Date.now||(Date.now=function(){return(new Date).getTime()});var nu={now:function(){0===tu&&(tu=Date.now()-1);var e=Date.now()-tu;return e>4294967295?(tu+=4294967295,Date.now()-tu):e},utc:function(){return Math.round(Date.now()/1e3)}},ou=function e(t,n,o,r){if(!Xi(t)||!Xi(n))return 0;for(var a,s=0,i=Object.keys(n),u=0,c=i.length;u<c;u++)if(a=i[u],!(Ji(n[a])||o&&o.includes(a)))if(Xi(t[a])&&Xi(n[a]))s+=e(t[a],n[a],o,r);else{if(r&&r.includes(n[a]))continue;t[a]!==n[a]&&(t[a]=n[a],s+=1)}return s},ru=function(e){return JSON.stringify(e,["message","code"])},au=function(e){if(0===e.length)return 0;for(var t=0,n=0,o="undefined"!=typeof document&&void 0!==document.characterSet?document.characterSet:"UTF-8";void 0!==e[t];)n+=e[t++].charCodeAt[t]<=255?1:!1===o?3:2;return n},su=function(e){var t=e||99999999;return Math.round(Math.random()*t)},iu="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",uu=iu.length,cu=function(e,t){for(var n in e)if(e[n]===t)return!0;return!1},lu={},du=function(){if(si)return"https:";if(ii&&"undefined"==typeof window)return"https:";var e=window.location.protocol;return["http:","https:"].indexOf(e)<0&&(e="http:"),e},pu=function(e){return-1===e.indexOf("http://")||-1===e.indexOf("https://")?"https://"+e:e.replace(/https|http/,"https")},gu=function e(t){if(0===Object.getOwnPropertyNames(t).length)return Object.create(null);var n=Array.isArray(t)?[]:Object.create(null),o="";for(var r in t)null!==t[r]?void 0!==t[r]?(o=wn(t[r]),["string","number","function","boolean"].indexOf(o)>=0?n[r]=t[r]:n[r]=e(t[r])):n[r]=void 0:n[r]=null;return n};function hu(e,t){zi(e)&&zi(t)?t.forEach((function(t){var n=t.key,o=t.value,r=e.find((function(e){return e.key===n}));r?r.value=o:e.push({key:n,value:o})})):Bi.warn("updateCustomField target 或 source 不是数组，忽略此次更新。")}var fu=function(e){return e===so.GRP_PUBLIC},_u=function(e){return e===so.GRP_AVCHATROOM},mu=function(e){return ji(e)&&e.slice(0,3)===so.CONV_C2C},vu=function(e){return ji(e)&&e.slice(0,5)===so.CONV_GROUP},Mu=function(e){return ji(e)&&e===so.CONV_SYSTEM};function yu(e,t){var n={};return Object.keys(e).forEach((function(o){n[o]=t(e[o],o)})),n}function Iu(){function e(){return(65536*(1+Math.random())|0).toString(16).substring(1)}return"".concat(e()+e()).concat(e()).concat(e()).concat(e()).concat(e()).concat(e()).concat(e())}function Tu(e){var t=e.originUrl,n=void 0===t?void 0:t,o=e.originWidth,r=e.originHeight,a=e.min,s=void 0===a?198:a,i=parseInt(o),u=parseInt(r),c={url:void 0,width:0,height:0};if((i<=u?i:u)<=s)c.url=n,c.width=i,c.height=u;else{u<=i?(c.width=Math.ceil(i*s/u),c.height=s):(c.width=s,c.height=Math.ceil(u*s/i));var l=n&&n.indexOf("?")>-1?"".concat(n,"&"):"".concat(n,"?");c.url="".concat(l,198===s?"imageView2/3/w/198/h/198":"imageView2/3/w/720/h/720")}return Ji(n)?Yn(c,["url"]):c}function Cu(e){var t=e[2];e[2]=e[1],e[1]=t;for(var n=0;n<e.length;n++)e[n].setType(n)}function Su(e){var t=e.servcmd;return t.slice(t.indexOf(".")+1)}function Au(e,t){return Math.round(Number(e)*Math.pow(10,t))/Math.pow(10,t)}function Eu(e,t){return e.includes(t)}function ku(e,t){return e.includes(t)}var Du=Object.prototype.hasOwnProperty;function Nu(e){if(null==e)return!0;if("boolean"==typeof e)return!1;if("number"==typeof e)return 0===e;if("string"==typeof e)return 0===e.length;if("function"==typeof e)return 0===e.length;if(Array.isArray(e))return 0===e.length;if(e instanceof Error)return""===e.message;if(Yi(e)){for(var t in e)if(Du.call(e,t))return!1;return!0}return!("map"!==Zi(e)&&!function(e){return"set"===Zi(e)}(e)&&!Ki(e))&&0===e.size}function Ou(e,t,n){if(void 0===t)return!0;var o=!0;if("object"===yo(t).toLowerCase())Object.keys(t).forEach((function(r){var a=1===e.length?e[0][r]:void 0;o=!!Ru(a,t[r],n,r)&&o}));else if("array"===yo(t).toLowerCase())for(var r=0;r<t.length;r++)o=!!Ru(e[r],t[r],n,t[r].name)&&o;if(o)return o;throw new Error("Params validate failed.")}function Ru(e,t,n,o){if(void 0===t)return!0;var r=!0;return t.required&&Nu(e)&&(Gi.error("TIM [".concat(n,'] Missing required params: "').concat(o,'".')),r=!1),Nu(e)||yo(e).toLowerCase()===t.type.toLowerCase()||(Gi.error("TIM [".concat(n,'] Invalid params: type check failed for "').concat(o,'".Expected ').concat(t.type,".")),r=!1),t.validator&&!t.validator(e)&&(Gi.error("TIM [".concat(n,"] Invalid params: custom validator check failed for params.")),r=!1),r}var Lu={f:Be},bu=O.f,wu=it.forEach,Gu=W("hidden"),Pu=Be("toPrimitive"),Uu=ne.set,Fu=ne.getterFor("Symbol"),qu=Object.prototype,xu=r.Symbol,Vu=se("JSON","stringify"),Bu=k.f,Ku=O.f,Hu=Mr.f,ju=l.f,Wu=V("symbols"),Yu=V("op-symbols"),zu=V("string-to-symbol-registry"),Ju=V("symbol-to-string-registry"),Xu=V("wks"),Qu=r.QObject,$u=!Qu||!Qu.prototype||!Qu.prototype.findChild,Zu=i&&a((function(){return 7!=Ht(Ku({},"a",{get:function(){return Ku(this,"a",{value:7}).a}})).a}))?function(e,t,n){var o=Bu(qu,t);o&&delete qu[t],Ku(e,t,n),o&&e!==qu&&Ku(qu,t,o)}:Ku,ec=function(e,t){var n=Wu[e]=Ht(xu.prototype);return Uu(n,{type:"Symbol",tag:e,description:t}),i||(n.description=t),n},tc=Fe?function(e){return"symbol"==s(e)}:function(e){return Object(e)instanceof xu},nc=function e(t,n,o){t===qu&&e(Yu,n,o),D(t);var r=M(n,!0);return D(o),I(Wu,r)?(o.enumerable?(I(t,Gu)&&t[Gu][r]&&(t[Gu][r]=!1),o=Ht(o,{enumerable:d(0,!1)})):(I(t,Gu)||Ku(t,Gu,d(1,{})),t[Gu][r]=!0),Zu(t,r,o)):Ku(t,r,o)},oc=function(e,t){D(e);var n=m(t),o=Ut(n).concat(ic(n));return wu(o,(function(t){i&&!rc.call(n,t)||nc(e,t,n[t])})),e},rc=function(e){var t=M(e,!0),n=ju.call(this,t);return!(this===qu&&I(Wu,t)&&!I(Yu,t))&&(!(n||!I(this,t)||!I(Wu,t)||I(this,Gu)&&this[Gu][t])||n)},ac=function(e,t){var n=m(e),o=M(t,!0);if(n!==qu||!I(Wu,o)||I(Yu,o)){var r=Bu(n,o);return!r||!I(Wu,o)||I(n,Gu)&&n[Gu][o]||(r.enumerable=!0),r}},sc=function(e){var t=Hu(m(e)),n=[];return wu(t,(function(e){I(Wu,e)||I(Y,e)||n.push(e)})),n},ic=function(e){var t=e===qu,n=Hu(t?Yu:m(e)),o=[];return wu(n,(function(e){!I(Wu,e)||t&&!I(qu,e)||o.push(Wu[e])})),o};if(Ue||(oe((xu=function(){if(this instanceof xu)throw TypeError("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?String(arguments[0]):void 0,t=H(e),n=function e(n){this===qu&&e.call(Yu,n),I(this,Gu)&&I(this[Gu],t)&&(this[Gu][t]=!1),Zu(this,t,d(1,n))};return i&&$u&&Zu(qu,t,{configurable:!0,set:n}),ec(t,e)}).prototype,"toString",(function(){return Fu(this).tag})),oe(xu,"withoutSetter",(function(e){return ec(H(e),e)})),l.f=rc,O.f=nc,k.f=ac,Ie.f=Mr.f=sc,Te.f=ic,Lu.f=function(e){return ec(Be(e),e)},i&&(Ku(xu.prototype,"description",{configurable:!0,get:function(){return Fu(this).description}}),oe(qu,"propertyIsEnumerable",rc,{unsafe:!0}))),be({global:!0,wrap:!0,forced:!Ue,sham:!Ue},{Symbol:xu}),wu(Ut(Xu),(function(e){!function(e){var t=re.Symbol||(re.Symbol={});I(t,e)||bu(t,e,{value:Lu.f(e)})}(e)})),be({target:"Symbol",stat:!0,forced:!Ue},{for:function(e){var t=String(e);if(I(zu,t))return zu[t];var n=xu(t);return zu[t]=n,Ju[n]=t,n},keyFor:function(e){if(!tc(e))throw TypeError(e+" is not a symbol");if(I(Ju,e))return Ju[e]},useSetter:function(){$u=!0},useSimple:function(){$u=!1}}),be({target:"Object",stat:!0,forced:!Ue,sham:!i},{create:function(e,t){return void 0===t?Ht(e):oc(Ht(e),t)},defineProperty:nc,defineProperties:oc,getOwnPropertyDescriptor:ac}),be({target:"Object",stat:!0,forced:!Ue},{getOwnPropertyNames:sc,getOwnPropertySymbols:ic}),be({target:"Object",stat:!0,forced:a((function(){Te.f(1)}))},{getOwnPropertySymbols:function(e){return Te.f(Ge(e))}}),Vu){var uc=!Ue||a((function(){var e=xu();return"[null]"!=Vu([e])||"{}"!=Vu({a:e})||"{}"!=Vu(Object(e))}));be({target:"JSON",stat:!0,forced:uc},{stringify:function(e,t,n){for(var o,r=[e],a=1;arguments.length>a;)r.push(arguments[a++]);if(o=t,(v(t)||void 0!==e)&&!tc(e))return we(t)||(t=function(e,t){if("function"==typeof o&&(t=o.call(this,e,t)),!tc(t))return t}),r[1]=t,Vu.apply(null,r)}})}xu.prototype[Pu]||R(xu.prototype,Pu,xu.prototype.valueOf),mn(xu,"Symbol"),Y[Gu]=!0;var cc=O.f,lc=r.Symbol;if(i&&"function"==typeof lc&&(!("description"in lc.prototype)||void 0!==lc().description)){var dc={},pc=function e(){var t=arguments.length<1||void 0===arguments[0]?void 0:String(arguments[0]),n=this instanceof e?new lc(t):void 0===t?lc():lc(t);return""===t&&(dc[n]=!0),n};Se(pc,lc);var gc=pc.prototype=lc.prototype;gc.constructor=pc;var hc=gc.toString,fc="Symbol(test)"==String(lc("test")),_c=/^Symbol\((.*)\)[^)]+$/;cc(gc,"description",{configurable:!0,get:function(){var e=v(this)?this.valueOf():this,t=hc.call(e);if(I(dc,e))return"";var n=fc?t.slice(7,-1):t.replace(_c,"$1");return""===n?void 0:n}}),be({global:!0,forced:!0},{Symbol:pc})}var mc,vc=k.f,Mc="".startsWith,yc=Math.min,Ic=gs("startsWith"),Tc=!(Ic||(mc=vc(String.prototype,"startsWith"),!mc||mc.writable));be({target:"String",proto:!0,forced:!Tc&&!Ic},{startsWith:function(e){var t=String(_(this));ds(e);var n=de(yc(arguments.length>1?arguments[1]:void 0,t.length)),o=String(e);return Mc?Mc.call(t,o,n):t.slice(n,n+o.length)===o}});var Cc={UNSEND:"unSend",SUCCESS:"success",FAIL:"fail"},Sc={NOT_START:"notStart",PENDING:"pengding",RESOLVED:"resolved",REJECTED:"rejected"},Ac=function(e){return!!e&&(!!(mu(e)||vu(e)||Mu(e))||!1)},Ec={type:"String",required:!0},kc={type:"Array",required:!0},Dc={type:"Object",required:!0},Nc={login:{userID:Ec,userSig:Ec},addToBlacklist:{userIDList:kc},on:[{name:"eventName",type:"String",validator:function(e){return"string"==typeof e&&0!==e.length||!1}},{name:"handler",type:"Function",validator:function(e){return"function"==typeof e&&(e.name,!0)}}],once:[{name:"eventName",type:"String",validator:function(e){return"string"==typeof e&&0!==e.length||!1}},{name:"handler",type:"Function",validator:function(e){return"function"==typeof e&&(e.name,!0)}}],off:[{name:"eventName",type:"String",validator:function(e){return"string"==typeof e&&0!==e.length||!1}},{name:"handler",type:"Function",validator:function(e){return"function"==typeof e&&(e.name,!0)}}],sendMessage:[xn({name:"message"},Dc)],getMessageList:{conversationID:xn(xn({},Ec),{},{validator:function(e){return Ac(e)}}),nextReqMessageID:{type:"String"},count:{type:"Number",validator:function(e){return!(!Ji(e)&&!/^[1-9][0-9]*$/.test(e))||!1}}},setMessageRead:{conversationID:xn(xn({},Ec),{},{validator:function(e){return Ac(e)}})},setAllMessageRead:{scope:{type:"String",required:!1,validator:function(e){return!e||-1!==[so.READ_ALL_C2C_MSG,so.READ_ALL_GROUP_MSG,so.READ_ALL_MSG].indexOf(e)||!1}}},getConversationProfile:[xn(xn({name:"conversationID"},Ec),{},{validator:function(e){return Ac(e)}})],deleteConversation:[xn(xn({name:"conversationID"},Ec),{},{validator:function(e){return Ac(e)}})],pinConversation:{conversationID:xn(xn({},Ec),{},{validator:function(e){return Ac(e)}}),isPinned:xn({},{type:"Boolean",required:!0})},getConversationList:[{name:"options",type:"Array",validator:function(e){return!!Ji(e)||0!==e.length||!1}}],getGroupList:{groupProfileFilter:{type:"Array"}},getGroupProfile:{groupID:Ec,groupCustomFieldFilter:{type:"Array"},memberCustomFieldFilter:{type:"Array"}},getGroupProfileAdvance:{groupIDList:kc},createGroup:{name:Ec},joinGroup:{groupID:Ec,type:{type:"String"},applyMessage:{type:"String"}},quitGroup:[xn({name:"groupID"},Ec)],handleApplication:{message:Dc,handleAction:Ec,handleMessage:{type:"String"}},changeGroupOwner:{groupID:Ec,newOwnerID:Ec},updateGroupProfile:{groupID:Ec,muteAllMembers:{type:"Boolean"}},dismissGroup:[xn({name:"groupID"},Ec)],searchGroupByID:[xn({name:"groupID"},Ec)],initGroupAttributes:{groupID:Ec,groupAttributes:xn(xn({},Dc),{},{validator:function(e){var t=!0;return Object.keys(e).forEach((function(n){if(!ji(e[n]))return t=!1})),t}})},setGroupAttributes:{groupID:Ec,groupAttributes:xn(xn({},Dc),{},{validator:function(e){var t=!0;return Object.keys(e).forEach((function(n){if(!ji(e[n]))return t=!1})),t}})},deleteGroupAttributes:{groupID:Ec,keyList:{type:"Array",validator:function(e){if(Ji(e))return!1;if(!zi(e))return!1;if(!Nu(e)){var t=!0;return e.forEach((function(e){if(!ji(e))return t=!1})),t}return!0}}},getGroupAttributes:{groupID:Ec,keyList:{type:"Array",validator:function(e){if(Ji(e))return!1;if(!zi(e))return!1;if(!Nu(e)){var t=!0;return e.forEach((function(e){if(!ji(e))return t=!1})),t}return!0}}},getGroupMemberList:{groupID:Ec,offset:{type:"Number"},count:{type:"Number"}},getGroupMemberProfile:{groupID:Ec,userIDList:kc,memberCustomFieldFilter:{type:"Array"}},addGroupMember:{groupID:Ec,userIDList:kc},setGroupMemberRole:{groupID:Ec,userID:Ec,role:Ec},setGroupMemberMuteTime:{groupID:Ec,userID:Ec,muteTime:{type:"Number",validator:function(e){return e>=0}}},setGroupMemberNameCard:{groupID:Ec,userID:{type:"String"},nameCard:{type:"String",validator:function(e){return!!ji(e)&&(e.length,!0)}}},setGroupMemberCustomField:{groupID:Ec,userID:{type:"String"},memberCustomField:kc},deleteGroupMember:{groupID:Ec},createTextMessage:{to:Ec,conversationType:Ec,payload:xn(xn({},Dc),{},{validator:function(e){return Yi(e)&&ji(e.text)&&0!==e.text.length||!1}})},createTextAtMessage:{to:Ec,conversationType:Ec,payload:xn(xn({},Dc),{},{validator:function(e){return!!Yi(e)&&(!!ji(e.text)&&(0!==e.text.length&&(!(e.atUserList&&!zi(e.atUserList))||!1)))}})},createCustomMessage:{to:Ec,conversationType:Ec,payload:xn(xn({},Dc),{},{validator:function(e){return!!Yi(e)&&(!(e.data&&!ji(e.data))&&(!(e.description&&!ji(e.description))&&(!(e.extension&&!ji(e.extension))||!1)))}})},createImageMessage:{to:Ec,conversationType:Ec,payload:xn(xn({},Dc),{},{validator:function(e){if(!Yi(e))return!1;if(Ji(e.file))return!1;if(ii){if(!(e.file instanceof HTMLInputElement||Ki(e.file)))return Yi(e.file)&&"undefined"!=typeof uni&&0!==e.file.tempFilePaths.length&&0!==e.file.tempFiles.length||!1;if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return!1}return!0},onProgress:{type:"Function",required:!1,validator:function(e){return Ji(e),!0}}})},createAudioMessage:{to:Ec,conversationType:Ec,payload:xn(xn({},Dc),{},{validator:function(e){return!!Yi(e)||!1}}),onProgress:{type:"Function",required:!1,validator:function(e){return Ji(e),!0}}},createVideoMessage:{to:Ec,conversationType:Ec,payload:xn(xn({},Dc),{},{validator:function(e){if(!Yi(e))return!1;if(Ji(e.file))return!1;if(ii){if(!(e.file instanceof HTMLInputElement||Ki(e.file)))return Yi(e.file)&&"undefined"!=typeof uni&&!!Ki(e.file.tempFile)||!1;if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return!1}return!0}}),onProgress:{type:"Function",required:!1,validator:function(e){return Ji(e),!0}}},createFaceMessage:{to:Ec,conversationType:Ec,payload:xn(xn({},Dc),{},{validator:function(e){return Yi(e)&&Hi(e.index)&&!!ji(e.data)||!1}})},createFileMessage:{to:Ec,conversationType:Ec,payload:xn(xn({},Dc),{},{validator:function(e){if(!Yi(e))return!1;if(Ji(e.file))return!1;if(ii){if(!(e.file instanceof HTMLInputElement||Ki(e.file)))return Yi(e.file)&&"undefined"!=typeof uni&&0!==e.file.tempFilePaths.length&&0!==e.file.tempFiles.length||!1;if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return!1}return!0}}),onProgress:{type:"Function",required:!1,validator:function(e){return Ji(e),!0}}},createLocationMessage:{to:Ec,conversationType:Ec,payload:xn(xn({},Dc),{},{validator:function(e){return Yi(e)&&ji(e.description)&&Hi(e.longitude)&&!!Hi(e.latitude)||!1}})},createMergerMessage:{to:Ec,conversationType:Ec,payload:xn(xn({},Dc),{},{validator:function(e){if(Nu(e.messageList))return!1;if(Nu(e.compatibleText))return!1;var t=!1;return e.messageList.forEach((function(e){e.status===Cc.FAIL&&(t=!0)})),!t||!1}})},revokeMessage:[xn(xn({name:"message"},Dc),{},{validator:function(e){return!Nu(e)&&(e.conversationType!==so.CONV_SYSTEM&&(!0!==e.isRevoked||!1))}})],deleteMessage:[xn(xn({name:"messageList"},kc),{},{validator:function(e){return!Nu(e)||!1}})],getUserProfile:{userIDList:{type:"Array",validator:function(e){return!!zi(e)&&(e.length,!0)}}},updateMyProfile:{profileCustomField:{type:"Array",validator:function(e){return!!Ji(e)||!!zi(e)||!1}}},addFriend:{to:Ec,source:{type:"String",required:!0,validator:function(e){return!!e&&(e.startsWith("AddSource_Type_")&&!(e.replace("AddSource_Type_","").length>8)||!1)}},remark:{type:"String",required:!1,validator:function(e){return!(ji(e)&&e.length>96)||!1}}},deleteFriend:{userIDList:kc},checkFriend:{userIDList:kc},getFriendProfile:{userIDList:kc},updateFriend:{userID:Ec,remark:{type:"String",required:!1,validator:function(e){return!(ji(e)&&e.length>96)||!1}},friendCustomField:{type:"Array",required:!1,validator:function(e){if(e){if(!zi(e))return!1;var t=!0;return e.forEach((function(e){return ji(e.key)&&-1!==e.key.indexOf("Tag_SNS_Custom")&&ji(e.value)?e.value.length>8?t=!1:void 0:t=!1})),t}return!0}}},acceptFriendApplication:{userID:Ec},refuseFriendApplication:{userID:Ec},deleteFriendApplication:{userID:Ec},createFriendGroup:{name:Ec},deleteFriendGroup:{name:Ec},addToFriendGroup:{name:Ec,userIDList:kc},removeFromFriendGroup:{name:Ec,userIDList:kc},renameFriendGroup:{oldName:Ec,newName:Ec}},Oc={login:"login",logout:"logout",on:"on",once:"once",off:"off",setLogLevel:"setLogLevel",registerPlugin:"registerPlugin",destroy:"destroy",createTextMessage:"createTextMessage",createTextAtMessage:"createTextAtMessage",createImageMessage:"createImageMessage",createAudioMessage:"createAudioMessage",createVideoMessage:"createVideoMessage",createCustomMessage:"createCustomMessage",createFaceMessage:"createFaceMessage",createFileMessage:"createFileMessage",createLocationMessage:"createLocationMessage",createMergerMessage:"createMergerMessage",downloadMergerMessage:"downloadMergerMessage",createForwardMessage:"createForwardMessage",sendMessage:"sendMessage",resendMessage:"resendMessage",revokeMessage:"revokeMessage",deleteMessage:"deleteMessage",getMessageList:"getMessageList",setMessageRead:"setMessageRead",setAllMessageRead:"setAllMessageRead",getConversationList:"getConversationList",getConversationProfile:"getConversationProfile",deleteConversation:"deleteConversation",pinConversation:"pinConversation",getGroupList:"getGroupList",getGroupProfile:"getGroupProfile",createGroup:"createGroup",joinGroup:"joinGroup",updateGroupProfile:"updateGroupProfile",quitGroup:"quitGroup",dismissGroup:"dismissGroup",changeGroupOwner:"changeGroupOwner",searchGroupByID:"searchGroupByID",setMessageRemindType:"setMessageRemindType",handleGroupApplication:"handleGroupApplication",initGroupAttributes:"initGroupAttributes",setGroupAttributes:"setGroupAttributes",deleteGroupAttributes:"deleteGroupAttributes",getGroupAttributes:"getGroupAttributes",getGroupMemberProfile:"getGroupMemberProfile",getGroupMemberList:"getGroupMemberList",addGroupMember:"addGroupMember",deleteGroupMember:"deleteGroupMember",setGroupMemberNameCard:"setGroupMemberNameCard",setGroupMemberMuteTime:"setGroupMemberMuteTime",setGroupMemberRole:"setGroupMemberRole",setGroupMemberCustomField:"setGroupMemberCustomField",getGroupOnlineMemberCount:"getGroupOnlineMemberCount",getMyProfile:"getMyProfile",getUserProfile:"getUserProfile",updateMyProfile:"updateMyProfile",getBlacklist:"getBlacklist",addToBlacklist:"addToBlacklist",removeFromBlacklist:"removeFromBlacklist",getFriendList:"getFriendList",addFriend:"addFriend",deleteFriend:"deleteFriend",checkFriend:"checkFriend",updateFriend:"updateFriend",getFriendProfile:"getFriendProfile",getFriendApplicationList:"getFriendApplicationList",refuseFriendApplication:"refuseFriendApplication",deleteFriendApplication:"deleteFriendApplication",acceptFriendApplication:"acceptFriendApplication",setFriendApplicationRead:"setFriendApplicationRead",getFriendGroupList:"getFriendGroupList",createFriendGroup:"createFriendGroup",renameFriendGroup:"renameFriendGroup",deleteFriendGroup:"deleteFriendGroup",addToFriendGroup:"addToFriendGroup",removeFromFriendGroup:"removeFromFriendGroup",callExperimentalAPI:"callExperimentalAPI"},Rc=!!kr&&a((function(){kr.prototype.finally.call({then:function(){}},(function(){}))}));be({target:"Promise",proto:!0,real:!0,forced:Rc},{finally:function(e){var t=wr(this,se("Promise")),n="function"==typeof e;return this.then(n?function(n){return va(t,e()).then((function(){return n}))}:e,n?function(n){return va(t,e()).then((function(){throw n}))}:e)}}),"function"!=typeof kr||kr.prototype.finally||oe(kr.prototype,"finally",se("Promise").prototype.finally);var Lc=[].slice,bc=/MSIE .\./.test(je),wc=function(e){return function(t,n){var o=arguments.length>2,r=o?Lc.call(arguments,2):void 0;return e(o?function(){("function"==typeof t?t:Function(t)).apply(this,r)}:t,n)}};be({global:!0,bind:!0,forced:bc},{setTimeout:wc(r.setTimeout),setInterval:wc(r.setInterval)});var Gc=it.filter,Pc=Qe("filter"),Uc=pt("filter");be({target:"Array",proto:!0,forced:!Pc||!Uc},{filter:function(e){return Gc(this,e,arguments.length>1?arguments[1]:void 0)}});var Fc,qc="sign",xc="message",Vc="user",Bc="c2c",Kc="group",Hc="sns",jc="groupMember",Wc="conversation",Yc="context",zc="storage",Jc="eventStat",Xc="netMonitor",Qc="bigDataChannel",$c="upload",Zc="plugin",el="syncUnreadMessage",tl="session",nl="channel",ol="message_loss_detection",rl="cloudControl",al="worker",sl="pullGroupMessage",il="qualityStat",ul="commercialConfig",cl=function(){function e(t){Gn(this,e),this._moduleManager=t,this._className=""}return Un(e,[{key:"isLoggedIn",value:function(){return this._moduleManager.getModule(Yc).isLoggedIn()}},{key:"isOversea",value:function(){return this._moduleManager.getModule(Yc).isOversea()}},{key:"getMyUserID",value:function(){return this._moduleManager.getModule(Yc).getUserID()}},{key:"getModule",value:function(e){return this._moduleManager.getModule(e)}},{key:"getPlatform",value:function(){return ci}},{key:"getNetworkType",value:function(){return this._moduleManager.getModule(Xc).getNetworkType()}},{key:"probeNetwork",value:function(){return this._moduleManager.getModule(Xc).probe()}},{key:"getCloudConfig",value:function(e){return this._moduleManager.getModule(rl).getCloudConfig(e)}},{key:"emitOuterEvent",value:function(e,t){this._moduleManager.getOuterEmitterInstance().emit(e,t)}},{key:"emitInnerEvent",value:function(e,t){this._moduleManager.getInnerEmitterInstance().emit(e,t)}},{key:"getInnerEmitterInstance",value:function(){return this._moduleManager.getInnerEmitterInstance()}},{key:"generateTjgID",value:function(e){return this._moduleManager.getModule(Yc).getTinyID()+"-"+e.random}},{key:"filterModifiedMessage",value:function(e){if(!Nu(e)){var t=e.filter((function(e){return!0===e.isModified}));t.length>0&&this.emitOuterEvent(ao.MESSAGE_MODIFIED,t)}}},{key:"filterUnmodifiedMessage",value:function(e){return Nu(e)?[]:e.filter((function(e){return!1===e.isModified}))}},{key:"request",value:function(e){return this._moduleManager.getModule(tl).request(e)}},{key:"canIUse",value:function(e){return this._moduleManager.getModule(ul).hasPurchasedFeature(e)}}]),e}(),ll="wslogin",dl="wslogout",pl="wshello",gl="KickOther",hl="getmsg",fl="authkey",_l="sendmsg",ml="send_group_msg",vl="portrait_get_all",Ml="portrait_set",yl="black_list_get",Il="black_list_add",Tl="black_list_delete",Cl="msgwithdraw",Sl="msgreaded",Al="set_c2c_peer_mute_notifications",El="get_c2c_peer_mute_notifications",kl="getroammsg",Dl="get_peer_read_time",Nl="delete_c2c_msg_ramble",Ol="page_get",Rl="get",Ll="delete",bl="top",wl="deletemsg",Gl="get_joined_group_list",Pl="get_group_self_member_info",Ul="create_group",Fl="destroy_group",ql="modify_group_base_info",xl="apply_join_group",Vl="apply_join_group_noauth",Bl="quit_group",Kl="get_group_public_info",Hl="change_group_owner",jl="handle_apply_join_group",Wl="handle_invite_join_group",Yl="group_msg_recall",zl="msg_read_report",Jl="read_all_unread_msg",Xl="group_msg_get",Ql="get_pendency",$l="deletemsg",Zl="get_msg",ed="get_msg_noauth",td="get_online_member_num",nd="delete_group_ramble_msg_by_seq",od="set_group_attr",rd="modify_group_attr",ad="delete_group_attr",sd="clear_group_attr",id="get_group_attr",ud="get_group_member_info",cd="get_specified_group_member_info",ld="add_group_member",dd="delete_group_member",pd="modify_group_member_info",gd="cos",hd="pre_sig",fd="tim_web_report_v2",_d="alive",md="msg_push",vd="ws_msg_push_ack",Md="stat_forceoffline",yd="save_relay_json_msg",Id="get_relay_json_msg",Td="fetch_config",Cd="push_configv2",Sd="fetch_imsdk_purchase_bitsv2",Ad="push_imsdk_purchase_bitsv2",Ed="notify2",kd={NO_SDKAPPID:2e3,NO_ACCOUNT_TYPE:2001,NO_IDENTIFIER:2002,NO_USERSIG:2003,NO_TINYID:2022,NO_A2KEY:2023,USER_NOT_LOGGED_IN:2024,REPEAT_LOGIN:2025,COS_UNDETECTED:2040,COS_GET_SIG_FAIL:2041,MESSAGE_SEND_FAIL:2100,MESSAGE_LIST_CONSTRUCTOR_NEED_OPTIONS:2103,MESSAGE_SEND_NEED_MESSAGE_INSTANCE:2105,MESSAGE_SEND_INVALID_CONVERSATION_TYPE:2106,MESSAGE_FILE_IS_EMPTY:2108,MESSAGE_ONPROGRESS_FUNCTION_ERROR:2109,MESSAGE_REVOKE_FAIL:2110,MESSAGE_DELETE_FAIL:2111,MESSAGE_UNREAD_ALL_FAIL:2112,MESSAGE_IMAGE_SELECT_FILE_FIRST:2251,MESSAGE_IMAGE_TYPES_LIMIT:2252,MESSAGE_IMAGE_SIZE_LIMIT:2253,MESSAGE_AUDIO_UPLOAD_FAIL:2300,MESSAGE_AUDIO_SIZE_LIMIT:2301,MESSAGE_VIDEO_UPLOAD_FAIL:2350,MESSAGE_VIDEO_SIZE_LIMIT:2351,MESSAGE_VIDEO_TYPES_LIMIT:2352,MESSAGE_FILE_UPLOAD_FAIL:2400,MESSAGE_FILE_SELECT_FILE_FIRST:2401,MESSAGE_FILE_SIZE_LIMIT:2402,MESSAGE_FILE_URL_IS_EMPTY:2403,MESSAGE_MERGER_TYPE_INVALID:2450,MESSAGE_MERGER_KEY_INVALID:2451,MESSAGE_MERGER_DOWNLOAD_FAIL:2452,MESSAGE_FORWARD_TYPE_INVALID:2453,CONVERSATION_NOT_FOUND:2500,USER_OR_GROUP_NOT_FOUND:2501,CONVERSATION_UN_RECORDED_TYPE:2502,ILLEGAL_GROUP_TYPE:2600,CANNOT_JOIN_WORK:2601,CANNOT_CHANGE_OWNER_IN_AVCHATROOM:2620,CANNOT_CHANGE_OWNER_TO_SELF:2621,CANNOT_DISMISS_Work:2622,MEMBER_NOT_IN_GROUP:2623,CANNOT_USE_GRP_ATTR_NOT_AVCHATROOM:2641,CANNOT_USE_GRP_ATTR_AVCHATROOM_UNJOIN:2642,JOIN_GROUP_FAIL:2660,CANNOT_ADD_MEMBER_IN_AVCHATROOM:2661,CANNOT_JOIN_NON_AVCHATROOM_WITHOUT_LOGIN:2662,CANNOT_KICK_MEMBER_IN_AVCHATROOM:2680,NOT_OWNER:2681,CANNOT_SET_MEMBER_ROLE_IN_WORK_AND_AVCHATROOM:2682,INVALID_MEMBER_ROLE:2683,CANNOT_SET_SELF_MEMBER_ROLE:2684,CANNOT_MUTE_SELF:2685,NOT_MY_FRIEND:2700,ALREADY_MY_FRIEND:2701,FRIEND_GROUP_EXISTED:2710,FRIEND_GROUP_NOT_EXIST:2711,FRIEND_APPLICATION_NOT_EXIST:2716,UPDATE_PROFILE_INVALID_PARAM:2721,UPDATE_PROFILE_NO_KEY:2722,ADD_BLACKLIST_INVALID_PARAM:2740,DEL_BLACKLIST_INVALID_PARAM:2741,CANNOT_ADD_SELF_TO_BLACKLIST:2742,ADD_FRIEND_INVALID_PARAM:2760,NETWORK_ERROR:2800,NETWORK_TIMEOUT:2801,NETWORK_BASE_OPTIONS_NO_URL:2802,NETWORK_UNDEFINED_SERVER_NAME:2803,NETWORK_PACKAGE_UNDEFINED:2804,NO_NETWORK:2805,CONVERTOR_IRREGULAR_PARAMS:2900,NOTICE_RUNLOOP_UNEXPECTED_CONDITION:2901,NOTICE_RUNLOOP_OFFSET_LOST:2902,UNCAUGHT_ERROR:2903,GET_LONGPOLL_ID_FAILED:2904,INVALID_OPERATION:2905,OVER_FREQUENCY_LIMIT:2996,CANNOT_FIND_PROTOCOL:2997,CANNOT_FIND_MODULE:2998,SDK_IS_NOT_READY:2999,LONG_POLL_KICK_OUT:91101,MESSAGE_A2KEY_EXPIRED:20002,ACCOUNT_A2KEY_EXPIRED:70001,LONG_POLL_API_PARAM_ERROR:90001,HELLO_ANSWER_KICKED_OUT:1002,OPEN_SERVICE_OVERLOAD_ERROR:60022},Dd="无 SDKAppID",Nd="无 userID",Od="无 userSig",Rd="无 tinyID",Ld="无 a2key",bd="用户未登录",wd="重复登录",Gd="未检测到 COS 上传插件",Pd="获取 COS 预签名 URL 失败",Ud="消息发送失败",Fd="需要 Message 的实例",qd='Message.conversationType 只能为 "C2C" 或 "GROUP"',xd="无法发送空文件",Vd="回调函数运行时遇到错误，请检查接入侧代码",Bd="消息撤回失败",Kd="消息删除失败",Hd="设置所有未读消息为已读处理失败",jd="请先选择一个图片",Wd="只允许上传 jpg png jpeg gif bmp image 格式的图片",Yd="图片大小超过20M，无法发送",zd="语音上传失败",Jd="语音大小大于20M，无法发送",Xd="视频上传失败",Qd="视频大小超过100M，无法发送",$d="只允许上传 mp4 格式的视频",Zd="文件上传失败",ep="请先选择一个文件",tp="文件大小超过100M，无法发送 ",np="缺少必要的参数文件 URL",op="非合并消息",rp="合并消息的 messageKey 无效",ap="下载合并消息失败",sp="选择的消息类型（如群提示消息）不可以转发",ip="没有找到相应的会话，请检查传入参数",up="没有找到相应的用户或群组，请检查传入参数",cp="未记录的会话类型",lp="非法的群类型，请检查传入参数",dp="不能加入 Work 类型的群组",pp="AVChatRoom 类型的群组不能转让群主",gp="不能把群主转让给自己",hp="不能解散 Work 类型的群组",fp="用户不在该群组内",_p="加群失败，请检查传入参数或重试",mp="AVChatRoom 类型的群不支持邀请群成员",vp="非 AVChatRoom 类型的群组不允许匿名加群，请先登录后再加群",Mp="不能在 AVChatRoom 类型的群组踢人",yp="你不是群主，只有群主才有权限操作",Ip="不能在 Work / AVChatRoom 类型的群中设置群成员身份",Tp="不合法的群成员身份，请检查传入参数",Cp="不能设置自己的群成员身份，请检查传入参数",Sp="不能将自己禁言，请检查传入参数",Ap="传入 updateMyProfile 接口的参数无效",Ep="updateMyProfile 无标配资料字段或自定义资料字段",kp="传入 addToBlacklist 接口的参数无效",Dp="传入 removeFromBlacklist 接口的参数无效",Np="不能拉黑自己",Op="网络错误",Rp="请求超时",Lp="未连接到网络",bp="无效操作，如调用了未定义或者未实现的方法等",wp="无法找到协议",Gp="无法找到模块",Pp="接口需要 SDK 处于 ready 状态后才能调用",Up="超出 SDK 频率控制",Fp="后台服务正忙，请稍后再试",qp="networkRTT",xp="messageE2EDelay",Vp="sendMessageC2C",Bp="sendMessageGroup",Kp="sendMessageGroupAV",Hp="sendMessageRichMedia",jp="cosUpload",Wp="messageReceivedGroup",Yp="messageReceivedGroupAVPush",zp="messageReceivedGroupAVPull",Jp=(Fn(Fc={},qp,2),Fn(Fc,xp,3),Fn(Fc,Vp,4),Fn(Fc,Bp,5),Fn(Fc,Kp,6),Fn(Fc,Hp,7),Fn(Fc,Wp,8),Fn(Fc,Yp,9),Fn(Fc,zp,10),Fn(Fc,jp,11),Fc),Xp={info:4,warning:5,error:6},Qp={wifi:1,"2g":2,"3g":3,"4g":4,"5g":5,unknown:6,none:7,online:8},$p={login:4},Zp=function(){function e(t){Gn(this,e),this.eventType=$p[t]||0,this.timestamp=0,this.networkType=8,this.code=0,this.message="",this.moreMessage="",this.extension=t,this.costTime=0,this.duplicate=!1,this.level=4,this._sentFlag=!1,this._startts=Ui()}return Un(e,[{key:"updateTimeStamp",value:function(){this.timestamp=Ui()}},{key:"start",value:function(e){return this._startts=e,this}},{key:"end",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this._sentFlag){var n=Ui();0===this.costTime&&(this.costTime=n-this._startts),this.setMoreMessage("startts:".concat(this._startts," endts:").concat(n)),t?(this._sentFlag=!0,this._eventStatModule&&this._eventStatModule.pushIn(this)):setTimeout((function(){e._sentFlag=!0,e._eventStatModule&&e._eventStatModule.pushIn(e)}),0)}}},{key:"setError",value:function(e,t,n){return e instanceof Error?(this._sentFlag||(this.setNetworkType(n),t?(e.code&&this.setCode(e.code),e.message&&this.setMoreMessage(e.message)):(this.setCode(kd.NO_NETWORK),this.setMoreMessage(Lp)),this.setLevel("error")),this):(Bi.warn("SSOLogData.setError value not instanceof Error, please check!"),this)}},{key:"setCode",value:function(e){return Ji(e)||this._sentFlag||("ECONNABORTED"===e&&(this.code=103),Hi(e)?this.code=e:Bi.warn("SSOLogData.setCode value not a number, please check!",e,wn(e))),this}},{key:"setMessage",value:function(e){return Ji(e)||this._sentFlag||(Hi(e)&&(this.message=e.toString()),ji(e)&&(this.message=e)),this}},{key:"setCostTime",value:function(e){return this.costTime=e,this}},{key:"setLevel",value:function(e){return Ji(e)||this._sentFlag||(this.level=Xp[e]),this}},{key:"setMoreMessage",value:function(e){return Nu(this.moreMessage)?this.moreMessage="".concat(e):this.moreMessage+=" ".concat(e),this}},{key:"setNetworkType",value:function(e){if(Ji(e))Bi.warn("SSOLogData.setNetworkType value is undefined, please check!");else{var t=Qp[e.toLowerCase()];Ji(t)||(this.networkType=t)}return this}},{key:"getStartTs",value:function(){return this._startts}}],[{key:"bindEventStatModule",value:function(t){e.prototype._eventStatModule=t}}]),e}(),eg="sdkConstruct",tg="sdkReady",ng="login",og="logout",rg="kickedOut",ag="registerPlugin",sg="kickOther",ig="wsConnect",ug="wsOnOpen",cg="wsOnClose",lg="wsOnError",dg="getCosAuthKey",pg="getCosPreSigUrl",gg="upload",hg="sendMessage",fg="getC2CRoamingMessages",_g="getGroupRoamingMessages",mg="revokeMessage",vg="deleteMessage",Mg="setC2CMessageRead",yg="setGroupMessageRead",Ig="emptyMessageBody",Tg="getPeerReadTime",Cg="uploadMergerMessage",Sg="downloadMergerMessage",Ag="jsonParseError",Eg="messageE2EDelayException",kg="getConversationList",Dg="getConversationProfile",Ng="deleteConversation",Og="pinConversation",Rg="getConversationListInStorage",Lg="syncConversationList",bg="setAllMessageRead",wg="createGroup",Gg="applyJoinGroup",Pg="quitGroup",Ug="searchGroupByID",Fg="changeGroupOwner",qg="handleGroupApplication",xg="handleGroupInvitation",Vg="setMessageRemindType",Bg="dismissGroup",Kg="updateGroupProfile",Hg="getGroupList",jg="getGroupProfile",Wg="getGroupListInStorage",Yg="getGroupLastSequence",zg="getGroupMissingMessage",Jg="pagingGetGroupList",Xg="getGroupSimplifiedInfo",Qg="joinWithoutAuth",$g="initGroupAttributes",Zg="setGroupAttributes",eh="deleteGroupAttributes",th="getGroupAttributes",nh="getGroupMemberList",oh="getGroupMemberProfile",rh="addGroupMember",ah="deleteGroupMember",sh="setGroupMemberMuteTime",ih="setGroupMemberNameCard",uh="setGroupMemberRole",ch="setGroupMemberCustomField",lh="getGroupOnlineMemberCount",dh="longPollingAVError",ph="messageLoss",gh="messageStacked",hh="getUserProfile",fh="updateMyProfile",_h="getBlacklist",mh="addToBlacklist",vh="removeFromBlacklist",Mh="callbackFunctionError",yh="fetchCloudControlConfig",Ih="pushedCloudControlConfig",Th="fetchCommercialConfig",Ch="pushedCommercialConfig",Sh="error",Ah="lastMessageNotExist",Eh=l.f,kh=function(e){return function(t){for(var n,o=m(t),r=Ut(o),a=r.length,s=0,u=[];a>s;)n=r[s++],i&&!Eh.call(o,n)||u.push(e?[n,o[n]]:o[n]);return u}},Dh={entries:kh(!0),values:kh(!1)}.values;be({target:"Object",stat:!0},{values:function(e){return Dh(e)}});var Nh=function(){function e(t){Gn(this,e),this.type=so.MSG_TEXT,this.content={text:t.text||""}}return Un(e,[{key:"setText",value:function(e){this.content.text=e}},{key:"sendable",value:function(){return 0!==this.content.text.length}}]),e}(),Oh=Object.assign,Rh=Object.defineProperty,Lh=!Oh||a((function(){if(i&&1!==Oh({b:1},Oh(Rh({},"a",{enumerable:!0,get:function(){Rh(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol();return e[n]=7,"abcdefghijklmnopqrst".split("").forEach((function(e){t[e]=e})),7!=Oh({},e)[n]||"abcdefghijklmnopqrst"!=Ut(Oh({},t)).join("")}))?function(e,t){for(var n=Ge(e),o=arguments.length,r=1,a=Te.f,s=l.f;o>r;)for(var u,c=f(arguments[r++]),d=a?Ut(c).concat(a(c)):Ut(c),p=d.length,g=0;p>g;)u=d[g++],i&&!s.call(c,u)||(n[u]=c[u]);return n}:Oh;be({target:"Object",stat:!0,forced:Object.assign!==Lh},{assign:Lh});var bh=Be("iterator"),wh=!a((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),t=e.searchParams,n="";return e.pathname="c%20d",t.forEach((function(e,o){t.delete("b"),n+=o+e})),!t.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[bh]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==n||"x"!==new URL("http://x",void 0).host})),Gh=/[^\0-\u007E]/,Ph=/[.\u3002\uFF0E\uFF61]/g,Uh="Overflow: input needs wider integers to process",Fh=Math.floor,qh=String.fromCharCode,xh=function(e){return e+22+75*(e<26)},Vh=function(e,t,n){var o=0;for(e=n?Fh(e/700):e>>1,e+=Fh(e/t);e>455;o+=36)e=Fh(e/35);return Fh(o+36*e/(e+38))},Bh=function(e){var t,n,o=[],r=(e=function(e){for(var t=[],n=0,o=e.length;n<o;){var r=e.charCodeAt(n++);if(r>=55296&&r<=56319&&n<o){var a=e.charCodeAt(n++);56320==(64512&a)?t.push(((1023&r)<<10)+(1023&a)+65536):(t.push(r),n--)}else t.push(r)}return t}(e)).length,a=128,s=0,i=72;for(t=0;t<e.length;t++)(n=e[t])<128&&o.push(qh(n));var u=o.length,c=u;for(u&&o.push("-");c<r;){var l=2147483647;for(t=0;t<e.length;t++)(n=e[t])>=a&&n<l&&(l=n);var d=c+1;if(l-a>Fh((2147483647-s)/d))throw RangeError(Uh);for(s+=(l-a)*d,a=l,t=0;t<e.length;t++){if((n=e[t])<a&&++s>2147483647)throw RangeError(Uh);if(n==a){for(var p=s,g=36;;g+=36){var h=g<=i?1:g>=i+26?26:g-i;if(p<h)break;var f=p-h,_=36-h;o.push(qh(xh(h+f%_))),p=Fh(f/_)}o.push(qh(xh(p))),i=Vh(s,d,c==u),s=0,++c}}++s,++a}return o.join("")},Kh=function(e){var t=Dt(e);if("function"!=typeof t)throw TypeError(String(e)+" is not iterable");return D(t.call(e))},Hh=se("fetch"),jh=se("Headers"),Wh=Be("iterator"),Yh=ne.set,zh=ne.getterFor("URLSearchParams"),Jh=ne.getterFor("URLSearchParamsIterator"),Xh=/\+/g,Qh=Array(4),$h=function(e){return Qh[e-1]||(Qh[e-1]=RegExp("((?:%[\\da-f]{2}){"+e+"})","gi"))},Zh=function(e){try{return decodeURIComponent(e)}catch(YI){return e}},ef=function(e){var t=e.replace(Xh," "),n=4;try{return decodeURIComponent(t)}catch(YI){for(;n;)t=t.replace($h(n--),Zh);return t}},tf=/[!'()~]|%20/g,nf={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},of=function(e){return nf[e]},rf=function(e){return encodeURIComponent(e).replace(tf,of)},af=function(e,t){if(t)for(var n,o,r=t.split("&"),a=0;a<r.length;)(n=r[a++]).length&&(o=n.split("="),e.push({key:ef(o.shift()),value:ef(o.join("="))}))},sf=function(e){this.entries.length=0,af(this.entries,e)},uf=function(e,t){if(e<t)throw TypeError("Not enough arguments")},cf=yn((function(e,t){Yh(this,{type:"URLSearchParamsIterator",iterator:Kh(zh(e).entries),kind:t})}),"Iterator",(function(){var e=Jh(this),t=e.kind,n=e.iterator.next(),o=n.value;return n.done||(n.value="keys"===t?o.key:"values"===t?o.value:[o.key,o.value]),n})),lf=function e(){Rr(this,e,"URLSearchParams");var t,n,o,r,a,s,i,u,c,l=arguments.length>0?arguments[0]:void 0,d=this,p=[];if(Yh(d,{type:"URLSearchParams",entries:p,updateURL:function(){},updateSearchParams:sf}),void 0!==l)if(v(l))if("function"==typeof(t=Dt(l)))for(o=(n=t.call(l)).next;!(r=o.call(n)).done;){if((i=(s=(a=Kh(D(r.value))).next).call(a)).done||(u=s.call(a)).done||!s.call(a).done)throw TypeError("Expected sequence with length 2");p.push({key:i.value+"",value:u.value+""})}else for(c in l)I(l,c)&&p.push({key:c,value:l[c]+""});else af(p,"string"==typeof l?"?"===l.charAt(0)?l.slice(1):l:l+"")},df=lf.prototype;Dr(df,{append:function(e,t){uf(arguments.length,2);var n=zh(this);n.entries.push({key:e+"",value:t+""}),n.updateURL()},delete:function(e){uf(arguments.length,1);for(var t=zh(this),n=t.entries,o=e+"",r=0;r<n.length;)n[r].key===o?n.splice(r,1):r++;t.updateURL()},get:function(e){uf(arguments.length,1);for(var t=zh(this).entries,n=e+"",o=0;o<t.length;o++)if(t[o].key===n)return t[o].value;return null},getAll:function(e){uf(arguments.length,1);for(var t=zh(this).entries,n=e+"",o=[],r=0;r<t.length;r++)t[r].key===n&&o.push(t[r].value);return o},has:function(e){uf(arguments.length,1);for(var t=zh(this).entries,n=e+"",o=0;o<t.length;)if(t[o++].key===n)return!0;return!1},set:function(e,t){uf(arguments.length,1);for(var n,o=zh(this),r=o.entries,a=!1,s=e+"",i=t+"",u=0;u<r.length;u++)(n=r[u]).key===s&&(a?r.splice(u--,1):(a=!0,n.value=i));a||r.push({key:s,value:i}),o.updateURL()},sort:function(){var e,t,n,o=zh(this),r=o.entries,a=r.slice();for(r.length=0,n=0;n<a.length;n++){for(e=a[n],t=0;t<n;t++)if(r[t].key>e.key){r.splice(t,0,e);break}t===n&&r.push(e)}o.updateURL()},forEach:function(e){for(var t,n=zh(this).entries,o=rt(e,arguments.length>1?arguments[1]:void 0,3),r=0;r<n.length;)o((t=n[r++]).value,t.key,this)},keys:function(){return new cf(this,"keys")},values:function(){return new cf(this,"values")},entries:function(){return new cf(this,"entries")}},{enumerable:!0}),oe(df,Wh,df.entries),oe(df,"toString",(function(){for(var e,t=zh(this).entries,n=[],o=0;o<t.length;)e=t[o++],n.push(rf(e.key)+"="+rf(e.value));return n.join("&")}),{enumerable:!0}),mn(lf,"URLSearchParams"),be({global:!0,forced:!wh},{URLSearchParams:lf}),wh||"function"!=typeof Hh||"function"!=typeof jh||be({global:!0,enumerable:!0,forced:!0},{fetch:function(e){var t,n,o,r=[e];return arguments.length>1&&(v(t=arguments[1])&&(n=t.body,"URLSearchParams"===Et(n)&&((o=t.headers?new jh(t.headers):new jh).has("content-type")||o.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),t=Ht(t,{body:d(0,String(n)),headers:d(0,o)}))),r.push(t)),Hh.apply(this,r)}});var pf,gf={URLSearchParams:lf,getState:zh},hf=sn.codeAt,ff=r.URL,_f=gf.URLSearchParams,mf=gf.getState,vf=ne.set,Mf=ne.getterFor("URL"),yf=Math.floor,If=Math.pow,Tf=/[A-Za-z]/,Cf=/[\d+-.A-Za-z]/,Sf=/\d/,Af=/^(0x|0X)/,Ef=/^[0-7]+$/,kf=/^\d+$/,Df=/^[\dA-Fa-f]+$/,Nf=/[\u0000\u0009\u000A\u000D #%/:?@[\\]]/,Of=/[\u0000\u0009\u000A\u000D #/:?@[\\]]/,Rf=/^[\u0000-\u001F ]+|[\u0000-\u001F ]+$/g,Lf=/[\u0009\u000A\u000D]/g,bf=function(e,t){var n,o,r;if("["==t.charAt(0)){if("]"!=t.charAt(t.length-1))return"Invalid host";if(!(n=Gf(t.slice(1,-1))))return"Invalid host";e.host=n}else if(Kf(e)){if(t=function(e){var t,n,o=[],r=e.toLowerCase().replace(Ph,".").split(".");for(t=0;t<r.length;t++)n=r[t],o.push(Gh.test(n)?"xn--"+Bh(n):n);return o.join(".")}(t),Nf.test(t))return"Invalid host";if(null===(n=wf(t)))return"Invalid host";e.host=n}else{if(Of.test(t))return"Invalid host";for(n="",o=Nt(t),r=0;r<o.length;r++)n+=Vf(o[r],Uf);e.host=n}},wf=function(e){var t,n,o,r,a,s,i,u=e.split(".");if(u.length&&""==u[u.length-1]&&u.pop(),(t=u.length)>4)return e;for(n=[],o=0;o<t;o++){if(""==(r=u[o]))return e;if(a=10,r.length>1&&"0"==r.charAt(0)&&(a=Af.test(r)?16:8,r=r.slice(8==a?1:2)),""===r)s=0;else{if(!(10==a?kf:8==a?Ef:Df).test(r))return e;s=parseInt(r,a)}n.push(s)}for(o=0;o<t;o++)if(s=n[o],o==t-1){if(s>=If(256,5-t))return null}else if(s>255)return null;for(i=n.pop(),o=0;o<n.length;o++)i+=n[o]*If(256,3-o);return i},Gf=function(e){var t,n,o,r,a,s,i,u=[0,0,0,0,0,0,0,0],c=0,l=null,d=0,p=function(){return e.charAt(d)};if(":"==p()){if(":"!=e.charAt(1))return;d+=2,l=++c}for(;p();){if(8==c)return;if(":"!=p()){for(t=n=0;n<4&&Df.test(p());)t=16*t+parseInt(p(),16),d++,n++;if("."==p()){if(0==n)return;if(d-=n,c>6)return;for(o=0;p();){if(r=null,o>0){if(!("."==p()&&o<4))return;d++}if(!Sf.test(p()))return;for(;Sf.test(p());){if(a=parseInt(p(),10),null===r)r=a;else{if(0==r)return;r=10*r+a}if(r>255)return;d++}u[c]=256*u[c]+r,2!=++o&&4!=o||c++}if(4!=o)return;break}if(":"==p()){if(d++,!p())return}else if(p())return;u[c++]=t}else{if(null!==l)return;d++,l=++c}}if(null!==l)for(s=c-l,c=7;0!=c&&s>0;)i=u[c],u[c--]=u[l+s-1],u[l+--s]=i;else if(8!=c)return;return u},Pf=function(e){var t,n,o,r;if("number"==typeof e){for(t=[],n=0;n<4;n++)t.unshift(e%256),e=yf(e/256);return t.join(".")}if("object"==s(e)){for(t="",o=function(e){for(var t=null,n=1,o=null,r=0,a=0;a<8;a++)0!==e[a]?(r>n&&(t=o,n=r),o=null,r=0):(null===o&&(o=a),++r);return r>n&&(t=o,n=r),t}(e),n=0;n<8;n++)r&&0===e[n]||(r&&(r=!1),o===n?(t+=n?":":"::",r=!0):(t+=e[n].toString(16),n<7&&(t+=":")));return"["+t+"]"}return e},Uf={},Ff=Lh({},Uf,{" ":1,'"':1,"<":1,">":1,"`":1}),qf=Lh({},Ff,{"#":1,"?":1,"{":1,"}":1}),xf=Lh({},qf,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),Vf=function(e,t){var n=hf(e,0);return n>32&&n<127&&!I(t,e)?e:encodeURIComponent(e)},Bf={ftp:21,file:null,http:80,https:443,ws:80,wss:443},Kf=function(e){return I(Bf,e.scheme)},Hf=function(e){return""!=e.username||""!=e.password},jf=function(e){return!e.host||e.cannotBeABaseURL||"file"==e.scheme},Wf=function(e,t){var n;return 2==e.length&&Tf.test(e.charAt(0))&&(":"==(n=e.charAt(1))||!t&&"|"==n)},Yf=function(e){var t;return e.length>1&&Wf(e.slice(0,2))&&(2==e.length||"/"===(t=e.charAt(2))||"\\"===t||"?"===t||"#"===t)},zf=function(e){var t=e.path,n=t.length;!n||"file"==e.scheme&&1==n&&Wf(t[0],!0)||t.pop()},Jf=function(e){return"."===e||"%2e"===e.toLowerCase()},Xf={},Qf={},$f={},Zf={},e_={},t_={},n_={},o_={},r_={},a_={},s_={},i_={},u_={},c_={},l_={},d_={},p_={},g_={},h_={},f_={},__={},m_=function(e,t,n,o){var r,a,s,i,u,c=n||Xf,l=0,d="",p=!1,g=!1,h=!1;for(n||(e.scheme="",e.username="",e.password="",e.host=null,e.port=null,e.path=[],e.query=null,e.fragment=null,e.cannotBeABaseURL=!1,t=t.replace(Rf,"")),t=t.replace(Lf,""),r=Nt(t);l<=r.length;){switch(a=r[l],c){case Xf:if(!a||!Tf.test(a)){if(n)return"Invalid scheme";c=$f;continue}d+=a.toLowerCase(),c=Qf;break;case Qf:if(a&&(Cf.test(a)||"+"==a||"-"==a||"."==a))d+=a.toLowerCase();else{if(":"!=a){if(n)return"Invalid scheme";d="",c=$f,l=0;continue}if(n&&(Kf(e)!=I(Bf,d)||"file"==d&&(Hf(e)||null!==e.port)||"file"==e.scheme&&!e.host))return;if(e.scheme=d,n)return void(Kf(e)&&Bf[e.scheme]==e.port&&(e.port=null));d="","file"==e.scheme?c=c_:Kf(e)&&o&&o.scheme==e.scheme?c=Zf:Kf(e)?c=o_:"/"==r[l+1]?(c=e_,l++):(e.cannotBeABaseURL=!0,e.path.push(""),c=h_)}break;case $f:if(!o||o.cannotBeABaseURL&&"#"!=a)return"Invalid scheme";if(o.cannotBeABaseURL&&"#"==a){e.scheme=o.scheme,e.path=o.path.slice(),e.query=o.query,e.fragment="",e.cannotBeABaseURL=!0,c=__;break}c="file"==o.scheme?c_:t_;continue;case Zf:if("/"!=a||"/"!=r[l+1]){c=t_;continue}c=r_,l++;break;case e_:if("/"==a){c=a_;break}c=g_;continue;case t_:if(e.scheme=o.scheme,a==pf)e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,e.path=o.path.slice(),e.query=o.query;else if("/"==a||"\\"==a&&Kf(e))c=n_;else if("?"==a)e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,e.path=o.path.slice(),e.query="",c=f_;else{if("#"!=a){e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,e.path=o.path.slice(),e.path.pop(),c=g_;continue}e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,e.path=o.path.slice(),e.query=o.query,e.fragment="",c=__}break;case n_:if(!Kf(e)||"/"!=a&&"\\"!=a){if("/"!=a){e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,c=g_;continue}c=a_}else c=r_;break;case o_:if(c=r_,"/"!=a||"/"!=d.charAt(l+1))continue;l++;break;case r_:if("/"!=a&&"\\"!=a){c=a_;continue}break;case a_:if("@"==a){p&&(d="%40"+d),p=!0,s=Nt(d);for(var f=0;f<s.length;f++){var _=s[f];if(":"!=_||h){var m=Vf(_,xf);h?e.password+=m:e.username+=m}else h=!0}d=""}else if(a==pf||"/"==a||"?"==a||"#"==a||"\\"==a&&Kf(e)){if(p&&""==d)return"Invalid authority";l-=Nt(d).length+1,d="",c=s_}else d+=a;break;case s_:case i_:if(n&&"file"==e.scheme){c=d_;continue}if(":"!=a||g){if(a==pf||"/"==a||"?"==a||"#"==a||"\\"==a&&Kf(e)){if(Kf(e)&&""==d)return"Invalid host";if(n&&""==d&&(Hf(e)||null!==e.port))return;if(i=bf(e,d))return i;if(d="",c=p_,n)return;continue}"["==a?g=!0:"]"==a&&(g=!1),d+=a}else{if(""==d)return"Invalid host";if(i=bf(e,d))return i;if(d="",c=u_,n==i_)return}break;case u_:if(!Sf.test(a)){if(a==pf||"/"==a||"?"==a||"#"==a||"\\"==a&&Kf(e)||n){if(""!=d){var v=parseInt(d,10);if(v>65535)return"Invalid port";e.port=Kf(e)&&v===Bf[e.scheme]?null:v,d=""}if(n)return;c=p_;continue}return"Invalid port"}d+=a;break;case c_:if(e.scheme="file","/"==a||"\\"==a)c=l_;else{if(!o||"file"!=o.scheme){c=g_;continue}if(a==pf)e.host=o.host,e.path=o.path.slice(),e.query=o.query;else if("?"==a)e.host=o.host,e.path=o.path.slice(),e.query="",c=f_;else{if("#"!=a){Yf(r.slice(l).join(""))||(e.host=o.host,e.path=o.path.slice(),zf(e)),c=g_;continue}e.host=o.host,e.path=o.path.slice(),e.query=o.query,e.fragment="",c=__}}break;case l_:if("/"==a||"\\"==a){c=d_;break}o&&"file"==o.scheme&&!Yf(r.slice(l).join(""))&&(Wf(o.path[0],!0)?e.path.push(o.path[0]):e.host=o.host),c=g_;continue;case d_:if(a==pf||"/"==a||"\\"==a||"?"==a||"#"==a){if(!n&&Wf(d))c=g_;else if(""==d){if(e.host="",n)return;c=p_}else{if(i=bf(e,d))return i;if("localhost"==e.host&&(e.host=""),n)return;d="",c=p_}continue}d+=a;break;case p_:if(Kf(e)){if(c=g_,"/"!=a&&"\\"!=a)continue}else if(n||"?"!=a)if(n||"#"!=a){if(a!=pf&&(c=g_,"/"!=a))continue}else e.fragment="",c=__;else e.query="",c=f_;break;case g_:if(a==pf||"/"==a||"\\"==a&&Kf(e)||!n&&("?"==a||"#"==a)){if(".."===(u=(u=d).toLowerCase())||"%2e."===u||".%2e"===u||"%2e%2e"===u?(zf(e),"/"==a||"\\"==a&&Kf(e)||e.path.push("")):Jf(d)?"/"==a||"\\"==a&&Kf(e)||e.path.push(""):("file"==e.scheme&&!e.path.length&&Wf(d)&&(e.host&&(e.host=""),d=d.charAt(0)+":"),e.path.push(d)),d="","file"==e.scheme&&(a==pf||"?"==a||"#"==a))for(;e.path.length>1&&""===e.path[0];)e.path.shift();"?"==a?(e.query="",c=f_):"#"==a&&(e.fragment="",c=__)}else d+=Vf(a,qf);break;case h_:"?"==a?(e.query="",c=f_):"#"==a?(e.fragment="",c=__):a!=pf&&(e.path[0]+=Vf(a,Uf));break;case f_:n||"#"!=a?a!=pf&&("'"==a&&Kf(e)?e.query+="%27":e.query+="#"==a?"%23":Vf(a,Uf)):(e.fragment="",c=__);break;case __:a!=pf&&(e.fragment+=Vf(a,Ff))}l++}},v_=function e(t){var n,o,r=Rr(this,e,"URL"),a=arguments.length>1?arguments[1]:void 0,s=String(t),u=vf(r,{type:"URL"});if(void 0!==a)if(a instanceof e)n=Mf(a);else if(o=m_(n={},String(a)))throw TypeError(o);if(o=m_(u,s,null,n))throw TypeError(o);var c=u.searchParams=new _f,l=mf(c);l.updateSearchParams(u.query),l.updateURL=function(){u.query=String(c)||null},i||(r.href=y_.call(r),r.origin=I_.call(r),r.protocol=T_.call(r),r.username=C_.call(r),r.password=S_.call(r),r.host=A_.call(r),r.hostname=E_.call(r),r.port=k_.call(r),r.pathname=D_.call(r),r.search=N_.call(r),r.searchParams=O_.call(r),r.hash=R_.call(r))},M_=v_.prototype,y_=function(){var e=Mf(this),t=e.scheme,n=e.username,o=e.password,r=e.host,a=e.port,s=e.path,i=e.query,u=e.fragment,c=t+":";return null!==r?(c+="//",Hf(e)&&(c+=n+(o?":"+o:"")+"@"),c+=Pf(r),null!==a&&(c+=":"+a)):"file"==t&&(c+="//"),c+=e.cannotBeABaseURL?s[0]:s.length?"/"+s.join("/"):"",null!==i&&(c+="?"+i),null!==u&&(c+="#"+u),c},I_=function(){var e=Mf(this),t=e.scheme,n=e.port;if("blob"==t)try{return new URL(t.path[0]).origin}catch(YI){return"null"}return"file"!=t&&Kf(e)?t+"://"+Pf(e.host)+(null!==n?":"+n:""):"null"},T_=function(){return Mf(this).scheme+":"},C_=function(){return Mf(this).username},S_=function(){return Mf(this).password},A_=function(){var e=Mf(this),t=e.host,n=e.port;return null===t?"":null===n?Pf(t):Pf(t)+":"+n},E_=function(){var e=Mf(this).host;return null===e?"":Pf(e)},k_=function(){var e=Mf(this).port;return null===e?"":String(e)},D_=function(){var e=Mf(this),t=e.path;return e.cannotBeABaseURL?t[0]:t.length?"/"+t.join("/"):""},N_=function(){var e=Mf(this).query;return e?"?"+e:""},O_=function(){return Mf(this).searchParams},R_=function(){var e=Mf(this).fragment;return e?"#"+e:""},L_=function(e,t){return{get:e,set:t,configurable:!0,enumerable:!0}};if(i&&Ft(M_,{href:L_(y_,(function(e){var t=Mf(this),n=String(e),o=m_(t,n);if(o)throw TypeError(o);mf(t.searchParams).updateSearchParams(t.query)})),origin:L_(I_),protocol:L_(T_,(function(e){var t=Mf(this);m_(t,String(e)+":",Xf)})),username:L_(C_,(function(e){var t=Mf(this),n=Nt(String(e));if(!jf(t)){t.username="";for(var o=0;o<n.length;o++)t.username+=Vf(n[o],xf)}})),password:L_(S_,(function(e){var t=Mf(this),n=Nt(String(e));if(!jf(t)){t.password="";for(var o=0;o<n.length;o++)t.password+=Vf(n[o],xf)}})),host:L_(A_,(function(e){var t=Mf(this);t.cannotBeABaseURL||m_(t,String(e),s_)})),hostname:L_(E_,(function(e){var t=Mf(this);t.cannotBeABaseURL||m_(t,String(e),i_)})),port:L_(k_,(function(e){var t=Mf(this);jf(t)||(""==(e=String(e))?t.port=null:m_(t,e,u_))})),pathname:L_(D_,(function(e){var t=Mf(this);t.cannotBeABaseURL||(t.path=[],m_(t,e+"",p_))})),search:L_(N_,(function(e){var t=Mf(this);""==(e=String(e))?t.query=null:("?"==e.charAt(0)&&(e=e.slice(1)),t.query="",m_(t,e,f_)),mf(t.searchParams).updateSearchParams(t.query)})),searchParams:L_(O_),hash:L_(R_,(function(e){var t=Mf(this);""!=(e=String(e))?("#"==e.charAt(0)&&(e=e.slice(1)),t.fragment="",m_(t,e,__)):t.fragment=null}))}),oe(M_,"toJSON",(function(){return y_.call(this)}),{enumerable:!0}),oe(M_,"toString",(function(){return y_.call(this)}),{enumerable:!0}),ff){var b_=ff.createObjectURL,w_=ff.revokeObjectURL;b_&&oe(v_,"createObjectURL",(function(e){return b_.apply(ff,arguments)})),w_&&oe(v_,"revokeObjectURL",(function(e){return w_.apply(ff,arguments)}))}mn(v_,"URL"),be({global:!0,forced:!wh,sham:!i},{URL:v_});var G_={JSON:{TYPE:{C2C:{NOTICE:1,COMMON:9,EVENT:10},GROUP:{COMMON:3,TIP:4,SYSTEM:5,TIP2:6},FRIEND:{NOTICE:7},PROFILE:{NOTICE:8}},SUBTYPE:{C2C:{COMMON:0,READED:92,KICKEDOUT:96},GROUP:{COMMON:0,LOVEMESSAGE:1,TIP:2,REDPACKET:3}},OPTIONS:{GROUP:{JOIN:1,QUIT:2,KICK:3,SET_ADMIN:4,CANCEL_ADMIN:5,MODIFY_GROUP_INFO:6,MODIFY_MEMBER_INFO:7}}},PROTOBUF:{},IMAGE_TYPES:{ORIGIN:1,LARGE:2,SMALL:3},IMAGE_FORMAT:{JPG:1,JPEG:1,GIF:2,PNG:3,BMP:4,UNKNOWN:255}},P_={NICK:"Tag_Profile_IM_Nick",GENDER:"Tag_Profile_IM_Gender",BIRTHDAY:"Tag_Profile_IM_BirthDay",LOCATION:"Tag_Profile_IM_Location",SELFSIGNATURE:"Tag_Profile_IM_SelfSignature",ALLOWTYPE:"Tag_Profile_IM_AllowType",LANGUAGE:"Tag_Profile_IM_Language",AVATAR:"Tag_Profile_IM_Image",MESSAGESETTINGS:"Tag_Profile_IM_MsgSettings",ADMINFORBIDTYPE:"Tag_Profile_IM_AdminForbidType",LEVEL:"Tag_Profile_IM_Level",ROLE:"Tag_Profile_IM_Role"},U_={UNKNOWN:"Gender_Type_Unknown",FEMALE:"Gender_Type_Female",MALE:"Gender_Type_Male"},F_={NONE:"AdminForbid_Type_None",SEND_OUT:"AdminForbid_Type_SendOut"},q_={NEED_CONFIRM:"AllowType_Type_NeedConfirm",ALLOW_ANY:"AllowType_Type_AllowAny",DENY_ANY:"AllowType_Type_DenyAny"},x_="JoinedSuccess",V_="WaitAdminApproval",B_=function(){function e(t){Gn(this,e),this._imageMemoryURL="",si?this.createImageDataASURLInWXMiniApp(t.file):this.createImageDataASURLInWeb(t.file),this._initImageInfoModel(),this.type=so.MSG_IMAGE,this._percent=0,this.content={imageFormat:t.imageFormat||G_.IMAGE_FORMAT.UNKNOWN,uuid:t.uuid,imageInfoArray:[]},this.initImageInfoArray(t.imageInfoArray),this._defaultImage="http://imgcache.qq.com/open/qcloud/video/act/webim-images/default.jpg",this._autoFixUrl()}return Un(e,[{key:"_initImageInfoModel",value:function(){var e=this;this._ImageInfoModel=function(t){this.instanceID=su(9999999),this.sizeType=t.type||0,this.type=0,this.size=t.size||0,this.width=t.width||0,this.height=t.height||0,this.imageUrl=t.url||"",this.url=t.url||e._imageMemoryURL||e._defaultImage},this._ImageInfoModel.prototype={setSizeType:function(e){this.sizeType=e},setType:function(e){this.type=e},setImageUrl:function(e){e&&(this.imageUrl=e)},getImageUrl:function(){return this.imageUrl}}}},{key:"initImageInfoArray",value:function(e){for(var t=0,n=null,o=null;t<=2;)o=Ji(e)||Ji(e[t])?{type:0,size:0,width:0,height:0,url:""}:e[t],(n=new this._ImageInfoModel(o)).setSizeType(t+1),n.setType(t),this.addImageInfo(n),t++;this.updateAccessSideImageInfoArray()}},{key:"updateImageInfoArray",value:function(e){for(var t,n=this.content.imageInfoArray.length,o=0;o<n;o++)t=this.content.imageInfoArray[o],e[o].size&&(t.size=e[o].size),e[o].url&&t.setImageUrl(e[o].url),e[o].width&&(t.width=e[o].width),e[o].height&&(t.height=e[o].height)}},{key:"_autoFixUrl",value:function(){for(var e=this.content.imageInfoArray.length,t="",n="",o=["http","https"],r=null,a=0;a<e;a++)this.content.imageInfoArray[a].url&&""!==(r=this.content.imageInfoArray[a]).imageUrl&&(n=r.imageUrl.slice(0,r.imageUrl.indexOf("://")+1),t=r.imageUrl.slice(r.imageUrl.indexOf("://")+1),o.indexOf(n)<0&&(n="https:"),this.content.imageInfoArray[a].setImageUrl([n,t].join("")))}},{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateImageFormat",value:function(e){this.content.imageFormat=G_.IMAGE_FORMAT[e.toUpperCase()]||G_.IMAGE_FORMAT.UNKNOWN}},{key:"createImageDataASURLInWeb",value:function(e){void 0!==e&&e.files.length>0&&(this._imageMemoryURL=window.URL.createObjectURL(e.files[0]))}},{key:"createImageDataASURLInWXMiniApp",value:function(e){e&&e.url&&(this._imageMemoryURL=e.url)}},{key:"replaceImageInfo",value:function(e,t){this.content.imageInfoArray[t]instanceof this._ImageInfoModel||(this.content.imageInfoArray[t]=e)}},{key:"addImageInfo",value:function(e){this.content.imageInfoArray.length>=3||this.content.imageInfoArray.push(e)}},{key:"updateAccessSideImageInfoArray",value:function(){var e=this.content.imageInfoArray,t=e[0],n=t.width,o=void 0===n?0:n,r=t.height,a=void 0===r?0:r;0!==o&&0!==a&&(Cu(e),Object.assign(e[2],Tu({originWidth:o,originHeight:a,min:720})))}},{key:"sendable",value:function(){return 0!==this.content.imageInfoArray.length&&""!==this.content.imageInfoArray[0].imageUrl&&0!==this.content.imageInfoArray[0].size}}]),e}(),K_=function(){function e(t){Gn(this,e),this.type=so.MSG_FACE,this.content=t||null}return Un(e,[{key:"sendable",value:function(){return null!==this.content}}]),e}(),H_=function(){function e(t){Gn(this,e),this.type=so.MSG_AUDIO,this._percent=0,this.content={downloadFlag:2,second:t.second,size:t.size,url:t.url,remoteAudioUrl:t.url||"",uuid:t.uuid}}return Un(e,[{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateAudioUrl",value:function(e){this.content.remoteAudioUrl=e}},{key:"sendable",value:function(){return""!==this.content.remoteAudioUrl}}]),e}();be({target:"Object",stat:!0,forced:!i,sham:!i},{defineProperty:O.f});var j_={from:!0,groupID:!0,groupName:!0,to:!0},W_=function(){function e(t){Gn(this,e),this.type=so.MSG_GRP_TIP,this.content={},this._initContent(t)}return Un(e,[{key:"_initContent",value:function(e){var t=this;Object.keys(e).forEach((function(n){switch(n){case"remarkInfo":break;case"groupProfile":t.content.groupProfile={},t._initGroupProfile(e[n]);break;case"operatorInfo":case"memberInfoList":break;case"msgMemberInfo":t.content.memberList=e[n],Object.defineProperty(t.content,"msgMemberInfo",{get:function(){return Bi.warn("!!! 禁言的群提示消息中的 payload.msgMemberInfo 属性即将废弃，请使用 payload.memberList 属性替代。 \n","msgMemberInfo 中的 shutupTime 属性对应更改为 memberList 中的 muteTime 属性，表示禁言时长。 \n","参考：群提示消息 https://web.sdk.qcloud.com/im/doc/zh-cn/Message.html#.GroupTipPayload"),t.content.memberList.map((function(e){return{userID:e.userID,shutupTime:e.muteTime}}))}});break;case"onlineMemberInfo":break;case"memberNum":t.content[n]=e[n],t.content.memberCount=e[n];break;default:t.content[n]=e[n]}})),this.content.userIDList||(this.content.userIDList=[this.content.operatorID])}},{key:"_initGroupProfile",value:function(e){for(var t=Object.keys(e),n=0;n<t.length;n++){var o=t[n];j_[o]&&(this.content.groupProfile[o]=e[o])}}}]),e}(),Y_={from:!0,groupID:!0,groupName:!0,to:!0},z_=function(){function e(t){Gn(this,e),this.type=so.MSG_GRP_SYS_NOTICE,this.content={},this._initContent(t)}return Un(e,[{key:"_initContent",value:function(e){var t=this;Object.keys(e).forEach((function(n){switch(n){case"memberInfoList":break;case"remarkInfo":t.content.handleMessage=e[n];break;case"groupProfile":t.content.groupProfile={},t._initGroupProfile(e[n]);break;default:t.content[n]=e[n]}}))}},{key:"_initGroupProfile",value:function(e){for(var t=Object.keys(e),n=0;n<t.length;n++){var o=t[n];Y_[o]&&("groupName"===o?this.content.groupProfile.name=e[o]:this.content.groupProfile[o]=e[o])}}}]),e}(),J_=Math.min,X_=[].lastIndexOf,Q_=!!X_&&1/[1].lastIndexOf(1,-0)<0,$_=ut("lastIndexOf"),Z_=pt("indexOf",{ACCESSORS:!0,1:0}),em=!Q_&&$_&&Z_?X_:function(e){if(Q_)return X_.apply(this,arguments)||0;var t=m(this),n=de(t.length),o=n-1;for(arguments.length>1&&(o=J_(o,ce(arguments[1]))),o<0&&(o=n+o);o>=0;o--)if(o in t&&t[o]===e)return o||0;return-1};be({target:"Array",proto:!0,forced:em!==[].lastIndexOf},{lastIndexOf:em});var tm=function(){function e(t){Gn(this,e),this.type=so.MSG_FILE,this._percent=0;var n=this._getFileInfo(t);this.content={downloadFlag:2,fileUrl:t.url||"",uuid:t.uuid,fileName:n.name||"",fileSize:n.size||0}}return Un(e,[{key:"_getFileInfo",value:function(e){if(!Ji(e.fileName)&&!Ji(e.fileSize))return{size:e.fileSize,name:e.fileName};if(si)return{};var t=e.file.files[0];return{size:t.size,name:t.name,type:t.type.slice(t.type.lastIndexOf("/")+1).toLowerCase()}}},{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateFileUrl",value:function(e){this.content.fileUrl=e}},{key:"sendable",value:function(){return""!==this.content.fileUrl&&""!==this.content.fileName&&0!==this.content.fileSize}}]),e}(),nm=function(){function e(t){Gn(this,e),this.type=so.MSG_CUSTOM,this.content={data:t.data||"",description:t.description||"",extension:t.extension||""}}return Un(e,[{key:"setData",value:function(e){return this.content.data=e,this}},{key:"setDescription",value:function(e){return this.content.description=e,this}},{key:"setExtension",value:function(e){return this.content.extension=e,this}},{key:"sendable",value:function(){return 0!==this.content.data.length||0!==this.content.description.length||0!==this.content.extension.length}}]),e}(),om=function(){function e(t){Gn(this,e),this.type=so.MSG_VIDEO,this._percent=0,this.content={remoteVideoUrl:t.remoteVideoUrl||t.videoUrl||"",videoFormat:t.videoFormat,videoSecond:parseInt(t.videoSecond,10),videoSize:t.videoSize,videoUrl:t.videoUrl,videoDownloadFlag:2,videoUUID:t.videoUUID,thumbUUID:t.thumbUUID,thumbFormat:t.thumbFormat,thumbWidth:t.thumbWidth,thumbHeight:t.thumbHeight,thumbSize:t.thumbSize,thumbDownloadFlag:2,thumbUrl:t.thumbUrl}}return Un(e,[{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateVideoUrl",value:function(e){e&&(this.content.remoteVideoUrl=e)}},{key:"sendable",value:function(){return""!==this.content.remoteVideoUrl}}]),e}(),rm=function(){function e(t){Gn(this,e),this.type=so.MSG_LOCATION;var n=t.description,o=t.longitude,r=t.latitude;this.content={description:n,longitude:o,latitude:r}}return Un(e,[{key:"sendable",value:function(){return!0}}]),e}(),am=function(){function e(t){if(Gn(this,e),this.from=t.from,this.messageSender=t.from,this.time=t.time,this.messageSequence=t.sequence,this.clientSequence=t.clientSequence||t.sequence,this.messageRandom=t.random,this.cloudCustomData=t.cloudCustomData||"",t.ID)this.nick=t.nick||"",this.avatar=t.avatar||"",this.messageBody=[{type:t.type,payload:t.payload}],t.conversationType.startsWith(so.CONV_C2C)?this.receiverUserID=t.to:t.conversationType.startsWith(so.CONV_GROUP)&&(this.receiverGroupID=t.to),this.messageReceiver=t.to;else{this.nick=t.nick||"",this.avatar=t.avatar||"",this.messageBody=[];var n=t.elements[0].type,o=t.elements[0].content;this._patchRichMediaPayload(n,o),n===so.MSG_MERGER?this.messageBody.push({type:n,payload:new sm(o).content}):this.messageBody.push({type:n,payload:o}),t.groupID&&(this.receiverGroupID=t.groupID,this.messageReceiver=t.groupID),t.to&&(this.receiverUserID=t.to,this.messageReceiver=t.to)}}return Un(e,[{key:"_patchRichMediaPayload",value:function(e,t){e===so.MSG_IMAGE?t.imageInfoArray.forEach((function(e){!e.imageUrl&&e.url&&(e.imageUrl=e.url,e.sizeType=e.type,1===e.type?e.type=0:3===e.type&&(e.type=1))})):e===so.MSG_VIDEO?!t.remoteVideoUrl&&t.videoUrl&&(t.remoteVideoUrl=t.videoUrl):e===so.MSG_AUDIO?!t.remoteAudioUrl&&t.url&&(t.remoteAudioUrl=t.url):e===so.MSG_FILE&&!t.fileUrl&&t.url&&(t.fileUrl=t.url,t.url=void 0)}}]),e}(),sm=function(){function e(t){if(Gn(this,e),this.type=so.MSG_MERGER,this.content={downloadKey:"",pbDownloadKey:"",messageList:[],title:"",abstractList:[],compatibleText:"",version:0,layersOverLimit:!1},t.downloadKey){var n=t.downloadKey,o=t.pbDownloadKey,r=t.title,a=t.abstractList,s=t.compatibleText,i=t.version;this.content.downloadKey=n,this.content.pbDownloadKey=o,this.content.title=r,this.content.abstractList=a,this.content.compatibleText=s,this.content.version=i||0}else if(Nu(t.messageList))1===t.layersOverLimit&&(this.content.layersOverLimit=!0);else{var u=t.messageList,c=t.title,l=t.abstractList,d=t.compatibleText,p=t.version,g=[];u.forEach((function(e){if(!Nu(e)){var t=new am(e);g.push(t)}})),this.content.messageList=g,this.content.title=c,this.content.abstractList=l,this.content.compatibleText=d,this.content.version=p||0}Bi.debug("MergerElement.content:",this.content)}return Un(e,[{key:"sendable",value:function(){return!Nu(this.content.messageList)||!Nu(this.content.downloadKey)}}]),e}(),im={1:so.MSG_PRIORITY_HIGH,2:so.MSG_PRIORITY_NORMAL,3:so.MSG_PRIORITY_LOW,4:so.MSG_PRIORITY_LOWEST},um=function(){function e(t){Gn(this,e),this.ID="",this.conversationID=t.conversationID||null,this.conversationType=t.conversationType||so.CONV_C2C,this.conversationSubType=t.conversationSubType,this.time=t.time||Math.ceil(Date.now()/1e3),this.sequence=t.sequence||0,this.clientSequence=t.clientSequence||t.sequence||0,this.random=t.random||0===t.random?t.random:su(),this.priority=this._computePriority(t.priority),this.nick=t.nick||"",this.avatar=t.avatar||"",this.isPeerRead=!1,this.nameCard="",this._elements=[],this.isPlaceMessage=t.isPlaceMessage||0,this.isRevoked=2===t.isPlaceMessage||8===t.msgFlagBits,this.from=t.from||null,this.to=t.to||null,this.flow="",this.isSystemMessage=t.isSystemMessage||!1,this.protocol=t.protocol||"JSON",this.isResend=!1,this.isRead=!1,this.status=t.status||Cc.SUCCESS,this._onlineOnlyFlag=!1,this._groupAtInfoList=[],this._relayFlag=!1,this.atUserList=[],this.cloudCustomData=t.cloudCustomData||"",this.isDeleted=!1,this.isModified=!1,this._isExcludedFromUnreadCount=!(!t.messageControlInfo||1!==t.messageControlInfo.excludedFromUnreadCount),this._isExcludedFromLastMessage=!(!t.messageControlInfo||1!==t.messageControlInfo.excludedFromLastMessage),this.reInitialize(t.currentUser),this.extractGroupInfo(t.groupProfile||null),this.handleGroupAtInfo(t)}return Un(e,[{key:"getElements",value:function(){return this._elements}},{key:"extractGroupInfo",value:function(e){if(null!==e){ji(e.nick)&&(this.nick=e.nick),ji(e.avatar)&&(this.avatar=e.avatar);var t=e.messageFromAccountExtraInformation;Yi(t)&&ji(t.nameCard)&&(this.nameCard=t.nameCard)}}},{key:"handleGroupAtInfo",value:function(e){var t=this;e.payload&&e.payload.atUserList&&e.payload.atUserList.forEach((function(e){e!==so.MSG_AT_ALL?(t._groupAtInfoList.push({groupAtAllFlag:0,groupAtUserID:e}),t.atUserList.push(e)):(t._groupAtInfoList.push({groupAtAllFlag:1}),t.atUserList.push(so.MSG_AT_ALL))})),zi(e.groupAtInfo)&&e.groupAtInfo.forEach((function(e){0===e.groupAtAllFlag?t.atUserList.push(e.groupAtUserID):1===e.groupAtAllFlag&&t.atUserList.push(so.MSG_AT_ALL)}))}},{key:"getGroupAtInfoList",value:function(){return this._groupAtInfoList}},{key:"_initProxy",value:function(){this._elements[0]&&(this.payload=this._elements[0].content,this.type=this._elements[0].type)}},{key:"reInitialize",value:function(e){e&&(this.status=this.from?Cc.SUCCESS:Cc.UNSEND,!this.from&&(this.from=e)),this._initFlow(e),this._initSequence(e),this._concatConversationID(e),this.generateMessageID(e)}},{key:"isSendable",value:function(){return 0!==this._elements.length&&("function"!=typeof this._elements[0].sendable?(Bi.warn("".concat(this._elements[0].type,' need "boolean : sendable()" method')),!1):this._elements[0].sendable())}},{key:"_initTo",value:function(e){this.conversationType===so.CONV_GROUP&&(this.to=e.groupID)}},{key:"_initSequence",value:function(e){0===this.clientSequence&&e&&(this.clientSequence=function(e){if(!e)return Bi.error("autoIncrementIndex(string: key) need key parameter"),!1;if(void 0===lu[e]){var t=new Date,n="3".concat(t.getHours()).slice(-2),o="0".concat(t.getMinutes()).slice(-2),r="0".concat(t.getSeconds()).slice(-2);lu[e]=parseInt([n,o,r,"0001"].join("")),n=null,o=null,r=null,Bi.log("autoIncrementIndex start index:".concat(lu[e]))}return lu[e]++}(e)),0===this.sequence&&this.conversationType===so.CONV_C2C&&(this.sequence=this.clientSequence)}},{key:"generateMessageID",value:function(e){var t=e===this.from?1:0,n=this.sequence>0?this.sequence:this.clientSequence;this.ID="".concat(this.conversationID,"-").concat(n,"-").concat(this.random,"-").concat(t)}},{key:"_initFlow",value:function(e){""!==e&&(e===this.from?(this.flow="out",this.isRead=!0):this.flow="in")}},{key:"_concatConversationID",value:function(e){var t=this.to,n="",o=this.conversationType;o!==so.CONV_SYSTEM?(n=o===so.CONV_C2C?e===this.from?t:this.from:this.to,this.conversationID="".concat(o).concat(n)):this.conversationID=so.CONV_SYSTEM}},{key:"isElement",value:function(e){return e instanceof Nh||e instanceof B_||e instanceof K_||e instanceof H_||e instanceof tm||e instanceof om||e instanceof W_||e instanceof z_||e instanceof nm||e instanceof rm||e instanceof sm}},{key:"setElement",value:function(e){var t=this;if(this.isElement(e))return this._elements=[e],void this._initProxy();var n=function(e){if(e.type&&e.content)switch(e.type){case so.MSG_TEXT:t.setTextElement(e.content);break;case so.MSG_IMAGE:t.setImageElement(e.content);break;case so.MSG_AUDIO:t.setAudioElement(e.content);break;case so.MSG_FILE:t.setFileElement(e.content);break;case so.MSG_VIDEO:t.setVideoElement(e.content);break;case so.MSG_CUSTOM:t.setCustomElement(e.content);break;case so.MSG_LOCATION:t.setLocationElement(e.content);break;case so.MSG_GRP_TIP:t.setGroupTipElement(e.content);break;case so.MSG_GRP_SYS_NOTICE:t.setGroupSystemNoticeElement(e.content);break;case so.MSG_FACE:t.setFaceElement(e.content);break;case so.MSG_MERGER:t.setMergerElement(e.content);break;default:Bi.warn(e.type,e.content,"no operation......")}};if(zi(e))for(var o=0;o<e.length;o++)n(e[o]);else n(e);this._initProxy()}},{key:"clearElement",value:function(){this._elements.length=0}},{key:"setTextElement",value:function(e){var t="string"==typeof e?e:e.text,n=new Nh({text:t});this._elements.push(n)}},{key:"setImageElement",value:function(e){var t=new B_(e);this._elements.push(t)}},{key:"setAudioElement",value:function(e){var t=new H_(e);this._elements.push(t)}},{key:"setFileElement",value:function(e){var t=new tm(e);this._elements.push(t)}},{key:"setVideoElement",value:function(e){var t=new om(e);this._elements.push(t)}},{key:"setLocationElement",value:function(e){var t=new rm(e);this._elements.push(t)}},{key:"setCustomElement",value:function(e){var t=new nm(e);this._elements.push(t)}},{key:"setGroupTipElement",value:function(e){var t={},n=e.operationType;Nu(e.memberInfoList)?e.operatorInfo&&(t=e.operatorInfo):n!==so.GRP_TIP_MBR_JOIN&&n!==so.GRP_TIP_MBR_KICKED_OUT&&n!==so.GRP_TIP_MBR_SET_ADMIN&&n!==so.GRP_TIP_MBR_CANCELED_ADMIN||(t=e.memberInfoList[0]);var o=t,r=o.nick,a=o.avatar;ji(r)&&(this.nick=r),ji(a)&&(this.avatar=a);var s=new W_(e);this._elements.push(s)}},{key:"setGroupSystemNoticeElement",value:function(e){var t=new z_(e);this._elements.push(t)}},{key:"setFaceElement",value:function(e){var t=new K_(e);this._elements.push(t)}},{key:"setMergerElement",value:function(e){var t=new sm(e);this._elements.push(t)}},{key:"setIsRead",value:function(e){this.isRead=e}},{key:"setRelayFlag",value:function(e){this._relayFlag=e}},{key:"getRelayFlag",value:function(){return this._relayFlag}},{key:"_computePriority",value:function(e){if(Ji(e))return so.MSG_PRIORITY_NORMAL;if(ji(e)&&-1!==Object.values(im).indexOf(e))return e;if(Hi(e)){var t=""+e;if(-1!==Object.keys(im).indexOf(t))return im[t]}return so.MSG_PRIORITY_NORMAL}},{key:"setNickAndAvatar",value:function(e){var t=e.nick,n=e.avatar;ji(t)&&(this.nick=t),ji(n)&&(this.avatar=n)}},{key:"setNameCard",value:function(e){ji(e)&&(this.nameCard=e)}},{key:"elements",get:function(){return Bi.warn("！！！Message 实例的 elements 属性即将废弃，请尽快修改。使用 type 和 payload 属性处理单条消息，兼容组合消息使用 _elements 属性！！！"),this._elements}}]),e}(),cm=function(e){return{code:0,data:e||{}}},lm="https://cloud.tencent.com/document/product/",dm="您可以在即时通信 IM 控制台的【开发辅助工具(https://console.cloud.tencent.com/im-detail/tool-usersig)】页面校验 UserSig。",pm="UserSig 非法，请使用官网提供的 API 重新生成 UserSig(".concat(lm,"269/32688)。"),gm="#.E6.B6.88.E6.81.AF.E5.85.83.E7.B4.A0-timmsgelement",hm={70001:"UserSig 已过期，请重新生成。建议 UserSig 有效期设置不小于24小时。",70002:"UserSig 长度为0，请检查传入的 UserSig 是否正确。",70003:pm,70005:pm,70009:"UserSig 验证失败，可能因为生成 UserSig 时混用了其他 SDKAppID 的私钥或密钥导致，请使用对应 SDKAppID 下的私钥或密钥重新生成 UserSig(".concat(lm,"269/32688)。"),70013:"请求中的 UserID 与生成 UserSig 时使用的 UserID 不匹配。".concat(dm),70014:"请求中的 SDKAppID 与生成 UserSig 时使用的 SDKAppID 不匹配。".concat(dm),70016:"密钥不存在，UserSig 验证失败，请在即时通信 IM 控制台获取密钥(".concat(lm,"269/32578#.E8.8E.B7.E5.8F.96.E5.AF.86.E9.92.A5)。"),70020:"SDKAppID 未找到，请在即时通信 IM 控制台确认应用信息。",70050:"UserSig 验证次数过于频繁。请检查 UserSig 是否正确，并于1分钟后重新验证。".concat(dm),70051:"帐号被拉入黑名单。",70052:"UserSig 已经失效，请重新生成，再次尝试。",70107:"因安全原因被限制登录，请不要频繁登录。",70169:"请求的用户帐号不存在。",70114:"".concat("服务端内部超时，请稍后重试。"),70202:"".concat("服务端内部超时，请稍后重试。"),70206:"请求中批量数量不合法。",70402:"参数非法，请检查必填字段是否填充，或者字段的填充是否满足协议要求。",70403:"请求失败，需要 App 管理员权限。",70398:"帐号数超限。如需创建多于100个帐号，请将应用升级为专业版，具体操作指引请参见购买指引(".concat(lm,"269/32458)。"),70500:"".concat("服务端内部错误，请重试。"),71e3:"删除帐号失败。仅支持删除体验版帐号，您当前应用为专业版，暂不支持帐号删除。",20001:"请求包非法。",20002:"UserSig 或 A2 失效。",20003:"消息发送方或接收方 UserID 无效或不存在，请检查 UserID 是否已导入即时通信 IM。",20004:"网络异常，请重试。",20005:"".concat("服务端内部错误，请重试。"),20006:"触发发送".concat("单聊消息","之前回调，App 后台返回禁止下发该消息。"),20007:"发送".concat("单聊消息","，被对方拉黑，禁止发送。消息发送状态默认展示为失败，您可以登录控制台修改该场景下的消息发送状态展示结果，具体操作请参见消息保留设置(").concat(lm,"269/38656)。"),20009:"消息发送双方互相不是好友，禁止发送（配置".concat("单聊消息","校验好友关系才会出现）。"),20010:"发送".concat("单聊消息","，自己不是对方的好友（单向关系），禁止发送。"),20011:"发送".concat("单聊消息","，对方不是自己的好友（单向关系），禁止发送。"),20012:"发送方被禁言，该条消息被禁止发送。",20016:"消息撤回超过了时间限制（默认2分钟）。",20018:"删除漫游内部错误。",90001:"JSON 格式解析失败，请检查请求包是否符合 JSON 规范。",90002:"".concat("JSON 格式请求包体","中 MsgBody 不符合消息格式描述，或者 MsgBody 不是 Array 类型，请参考 TIMMsgElement 对象的定义(").concat(lm,"269/2720").concat(gm,")。"),90003:"".concat("JSON 格式请求包体","中缺少 To_Account 字段或者 To_Account 帐号不存在。"),90005:"".concat("JSON 格式请求包体","中缺少 MsgRandom 字段或者 MsgRandom 字段不是 Integer 类型。"),90006:"".concat("JSON 格式请求包体","中缺少 MsgTimeStamp 字段或者 MsgTimeStamp 字段不是 Integer 类型。"),90007:"".concat("JSON 格式请求包体","中 MsgBody 类型不是 Array 类型，请将其修改为 Array 类型。"),90008:"".concat("JSON 格式请求包体","中缺少 From_Account 字段或者 From_Account 帐号不存在。"),90009:"请求需要 App 管理员权限。",90010:"".concat("JSON 格式请求包体","不符合消息格式描述，请参考 TIMMsgElement 对象的定义(").concat(lm,"269/2720").concat(gm,")。"),90011:"批量发消息目标帐号超过500，请减少 To_Account 中目标帐号数量。",90012:"To_Account 没有注册或不存在，请确认 To_Account 是否导入即时通信 IM 或者是否拼写错误。",90026:"消息离线存储时间错误（最多不能超过7天）。",90031:"".concat("JSON 格式请求包体","中 SyncOtherMachine 字段不是 Integer 类型。"),90044:"".concat("JSON 格式请求包体","中 MsgLifeTime 字段不是 Integer 类型。"),90048:"请求的用户帐号不存在。",90054:"撤回请求中的 MsgKey 不合法。",90994:"".concat("服务端内部错误，请重试。"),90995:"".concat("服务端内部错误，请重试。"),91e3:"".concat("服务端内部错误，请重试。"),90992:"".concat("服务端内部错误，请重试。","如果所有请求都返回该错误码，且 App 配置了第三方回调，请检查 App 服务端是否正常向即时通信 IM 后台服务端返回回调结果。"),93e3:"JSON 数据包超长，消息包体请不要超过8k。",91101:"Web 端长轮询被踢（Web 端同时在线实例个数超出限制）。",10002:"".concat("服务端内部错误，请重试。"),10003:"请求中的接口名称错误，请核对接口名称并重试。",10004:"参数非法，请根据错误描述检查请求是否正确。",10005:"请求包体中携带的帐号数量过多。",10006:"操作频率限制，请尝试降低调用的频率。",10007:"操作权限不足，例如 Work ".concat("群组","中普通成员尝试执行踢人操作，但只有 App 管理员才有权限。"),10008:"请求非法，可能是请求中携带的签名信息验证不正确，请再次尝试。",10009:"该群不允许群主主动退出。",10010:"".concat("群组","不存在，或者曾经存在过，但是目前已经被解散。"),10011:"解析 JSON 包体失败，请检查包体的格式是否符合 JSON 格式。",10012:"发起操作的 UserID 非法，请检查发起操作的用户 UserID 是否填写正确。",10013:"被邀请加入的用户已经是群成员。",10014:"群已满员，无法将请求中的用户加入".concat("群组","，如果是批量加人，可以尝试减少加入用户的数量。"),10015:"找不到指定 ID 的".concat("群组","。"),10016:"App 后台通过第三方回调拒绝本次操作。",10017:"因被禁言而不能发送消息，请检查发送者是否被设置禁言。",10018:"应答包长度超过最大包长（1MB），请求的内容过多，请尝试减少单次请求的数据量。",10019:"请求的用户帐号不存在。",10021:"".concat("群组"," ID 已被使用，请选择其他的").concat("群组"," ID。"),10023:"发消息的频率超限，请延长两次发消息时间的间隔。",10024:"此邀请或者申请请求已经被处理。",10025:"".concat("群组"," ID 已被使用，并且操作者为群主，可以直接使用。"),10026:"该 SDKAppID 请求的命令字已被禁用。",10030:"请求撤回的消息不存在。",10031:"消息撤回超过了时间限制（默认2分钟）。",10032:"请求撤回的消息不支持撤回操作。",10033:"".concat("群组","类型不支持消息撤回操作。"),10034:"该消息类型不支持删除操作。",10035:"直播群和在线成员广播大群不支持删除消息。",10036:"直播群创建数量超过了限制，请参考价格说明(".concat(lm,"269/11673)购买预付费套餐“IM直播群”。"),10037:"单个用户可创建和加入的".concat("群组","数量超过了限制，请参考价格说明(").concat(lm,"269/11673)购买或升级预付费套餐“单人可创建与加入").concat("群组","数”。"),10038:"群成员数量超过限制，请参考价格说明(".concat(lm,"269/11673)购买或升级预付费套餐“扩展群人数上限”。"),10041:"该应用（SDKAppID）已配置不支持群消息撤回。",10050:"群属性 key 不存在",10056:"请在写入群属性前先使用 getGroupAttributes 接口更新本地群属性，避免冲突。",30001:"请求参数错误，请根据错误描述检查请求参数",30002:"SDKAppID 不匹配",30003:"请求的用户帐号不存在",30004:"请求需要 App 管理员权限",30005:"关系链字段中包含敏感词",30006:"".concat("服务端内部错误，请重试。"),30007:"".concat("网络超时，请稍后重试. "),30008:"并发写导致写冲突，建议使用批量方式",30009:"后台禁止该用户发起加好友请求",30010:"自己的好友数已达系统上限",30011:"分组已达系统上限",30012:"未决数已达系统上限",30014:"对方的好友数已达系统上限",30515:"请求添加好友时，对方在自己的黑名单中，不允许加好友",30516:"请求添加好友时，对方的加好友验证方式是不允许任何人添加自己为好友",30525:"请求添加好友时，自己在对方的黑名单中，不允许加好友",30539:"等待对方同意",30540:"添加好友请求被安全策略打击，请勿频繁发起添加好友请求",31704:"与请求删除的帐号之间不存在好友关系",31707:"删除好友请求被安全策略打击，请勿频繁发起删除好友请求"},fm=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this)).code=e.code,o.message=hm[e.code]||e.message,o.data=e.data||{},o}return n}(Wn(Error)),_m=null,mm=function(e){_m=e},vm=function(e){return Promise.resolve(cm(e))},Mm=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e instanceof fm)return t&&null!==_m&&_m.emit(ao.ERROR,e),Promise.reject(e);if(e instanceof Error){var n=new fm({code:kd.UNCAUGHT_ERROR,message:e.message});return t&&null!==_m&&_m.emit(ao.ERROR,n),Promise.reject(n)}if(Ji(e)||Ji(e.code)||Ji(e.message))Bi.error("IMPromise.reject 必须指定code(错误码)和message(错误信息)!!!");else{if(Hi(e.code)&&ji(e.message)){var o=new fm(e);return t&&null!==_m&&_m.emit(ao.ERROR,o),Promise.reject(o)}Bi.error("IMPromise.reject code(错误码)必须为数字，message(错误信息)必须为字符串!!!")}},ym=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="C2CModule",o}return Un(n,[{key:"onNewC2CMessage",value:function(e){var t=e.dataList,n=e.isInstantMessage,o=e.C2CRemainingUnreadList,r=e.C2CPairUnreadList;Bi.debug("".concat(this._className,".onNewC2CMessage count:").concat(t.length," isInstantMessage:").concat(n));var a=this._newC2CMessageStoredAndSummary({dataList:t,C2CRemainingUnreadList:o,C2CPairUnreadList:r,isInstantMessage:n}),s=a.conversationOptionsList,i=a.messageList,u=a.isUnreadC2CMessage;this.filterModifiedMessage(i),s.length>0&&this.getModule(Wc).onNewMessage({conversationOptionsList:s,isInstantMessage:n,isUnreadC2CMessage:u});var c=this.filterUnmodifiedMessage(i);n&&c.length>0&&this.emitOuterEvent(ao.MESSAGE_RECEIVED,c),i.length=0}},{key:"_newC2CMessageStoredAndSummary",value:function(e){for(var t=e.dataList,n=e.C2CRemainingUnreadList,o=e.C2CPairUnreadList,r=e.isInstantMessage,a=null,s=[],i=[],u={},c=this.getModule(Qc),l=!1,d=0,p=t.length;d<p;d++){var g=t[d];g.currentUser=this.getMyUserID(),g.conversationType=so.CONV_C2C,g.isSystemMessage=!!g.isSystemMessage,(Ji(g.nick)||Ji(g.avatar))&&(l=!0,Bi.debug("".concat(this._className,"._newC2CMessageStoredAndSummary nick or avatar missing!"))),a=new um(g),g.elements=c.parseElements(g.elements,g.from),a.setElement(g.elements),a.setNickAndAvatar({nick:g.nick,avatar:g.avatar});var h=a.conversationID;if(r){var f=!1,_=this.getModule(Wc);if(a.from!==this.getMyUserID()){var m=_.getLatestMessageSentByPeer(h);if(m){var v=m.nick,M=m.avatar;l?a.setNickAndAvatar({nick:v,avatar:M}):v===a.nick&&M===a.avatar||(f=!0)}}else{var y=_.getLatestMessageSentByMe(h);if(y){var I=y.nick,T=y.avatar;I===a.nick&&T===a.avatar||_.modifyMessageSentByMe({conversationID:h,latestNick:a.nick,latestAvatar:a.avatar})}}var C=1===t[d].isModified;if(_.isMessageSentByCurrentInstance(a)?a.isModified=C:C=!1,0===g.msgLifeTime)a._onlineOnlyFlag=!0,i.push(a);else{if(!_.pushIntoMessageList(i,a,C))continue;f&&(_.modifyMessageSentByPeer({conversationID:h,latestNick:a.nick,latestAvatar:a.avatar}),_.updateUserProfileSpecifiedKey({conversationID:h,nick:a.nick,avatar:a.avatar}))}this.getModule(il).addMessageDelay({currentTime:Date.now(),time:a.time})}if(0!==g.msgLifeTime){if(!1===a._onlineOnlyFlag)if(Ji(u[h])){var S=0;"in"===a.flow&&(a._isExcludedFromUnreadCount||(S=1)),u[h]=s.push({conversationID:h,unreadCount:S,type:a.conversationType,subType:a.conversationSubType,lastMessage:a._isExcludedFromLastMessage?"":a})-1}else{var A=u[h];s[A].type=a.conversationType,s[A].subType=a.conversationSubType,s[A].lastMessage=a._isExcludedFromLastMessage?"":a,"in"===a.flow&&(a._isExcludedFromUnreadCount||s[A].unreadCount++)}}else a._onlineOnlyFlag=!0}var E=!1;if(zi(o))for(var k=function(e,t){if(o[e].unreadCount>0){E=!0;var n=s.find((function(t){return t.conversationID==="C2C".concat(o[e].from)}));n?n.unreadCount=o[e].unreadCount:s.push({conversationID:"C2C".concat(o[e].from),unreadCount:o[e].unreadCount,type:so.CONV_C2C})}},D=0,N=o.length;D<N;D++)k(D);if(zi(n))for(var O=function(e,t){s.find((function(t){return t.conversationID==="C2C".concat(n[e].from)}))||s.push({conversationID:"C2C".concat(n[e].from),type:so.CONV_C2C,lastMsgTime:n[e].lastMsgTime})},R=0,L=n.length;R<L;R++)O(R);return{conversationOptionsList:s,messageList:i,isUnreadC2CMessage:E}}},{key:"onC2CMessageRevoked",value:function(e){var t=this;Bi.debug("".concat(this._className,".onC2CMessageRevoked count:").concat(e.dataList.length));var n=this.getModule(Wc),o=[],r=null;e.dataList.forEach((function(e){if(e.c2cMessageRevokedNotify){var a=e.c2cMessageRevokedNotify.revokedInfos;Ji(a)||a.forEach((function(e){var a=t.getMyUserID()===e.from?"".concat(so.CONV_C2C).concat(e.to):"".concat(so.CONV_C2C).concat(e.from);(r=n.revoke(a,e.sequence,e.random))&&o.push(r)}))}})),0!==o.length&&(n.onMessageRevoked(o),this.emitOuterEvent(ao.MESSAGE_REVOKED,o))}},{key:"onC2CMessageReadReceipt",value:function(e){var t=this;e.dataList.forEach((function(e){if(!Nu(e.c2cMessageReadReceipt)){var n=e.c2cMessageReadReceipt.to;e.c2cMessageReadReceipt.uinPairReadArray.forEach((function(e){var o=e.peerReadTime;Bi.debug("".concat(t._className,"._onC2CMessageReadReceipt to:").concat(n," peerReadTime:").concat(o));var r="".concat(so.CONV_C2C).concat(n),a=t.getModule(Wc);a.recordPeerReadTime(r,o),a.updateMessageIsPeerReadProperty(r,o)}))}}))}},{key:"onC2CMessageReadNotice",value:function(e){var t=this;e.dataList.forEach((function(e){if(!Nu(e.c2cMessageReadNotice)){var n=t.getModule(Wc);e.c2cMessageReadNotice.uinPairReadArray.forEach((function(e){var o=e.from,r=e.peerReadTime;Bi.debug("".concat(t._className,".onC2CMessageReadNotice from:").concat(o," lastReadTime:").concat(r));var a="".concat(so.CONV_C2C).concat(o);n.updateIsReadAfterReadReport({conversationID:a,lastMessageTime:r}),n.updateUnreadCount(a)}))}}))}},{key:"sendMessage",value:function(e,t){var n=this._createC2CMessagePack(e,t);return this.request(n)}},{key:"_createC2CMessagePack",value:function(e,t){var n=null;t&&(t.offlinePushInfo&&(n=t.offlinePushInfo),!0===t.onlineUserOnly&&(n?n.disablePush=!0:n={disablePush:!0}));var o="";ji(e.cloudCustomData)&&e.cloudCustomData.length>0&&(o=e.cloudCustomData);var r=[];if(Yi(t)&&Yi(t.messageControlInfo)){var a=t.messageControlInfo,s=a.excludedFromUnreadCount,i=a.excludedFromLastMessage;!0===s&&r.push("NoUnread"),!0===i&&r.push("NoLastMsg")}return{protocolName:_l,tjgID:this.generateTjgID(e),requestData:{fromAccount:this.getMyUserID(),toAccount:e.to,msgTimeStamp:Math.ceil(Date.now()/1e3),msgBody:e.getElements(),cloudCustomData:o,msgSeq:e.sequence,msgRandom:e.random,msgLifeTime:this.isOnlineMessage(e,t)?0:void 0,nick:e.nick,avatar:e.avatar,offlinePushInfo:n?{pushFlag:!0===n.disablePush?1:0,title:n.title||"",desc:n.description||"",ext:n.extension||"",apnsInfo:{badgeMode:!0===n.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:n.androidOPPOChannelID||""}}:void 0,messageControlInfo:r}}}},{key:"isOnlineMessage",value:function(e,t){return!(!t||!0!==t.onlineUserOnly)}},{key:"revokeMessage",value:function(e){return this.request({protocolName:Cl,requestData:{msgInfo:{fromAccount:e.from,toAccount:e.to,msgSeq:e.sequence,msgRandom:e.random,msgTimeStamp:e.time}}})}},{key:"deleteMessage",value:function(e){var t=e.to,n=e.keyList;return Bi.log("".concat(this._className,".deleteMessage toAccount:").concat(t," count:").concat(n.length)),this.request({protocolName:Nl,requestData:{fromAccount:this.getMyUserID(),to:t,keyList:n}})}},{key:"setMessageRead",value:function(e){var t=this,n=e.conversationID,o=e.lastMessageTime,r="".concat(this._className,".setMessageRead");Bi.log("".concat(r," conversationID:").concat(n," lastMessageTime:").concat(o)),Hi(o)||Bi.warn("".concat(r," 请勿修改 Conversation.lastMessage.lastTime，否则可能会导致已读上报结果不准确"));var a=new Zp(Mg);return a.setMessage("conversationID:".concat(n," lastMessageTime:").concat(o)),this.request({protocolName:Sl,requestData:{C2CMsgReaded:{cookie:"",C2CMsgReadedItem:[{toAccount:n.replace("C2C",""),lastMessageTime:o,receipt:1}]}}}).then((function(){a.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(r," ok"));var e=t.getModule(Wc);return e.updateIsReadAfterReadReport({conversationID:n,lastMessageTime:o}),e.updateUnreadCount(n),cm()})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),Bi.log("".concat(r," failed. error:"),e),Mm(e)}))}},{key:"getRoamingMessage",value:function(e){var t=this,n="".concat(this._className,".getRoamingMessage"),o=e.peerAccount,r=e.conversationID,a=e.count,s=e.lastMessageTime,i=e.messageKey,u="peerAccount:".concat(o," count:").concat(a||15," lastMessageTime:").concat(s||0," messageKey:").concat(i);Bi.log("".concat(n," ").concat(u));var c=new Zp(fg);return this.request({protocolName:kl,requestData:{peerAccount:o,count:a||15,lastMessageTime:s||0,messageKey:i}}).then((function(e){var o=e.data,a=o.complete,s=o.messageList,i=o.messageKey,l=o.lastMessageTime;Ji(s)?Bi.log("".concat(n," ok. complete:").concat(a," but messageList is undefined!")):Bi.log("".concat(n," ok. complete:").concat(a," count:").concat(s.length)),c.setNetworkType(t.getNetworkType()).setMessage("".concat(u," complete:").concat(a," length:").concat(s.length)).end();var d=t.getModule(Wc);1===a&&d.setCompleted(r);var p=d.storeRoamingMessage(s,r);d.modifyMessageList(r),d.updateIsRead(r),d.updateRoamingMessageKeyAndTime(r,i,l);var g=d.getPeerReadTime(r);if(Bi.log("".concat(n," update isPeerRead property. conversationID:").concat(r," peerReadTime:").concat(g)),g)d.updateMessageIsPeerReadProperty(r,g);else{var h=r.replace(so.CONV_C2C,"");t.getRemotePeerReadTime([h]).then((function(){d.updateMessageIsPeerReadProperty(r,d.getPeerReadTime(r))}))}return p})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];c.setMessage(u).setError(e,o,r).end()})),Bi.warn("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"getRemotePeerReadTime",value:function(e){var t=this,n="".concat(this._className,".getRemotePeerReadTime");if(Nu(e))return Bi.warn("".concat(n," userIDList is empty!")),Promise.resolve();var o=new Zp(Tg);return Bi.log("".concat(n," userIDList:").concat(e)),this.request({protocolName:Dl,requestData:{userIDList:e}}).then((function(r){var a=r.data.peerReadTimeList;Bi.log("".concat(n," ok. peerReadTimeList:").concat(a));for(var s="",i=t.getModule(Wc),u=0;u<e.length;u++)s+="".concat(e[u],"-").concat(a[u]," "),a[u]>0&&i.recordPeerReadTime("C2C".concat(e[u]),a[u]);o.setNetworkType(t.getNetworkType()).setMessage(s).end()})).catch((function(e){t.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.warn("".concat(n," failed. error:"),e)}))}}]),n}(cl),Im=it.findIndex,Tm=!0,Cm=pt("findIndex");"findIndex"in[]&&Array(1).findIndex((function(){Tm=!1})),be({target:"Array",proto:!0,forced:Tm||!Cm},{findIndex:function(e){return Im(this,e,arguments.length>1?arguments[1]:void 0)}}),Co("findIndex");var Sm=[],Am=Sm.sort,Em=a((function(){Sm.sort(void 0)})),km=a((function(){Sm.sort(null)})),Dm=ut("sort");be({target:"Array",proto:!0,forced:Em||!km||!Dm},{sort:function(e){return void 0===e?Am.call(Ge(this)):Am.call(Ge(this),ot(e))}});var Nm=function(){function e(t){Gn(this,e),this.list=new Map,this._className="MessageListHandler",this._latestMessageSentByPeerMap=new Map,this._latestMessageSentByMeMap=new Map,this._groupLocalLastMessageSequenceMap=new Map}return Un(e,[{key:"getLocalOldestMessageByConversationID",value:function(e){if(!e)return null;if(!this.list.has(e))return null;var t=this.list.get(e).values();return t?t.next().value:null}},{key:"pushIn",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.conversationID,o=e.ID,r=!0;this.list.has(n)||this.list.set(n,new Map);var a=this.list.get(n).has(o);if(a){var s=this.list.get(n).get(o);if(!t||!0===s.isModified)return!1}return this.list.get(n).set(o,e),this._setLatestMessageSentByPeer(n,e),this._setLatestMessageSentByMe(n,e),this._setGroupLocalLastMessageSequence(n,e),r}},{key:"unshift",value:function(e){var t;if(zi(e)){if(e.length>0){t=e[0].conversationID;var n=e.length;this._unshiftMultipleMessages(e),this._setGroupLocalLastMessageSequence(t,e[n-1])}}else t=e.conversationID,this._unshiftSingleMessage(e),this._setGroupLocalLastMessageSequence(t,e);if(t&&t.startsWith(so.CONV_C2C)){var o=Array.from(this.list.get(t).values()),r=o.length;if(0===r)return;for(var a=r-1;a>=0;a--)if("out"===o[a].flow){this._setLatestMessageSentByMe(t,o[a]);break}for(var s=r-1;s>=0;s--)if("in"===o[s].flow){this._setLatestMessageSentByPeer(t,o[s]);break}}}},{key:"_unshiftSingleMessage",value:function(e){var t=e.conversationID,n=e.ID;if(!this.list.has(t))return this.list.set(t,new Map),void this.list.get(t).set(n,e);var o=Array.from(this.list.get(t));o.unshift([n,e]),this.list.set(t,new Map(o))}},{key:"_unshiftMultipleMessages",value:function(e){for(var t=e.length,n=[],o=e[0].conversationID,r=this.list.has(o)?Array.from(this.list.get(o)):[],a=0;a<t;a++)n.push([e[a].ID,e[a]]);this.list.set(o,new Map(n.concat(r)))}},{key:"remove",value:function(e){var t=e.conversationID,n=e.ID;this.list.has(t)&&this.list.get(t).delete(n)}},{key:"revoke",value:function(e,t,n){if(Bi.debug("revoke message",e,t,n),this.list.has(e)){var o,r=ro(this.list.get(e));try{for(r.s();!(o=r.n()).done;){var a=Qn(o.value,2)[1];if(a.sequence===t&&!a.isRevoked&&(Ji(n)||a.random===n))return a.isRevoked=!0,a}}catch(u){r.e(u)}finally{r.f()}}return null}},{key:"removeByConversationID",value:function(e){this.list.has(e)&&(this.list.delete(e),this._latestMessageSentByPeerMap.delete(e),this._latestMessageSentByMeMap.delete(e))}},{key:"updateMessageIsPeerReadProperty",value:function(e,t){var n=[];if(this.list.has(e)){var o,r=ro(this.list.get(e));try{for(r.s();!(o=r.n()).done;){var a=Qn(o.value,2)[1];a.time<=t&&!a.isPeerRead&&"out"===a.flow&&(a.isPeerRead=!0,n.push(a))}}catch(u){r.e(u)}finally{r.f()}Bi.log("".concat(this._className,".updateMessageIsPeerReadProperty conversationID:").concat(e," peerReadTime:").concat(t," count:").concat(n.length))}return n}},{key:"updateMessageIsModifiedProperty",value:function(e){var t=e.conversationID,n=e.ID;if(this.list.has(t)){var o=this.list.get(t).get(n);o&&(o.isModified=!0)}}},{key:"hasLocalMessageList",value:function(e){return this.list.has(e)}},{key:"getLocalMessageList",value:function(e){return this.hasLocalMessageList(e)?$n(this.list.get(e).values()):[]}},{key:"hasLocalMessage",value:function(e,t){return!!this.hasLocalMessageList(e)&&this.list.get(e).has(t)}},{key:"getLocalMessage",value:function(e,t){return this.hasLocalMessage(e,t)?this.list.get(e).get(t):null}},{key:"getLocalLastMessage",value:function(e){var t=this.getLocalMessageList(e);return t[t.length-1]}},{key:"_setLatestMessageSentByPeer",value:function(e,t){e.startsWith(so.CONV_C2C)&&"in"===t.flow&&this._latestMessageSentByPeerMap.set(e,t)}},{key:"_setLatestMessageSentByMe",value:function(e,t){e.startsWith(so.CONV_C2C)&&"out"===t.flow&&this._latestMessageSentByMeMap.set(e,t)}},{key:"_setGroupLocalLastMessageSequence",value:function(e,t){e.startsWith(so.CONV_GROUP)&&this._groupLocalLastMessageSequenceMap.set(e,t.sequence)}},{key:"getLatestMessageSentByPeer",value:function(e){return this._latestMessageSentByPeerMap.get(e)}},{key:"getLatestMessageSentByMe",value:function(e){return this._latestMessageSentByMeMap.get(e)}},{key:"getGroupLocalLastMessageSequence",value:function(e){return this._groupLocalLastMessageSequenceMap.get(e)||0}},{key:"modifyMessageSentByPeer",value:function(e){var t=e.conversationID,n=e.latestNick,o=e.latestAvatar,r=this.list.get(t);if(!Nu(r)){var a=Array.from(r.values()),s=a.length;if(0!==s){for(var i=null,u=0,c=!1,l=s-1;l>=0;l--)"in"===a[l].flow&&((i=a[l]).nick!==n&&(i.setNickAndAvatar({nick:n}),c=!0),i.avatar!==o&&(i.setNickAndAvatar({avatar:o}),c=!0),c&&(u+=1));Bi.log("".concat(this._className,".modifyMessageSentByPeer conversationID:").concat(t," count:").concat(u))}}}},{key:"modifyMessageSentByMe",value:function(e){var t=e.conversationID,n=e.latestNick,o=e.latestAvatar,r=this.list.get(t);if(!Nu(r)){var a=Array.from(r.values()),s=a.length;if(0!==s){for(var i=null,u=0,c=!1,l=s-1;l>=0;l--)"out"===a[l].flow&&((i=a[l]).nick!==n&&(i.setNickAndAvatar({nick:n}),c=!0),i.avatar!==o&&(i.setNickAndAvatar({avatar:o}),c=!0),c&&(u+=1));Bi.log("".concat(this._className,".modifyMessageSentByMe conversationID:").concat(t," count:").concat(u))}}}},{key:"traversal",value:function(){if(0!==this.list.size&&-1===Bi.getLevel()){var e,t=ro(this.list);try{for(t.s();!(e=t.n()).done;){var n=Qn(e.value,2);n[0],n[1]}}catch(i){t.e(i)}finally{t.f()}}}},{key:"reset",value:function(){this.list.clear(),this._latestMessageSentByPeerMap.clear(),this._latestMessageSentByMeMap.clear(),this._groupLocalLastMessageSequenceMap.clear()}}]),e}(),Om="_a2KeyAndTinyIDUpdated",Rm="_cloudConfigUpdated",Lm="_profileUpdated";function bm(e){this.mixin(e)}bm.mixin=function(e){var t=e.prototype||e;t._isReady=!1,t.ready=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e)return this._isReady?void(t?e.call(this):setTimeout(e,1)):(this._readyQueue=this._readyQueue||[],void this._readyQueue.push(e))},t.triggerReady=function(){var e=this;this._isReady=!0,setTimeout((function(){var t=e._readyQueue;e._readyQueue=[],t&&t.length>0&&t.forEach((function(e){e.call(this)}),e)}),1)},t.resetReady=function(){this._isReady=!1,this._readyQueue=[]},t.isReady=function(){return this._isReady}};var wm=["jpg","jpeg","gif","png","bmp","image"],Gm=["mp4"],Pm=1,Um=2,Fm=3,qm=255,xm=function(){function e(t){var n=this;Gn(this,e),Nu(t)||(this.userID=t.userID||"",this.nick=t.nick||"",this.gender=t.gender||"",this.birthday=t.birthday||0,this.location=t.location||"",this.selfSignature=t.selfSignature||"",this.allowType=t.allowType||so.ALLOW_TYPE_ALLOW_ANY,this.language=t.language||0,this.avatar=t.avatar||"",this.messageSettings=t.messageSettings||0,this.adminForbidType=t.adminForbidType||so.FORBID_TYPE_NONE,this.level=t.level||0,this.role=t.role||0,this.lastUpdatedTime=0,this.profileCustomField=[],Nu(t.profileCustomField)||t.profileCustomField.forEach((function(e){n.profileCustomField.push({key:e.key,value:e.value})})))}return Un(e,[{key:"validate",value:function(e){var t=!0,n="";if(Nu(e))return{valid:!1,tips:"empty options"};if(e.profileCustomField)for(var o=e.profileCustomField.length,r=null,a=0;a<o;a++){if(r=e.profileCustomField[a],!ji(r.key)||-1===r.key.indexOf("Tag_Profile_Custom"))return{valid:!1,tips:"自定义资料字段的前缀必须是 Tag_Profile_Custom"};if(!ji(r.value))return{valid:!1,tips:"自定义资料字段的 value 必须是字符串"}}for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if("profileCustomField"===s)continue;if(Nu(e[s])&&!ji(e[s])&&!Hi(e[s])){n="key:"+s+", invalid value:"+e[s],t=!1;continue}switch(s){case"nick":ji(e[s])||(n="nick should be a string",t=!1),au(e[s])>500&&(n="nick name limited: must less than or equal to ".concat(500," bytes, current size: ").concat(au(e[s])," bytes"),t=!1);break;case"gender":cu(U_,e.gender)||(n="key:gender, invalid value:"+e.gender,t=!1);break;case"birthday":Hi(e.birthday)||(n="birthday should be a number",t=!1);break;case"location":ji(e.location)||(n="location should be a string",t=!1);break;case"selfSignature":ji(e.selfSignature)||(n="selfSignature should be a string",t=!1);break;case"allowType":cu(q_,e.allowType)||(n="key:allowType, invalid value:"+e.allowType,t=!1);break;case"language":Hi(e.language)||(n="language should be a number",t=!1);break;case"avatar":ji(e.avatar)||(n="avatar should be a string",t=!1);break;case"messageSettings":0!==e.messageSettings&&1!==e.messageSettings&&(n="messageSettings should be 0 or 1",t=!1);break;case"adminForbidType":cu(F_,e.adminForbidType)||(n="key:adminForbidType, invalid value:"+e.adminForbidType,t=!1);break;case"level":Hi(e.level)||(n="level should be a number",t=!1);break;case"role":Hi(e.role)||(n="role should be a number",t=!1);break;default:n="unknown key:"+s+"  "+e[s],t=!1}}return{valid:t,tips:n}}}]),e}(),Vm=function e(t){Gn(this,e),this.value=t,this.next=null},Bm=function(){function e(t){Gn(this,e),this.MAX_LENGTH=t,this.pTail=null,this.pNodeToDel=null,this.map=new Map,Bi.debug("SinglyLinkedList init MAX_LENGTH:".concat(this.MAX_LENGTH))}return Un(e,[{key:"set",value:function(e){var t=new Vm(e);if(this.map.size<this.MAX_LENGTH)null===this.pTail?(this.pTail=t,this.pNodeToDel=t):(this.pTail.next=t,this.pTail=t),this.map.set(e,1);else{var n=this.pNodeToDel;this.pNodeToDel=this.pNodeToDel.next,this.map.delete(n.value),n.next=null,n=null,this.pTail.next=t,this.pTail=t,this.map.set(e,1)}}},{key:"has",value:function(e){return this.map.has(e)}},{key:"delete",value:function(e){this.has(e)&&this.map.delete(e)}},{key:"tail",value:function(){return this.pTail}},{key:"size",value:function(){return this.map.size}},{key:"data",value:function(){return Array.from(this.map.keys())}},{key:"reset",value:function(){for(var e;null!==this.pNodeToDel;)e=this.pNodeToDel,this.pNodeToDel=this.pNodeToDel.next,e.next=null,e=null;this.pTail=null,this.map.clear()}}]),e}(),Km=["groupID","name","avatar","type","introduction","notification","ownerID","selfInfo","createTime","infoSequence","lastInfoTime","lastMessage","nextMessageSeq","memberNum","maxMemberNum","memberList","joinOption","groupCustomField","muteAllMembers"],Hm=function(){function e(t){Gn(this,e),this.groupID="",this.name="",this.avatar="",this.type="",this.introduction="",this.notification="",this.ownerID="",this.createTime="",this.infoSequence="",this.lastInfoTime="",this.selfInfo={messageRemindType:"",joinTime:"",nameCard:"",role:"",userID:"",memberCustomField:void 0,readedSequence:0,excludedUnreadSequenceList:void 0},this.lastMessage={lastTime:"",lastSequence:"",fromAccount:"",messageForShow:""},this.nextMessageSeq="",this.memberNum="",this.memberCount="",this.maxMemberNum="",this.maxMemberCount="",this.joinOption="",this.groupCustomField=[],this.muteAllMembers=void 0,this._initGroup(t)}return Un(e,[{key:"_initGroup",value:function(e){for(var t in e)Km.indexOf(t)<0||("selfInfo"!==t?("memberNum"===t&&(this.memberCount=e[t]),"maxMemberNum"===t&&(this.maxMemberCount=e[t]),this[t]=e[t]):this.updateSelfInfo(e[t]))}},{key:"updateGroup",value:function(e){var t=this,n=JSON.parse(JSON.stringify(e));n.lastMsgTime&&(this.lastMessage.lastTime=n.lastMsgTime),Ji(n.muteAllMembers)||("On"===n.muteAllMembers?n.muteAllMembers=!0:n.muteAllMembers=!1),n.groupCustomField&&hu(this.groupCustomField,n.groupCustomField),Ji(n.memberNum)||(this.memberCount=n.memberNum),Ji(n.maxMemberNum)||(this.maxMemberCount=n.maxMemberNum),ou(this,n,["members","errorCode","lastMsgTime","groupCustomField","memberNum","maxMemberNum"]),zi(n.members)&&n.members.length>0&&n.members.forEach((function(e){e.userID===t.selfInfo.userID&&ou(t.selfInfo,e,["sequence"])}))}},{key:"updateSelfInfo",value:function(e){var t=e.nameCard,n=e.joinTime,o=e.role,r=e.messageRemindType,a=e.readedSequence,s=e.excludedUnreadSequenceList;ou(this.selfInfo,{nameCard:t,joinTime:n,role:o,messageRemindType:r,readedSequence:a,excludedUnreadSequenceList:s},[],["",null,void 0,0,NaN])}},{key:"setSelfNameCard",value:function(e){this.selfInfo.nameCard=e}},{key:"memberNum",set:function(e){},get:function(){return Bi.warn("！！！v2.8.0起弃用memberNum，请使用 memberCount"),this.memberCount}},{key:"maxMemberNum",set:function(e){},get:function(){return Bi.warn("！！！v2.8.0起弃用maxMemberNum，请使用 maxMemberCount"),this.maxMemberCount}}]),e}(),jm=function(e,t){if(Ji(t))return"";switch(e){case so.MSG_TEXT:return t.text;case so.MSG_IMAGE:return"[图片]";case so.MSG_LOCATION:return"[位置]";case so.MSG_AUDIO:return"[语音]";case so.MSG_VIDEO:return"[视频]";case so.MSG_FILE:return"[文件]";case so.MSG_CUSTOM:return"[自定义消息]";case so.MSG_GRP_TIP:return"[群提示消息]";case so.MSG_GRP_SYS_NOTICE:return"[群系统通知]";case so.MSG_FACE:return"[动画表情]";case so.MSG_MERGER:return"[聊天记录]";default:return""}},Wm=function(e){return Ji(e)?{lastTime:0,lastSequence:0,fromAccount:0,messageForShow:"",payload:null,type:"",isRevoked:!1,cloudCustomData:"",onlineOnlyFlag:!1,nick:"",nameCard:""}:e instanceof um?{lastTime:e.time||0,lastSequence:e.sequence||0,fromAccount:e.from||"",messageForShow:jm(e.type,e.payload),payload:e.payload||null,type:e.type||null,isRevoked:e.isRevoked||!1,cloudCustomData:e.cloudCustomData||"",onlineOnlyFlag:e._onlineOnlyFlag||!1,nick:e.nick||"",nameCard:e.nameCard||""}:xn(xn({},e),{},{messageForShow:jm(e.type,e.payload)})},Ym=function(){function e(t){Gn(this,e),this.conversationID=t.conversationID||"",this.unreadCount=t.unreadCount||0,this.type=t.type||"",this.lastMessage=Wm(t.lastMessage),t.lastMsgTime&&(this.lastMessage.lastTime=t.lastMsgTime),this._isInfoCompleted=!1,this.peerReadTime=t.peerReadTime||0,this.groupAtInfoList=[],this.remark="",this.isPinned=t.isPinned||!1,this.messageRemindType="",this._initProfile(t)}return Un(e,[{key:"_initProfile",value:function(e){var t=this;Object.keys(e).forEach((function(n){switch(n){case"userProfile":t.userProfile=e.userProfile;break;case"groupProfile":t.groupProfile=e.groupProfile}})),Ji(this.userProfile)&&this.type===so.CONV_C2C?this.userProfile=new xm({userID:e.conversationID.replace("C2C","")}):Ji(this.groupProfile)&&this.type===so.CONV_GROUP&&(this.groupProfile=new Hm({groupID:e.conversationID.replace("GROUP","")}))}},{key:"updateUnreadCount",value:function(e){var t=e.nextUnreadCount,n=e.isFromGetConversationList,o=e.isUnreadC2CMessage;Ji(t)||(_u(this.subType)?this.unreadCount=0:n&&this.type===so.CONV_GROUP||o&&this.type===so.CONV_C2C?this.unreadCount=t:this.unreadCount=this.unreadCount+t)}},{key:"updateLastMessage",value:function(e){this.lastMessage=Wm(e)}},{key:"updateGroupAtInfoList",value:function(e){var t,n=(Zn(t=e.groupAtType)||eo(t)||to(t)||oo()).slice(0);-1!==n.indexOf(so.CONV_AT_ME)&&-1!==n.indexOf(so.CONV_AT_ALL)&&(n=[so.CONV_AT_ALL_AT_ME]);var o={from:e.from,groupID:e.groupID,messageSequence:e.sequence,atTypeArray:n,__random:e.__random,__sequence:e.__sequence};this.groupAtInfoList.push(o),Bi.debug("Conversation.updateGroupAtInfoList conversationID:".concat(this.conversationID),this.groupAtInfoList)}},{key:"clearGroupAtInfoList",value:function(){this.groupAtInfoList.length=0}},{key:"reduceUnreadCount",value:function(){this.unreadCount>=1&&(this.unreadCount-=1)}},{key:"isLastMessageRevoked",value:function(e){var t=e.sequence,n=e.time;return this.type===so.CONV_C2C&&t===this.lastMessage.lastSequence&&n===this.lastMessage.lastTime||this.type===so.CONV_GROUP&&t===this.lastMessage.lastSequence}},{key:"setLastMessageRevoked",value:function(e){this.lastMessage.isRevoked=e}},{key:"toAccount",get:function(){return this.conversationID.startsWith(so.CONV_C2C)?this.conversationID.replace(so.CONV_C2C,""):this.conversationID.startsWith(so.CONV_GROUP)?this.conversationID.replace(so.CONV_GROUP,""):""}},{key:"subType",get:function(){return this.groupProfile?this.groupProfile.type:""}}]),e}(),zm=function(){function e(t){Gn(this,e),this._conversationModule=t,this._className="MessageRemindHandler",this._updateSequence=0}return Un(e,[{key:"getC2CMessageRemindType",value:function(){var e=this,t="".concat(this._className,".getC2CMessageRemindType");return this._conversationModule.request({protocolName:El,updateSequence:this._updateSequence}).then((function(n){Bi.log("".concat(t," ok"));var o=n.data,r=o.updateSequence,a=o.muteFlagList;e._updateSequence=r,e._patchC2CMessageRemindType(a)})).catch((function(e){Bi.error("".concat(t," failed. error:"),e)}))}},{key:"_patchC2CMessageRemindType",value:function(e){var t=this,n=0,o="";zi(e)&&e.length>0&&e.forEach((function(e){var r=e.userID,a=e.muteFlag;0===a?o=so.MSG_REMIND_ACPT_AND_NOTE:1===a?o=so.MSG_REMIND_DISCARD:2===a&&(o=so.MSG_REMIND_ACPT_NOT_NOTE),!0===t._conversationModule.patchMessageRemindType({ID:r,isC2CConversation:!0,messageRemindType:o})&&(n+=1)})),Bi.log("".concat(this._className,"._patchC2CMessageRemindType count:").concat(n))}},{key:"set",value:function(e){return e.groupID?this._setGroupMessageRemindType(e):zi(e.userIDList)?this._setC2CMessageRemindType(e):void 0}},{key:"_setGroupMessageRemindType",value:function(e){var t=this,n="".concat(this._className,"._setGroupMessageRemindType"),o=e.groupID,r=e.messageRemindType,a="groupID:".concat(o," messageRemindType:").concat(r),s=new Zp(Vg);return s.setMessage(a),this._getModule(jc).modifyGroupMemberInfo({groupID:o,messageRemindType:r,userID:this._conversationModule.getMyUserID()}).then((function(){s.setNetworkType(t._conversationModule.getNetworkType()).end(),Bi.log("".concat(n," ok. ").concat(a));var e=t._getModule(Kc).getLocalGroupProfile(o);return e&&(e.selfInfo.messageRemindType=r),t._conversationModule.patchMessageRemindType({ID:o,isC2CConversation:!1,messageRemindType:r})&&t._emitConversationUpdate(),cm({group:e})})).catch((function(e){return t._conversationModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];s.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"_setC2CMessageRemindType",value:function(e){var t=this,n="".concat(this._className,"._setC2CMessageRemindType"),o=e.userIDList,r=e.messageRemindType,a=o.slice(0,30),s=0;r===so.MSG_REMIND_DISCARD?s=1:r===so.MSG_REMIND_ACPT_NOT_NOTE&&(s=2);var i="userIDList:".concat(a," messageRemindType:").concat(r),u=new Zp(Vg);return u.setMessage(i),this._conversationModule.request({protocolName:Al,requestData:{userIDList:a,muteFlag:s}}).then((function(e){u.setNetworkType(t._conversationModule.getNetworkType()).end();var o=e.data,s=o.updateSequence,i=o.errorList;t._updateSequence=s;var c=[],l=[];zi(i)&&i.forEach((function(e){c.push(e.userID),l.push({userID:e.userID,code:e.errorCode})}));var d=a.filter((function(e){return-1===c.indexOf(e)}));Bi.log("".concat(n," ok. successUserIDList:").concat(d," failureUserIDList:").concat(JSON.stringify(l)));var p=0;return d.forEach((function(e){t._conversationModule.patchMessageRemindType({ID:e,isC2CConversation:!0,messageRemindType:r})&&(p+=1)})),p>=1&&t._emitConversationUpdate(),a.length=c.length=0,vm({successUserIDList:d.map((function(e){return{userID:e}})),failureUserIDList:l})})).catch((function(e){return t._conversationModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];u.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"_getModule",value:function(e){return this._conversationModule.getModule(e)}},{key:"_emitConversationUpdate",value:function(){this._conversationModule.emitConversationUpdate(!0,!1)}},{key:"setUpdateSequence",value:function(e){this._updateSequence=e}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._updateSequence=0}}]),e}(),Jm=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="ConversationModule",bm.mixin(zn(o)),o._messageListHandler=new Nm,o._messageRemindHandler=new zm(zn(o)),o.singlyLinkedList=new Bm(100),o._pagingStatus=Sc.NOT_START,o._pagingTimeStamp=0,o._pagingStartIndex=0,o._pagingPinnedTimeStamp=0,o._pagingPinnedStartIndex=0,o._conversationMap=new Map,o._tmpGroupList=[],o._tmpGroupAtTipsList=[],o._peerReadTimeMap=new Map,o._completedMap=new Map,o._roamingMessageKeyAndTimeMap=new Map,o._remoteGroupReadSequenceMap=new Map,o._initListeners(),o}return Un(n,[{key:"_initListeners",value:function(){var e=this.getInnerEmitterInstance();e.on(Om,this._initLocalConversationList,this),e.on(Lm,this._onProfileUpdated,this)}},{key:"onCheckTimer",value:function(e){e%60==0&&this._messageListHandler.traversal()}},{key:"_initLocalConversationList",value:function(){var e=this,t=new Zp(Rg);Bi.log("".concat(this._className,"._initLocalConversationList."));var n="",o=this._getStorageConversationList();if(o){for(var r=o.length,a=0;a<r;a++){var s=o[a];if(s){if(s.conversationID==="".concat(so.CONV_C2C,"@TLS#ERROR")||s.conversationID==="".concat(so.CONV_C2C,"@TLS#NOT_FOUND"))continue;if(s.groupProfile){var i=s.groupProfile.type;if(_u(i))continue}}this._conversationMap.set(o[a].conversationID,new Ym(o[a]))}this.emitConversationUpdate(!0,!1),n="count:".concat(r)}else n="count:0";t.setNetworkType(this.getNetworkType()).setMessage(n).end(),this.getModule(Bc)||this.triggerReady(),this.ready((function(){e._tmpGroupList.length>0&&(e.updateConversationGroupProfile(e._tmpGroupList),e._tmpGroupList.length=0)})),this._syncConversationList()}},{key:"onMessageSent",value:function(e){this._onSendOrReceiveMessage({conversationOptionsList:e.conversationOptionsList,isInstantMessage:!0})}},{key:"onNewMessage",value:function(e){this._onSendOrReceiveMessage(e)}},{key:"_onSendOrReceiveMessage",value:function(e){var t=this,n=e.conversationOptionsList,o=e.isInstantMessage,r=void 0===o||o,a=e.isUnreadC2CMessage,s=void 0!==a&&a;this._isReady?0!==n.length&&(this._getC2CPeerReadTime(n),this._updateLocalConversationList({conversationOptionsList:n,isInstantMessage:r,isUnreadC2CMessage:s,isFromGetConversations:!1}),this._setStorageConversationList(),this.emitConversationUpdate()):this.ready((function(){t._onSendOrReceiveMessage(e)}))}},{key:"updateConversationGroupProfile",value:function(e){var t=this;zi(e)&&0===e.length||(0!==this._conversationMap.size?(e.forEach((function(e){var n="GROUP".concat(e.groupID);if(t._conversationMap.has(n)){var o=t._conversationMap.get(n);o.groupProfile=e,o.lastMessage.lastSequence<e.nextMessageSeq&&(o.lastMessage.lastSequence=e.nextMessageSeq-1),o.subType||(o.subType=e.type)}})),this.emitConversationUpdate(!0,!1)):this._tmpGroupList=e)}},{key:"_updateConversationUserProfile",value:function(e){var t=this;e.data.forEach((function(e){var n="C2C".concat(e.userID);t._conversationMap.has(n)&&(t._conversationMap.get(n).userProfile=e)})),this.emitConversationUpdate(!0,!1)}},{key:"onMessageRevoked",value:function(e){var t=this;if(0!==e.length){var n=null,o=!1;e.forEach((function(e){(n=t._conversationMap.get(e.conversationID))&&n.isLastMessageRevoked(e)&&(o=!0,n.setLastMessageRevoked(!0))})),o&&this.emitConversationUpdate(!0,!1)}}},{key:"onMessageDeleted",value:function(e){if(0!==e.length){e.forEach((function(e){e.isDeleted=!0}));for(var t=e[0].conversationID,n=this._messageListHandler.getLocalMessageList(t),o={},r=n.length-1;r>=0;r--)if(!n[r].isDeleted){o=n[r];break}var a=this._conversationMap.get(t);if(a){var s=!1;a.lastMessage.lastSequence===o.sequence&&a.lastMessage.lastTime===o.time||(Nu(o)&&(o=void 0),a.updateLastMessage(o),s=!0,Bi.log("".concat(this._className,".onMessageDeleted. update conversationID:").concat(t," with lastMessage:"),a.lastMessage)),t.startsWith(so.CONV_C2C)&&this.updateUnreadCount(t),s&&this.emitConversationUpdate(!0,!1)}}}},{key:"onNewGroupAtTips",value:function(e){var t=this,n=e.dataList,o=null;n.forEach((function(e){e.groupAtTips?o=e.groupAtTips:e.elements&&(o=e.elements),o.__random=e.random,o.__sequence=e.clientSequence,t._tmpGroupAtTipsList.push(o)})),Bi.debug("".concat(this._className,".onNewGroupAtTips isReady:").concat(this._isReady),this._tmpGroupAtTipsList),this._isReady&&this._handleGroupAtTipsList()}},{key:"_handleGroupAtTipsList",value:function(){var e=this;if(0!==this._tmpGroupAtTipsList.length){var t=!1;this._tmpGroupAtTipsList.forEach((function(n){var o=n.groupID;if(n.from!==e.getMyUserID()){var r=e._conversationMap.get("".concat(so.CONV_GROUP).concat(o));r&&(r.updateGroupAtInfoList(n),t=!0)}})),t&&this.emitConversationUpdate(!0,!1),this._tmpGroupAtTipsList.length=0}}},{key:"_getC2CPeerReadTime",value:function(e){var t=this,n=[];if(e.forEach((function(e){t._conversationMap.has(e.conversationID)||e.type!==so.CONV_C2C||n.push(e.conversationID.replace(so.CONV_C2C,""))})),n.length>0){Bi.debug("".concat(this._className,"._getC2CPeerReadTime userIDList:").concat(n));var o=this.getModule(Bc);o&&o.getRemotePeerReadTime(n)}}},{key:"_getStorageConversationList",value:function(){return this.getModule(zc).getItem("conversationMap")}},{key:"_setStorageConversationList",value:function(){var e=this.getLocalConversationList().slice(0,20).map((function(e){return{conversationID:e.conversationID,type:e.type,subType:e.subType,lastMessage:e.lastMessage,groupProfile:e.groupProfile,userProfile:e.userProfile}}));this.getModule(zc).setItem("conversationMap",e)}},{key:"emitConversationUpdate",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=$n(this._conversationMap.values());if(t){var o=this.getModule(Kc);o&&o.updateGroupLastMessage(n)}e&&this.emitOuterEvent(ao.CONVERSATION_LIST_UPDATED,n)}},{key:"getLocalConversationList",value:function(){return $n(this._conversationMap.values())}},{key:"getLocalConversation",value:function(e){return this._conversationMap.get(e)}},{key:"_syncConversationList",value:function(){var e=this,t=new Zp(Lg);return this._pagingStatus===Sc.NOT_START&&this._conversationMap.clear(),this._pagingGetConversationList().then((function(n){return e._pagingStatus=Sc.RESOLVED,e._setStorageConversationList(),e._handleC2CPeerReadTime(),e._patchConversationProperties(),t.setMessage(e._conversationMap.size).setNetworkType(e.getNetworkType()).end(),n})).catch((function(n){return e._pagingStatus=Sc.REJECTED,t.setMessage(e._pagingTimeStamp),e.probeNetwork().then((function(e){var o=Qn(e,2),r=o[0],a=o[1];t.setError(n,r,a).end()})),Mm(n)}))}},{key:"_patchConversationProperties",value:function(){var e=this,t=Date.now(),n=this.checkAndPatchRemark(),o=this._messageRemindHandler.getC2CMessageRemindType(),r=this.getModule(Kc).getGroupList();Promise.all([n,o,r]).then((function(){var n=Date.now()-t;Bi.log("".concat(e._className,"._patchConversationProperties ok. cost ").concat(n," ms")),e.emitConversationUpdate(!0,!1)}))}},{key:"_pagingGetConversationList",value:function(){var e=this,t="".concat(this._className,"._pagingGetConversationList");return Bi.log("".concat(t," timeStamp:").concat(this._pagingTimeStamp," startIndex:").concat(this._pagingStartIndex)+" pinnedTimeStamp:".concat(this._pagingPinnedTimeStamp," pinnedStartIndex:").concat(this._pagingPinnedStartIndex)),this._pagingStatus=Sc.PENDING,this.request({protocolName:Ol,requestData:{fromAccount:this.getMyUserID(),timeStamp:this._pagingTimeStamp,startIndex:this._pagingStartIndex,pinnedTimeStamp:this._pagingPinnedTimeStamp,pinnedStartIndex:this._pagingStartIndex,orderType:1}}).then((function(n){var o=n.data,r=o.completeFlag,a=o.conversations,s=void 0===a?[]:a,i=o.timeStamp,u=o.startIndex,c=o.pinnedTimeStamp,l=o.pinnedStartIndex;if(Bi.log("".concat(t," ok. completeFlag:").concat(r," count:").concat(s.length," isReady:").concat(e._isReady)),s.length>0){var d=e._getConversationOptions(s);e._updateLocalConversationList({conversationOptionsList:d,isFromGetConversations:!0}),e.isLoggedIn()&&e.emitConversationUpdate()}if(!e._isReady){if(!e.isLoggedIn())return vm();e.triggerReady()}return e._pagingTimeStamp=i,e._pagingStartIndex=u,e._pagingPinnedTimeStamp=c,e._pagingPinnedStartIndex=l,1!==r?e._pagingGetConversationList():(e._handleGroupAtTipsList(),vm())})).catch((function(n){throw e.isLoggedIn()&&(e._isReady||(Bi.warn("".concat(t," failed. error:"),n),e.triggerReady())),n}))}},{key:"_updateLocalConversationList",value:function(e){var t,n=e.isFromGetConversations,o=Date.now();t=this._getTmpConversationListMapping(e),this._conversationMap=new Map(this._sortConversationList([].concat($n(t.toBeUpdatedConversationList),$n(this._conversationMap)))),n||this._updateUserOrGroupProfile(t.newConversationList),Bi.debug("".concat(this._className,"._updateLocalConversationList cost ").concat(Date.now()-o," ms"))}},{key:"_getTmpConversationListMapping",value:function(e){for(var t=e.conversationOptionsList,n=e.isFromGetConversations,o=e.isInstantMessage,r=e.isUnreadC2CMessage,a=void 0!==r&&r,s=[],i=[],u=this.getModule(Kc),c=this.getModule(Hc),l=0,d=t.length;l<d;l++){var p=new Ym(t[l]),g=p.conversationID;if(g!=="".concat(so.CONV_C2C,"@TLS#ERROR")&&g!=="".concat(so.CONV_C2C,"@TLS#NOT_FOUND"))if(this._conversationMap.has(g)){var h=this._conversationMap.get(g),f=["unreadCount","allowType","adminForbidType","payload","isPinned"];!1===o&&f.push("lastMessage");var _=t[l].lastMessage,m=!Ji(_);m||this._onLastMessageNotExist(t[l]),Ji(o)&&m&&null===h.lastMessage.payload&&(h.lastMessage.payload=_.payload),ou(h,p,f,[null,void 0,"",0,NaN]),h.updateUnreadCount({nextUnreadCount:p.unreadCount,isFromGetConversations:n,isUnreadC2CMessage:a}),o&&m&&(h.lastMessage.payload=_.payload,h.type===so.CONV_GROUP&&(h.lastMessage.nameCard=_.nameCard,h.lastMessage.nick=_.nick)),m&&h.lastMessage.cloudCustomData!==_.cloudCustomData&&(h.lastMessage.cloudCustomData=_.cloudCustomData||""),this._conversationMap.delete(g),s.push([g,h])}else{if(p.type===so.CONV_GROUP&&u){var v=p.groupProfile.groupID,M=u.getLocalGroupProfile(v);M&&(p.groupProfile=M,p.updateUnreadCount({nextUnreadCount:0}))}else if(p.type===so.CONV_C2C){var y=g.replace(so.CONV_C2C,"");c&&c.isMyFriend(y)&&(p.remark=c.getFriendRemark(y))}i.push(p),s.push([g,p])}}return{toBeUpdatedConversationList:s,newConversationList:i}}},{key:"_onLastMessageNotExist",value:function(e){new Zp(Ah).setMessage("".concat(JSON.stringify(e))).setNetworkType(this.getNetworkType()).end()}},{key:"_sortConversationList",value:function(e){var t=[],n=[];return e.forEach((function(e){!0===e[1].isPinned?t.push(e):n.push(e)})),t.sort((function(e,t){return t[1].lastMessage.lastTime-e[1].lastMessage.lastTime})).concat(n.sort((function(e,t){return t[1].lastMessage.lastTime-e[1].lastMessage.lastTime})))}},{key:"_sortConversationListAndEmitEvent",value:function(){this._conversationMap=new Map(this._sortConversationList($n(this._conversationMap))),this.emitConversationUpdate(!0,!1)}},{key:"_updateUserOrGroupProfile",value:function(e){var t=this;if(0!==e.length){var n=[],o=[],r=this.getModule(Vc),a=this.getModule(Kc);e.forEach((function(e){if(e.type===so.CONV_C2C)n.push(e.toAccount);else if(e.type===so.CONV_GROUP){var t=e.toAccount;a.hasLocalGroup(t)?e.groupProfile=a.getLocalGroupProfile(t):o.push(t)}})),Bi.log("".concat(this._className,"._updateUserOrGroupProfile c2cUserIDList:").concat(n," groupIDList:").concat(o)),n.length>0&&r.getUserProfile({userIDList:n}).then((function(e){var n=e.data;zi(n)?n.forEach((function(e){t._conversationMap.get("C2C".concat(e.userID)).userProfile=e})):t._conversationMap.get("C2C".concat(n.userID)).userProfile=n})),o.length>0&&a.getGroupProfileAdvance({groupIDList:o,responseFilter:{groupBaseInfoFilter:["Type","Name","FaceUrl"]}}).then((function(e){e.data.successGroupList.forEach((function(e){var n="GROUP".concat(e.groupID);if(t._conversationMap.has(n)){var o=t._conversationMap.get(n);ou(o.groupProfile,e,[],[null,void 0,"",0,NaN]),!o.subType&&e.type&&(o.subType=e.type)}}))}))}}},{key:"_getConversationOptions",value:function(e){var t=[],n=e.filter((function(e){var t=e.lastMsg;return Yi(t)})).filter((function(e){var t=e.type,n=e.userID;return 1===t&&"@TLS#NOT_FOUND"!==n&&"@TLS#ERROR"!==n||2===t})).map((function(e){if(1===e.type){var n={userID:e.userID,nick:e.peerNick,avatar:e.peerAvatar};return t.push(n),{conversationID:"C2C".concat(e.userID),type:"C2C",lastMessage:{lastTime:e.time,lastSequence:e.sequence,fromAccount:e.lastC2CMsgFromAccount,messageForShow:e.messageShow,type:e.lastMsg.elements[0]?e.lastMsg.elements[0].type:null,payload:e.lastMsg.elements[0]?e.lastMsg.elements[0].content:null,cloudCustomData:e.cloudCustomData||"",isRevoked:8===e.lastMessageFlag,onlineOnlyFlag:!1,nick:"",nameCard:""},userProfile:new xm(n),peerReadTime:e.c2cPeerReadTime,isPinned:1===e.isPinned,messageRemindType:""}}return{conversationID:"GROUP".concat(e.groupID),type:"GROUP",lastMessage:{lastTime:e.time,lastSequence:e.messageReadSeq+e.unreadCount,fromAccount:e.msgGroupFromAccount,messageForShow:e.messageShow,type:e.lastMsg.elements[0]?e.lastMsg.elements[0].type:null,payload:e.lastMsg.elements[0]?e.lastMsg.elements[0].content:null,cloudCustomData:e.cloudCustomData||"",isRevoked:2===e.lastMessageFlag,onlineOnlyFlag:!1,nick:e.senderNick||"",nameCard:e.senderNameCard||""},groupProfile:new Hm({groupID:e.groupID,name:e.groupNick,avatar:e.groupImage}),unreadCount:e.unreadCount,peerReadTime:0,isPinned:1===e.isPinned,messageRemindType:""}}));return t.length>0&&this.getModule(Vc).onConversationsProfileUpdated(t),n}},{key:"getLocalMessageList",value:function(e){return this._messageListHandler.getLocalMessageList(e)}},{key:"deleteLocalMessage",value:function(e){e instanceof um&&this._messageListHandler.remove(e)}},{key:"onConversationDeleted",value:function(e){var t=this;Bi.log("".concat(this._className,".onConversationDeleted")),zi(e)&&e.forEach((function(e){var n=e.type,o=e.userID,r=e.groupID,a="";1===n?a="".concat(so.CONV_C2C).concat(o):2===n&&(a="".concat(so.CONV_GROUP).concat(r)),t.deleteLocalConversation(a)}))}},{key:"onConversationPinned",value:function(e){var t=this;if(zi(e)){var n=!1;e.forEach((function(e){var o,r=e.type,a=e.userID,s=e.groupID;1===r?o=t.getLocalConversation("".concat(so.CONV_C2C).concat(a)):2===r&&(o=t.getLocalConversation("".concat(so.CONV_GROUP).concat(s))),o&&(Bi.log("".concat(t._className,".onConversationPinned conversationID:").concat(o.conversationID," isPinned:").concat(o.isPinned)),o.isPinned||(o.isPinned=!0,n=!0))})),n&&this._sortConversationListAndEmitEvent()}}},{key:"onConversationUnpinned",value:function(e){var t=this;if(zi(e)){var n=!1;e.forEach((function(e){var o,r=e.type,a=e.userID,s=e.groupID;1===r?o=t.getLocalConversation("".concat(so.CONV_C2C).concat(a)):2===r&&(o=t.getLocalConversation("".concat(so.CONV_GROUP).concat(s))),o&&(Bi.log("".concat(t._className,".onConversationUnpinned conversationID:").concat(o.conversationID," isPinned:").concat(o.isPinned)),o.isPinned&&(o.isPinned=!1,n=!0))})),n&&this._sortConversationListAndEmitEvent()}}},{key:"getMessageList",value:function(e){var t=this,n=e.conversationID,o=e.nextReqMessageID,r=e.count,a="".concat(this._className,".getMessageList"),s=this.getLocalConversation(n),i="";if(s&&s.groupProfile&&(i=s.groupProfile.type),_u(i))return Bi.log("".concat(a," not available in avchatroom. conversationID:").concat(n)),vm({messageList:[],nextReqMessageID:"",isCompleted:!0});(Ji(r)||r>15)&&(r=15);var u=this._computeLeftCount({conversationID:n,nextReqMessageID:o});return Bi.log("".concat(a," conversationID:").concat(n," leftCount:").concat(u," count:").concat(r," nextReqMessageID:").concat(o)),this._needGetHistory({conversationID:n,leftCount:u,count:r})?this.getHistoryMessages({conversationID:n,nextReqMessageID:o,count:20}).then((function(){return u=t._computeLeftCount({conversationID:n,nextReqMessageID:o}),cm(t._computeResult({conversationID:n,nextReqMessageID:o,count:r,leftCount:u}))})):(Bi.log("".concat(a,".getMessageList get message list from memory")),this.modifyMessageList(n),vm(this._computeResult({conversationID:n,nextReqMessageID:o,count:r,leftCount:u})))}},{key:"_computeLeftCount",value:function(e){var t=e.conversationID,n=e.nextReqMessageID;return n?this._messageListHandler.getLocalMessageList(t).findIndex((function(e){return e.ID===n})):this._getMessageListSize(t)}},{key:"_getMessageListSize",value:function(e){return this._messageListHandler.getLocalMessageList(e).length}},{key:"_needGetHistory",value:function(e){var t=e.conversationID,n=e.leftCount,o=e.count,r=this.getLocalConversation(t),a="";return r&&r.groupProfile&&(a=r.groupProfile.type),!Mu(t)&&!_u(a)&&n<o&&!this._completedMap.has(t)}},{key:"_computeResult",value:function(e){var t=e.conversationID,n=e.nextReqMessageID,o=e.count,r=e.leftCount,a=this._computeMessageList({conversationID:t,nextReqMessageID:n,count:o}),s=this._computeIsCompleted({conversationID:t,leftCount:r,count:o}),i=this._computeNextReqMessageID({messageList:a,isCompleted:s,conversationID:t}),u="".concat(this._className,"._computeResult. conversationID:").concat(t);return Bi.log("".concat(u," leftCount:").concat(r," count:").concat(o," nextReqMessageID:").concat(i," isCompleted:").concat(s)),{messageList:a,nextReqMessageID:i,isCompleted:s}}},{key:"_computeMessageList",value:function(e){var t=e.conversationID,n=e.nextReqMessageID,o=e.count,r=this._messageListHandler.getLocalMessageList(t),a=this._computeIndexEnd({nextReqMessageID:n,messageList:r}),s=this._computeIndexStart({indexEnd:a,count:o});return r.slice(s,a)}},{key:"_computeNextReqMessageID",value:function(e){var t=e.messageList,n=e.isCompleted,o=e.conversationID;if(!n)return 0===t.length?"":t[0].ID;var r=this._messageListHandler.getLocalMessageList(o);return 0===r.length?"":r[0].ID}},{key:"_computeIndexEnd",value:function(e){var t=e.messageList,n=void 0===t?[]:t,o=e.nextReqMessageID;return o?n.findIndex((function(e){return e.ID===o})):n.length}},{key:"_computeIndexStart",value:function(e){var t=e.indexEnd,n=e.count;return t>n?t-n:0}},{key:"_computeIsCompleted",value:function(e){var t=e.conversationID;return!!(e.leftCount<=e.count&&this._completedMap.has(t))}},{key:"getHistoryMessages",value:function(e){var t=e.conversationID,n=e.nextReqMessageID;if(t===so.CONV_SYSTEM)return vm();e.count?e.count>20&&(e.count=20):e.count=15;var o=this._messageListHandler.getLocalOldestMessageByConversationID(t);o||((o={}).time=0,o.sequence=0,0===t.indexOf(so.CONV_C2C)?(o.to=t.replace(so.CONV_C2C,""),o.conversationType=so.CONV_C2C):0===t.indexOf(so.CONV_GROUP)&&(o.to=t.replace(so.CONV_GROUP,""),o.conversationType=so.CONV_GROUP));var r="",a=null,s=this._roamingMessageKeyAndTimeMap.has(t);switch(o.conversationType){case so.CONV_C2C:return r=t.replace(so.CONV_C2C,""),(a=this.getModule(Bc))?a.getRoamingMessage({conversationID:e.conversationID,peerAccount:r,count:e.count,lastMessageTime:s?this._roamingMessageKeyAndTimeMap.get(t).lastMessageTime:0,messageKey:s?this._roamingMessageKeyAndTimeMap.get(t).messageKey:""}):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp});case so.CONV_GROUP:return(a=this.getModule(Kc))?a.getRoamingMessage({conversationID:e.conversationID,groupID:o.to,count:e.count,sequence:n&&!1===o._onlineOnlyFlag?o.sequence-1:o.sequence}):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp});default:return vm()}}},{key:"patchConversationLastMessage",value:function(e){var t=this.getLocalConversation(e);if(t){var n=t.lastMessage,o=n.messageForShow,r=n.payload;if(Nu(o)||Nu(r)){var a=this._messageListHandler.getLocalMessageList(e);if(0===a.length)return;var s=a[a.length-1];Bi.log("".concat(this._className,".patchConversationLastMessage conversationID:").concat(e," payload:"),s.payload),t.updateLastMessage(s)}}}},{key:"storeRoamingMessage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=t.startsWith(so.CONV_C2C)?so.CONV_C2C:so.CONV_GROUP,o=null,r=[],a=0,s=e.length,i=null,u=n===so.CONV_GROUP,c=this.getModule(Qc),l=function(){a=u?e.length-1:0,s=u?0:e.length},d=function(){u?--a:++a},p=function(){return u?a>=s:a<s};for(l();p();d())if(u&&1===e[a].sequence&&this.setCompleted(t),1!==e[a].isPlaceMessage)if((o=new um(e[a])).to=e[a].to,o.isSystemMessage=!!e[a].isSystemMessage,o.conversationType=n,4===e[a].event?i={type:so.MSG_GRP_TIP,content:xn(xn({},e[a].elements),{},{groupProfile:e[a].groupProfile})}:(e[a].elements=c.parseElements(e[a].elements,e[a].from),i=e[a].elements),u||o.setNickAndAvatar({nick:e[a].nick,avatar:e[a].avatar}),Nu(i)){var g=new Zp(Ig);g.setMessage("from:".concat(o.from," to:").concat(o.to," sequence:").concat(o.sequence," event:").concat(e[a].event)),g.setNetworkType(this.getNetworkType()).setLevel("warning").end()}else o.setElement(i),o.reInitialize(this.getMyUserID()),r.push(o);return this._messageListHandler.unshift(r),l=d=p=null,r}},{key:"setMessageRead",value:function(e){var t=e.conversationID,n=(e.messageID,this.getLocalConversation(t));if(Bi.log("".concat(this._className,".setMessageRead conversationID:").concat(t," unreadCount:").concat(n?n.unreadCount:0)),!n)return vm();if(n.type!==so.CONV_GROUP||Nu(n.groupAtInfoList)||this.deleteGroupAtTips(t),0===n.unreadCount)return vm();var o=this._messageListHandler.getLocalLastMessage(t),r=n.lastMessage.lastTime;o&&r<o.time&&(r=o.time);var a=n.lastMessage.lastSequence;o&&a<o.sequence&&(a=o.sequence);var s=null;switch(n.type){case so.CONV_C2C:return(s=this.getModule(Bc))?s.setMessageRead({conversationID:t,lastMessageTime:r}):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp});case so.CONV_GROUP:return(s=this._moduleManager.getModule(Kc))?s.setMessageRead({conversationID:t,lastMessageSeq:a}):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp});case so.CONV_SYSTEM:return n.unreadCount=0,this.emitConversationUpdate(!0,!1),vm();default:return vm()}}},{key:"setAllMessageRead",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n="".concat(this._className,".setAllMessageRead");t.scope||(t.scope=so.READ_ALL_MSG),Bi.log("".concat(n," options:"),t);var o=this._createSetAllMessageReadPack(t);if(0===o.readAllC2CMessage&&0===o.groupMessageReadInfoList.length)return vm();var r=new Zp(bg);return this.request({protocolName:Jl,requestData:o}).then((function(n){var o=n.data,a=e._handleAllMessageRead(o);return r.setMessage("scope:".concat(t.scope," failureGroups:").concat(JSON.stringify(a))).setNetworkType(e.getNetworkType()).end(),vm()})).catch((function(t){return e.probeNetwork().then((function(e){var n=Qn(e,2),o=n[0],a=n[1];r.setError(t,o,a).end()})),Bi.warn("".concat(n," failed. error:"),t),Mm({code:t&&t.code?t.code:kd.MESSAGE_UNREAD_ALL_FAIL,message:t&&t.message?t.message:Hd})}))}},{key:"_getConversationLastMessageSequence",value:function(e){var t=this._messageListHandler.getLocalLastMessage(e.conversationID),n=e.lastMessage.lastSequence;return t&&n<t.sequence&&(n=t.sequence),n}},{key:"_getConversationLastMessageTime",value:function(e){var t=this._messageListHandler.getLocalLastMessage(e.conversationID),n=e.lastMessage.lastTime;return t&&n<t.time&&(n=t.time),n}},{key:"_createSetAllMessageReadPack",value:function(e){var t,n={readAllC2CMessage:0,groupMessageReadInfoList:[]},o=e.scope,r=ro(this._conversationMap);try{for(r.s();!(t=r.n()).done;){var a=Qn(t.value,2)[1];if(a.unreadCount>0)if(a.type===so.CONV_C2C&&0===n.readAllC2CMessage){if(o===so.READ_ALL_MSG)n.readAllC2CMessage=1;else if(o===so.READ_ALL_C2C_MSG){n.readAllC2CMessage=1;break}}else if(a.type===so.CONV_GROUP&&(o===so.READ_ALL_GROUP_MSG||o===so.READ_ALL_MSG)){var s=this._getConversationLastMessageSequence(a);n.groupMessageReadInfoList.push({groupID:a.groupProfile.groupID,messageSequence:s})}}}catch(c){r.e(c)}finally{r.f()}return n}},{key:"onPushedAllMessageRead",value:function(e){this._handleAllMessageRead(e)}},{key:"_handleAllMessageRead",value:function(e){var t=e.groupMessageReadInfoList,n=e.readAllC2CMessage,o=this._parseGroupReadInfo(t);return this._updateAllConversationUnreadCount({readAllC2CMessage:n})>=1&&this.emitConversationUpdate(!0,!1),o}},{key:"_parseGroupReadInfo",value:function(e){var t=[];if(e&&e.length)for(var n=0,o=e.length;n<o;n++){var r=e[n],a=r.groupID,s=r.sequence,i=r.retCode,u=r.lastMessageSeq;Ji(i)?this._remoteGroupReadSequenceMap.set(a,u):(this._remoteGroupReadSequenceMap.set(a,s),0!==i&&t.push("".concat(a,"-").concat(s,"-").concat(i)))}return t}},{key:"_updateAllConversationUnreadCount",value:function(e){var t,n=e.readAllC2CMessage,o=0,r=ro(this._conversationMap);try{for(r.s();!(t=r.n()).done;){var a=Qn(t.value,2),s=a[0],i=a[1];if(i.unreadCount>=1){if(1===n&&i.type===so.CONV_C2C){var u=this._getConversationLastMessageTime(i);this.updateIsReadAfterReadReport({conversationID:s,lastMessageTime:u})}else if(i.type===so.CONV_GROUP){var c=s.replace(so.CONV_GROUP,"");if(this._remoteGroupReadSequenceMap.has(c)){var l=this._remoteGroupReadSequenceMap.get(c),d=this._getConversationLastMessageSequence(i);this.updateIsReadAfterReadReport({conversationID:s,remoteReadSequence:l}),d>=l&&this._remoteGroupReadSequenceMap.delete(c)}}this.updateUnreadCount(s,!1)&&(o+=1)}}}catch(h){r.e(h)}finally{r.f()}return o}},{key:"isRemoteRead",value:function(e){var t=e.conversationID,n=e.sequence,o=t.replace(so.CONV_GROUP,""),r=!1;if(this._remoteGroupReadSequenceMap.has(o)){var a=this._remoteGroupReadSequenceMap.get(o);n<=a&&(r=!0,Bi.log("".concat(this._className,".isRemoteRead conversationID:").concat(t," messageSequence:").concat(n," remoteReadSequence:").concat(a))),n>=a+10&&this._remoteGroupReadSequenceMap.delete(o)}return r}},{key:"updateIsReadAfterReadReport",value:function(e){var t=e.conversationID,n=e.lastMessageSeq,o=e.lastMessageTime,r=this._messageListHandler.getLocalMessageList(t);if(0!==r.length)for(var a,s=r.length-1;s>=0;s--)if(a=r[s],!(o&&a.time>o||n&&a.sequence>n)){if("in"===a.flow&&a.isRead)break;a.setIsRead(!0)}}},{key:"updateUnreadCount",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=!1,o=this.getLocalConversation(e),r=this._messageListHandler.getLocalMessageList(e);if(o){var a=o.unreadCount,s=r.filter((function(e){return!e.isRead&&!e._onlineOnlyFlag&&!e.isDeleted})).length;return a!==s&&(o.unreadCount=s,n=!0,Bi.log("".concat(this._className,".updateUnreadCount from ").concat(a," to ").concat(s,", conversationID:").concat(e)),!0===t&&this.emitConversationUpdate(!0,!1)),n}}},{key:"recomputeGroupUnreadCount",value:function(e){var t=e.conversationID,n=e.count,o=this.getLocalConversation(t);if(o){var r=o.unreadCount,a=r-n;a<0&&(a=0),o.unreadCount=a,Bi.log("".concat(this._className,".recomputeGroupUnreadCount from ").concat(r," to ").concat(a,", conversationID:").concat(t))}}},{key:"updateIsRead",value:function(e){var t=this.getLocalConversation(e),n=this.getLocalMessageList(e);if(t&&0!==n.length&&!Mu(t.type)){for(var o=[],r=0,a=n.length;r<a;r++)"in"!==n[r].flow?"out"!==n[r].flow||n[r].isRead||n[r].setIsRead(!0):o.push(n[r]);var s=0;if(t.type===so.CONV_C2C){var i=o.slice(-t.unreadCount).filter((function(e){return e.isRevoked})).length;s=o.length-t.unreadCount-i}else s=o.length-t.unreadCount;for(var u=0;u<s&&!o[u].isRead;u++)o[u].setIsRead(!0)}}},{key:"deleteGroupAtTips",value:function(e){var t="".concat(this._className,".deleteGroupAtTips");Bi.log("".concat(t));var n=this._conversationMap.get(e);if(!n)return Promise.resolve();var o=n.groupAtInfoList;if(0===o.length)return Promise.resolve();var r=this.getMyUserID();return this.request({protocolName:wl,requestData:{messageListToDelete:o.map((function(e){return{from:e.from,to:r,messageSeq:e.__sequence,messageRandom:e.__random,groupID:e.groupID}}))}}).then((function(){return Bi.log("".concat(t," ok. count:").concat(o.length)),n.clearGroupAtInfoList(),Promise.resolve()})).catch((function(e){return Bi.error("".concat(t," failed. error:"),e),Mm(e)}))}},{key:"appendToMessageList",value:function(e){this._messageListHandler.pushIn(e)}},{key:"setMessageRandom",value:function(e){this.singlyLinkedList.set(e.random)}},{key:"deleteMessageRandom",value:function(e){this.singlyLinkedList.delete(e.random)}},{key:"pushIntoMessageList",value:function(e,t,n){return!(!this._messageListHandler.pushIn(t,n)||this._isMessageFromCurrentInstance(t)&&!n)&&(e.push(t),!0)}},{key:"_isMessageFromCurrentInstance",value:function(e){return this.singlyLinkedList.has(e.random)}},{key:"revoke",value:function(e,t,n){return this._messageListHandler.revoke(e,t,n)}},{key:"getPeerReadTime",value:function(e){return this._peerReadTimeMap.get(e)}},{key:"recordPeerReadTime",value:function(e,t){this._peerReadTimeMap.has(e)?this._peerReadTimeMap.get(e)<t&&this._peerReadTimeMap.set(e,t):this._peerReadTimeMap.set(e,t)}},{key:"updateMessageIsPeerReadProperty",value:function(e,t){if(e.startsWith(so.CONV_C2C)&&t>0){var n=this._messageListHandler.updateMessageIsPeerReadProperty(e,t);n.length>0&&this.emitOuterEvent(ao.MESSAGE_READ_BY_PEER,n)}}},{key:"updateMessageIsModifiedProperty",value:function(e){this._messageListHandler.updateMessageIsModifiedProperty(e)}},{key:"setCompleted",value:function(e){Bi.log("".concat(this._className,".setCompleted. conversationID:").concat(e)),this._completedMap.set(e,!0)}},{key:"updateRoamingMessageKeyAndTime",value:function(e,t,n){this._roamingMessageKeyAndTimeMap.set(e,{messageKey:t,lastMessageTime:n})}},{key:"getConversationList",value:function(e){var t=this,n="".concat(this._className,".getConversationList"),o="pagingStatus:".concat(this._pagingStatus,", local conversation count:").concat(this._conversationMap.size,", options:").concat(e);if(Bi.log("".concat(n,". ").concat(o)),this._pagingStatus===Sc.REJECTED){var r=new Zp(kg);return r.setMessage(o),this._syncConversationList().then((function(){r.setNetworkType(t.getNetworkType()).end();var n=t._getConversationList(e);return cm({conversationList:n})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],a=n[1];r.setError(e,o,a).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))}if(0===this._conversationMap.size){var a=new Zp(kg);return a.setMessage(o),this._syncConversationList().then((function(){a.setNetworkType(t.getNetworkType()).end();var n=t._getConversationList(e);return cm({conversationList:n})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))}var s=this._getConversationList(e);return Bi.log("".concat(n,". returned conversation count:").concat(s.length)),vm({conversationList:s})}},{key:"_getConversationList",value:function(e){var t=this;if(Ji(e))return this.getLocalConversationList();if(zi(e)){var n=[];return e.forEach((function(e){if(t._conversationMap.has(e)){var o=t.getLocalConversation(e);n.push(o)}})),n}}},{key:"_handleC2CPeerReadTime",value:function(){var e,t=ro(this._conversationMap);try{for(t.s();!(e=t.n()).done;){var n=Qn(e.value,2),o=n[0],r=n[1];r.type===so.CONV_C2C&&(Bi.debug("".concat(this._className,"._handleC2CPeerReadTime conversationID:").concat(o," peerReadTime:").concat(r.peerReadTime)),this.recordPeerReadTime(o,r.peerReadTime))}}catch(i){t.e(i)}finally{t.f()}}},{key:"getConversationProfile",value:function(e){var t,n=this;if((t=this._conversationMap.has(e)?this._conversationMap.get(e):new Ym({conversationID:e,type:e.slice(0,3)===so.CONV_C2C?so.CONV_C2C:so.CONV_GROUP}))._isInfoCompleted||t.type===so.CONV_SYSTEM)return vm({conversation:t});var o=new Zp(Dg),r="".concat(this._className,".getConversationProfile");return Bi.log("".concat(r,". conversationID:").concat(e," remark:").concat(t.remark," lastMessage:"),t.lastMessage),this._updateUserOrGroupProfileCompletely(t).then((function(a){o.setNetworkType(n.getNetworkType()).setMessage("conversationID:".concat(e," unreadCount:").concat(a.data.conversation.unreadCount)).end();var s=n.getModule(Hc);if(s&&t.type===so.CONV_C2C){var i=e.replace(so.CONV_C2C,"");if(s.isMyFriend(i)){var u=s.getFriendRemark(i);t.remark!==u&&(t.remark=u,Bi.log("".concat(r,". conversationID:").concat(e," patch remark:").concat(t.remark)))}}return Bi.log("".concat(r," ok. conversationID:").concat(e)),a})).catch((function(t){return n.probeNetwork().then((function(n){var r=Qn(n,2),a=r[0],s=r[1];o.setError(t,a,s).setMessage("conversationID:".concat(e)).end()})),Bi.error("".concat(r," failed. error:"),t),Mm(t)}))}},{key:"_updateUserOrGroupProfileCompletely",value:function(e){var t=this;return e.type===so.CONV_C2C?this.getModule(Vc).getUserProfile({userIDList:[e.toAccount]}).then((function(n){var o=n.data;return 0===o.length?Mm(new fm({code:kd.USER_OR_GROUP_NOT_FOUND,message:up})):(e.userProfile=o[0],e._isInfoCompleted=!0,t._unshiftConversation(e),vm({conversation:e}))})):this.getModule(Kc).getGroupProfile({groupID:e.toAccount}).then((function(n){return e.groupProfile=n.data.group,e._isInfoCompleted=!0,t._unshiftConversation(e),vm({conversation:e})}))}},{key:"_unshiftConversation",value:function(e){e instanceof Ym&&!this._conversationMap.has(e.conversationID)&&(this._conversationMap=new Map([[e.conversationID,e]].concat($n(this._conversationMap))),this._setStorageConversationList(),this.emitConversationUpdate(!0,!1))}},{key:"_onProfileUpdated",value:function(e){var t=this;e.data.forEach((function(e){var n=e.userID;if(n===t.getMyUserID())t._onMyProfileModified({latestNick:e.nick,latestAvatar:e.avatar});else{var o=t._conversationMap.get("".concat(so.CONV_C2C).concat(n));o&&(o.userProfile=e)}}))}},{key:"deleteConversation",value:function(e){var t=this,n={fromAccount:this.getMyUserID(),toAccount:void 0,type:void 0};if(!this._conversationMap.has(e)){var o=new fm({code:kd.CONVERSATION_NOT_FOUND,message:ip});return Mm(o)}switch(this._conversationMap.get(e).type){case so.CONV_C2C:n.type=1,n.toAccount=e.replace(so.CONV_C2C,"");break;case so.CONV_GROUP:n.type=2,n.toGroupID=e.replace(so.CONV_GROUP,"");break;case so.CONV_SYSTEM:return this.getModule(Kc).deleteGroupSystemNotice({messageList:this._messageListHandler.getLocalMessageList(e)}),this.deleteLocalConversation(e),vm({conversationID:e});default:var r=new fm({code:kd.CONVERSATION_UN_RECORDED_TYPE,message:cp});return Mm(r)}var a=new Zp(Ng);a.setMessage("conversationID:".concat(e));var s="".concat(this._className,".deleteConversation");return Bi.log("".concat(s,". conversationID:").concat(e)),this.setMessageRead({conversationID:e}).then((function(){return t.request({protocolName:Ll,requestData:n})})).then((function(){return a.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(s," ok")),t.deleteLocalConversation(e),vm({conversationID:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),Bi.error("".concat(s," failed. error:"),e),Mm(e)}))}},{key:"pinConversation",value:function(e){var t=this,n=e.conversationID,o=e.isPinned;if(!this._conversationMap.has(n))return Mm({code:kd.CONVERSATION_NOT_FOUND,message:ip});var r=this.getLocalConversation(n);if(r.isPinned===o)return vm({conversationID:n});var a=new Zp(Og);a.setMessage("conversationID:".concat(n," isPinned:").concat(o));var s="".concat(this._className,".pinConversation");Bi.log("".concat(s,". conversationID:").concat(n," isPinned:").concat(o));var i=null;return mu(n)?i={type:1,toAccount:n.replace(so.CONV_C2C,"")}:vu(n)&&(i={type:2,groupID:n.replace(so.CONV_GROUP,"")}),this.request({protocolName:bl,requestData:{fromAccount:this.getMyUserID(),operationType:!0===o?1:2,itemList:[i]}}).then((function(){return a.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(s," ok")),r.isPinned!==o&&(r.isPinned=o,t._sortConversationListAndEmitEvent()),cm({conversationID:n})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),Bi.error("".concat(s," failed. error:"),e),Mm(e)}))}},{key:"setMessageRemindType",value:function(e){return this._messageRemindHandler.set(e)}},{key:"patchMessageRemindType",value:function(e){var t=e.ID,n=e.isC2CConversation,o=e.messageRemindType,r=!1,a=this.getLocalConversation(n?"".concat(so.CONV_C2C).concat(t):"".concat(so.CONV_GROUP).concat(t));return a&&a.messageRemindType!==o&&(a.messageRemindType=o,r=!0),r}},{key:"onC2CMessageRemindTypeSynced",value:function(e){var t=this;Bi.debug("".concat(this._className,".onC2CMessageRemindTypeSynced options:"),e),e.dataList.forEach((function(e){if(!Nu(e.muteNotificationsSync)){var n,o=e.muteNotificationsSync,r=o.to,a=o.updateSequence,s=o.muteFlag;t._messageRemindHandler.setUpdateSequence(a),0===s?n=so.MSG_REMIND_ACPT_AND_NOTE:1===s?n=so.MSG_REMIND_DISCARD:2===s&&(n=so.MSG_REMIND_ACPT_NOT_NOTE);var i=0;t.patchMessageRemindType({ID:r,isC2CConversation:!0,messageRemindType:n})&&(i+=1),Bi.log("".concat(t._className,".onC2CMessageRemindTypeSynced updateCount:").concat(i)),i>=1&&t.emitConversationUpdate(!0,!1)}}))}},{key:"deleteLocalConversation",value:function(e){var t=this._conversationMap.has(e);Bi.log("".concat(this._className,".deleteLocalConversation conversationID:").concat(e," has:").concat(t)),t&&(this._conversationMap.delete(e),this._roamingMessageKeyAndTimeMap.delete(e),this._setStorageConversationList(),this._messageListHandler.removeByConversationID(e),this._completedMap.delete(e),this.emitConversationUpdate(!0,!1))}},{key:"isMessageSentByCurrentInstance",value:function(e){return!(!this._messageListHandler.hasLocalMessage(e.conversationID,e.ID)&&!this.singlyLinkedList.has(e.random))}},{key:"modifyMessageList",value:function(e){if(e.startsWith(so.CONV_C2C)&&this._conversationMap.has(e)){var t=this._conversationMap.get(e),n=Date.now();this._messageListHandler.modifyMessageSentByPeer({conversationID:e,latestNick:t.userProfile.nick,latestAvatar:t.userProfile.avatar});var o=this.getModule(Vc).getNickAndAvatarByUserID(this.getMyUserID());this._messageListHandler.modifyMessageSentByMe({conversationID:e,latestNick:o.nick,latestAvatar:o.avatar}),Bi.log("".concat(this._className,".modifyMessageList conversationID:").concat(e," cost ").concat(Date.now()-n," ms"))}}},{key:"updateUserProfileSpecifiedKey",value:function(e){Bi.log("".concat(this._className,".updateUserProfileSpecifiedKey options:"),e);var t=e.conversationID,n=e.nick,o=e.avatar;if(this._conversationMap.has(t)){var r=this._conversationMap.get(t).userProfile;ji(n)&&r.nick!==n&&(r.nick=n),ji(o)&&r.avatar!==o&&(r.avatar=o),this.emitConversationUpdate(!0,!1)}}},{key:"_onMyProfileModified",value:function(e){var t=this,n=this.getLocalConversationList(),o=Date.now();n.forEach((function(n){t.modifyMessageSentByMe(xn({conversationID:n.conversationID},e))})),Bi.log("".concat(this._className,"._onMyProfileModified. modify all messages sent by me, cost ").concat(Date.now()-o," ms"))}},{key:"modifyMessageSentByMe",value:function(e){this._messageListHandler.modifyMessageSentByMe(e)}},{key:"getLatestMessageSentByMe",value:function(e){return this._messageListHandler.getLatestMessageSentByMe(e)}},{key:"modifyMessageSentByPeer",value:function(e){this._messageListHandler.modifyMessageSentByPeer(e)}},{key:"getLatestMessageSentByPeer",value:function(e){return this._messageListHandler.getLatestMessageSentByPeer(e)}},{key:"pushIntoNoticeResult",value:function(e,t){return!(!this._messageListHandler.pushIn(t)||this.singlyLinkedList.has(t.random))&&(e.push(t),!0)}},{key:"getGroupLocalLastMessageSequence",value:function(e){return this._messageListHandler.getGroupLocalLastMessageSequence(e)}},{key:"checkAndPatchRemark",value:function(){var e=Promise.resolve();if(0===this._conversationMap.size)return e;var t=this.getModule(Hc);if(!t)return e;var n=$n(this._conversationMap.values()).filter((function(e){return e.type===so.CONV_C2C}));if(0===n.length)return e;var o=0;return n.forEach((function(e){var n=e.conversationID.replace(so.CONV_C2C,"");if(t.isMyFriend(n)){var r=t.getFriendRemark(n);e.remark!==r&&(e.remark=r,o+=1)}})),Bi.log("".concat(this._className,".checkAndPatchRemark. c2c conversation count:").concat(n.length,", patched count:").concat(o)),e}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._pagingStatus=Sc.NOT_START,this._messageListHandler.reset(),this._messageRemindHandler.reset(),this._roamingMessageKeyAndTimeMap.clear(),this.singlyLinkedList.reset(),this._peerReadTimeMap.clear(),this._completedMap.clear(),this._conversationMap.clear(),this._pagingTimeStamp=0,this._pagingStartIndex=0,this._pagingPinnedTimeStamp=0,this._pagingPinnedStartIndex=0,this._remoteGroupReadSequenceMap.clear(),this.resetReady()}}]),n}(cl),Xm=function(){function e(t){Gn(this,e),this._groupModule=t,this._className="GroupTipsHandler",this._cachedGroupTipsMap=new Map,this._checkCountMap=new Map,this.MAX_CHECK_COUNT=4}return Un(e,[{key:"onCheckTimer",value:function(e){e%1==0&&this._cachedGroupTipsMap.size>0&&this._checkCachedGroupTips()}},{key:"_checkCachedGroupTips",value:function(){var e=this;this._cachedGroupTipsMap.forEach((function(t,n){var o=e._checkCountMap.get(n),r=e._groupModule.hasLocalGroup(n);Bi.log("".concat(e._className,"._checkCachedGroupTips groupID:").concat(n," hasLocalGroup:").concat(r," checkCount:").concat(o)),r?(e._notifyCachedGroupTips(n),e._checkCountMap.delete(n),e._groupModule.deleteUnjoinedAVChatRoom(n)):o>=e.MAX_CHECK_COUNT?(e._deleteCachedGroupTips(n),e._checkCountMap.delete(n)):(o++,e._checkCountMap.set(n,o))}))}},{key:"onNewGroupTips",value:function(e){Bi.debug("".concat(this._className,".onReceiveGroupTips count:").concat(e.dataList.length));var t=this.newGroupTipsStoredAndSummary(e),n=t.eventDataList,o=t.result,r=t.AVChatRoomMessageList;r.length>0&&this._groupModule.onAVChatRoomMessage(r),n.length>0&&(this._groupModule.getModule(Wc).onNewMessage({conversationOptionsList:n,isInstantMessage:!0}),this._groupModule.updateNextMessageSeq(n)),o.length>0&&(this._groupModule.emitOuterEvent(ao.MESSAGE_RECEIVED,o),this.handleMessageList(o))}},{key:"newGroupTipsStoredAndSummary",value:function(e){for(var t=e.event,n=e.dataList,o=null,r=[],a=[],s={},i=[],u=0,c=n.length;u<c;u++){var l=n[u],d=l.groupProfile.groupID,p=this._groupModule.hasLocalGroup(d);if(p||!this._groupModule.isUnjoinedAVChatRoom(d))if(p)if(this._groupModule.isMessageFromAVChatroom(d)){var g=gu(l);g.event=t,i.push(g)}else{l.currentUser=this._groupModule.getMyUserID(),l.conversationType=so.CONV_GROUP,(o=new um(l)).setElement({type:so.MSG_GRP_TIP,content:xn(xn({},l.elements),{},{groupProfile:l.groupProfile})}),o.isSystemMessage=!1;var h=this._groupModule.getModule(Wc),f=o,_=f.conversationID,m=f.sequence;if(6===t)o._onlineOnlyFlag=!0,a.push(o);else if(!h.pushIntoNoticeResult(a,o))continue;if(6!==t||!h.getLocalConversation(_)){6!==t&&this._groupModule.getModule(il).addMessageSequence({key:Wp,message:o});var v=h.isRemoteRead({conversationID:_,sequence:m});if(Ji(s[_]))s[_]=r.push({conversationID:_,unreadCount:"in"!==o.flow||o._onlineOnlyFlag||v?0:1,type:o.conversationType,subType:o.conversationSubType,lastMessage:o})-1;else{var M=s[_];r[M].type=o.conversationType,r[M].subType=o.conversationSubType,r[M].lastMessage=o,"in"!==o.flow||o._onlineOnlyFlag||v||r[M].unreadCount++}}}else this._cacheGroupTipsAndProbe({groupID:d,event:t,item:l})}return{eventDataList:r,result:a,AVChatRoomMessageList:i}}},{key:"handleMessageList",value:function(e){var t=this;e.forEach((function(e){switch(e.payload.operationType){case 1:t._onNewMemberComeIn(e);break;case 2:t._onMemberQuit(e);break;case 3:t._onMemberKickedOut(e);break;case 4:t._onMemberSetAdmin(e);break;case 5:t._onMemberCancelledAdmin(e);break;case 6:t._onGroupProfileModified(e);break;case 7:t._onMemberInfoModified(e);break;default:Bi.warn("".concat(t._className,".handleMessageList unknown operationType:").concat(e.payload.operationType))}}))}},{key:"_onNewMemberComeIn",value:function(e){var t=e.payload,n=t.memberNum,o=t.groupProfile.groupID,r=this._groupModule.getLocalGroupProfile(o);r&&Hi(n)&&(r.memberNum=n)}},{key:"_onMemberQuit",value:function(e){var t=e.payload,n=t.memberNum,o=t.groupProfile.groupID,r=this._groupModule.getLocalGroupProfile(o);r&&Hi(n)&&(r.memberNum=n),this._groupModule.deleteLocalGroupMembers(o,e.payload.userIDList)}},{key:"_onMemberKickedOut",value:function(e){var t=e.payload,n=t.memberNum,o=t.groupProfile.groupID,r=this._groupModule.getLocalGroupProfile(o);r&&Hi(n)&&(r.memberNum=n),this._groupModule.deleteLocalGroupMembers(o,e.payload.userIDList)}},{key:"_onMemberSetAdmin",value:function(e){var t=e.payload.groupProfile.groupID,n=e.payload.userIDList,o=this._groupModule.getModule(jc);n.forEach((function(e){var n=o.getLocalGroupMemberInfo(t,e);n&&n.updateRole(so.GRP_MBR_ROLE_ADMIN)}))}},{key:"_onMemberCancelledAdmin",value:function(e){var t=e.payload.groupProfile.groupID,n=e.payload.userIDList,o=this._groupModule.getModule(jc);n.forEach((function(e){var n=o.getLocalGroupMemberInfo(t,e);n&&n.updateRole(so.GRP_MBR_ROLE_MEMBER)}))}},{key:"_onGroupProfileModified",value:function(e){var t=this,n=e.payload,o=n.newGroupProfile,r=n.groupProfile.groupID,a=this._groupModule.getLocalGroupProfile(r);Object.keys(o).forEach((function(e){switch(e){case"ownerID":t._ownerChanged(a,o);break;default:a[e]=o[e]}})),this._groupModule.emitGroupListUpdate(!0,!0)}},{key:"_ownerChanged",value:function(e,t){var n=e.groupID,o=this._groupModule.getLocalGroupProfile(n),r=this._groupModule.getMyUserID();if(r===t.ownerID){o.updateGroup({selfInfo:{role:so.GRP_MBR_ROLE_OWNER}});var a=this._groupModule.getModule(jc),s=a.getLocalGroupMemberInfo(n,r),i=this._groupModule.getLocalGroupProfile(n).ownerID,u=a.getLocalGroupMemberInfo(n,i);s&&s.updateRole(so.GRP_MBR_ROLE_OWNER),u&&u.updateRole(so.GRP_MBR_ROLE_MEMBER)}}},{key:"_onMemberInfoModified",value:function(e){var t=e.payload.groupProfile.groupID,n=this._groupModule.getModule(jc);e.payload.memberList.forEach((function(e){var o=n.getLocalGroupMemberInfo(t,e.userID);o&&e.muteTime&&o.updateMuteUntil(e.muteTime)}))}},{key:"_cacheGroupTips",value:function(e,t){this._cachedGroupTipsMap.has(e)||this._cachedGroupTipsMap.set(e,[]),this._cachedGroupTipsMap.get(e).push(t)}},{key:"_deleteCachedGroupTips",value:function(e){this._cachedGroupTipsMap.has(e)&&this._cachedGroupTipsMap.delete(e)}},{key:"_notifyCachedGroupTips",value:function(e){var t=this,n=this._cachedGroupTipsMap.get(e)||[];n.forEach((function(e){t.onNewGroupTips(e)})),this._deleteCachedGroupTips(e),Bi.log("".concat(this._className,"._notifyCachedGroupTips groupID:").concat(e," count:").concat(n.length))}},{key:"_cacheGroupTipsAndProbe",value:function(e){var t=this,n=e.groupID,o=e.event,r=e.item;this._cacheGroupTips(n,{event:o,dataList:[r]}),this._groupModule.getGroupSimplifiedInfo(n).then((function(e){e.type===so.GRP_AVCHATROOM?t._groupModule.hasLocalGroup(n)?t._notifyCachedGroupTips(n):t._groupModule.setUnjoinedAVChatRoom(n):(t._groupModule.updateGroupMap([e]),t._notifyCachedGroupTips(n))})),this._checkCountMap.has(n)||this._checkCountMap.set(n,0),Bi.log("".concat(this._className,"._cacheGroupTipsAndProbe groupID:").concat(n))}},{key:"reset",value:function(){this._cachedGroupTipsMap.clear(),this._checkCountMap.clear()}}]),e}(),Qm=[].push,$m=Math.min,Zm=!a((function(){return!RegExp(4294967295,"y")}));ys("split",2,(function(e,t,n){var o;return o="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(e,n){var o=String(_(this)),r=void 0===n?4294967295:n>>>0;if(0===r)return[];if(void 0===e)return[o];if(!ls(e))return t.call(o,e,r);for(var a,s,i,u=[],c=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),l=0,d=new RegExp(e.source,c+"g");(a=os.call(d,o))&&!((s=d.lastIndex)>l&&(u.push(o.slice(l,a.index)),a.length>1&&a.index<o.length&&Qm.apply(u,a.slice(1)),i=a[0].length,l=s,u.length>=r));)d.lastIndex===a.index&&d.lastIndex++;return l===o.length?!i&&d.test("")||u.push(""):u.push(o.slice(l)),u.length>r?u.slice(0,r):u}:"0".split(void 0,0).length?function(e,n){return void 0===e&&0===n?[]:t.call(this,e,n)}:t,[function(t,n){var r=_(this),a=null==t?void 0:t[e];return void 0!==a?a.call(t,r,n):o.call(String(r),t,n)},function(e,r){var a=n(o,e,this,r,o!==t);if(a.done)return a.value;var s=D(e),i=String(this),u=wr(s,RegExp),c=s.unicode,l=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.unicode?"u":"")+(Zm?"y":"g"),d=new u(Zm?s:"^(?:"+s.source+")",l),p=void 0===r?4294967295:r>>>0;if(0===p)return[];if(0===i.length)return null===Cs(d,i)?[i]:[];for(var g=0,h=0,f=[];h<i.length;){d.lastIndex=Zm?h:0;var _,m=Cs(d,Zm?i:i.slice(h));if(null===m||(_=$m(de(d.lastIndex+(Zm?0:h)),i.length))===g)h=Ts(i,h,c);else{if(f.push(i.slice(g,h)),f.length===p)return f;for(var v=1;v<=m.length-1;v++)if(f.push(m[v]),f.length===p)return f;h=g=_}}return f.push(i.slice(g)),f}]}),!Zm);var ev=function(){function e(t){Gn(this,e),this._groupModule=t,this._className="CommonGroupHandler",this.tempConversationList=null,this._cachedGroupMessageMap=new Map,this._checkCountMap=new Map,this.MAX_CHECK_COUNT=4,t.getInnerEmitterInstance().once(Om,this._initGroupList,this)}return Un(e,[{key:"onCheckTimer",value:function(e){e%1==0&&this._cachedGroupMessageMap.size>0&&this._checkCachedGroupMessage()}},{key:"_checkCachedGroupMessage",value:function(){var e=this;this._cachedGroupMessageMap.forEach((function(t,n){var o=e._checkCountMap.get(n),r=e._groupModule.hasLocalGroup(n);Bi.log("".concat(e._className,"._checkCachedGroupMessage groupID:").concat(n," hasLocalGroup:").concat(r," checkCount:").concat(o)),r?(e._notifyCachedGroupMessage(n),e._checkCountMap.delete(n),e._groupModule.deleteUnjoinedAVChatRoom(n)):o>=e.MAX_CHECK_COUNT?(e._deleteCachedGroupMessage(n),e._checkCountMap.delete(n)):(o++,e._checkCountMap.set(n,o))}))}},{key:"_initGroupList",value:function(){var e=this;Bi.log("".concat(this._className,"._initGroupList"));var t=new Zp(Wg),n=this._groupModule.getStorageGroupList();if(zi(n)&&n.length>0){n.forEach((function(t){e._groupModule.initGroupMap(t)})),this._groupModule.emitGroupListUpdate(!0,!1);var o=this._groupModule.getLocalGroupList().length;t.setNetworkType(this._groupModule.getNetworkType()).setMessage("group count:".concat(o)).end()}else t.setNetworkType(this._groupModule.getNetworkType()).setMessage("group count:0").end();Bi.log("".concat(this._className,"._initGroupList ok"))}},{key:"handleUpdateGroupLastMessage",value:function(e){var t="".concat(this._className,".handleUpdateGroupLastMessage");if(Bi.debug("".concat(t," conversation count:").concat(e.length,", local group count:").concat(this._groupModule.getLocalGroupList().length)),0!==this._groupModule.getGroupMap().size){for(var n,o,r,a=!1,s=0,i=e.length;s<i;s++)(n=e[s]).type===so.CONV_GROUP&&(o=n.conversationID.split(/^GROUP/)[1],(r=this._groupModule.getLocalGroupProfile(o))&&(r.lastMessage=n.lastMessage,a=!0));a&&(this._groupModule.sortLocalGroupList(),this._groupModule.emitGroupListUpdate(!0,!1))}else this.tempConversationList=e}},{key:"onNewGroupMessage",value:function(e){Bi.debug("".concat(this._className,".onNewGroupMessage count:").concat(e.dataList.length));var t=this._newGroupMessageStoredAndSummary(e),n=t.conversationOptionsList,o=t.messageList,r=t.AVChatRoomMessageList;r.length>0&&this._groupModule.onAVChatRoomMessage(r),this._groupModule.filterModifiedMessage(o),n.length>0&&(this._groupModule.getModule(Wc).onNewMessage({conversationOptionsList:n,isInstantMessage:!0}),this._groupModule.updateNextMessageSeq(n));var a=this._groupModule.filterUnmodifiedMessage(o);a.length>0&&this._groupModule.emitOuterEvent(ao.MESSAGE_RECEIVED,a),o.length=0}},{key:"_newGroupMessageStoredAndSummary",value:function(e){var t=e.dataList,n=e.event,o=e.isInstantMessage,r=null,a=[],s=[],i=[],u={},c=so.CONV_GROUP,l=this._groupModule.getModule(Qc),d=t.length;d>1&&t.sort((function(e,t){return e.sequence-t.sequence}));for(var p=0;p<d;p++){var g=t[p],h=g.groupProfile.groupID,f=this._groupModule.hasLocalGroup(h);if(f||!this._groupModule.isUnjoinedAVChatRoom(h))if(f)if(this._groupModule.isMessageFromAVChatroom(h)){var _=gu(g);_.event=n,i.push(_)}else{g.currentUser=this._groupModule.getMyUserID(),g.conversationType=c,g.isSystemMessage=!!g.isSystemMessage,r=new um(g),g.elements=l.parseElements(g.elements,g.from),r.setElement(g.elements);var m=1===t[p].isModified,v=this._groupModule.getModule(Wc);v.isMessageSentByCurrentInstance(r)?r.isModified=m:m=!1;var M=this._groupModule.getModule(il);if(o&&M.addMessageDelay({currentTime:Date.now(),time:r.time}),1===g.onlineOnlyFlag)r._onlineOnlyFlag=!0,s.push(r);else{if(!v.pushIntoMessageList(s,r,m))continue;M.addMessageSequence({key:Wp,message:r});var y=r,I=y.conversationID,T=y.sequence,C=v.isRemoteRead({conversationID:I,sequence:T});if(Ji(u[I])){var S=0;"in"===r.flow&&(r._isExcludedFromUnreadCount||C||(S=1)),u[I]=a.push({conversationID:I,unreadCount:S,type:r.conversationType,subType:r.conversationSubType,lastMessage:r._isExcludedFromLastMessage?"":r})-1}else{var A=u[I];a[A].type=r.conversationType,a[A].subType=r.conversationSubType,a[A].lastMessage=r._isExcludedFromLastMessage?"":r,"in"===r.flow&&(r._isExcludedFromUnreadCount||C||a[A].unreadCount++)}}}else this._cacheGroupMessageAndProbe({groupID:h,event:n,item:g})}return{conversationOptionsList:a,messageList:s,AVChatRoomMessageList:i}}},{key:"onGroupMessageRevoked",value:function(e){Bi.debug("".concat(this._className,".onGroupMessageRevoked nums:").concat(e.dataList.length));var t=this._groupModule.getModule(Wc),n=[],o=null;e.dataList.forEach((function(e){var r=e.elements.revokedInfos;Ji(r)||r.forEach((function(e){(o=t.revoke("GROUP".concat(e.groupID),e.sequence,e.random))&&n.push(o)}))})),0!==n.length&&(t.onMessageRevoked(n),this._groupModule.emitOuterEvent(ao.MESSAGE_REVOKED,n))}},{key:"_groupListTreeShaking",value:function(e){for(var t=new Map($n(this._groupModule.getGroupMap())),n=0,o=e.length;n<o;n++)t.delete(e[n].groupID);this._groupModule.hasJoinedAVChatRoom()&&this._groupModule.getJoinedAVChatRoom().forEach((function(e){t.delete(e)}));for(var r=$n(t.keys()),a=0,s=r.length;a<s;a++)this._groupModule.deleteGroup(r[a])}},{key:"getGroupList",value:function(e){var t=this,n="".concat(this._className,".getGroupList"),o=new Zp(Hg);Bi.log("".concat(n));var r={introduction:"Introduction",notification:"Notification",createTime:"CreateTime",ownerID:"Owner_Account",lastInfoTime:"LastInfoTime",memberNum:"MemberNum",maxMemberNum:"MaxMemberNum",joinOption:"ApplyJoinOption",muteAllMembers:"ShutUpAllMember"},a=["Type","Name","FaceUrl","NextMsgSeq","LastMsgTime"],s=[];return e&&e.groupProfileFilter&&e.groupProfileFilter.forEach((function(e){r[e]&&a.push(r[e])})),this._pagingGetGroupList({limit:50,offset:0,groupBaseInfoFilter:a,groupList:s}).then((function(){Bi.log("".concat(n," ok. count:").concat(s.length)),t._groupListTreeShaking(s),t._groupModule.updateGroupMap(s);var e=t._groupModule.getLocalGroupList().length;return o.setNetworkType(t._groupModule.getNetworkType()).setMessage("remote count:".concat(s.length,", after tree shaking, local count:").concat(e)).end(),t.tempConversationList&&(Bi.log("".concat(n," update last message with tempConversationList, count:").concat(t.tempConversationList.length)),t.handleUpdateGroupLastMessage({data:t.tempConversationList}),t.tempConversationList=null),t._groupModule.emitGroupListUpdate(),t._groupModule.patchGroupMessageRemindType(),t._groupModule.recomputeUnreadCount(),cm({groupList:t._groupModule.getLocalGroupList()})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"_pagingGetGroupList",value:function(e){var t=this,n="".concat(this._className,"._pagingGetGroupList"),o=e.limit,r=e.offset,a=e.groupBaseInfoFilter,s=e.groupList,i=new Zp(Jg);return this._groupModule.request({protocolName:Gl,requestData:{memberAccount:this._groupModule.getMyUserID(),limit:o,offset:r,responseFilter:{groupBaseInfoFilter:a,selfInfoFilter:["Role","JoinTime","MsgFlag","MsgSeq"]}}}).then((function(e){var u=e.data,c=u.groups,l=u.totalCount;s.push.apply(s,$n(c));var d=r+o,p=!(l>d);return i.setNetworkType(t._groupModule.getNetworkType()).setMessage("offset:".concat(r," totalCount:").concat(l," isCompleted:").concat(p," currentCount:").concat(s.length)).end(),p?(Bi.log("".concat(n," ok. totalCount:").concat(l)),cm({groupList:s})):(r=d,t._pagingGetGroupList({limit:o,offset:r,groupBaseInfoFilter:a,groupList:s}))})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];i.setError(e,o,r).end()})),Mm(e)}))}},{key:"_cacheGroupMessage",value:function(e,t){this._cachedGroupMessageMap.has(e)||this._cachedGroupMessageMap.set(e,[]),this._cachedGroupMessageMap.get(e).push(t)}},{key:"_deleteCachedGroupMessage",value:function(e){this._cachedGroupMessageMap.has(e)&&this._cachedGroupMessageMap.delete(e)}},{key:"_notifyCachedGroupMessage",value:function(e){var t=this,n=this._cachedGroupMessageMap.get(e)||[];n.forEach((function(e){t.onNewGroupMessage(e)})),this._deleteCachedGroupMessage(e),Bi.log("".concat(this._className,"._notifyCachedGroupMessage groupID:").concat(e," count:").concat(n.length))}},{key:"_cacheGroupMessageAndProbe",value:function(e){var t=this,n=e.groupID,o=e.event,r=e.item;this._cacheGroupMessage(n,{event:o,dataList:[r]}),this._groupModule.getGroupSimplifiedInfo(n).then((function(e){e.type===so.GRP_AVCHATROOM?t._groupModule.hasLocalGroup(n)?t._notifyCachedGroupMessage(n):t._groupModule.setUnjoinedAVChatRoom(n):(t._groupModule.updateGroupMap([e]),t._notifyCachedGroupMessage(n))})),this._checkCountMap.has(n)||this._checkCountMap.set(n,0),Bi.log("".concat(this._className,"._cacheGroupMessageAndProbe groupID:").concat(n))}},{key:"reset",value:function(){this._cachedGroupMessageMap.clear(),this._checkCountMap.clear(),this._groupModule.getInnerEmitterInstance().once(Om,this._initGroupList,this)}}]),e}(),tv={1:"init",2:"modify",3:"clear",4:"delete"},nv=function(){function e(t){Gn(this,e),this._groupModule=t,this._className="GroupAttributesHandler",this._groupAttributesMap=new Map,this.CACHE_EXPIRE_TIME=3e4,this._groupModule.getInnerEmitterInstance().on(Rm,this._onCloudConfigUpdated,this)}return Un(e,[{key:"_onCloudConfigUpdated",value:function(){var e=this._groupModule.getCloudConfig("grp_attr_cache_time");Ji(e)||(this.CACHE_EXPIRE_TIME=Number(e))}},{key:"updateLocalMainSequenceOnReconnected",value:function(){this._groupAttributesMap.forEach((function(e){e.localMainSequence=0}))}},{key:"onGroupAttributesUpdated",value:function(e){var t=this,n=e.groupID,o=e.groupAttributeOption,r=o.mainSequence,a=o.hasChangedAttributeInfo,s=o.groupAttributeList,i=void 0===s?[]:s,u=o.operationType;if(Bi.log("".concat(this._className,".onGroupAttributesUpdated. hasChangedAttributeInfo:").concat(a," operationType:").concat(u)),!Ji(u)){if(1===a){if(4===u){var c=[];i.forEach((function(e){c.push(e.key)})),i=$n(c),c=null}return this._refreshCachedGroupAttributes({groupID:n,remoteMainSequence:r,groupAttributeList:i,operationType:tv[u]}),void this._emitGroupAttributesUpdated(n)}if(this._groupAttributesMap.has(n)){var l=this._groupAttributesMap.get(n).avChatRoomKey;this._getGroupAttributes({groupID:n,avChatRoomKey:l}).then((function(){t._emitGroupAttributesUpdated(n)}))}}}},{key:"initGroupAttributesCache",value:function(e){var t=e.groupID,n=e.avChatRoomKey;this._groupAttributesMap.set(t,{lastUpdateTime:0,localMainSequence:0,remoteMainSequence:0,attributes:new Map,avChatRoomKey:n}),Bi.log("".concat(this._className,".initGroupAttributesCache groupID:").concat(t," avChatRoomKey:").concat(n))}},{key:"initGroupAttributes",value:function(e){var t=this,n=e.groupID,o=e.groupAttributes,r=this._checkCachedGroupAttributes({groupID:n,funcName:"initGroupAttributes"});if(!0!==r)return Mm(r);var a=this._groupAttributesMap.get(n),s=a.remoteMainSequence,i=a.avChatRoomKey,u=new Zp($g);return u.setMessage("groupID:".concat(n," mainSequence:").concat(s," groupAttributes:").concat(JSON.stringify(o))),this._groupModule.request({protocolName:od,requestData:{groupID:n,avChatRoomKey:i,mainSequence:s,groupAttributeList:this._transformGroupAttributes(o)}}).then((function(e){var r=e.data,a=r.mainSequence,s=$n(r.groupAttributeList);return s.forEach((function(e){e.value=o[e.key]})),t._refreshCachedGroupAttributes({groupID:n,remoteMainSequence:a,groupAttributeList:s,operationType:"init"}),u.setNetworkType(t._groupModule.getNetworkType()).end(),Bi.log("".concat(t._className,".initGroupAttributes ok. groupID:").concat(n)),cm({groupAttributes:o})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];u.setError(e,o,r).end()})),Mm(e)}))}},{key:"setGroupAttributes",value:function(e){var t=this,n=e.groupID,o=e.groupAttributes,r=this._checkCachedGroupAttributes({groupID:n,funcName:"setGroupAttributes"});if(!0!==r)return Mm(r);var a=this._groupAttributesMap.get(n),s=a.remoteMainSequence,i=a.avChatRoomKey,u=a.attributes,c=this._transformGroupAttributes(o);c.forEach((function(e){var t=e.key;e.sequence=0,u.has(t)&&(e.sequence=u.get(t).sequence)}));var l=new Zp(Zg);return l.setMessage("groupID:".concat(n," mainSequence:").concat(s," groupAttributes:").concat(JSON.stringify(o))),this._groupModule.request({protocolName:rd,requestData:{groupID:n,avChatRoomKey:i,mainSequence:s,groupAttributeList:c}}).then((function(e){var r=e.data,a=r.mainSequence,s=$n(r.groupAttributeList);return s.forEach((function(e){e.value=o[e.key]})),t._refreshCachedGroupAttributes({groupID:n,remoteMainSequence:a,groupAttributeList:s,operationType:"modify"}),l.setNetworkType(t._groupModule.getNetworkType()).end(),Bi.log("".concat(t._className,".setGroupAttributes ok. groupID:").concat(n)),cm({groupAttributes:o})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];l.setError(e,o,r).end()})),Mm(e)}))}},{key:"deleteGroupAttributes",value:function(e){var t=this,n=e.groupID,o=e.keyList,r=void 0===o?[]:o,a=this._checkCachedGroupAttributes({groupID:n,funcName:"deleteGroupAttributes"});if(!0!==a)return Mm(a);var s=this._groupAttributesMap.get(n),i=s.remoteMainSequence,u=s.avChatRoomKey,c=s.attributes,l=$n(c.keys()),d=sd,p="clear",g={groupID:n,avChatRoomKey:u,mainSequence:i};if(r.length>0){var h=[];l=[],d=ad,p="delete",r.forEach((function(e){var t=0;c.has(e)&&(t=c.get(e).sequence,l.push(e)),h.push({key:e,sequence:t})})),g.groupAttributeList=h}var f=new Zp(eh);return f.setMessage("groupID:".concat(n," mainSequence:").concat(i," keyList:").concat(r," protocolName:").concat(d)),this._groupModule.request({protocolName:d,requestData:g}).then((function(e){var o=e.data.mainSequence;return t._refreshCachedGroupAttributes({groupID:n,remoteMainSequence:o,groupAttributeList:r,operationType:p}),f.setNetworkType(t._groupModule.getNetworkType()).end(),Bi.log("".concat(t._className,".deleteGroupAttributes ok. groupID:").concat(n)),cm({keyList:l})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];f.setError(e,o,r).end()})),Mm(e)}))}},{key:"getGroupAttributes",value:function(e){var t=this,n=e.groupID,o=this._checkCachedGroupAttributes({groupID:n,funcName:"getGroupAttributes"});if(!0!==o)return Mm(o);var r=this._groupAttributesMap.get(n),a=r.avChatRoomKey,s=r.lastUpdateTime,i=r.localMainSequence,u=r.remoteMainSequence,c=new Zp(th);if(c.setMessage("groupID:".concat(n," localMainSequence:").concat(i," remoteMainSequence:").concat(u," keyList:").concat(e.keyList)),Date.now()-s>=this.CACHE_EXPIRE_TIME||i<u)return this._getGroupAttributes({groupID:n,avChatRoomKey:a}).then((function(o){c.setMoreMessage("get attributes from remote. count:".concat(o.length)).setNetworkType(t._groupModule.getNetworkType()).end(),Bi.log("".concat(t._className,".getGroupAttributes from remote. groupID:").concat(n));var r=t._getLocalGroupAttributes(e);return cm({groupAttributes:r})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];c.setError(e,o,r).end()})),Mm(e)}));c.setMoreMessage("get attributes from cache").setNetworkType(this._groupModule.getNetworkType()).end(),Bi.log("".concat(this._className,".getGroupAttributes from cache. groupID:").concat(n));var l=this._getLocalGroupAttributes(e);return vm({groupAttributes:l})}},{key:"_getGroupAttributes",value:function(e){var t=this;return this._groupModule.request({protocolName:id,requestData:xn({},e)}).then((function(n){var o=n.data,r=o.mainSequence,a=o.groupAttributeList,s=$n(a);return Ji(r)||t._refreshCachedGroupAttributes({groupID:e.groupID,remoteMainSequence:r,groupAttributeList:s,operationType:"get"}),Bi.log("".concat(t._className,"._getGroupAttributes ok. groupID:").concat(e.groupID)),a})).catch((function(e){return Mm(e)}))}},{key:"_getLocalGroupAttributes",value:function(e){var t=e.groupID,n=e.keyList,o=void 0===n?[]:n,r={};if(!this._groupAttributesMap.has(t))return r;var a=this._groupAttributesMap.get(t).attributes;if(o.length>0)o.forEach((function(e){a.has(e)&&(r[e]=a.get(e).value)}));else{var s,i=ro(a.keys());try{for(i.s();!(s=i.n()).done;){var u=s.value;r[u]=a.get(u).value}}catch(d){i.e(d)}finally{i.f()}}return r}},{key:"_refreshCachedGroupAttributes",value:function(e){var t=e.groupID,n=e.remoteMainSequence,o=e.groupAttributeList,r=e.operationType;if(this._groupAttributesMap.has(t)){var a=this._groupAttributesMap.get(t),s=a.localMainSequence;if("get"===r||n-s==1)a.remoteMainSequence=n,a.localMainSequence=n,a.lastUpdateTime=Date.now(),this._updateCachedAttributes({groupAttributes:a,groupAttributeList:o,operationType:r});else{if(s===n)return;a.remoteMainSequence=n}this._groupAttributesMap.set(t,a);var i="operationType:".concat(r," localMainSequence:").concat(s," remoteMainSequence:").concat(n);Bi.log("".concat(this._className,"._refreshCachedGroupAttributes. ").concat(i))}}},{key:"_updateCachedAttributes",value:function(e){var t=e.groupAttributes,n=e.groupAttributeList,o=e.operationType;"clear"!==o?"delete"!==o?("init"===o&&t.attributes.clear(),n.forEach((function(e){var n=e.key,o=e.value,r=e.sequence;t.attributes.set(n,{value:o,sequence:r})}))):n.forEach((function(e){t.attributes.delete(e)})):t.attributes.clear()}},{key:"_checkCachedGroupAttributes",value:function(e){var t=e.groupID,n=e.funcName;if(this._groupModule.hasLocalGroup(t)&&this._groupModule.getLocalGroupProfile(t).type!==so.GRP_AVCHATROOM)return Bi.warn("".concat(this._className,"._checkCachedGroupAttributes. ").concat("非直播群不能使用群属性 API")),new fm({code:kd.CANNOT_USE_GRP_ATTR_NOT_AVCHATROOM,message:"非直播群不能使用群属性 API"});var o=this._groupAttributesMap.get(t);if(Ji(o)){var r="如果 groupID:".concat(t," 是直播群，使用 ").concat(n," 前先使用 joinGroup 接口申请加入群组，详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#joinGroup");return Bi.warn("".concat(this._className,"._checkCachedGroupAttributes. ").concat(r)),new fm({code:kd.CANNOT_USE_GRP_ATTR_AVCHATROOM_UNJOIN,message:r})}return!0}},{key:"_transformGroupAttributes",value:function(e){var t=[];return Object.keys(e).forEach((function(n){t.push({key:n,value:e[n]})})),t}},{key:"_emitGroupAttributesUpdated",value:function(e){var t=this._getLocalGroupAttributes({groupID:e});this._groupModule.emitOuterEvent(ao.GROUP_ATTRIBUTES_UPDATED,{groupID:e,groupAttributes:t})}},{key:"reset",value:function(){this._groupAttributesMap.clear(),this.CACHE_EXPIRE_TIME=3e4}}]),e}(),ov=function(){function e(t){Gn(this,e);var n=t.manager,o=t.groupID,r=t.onInit,a=t.onSuccess,s=t.onFail;this._className="Polling",this._manager=n,this._groupModule=n._groupModule,this._onInit=r,this._onSuccess=a,this._onFail=s,this._groupID=o,this._timeoutID=-1,this._isRunning=!1,this._protocolName=Zl}return Un(e,[{key:"start",value:function(){var e=this._groupModule.isLoggedIn();e||(this._protocolName=ed),Bi.log("".concat(this._className,".start pollingInterval:").concat(this._manager.getPollingInterval()," isLoggedIn:").concat(e)),this._isRunning=!0,this._request()}},{key:"isRunning",value:function(){return this._isRunning}},{key:"_request",value:function(){var e=this,t=this._onInit(this._groupID);this._groupModule.request({protocolName:this._protocolName,requestData:t}).then((function(t){e._onSuccess(e._groupID,t),e.isRunning()&&(clearTimeout(e._timeoutID),e._timeoutID=setTimeout(e._request.bind(e),e._manager.getPollingInterval()))})).catch((function(t){e._onFail(e._groupID,t),e.isRunning()&&(clearTimeout(e._timeoutID),e._timeoutID=setTimeout(e._request.bind(e),e._manager.MAX_POLLING_INTERVAL))}))}},{key:"stop",value:function(){Bi.log("".concat(this._className,".stop")),this._timeoutID>0&&(clearTimeout(this._timeoutID),this._timeoutID=-1),this._isRunning=!1}}]),e}(),rv={3:!0,4:!0,5:!0,6:!0},av=function(){function e(t){Gn(this,e),this._groupModule=t,this._className="AVChatRoomHandler",this._joinedGroupMap=new Map,this._pollingRequestInfoMap=new Map,this._pollingInstanceMap=new Map,this.sequencesLinkedList=new Bm(100),this.messageIDLinkedList=new Bm(100),this.receivedMessageCount=0,this._reportMessageStackedCount=0,this._onlineMemberCountMap=new Map,this.DEFAULT_EXPIRE_TIME=60,this.DEFAULT_POLLING_INTERVAL=300,this.MAX_POLLING_INTERVAL=2e3,this._pollingInterval=this.DEFAULT_POLLING_INTERVAL}return Un(e,[{key:"hasJoinedAVChatRoom",value:function(){return this._joinedGroupMap.size>0}},{key:"checkJoinedAVChatRoomByID",value:function(e){return this._joinedGroupMap.has(e)}},{key:"getJoinedAVChatRoom",value:function(){return this._joinedGroupMap.size>0?$n(this._joinedGroupMap.keys()):null}},{key:"_updateRequestData",value:function(e){return xn({},this._pollingRequestInfoMap.get(e))}},{key:"_handleSuccess",value:function(e,t){var n=t.data,o=n.key,r=n.nextSeq,a=n.rspMsgList;if(0!==n.errorCode){var s=this._pollingRequestInfoMap.get(e),i=new Zp(dh),u=s?"".concat(s.key,"-").concat(s.startSeq):"requestInfo is undefined";i.setMessage("".concat(e,"-").concat(u,"-").concat(t.errorInfo)).setCode(t.errorCode).setNetworkType(this._groupModule.getNetworkType()).end(!0)}else{if(!this.checkJoinedAVChatRoomByID(e))return;ji(o)&&Hi(r)&&this._pollingRequestInfoMap.set(e,{key:o,startSeq:r}),zi(a)&&a.length>0&&(a.forEach((function(e){e.to=e.groupID})),this.onMessage(a))}}},{key:"_handleFailure",value:function(e,t){}},{key:"onMessage",value:function(e){if(zi(e)&&0!==e.length){var t=null,n=[],o=this._getModule(Wc),r=e.length;r>1&&e.sort((function(e,t){return e.sequence-t.sequence}));for(var a=this._getModule(Yc),s=0;s<r;s++)if(rv[e[s].event]){this.receivedMessageCount+=1,t=this.packMessage(e[s],e[s].event);var i=1===e[s].isModified,u=1===e[s].isHistoryMessage;if((a.isUnlimitedAVChatRoom()||!this.sequencesLinkedList.has(t.sequence))&&!this.messageIDLinkedList.has(t.ID)){var c=t.conversationID;if(this.receivedMessageCount%40==0&&this._getModule(ol).detectMessageLoss(c,this.sequencesLinkedList.data()),null!==this.sequencesLinkedList.tail()){var l=this.sequencesLinkedList.tail().value,d=t.sequence-l;d>1&&d<=20?this._getModule(ol).onMessageMaybeLost(c,l+1,d-1):d<-1&&d>=-20&&this._getModule(ol).onMessageMaybeLost(c,t.sequence+1,Math.abs(d)-1)}this.sequencesLinkedList.set(t.sequence),this.messageIDLinkedList.set(t.ID);var p=!1;if(this._isMessageSentByCurrentInstance(t)?i&&(p=!0,t.isModified=i,o.updateMessageIsModifiedProperty(t)):p=!0,p){if(t.conversationType,so.CONV_SYSTEM,!u&&t.conversationType!==so.CONV_SYSTEM){var g=this._getModule(il),h=t.conversationID.replace(so.CONV_GROUP,"");this._pollingInstanceMap.has(h)?g.addMessageSequence({key:zp,message:t}):(t.type!==so.MSG_GRP_TIP&&g.addMessageDelay({currentTime:Date.now(),time:t.time}),g.addMessageSequence({key:Yp,message:t}))}n.push(t)}}}else Bi.warn("".concat(this._className,".onMessage 未处理的 event 类型: ").concat(e[s].event));if(0!==n.length){this._groupModule.filterModifiedMessage(n);var f=this.packConversationOption(n);f.length>0&&this._getModule(Wc).onNewMessage({conversationOptionsList:f,isInstantMessage:!0}),Bi.debug("".concat(this._className,".onMessage count:").concat(n.length)),this._checkMessageStacked(n);var _=this._groupModule.filterUnmodifiedMessage(n);_.length>0&&this._groupModule.emitOuterEvent(ao.MESSAGE_RECEIVED,_),n.length=0}}}},{key:"_checkMessageStacked",value:function(e){var t=e.length;t>=100&&(Bi.warn("".concat(this._className,"._checkMessageStacked 直播群消息堆积数:").concat(e.length,'！可能会导致微信小程序渲染时遇到 "Dom limit exceeded" 的错误，建议接入侧此时只渲染最近的10条消息')),this._reportMessageStackedCount<5&&(new Zp(gh).setNetworkType(this._groupModule.getNetworkType()).setMessage("count:".concat(t," groupID:").concat($n(this._joinedGroupMap.keys()))).setLevel("warning").end(),this._reportMessageStackedCount+=1))}},{key:"_isMessageSentByCurrentInstance",value:function(e){return!!this._getModule(Wc).isMessageSentByCurrentInstance(e)}},{key:"packMessage",value:function(e,t){e.currentUser=this._groupModule.getMyUserID(),e.conversationType=5===t?so.CONV_SYSTEM:so.CONV_GROUP,e.isSystemMessage=!!e.isSystemMessage;var n=new um(e),o=this.packElements(e,t);return n.setElement(o),n}},{key:"packElements",value:function(e,t){return 4===t||6===t?(this._updateMemberCountByGroupTips(e),this._onGroupAttributesUpdated(e),{type:so.MSG_GRP_TIP,content:xn(xn({},e.elements),{},{groupProfile:e.groupProfile})}):5===t?{type:so.MSG_GRP_SYS_NOTICE,content:xn(xn({},e.elements),{},{groupProfile:e.groupProfile})}:this._getModule(Qc).parseElements(e.elements,e.from)}},{key:"packConversationOption",value:function(e){for(var t=new Map,n=0;n<e.length;n++){var o=e[n],r=o.conversationID;if(t.has(r)){var a=t.get(r);a.lastMessage=o,"in"===o.flow&&a.unreadCount++}else t.set(r,{conversationID:o.conversationID,unreadCount:"out"===o.flow?0:1,type:o.conversationType,subType:o.conversationSubType,lastMessage:o})}return $n(t.values())}},{key:"_updateMemberCountByGroupTips",value:function(e){var t=e.groupProfile.groupID,n=e.elements.onlineMemberInfo,o=void 0===n?void 0:n;if(!Nu(o)){var r=o.onlineMemberNum,a=void 0===r?0:r,s=o.expireTime,i=void 0===s?this.DEFAULT_EXPIRE_TIME:s,u=this._onlineMemberCountMap.get(t)||{},c=Date.now();Nu(u)?Object.assign(u,{lastReqTime:0,lastSyncTime:0,latestUpdateTime:c,memberCount:a,expireTime:i}):(u.latestUpdateTime=c,u.memberCount=a),Bi.debug("".concat(this._className,"._updateMemberCountByGroupTips info:"),u),this._onlineMemberCountMap.set(t,u)}}},{key:"start",value:function(e){if(this._pollingInstanceMap.has(e)){var t=this._pollingInstanceMap.get(e);t.isRunning()||t.start()}else{var n=new ov({manager:this,groupID:e,onInit:this._updateRequestData.bind(this),onSuccess:this._handleSuccess.bind(this),onFail:this._handleFailure.bind(this)});n.start(),this._pollingInstanceMap.set(e,n),Bi.log("".concat(this._className,".start groupID:").concat(e))}}},{key:"handleJoinResult",value:function(e){var t=this;return this._preCheck().then((function(){var n=e.longPollingKey,o=e.group,r=o.groupID;return t._joinedGroupMap.set(r,o),t._groupModule.updateGroupMap([o]),t._groupModule.deleteUnjoinedAVChatRoom(r),t._groupModule.emitGroupListUpdate(!0,!1),Ji(n)?vm({status:x_,group:o}):Promise.resolve()}))}},{key:"startRunLoop",value:function(e){var t=this;return this.handleJoinResult(e).then((function(){var n=e.longPollingKey,o=e.group,r=o.groupID;return t._pollingRequestInfoMap.set(r,{key:n,startSeq:0}),t.start(r),t._groupModule.isLoggedIn()?vm({status:x_,group:o}):vm({status:x_})}))}},{key:"_preCheck",value:function(){if(this._getModule(Yc).isUnlimitedAVChatRoom())return Promise.resolve();if(!this.hasJoinedAVChatRoom())return Promise.resolve();var e=Qn(this._joinedGroupMap.entries().next().value,2),t=e[0],n=e[1];if(this._groupModule.isLoggedIn()){if(n.selfInfo.role!==so.GRP_MBR_ROLE_OWNER&&n.ownerID!==this._groupModule.getMyUserID())return this._groupModule.quitGroup(t);this._groupModule.deleteLocalGroupAndConversation(t)}else this._groupModule.deleteLocalGroupAndConversation(t);return this.reset(t),Promise.resolve()}},{key:"joinWithoutAuth",value:function(e){var t=this,n=e.groupID,o="".concat(this._className,".joinWithoutAuth"),r=new Zp(Qg);return this._groupModule.request({protocolName:Vl,requestData:e}).then((function(e){var a=e.data.longPollingKey;if(t._groupModule.probeNetwork().then((function(e){var t=Qn(e,2),o=(t[0],t[1]);r.setNetworkType(o).setMessage("groupID:".concat(n," longPollingKey:").concat(a)).end(!0)})),Ji(a))return Mm(new fm({code:kd.CANNOT_JOIN_NON_AVCHATROOM_WITHOUT_LOGIN,message:vp}));Bi.log("".concat(o," ok. groupID:").concat(n)),t._getModule(Wc).setCompleted("".concat(so.CONV_GROUP).concat(n));var s=new Hm({groupID:n});return t.startRunLoop({group:s,longPollingKey:a}),cm({status:x_})})).catch((function(e){return Bi.error("".concat(o," failed. groupID:").concat(n," error:"),e),t._groupModule.probeNetwork().then((function(t){var o=Qn(t,2),a=o[0],s=o[1];r.setError(e,a,s).setMessage("groupID:".concat(n)).end(!0)})),Mm(e)})).finally((function(){t._groupModule.getModule(Jc).reportAtOnce()}))}},{key:"getGroupOnlineMemberCount",value:function(e){var t=this._onlineMemberCountMap.get(e)||{},n=Date.now();return Nu(t)||n-t.lastSyncTime>1e3*t.expireTime&&n-t.latestUpdateTime>1e4&&n-t.lastReqTime>3e3?(t.lastReqTime=n,this._onlineMemberCountMap.set(e,t),this._getGroupOnlineMemberCount(e).then((function(e){return cm({memberCount:e.memberCount})})).catch((function(e){return Mm(e)}))):vm({memberCount:t.memberCount})}},{key:"_getGroupOnlineMemberCount",value:function(e){var t=this,n="".concat(this._className,"._getGroupOnlineMemberCount");return this._groupModule.request({protocolName:td,requestData:{groupID:e}}).then((function(o){var r=t._onlineMemberCountMap.get(e)||{},a=o.data,s=a.onlineMemberNum,i=void 0===s?0:s,u=a.expireTime,c=void 0===u?t.DEFAULT_EXPIRE_TIME:u;Bi.log("".concat(n," ok. groupID:").concat(e," memberCount:").concat(i," expireTime:").concat(c));var l=Date.now();return Nu(r)&&(r.lastReqTime=l),t._onlineMemberCountMap.set(e,Object.assign(r,{lastSyncTime:l,latestUpdateTime:l,memberCount:i,expireTime:c})),{memberCount:i}})).catch((function(o){return Bi.warn("".concat(n," failed. error:"),o),new Zp(lh).setCode(o.code).setMessage("groupID:".concat(e," error:").concat(JSON.stringify(o))).setNetworkType(t._groupModule.getNetworkType()).end(),Promise.reject(o)}))}},{key:"_onGroupAttributesUpdated",value:function(e){var t=e.groupProfile.groupID,n=e.elements,o=n.operationType,r=n.newGroupProfile;if(6===o){var a=(void 0===r?void 0:r).groupAttributeOption;Nu(a)||this._groupModule.onGroupAttributesUpdated({groupID:t,groupAttributeOption:a})}}},{key:"_getModule",value:function(e){return this._groupModule.getModule(e)}},{key:"setPollingInterval",value:function(e){Ji(e)||Hi(e)||(this._pollingInterval=parseInt(e,10),Bi.log("".concat(this._className,".setPollingInterval value:").concat(this._pollingInterval)))}},{key:"getPollingInterval",value:function(){return this._pollingInterval}},{key:"reset",value:function(e){if(e){Bi.log("".concat(this._className,".reset groupID:").concat(e));var t=this._pollingInstanceMap.get(e);t&&t.stop(),this._pollingInstanceMap.delete(e),this._joinedGroupMap.delete(e),this._pollingRequestInfoMap.delete(e),this._onlineMemberCountMap.delete(e)}else{Bi.log("".concat(this._className,".reset all"));var n,o=ro(this._pollingInstanceMap.values());try{for(o.s();!(n=o.n()).done;)n.value.stop()}catch(a){o.e(a)}finally{o.f()}this._pollingInstanceMap.clear(),this._joinedGroupMap.clear(),this._pollingRequestInfoMap.clear(),this._onlineMemberCountMap.clear()}this.sequencesLinkedList.reset(),this.messageIDLinkedList.reset(),this.receivedMessageCount=0,this._reportMessageStackedCount=0,this._pollingInterval=this.DEFAULT_POLLING_INTERVAL}}]),e}(),sv=1,iv=15,uv=function(){function e(t){Gn(this,e),this._groupModule=t,this._className="GroupSystemNoticeHandler",this.pendencyMap=new Map}return Un(e,[{key:"onNewGroupSystemNotice",value:function(e){var t=e.dataList,n=e.isSyncingEnded,o=e.isInstantMessage;Bi.debug("".concat(this._className,".onReceiveSystemNotice count:").concat(t.length));var r=this.newSystemNoticeStoredAndSummary({notifiesList:t,isInstantMessage:o}),a=r.eventDataList,s=r.result;a.length>0&&(this._groupModule.getModule(Wc).onNewMessage({conversationOptionsList:a,isInstantMessage:o}),this._onReceivedGroupSystemNotice({result:s,isInstantMessage:o})),o?s.length>0&&this._groupModule.emitOuterEvent(ao.MESSAGE_RECEIVED,s):!0===n&&this._clearGroupSystemNotice()}},{key:"newSystemNoticeStoredAndSummary",value:function(e){var t=e.notifiesList,n=e.isInstantMessage,o=null,r=t.length,a=0,s=[],i={conversationID:so.CONV_SYSTEM,unreadCount:0,type:so.CONV_SYSTEM,subType:null,lastMessage:null};for(a=0;a<r;a++){var u=t[a];u.elements.operationType!==iv&&(u.currentUser=this._groupModule.getMyUserID(),u.conversationType=so.CONV_SYSTEM,u.conversationID=so.CONV_SYSTEM,(o=new um(u)).setElement({type:so.MSG_GRP_SYS_NOTICE,content:xn(xn({},u.elements),{},{groupProfile:u.groupProfile})}),o.isSystemMessage=!0,(1===o.sequence&&1===o.random||2===o.sequence&&2===o.random)&&(o.sequence=su(),o.random=su(),o.generateMessageID(u.currentUser),Bi.log("".concat(this._className,".newSystemNoticeStoredAndSummary sequence and random maybe duplicated, regenerate. ID:").concat(o.ID))),this._groupModule.getModule(Wc).pushIntoNoticeResult(s,o)&&(n?i.unreadCount++:o.setIsRead(!0),i.subType=o.conversationSubType))}return i.lastMessage=s[s.length-1],{eventDataList:s.length>0?[i]:[],result:s}}},{key:"_clearGroupSystemNotice",value:function(){var e=this;this.getPendencyList().then((function(t){t.forEach((function(t){e.pendencyMap.set("".concat(t.from,"_").concat(t.groupID,"_").concat(t.to),t)}));var n=e._groupModule.getModule(Wc).getLocalMessageList(so.CONV_SYSTEM),o=[];n.forEach((function(t){var n=t.payload,r=n.operatorID,a=n.operationType,s=n.groupProfile;if(a===sv){var i="".concat(r,"_").concat(s.groupID,"_").concat(s.to),u=e.pendencyMap.get(i);u&&Hi(u.handled)&&0!==u.handled&&o.push(t)}})),e.deleteGroupSystemNotice({messageList:o})}))}},{key:"deleteGroupSystemNotice",value:function(e){var t=this,n="".concat(this._className,".deleteGroupSystemNotice");return zi(e.messageList)&&0!==e.messageList.length?(Bi.log("".concat(n)+e.messageList.map((function(e){return e.ID}))),this._groupModule.request({protocolName:$l,requestData:{messageListToDelete:e.messageList.map((function(e){return{from:so.CONV_SYSTEM,messageSeq:e.clientSequence,messageRandom:e.random}}))}}).then((function(){Bi.log("".concat(n," ok"));var o=t._groupModule.getModule(Wc);return e.messageList.forEach((function(e){o.deleteLocalMessage(e)})),cm()})).catch((function(e){return Bi.error("".concat(n," error:"),e),Mm(e)}))):vm()}},{key:"getPendencyList",value:function(e){var t=this;return this._groupModule.request({protocolName:Ql,requestData:{startTime:e&&e.startTime?e.startTime:0,limit:e&&e.limit?e.limit:10,handleAccount:this._groupModule.getMyUserID()}}).then((function(e){var n=e.data.pendencyList;return 0!==e.data.nextStartTime?t.getPendencyList({startTime:e.data.nextStartTime}).then((function(e){return[].concat($n(n),$n(e))})):n}))}},{key:"_onReceivedGroupSystemNotice",value:function(e){var t=this,n=e.result;e.isInstantMessage&&n.forEach((function(e){switch(e.payload.operationType){case 1:break;case 2:t._onApplyGroupRequestAgreed(e);break;case 3:break;case 4:t._onMemberKicked(e);break;case 5:t._onGroupDismissed(e);break;case 6:break;case 7:t._onInviteGroup(e);break;case 8:t._onQuitGroup(e);break;case 9:t._onSetManager(e);break;case 10:t._onDeleteManager(e)}}))}},{key:"_onApplyGroupRequestAgreed",value:function(e){var t=this,n=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(n)||this._groupModule.getGroupProfile({groupID:n}).then((function(e){var n=e.data.group;n&&(t._groupModule.updateGroupMap([n]),t._groupModule.emitGroupListUpdate())}))}},{key:"_onMemberKicked",value:function(e){var t=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(t)&&this._groupModule.deleteLocalGroupAndConversation(t)}},{key:"_onGroupDismissed",value:function(e){var t=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(t)&&this._groupModule.deleteLocalGroupAndConversation(t);var n=this._groupModule._AVChatRoomHandler;n&&n.checkJoinedAVChatRoomByID(t)&&n.reset(t)}},{key:"_onInviteGroup",value:function(e){var t=this,n=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(n)||this._groupModule.getGroupProfile({groupID:n}).then((function(e){var n=e.data.group;n&&(t._groupModule.updateGroupMap([n]),t._groupModule.emitGroupListUpdate())}))}},{key:"_onQuitGroup",value:function(e){var t=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(t)&&this._groupModule.deleteLocalGroupAndConversation(t)}},{key:"_onSetManager",value:function(e){var t=e.payload.groupProfile,n=t.to,o=t.groupID,r=this._groupModule.getModule(jc).getLocalGroupMemberInfo(o,n);r&&r.updateRole(so.GRP_MBR_ROLE_ADMIN)}},{key:"_onDeleteManager",value:function(e){var t=e.payload.groupProfile,n=t.to,o=t.groupID,r=this._groupModule.getModule(jc).getLocalGroupMemberInfo(o,n);r&&r.updateRole(so.GRP_MBR_ROLE_MEMBER)}},{key:"reset",value:function(){this.pendencyMap.clear()}}]),e}(),cv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="GroupModule",o._commonGroupHandler=null,o._AVChatRoomHandler=null,o._groupSystemNoticeHandler=null,o._commonGroupHandler=new ev(zn(o)),o._groupAttributesHandler=new nv(zn(o)),o._AVChatRoomHandler=new av(zn(o)),o._groupTipsHandler=new Xm(zn(o)),o._groupSystemNoticeHandler=new uv(zn(o)),o.groupMap=new Map,o._unjoinedAVChatRoomList=new Map,o.getInnerEmitterInstance().on(Rm,o._onCloudConfigUpdated,zn(o)),o}return Un(n,[{key:"_onCloudConfigUpdated",value:function(){var e=this.getCloudConfig("polling_interval");this._AVChatRoomHandler&&this._AVChatRoomHandler.setPollingInterval(e)}},{key:"onCheckTimer",value:function(e){this.isLoggedIn()&&(this._commonGroupHandler.onCheckTimer(e),this._groupTipsHandler.onCheckTimer(e))}},{key:"guardForAVChatRoom",value:function(e){var t=this;if(e.conversationType===so.CONV_GROUP){var n=e.to;return this.hasLocalGroup(n)?vm():this.getGroupProfile({groupID:n}).then((function(o){var r=o.data.group.type;if(Bi.log("".concat(t._className,".guardForAVChatRoom. groupID:").concat(n," type:").concat(r)),r===so.GRP_AVCHATROOM){var a="userId:".concat(e.from," 未加入群 groupID:").concat(n,"。发消息前先使用 joinGroup 接口申请加群，详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#joinGroup");return Bi.warn("".concat(t._className,".guardForAVChatRoom sendMessage not allowed. ").concat(a)),Mm(new fm({code:kd.MESSAGE_SEND_FAIL,message:a,data:{message:e}}))}return vm()}))}return vm()}},{key:"checkJoinedAVChatRoomByID",value:function(e){return!!this._AVChatRoomHandler&&this._AVChatRoomHandler.checkJoinedAVChatRoomByID(e)}},{key:"onNewGroupMessage",value:function(e){this._commonGroupHandler&&this._commonGroupHandler.onNewGroupMessage(e)}},{key:"updateNextMessageSeq",value:function(e){var t=this;zi(e)&&e.forEach((function(e){var n=e.conversationID.replace(so.CONV_GROUP,"");t.groupMap.has(n)&&(t.groupMap.get(n).nextMessageSeq=e.lastMessage.sequence+1)}))}},{key:"onNewGroupTips",value:function(e){this._groupTipsHandler&&this._groupTipsHandler.onNewGroupTips(e)}},{key:"onGroupMessageRevoked",value:function(e){this._commonGroupHandler&&this._commonGroupHandler.onGroupMessageRevoked(e)}},{key:"onNewGroupSystemNotice",value:function(e){this._groupSystemNoticeHandler&&this._groupSystemNoticeHandler.onNewGroupSystemNotice(e)}},{key:"onGroupMessageReadNotice",value:function(e){var t=this;e.dataList.forEach((function(e){var n=e.elements.groupMessageReadNotice;if(!Ji(n)){var o=t.getModule(Wc);n.forEach((function(e){var n=e.groupID,r=e.lastMessageSeq;Bi.debug("".concat(t._className,".onGroupMessageReadNotice groupID:").concat(n," lastMessageSeq:").concat(r));var a="".concat(so.CONV_GROUP).concat(n);o.updateIsReadAfterReadReport({conversationID:a,lastMessageSeq:r}),o.updateUnreadCount(a)}))}}))}},{key:"deleteGroupSystemNotice",value:function(e){this._groupSystemNoticeHandler&&this._groupSystemNoticeHandler.deleteGroupSystemNotice(e)}},{key:"initGroupMap",value:function(e){this.groupMap.set(e.groupID,new Hm(e))}},{key:"deleteGroup",value:function(e){this.groupMap.delete(e)}},{key:"updateGroupMap",value:function(e){var t=this;e.forEach((function(e){t.groupMap.has(e.groupID)?t.groupMap.get(e.groupID).updateGroup(e):t.groupMap.set(e.groupID,new Hm(e))}));var n,o=this.getMyUserID(),r=ro(this.groupMap);try{for(r.s();!(n=r.n()).done;)Qn(n.value,2)[1].selfInfo.userID=o}catch(i){r.e(i)}finally{r.f()}this._setStorageGroupList()}},{key:"getStorageGroupList",value:function(){return this.getModule(zc).getItem("groupMap")}},{key:"_setStorageGroupList",value:function(){var e=this.getLocalGroupList().filter((function(e){var t=e.type;return!_u(t)})).slice(0,20).map((function(e){return{groupID:e.groupID,name:e.name,avatar:e.avatar,type:e.type}}));this.getModule(zc).setItem("groupMap",e)}},{key:"getGroupMap",value:function(){return this.groupMap}},{key:"getLocalGroupList",value:function(){return $n(this.groupMap.values())}},{key:"getLocalGroupProfile",value:function(e){return this.groupMap.get(e)}},{key:"sortLocalGroupList",value:function(){var e=$n(this.groupMap).filter((function(e){var t=Qn(e,2);return t[0],!Nu(t[1].lastMessage)}));e.sort((function(e,t){return t[1].lastMessage.lastTime-e[1].lastMessage.lastTime})),this.groupMap=new Map($n(e))}},{key:"updateGroupLastMessage",value:function(e){this._commonGroupHandler&&this._commonGroupHandler.handleUpdateGroupLastMessage(e)}},{key:"emitGroupListUpdate",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=this.getLocalGroupList();if(e&&this.emitOuterEvent(ao.GROUP_LIST_UPDATED,n),t){var o=JSON.parse(JSON.stringify(n)),r=this.getModule(Wc);r.updateConversationGroupProfile(o)}}},{key:"patchGroupMessageRemindType",value:function(){var e=this.getLocalGroupList(),t=this.getModule(Wc),n=0;e.forEach((function(e){!0===t.patchMessageRemindType({ID:e.groupID,isC2CConversation:!1,messageRemindType:e.selfInfo.messageRemindType})&&(n+=1)})),Bi.log("".concat(this._className,".patchGroupMessageRemindType count:").concat(n))}},{key:"recomputeUnreadCount",value:function(){var e=this.getLocalGroupList(),t=this.getModule(Wc);e.forEach((function(e){var n=e.groupID,o=e.selfInfo,r=o.excludedUnreadSequenceList,a=o.readedSequence;if(zi(r)){var s=0;r.forEach((function(t){t>=a&&t<=e.nextMessageSeq-1&&(s+=1)})),s>=1&&t.recomputeGroupUnreadCount({conversationID:"".concat(so.CONV_GROUP).concat(n),count:s})}}))}},{key:"getMyNameCardByGroupID",value:function(e){var t=this.getLocalGroupProfile(e);return t?t.selfInfo.nameCard:""}},{key:"getGroupList",value:function(e){return this._commonGroupHandler?this._commonGroupHandler.getGroupList(e):vm()}},{key:"getGroupProfile",value:function(e){var t=this,n=new Zp(jg),o="".concat(this._className,".getGroupProfile"),r=e.groupID,a=e.groupCustomFieldFilter;Bi.log("".concat(o," groupID:").concat(r));var s={groupIDList:[r],responseFilter:{groupBaseInfoFilter:["Type","Name","Introduction","Notification","FaceUrl","Owner_Account","CreateTime","InfoSeq","LastInfoTime","LastMsgTime","MemberNum","MaxMemberNum","ApplyJoinOption","NextMsgSeq","ShutUpAllMember"],groupCustomFieldFilter:a,memberInfoFilter:["Role","JoinTime","MsgSeq","MsgFlag","NameCard"]}};return this.getGroupProfileAdvance(s).then((function(e){var a,s=e.data,i=s.successGroupList,u=s.failureGroupList;return Bi.log("".concat(o," ok")),u.length>0?Mm(u[0]):(_u(i[0].type)&&!t.hasLocalGroup(r)?a=new Hm(i[0]):(t.updateGroupMap(i),a=t.getLocalGroupProfile(r)),n.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(r," type:").concat(a.type," muteAllMembers:").concat(a.muteAllMembers," ownerID:").concat(a.ownerID)).end(),cm({group:a}))})).catch((function(r){return t.probeNetwork().then((function(t){var o=Qn(t,2),a=o[0],s=o[1];n.setError(r,a,s).setMessage("groupID:".concat(e.groupID)).end()})),Bi.error("".concat(o," failed. error:"),r),Mm(r)}))}},{key:"getGroupProfileAdvance",value:function(e){var t="".concat(this._className,".getGroupProfileAdvance");return zi(e.groupIDList)&&e.groupIDList.length>50&&(Bi.warn("".concat(t," 获取群资料的数量不能超过50个")),e.groupIDList.length=50),Bi.log("".concat(t," groupIDList:").concat(e.groupIDList)),this.request({protocolName:Pl,requestData:e}).then((function(e){Bi.log("".concat(t," ok"));var n=e.data.groups,o=n.filter((function(e){return Ji(e.errorCode)||0===e.errorCode})),r=n.filter((function(e){return e.errorCode&&0!==e.errorCode})).map((function(e){return new fm({code:e.errorCode,message:e.errorInfo,data:{groupID:e.groupID}})}));return cm({successGroupList:o,failureGroupList:r})})).catch((function(e){return Bi.error("".concat(t," failed. error:"),e),Mm(e)}))}},{key:"createGroup",value:function(e){var t=this,n="".concat(this._className,".createGroup");if(!["Public","Private","ChatRoom","AVChatRoom"].includes(e.type)){var o=new fm({code:kd.ILLEGAL_GROUP_TYPE,message:lp});return Mm(o)}_u(e.type)&&!Ji(e.memberList)&&e.memberList.length>0&&(Bi.warn("".concat(n," 创建 AVChatRoom 时不能添加群成员，自动忽略该字段")),e.memberList=void 0),fu(e.type)||Ji(e.joinOption)||(Bi.warn("".concat(n," 创建 Work/Meeting/AVChatRoom 群时不能设置字段 joinOption，自动忽略该字段")),e.joinOption=void 0);var r=new Zp(wg);Bi.log("".concat(n," options:"),e);var a=[];return this.request({protocolName:Ul,requestData:xn(xn({},e),{},{ownerID:this.getMyUserID(),webPushFlag:1})}).then((function(o){var s=o.data,i=s.groupID,u=s.overLimitUserIDList,c=void 0===u?[]:u;if(a=c,r.setNetworkType(t.getNetworkType()).setMessage("groupType:".concat(e.type," groupID:").concat(i," overLimitUserIDList=").concat(c)).end(),Bi.log("".concat(n," ok groupID:").concat(i," overLimitUserIDList:"),c),e.type===so.GRP_AVCHATROOM)return t.getGroupProfile({groupID:i});Nu(e.memberList)||Nu(c)||(e.memberList=e.memberList.filter((function(e){return-1===c.indexOf(e.userID)}))),t.updateGroupMap([xn(xn({},e),{},{groupID:i})]);var l=t.getModule(xc),d=l.createCustomMessage({to:i,conversationType:so.CONV_GROUP,payload:{data:"group_create",extension:"".concat(t.getMyUserID(),"创建群组")}});return l.sendMessageInstance(d),t.emitGroupListUpdate(),t.getGroupProfile({groupID:i})})).then((function(e){var t=e.data.group,n=t.selfInfo,o=n.nameCard,r=n.joinTime;return t.updateSelfInfo({nameCard:o,joinTime:r,messageRemindType:so.MSG_REMIND_ACPT_AND_NOTE,role:so.GRP_MBR_ROLE_OWNER}),cm({group:t,overLimitUserIDList:a})})).catch((function(o){return r.setMessage("groupType:".concat(e.type)),t.probeNetwork().then((function(e){var t=Qn(e,2),n=t[0],a=t[1];r.setError(o,n,a).end()})),Bi.error("".concat(n," failed. error:"),o),Mm(o)}))}},{key:"dismissGroup",value:function(e){var t=this,n="".concat(this._className,".dismissGroup");if(this.hasLocalGroup(e)&&this.getLocalGroupProfile(e).type===so.GRP_WORK)return Mm(new fm({code:kd.CANNOT_DISMISS_WORK,message:hp}));var o=new Zp(Bg);return o.setMessage("groupID:".concat(e)),Bi.log("".concat(n," groupID:").concat(e)),this.request({protocolName:Fl,requestData:{groupID:e}}).then((function(){return o.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),t.deleteLocalGroupAndConversation(e),t.checkJoinedAVChatRoomByID(e)&&t._AVChatRoomHandler.reset(e),cm({groupID:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"updateGroupProfile",value:function(e){var t=this,n="".concat(this._className,".updateGroupProfile");!this.hasLocalGroup(e.groupID)||fu(this.getLocalGroupProfile(e.groupID).type)||Ji(e.joinOption)||(Bi.warn("".concat(n," Work/Meeting/AVChatRoom 群不能设置字段 joinOption，自动忽略该字段")),e.joinOption=void 0),Ji(e.muteAllMembers)||(e.muteAllMembers?e.muteAllMembers="On":e.muteAllMembers="Off");var o=new Zp(Kg);return o.setMessage(JSON.stringify(e)),Bi.log("".concat(n," groupID:").concat(e.groupID)),this.request({protocolName:ql,requestData:e}).then((function(){return o.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),t.hasLocalGroup(e.groupID)&&(t.groupMap.get(e.groupID).updateGroup(e),t._setStorageGroupList()),cm({group:t.groupMap.get(e.groupID)})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.log("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"joinGroup",value:function(e){var t=this,n=e.groupID,o=e.type,r="".concat(this._className,".joinGroup");if(o===so.GRP_WORK){var a=new fm({code:kd.CANNOT_JOIN_WORK,message:dp});return Mm(a)}if(this.deleteUnjoinedAVChatRoom(n),this.hasLocalGroup(n)){if(!this.isLoggedIn())return vm({status:so.JOIN_STATUS_ALREADY_IN_GROUP});var s=new Zp(Gg);return this.getGroupProfile({groupID:n}).then((function(){return s.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(n," joinedStatus:").concat(so.JOIN_STATUS_ALREADY_IN_GROUP)).end(),vm({status:so.JOIN_STATUS_ALREADY_IN_GROUP})})).catch((function(o){return s.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(n," unjoined")).end(),Bi.warn("".concat(r," ").concat(n," was unjoined, now join!")),t.groupMap.delete(n),t.applyJoinGroup(e)}))}return Bi.log("".concat(r," groupID:").concat(n)),this.isLoggedIn()?this.applyJoinGroup(e):this._AVChatRoomHandler.joinWithoutAuth(e)}},{key:"applyJoinGroup",value:function(e){var t=this,n="".concat(this._className,".applyJoinGroup"),o=e.groupID,r=new Zp(Gg),a=xn({},e),s=this.canIUse(Xs.AVCHATROOM_HISTORY_MSG);return s&&(a.historyMessageFlag=1),this.request({protocolName:xl,requestData:a}).then((function(e){var a=e.data,i=a.joinedStatus,u=a.longPollingKey,c=a.avChatRoomFlag,l=a.avChatRoomKey,d=a.messageList,p="groupID:".concat(o," joinedStatus:").concat(i," longPollingKey:").concat(u)+" avChatRoomFlag:".concat(c," canGetAVChatRoomHistoryMessage:").concat(s);switch(r.setNetworkType(t.getNetworkType()).setMessage("".concat(p)).end(),Bi.log("".concat(n," ok. ").concat(p)),i){case V_:return cm({status:V_});case x_:return t.getGroupProfile({groupID:o}).then((function(e){var n,r=e.data.group,a={status:x_,group:r};return 1===c?(t.getModule(Wc).setCompleted("".concat(so.CONV_GROUP).concat(o)),t._groupAttributesHandler.initGroupAttributesCache({groupID:o,avChatRoomKey:l}),(n=Ji(u)?t._AVChatRoomHandler.handleJoinResult({group:r}):t._AVChatRoomHandler.startRunLoop({longPollingKey:u,group:r})).then((function(){t._onAVChatRoomHistoryMessage(d)})),n):(t.emitGroupListUpdate(!0,!1),cm(a))}));default:var g=new fm({code:kd.JOIN_GROUP_FAIL,message:_p});return Bi.error("".concat(n," error:"),g),Mm(g)}})).catch((function(o){return r.setMessage("groupID:".concat(e.groupID)),t.probeNetwork().then((function(e){var t=Qn(e,2),n=t[0],a=t[1];r.setError(o,n,a).end()})),Bi.error("".concat(n," error:"),o),Mm(o)}))}},{key:"quitGroup",value:function(e){var t=this,n="".concat(this._className,".quitGroup");Bi.log("".concat(n," groupID:").concat(e));var o=this.checkJoinedAVChatRoomByID(e);if(!o&&!this.hasLocalGroup(e)){var r=new fm({code:kd.MEMBER_NOT_IN_GROUP,message:fp});return Mm(r)}if(o&&!this.isLoggedIn())return Bi.log("".concat(n," anonymously ok. groupID:").concat(e)),this.deleteLocalGroupAndConversation(e),this._AVChatRoomHandler.reset(e),vm({groupID:e});var a=new Zp(Pg);return a.setMessage("groupID:".concat(e)),this.request({protocolName:Bl,requestData:{groupID:e}}).then((function(){return a.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),o&&t._AVChatRoomHandler.reset(e),t.deleteLocalGroupAndConversation(e),cm({groupID:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"searchGroupByID",value:function(e){var t=this,n="".concat(this._className,".searchGroupByID"),o={groupIDList:[e]},r=new Zp(Ug);return r.setMessage("groupID:".concat(e)),Bi.log("".concat(n," groupID:").concat(e)),this.request({protocolName:Kl,requestData:o}).then((function(e){var o=e.data.groupProfile;if(0!==o[0].errorCode)throw new fm({code:o[0].errorCode,message:o[0].errorInfo});return r.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),cm({group:new Hm(o[0])})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],a=n[1];r.setError(e,o,a).end()})),Bi.warn("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"changeGroupOwner",value:function(e){var t=this,n="".concat(this._className,".changeGroupOwner");if(this.hasLocalGroup(e.groupID)&&this.getLocalGroupProfile(e.groupID).type===so.GRP_AVCHATROOM)return Mm(new fm({code:kd.CANNOT_CHANGE_OWNER_IN_AVCHATROOM,message:pp}));if(e.newOwnerID===this.getMyUserID())return Mm(new fm({code:kd.CANNOT_CHANGE_OWNER_TO_SELF,message:gp}));var o=new Zp(Fg);return o.setMessage("groupID:".concat(e.groupID," newOwnerID:").concat(e.newOwnerID)),Bi.log("".concat(n," groupID:").concat(e.groupID)),this.request({protocolName:Hl,requestData:e}).then((function(){o.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok"));var r=e.groupID,a=e.newOwnerID;t.groupMap.get(r).ownerID=a;var s=t.getModule(jc).getLocalGroupMemberList(r);if(s instanceof Map){var i=s.get(t.getMyUserID());Ji(i)||(i.updateRole("Member"),t.groupMap.get(r).selfInfo.role="Member");var u=s.get(a);Ji(u)||u.updateRole("Owner")}return t.emitGroupListUpdate(!0,!1),cm({group:t.groupMap.get(r)})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"handleGroupApplication",value:function(e){var t=this,n="".concat(this._className,".handleGroupApplication"),o=e.message.payload,r=o.groupProfile.groupID,a=o.authentication,s=o.messageKey,i=o.operatorID,u=new Zp(qg);return u.setMessage("groupID:".concat(r)),Bi.log("".concat(n," groupID:").concat(r)),this.request({protocolName:jl,requestData:xn(xn({},e),{},{applicant:i,groupID:r,authentication:a,messageKey:s})}).then((function(){return u.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),t._groupSystemNoticeHandler.deleteGroupSystemNotice({messageList:[e.message]}),cm({group:t.getLocalGroupProfile(r)})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];u.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error"),e),Mm(e)}))}},{key:"handleGroupInvitation",value:function(e){var t=this,n="".concat(this._className,".handleGroupInvitation"),o=e.message.payload,r=o.groupProfile.groupID,a=o.authentication,s=o.messageKey,i=o.operatorID,u=e.handleAction,c=new Zp(xg);return c.setMessage("groupID:".concat(r," inviter:").concat(i," handleAction:").concat(u)),Bi.log("".concat(n," groupID:").concat(r," inviter:").concat(i," handleAction:").concat(u)),this.request({protocolName:Wl,requestData:xn(xn({},e),{},{inviter:i,groupID:r,authentication:a,messageKey:s})}).then((function(){return c.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),t._groupSystemNoticeHandler.deleteGroupSystemNotice({messageList:[e.message]}),cm({group:t.getLocalGroupProfile(r)})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];c.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error"),e),Mm(e)}))}},{key:"getGroupOnlineMemberCount",value:function(e){return this._AVChatRoomHandler?this._AVChatRoomHandler.checkJoinedAVChatRoomByID(e)?this._AVChatRoomHandler.getGroupOnlineMemberCount(e):vm({memberCount:0}):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"hasLocalGroup",value:function(e){return this.groupMap.has(e)}},{key:"deleteLocalGroupAndConversation",value:function(e){this._deleteLocalGroup(e),this.getModule(Wc).deleteLocalConversation("GROUP".concat(e)),this.emitGroupListUpdate(!0,!1)}},{key:"_deleteLocalGroup",value:function(e){this.groupMap.delete(e),this.getModule(jc).deleteGroupMemberList(e),this._setStorageGroupList()}},{key:"sendMessage",value:function(e,t){var n=this.createGroupMessagePack(e,t);return this.request(n)}},{key:"createGroupMessagePack",value:function(e,t){var n=null;t&&t.offlinePushInfo&&(n=t.offlinePushInfo);var o="";ji(e.cloudCustomData)&&e.cloudCustomData.length>0&&(o=e.cloudCustomData);var r=[];if(Yi(t)&&Yi(t.messageControlInfo)){var a=t.messageControlInfo,s=a.excludedFromUnreadCount,i=a.excludedFromLastMessage;!0===s&&r.push("NoUnread"),!0===i&&r.push("NoLastMsg")}var u=e.getGroupAtInfoList();return{protocolName:ml,tjgID:this.generateTjgID(e),requestData:{fromAccount:this.getMyUserID(),groupID:e.to,msgBody:e.getElements(),cloudCustomData:o,random:e.random,priority:e.priority,clientSequence:e.clientSequence,groupAtInfo:e.type!==so.MSG_TEXT||Nu(u)?void 0:u,onlineOnlyFlag:this.isOnlineMessage(e,t)?1:0,offlinePushInfo:n?{pushFlag:!0===n.disablePush?1:0,title:n.title||"",desc:n.description||"",ext:n.extension||"",apnsInfo:{badgeMode:!0===n.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:n.androidOPPOChannelID||""}}:void 0,messageControlInfo:r}}}},{key:"revokeMessage",value:function(e){return this.request({protocolName:Yl,requestData:{to:e.to,msgSeqList:[{msgSeq:e.sequence}]}})}},{key:"deleteMessage",value:function(e){var t=e.to,n=e.keyList;return Bi.log("".concat(this._className,".deleteMessage groupID:").concat(t," count:").concat(n.length)),this.request({protocolName:nd,requestData:{groupID:t,deleter:this.getMyUserID(),keyList:n}})}},{key:"getRoamingMessage",value:function(e){var t=this,n="".concat(this._className,".getRoamingMessage"),o=new Zp(_g),r=0;return this._computeLastSequence(e).then((function(n){return r=n,Bi.log("".concat(t._className,".getRoamingMessage groupID:").concat(e.groupID," lastSequence:").concat(r)),t.request({protocolName:Xl,requestData:{groupID:e.groupID,count:21,sequence:r}})})).then((function(a){var s=a.data,i=s.messageList,u=s.complete;Ji(i)?Bi.log("".concat(n," ok. complete:").concat(u," but messageList is undefined!")):Bi.log("".concat(n," ok. complete:").concat(u," count:").concat(i.length)),o.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(e.groupID," lastSequence:").concat(r," complete:").concat(u," count:").concat(i?i.length:"undefined")).end();var c="GROUP".concat(e.groupID),l=t.getModule(Wc);if(2===u||Nu(i))return l.setCompleted(c),[];var d=l.storeRoamingMessage(i,c);return l.updateIsRead(c),l.patchConversationLastMessage(c),d})).catch((function(a){return t.probeNetwork().then((function(t){var n=Qn(t,2),s=n[0],i=n[1];o.setError(a,s,i).setMessage("groupID:".concat(e.groupID," lastSequence:").concat(r)).end()})),Bi.warn("".concat(n," failed. error:"),a),Mm(a)}))}},{key:"setMessageRead",value:function(e){var t=this,n=e.conversationID,o=e.lastMessageSeq,r="".concat(this._className,".setMessageRead");Bi.log("".concat(r," conversationID:").concat(n," lastMessageSeq:").concat(o)),Hi(o)||Bi.warn("".concat(r," 请勿修改 Conversation.lastMessage.lastSequence，否则可能会导致已读上报结果不准确"));var a=new Zp(yg);return a.setMessage("".concat(n,"-").concat(o)),this.request({protocolName:zl,requestData:{groupID:n.replace("GROUP",""),messageReadSeq:o}}).then((function(){a.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(r," ok."));var e=t.getModule(Wc);return e.updateIsReadAfterReadReport({conversationID:n,lastMessageSeq:o}),e.updateUnreadCount(n),cm()})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),Bi.log("".concat(r," failed. error:"),e),Mm(e)}))}},{key:"_computeLastSequence",value:function(e){return e.sequence>0?Promise.resolve(e.sequence):this.getGroupLastSequence(e.groupID)}},{key:"getGroupLastSequence",value:function(e){var t=this,n="".concat(this._className,".getGroupLastSequence"),o=new Zp(Yg),r=0,a="";if(this.hasLocalGroup(e)){var s=this.getLocalGroupProfile(e),i=s.lastMessage;if(i.lastSequence>0&&!1===i.onlineOnlyFlag)return r=i.lastSequence,a="got lastSequence:".concat(r," from local group profile[lastMessage.lastSequence]. groupID:").concat(e),Bi.log("".concat(n," ").concat(a)),o.setNetworkType(this.getNetworkType()).setMessage("".concat(a)).end(),Promise.resolve(r);if(s.nextMessageSeq>1)return r=s.nextMessageSeq-1,a="got lastSequence:".concat(r," from local group profile[nextMessageSeq]. groupID:").concat(e),Bi.log("".concat(n," ").concat(a)),o.setNetworkType(this.getNetworkType()).setMessage("".concat(a)).end(),Promise.resolve(r)}var u="GROUP".concat(e),c=this.getModule(Wc).getLocalConversation(u);if(c&&c.lastMessage.lastSequence&&!1===c.lastMessage.onlineOnlyFlag)return r=c.lastMessage.lastSequence,a="got lastSequence:".concat(r," from local conversation profile[lastMessage.lastSequence]. groupID:").concat(e),Bi.log("".concat(n," ").concat(a)),o.setNetworkType(this.getNetworkType()).setMessage("".concat(a)).end(),Promise.resolve(r);var l={groupIDList:[e],responseFilter:{groupBaseInfoFilter:["NextMsgSeq"]}};return this.getGroupProfileAdvance(l).then((function(s){var i=s.data.successGroupList;return Nu(i)?Bi.log("".concat(n," successGroupList is empty. groupID:").concat(e)):(r=i[0].nextMessageSeq-1,a="got lastSequence:".concat(r," from getGroupProfileAdvance. groupID:").concat(e),Bi.log("".concat(n," ").concat(a))),o.setNetworkType(t.getNetworkType()).setMessage("".concat(a)).end(),r})).catch((function(r){return t.probeNetwork().then((function(t){var n=Qn(t,2),a=n[0],s=n[1];o.setError(r,a,s).setMessage("get lastSequence failed from getGroupProfileAdvance. groupID:".concat(e)).end()})),Bi.warn("".concat(n," failed. error:"),r),Mm(r)}))}},{key:"isMessageFromAVChatroom",value:function(e){return!!this._AVChatRoomHandler&&this._AVChatRoomHandler.checkJoinedAVChatRoomByID(e)}},{key:"hasJoinedAVChatRoom",value:function(){return this._AVChatRoomHandler?this._AVChatRoomHandler.hasJoinedAVChatRoom():0}},{key:"getJoinedAVChatRoom",value:function(){return this._AVChatRoomHandler?this._AVChatRoomHandler.getJoinedAVChatRoom():[]}},{key:"isOnlineMessage",value:function(e,t){return!(!this._canIUseOnlineOnlyFlag(e)||!t||!0!==t.onlineUserOnly)}},{key:"_canIUseOnlineOnlyFlag",value:function(e){var t=this.getJoinedAVChatRoom();return!t||!t.includes(e.to)||e.conversationType!==so.CONV_GROUP}},{key:"deleteLocalGroupMembers",value:function(e,t){this.getModule(jc).deleteLocalGroupMembers(e,t)}},{key:"_onAVChatRoomHistoryMessage",value:function(e){if(!Nu(e)){Bi.log("".concat(this._className,"._onAVChatRoomHistoryMessage count:").concat(e.length));var t=[];e.forEach((function(e){t.push(xn(xn({},e),{},{isHistoryMessage:1}))})),this.onAVChatRoomMessage(t)}}},{key:"onAVChatRoomMessage",value:function(e){this._AVChatRoomHandler&&this._AVChatRoomHandler.onMessage(e)}},{key:"getGroupSimplifiedInfo",value:function(e){var t=this,n=new Zp(Xg),o={groupIDList:[e],responseFilter:{groupBaseInfoFilter:["Type","Name"]}};return this.getGroupProfileAdvance(o).then((function(o){var r=o.data.successGroupList;return n.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(e," type:").concat(r[0].type)).end(),r[0]})).catch((function(o){t.probeNetwork().then((function(t){var r=Qn(t,2),a=r[0],s=r[1];n.setError(o,a,s).setMessage("groupID:".concat(e)).end()}))}))}},{key:"setUnjoinedAVChatRoom",value:function(e){this._unjoinedAVChatRoomList.set(e,1)}},{key:"deleteUnjoinedAVChatRoom",value:function(e){this._unjoinedAVChatRoomList.has(e)&&this._unjoinedAVChatRoomList.delete(e)}},{key:"isUnjoinedAVChatRoom",value:function(e){return this._unjoinedAVChatRoomList.has(e)}},{key:"onGroupAttributesUpdated",value:function(e){this._groupAttributesHandler&&this._groupAttributesHandler.onGroupAttributesUpdated(e)}},{key:"updateLocalMainSequenceOnReconnected",value:function(){this._groupAttributesHandler&&this._groupAttributesHandler.updateLocalMainSequenceOnReconnected()}},{key:"initGroupAttributes",value:function(e){return this._groupAttributesHandler.initGroupAttributes(e)}},{key:"setGroupAttributes",value:function(e){return this._groupAttributesHandler.setGroupAttributes(e)}},{key:"deleteGroupAttributes",value:function(e){return this._groupAttributesHandler.deleteGroupAttributes(e)}},{key:"getGroupAttributes",value:function(e){return this._groupAttributesHandler.getGroupAttributes(e)}},{key:"reset",value:function(){this.groupMap.clear(),this._unjoinedAVChatRoomList.clear(),this._commonGroupHandler.reset(),this._groupSystemNoticeHandler.reset(),this._groupTipsHandler.reset(),this._AVChatRoomHandler&&this._AVChatRoomHandler.reset()}}]),n}(cl),lv=function(){function e(t){Gn(this,e),this.userID="",this.avatar="",this.nick="",this.role="",this.joinTime="",this.lastSendMsgTime="",this.nameCard="",this.muteUntil=0,this.memberCustomField=[],this._initMember(t)}return Un(e,[{key:"_initMember",value:function(e){this.updateMember(e)}},{key:"updateMember",value:function(e){var t=[null,void 0,"",0,NaN];e.memberCustomField&&hu(this.memberCustomField,e.memberCustomField),ou(this,e,["memberCustomField"],t)}},{key:"updateRole",value:function(e){["Owner","Admin","Member"].indexOf(e)<0||(this.role=e)}},{key:"updateMuteUntil",value:function(e){Ji(e)||(this.muteUntil=Math.floor((Date.now()+1e3*e)/1e3))}},{key:"updateNameCard",value:function(e){Ji(e)||(this.nameCard=e)}},{key:"updateMemberCustomField",value:function(e){e&&hu(this.memberCustomField,e)}}]),e}(),dv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="GroupMemberModule",o.groupMemberListMap=new Map,o.getInnerEmitterInstance().on(Lm,o._onProfileUpdated,zn(o)),o}return Un(n,[{key:"_onProfileUpdated",value:function(e){for(var t=this,n=e.data,o=function(e){var o=n[e];t.groupMemberListMap.forEach((function(e){e.has(o.userID)&&e.get(o.userID).updateMember({nick:o.nick,avatar:o.avatar})}))},r=0;r<n.length;r++)o(r)}},{key:"deleteGroupMemberList",value:function(e){this.groupMemberListMap.delete(e)}},{key:"getGroupMemberList",value:function(e){var t=this,n=e.groupID,o=e.offset,r=void 0===o?0:o,a=e.count,s=void 0===a?15:a,i="".concat(this._className,".getGroupMemberList"),u=new Zp(nh);Bi.log("".concat(i," groupID:").concat(n," offset:").concat(r," count:").concat(s));var c=[];return this.request({protocolName:ud,requestData:{groupID:n,offset:r,limit:s>100?100:s}}).then((function(e){var o=e.data,r=o.members,a=o.memberNum;if(!zi(r)||0===r.length)return Promise.resolve([]);var s=t.getModule(Kc);return s.hasLocalGroup(n)&&(s.getLocalGroupProfile(n).memberNum=a),c=t._updateLocalGroupMemberMap(n,r),t.getModule(Vc).getUserProfile({userIDList:r.map((function(e){return e.userID})),tagList:[P_.NICK,P_.AVATAR]})})).then((function(e){var o=e.data;if(!zi(o)||0===o.length)return vm({memberList:[]});var a=o.map((function(e){return{userID:e.userID,nick:e.nick,avatar:e.avatar}}));return t._updateLocalGroupMemberMap(n,a),u.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(n," offset:").concat(r," count:").concat(s)).end(),Bi.log("".concat(i," ok.")),cm({memberList:c})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];u.setError(e,o,r).end()})),Bi.error("".concat(i," failed. error:"),e),Mm(e)}))}},{key:"getGroupMemberProfile",value:function(e){var t=this,n="".concat(this._className,".getGroupMemberProfile"),o=new Zp(oh);o.setMessage(e.userIDList.length>5?"userIDList.length:".concat(e.userIDList.length):"userIDList:".concat(e.userIDList)),Bi.log("".concat(n," groupID:").concat(e.groupID," userIDList:").concat(e.userIDList.join(","))),e.userIDList.length>50&&(e.userIDList=e.userIDList.slice(0,50));var r=e.groupID,a=e.userIDList;return this._getGroupMemberProfileAdvance(xn(xn({},e),{},{userIDList:a})).then((function(e){var n=e.data.members;return zi(n)&&0!==n.length?(t._updateLocalGroupMemberMap(r,n),t.getModule(Vc).getUserProfile({userIDList:n.map((function(e){return e.userID})),tagList:[P_.NICK,P_.AVATAR]})):vm([])})).then((function(e){var n=e.data.map((function(e){return{userID:e.userID,nick:e.nick,avatar:e.avatar}}));t._updateLocalGroupMemberMap(r,n);var s=a.filter((function(e){return t.hasLocalGroupMember(r,e)})).map((function(e){return t.getLocalGroupMemberInfo(r,e)}));return o.setNetworkType(t.getNetworkType()).end(),cm({memberList:s})}))}},{key:"addGroupMember",value:function(e){var t=this,n="".concat(this._className,".addGroupMember"),o=e.groupID,r=this.getModule(Kc).getLocalGroupProfile(o),a=r.type,s=new Zp(rh);if(s.setMessage("groupID:".concat(o," groupType:").concat(a)),_u(a)){var i=new fm({code:kd.CANNOT_ADD_MEMBER_IN_AVCHATROOM,message:mp});return s.setCode(kd.CANNOT_ADD_MEMBER_IN_AVCHATROOM).setError(mp).setNetworkType(this.getNetworkType()).end(),Mm(i)}return e.userIDList=e.userIDList.map((function(e){return{userID:e}})),Bi.log("".concat(n," groupID:").concat(o)),this.request({protocolName:ld,requestData:e}).then((function(o){var a=o.data.members;Bi.log("".concat(n," ok"));var i=a.filter((function(e){return 1===e.result})).map((function(e){return e.userID})),u=a.filter((function(e){return 0===e.result})).map((function(e){return e.userID})),c=a.filter((function(e){return 2===e.result})).map((function(e){return e.userID})),l=a.filter((function(e){return 4===e.result})).map((function(e){return e.userID})),d="groupID:".concat(e.groupID,", ")+"successUserIDList:".concat(i,", ")+"failureUserIDList:".concat(u,", ")+"existedUserIDList:".concat(c,", ")+"overLimitUserIDList:".concat(l);return s.setNetworkType(t.getNetworkType()).setMoreMessage(d).end(),0===i.length?cm({successUserIDList:i,failureUserIDList:u,existedUserIDList:c,overLimitUserIDList:l}):(r.memberNum+=i.length,cm({successUserIDList:i,failureUserIDList:u,existedUserIDList:c,overLimitUserIDList:l,group:r}))})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];s.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"deleteGroupMember",value:function(e){var t=this,n="".concat(this._className,".deleteGroupMember"),o=e.groupID,r=e.userIDList,a=new Zp(ah),s="groupID:".concat(o," ").concat(r.length>5?"userIDList.length:".concat(r.length):"userIDList:".concat(r));a.setMessage(s),Bi.log("".concat(n," groupID:").concat(o," userIDList:"),r);var i=this.getModule(Kc).getLocalGroupProfile(o);return _u(i.type)?Mm(new fm({code:kd.CANNOT_KICK_MEMBER_IN_AVCHATROOM,message:Mp})):this.request({protocolName:dd,requestData:e}).then((function(){return a.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),i.memberNum--,t.deleteLocalGroupMembers(o,r),cm({group:i,userIDList:r})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"setGroupMemberMuteTime",value:function(e){var t=this,n=e.groupID,o=e.userID,r=e.muteTime,a="".concat(this._className,".setGroupMemberMuteTime");if(o===this.getMyUserID())return Mm(new fm({code:kd.CANNOT_MUTE_SELF,message:Sp}));Bi.log("".concat(a," groupID:").concat(n," userID:").concat(o));var s=new Zp(sh);return s.setMessage("groupID:".concat(n," userID:").concat(o," muteTime:").concat(r)),this.modifyGroupMemberInfo({groupID:n,userID:o,muteTime:r}).then((function(e){s.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(a," ok"));var o=t.getModule(Kc);return cm({group:o.getLocalGroupProfile(n),member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];s.setError(e,o,r).end()})),Bi.error("".concat(a," failed. error:"),e),Mm(e)}))}},{key:"setGroupMemberRole",value:function(e){var t=this,n="".concat(this._className,".setGroupMemberRole"),o=e.groupID,r=e.userID,a=e.role,s=this.getModule(Kc).getLocalGroupProfile(o);if(s.selfInfo.role!==so.GRP_MBR_ROLE_OWNER)return Mm(new fm({code:kd.NOT_OWNER,message:yp}));if([so.GRP_WORK,so.GRP_AVCHATROOM].includes(s.type))return Mm(new fm({code:kd.CANNOT_SET_MEMBER_ROLE_IN_WORK_AND_AVCHATROOM,message:Ip}));if([so.GRP_MBR_ROLE_ADMIN,so.GRP_MBR_ROLE_MEMBER].indexOf(a)<0)return Mm(new fm({code:kd.INVALID_MEMBER_ROLE,message:Tp}));if(r===this.getMyUserID())return Mm(new fm({code:kd.CANNOT_SET_SELF_MEMBER_ROLE,message:Cp}));var i=new Zp(uh);return i.setMessage("groupID:".concat(o," userID:").concat(r," role:").concat(a)),Bi.log("".concat(n," groupID:").concat(o," userID:").concat(r)),this.modifyGroupMemberInfo({groupID:o,userID:r,role:a}).then((function(e){return i.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok")),cm({group:s,member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];i.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"setGroupMemberNameCard",value:function(e){var t=this,n="".concat(this._className,".setGroupMemberNameCard"),o=e.groupID,r=e.userID,a=void 0===r?this.getMyUserID():r,s=e.nameCard;Bi.log("".concat(n," groupID:").concat(o," userID:").concat(a));var i=new Zp(ih);return i.setMessage("groupID:".concat(o," userID:").concat(a," nameCard:").concat(s)),this.modifyGroupMemberInfo({groupID:o,userID:a,nameCard:s}).then((function(e){Bi.log("".concat(n," ok")),i.setNetworkType(t.getNetworkType()).end();var r=t.getModule(Kc).getLocalGroupProfile(o);return a===t.getMyUserID()&&r&&r.setSelfNameCard(s),cm({group:r,member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];i.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"setGroupMemberCustomField",value:function(e){var t=this,n="".concat(this._className,".setGroupMemberCustomField"),o=e.groupID,r=e.userID,a=void 0===r?this.getMyUserID():r,s=e.memberCustomField;Bi.log("".concat(n," groupID:").concat(o," userID:").concat(a));var i=new Zp(ch);return i.setMessage("groupID:".concat(o," userID:").concat(a," memberCustomField:").concat(JSON.stringify(s))),this.modifyGroupMemberInfo({groupID:o,userID:a,memberCustomField:s}).then((function(e){i.setNetworkType(t.getNetworkType()).end(),Bi.log("".concat(n," ok"));var r=t.getModule(Kc).getLocalGroupProfile(o);return cm({group:r,member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];i.setError(e,o,r).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"modifyGroupMemberInfo",value:function(e){var t=this,n=e.groupID,o=e.userID;return this.request({protocolName:pd,requestData:e}).then((function(){if(t.hasLocalGroupMember(n,o)){var r=t.getLocalGroupMemberInfo(n,o);return Ji(e.muteTime)||r.updateMuteUntil(e.muteTime),Ji(e.role)||r.updateRole(e.role),Ji(e.nameCard)||r.updateNameCard(e.nameCard),Ji(e.memberCustomField)||r.updateMemberCustomField(e.memberCustomField),r}return t.getGroupMemberProfile({groupID:n,userIDList:[o]}).then((function(e){return Qn(e.data.memberList,1)[0]}))}))}},{key:"_getGroupMemberProfileAdvance",value:function(e){return this.request({protocolName:cd,requestData:xn(xn({},e),{},{memberInfoFilter:e.memberInfoFilter?e.memberInfoFilter:["Role","JoinTime","NameCard","ShutUpUntil"]})})}},{key:"_updateLocalGroupMemberMap",value:function(e,t){var n=this;return zi(t)&&0!==t.length?t.map((function(t){return n.hasLocalGroupMember(e,t.userID)?n.getLocalGroupMemberInfo(e,t.userID).updateMember(t):n.setLocalGroupMember(e,new lv(t)),n.getLocalGroupMemberInfo(e,t.userID)})):[]}},{key:"deleteLocalGroupMembers",value:function(e,t){var n=this.groupMemberListMap.get(e);n&&t.forEach((function(e){n.delete(e)}))}},{key:"getLocalGroupMemberInfo",value:function(e,t){return this.groupMemberListMap.has(e)?this.groupMemberListMap.get(e).get(t):null}},{key:"setLocalGroupMember",value:function(e,t){if(this.groupMemberListMap.has(e))this.groupMemberListMap.get(e).set(t.userID,t);else{var n=(new Map).set(t.userID,t);this.groupMemberListMap.set(e,n)}}},{key:"getLocalGroupMemberList",value:function(e){return this.groupMemberListMap.get(e)}},{key:"hasLocalGroupMember",value:function(e,t){return this.groupMemberListMap.has(e)&&this.groupMemberListMap.get(e).has(t)}},{key:"hasLocalGroupMemberMap",value:function(e){return this.groupMemberListMap.has(e)}},{key:"reset",value:function(){this.groupMemberListMap.clear()}}]),n}(cl),pv=function(){function e(t){Gn(this,e),this._userModule=t,this._className="ProfileHandler",this.TAG="profile",this.accountProfileMap=new Map,this.expirationTime=864e5}return Un(e,[{key:"setExpirationTime",value:function(e){this.expirationTime=e}},{key:"getUserProfile",value:function(e){var t=this,n=e.userIDList;e.fromAccount=this._userModule.getMyAccount(),n.length>100&&(Bi.warn("".concat(this._className,".getUserProfile 获取用户资料人数不能超过100人")),n.length=100);for(var o,r=[],a=[],s=0,i=n.length;s<i;s++)o=n[s],this._userModule.isMyFriend(o)&&this._containsAccount(o)?a.push(this._getProfileFromMap(o)):r.push(o);if(0===r.length)return vm(a);e.toAccount=r;var u=e.bFromGetMyProfile||!1,c=[];e.toAccount.forEach((function(e){c.push({toAccount:e,standardSequence:0,customSequence:0})})),e.userItem=c;var l=new Zp(hh);return l.setMessage(n.length>5?"userIDList.length:".concat(n.length):"userIDList:".concat(n)),this._userModule.request({protocolName:vl,requestData:e}).then((function(e){l.setNetworkType(t._userModule.getNetworkType()).end(),Bi.info("".concat(t._className,".getUserProfile ok"));var n=t._handleResponse(e).concat(a);return cm(u?n[0]:n)})).catch((function(e){return t._userModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];l.setError(e,o,r).end()})),Bi.error("".concat(t._className,".getUserProfile failed. error:"),e),Mm(e)}))}},{key:"getMyProfile",value:function(){var e=this._userModule.getMyAccount();if(Bi.log("".concat(this._className,".getMyProfile myAccount:").concat(e)),this._fillMap(),this._containsAccount(e)){var t=this._getProfileFromMap(e);return Bi.debug("".concat(this._className,".getMyProfile from cache, myProfile:")+JSON.stringify(t)),vm(t)}return this.getUserProfile({fromAccount:e,userIDList:[e],bFromGetMyProfile:!0})}},{key:"_handleResponse",value:function(e){for(var t,n,o=nu.now(),r=e.data.userProfileItem,a=[],s=0,i=r.length;s<i;s++)"@TLS#NOT_FOUND"!==r[s].to&&""!==r[s].to&&(t=r[s].to,n=this._updateMap(t,this._getLatestProfileFromResponse(t,r[s].profileItem)),a.push(n));return Bi.log("".concat(this._className,"._handleResponse cost ").concat(nu.now()-o," ms")),a}},{key:"_getLatestProfileFromResponse",value:function(e,t){var n={};if(n.userID=e,n.profileCustomField=[],!Nu(t))for(var o=0,r=t.length;o<r;o++)if(t[o].tag.indexOf("Tag_Profile_Custom")>-1)n.profileCustomField.push({key:t[o].tag,value:t[o].value});else switch(t[o].tag){case P_.NICK:n.nick=t[o].value;break;case P_.GENDER:n.gender=t[o].value;break;case P_.BIRTHDAY:n.birthday=t[o].value;break;case P_.LOCATION:n.location=t[o].value;break;case P_.SELFSIGNATURE:n.selfSignature=t[o].value;break;case P_.ALLOWTYPE:n.allowType=t[o].value;break;case P_.LANGUAGE:n.language=t[o].value;break;case P_.AVATAR:n.avatar=t[o].value;break;case P_.MESSAGESETTINGS:n.messageSettings=t[o].value;break;case P_.ADMINFORBIDTYPE:n.adminForbidType=t[o].value;break;case P_.LEVEL:n.level=t[o].value;break;case P_.ROLE:n.role=t[o].value;break;default:Bi.warn("".concat(this._className,"._handleResponse unknown tag:"),t[o].tag,t[o].value)}return n}},{key:"updateMyProfile",value:function(e){var t=this,n="".concat(this._className,".updateMyProfile"),o=new Zp(fh);o.setMessage(JSON.stringify(e));var r=(new xm).validate(e);if(!r.valid)return o.setCode(kd.UPDATE_PROFILE_INVALID_PARAM).setMoreMessage("".concat(n," info:").concat(r.tips)).setNetworkType(this._userModule.getNetworkType()).end(),Bi.error("".concat(n," info:").concat(r.tips,"，请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#updateMyProfile")),Mm({code:kd.UPDATE_PROFILE_INVALID_PARAM,message:Ap});var a=[];for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&("profileCustomField"===s?e.profileCustomField.forEach((function(e){a.push({tag:e.key,value:e.value})})):a.push({tag:P_[s.toUpperCase()],value:e[s]}));return 0===a.length?(o.setCode(kd.UPDATE_PROFILE_NO_KEY).setMoreMessage(Ep).setNetworkType(this._userModule.getNetworkType()).end(),Bi.error("".concat(n," info:").concat(Ep,"，请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#updateMyProfile")),Mm({code:kd.UPDATE_PROFILE_NO_KEY,message:Ep})):this._userModule.request({protocolName:Ml,requestData:{fromAccount:this._userModule.getMyAccount(),profileItem:a}}).then((function(r){o.setNetworkType(t._userModule.getNetworkType()).end(),Bi.info("".concat(n," ok"));var a=t._updateMap(t._userModule.getMyAccount(),e);return t._userModule.emitOuterEvent(ao.PROFILE_UPDATED,[a]),vm(a)})).catch((function(e){return t._userModule.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))}},{key:"onProfileModified",value:function(e){var t=e.dataList;if(!Nu(t)){var n,o,r=t.length;Bi.debug("".concat(this._className,".onProfileModified count:").concat(r," dataList:"),e.dataList);for(var a=[],s=0;s<r;s++)n=t[s].userID,o=this._updateMap(n,this._getLatestProfileFromResponse(n,t[s].profileList)),a.push(o);a.length>0&&(this._userModule.emitInnerEvent(Lm,a),this._userModule.emitOuterEvent(ao.PROFILE_UPDATED,a))}}},{key:"_fillMap",value:function(){if(0===this.accountProfileMap.size){for(var e=this._getCachedProfiles(),t=Date.now(),n=0,o=e.length;n<o;n++)t-e[n].lastUpdatedTime<this.expirationTime&&this.accountProfileMap.set(e[n].userID,e[n]);Bi.log("".concat(this._className,"._fillMap from cache, map.size:").concat(this.accountProfileMap.size))}}},{key:"_updateMap",value:function(e,t){var n,o=Date.now();return this._containsAccount(e)?(n=this._getProfileFromMap(e),t.profileCustomField&&hu(n.profileCustomField,t.profileCustomField),ou(n,t,["profileCustomField"]),n.lastUpdatedTime=o):(n=new xm(t),(this._userModule.isMyFriend(e)||e===this._userModule.getMyAccount())&&(n.lastUpdatedTime=o,this.accountProfileMap.set(e,n))),this._flushMap(e===this._userModule.getMyAccount()),n}},{key:"_flushMap",value:function(e){var t=$n(this.accountProfileMap.values()),n=this._userModule.getStorageModule();Bi.debug("".concat(this._className,"._flushMap length:").concat(t.length," flushAtOnce:").concat(e)),n.setItem(this.TAG,t,e)}},{key:"_containsAccount",value:function(e){return this.accountProfileMap.has(e)}},{key:"_getProfileFromMap",value:function(e){return this.accountProfileMap.get(e)}},{key:"_getCachedProfiles",value:function(){var e=this._userModule.getStorageModule().getItem(this.TAG);return Nu(e)?[]:e}},{key:"onConversationsProfileUpdated",value:function(e){for(var t,n,o,r=[],a=0,s=e.length;a<s;a++)n=(t=e[a]).userID,this._userModule.isMyFriend(n)||(this._containsAccount(n)?(o=this._getProfileFromMap(n),ou(o,t)>0&&r.push(n)):r.push(t.userID));0!==r.length&&(Bi.info("".concat(this._className,".onConversationsProfileUpdated toAccountList:").concat(r)),this.getUserProfile({userIDList:r}))}},{key:"getNickAndAvatarByUserID",value:function(e){if(this._containsAccount(e)){var t=this._getProfileFromMap(e);return{nick:t.nick,avatar:t.avatar}}return{nick:"",avatar:""}}},{key:"reset",value:function(){this._flushMap(!0),this.accountProfileMap.clear()}}]),e}(),gv=function e(t){Gn(this,e),Nu||(this.userID=t.userID||"",this.timeStamp=t.timeStamp||0)},hv=function(){function e(t){Gn(this,e),this._userModule=t,this._className="BlacklistHandler",this._blacklistMap=new Map,this.startIndex=0,this.maxLimited=100,this.currentSequence=0}return Un(e,[{key:"getLocalBlacklist",value:function(){return $n(this._blacklistMap.keys())}},{key:"getBlacklist",value:function(){var e=this,t="".concat(this._className,".getBlacklist"),n={fromAccount:this._userModule.getMyAccount(),maxLimited:this.maxLimited,startIndex:0,lastSequence:this.currentSequence},o=new Zp(_h);return this._userModule.request({protocolName:yl,requestData:n}).then((function(n){var r=n.data,a=r.blackListItem,s=r.currentSequence,i=Nu(a)?0:a.length;o.setNetworkType(e._userModule.getNetworkType()).setMessage("blackList count:".concat(i)).end(),Bi.info("".concat(t," ok")),e.currentSequence=s,e._handleResponse(a,!0),e._userModule.emitOuterEvent(ao.BLACKLIST_UPDATED,$n(e._blacklistMap.keys()))})).catch((function(n){return e._userModule.probeNetwork().then((function(e){var t=Qn(e,2),r=t[0],a=t[1];o.setError(n,r,a).end()})),Bi.error("".concat(t," failed. error:"),n),Mm(n)}))}},{key:"addBlacklist",value:function(e){var t=this,n="".concat(this._className,".addBlacklist"),o=new Zp(mh);if(!zi(e.userIDList))return o.setCode(kd.ADD_BLACKLIST_INVALID_PARAM).setMessage(kp).setNetworkType(this._userModule.getNetworkType()).end(),Bi.error("".concat(n," options.userIDList 必需是数组")),Mm({code:kd.ADD_BLACKLIST_INVALID_PARAM,message:kp});var r=this._userModule.getMyAccount();return 1===e.userIDList.length&&e.userIDList[0]===r?(o.setCode(kd.CANNOT_ADD_SELF_TO_BLACKLIST).setMessage(Np).setNetworkType(this._userModule.getNetworkType()).end(),Bi.error("".concat(n," 不能把自己拉黑")),Mm({code:kd.CANNOT_ADD_SELF_TO_BLACKLIST,message:Np})):(e.userIDList.includes(r)&&(e.userIDList=e.userIDList.filter((function(e){return e!==r})),Bi.warn("".concat(n," 不能把自己拉黑，已过滤"))),e.fromAccount=this._userModule.getMyAccount(),e.toAccount=e.userIDList,this._userModule.request({protocolName:Il,requestData:e}).then((function(r){return o.setNetworkType(t._userModule.getNetworkType()).setMessage(e.userIDList.length>5?"userIDList.length:".concat(e.userIDList.length):"userIDList:".concat(e.userIDList)).end(),Bi.info("".concat(n," ok")),t._handleResponse(r.resultItem,!0),cm($n(t._blacklistMap.keys()))})).catch((function(e){return t._userModule.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)})))}},{key:"_handleResponse",value:function(e,t){if(!Nu(e))for(var n,o,r,a=0,s=e.length;a<s;a++)o=e[a].to,r=e[a].resultCode,(Ji(r)||0===r)&&(t?((n=this._blacklistMap.has(o)?this._blacklistMap.get(o):new gv).userID=o,!Nu(e[a].addBlackTimeStamp)&&(n.timeStamp=e[a].addBlackTimeStamp),this._blacklistMap.set(o,n)):this._blacklistMap.has(o)&&(n=this._blacklistMap.get(o),this._blacklistMap.delete(o)));Bi.log("".concat(this._className,"._handleResponse total:").concat(this._blacklistMap.size," bAdd:").concat(t))}},{key:"deleteBlacklist",value:function(e){var t=this,n="".concat(this._className,".deleteBlacklist"),o=new Zp(vh);return zi(e.userIDList)?(e.fromAccount=this._userModule.getMyAccount(),e.toAccount=e.userIDList,this._userModule.request({protocolName:Tl,requestData:e}).then((function(r){return o.setNetworkType(t._userModule.getNetworkType()).setMessage(e.userIDList.length>5?"userIDList.length:".concat(e.userIDList.length):"userIDList:".concat(e.userIDList)).end(),Bi.info("".concat(n," ok")),t._handleResponse(r.data.resultItem,!1),cm($n(t._blacklistMap.keys()))})).catch((function(e){return t._userModule.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),Bi.error("".concat(n," failed. error:"),e),Mm(e)}))):(o.setCode(kd.DEL_BLACKLIST_INVALID_PARAM).setMessage(Dp).setNetworkType(this._userModule.getNetworkType()).end(),Bi.error("".concat(n," options.userIDList 必需是数组")),Mm({code:kd.DEL_BLACKLIST_INVALID_PARAM,message:Dp}))}},{key:"onAccountDeleted",value:function(e){for(var t,n=[],o=0,r=e.length;o<r;o++)t=e[o],this._blacklistMap.has(t)&&(this._blacklistMap.delete(t),n.push(t));n.length>0&&(Bi.log("".concat(this._className,".onAccountDeleted count:").concat(n.length," userIDList:"),n),this._userModule.emitOuterEvent(ao.BLACKLIST_UPDATED,$n(this._blacklistMap.keys())))}},{key:"onAccountAdded",value:function(e){for(var t,n=[],o=0,r=e.length;o<r;o++)t=e[o],this._blacklistMap.has(t)||(this._blacklistMap.set(t,new gv({userID:t})),n.push(t));n.length>0&&(Bi.log("".concat(this._className,".onAccountAdded count:").concat(n.length," userIDList:"),n),this._userModule.emitOuterEvent(ao.BLACKLIST_UPDATED,$n(this._blacklistMap.keys())))}},{key:"reset",value:function(){this._blacklistMap.clear(),this.startIndex=0,this.maxLimited=100,this.currentSequence=0}}]),e}(),fv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="UserModule",o._profileHandler=new pv(zn(o)),o._blacklistHandler=new hv(zn(o)),o.getInnerEmitterInstance().on(Om,o.onContextUpdated,zn(o)),o}return Un(n,[{key:"onContextUpdated",value:function(e){this._profileHandler.getMyProfile(),this._blacklistHandler.getBlacklist()}},{key:"onProfileModified",value:function(e){this._profileHandler.onProfileModified(e)}},{key:"onRelationChainModified",value:function(e){var t=e.dataList;if(!Nu(t)){var n=[];t.forEach((function(e){e.blackListDelAccount&&n.push.apply(n,$n(e.blackListDelAccount))})),n.length>0&&this._blacklistHandler.onAccountDeleted(n);var o=[];t.forEach((function(e){e.blackListAddAccount&&o.push.apply(o,$n(e.blackListAddAccount))})),o.length>0&&this._blacklistHandler.onAccountAdded(o)}}},{key:"onConversationsProfileUpdated",value:function(e){this._profileHandler.onConversationsProfileUpdated(e)}},{key:"getMyAccount",value:function(){return this.getMyUserID()}},{key:"getMyProfile",value:function(){return this._profileHandler.getMyProfile()}},{key:"getStorageModule",value:function(){return this.getModule(zc)}},{key:"isMyFriend",value:function(e){var t=this.getModule(Hc);return!!t&&t.isMyFriend(e)}},{key:"getUserProfile",value:function(e){return this._profileHandler.getUserProfile(e)}},{key:"updateMyProfile",value:function(e){return this._profileHandler.updateMyProfile(e)}},{key:"getNickAndAvatarByUserID",value:function(e){return this._profileHandler.getNickAndAvatarByUserID(e)}},{key:"getLocalBlacklist",value:function(){var e=this._blacklistHandler.getLocalBlacklist();return vm(e)}},{key:"addBlacklist",value:function(e){return this._blacklistHandler.addBlacklist(e)}},{key:"deleteBlacklist",value:function(e){return this._blacklistHandler.deleteBlacklist(e)}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._profileHandler.reset(),this._blacklistHandler.reset()}}]),n}(cl),_v=function(){function e(t,n){Gn(this,e),this._moduleManager=t,this._isLoggedIn=!1,this._SDKAppID=n.SDKAppID,this._userID=n.userID||"",this._userSig=n.userSig||"",this._version="2.16.1",this._a2Key="",this._tinyID="",this._contentType="json",this._unlimitedAVChatRoom=n.unlimitedAVChatRoom,this._scene=n.scene||"",this._oversea=n.oversea,this._instanceID=n.instanceID,this._statusInstanceID=0,this._isDevMode=n.devMode}return Un(e,[{key:"isLoggedIn",value:function(){return this._isLoggedIn}},{key:"isOversea",value:function(){return this._oversea}},{key:"isDevMode",value:function(){return this._isDevMode}},{key:"isUnlimitedAVChatRoom",value:function(){return this._unlimitedAVChatRoom}},{key:"getUserID",value:function(){return this._userID}},{key:"setUserID",value:function(e){this._userID=e}},{key:"setUserSig",value:function(e){this._userSig=e}},{key:"getUserSig",value:function(){return this._userSig}},{key:"getSDKAppID",value:function(){return this._SDKAppID}},{key:"getTinyID",value:function(){return this._tinyID}},{key:"setTinyID",value:function(e){this._tinyID=e,this._isLoggedIn=!0}},{key:"getScene",value:function(){return this._isTUIKit()?"tuikit":this._scene}},{key:"getInstanceID",value:function(){return this._instanceID}},{key:"getStatusInstanceID",value:function(){return this._statusInstanceID}},{key:"setStatusInstanceID",value:function(e){this._statusInstanceID=e}},{key:"getVersion",value:function(){return this._version}},{key:"getA2Key",value:function(){return this._a2Key}},{key:"setA2Key",value:function(e){this._a2Key=e}},{key:"getContentType",value:function(){return this._contentType}},{key:"_isTUIKit",value:function(){var e=!1,t=!1,n=!1,o=!1,r=[];si&&(r=Object.keys(ui)),ii&&(r=Object.keys(window));for(var a=0,s=r.length;a<s;a++)if(r[a].toLowerCase().includes("uikit")){e=!0;break}if(r=null,si&&Qi(getApp)){var i=getApp().globalData;Yi(i)&&!0===i.isTUIKit&&(t=!0)}!0===this._moduleManager.getModule(zc).getStorageSync("TIM_".concat(this._SDKAppID,"_isTUIKit"))&&(n=!0);var u=null;if(ei&&"undefined"==typeof uni&&__wxConfig&&(u=__wxConfig.pages),ti&&"undefined"==typeof uni&&__qqConfig&&(u=__qqConfig.pages),zi(u)&&u.length>0){for(var c=0,l=u.length;c<l;c++)if(u[c].toLowerCase().includes("tui")){o=!0;break}u=null}return e||t||n||o}},{key:"reset",value:function(){this._isLoggedIn=!1,this._userSig="",this._a2Key="",this._tinyID="",this._statusInstanceID=0}}]),e}(),mv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="SignModule",o._helloInterval=120,o._lastLoginTs=0,o._lastWsHelloTs=0,bm.mixin(zn(o)),o}return Un(n,[{key:"onCheckTimer",value:function(e){this.isLoggedIn()&&e%this._helloInterval==0&&this._hello()}},{key:"login",value:function(e){if(this.isLoggedIn()){var t="您已经登录账号".concat(e.userID,"！如需切换账号登录，请先调用 logout 接口登出，再调用 login 接口登录。");return Bi.warn(t),vm({actionStatus:"OK",errorCode:0,errorInfo:t,repeatLogin:!0})}if(Date.now()-this._lastLoginTs<=15e3)return Bi.warn("您正在尝试登录账号".concat(e.userID,"！请勿重复登录。")),Mm({code:kd.REPEAT_LOGIN,message:wd});Bi.log("".concat(this._className,".login userID:").concat(e.userID));var n=this._checkLoginInfo(e);if(0!==n.code)return Mm(n);var o=this.getModule(Yc),r=e.userID,a=e.userSig;return o.setUserID(r),o.setUserSig(a),this.getModule(tl).updateProtocolConfig(),this._login()}},{key:"_login",value:function(){var e=this,t=this.getModule(Yc),n=t.getScene(),o=new Zp(ng);return o.setMessage("".concat(n)).setMoreMessage("identifier:".concat(this.getMyUserID())),this._lastLoginTs=Date.now(),this.request({protocolName:ll}).then((function(r){e._lastLoginTs=0;var a=Date.now(),s=null,i=r.data,u=i.a2Key,c=i.tinyID,l=i.helloInterval,d=i.instanceID,p=i.timeStamp;Bi.log("".concat(e._className,".login ok. scene:").concat(n," helloInterval:").concat(l," instanceID:").concat(d," timeStamp:").concat(p));var g=1e3*p,h=a-o.getStartTs(),f=g+parseInt(h/2)-a,_=o.getStartTs()+f;if(o.start(_),function(e,t){Pi=t;var n=new Date;n.setTime(e),Bi.info("baseTime from server: ".concat(n," offset: ").concat(Pi))}(g,f),!c)throw s=new fm({code:kd.NO_TINYID,message:Rd}),o.setError(s,!0,e.getNetworkType()).end(),s;if(!u)throw s=new fm({code:kd.NO_A2KEY,message:Ld}),o.setError(s,!0,e.getNetworkType()).end(),s;return o.setNetworkType(e.getNetworkType()).setMoreMessage("helloInterval:".concat(l," instanceID:").concat(d," offset:").concat(f)).end(),t.setA2Key(u),t.setTinyID(c),t.setStatusInstanceID(d),e.getModule(tl).updateProtocolConfig(),e.emitInnerEvent(Om),e._helloInterval=l,e.triggerReady(),e._fetchCloudControlConfig(),r})).catch((function(t){return e.probeNetwork().then((function(e){var n=Qn(e,2),r=n[0],a=n[1];o.setError(t,r,a).end(!0)})),Bi.error("".concat(e._className,".login failed. error:"),t),e._moduleManager.onLoginFailed(),Mm(t)}))}},{key:"logout",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(!this.isLoggedIn())return Mm({code:kd.USER_NOT_LOGGED_IN,message:bd});var n=new Zp(og);return n.setNetworkType(this.getNetworkType()).setMessage("identifier:".concat(this.getMyUserID())).end(!0),Bi.info("".concat(this._className,".logout type:").concat(t)),this.request({protocolName:dl,requestData:{type:t}}).then((function(){return e.resetReady(),vm({})})).catch((function(t){return Bi.error("".concat(e._className,"._logout error:"),t),e.resetReady(),vm({})}))}},{key:"_fetchCloudControlConfig",value:function(){this.getModule(rl).fetchConfig()}},{key:"_hello",value:function(){var e=this;this._lastWsHelloTs=Date.now(),this.request({protocolName:pl}).catch((function(t){Bi.warn("".concat(e._className,"._hello error:"),t)}))}},{key:"getLastWsHelloTs",value:function(){return this._lastWsHelloTs}},{key:"_checkLoginInfo",value:function(e){var t=0,n="";return Nu(this.getModule(Yc).getSDKAppID())?(t=kd.NO_SDKAPPID,n=Dd):Nu(e.userID)?(t=kd.NO_IDENTIFIER,n=Nd):Nu(e.userSig)&&(t=kd.NO_USERSIG,n=Od),{code:t,message:n}}},{key:"onMultipleAccountKickedOut",value:function(e){var t=this;new Zp(rg).setNetworkType(this.getNetworkType()).setMessage("type:".concat(so.KICKED_OUT_MULT_ACCOUNT," newInstanceInfo:").concat(JSON.stringify(e))).end(!0),Bi.warn("".concat(this._className,".onMultipleAccountKickedOut userID:").concat(this.getMyUserID()," newInstanceInfo:"),e),this.logout(1).then((function(){t.emitOuterEvent(ao.KICKED_OUT,{type:so.KICKED_OUT_MULT_ACCOUNT}),t._moduleManager.reset()}))}},{key:"onMultipleDeviceKickedOut",value:function(e){var t=this;new Zp(rg).setNetworkType(this.getNetworkType()).setMessage("type:".concat(so.KICKED_OUT_MULT_DEVICE," newInstanceInfo:").concat(JSON.stringify(e))).end(!0),Bi.warn("".concat(this._className,".onMultipleDeviceKickedOut userID:").concat(this.getMyUserID()," newInstanceInfo:"),e),this.logout(1).then((function(){t.emitOuterEvent(ao.KICKED_OUT,{type:so.KICKED_OUT_MULT_DEVICE}),t._moduleManager.reset()}))}},{key:"onUserSigExpired",value:function(){new Zp(rg).setNetworkType(this.getNetworkType()).setMessage(so.KICKED_OUT_USERSIG_EXPIRED).end(!0),Bi.warn("".concat(this._className,".onUserSigExpired: userSig 签名过期被踢下线")),0!==this.getModule(Yc).getStatusInstanceID()&&(this.emitOuterEvent(ao.KICKED_OUT,{type:so.KICKED_OUT_USERSIG_EXPIRED}),this._moduleManager.reset())}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this.resetReady(),this._helloInterval=120,this._lastLoginTs=0,this._lastWsHelloTs=0}}]),n}(cl);function vv(){return null}var Mv=function(){function e(t){Gn(this,e),this._moduleManager=t,this._className="StorageModule",this._storageQueue=new Map,this._errorTolerantHandle()}return Un(e,[{key:"_errorTolerantHandle",value:function(){si||!Ji(window)&&!Ji(window.localStorage)||(this.getItem=vv,this.setItem=vv,this.removeItem=vv,this.clear=vv)}},{key:"onCheckTimer",value:function(e){if(e%20==0){if(0===this._storageQueue.size)return;this._doFlush()}}},{key:"_doFlush",value:function(){try{var e,t=ro(this._storageQueue);try{for(t.s();!(e=t.n()).done;){var n=Qn(e.value,2),o=n[0],r=n[1];this._setStorageSync(this._getKey(o),r)}}catch(i){t.e(i)}finally{t.f()}this._storageQueue.clear()}catch(YI){Bi.warn("".concat(this._className,"._doFlush error:"),YI)}}},{key:"_getPrefix",value:function(){var e=this._moduleManager.getModule(Yc);return"TIM_".concat(e.getSDKAppID(),"_").concat(e.getUserID(),"_")}},{key:"_getKey",value:function(e){return"".concat(this._getPrefix()).concat(e)}},{key:"getItem",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];try{var n=t?this._getKey(e):e;return this.getStorageSync(n)}catch(YI){return Bi.warn("".concat(this._className,".getItem error:"),YI),{}}}},{key:"setItem",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(n){var r=o?this._getKey(e):e;this._setStorageSync(r,t)}else this._storageQueue.set(e,t)}},{key:"clear",value:function(){try{si?ui.clearStorageSync():localStorage&&localStorage.clear()}catch(YI){Bi.warn("".concat(this._className,".clear error:"),YI)}}},{key:"removeItem",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];try{var n=t?this._getKey(e):e;this._removeStorageSync(n)}catch(YI){Bi.warn("".concat(this._className,".removeItem error:"),YI)}}},{key:"getSize",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"b";try{var o={size:0,limitSize:5242880,unit:n};if(Object.defineProperty(o,"leftSize",{enumerable:!0,get:function(){return o.limitSize-o.size}}),si&&(o.limitSize=1024*ui.getStorageInfoSync().limitSize),e)o.size=JSON.stringify(this.getItem(e)).length+this._getKey(e).length;else if(si){var r=ui.getStorageInfoSync(),a=r.keys;a.forEach((function(e){o.size+=JSON.stringify(t.getStorageSync(e)).length+t._getKey(e).length}))}else if(localStorage)for(var s in localStorage)localStorage.hasOwnProperty(s)&&(o.size+=localStorage.getItem(s).length+s.length);return this._convertUnit(o)}catch(YI){Bi.warn("".concat(this._className," error:"),YI)}}},{key:"_convertUnit",value:function(e){var t={},n=e.unit;for(var o in t.unit=n,e)"number"==typeof e[o]&&("kb"===n.toLowerCase()?t[o]=Math.round(e[o]/1024):"mb"===n.toLowerCase()?t[o]=Math.round(e[o]/1024/1024):t[o]=e[o]);return t}},{key:"_setStorageSync",value:function(e,t){si?ri?my.setStorageSync({key:e,data:t}):ui.setStorageSync(e,t):localStorage&&localStorage.setItem(e,JSON.stringify(t))}},{key:"getStorageSync",value:function(e){return si?ri?my.getStorageSync({key:e}).data:ui.getStorageSync(e):localStorage?JSON.parse(localStorage.getItem(e)):{}}},{key:"_removeStorageSync",value:function(e){si?ri?my.removeStorageSync({key:e}):ui.removeStorageSync(e):localStorage&&localStorage.removeItem(e)}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._doFlush()}}]),e}(),yv=function(){function e(t){Gn(this,e),this._className="SSOLogBody",this._report=[]}return Un(e,[{key:"pushIn",value:function(e){Bi.debug("".concat(this._className,".pushIn"),this._report.length,e),this._report.push(e)}},{key:"backfill",value:function(e){var t;zi(e)&&0!==e.length&&(Bi.debug("".concat(this._className,".backfill"),this._report.length,e.length),(t=this._report).unshift.apply(t,$n(e)))}},{key:"getLogsNumInMemory",value:function(){return this._report.length}},{key:"isEmpty",value:function(){return 0===this._report.length}},{key:"_reset",value:function(){this._report.length=0,this._report=[]}},{key:"getLogsInMemory",value:function(){var e=this._report.slice();return this._reset(),e}}]),e}(),Iv=function(e){var t=e.getModule(Yc);return{SDKType:10,SDKAppID:t.getSDKAppID(),SDKVersion:t.getVersion(),tinyID:Number(t.getTinyID()),userID:t.getUserID(),platform:e.getPlatform(),instanceID:t.getInstanceID(),traceID:Ui()}},Tv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;Gn(this,n),(o=t.call(this,e))._className="EventStatModule",o.TAG="im-ssolog-event",o._reportBody=new yv,o.MIN_THRESHOLD=20,o.MAX_THRESHOLD=100,o.WAITING_TIME=6e4,o.REPORT_LEVEL=[4,5,6],o.REPORT_SDKAPPID_BLACKLIST=[],o.REPORT_TINYID_WHITELIST=[],o._lastReportTime=Date.now();var r=o.getInnerEmitterInstance();return r.on(Om,o._onLoginSuccess,zn(o)),r.on(Rm,o._onCloudConfigUpdated,zn(o)),o}return Un(n,[{key:"reportAtOnce",value:function(){Bi.debug("".concat(this._className,".reportAtOnce")),this._report()}},{key:"_onLoginSuccess",value:function(){var e=this,t=this.getModule(zc),n=t.getItem(this.TAG,!1);!Nu(n)&&Qi(n.forEach)&&(Bi.log("".concat(this._className,"._onLoginSuccess get ssolog in storage, count:").concat(n.length)),n.forEach((function(t){e._reportBody.pushIn(t)})),t.removeItem(this.TAG,!1))}},{key:"_onCloudConfigUpdated",value:function(){var e=this.getCloudConfig("evt_rpt_threshold"),t=this.getCloudConfig("evt_rpt_waiting"),n=this.getCloudConfig("evt_rpt_level"),o=this.getCloudConfig("evt_rpt_sdkappid_bl"),r=this.getCloudConfig("evt_rpt_tinyid_wl");Ji(e)||(this.MIN_THRESHOLD=Number(e)),Ji(t)||(this.WAITING_TIME=Number(t)),Ji(n)||(this.REPORT_LEVEL=n.split(",").map((function(e){return Number(e)}))),Ji(o)||(this.REPORT_SDKAPPID_BLACKLIST=o.split(",").map((function(e){return Number(e)}))),Ji(r)||(this.REPORT_TINYID_WHITELIST=r.split(","))}},{key:"pushIn",value:function(e){e instanceof Zp&&(e.updateTimeStamp(),this._reportBody.pushIn(e),this._reportBody.getLogsNumInMemory()>=this.MIN_THRESHOLD&&this._report())}},{key:"onCheckTimer",value:function(){Date.now()<this._lastReportTime+this.WAITING_TIME||this._reportBody.isEmpty()||this._report()}},{key:"_filterLogs",value:function(e){var t=this,n=this.getModule(Yc),o=n.getSDKAppID(),r=n.getTinyID();return Eu(this.REPORT_SDKAPPID_BLACKLIST,o)&&!ku(this.REPORT_TINYID_WHITELIST,r)?[]:e.filter((function(e){return t.REPORT_LEVEL.includes(e.level)}))}},{key:"_report",value:function(){var e=this;if(!this._reportBody.isEmpty()){var t=this._reportBody.getLogsInMemory(),n=this._filterLogs(t);if(0!==n.length){var o={header:Iv(this),event:n};this.request({protocolName:fd,requestData:xn({},o)}).then((function(){e._lastReportTime=Date.now()})).catch((function(n){Bi.warn("".concat(e._className,".report failed. networkType:").concat(e.getNetworkType()," error:"),n),e._reportBody.backfill(t),e._reportBody.getLogsNumInMemory()>e.MAX_THRESHOLD&&e._flushAtOnce()}))}else this._lastReportTime=Date.now()}}},{key:"_flushAtOnce",value:function(){var e=this.getModule(zc),t=e.getItem(this.TAG,!1),n=this._reportBody.getLogsInMemory();if(Nu(t))Bi.log("".concat(this._className,"._flushAtOnce count:").concat(n.length)),e.setItem(this.TAG,n,!0,!1);else{var o=n.concat(t);o.length>this.MAX_THRESHOLD&&(o=o.slice(0,this.MAX_THRESHOLD)),Bi.log("".concat(this._className,"._flushAtOnce count:").concat(o.length)),e.setItem(this.TAG,o,!0,!1)}}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._lastReportTime=0,this._report(),this.REPORT_SDKAPPID_BLACKLIST=[],this.REPORT_TINYID_WHITELIST=[]}}]),n}(cl),Cv="none",Sv="online",Av=function(){function e(t){Gn(this,e),this._moduleManager=t,this._networkType="",this._className="NetMonitorModule",this.MAX_WAIT_TIME=3e3}return Un(e,[{key:"start",value:function(){var e=this;if(si){ui.getNetworkType({success:function(t){e._networkType=t.networkType,t.networkType===Cv?Bi.warn("".concat(e._className,".start no network, please check!")):Bi.info("".concat(e._className,".start networkType:").concat(t.networkType))}});var t=this._onNetworkStatusChange.bind(this);ui.offNetworkStatusChange&&(ai||ni?ui.offNetworkStatusChange(t):ui.offNetworkStatusChange()),ui.onNetworkStatusChange(t)}else this._networkType=Sv}},{key:"_onNetworkStatusChange",value:function(e){e.isConnected?(Bi.info("".concat(this._className,"._onNetworkStatusChange previousNetworkType:").concat(this._networkType," currentNetworkType:").concat(e.networkType)),this._networkType!==e.networkType&&this._moduleManager.getModule(nl).reConnect()):Bi.warn("".concat(this._className,"._onNetworkStatusChange no network, please check!")),this._networkType=e.networkType}},{key:"probe",value:function(){var e=this;return new Promise((function(t,n){if(si)ui.getNetworkType({success:function(n){e._networkType=n.networkType,n.networkType===Cv?(Bi.warn("".concat(e._className,".probe no network, please check!")),t([!1,n.networkType])):(Bi.info("".concat(e._className,".probe networkType:").concat(n.networkType)),t([!0,n.networkType]))}});else if(window&&window.fetch)fetch("".concat(du(),"//web.sdk.qcloud.com/im/assets/speed.xml?random=").concat(Math.random())).then((function(e){e.ok?t([!0,Sv]):t([!1,Cv])})).catch((function(e){t([!1,Cv])}));else{var o=new XMLHttpRequest,r=setTimeout((function(){Bi.warn("".concat(e._className,".probe fetch timeout. Probably no network, please check!")),o.abort(),e._networkType=Cv,t([!1,Cv])}),e.MAX_WAIT_TIME);o.onreadystatechange=function(){4===o.readyState&&(clearTimeout(r),200===o.status||304===o.status?(this._networkType=Sv,t([!0,Sv])):(Bi.warn("".concat(this.className,".probe fetch status:").concat(o.status,". Probably no network, please check!")),this._networkType=Cv,t([!1,Cv])))},o.open("GET","".concat(du(),"//web.sdk.qcloud.com/im/assets/speed.xml?random=").concat(Math.random())),o.send()}}))}},{key:"getNetworkType",value:function(){return this._networkType}}]),e}(),Ev=t((function(e){var t=Object.prototype.hasOwnProperty,n="~";function o(){}function r(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function a(e,t,o,a,s){if("function"!=typeof o)throw new TypeError("The listener must be a function");var i=new r(o,a||e,s),u=n?n+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],i]:e._events[u].push(i):(e._events[u]=i,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new o:delete e._events[t]}function i(){this._events=new o,this._eventsCount=0}Object.create&&(o.prototype=Object.create(null),(new o).__proto__||(n=!1)),i.prototype.eventNames=function(){var e,o,r=[];if(0===this._eventsCount)return r;for(o in e=this._events)t.call(e,o)&&r.push(n?o.slice(1):o);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(e)):r},i.prototype.listeners=function(e){var t=n?n+e:e,o=this._events[t];if(!o)return[];if(o.fn)return[o.fn];for(var r=0,a=o.length,s=new Array(a);r<a;r++)s[r]=o[r].fn;return s},i.prototype.listenerCount=function(e){var t=n?n+e:e,o=this._events[t];return o?o.fn?1:o.length:0},i.prototype.emit=function(e,t,o,r,a,s){var i=n?n+e:e;if(!this._events[i])return!1;var u,c,l=this._events[i],d=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),d){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,o),!0;case 4:return l.fn.call(l.context,t,o,r),!0;case 5:return l.fn.call(l.context,t,o,r,a),!0;case 6:return l.fn.call(l.context,t,o,r,a,s),!0}for(c=1,u=new Array(d-1);c<d;c++)u[c-1]=arguments[c];l.fn.apply(l.context,u)}else{var p,g=l.length;for(c=0;c<g;c++)switch(l[c].once&&this.removeListener(e,l[c].fn,void 0,!0),d){case 1:l[c].fn.call(l[c].context);break;case 2:l[c].fn.call(l[c].context,t);break;case 3:l[c].fn.call(l[c].context,t,o);break;case 4:l[c].fn.call(l[c].context,t,o,r);break;default:if(!u)for(p=1,u=new Array(d-1);p<d;p++)u[p-1]=arguments[p];l[c].fn.apply(l[c].context,u)}}return!0},i.prototype.on=function(e,t,n){return a(this,e,t,n,!1)},i.prototype.once=function(e,t,n){return a(this,e,t,n,!0)},i.prototype.removeListener=function(e,t,o,r){var a=n?n+e:e;if(!this._events[a])return this;if(!t)return s(this,a),this;var i=this._events[a];if(i.fn)i.fn!==t||r&&!i.once||o&&i.context!==o||s(this,a);else{for(var u=0,c=[],l=i.length;u<l;u++)(i[u].fn!==t||r&&!i[u].once||o&&i[u].context!==o)&&c.push(i[u]);c.length?this._events[a]=1===c.length?c[0]:c:s(this,a)}return this},i.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&s(this,t)):(this._events=new o,this._eventsCount=0),this},i.prototype.off=i.prototype.removeListener,i.prototype.addListener=i.prototype.on,i.prefixed=n,i.EventEmitter=i,e.exports=i})),kv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="BigDataChannelModule",o.FILETYPE={SOUND:2106,FILE:2107,VIDEO:2113},o._bdh_download_server="grouptalk.c2c.qq.com",o._BDHBizID=10001,o._authKey="",o._expireTime=0,o.getInnerEmitterInstance().on(Om,o._getAuthKey,zn(o)),o}return Un(n,[{key:"_getAuthKey",value:function(){var e=this;this.request({protocolName:fl}).then((function(t){t.data.authKey&&(e._authKey=t.data.authKey,e._expireTime=parseInt(t.data.expireTime))}))}},{key:"_isFromOlderVersion",value:function(e){return!(!e.content||2===e.content.downloadFlag)}},{key:"parseElements",value:function(e,t){if(!zi(e)||!t)return[];for(var n=[],o=null,r=0;r<e.length;r++)o=e[r],this._needParse(o)?n.push(this._parseElement(o,t)):n.push(e[r]);return n}},{key:"_needParse",value:function(e){return!e.cloudCustomData&&!(!this._isFromOlderVersion(e)||e.type!==so.MSG_AUDIO&&e.type!==so.MSG_FILE&&e.type!==so.MSG_VIDEO)}},{key:"_parseElement",value:function(e,t){switch(e.type){case so.MSG_AUDIO:return this._parseAudioElement(e,t);case so.MSG_FILE:return this._parseFileElement(e,t);case so.MSG_VIDEO:return this._parseVideoElement(e,t)}}},{key:"_parseAudioElement",value:function(e,t){return e.content.url=this._genAudioUrl(e.content.uuid,t),e}},{key:"_parseFileElement",value:function(e,t){return e.content.url=this._genFileUrl(e.content.uuid,t,e.content.fileName),e}},{key:"_parseVideoElement",value:function(e,t){return e.content.url=this._genVideoUrl(e.content.uuid,t),e}},{key:"_genAudioUrl",value:function(e,t){if(""===this._authKey)return Bi.warn("".concat(this._className,"._genAudioUrl no authKey!")),"";var n=this.getModule(Yc).getSDKAppID();return"https://".concat(this._bdh_download_server,"/asn.com/stddownload_common_file?authkey=").concat(this._authKey,"&bid=").concat(this._BDHBizID,"&subbid=").concat(n,"&fileid=").concat(e,"&filetype=").concat(this.FILETYPE.SOUND,"&openid=").concat(t,"&ver=0")}},{key:"_genFileUrl",value:function(e,t,n){if(""===this._authKey)return Bi.warn("".concat(this._className,"._genFileUrl no authKey!")),"";n||(n="".concat(Math.floor(1e5*Math.random()),"-").concat(Date.now()));var o=this.getModule(Yc).getSDKAppID();return"https://".concat(this._bdh_download_server,"/asn.com/stddownload_common_file?authkey=").concat(this._authKey,"&bid=").concat(this._BDHBizID,"&subbid=").concat(o,"&fileid=").concat(e,"&filetype=").concat(this.FILETYPE.FILE,"&openid=").concat(t,"&ver=0&filename=").concat(encodeURIComponent(n))}},{key:"_genVideoUrl",value:function(e,t){if(""===this._authKey)return Bi.warn("".concat(this._className,"._genVideoUrl no authKey!")),"";var n=this.getModule(Yc).getSDKAppID();return"https://".concat(this._bdh_download_server,"/asn.com/stddownload_common_file?authkey=").concat(this._authKey,"&bid=").concat(this._BDHBizID,"&subbid=").concat(n,"&fileid=").concat(e,"&filetype=").concat(this.FILETYPE.VIDEO,"&openid=").concat(t,"&ver=0")}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._authKey="",this.expireTime=0}}]),n}(cl),Dv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="UploadModule",o.TIMUploadPlugin=null,o.timUploadPlugin=null,o.COSSDK=null,o._cosUploadMethod=null,o.expiredTimeLimit=600,o.appid=0,o.bucketName="",o.ciUrl="",o.directory="",o.downloadUrl="",o.uploadUrl="",o.region="ap-shanghai",o.cos=null,o.cosOptions={secretId:"",secretKey:"",sessionToken:"",expiredTime:0},o.uploadFileType="",o.duration=900,o.tryCount=0,o.getInnerEmitterInstance().on(Om,o._init,zn(o)),o}return Un(n,[{key:"_init",value:function(){var e="".concat(this._className,"._init"),t=this.getModule(Zc);if(this.TIMUploadPlugin=t.getPlugin("tim-upload-plugin"),this.TIMUploadPlugin)this._initUploaderMethod();else{var n=si?"cos-wx-sdk":"cos-js-sdk";this.COSSDK=t.getPlugin(n),this.COSSDK?(this._getAuthorizationKey(),Bi.warn("".concat(e," v2.9.2起推荐使用 tim-upload-plugin 代替 ").concat(n,"，上传更快更安全。详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#registerPlugin"))):Bi.warn("".concat(e," 没有检测到上传插件，将无法发送图片、音频、视频、文件等类型的消息。详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#registerPlugin"))}}},{key:"_getAuthorizationKey",value:function(){var e=this,t=new Zp(dg),n=Math.ceil(Date.now()/1e3);this.request({protocolName:gd,requestData:{duration:this.expiredTimeLimit}}).then((function(o){var r=o.data;Bi.log("".concat(e._className,"._getAuthorizationKey ok. data:"),r);var a=r.expiredTime-n;t.setMessage("requestId:".concat(r.requestId," requestTime:").concat(n," expiredTime:").concat(r.expiredTime," diff:").concat(a,"s")).setNetworkType(e.getNetworkType()).end(),!si&&r.region&&(e.region=r.region),e.appid=r.appid,e.bucketName=r.bucketName,e.ciUrl=r.ciUrl,e.directory=r.directory,e.downloadUrl=r.downloadUrl,e.uploadUrl=r.uploadUrl,e.cosOptions={secretId:r.secretId,secretKey:r.secretKey,sessionToken:r.sessionToken,expiredTime:r.expiredTime},Bi.log("".concat(e._className,"._getAuthorizationKey ok. region:").concat(e.region," bucketName:").concat(e.bucketName)),e._initUploaderMethod()})).catch((function(n){e.probeNetwork().then((function(e){var o=Qn(e,2),r=o[0],a=o[1];t.setError(n,r,a).end()})),Bi.warn("".concat(e._className,"._getAuthorizationKey failed. error:"),n)}))}},{key:"_getCosPreSigUrl",value:function(e){var t=this,n="".concat(this._className,"._getCosPreSigUrl"),o=Math.ceil(Date.now()/1e3),r=new Zp(pg);return this.request({protocolName:hd,requestData:{fileType:e.fileType,fileName:e.fileName,uploadMethod:e.uploadMethod,duration:e.duration}}).then((function(e){t.tryCount=0;var a=e.data||{},s=a.expiredTime-o;return Bi.log("".concat(n," ok. data:"),a),r.setMessage("requestId:".concat(a.requestId," expiredTime:").concat(a.expiredTime," diff:").concat(s,"s")).setNetworkType(t.getNetworkType()).end(),a})).catch((function(o){return-1===o.code&&(o.code=kd.COS_GET_SIG_FAIL),t.probeNetwork().then((function(e){var t=Qn(e,2),n=t[0],a=t[1];r.setError(o,n,a).end()})),Bi.warn("".concat(n," failed. error:"),o),t.tryCount<1?(t.tryCount++,t._getCosPreSigUrl(e)):(t.tryCount=0,Mm({code:kd.COS_GET_SIG_FAIL,message:Pd}))}))}},{key:"_initUploaderMethod",value:function(){var e=this;if(this.TIMUploadPlugin)return this.timUploadPlugin=new this.TIMUploadPlugin,void(this._cosUploadMethod=function(t,n){e.timUploadPlugin.uploadFile(t,n)});this.appid&&(this.cos=si?new this.COSSDK({ForcePathStyle:!0,getAuthorization:this._getAuthorization.bind(this)}):new this.COSSDK({getAuthorization:this._getAuthorization.bind(this)}),this._cosUploadMethod=si?function(t,n){e.cos.postObject(t,n)}:function(t,n){e.cos.uploadFiles(t,n)})}},{key:"onCheckTimer",value:function(e){this.COSSDK&&(this.TIMUploadPlugin||this.isLoggedIn()&&e%60==0&&Math.ceil(Date.now()/1e3)>=this.cosOptions.expiredTime-120&&this._getAuthorizationKey())}},{key:"_getAuthorization",value:function(e,t){t({TmpSecretId:this.cosOptions.secretId,TmpSecretKey:this.cosOptions.secretKey,XCosSecurityToken:this.cosOptions.sessionToken,ExpiredTime:this.cosOptions.expiredTime})}},{key:"upload",value:function(e){if(!0===e.getRelayFlag())return Promise.resolve();var t=this.getModule(il);switch(e.type){case so.MSG_IMAGE:return t.addTotalCount(jp),this._uploadImage(e);case so.MSG_FILE:return t.addTotalCount(jp),this._uploadFile(e);case so.MSG_AUDIO:return t.addTotalCount(jp),this._uploadAudio(e);case so.MSG_VIDEO:return t.addTotalCount(jp),this._uploadVideo(e);default:return Promise.resolve()}}},{key:"_uploadImage",value:function(e){var t=this.getModule(xc),n=e.getElements()[0],o=t.getMessageOptionByID(e.ID);return this.doUploadImage({file:o.payload.file,to:o.to,onProgress:function(e){if(n.updatePercent(e),Qi(o.onProgress))try{o.onProgress(e)}catch(t){return Mm({code:kd.MESSAGE_ONPROGRESS_FUNCTION_ERROR,message:Vd})}}}).then((function(t){var o=t.location,r=t.fileType,a=t.fileSize,s=t.width,i=t.height,u=pu(o);n.updateImageFormat(r);var c=Tu({originUrl:u,originWidth:s,originHeight:i,min:198}),l=Tu({originUrl:u,originWidth:s,originHeight:i,min:720});return n.updateImageInfoArray([{size:a,url:u,width:s,height:i},xn({},l),xn({},c)]),e}))}},{key:"_uploadFile",value:function(e){var t=this.getModule(xc),n=e.getElements()[0],o=t.getMessageOptionByID(e.ID);return this.doUploadFile({file:o.payload.file,to:o.to,onProgress:function(e){if(n.updatePercent(e),Qi(o.onProgress))try{o.onProgress(e)}catch(t){return Mm({code:kd.MESSAGE_ONPROGRESS_FUNCTION_ERROR,message:Vd})}}}).then((function(t){var o=t.location,r=pu(o);return n.updateFileUrl(r),e}))}},{key:"_uploadAudio",value:function(e){var t=this.getModule(xc),n=e.getElements()[0],o=t.getMessageOptionByID(e.ID);return this.doUploadAudio({file:o.payload.file,to:o.to,onProgress:function(e){if(n.updatePercent(e),Qi(o.onProgress))try{o.onProgress(e)}catch(t){return Mm({code:kd.MESSAGE_ONPROGRESS_FUNCTION_ERROR,message:Vd})}}}).then((function(t){var o=t.location,r=pu(o);return n.updateAudioUrl(r),e}))}},{key:"_uploadVideo",value:function(e){var t=this.getModule(xc),n=e.getElements()[0],o=t.getMessageOptionByID(e.ID);return this.doUploadVideo({file:o.payload.file,to:o.to,onProgress:function(e){if(n.updatePercent(e),Qi(o.onProgress))try{o.onProgress(e)}catch(t){return Mm({code:kd.MESSAGE_ONPROGRESS_FUNCTION_ERROR,message:Vd})}}}).then((function(t){var o=pu(t.location);return n.updateVideoUrl(o),e}))}},{key:"doUploadImage",value:function(e){if(!e.file)return Mm({code:kd.MESSAGE_IMAGE_SELECT_FILE_FIRST,message:jd});var t=this._checkImageType(e.file);if(!0!==t)return t;var n=this._checkImageSize(e.file);if(!0!==n)return n;var o=null;return this._setUploadFileType(Pm),this.uploadByCOS(e).then((function(e){return o=e,t="https://".concat(e.location),si?new Promise((function(e,n){ui.getImageInfo({src:t,success:function(t){e({width:t.width,height:t.height})},fail:function(){e({width:0,height:0})}})})):vi&&9===Mi?Promise.resolve({width:0,height:0}):new Promise((function(e,n){var o=new Image;o.onload=function(){e({width:this.width,height:this.height}),o=null},o.onerror=function(){e({width:0,height:0}),o=null},o.src=t}));var t})).then((function(e){return o.width=e.width,o.height=e.height,Promise.resolve(o)}))}},{key:"_checkImageType",value:function(e){var t="";return t=si?e.url.slice(e.url.lastIndexOf(".")+1):e.files[0].name.slice(e.files[0].name.lastIndexOf(".")+1),wm.indexOf(t.toLowerCase())>=0||Mm({code:kd.MESSAGE_IMAGE_TYPES_LIMIT,message:Wd})}},{key:"_checkImageSize",value:function(e){var t=0;return 0===(t=si?e.size:e.files[0].size)?Mm({code:kd.MESSAGE_FILE_IS_EMPTY,message:"".concat(xd)}):t<20971520||Mm({code:kd.MESSAGE_IMAGE_SIZE_LIMIT,message:"".concat(Yd)})}},{key:"doUploadFile",value:function(e){var t=null;return e.file?e.file.files[0].size>104857600?Mm(t={code:kd.MESSAGE_FILE_SIZE_LIMIT,message:tp}):0===e.file.files[0].size?(t={code:kd.MESSAGE_FILE_IS_EMPTY,message:"".concat(xd)},Mm(t)):(this._setUploadFileType(qm),this.uploadByCOS(e)):Mm(t={code:kd.MESSAGE_FILE_SELECT_FILE_FIRST,message:ep})}},{key:"doUploadVideo",value:function(e){return e.file.videoFile.size>104857600?Mm({code:kd.MESSAGE_VIDEO_SIZE_LIMIT,message:"".concat(Qd)}):0===e.file.videoFile.size?Mm({code:kd.MESSAGE_FILE_IS_EMPTY,message:"".concat(xd)}):-1===Gm.indexOf(e.file.videoFile.type)?Mm({code:kd.MESSAGE_VIDEO_TYPES_LIMIT,message:"".concat($d)}):(this._setUploadFileType(Um),si?this.handleVideoUpload({file:e.file.videoFile,onProgress:e.onProgress}):ii?this.handleVideoUpload(e):void 0)}},{key:"handleVideoUpload",value:function(e){var t=this;return new Promise((function(n,o){t.uploadByCOS(e).then((function(e){n(e)})).catch((function(){t.uploadByCOS(e).then((function(e){n(e)})).catch((function(){o(new fm({code:kd.MESSAGE_VIDEO_UPLOAD_FAIL,message:Xd}))}))}))}))}},{key:"doUploadAudio",value:function(e){return e.file?e.file.size>20971520?Mm(new fm({code:kd.MESSAGE_AUDIO_SIZE_LIMIT,message:"".concat(Jd)})):0===e.file.size?Mm(new fm({code:kd.MESSAGE_FILE_IS_EMPTY,message:"".concat(xd)})):(this._setUploadFileType(Fm),this.uploadByCOS(e)):Mm(new fm({code:kd.MESSAGE_AUDIO_UPLOAD_FAIL,message:zd}))}},{key:"uploadByCOS",value:function(e){var t=this,n="".concat(this._className,".uploadByCOS");if(!Qi(this._cosUploadMethod))return Bi.warn("".concat(n," 没有检测到上传插件，将无法发送图片、音频、视频、文件等类型的消息。详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#registerPlugin")),Mm({code:kd.COS_UNDETECTED,message:Gd});if(this.timUploadPlugin)return this._uploadWithPreSigUrl(e);var o=new Zp(gg),r=Date.now(),a=si?e.file:e.file.files[0];return new Promise((function(s,i){var u=si?t._createCosOptionsWXMiniApp(e):t._createCosOptionsWeb(e),c=t;t._cosUploadMethod(u,(function(e,u){var l=Object.create(null);if(u){if(e||zi(u.files)&&u.files[0].error){var d=new fm({code:kd.MESSAGE_FILE_UPLOAD_FAIL,message:Zd});return o.setError(d,!0,t.getNetworkType()).end(),Bi.log("".concat(n," failed. error:"),u.files[0].error),403===u.files[0].error.statusCode&&(Bi.warn("".concat(n," failed. cos AccessKeyId was invalid, regain auth key!")),t._getAuthorizationKey()),void i(d)}l.fileName=a.name,l.fileSize=a.size,l.fileType=a.type.slice(a.type.indexOf("/")+1).toLowerCase(),l.location=si?u.Location:u.files[0].data.Location;var p=Date.now()-r,g=c._formatFileSize(a.size),h=c._formatSpeed(1e3*a.size/p),f="size:".concat(g," time:").concat(p,"ms speed:").concat(h);Bi.log("".concat(n," success. name:").concat(a.name," ").concat(f)),s(l);var _=t.getModule(il);return _.addCost(jp,p),_.addFileSize(jp,a.size),void o.setNetworkType(t.getNetworkType()).setMessage(f).end()}var m=new fm({code:kd.MESSAGE_FILE_UPLOAD_FAIL,message:Zd});o.setError(m,!0,c.getNetworkType()).end(),Bi.warn("".concat(n," failed. error:"),e),403===e.statusCode&&(Bi.warn("".concat(n," failed. cos AccessKeyId was invalid, regain auth key!")),t._getAuthorizationKey()),i(m)}))}))}},{key:"_uploadWithPreSigUrl",value:function(e){var t=this,n="".concat(this._className,"._uploadWithPreSigUrl"),o=si?e.file:e.file.files[0];return this._createCosOptionsPreSigUrl(e).then((function(e){return new Promise((function(r,a){var s=new Zp(gg),i=Date.now();t._cosUploadMethod(e,(function(e,u){var c=Object.create(null);if(e||403===u.statusCode){var l=new fm({code:kd.MESSAGE_FILE_UPLOAD_FAIL,message:Zd});return s.setError(l,!0,t.getNetworkType()).end(),Bi.log("".concat(n," failed, error:"),e),void a(l)}var d=u.data.location||"";0!==d.indexOf("https://")&&0!==d.indexOf("http://")||(d=d.split("//")[1]),c.fileName=o.name,c.fileSize=o.size,c.fileType=o.type.slice(o.type.indexOf("/")+1).toLowerCase(),c.location=d;var p=Date.now()-i,g=t._formatFileSize(o.size),h=t._formatSpeed(1e3*o.size/p),f="size:".concat(g,",time:").concat(p,"ms,speed:").concat(h," res:").concat(JSON.stringify(u.data));Bi.log("".concat(n," success name:").concat(o.name,",").concat(f)),s.setNetworkType(t.getNetworkType()).setMessage(f).end();var _=t.getModule(il);_.addCost(jp,p),_.addFileSize(jp,o.size),r(c)}))}))}))}},{key:"_formatFileSize",value:function(e){return e<1024?e+"B":e<1048576?Math.floor(e/1024)+"KB":Math.floor(e/1048576)+"MB"}},{key:"_formatSpeed",value:function(e){return e<=1048576?Au(e/1024,1)+"KB/s":Au(e/1048576,1)+"MB/s"}},{key:"_createCosOptionsWeb",value:function(e){var t=e.file.files[0].name,n=t.slice(t.lastIndexOf(".")),o=this._genFileName("".concat(su(999999)).concat(n));return{files:[{Bucket:"".concat(this.bucketName,"-").concat(this.appid),Region:this.region,Key:"".concat(this.directory,"/").concat(o),Body:e.file.files[0]}],SliceSize:1048576,onProgress:function(t){if("function"==typeof e.onProgress)try{e.onProgress(t.percent)}catch(n){Bi.warn("onProgress callback error:",n)}},onFileFinish:function(e,t,n){}}}},{key:"_createCosOptionsWXMiniApp",value:function(e){var t=this._genFileName(e.file.name),n=e.file.url;return{Bucket:"".concat(this.bucketName,"-").concat(this.appid),Region:this.region,Key:"".concat(this.directory,"/").concat(t),FilePath:n,onProgress:function(t){if(Bi.log(JSON.stringify(t)),"function"==typeof e.onProgress)try{e.onProgress(t.percent)}catch(n){Bi.warn("onProgress callback error:",n)}}}}},{key:"_createCosOptionsPreSigUrl",value:function(e){var t=this,n="",o="",r=0;if(si)n=this._genFileName(e.file.name),o=e.file.url,r=1;else{var a=e.file.files[0].name,s=a.slice(a.lastIndexOf("."));n=this._genFileName("".concat(su(999999)).concat(s)),o=e.file.files[0],r=0}return this._getCosPreSigUrl({fileType:this.uploadFileType,fileName:n,uploadMethod:r,duration:this.duration}).then((function(r){var a=r.uploadUrl,s=r.downloadUrl;return{url:a,fileType:t.uploadFileType,fileName:n,resources:o,downloadUrl:s,onProgress:function(t){if("function"==typeof e.onProgress)try{e.onProgress(t.percent)}catch(n){Bi.warn("onProgress callback error:",n),Bi.error(n)}}}}))}},{key:"_genFileName",value:function(e){return"".concat(Iu(),"-").concat(e)}},{key:"_setUploadFileType",value:function(e){this.uploadFileType=e}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset"))}}]),n}(cl),Nv=function(){function e(t){Gn(this,e),this._className="MergerMessageHandler",this._messageModule=t}return Un(e,[{key:"uploadMergerMessage",value:function(e,t){var n=this;Bi.debug("".concat(this._className,".uploadMergerMessage message:"),e,"messageBytes:".concat(t));var o=e.payload.messageList,r=o.length,a=new Zp(Cg);return this._messageModule.request({protocolName:yd,requestData:{messageList:o}}).then((function(e){Bi.debug("".concat(n._className,".uploadMergerMessage ok. response:"),e.data);var o=e.data,s=o.pbDownloadKey,i=o.downloadKey,u={pbDownloadKey:s,downloadKey:i,messageNumber:r};return a.setNetworkType(n._messageModule.getNetworkType()).setMessage("".concat(r,"-").concat(t,"-").concat(i)).end(),u})).catch((function(e){throw Bi.warn("".concat(n._className,".uploadMergerMessage failed. error:"),e),n._messageModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),e}))}},{key:"downloadMergerMessage",value:function(e){var t=this;Bi.debug("".concat(this._className,".downloadMergerMessage message:"),e);var n=e.payload.downloadKey,o=new Zp(Sg);return o.setMessage("downloadKey:".concat(n)),this._messageModule.request({protocolName:Id,requestData:{downloadKey:n}}).then((function(n){if(Bi.debug("".concat(t._className,".downloadMergerMessage ok. response:"),n.data),Qi(e.clearElement)){var r=e.payload,a=(r.downloadKey,r.pbDownloadKey,r.messageList,Yn(r,["downloadKey","pbDownloadKey","messageList"]));e.clearElement(),e.setElement({type:e.type,content:xn({messageList:n.data.messageList},a)})}else{var s=[];n.data.messageList.forEach((function(e){if(!Nu(e)){var t=new am(e);s.push(t)}})),e.payload.messageList=s,e.payload.downloadKey="",e.payload.pbDownloadKey=""}return o.setNetworkType(t._messageModule.getNetworkType()).end(),e})).catch((function(e){throw Bi.warn("".concat(t._className,".downloadMergerMessage failed. key:").concat(n," error:"),e),t._messageModule.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),e}))}},{key:"createMergerMessagePack",value:function(e,t,n){return e.conversationType===so.CONV_C2C?this._createC2CMergerMessagePack(e,t,n):this._createGroupMergerMessagePack(e,t,n)}},{key:"_createC2CMergerMessagePack",value:function(e,t,n){var o=null;t&&(t.offlinePushInfo&&(o=t.offlinePushInfo),!0===t.onlineUserOnly&&(o?o.disablePush=!0:o={disablePush:!0}));var r="";ji(e.cloudCustomData)&&e.cloudCustomData.length>0&&(r=e.cloudCustomData);var a=n.pbDownloadKey,s=n.downloadKey,i=n.messageNumber,u=e.payload,c=u.title,l=u.abstractList,d=u.compatibleText,p=this._messageModule.getModule(Bc);return{protocolName:_l,tjgID:this._messageModule.generateTjgID(e),requestData:{fromAccount:this._messageModule.getMyUserID(),toAccount:e.to,msgBody:[{msgType:e.type,msgContent:{pbDownloadKey:a,downloadKey:s,title:c,abstractList:l,compatibleText:d,messageNumber:i}}],cloudCustomData:r,msgSeq:e.sequence,msgRandom:e.random,msgLifeTime:p&&p.isOnlineMessage(e,t)?0:void 0,offlinePushInfo:o?{pushFlag:!0===o.disablePush?1:0,title:o.title||"",desc:o.description||"",ext:o.extension||"",apnsInfo:{badgeMode:!0===o.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:o.androidOPPOChannelID||""}}:void 0}}}},{key:"_createGroupMergerMessagePack",value:function(e,t,n){var o=null;t&&t.offlinePushInfo&&(o=t.offlinePushInfo);var r="";ji(e.cloudCustomData)&&e.cloudCustomData.length>0&&(r=e.cloudCustomData);var a=n.pbDownloadKey,s=n.downloadKey,i=n.messageNumber,u=e.payload,c=u.title,l=u.abstractList,d=u.compatibleText,p=this._messageModule.getModule(Kc);return{protocolName:ml,tjgID:this._messageModule.generateTjgID(e),requestData:{fromAccount:this._messageModule.getMyUserID(),groupID:e.to,msgBody:[{msgType:e.type,msgContent:{pbDownloadKey:a,downloadKey:s,title:c,abstractList:l,compatibleText:d,messageNumber:i}}],random:e.random,priority:e.priority,clientSequence:e.clientSequence,groupAtInfo:void 0,cloudCustomData:r,onlineOnlyFlag:p&&p.isOnlineMessage(e,t)?1:0,offlinePushInfo:o?{pushFlag:!0===o.disablePush?1:0,title:o.title||"",desc:o.description||"",ext:o.extension||"",apnsInfo:{badgeMode:!0===o.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:o.androidOPPOChannelID||""}}:void 0}}}}]),e}(),Ov={ERR_SVR_COMM_SENSITIVE_TEXT:80001,ERR_SVR_COMM_BODY_SIZE_LIMIT:80002,OPEN_SERVICE_OVERLOAD_ERROR:60022,ERR_SVR_MSG_PKG_PARSE_FAILED:20001,ERR_SVR_MSG_INTERNAL_AUTH_FAILED:20002,ERR_SVR_MSG_INVALID_ID:20003,ERR_SVR_MSG_PUSH_DENY:20006,ERR_SVR_MSG_IN_PEER_BLACKLIST:20007,ERR_SVR_MSG_BOTH_NOT_FRIEND:20009,ERR_SVR_MSG_NOT_PEER_FRIEND:20010,ERR_SVR_MSG_NOT_SELF_FRIEND:20011,ERR_SVR_MSG_SHUTUP_DENY:20012,ERR_SVR_GROUP_INVALID_PARAMETERS:10004,ERR_SVR_GROUP_PERMISSION_DENY:10007,ERR_SVR_GROUP_NOT_FOUND:10010,ERR_SVR_GROUP_INVALID_GROUPID:10015,ERR_SVR_GROUP_REJECT_FROM_THIRDPARTY:10016,ERR_SVR_GROUP_SHUTUP_DENY:10017,MESSAGE_SEND_FAIL:2100,OVER_FREQUENCY_LIMIT:2996},Rv=[kd.MESSAGE_ONPROGRESS_FUNCTION_ERROR,kd.MESSAGE_IMAGE_SELECT_FILE_FIRST,kd.MESSAGE_IMAGE_TYPES_LIMIT,kd.MESSAGE_FILE_IS_EMPTY,kd.MESSAGE_IMAGE_SIZE_LIMIT,kd.MESSAGE_FILE_SELECT_FILE_FIRST,kd.MESSAGE_FILE_SIZE_LIMIT,kd.MESSAGE_VIDEO_SIZE_LIMIT,kd.MESSAGE_VIDEO_TYPES_LIMIT,kd.MESSAGE_AUDIO_UPLOAD_FAIL,kd.MESSAGE_AUDIO_SIZE_LIMIT,kd.COS_UNDETECTED],Lv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="MessageModule",o._messageOptionsMap=new Map,o._mergerMessageHandler=new Nv(zn(o)),o}return Un(n,[{key:"createTextMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new um(e),o="string"==typeof e.payload?e.payload:e.payload.text,r=new Nh({text:o}),a=this._getNickAndAvatarByUserID(t);return n.setElement(r),n.setNickAndAvatar(a),n.setNameCard(this._getNameCardByGroupID(n)),n}},{key:"createImageMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new um(e);if(si){var o=e.payload.file;if(Ki(o))return void Bi.warn("小程序环境下调用 createImageMessage 接口时，payload.file 不支持传入 File 对象");var r=o.tempFilePaths[0],a={url:r,name:r.slice(r.lastIndexOf("/")+1),size:o.tempFiles&&o.tempFiles[0].size||1,type:r.slice(r.lastIndexOf(".")+1).toLowerCase()};e.payload.file=a}else if(ii)if(Ki(e.payload.file)){var s=e.payload.file;e.payload.file={files:[s]}}else if(Yi(e.payload.file)&&"undefined"!=typeof uni){var i=e.payload.file.tempFiles[0];e.payload.file={files:[i]}}var u=new B_({imageFormat:G_.IMAGE_FORMAT.UNKNOWN,uuid:this._generateUUID(),file:e.payload.file}),c=this._getNickAndAvatarByUserID(t);return n.setElement(u),n.setNickAndAvatar(c),n.setNameCard(this._getNameCardByGroupID(n)),this._messageOptionsMap.set(n.ID,e),n}},{key:"createAudioMessage",value:function(e){if(si){var t=e.payload.file;if(si){var n={url:t.tempFilePath,name:t.tempFilePath.slice(t.tempFilePath.lastIndexOf("/")+1),size:t.fileSize,second:parseInt(t.duration)/1e3,type:t.tempFilePath.slice(t.tempFilePath.lastIndexOf(".")+1).toLowerCase()};e.payload.file=n}var o=this.getMyUserID();e.currentUser=o;var r=new um(e),a=new H_({second:Math.floor(t.duration/1e3),size:t.fileSize,url:t.tempFilePath,uuid:this._generateUUID()}),s=this._getNickAndAvatarByUserID(o);return r.setElement(a),r.setNickAndAvatar(s),r.setNameCard(this._getNameCardByGroupID(r)),this._messageOptionsMap.set(r.ID,e),r}Bi.warn("createAudioMessage 目前只支持小程序环境下发语音消息")}},{key:"createVideoMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t,e.payload.file.thumbUrl="https://web.sdk.qcloud.com/im/assets/images/transparent.png",e.payload.file.thumbSize=1668;var n={};if(si){if(ri)return void Bi.warn("createVideoMessage 不支持在支付宝小程序环境下使用");if(Ki(e.payload.file))return void Bi.warn("小程序环境下调用 createVideoMessage 接口时，payload.file 不支持传入 File 对象");var o=e.payload.file;n.url=o.tempFilePath,n.name=o.tempFilePath.slice(o.tempFilePath.lastIndexOf("/")+1),n.size=o.size,n.second=o.duration,n.type=o.tempFilePath.slice(o.tempFilePath.lastIndexOf(".")+1).toLowerCase()}else if(ii){if(Ki(e.payload.file)){var r=e.payload.file;e.payload.file.files=[r]}else if(Yi(e.payload.file)&&"undefined"!=typeof uni){var a=e.payload.file.tempFile;e.payload.file.files=[a]}var s=e.payload.file;n.url=window.URL.createObjectURL(s.files[0]),n.name=s.files[0].name,n.size=s.files[0].size,n.second=s.files[0].duration||0,n.type=s.files[0].type.split("/")[1]}e.payload.file.videoFile=n;var i=new um(e),u=new om({videoFormat:n.type,videoSecond:Au(n.second,0),videoSize:n.size,remoteVideoUrl:"",videoUrl:n.url,videoUUID:this._generateUUID(),thumbUUID:this._generateUUID(),thumbWidth:e.payload.file.width||200,thumbHeight:e.payload.file.height||200,thumbUrl:e.payload.file.thumbUrl,thumbSize:e.payload.file.thumbSize,thumbFormat:e.payload.file.thumbUrl.slice(e.payload.file.thumbUrl.lastIndexOf(".")+1).toLowerCase()}),c=this._getNickAndAvatarByUserID(t);return i.setElement(u),i.setNickAndAvatar(c),i.setNameCard(this._getNameCardByGroupID(i)),this._messageOptionsMap.set(i.ID,e),i}},{key:"createCustomMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new um(e),o=new nm({data:e.payload.data,description:e.payload.description,extension:e.payload.extension}),r=this._getNickAndAvatarByUserID(t);return n.setElement(o),n.setNickAndAvatar(r),n.setNameCard(this._getNameCardByGroupID(n)),n}},{key:"createFaceMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new um(e),o=new K_(e.payload),r=this._getNickAndAvatarByUserID(t);return n.setElement(o),n.setNickAndAvatar(r),n.setNameCard(this._getNameCardByGroupID(n)),n}},{key:"createMergerMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=this._getNickAndAvatarByUserID(t),o=new um(e),r=new sm(e.payload);return o.setElement(r),o.setNickAndAvatar(n),o.setNameCard(this._getNameCardByGroupID(o)),o.setRelayFlag(!0),o}},{key:"createForwardMessage",value:function(e){var t=e.to,n=e.conversationType,o=e.priority,r=e.payload,a=this.getMyUserID(),s=this._getNickAndAvatarByUserID(a);if(r.type===so.MSG_GRP_TIP)return Mm(new fm({code:kd.MESSAGE_FORWARD_TYPE_INVALID,message:sp}));var i={to:t,conversationType:n,conversationID:"".concat(n).concat(t),priority:o,isPlaceMessage:0,status:Cc.UNSEND,currentUser:a,cloudCustomData:e.cloudCustomData||r.cloudCustomData||""},u=new um(i);return u.setElement(r.getElements()[0]),u.setNickAndAvatar(s),u.setNameCard(this._getNameCardByGroupID(r)),u.setRelayFlag(!0),u}},{key:"downloadMergerMessage",value:function(e){return this._mergerMessageHandler.downloadMergerMessage(e)}},{key:"createFileMessage",value:function(e){if(!si){if(ii)if(Ki(e.payload.file)){var t=e.payload.file;e.payload.file={files:[t]}}else if(Yi(e.payload.file)&&"undefined"!=typeof uni){var n=e.payload.file.tempFiles[0];e.payload.file={files:[n]}}var o=this.getMyUserID();e.currentUser=o;var r=new um(e),a=new tm({uuid:this._generateUUID(),file:e.payload.file}),s=this._getNickAndAvatarByUserID(o);return r.setElement(a),r.setNickAndAvatar(s),r.setNameCard(this._getNameCardByGroupID(r)),this._messageOptionsMap.set(r.ID,e),r}Bi.warn("小程序目前不支持选择文件， createFileMessage 接口不可用！")}},{key:"createLocationMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new um(e),o=new rm(e.payload),r=this._getNickAndAvatarByUserID(t);return n.setElement(o),n.setNickAndAvatar(r),n.setNameCard(this._getNameCardByGroupID(n)),this._messageOptionsMap.set(n.ID,e),n}},{key:"_onCannotFindModule",value:function(){return Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"sendMessageInstance",value:function(e,t){var n,o=this,r=null;switch(e.conversationType){case so.CONV_C2C:if(!(r=this.getModule(Bc)))return this._onCannotFindModule();break;case so.CONV_GROUP:if(!(r=this.getModule(Kc)))return this._onCannotFindModule();break;default:return Mm({code:kd.MESSAGE_SEND_INVALID_CONVERSATION_TYPE,message:qd})}var a=this.getModule($c),s=this.getModule(Kc);return a.upload(e).then((function(){return o._getSendMessageSpecifiedKey(e)===Hp&&o.getModule(il).addSuccessCount(jp),s.guardForAVChatRoom(e).then((function(){if(!e.isSendable())return Mm({code:kd.MESSAGE_FILE_URL_IS_EMPTY,message:np});o._addSendMessageTotalCount(e),n=Date.now();var a=function(e){var t="utf-8";ii&&document&&(t=document.charset.toLowerCase());var n,o,r=0;if(o=e.length,"utf-8"===t||"utf8"===t)for(var a=0;a<o;a++)(n=e.codePointAt(a))<=127?r+=1:n<=2047?r+=2:n<=65535?r+=3:(r+=4,a++);else if("utf-16"===t||"utf16"===t)for(var s=0;s<o;s++)(n=e.codePointAt(s))<=65535?r+=2:(r+=4,s++);else r=e.replace(/[^\x00-\xff]/g,"aa").length;return r}(JSON.stringify(e));return e.type===so.MSG_MERGER&&a>7e3?o._mergerMessageHandler.uploadMergerMessage(e,a).then((function(n){var r=o._mergerMessageHandler.createMergerMessagePack(e,t,n);return o.request(r)})):(o.getModule(Wc).setMessageRandom(e),e.conversationType===so.CONV_C2C||e.conversationType===so.CONV_GROUP?r.sendMessage(e,t):void 0)})).then((function(a){var s=a.data,i=s.time,u=s.sequence;o._addSendMessageSuccessCount(e,n),o._messageOptionsMap.delete(e.ID);var c=o.getModule(Wc);e.status=Cc.SUCCESS,e.time=i;var l=!1;if(e.conversationType===so.CONV_GROUP)e.sequence=u,e.generateMessageID(o.getMyUserID());else if(e.conversationType===so.CONV_C2C){var d=c.getLatestMessageSentByMe(e.conversationID);if(d){var p=d.nick,g=d.avatar;p===e.nick&&g===e.avatar||(l=!0)}}if(c.appendToMessageList(e),l&&c.modifyMessageSentByMe({conversationID:e.conversationID,latestNick:e.nick,latestAvatar:e.avatar}),r.isOnlineMessage(e,t))e._onlineOnlyFlag=!0;else{var h=e;Yi(t)&&Yi(t.messageControlInfo)&&(!0===t.messageControlInfo.excludedFromLastMessage&&(e._isExcludedFromLastMessage=!0,h=""),!0===t.messageControlInfo.excludedFromUnreadCount&&(e._isExcludedFromUnreadCount=!0)),c.onMessageSent({conversationOptionsList:[{conversationID:e.conversationID,unreadCount:0,type:e.conversationType,subType:e.conversationSubType,lastMessage:h}]})}return e.getRelayFlag()||"TIMImageElem"!==e.type||Cu(e.payload.imageInfoArray),cm({message:e})}))})).catch((function(t){return o._onSendMessageFailed(e,t)}))}},{key:"_onSendMessageFailed",value:function(e,t){e.status=Cc.FAIL,this.getModule(Wc).deleteMessageRandom(e),this._addSendMessageFailCountOnUser(e,t);var n=new Zp(hg);return n.setMessage("tjg_id:".concat(this.generateTjgID(e)," type:").concat(e.type," from:").concat(e.from," to:").concat(e.to)),this.probeNetwork().then((function(e){var o=Qn(e,2),r=o[0],a=o[1];n.setError(t,r,a).end()})),Bi.error("".concat(this._className,"._onSendMessageFailed error:"),t),Mm(new fm({code:t&&t.code?t.code:kd.MESSAGE_SEND_FAIL,message:t&&t.message?t.message:Ud,data:{message:e}}))}},{key:"_getSendMessageSpecifiedKey",value:function(e){if([so.MSG_IMAGE,so.MSG_AUDIO,so.MSG_VIDEO,so.MSG_FILE].includes(e.type))return Hp;if(e.conversationType===so.CONV_C2C)return Vp;if(e.conversationType===so.CONV_GROUP){var t=this.getModule(Kc).getLocalGroupProfile(e.to);if(!t)return;var n=t.type;return _u(n)?Kp:Bp}}},{key:"_addSendMessageTotalCount",value:function(e){var t=this._getSendMessageSpecifiedKey(e);t&&this.getModule(il).addTotalCount(t)}},{key:"_addSendMessageSuccessCount",value:function(e,t){var n=Math.abs(Date.now()-t),o=this._getSendMessageSpecifiedKey(e);if(o){var r=this.getModule(il);r.addSuccessCount(o),r.addCost(o,n)}}},{key:"_addSendMessageFailCountOnUser",value:function(e,t){var n,o,r=t.code,a=void 0===r?-1:r,s=this.getModule(il),i=this._getSendMessageSpecifiedKey(e);i===Hp&&(n=a,o=!1,Rv.includes(n)&&(o=!0),o)?s.addFailedCountOfUserSide(jp):function(e){var t=!1;return Object.values(Ov).includes(e)&&(t=!0),(e>=120001&&e<=13e4||e>=10100&&e<=10200)&&(t=!0),t}(a)&&i&&s.addFailedCountOfUserSide(i)}},{key:"resendMessage",value:function(e){return e.isResend=!0,e.status=Cc.UNSEND,e.random=su(),e.generateMessageID(this.getMyUserID()),this.sendMessageInstance(e)}},{key:"revokeMessage",value:function(e){var t=this,n=null;if(e.conversationType===so.CONV_C2C){if(!(n=this.getModule(Bc)))return this._onCannotFindModule()}else if(e.conversationType===so.CONV_GROUP&&!(n=this.getModule(Kc)))return this._onCannotFindModule();var o=new Zp(mg);return o.setMessage("tjg_id:".concat(this.generateTjgID(e)," type:").concat(e.type," from:").concat(e.from," to:").concat(e.to)),n.revokeMessage(e).then((function(n){var r=n.data.recallRetList;if(!Nu(r)&&0!==r[0].retCode){var a=new fm({code:r[0].retCode,message:hm[r[0].retCode]||Bd,data:{message:e}});return o.setCode(a.code).setMoreMessage(a.message).end(),Mm(a)}return Bi.info("".concat(t._className,".revokeMessage ok. ID:").concat(e.ID)),e.isRevoked=!0,o.end(),t.getModule(Wc).onMessageRevoked([e]),cm({message:e})})).catch((function(n){t.probeNetwork().then((function(e){var t=Qn(e,2),r=t[0],a=t[1];o.setError(n,r,a).end()}));var r=new fm({code:n&&n.code?n.code:kd.MESSAGE_REVOKE_FAIL,message:n&&n.message?n.message:Bd,data:{message:e}});return Bi.warn("".concat(t._className,".revokeMessage failed. error:"),n),Mm(r)}))}},{key:"deleteMessage",value:function(e){var t=this,n=null,o=e[0],r=o.conversationID,a="",s=[],i=[];if(o.conversationType===so.CONV_C2C?(n=this.getModule(Bc),a=r.replace(so.CONV_C2C,""),e.forEach((function(e){e&&e.status===Cc.SUCCESS&&e.conversationID===r&&(e._onlineOnlyFlag||s.push("".concat(e.sequence,"_").concat(e.random,"_").concat(e.time)),i.push(e))}))):o.conversationType===so.CONV_GROUP&&(n=this.getModule(Kc),a=r.replace(so.CONV_GROUP,""),e.forEach((function(e){e&&e.status===Cc.SUCCESS&&e.conversationID===r&&(e._onlineOnlyFlag||s.push("".concat(e.sequence)),i.push(e))}))),!n)return this._onCannotFindModule();if(0===s.length)return this._onMessageDeleted(i);s.length>30&&(s=s.slice(0,30),i=i.slice(0,30));var u=new Zp(vg);return u.setMessage("to:".concat(a," count:").concat(s.length)),n.deleteMessage({to:a,keyList:s}).then((function(e){return u.end(),Bi.info("".concat(t._className,".deleteMessage ok")),t._onMessageDeleted(i)})).catch((function(e){t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];u.setError(e,o,r).end()})),Bi.warn("".concat(t._className,".deleteMessage failed. error:"),e);var n=new fm({code:e&&e.code?e.code:kd.MESSAGE_DELETE_FAIL,message:e&&e.message?e.message:Kd});return Mm(n)}))}},{key:"_onMessageDeleted",value:function(e){return this.getModule(Wc).onMessageDeleted(e),vm({messageList:e})}},{key:"_generateUUID",value:function(){var e=this.getModule(Yc);return"".concat(e.getSDKAppID(),"-").concat(e.getUserID(),"-").concat(function(){for(var e="",t=32;t>0;--t)e+=iu[Math.floor(Math.random()*uu)];return e}())}},{key:"getMessageOptionByID",value:function(e){return this._messageOptionsMap.get(e)}},{key:"_getNickAndAvatarByUserID",value:function(e){return this.getModule(Vc).getNickAndAvatarByUserID(e)}},{key:"_getNameCardByGroupID",value:function(e){if(e.conversationType===so.CONV_GROUP){var t=this.getModule(Kc);if(t)return t.getMyNameCardByGroupID(e.to)}return""}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._messageOptionsMap.clear()}}]),n}(cl),bv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="PluginModule",o.plugins={},o}return Un(n,[{key:"registerPlugin",value:function(e){var t=this;Object.keys(e).forEach((function(n){t.plugins[n]=e[n]})),new Zp(ag).setMessage("key=".concat(Object.keys(e))).end()}},{key:"getPlugin",value:function(e){return this.plugins[e]}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset"))}}]),n}(cl),wv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="SyncUnreadMessageModule",o._cookie="",o._onlineSyncFlag=!1,o.getInnerEmitterInstance().on(Om,o._onLoginSuccess,zn(o)),o}return Un(n,[{key:"_onLoginSuccess",value:function(e){this._startSync({cookie:this._cookie,syncFlag:0,isOnlineSync:0})}},{key:"_startSync",value:function(e){var t=this,n=e.cookie,o=e.syncFlag,r=e.isOnlineSync;Bi.log("".concat(this._className,"._startSync cookie:").concat(n," syncFlag:").concat(o," isOnlineSync:").concat(r)),this.request({protocolName:hl,requestData:{cookie:n,syncFlag:o,isOnlineSync:r}}).then((function(e){var n=e.data,o=n.cookie,r=n.syncFlag,a=n.eventArray,s=n.messageList,i=n.C2CRemainingUnreadList,u=n.C2CPairUnreadList;t._cookie=o,Nu(o)||(0===r||1===r?(a&&t.getModule(tl).onMessage({head:{},body:{eventArray:a,isInstantMessage:t._onlineSyncFlag,isSyncingEnded:!1}}),t.getModule(Bc).onNewC2CMessage({dataList:s,isInstantMessage:!1,C2CRemainingUnreadList:i,C2CPairUnreadList:u}),t._startSync({cookie:o,syncFlag:r,isOnlineSync:0})):2===r&&(a&&t.getModule(tl).onMessage({head:{},body:{eventArray:a,isInstantMessage:t._onlineSyncFlag,isSyncingEnded:!0}}),t.getModule(Bc).onNewC2CMessage({dataList:s,isInstantMessage:t._onlineSyncFlag,C2CRemainingUnreadList:i,C2CPairUnreadList:u})))})).catch((function(e){Bi.error("".concat(t._className,"._startSync failed. error:"),e)}))}},{key:"startOnlineSync",value:function(){Bi.log("".concat(this._className,".startOnlineSync")),this._onlineSyncFlag=!0,this._startSync({cookie:this._cookie,syncFlag:0,isOnlineSync:1})}},{key:"startSyncOnReconnected",value:function(){Bi.log("".concat(this._className,".startSyncOnReconnected.")),this._onlineSyncFlag=!0,this._startSync({cookie:this._cookie,syncFlag:0,isOnlineSync:0})}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._onlineSyncFlag=!1,this._cookie=""}}]),n}(cl),Gv={request:{toAccount:"To_Account",fromAccount:"From_Account",to:"To_Account",from:"From_Account",groupID:"GroupId",groupAtUserID:"GroupAt_Account",extension:"Ext",data:"Data",description:"Desc",elements:"MsgBody",sizeType:"Type",downloadFlag:"Download_Flag",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",videoUrl:"",imageUrl:"URL",fileUrl:"Url",uuid:"UUID",priority:"MsgPriority",receiverUserID:"To_Account",receiverGroupID:"GroupId",messageSender:"SenderId",messageReceiver:"ReceiverId",nick:"From_AccountNick",avatar:"From_AccountHeadurl",messageNumber:"MsgNum",pbDownloadKey:"PbMsgKey",downloadKey:"JsonMsgKey",applicationType:"PendencyType",userIDList:"To_Account",groupNameList:"GroupName",userID:"To_Account",groupAttributeList:"GroupAttr",mainSequence:"AttrMainSeq",avChatRoomKey:"BytesKey",attributeControl:"AttrControl",sequence:"seq",messageControlInfo:"SendMsgControl",updateSequence:"UpdateSeq"},response:{MsgPriority:"priority",ThumbUUID:"thumbUUID",VideoUUID:"videoUUID",Download_Flag:"downloadFlag",GroupId:"groupID",Member_Account:"userID",MsgList:"messageList",SyncFlag:"syncFlag",To_Account:"to",From_Account:"from",MsgSeq:"sequence",MsgRandom:"random",MsgTime:"time",MsgTimeStamp:"time",MsgContent:"content",MsgBody:"elements",From_AccountNick:"nick",From_AccountHeadurl:"avatar",GroupWithdrawInfoArray:"revokedInfos",GroupReadInfoArray:"groupMessageReadNotice",LastReadMsgSeq:"lastMessageSeq",WithdrawC2cMsgNotify:"c2cMessageRevokedNotify",C2cWithdrawInfoArray:"revokedInfos",C2cReadedReceipt:"c2cMessageReadReceipt",ReadC2cMsgNotify:"c2cMessageReadNotice",LastReadTime:"peerReadTime",MsgRand:"random",MsgType:"type",MsgShow:"messageShow",NextMsgSeq:"nextMessageSeq",FaceUrl:"avatar",ProfileDataMod:"profileModify",Profile_Account:"userID",ValueBytes:"value",ValueNum:"value",NoticeSeq:"noticeSequence",NotifySeq:"notifySequence",MsgFrom_AccountExtraInfo:"messageFromAccountExtraInformation",Operator_Account:"operatorID",OpType:"operationType",ReportType:"operationType",UserId:"userID",User_Account:"userID",List_Account:"userIDList",MsgOperatorMemberExtraInfo:"operatorInfo",MsgMemberExtraInfo:"memberInfoList",ImageUrl:"avatar",NickName:"nick",MsgGroupNewInfo:"newGroupProfile",MsgAppDefinedData:"groupCustomField",Owner_Account:"ownerID",GroupFaceUrl:"avatar",GroupIntroduction:"introduction",GroupNotification:"notification",GroupApplyJoinOption:"joinOption",MsgKey:"messageKey",GroupInfo:"groupProfile",ShutupTime:"muteTime",Desc:"description",Ext:"extension",GroupAt_Account:"groupAtUserID",MsgNum:"messageNumber",PbMsgKey:"pbDownloadKey",JsonMsgKey:"downloadKey",MsgModifiedFlag:"isModified",PendencyItem:"applicationItem",PendencyType:"applicationType",AddTime:"time",AddSource:"source",AddWording:"wording",ProfileImImage:"avatar",PendencyAdd:"friendApplicationAdded",FrienPencydDel_Account:"friendApplicationDeletedUserIDList",Peer_Account:"userID",GroupAttr:"groupAttributeList",GroupAttrAry:"groupAttributeList",AttrMainSeq:"mainSequence",seq:"sequence",GroupAttrOption:"groupAttributeOption",BytesChangedKeys:"changedKeyList",GroupAttrInfo:"groupAttributeList",GroupAttrSeq:"mainSequence",PushChangedAttrValFlag:"hasChangedAttributeInfo",SubKeySeq:"sequence",Val:"value",MsgGroupFromCardName:"senderNameCard",MsgGroupFromNickName:"senderNick",C2cNick:"peerNick",C2cImage:"peerAvatar",SendMsgControl:"messageControlInfo",NoLastMsg:"excludedFromLastMessage",NoUnread:"excludedFromUnreadCount",UpdateSeq:"updateSequence",MuteNotifications:"muteFlag"},ignoreKeyWord:["C2C","ID","USP"]},Pv=Qt.trim;function Uv(e,t){if("string"!=typeof e&&!Array.isArray(e))throw new TypeError("Expected the input to be `string | string[]`");var n;return t=Object.assign({pascalCase:!1},t),0===(e=Array.isArray(e)?e.map((function(e){return e.trim()})).filter((function(e){return e.length})).join("-"):e.trim()).length?"":1===e.length?t.pascalCase?e.toUpperCase():e.toLowerCase():(e!==e.toLowerCase()&&(e=Fv(e)),e=e.replace(/^[_.\- ]+/,"").toLowerCase().replace(/[_.\- ]+(\w|$)/g,(function(e,t){return t.toUpperCase()})).replace(/\d+(\w|$)/g,(function(e){return e.toUpperCase()})),n=e,t.pascalCase?n.charAt(0).toUpperCase()+n.slice(1):n)}be({target:"String",proto:!0,forced:function(e){return a((function(){return!!Wt[e]()||"​᠎"!="​᠎"[e]()||Wt[e].name!==e}))}("trim")},{trim:function(){return Pv(this)}});var Fv=function(e){for(var t=!1,n=!1,o=!1,r=0;r<e.length;r++){var a=e[r];t&&/[a-zA-Z]/.test(a)&&a.toUpperCase()===a?(e=e.slice(0,r)+"-"+e.slice(r),t=!1,o=n,n=!0,r++):n&&o&&/[a-zA-Z]/.test(a)&&a.toLowerCase()===a?(e=e.slice(0,r-1)+"-"+e.slice(r-1),o=n,n=!1,t=!0):(t=a.toLowerCase()===a&&a.toUpperCase()!==a,o=n,n=a.toUpperCase()===a&&a.toLowerCase()!==a)}return e};function qv(e,t){var n=0;return function e(t,o){if(++n>100)return n--,t;if(zi(t)){var r=t.map((function(t){return Wi(t)?e(t,o):t}));return n--,r}if(Wi(t)){var a=(s=t,i=function(e,t){if(!eu(t))return!1;if((r=t)!==Uv(r))for(var n=0;n<Gv.ignoreKeyWord.length&&!t.includes(Gv.ignoreKeyWord[n]);n++);var r;return Ji(o[t])?function(e){return"OPPOChannelID"===e?e:e[0].toUpperCase()+Uv(e).slice(1)}(t):o[t]},u=Object.create(null),Object.keys(s).forEach((function(e){var t=i(s[e],e);t&&(u[t]=s[e])})),u);return a=yu(a,(function(t,n){return zi(t)||Wi(t)?e(t,o):t})),n--,a}var s,i,u}(e,t)}function xv(e,t){if(zi(e))return e.map((function(e){return Wi(e)?xv(e,t):e}));if(Wi(e)){var n=(o=e,r=function(e,n){return Ji(t[n])?Uv(n):t[n]},a={},Object.keys(o).forEach((function(e){a[r(o[e],e)]=o[e]})),a);return yu(n,(function(e){return zi(e)||Wi(e)?xv(e,t):e}))}var o,r,a}var Vv="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView,Bv=function(e){if(void 0===e)return 0;var t=ce(e),n=de(t);if(t!==n)throw RangeError("Wrong length or index");return n},Kv=Math.abs,Hv=Math.pow,jv=Math.floor,Wv=Math.log,Yv=Math.LN2,zv=function(e,t,n){var o,r,a,s=new Array(n),i=8*n-t-1,u=(1<<i)-1,c=u>>1,l=23===t?Hv(2,-24)-Hv(2,-77):0,d=e<0||0===e&&1/e<0?1:0,p=0;for((e=Kv(e))!=e||e===1/0?(r=e!=e?1:0,o=u):(o=jv(Wv(e)/Yv),e*(a=Hv(2,-o))<1&&(o--,a*=2),(e+=o+c>=1?l/a:l*Hv(2,1-c))*a>=2&&(o++,a/=2),o+c>=u?(r=0,o=u):o+c>=1?(r=(e*a-1)*Hv(2,t),o+=c):(r=e*Hv(2,c-1)*Hv(2,t),o=0));t>=8;s[p++]=255&r,r/=256,t-=8);for(o=o<<t|r,i+=t;i>0;s[p++]=255&o,o/=256,i-=8);return s[--p]|=128*d,s},Jv=function(e,t){var n,o=e.length,r=8*o-t-1,a=(1<<r)-1,s=a>>1,i=r-7,u=o-1,c=e[u--],l=127&c;for(c>>=7;i>0;l=256*l+e[u],u--,i-=8);for(n=l&(1<<-i)-1,l>>=-i,i+=t;i>0;n=256*n+e[u],u--,i-=8);if(0===l)l=1-s;else{if(l===a)return n?NaN:c?-1/0:1/0;n+=Hv(2,t),l-=s}return(c?-1:1)*n*Hv(2,l-t)},Xv=function(e){for(var t=Ge(this),n=de(t.length),o=arguments.length,r=he(o>1?arguments[1]:void 0,n),a=o>2?arguments[2]:void 0,s=void 0===a?n:he(a,n);s>r;)t[r++]=e;return t},Qv=Ie.f,$v=O.f,Zv=ne.get,eM=ne.set,tM=r.ArrayBuffer,nM=tM,oM=r.DataView,rM=oM&&oM.prototype,aM=Object.prototype,sM=r.RangeError,iM=zv,uM=Jv,cM=function(e){return[255&e]},lM=function(e){return[255&e,e>>8&255]},dM=function(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]},pM=function(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]},gM=function(e){return iM(e,23,4)},hM=function(e){return iM(e,52,8)},fM=function(e,t){$v(e.prototype,t,{get:function(){return Zv(this)[t]}})},_M=function(e,t,n,o){var r=Bv(n),a=Zv(e);if(r+t>a.byteLength)throw sM("Wrong index");var s=Zv(a.buffer).bytes,i=r+a.byteOffset,u=s.slice(i,i+t);return o?u:u.reverse()},mM=function(e,t,n,o,r,a){var s=Bv(n),i=Zv(e);if(s+t>i.byteLength)throw sM("Wrong index");for(var u=Zv(i.buffer).bytes,c=s+i.byteOffset,l=o(+r),d=0;d<t;d++)u[c+d]=l[a?d:t-d-1]};if(Vv){if(!a((function(){tM(1)}))||!a((function(){new tM(-1)}))||a((function(){return new tM,new tM(1.5),new tM(NaN),"ArrayBuffer"!=tM.name}))){for(var vM,MM=(nM=function(e){return Rr(this,nM),new tM(Bv(e))}).prototype=tM.prototype,yM=Qv(tM),IM=0;yM.length>IM;)(vM=yM[IM++])in nM||R(nM,vM,tM[vM]);MM.constructor=nM}In&&dn(rM)!==aM&&In(rM,aM);var TM=new oM(new nM(2)),CM=rM.setInt8;TM.setInt8(0,2147483648),TM.setInt8(1,2147483649),!TM.getInt8(0)&&TM.getInt8(1)||Dr(rM,{setInt8:function(e,t){CM.call(this,e,t<<24>>24)},setUint8:function(e,t){CM.call(this,e,t<<24>>24)}},{unsafe:!0})}else nM=function(e){Rr(this,nM,"ArrayBuffer");var t=Bv(e);eM(this,{bytes:Xv.call(new Array(t),0),byteLength:t}),i||(this.byteLength=t)},oM=function(e,t,n){Rr(this,oM,"DataView"),Rr(e,nM,"DataView");var o=Zv(e).byteLength,r=ce(t);if(r<0||r>o)throw sM("Wrong offset");if(r+(n=void 0===n?o-r:de(n))>o)throw sM("Wrong length");eM(this,{buffer:e,byteLength:n,byteOffset:r}),i||(this.buffer=e,this.byteLength=n,this.byteOffset=r)},i&&(fM(nM,"byteLength"),fM(oM,"buffer"),fM(oM,"byteLength"),fM(oM,"byteOffset")),Dr(oM.prototype,{getInt8:function(e){return _M(this,1,e)[0]<<24>>24},getUint8:function(e){return _M(this,1,e)[0]},getInt16:function(e){var t=_M(this,2,e,arguments.length>1?arguments[1]:void 0);return(t[1]<<8|t[0])<<16>>16},getUint16:function(e){var t=_M(this,2,e,arguments.length>1?arguments[1]:void 0);return t[1]<<8|t[0]},getInt32:function(e){return pM(_M(this,4,e,arguments.length>1?arguments[1]:void 0))},getUint32:function(e){return pM(_M(this,4,e,arguments.length>1?arguments[1]:void 0))>>>0},getFloat32:function(e){return uM(_M(this,4,e,arguments.length>1?arguments[1]:void 0),23)},getFloat64:function(e){return uM(_M(this,8,e,arguments.length>1?arguments[1]:void 0),52)},setInt8:function(e,t){mM(this,1,e,cM,t)},setUint8:function(e,t){mM(this,1,e,cM,t)},setInt16:function(e,t){mM(this,2,e,lM,t,arguments.length>2?arguments[2]:void 0)},setUint16:function(e,t){mM(this,2,e,lM,t,arguments.length>2?arguments[2]:void 0)},setInt32:function(e,t){mM(this,4,e,dM,t,arguments.length>2?arguments[2]:void 0)},setUint32:function(e,t){mM(this,4,e,dM,t,arguments.length>2?arguments[2]:void 0)},setFloat32:function(e,t){mM(this,4,e,gM,t,arguments.length>2?arguments[2]:void 0)},setFloat64:function(e,t){mM(this,8,e,hM,t,arguments.length>2?arguments[2]:void 0)}});mn(nM,"ArrayBuffer"),mn(oM,"DataView");var SM={ArrayBuffer:nM,DataView:oM},AM=SM.ArrayBuffer,EM=SM.DataView,kM=AM.prototype.slice,DM=a((function(){return!new AM(2).slice(1,void 0).byteLength}));be({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:DM},{slice:function(e,t){if(void 0!==kM&&void 0===t)return kM.call(D(this),e);for(var n=D(this).byteLength,o=he(e,n),r=he(void 0===t?n:t,n),a=new(wr(this,AM))(de(r-o)),s=new EM(this),i=new EM(a),u=0;o<r;)i.setUint8(u++,s.getUint8(o++));return a}});var NM,OM=O.f,RM=r.Int8Array,LM=RM&&RM.prototype,bM=r.Uint8ClampedArray,wM=bM&&bM.prototype,GM=RM&&dn(RM),PM=LM&&dn(LM),UM=Object.prototype,FM=UM.isPrototypeOf,qM=Be("toStringTag"),xM=H("TYPED_ARRAY_TAG"),VM=Vv&&!!In&&"Opera"!==Et(r.opera),BM=!1,KM={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},HM=function(e){return v(e)&&I(KM,Et(e))};for(NM in KM)r[NM]||(VM=!1);if((!VM||"function"!=typeof GM||GM===Function.prototype)&&(GM=function(){throw TypeError("Incorrect invocation")},VM))for(NM in KM)r[NM]&&In(r[NM],GM);if((!VM||!PM||PM===UM)&&(PM=GM.prototype,VM))for(NM in KM)r[NM]&&In(r[NM].prototype,PM);if(VM&&dn(wM)!==PM&&In(wM,PM),i&&!I(PM,qM))for(NM in BM=!0,OM(PM,qM,{get:function(){return v(this)?this[xM]:void 0}}),KM)r[NM]&&R(r[NM],xM,NM);var jM={NATIVE_ARRAY_BUFFER_VIEWS:VM,TYPED_ARRAY_TAG:BM&&xM,aTypedArray:function(e){if(HM(e))return e;throw TypeError("Target is not a typed array")},aTypedArrayConstructor:function(e){if(In){if(FM.call(GM,e))return e}else for(var t in KM)if(I(KM,NM)){var n=r[t];if(n&&(e===n||FM.call(n,e)))return e}throw TypeError("Target is not a typed array constructor")},exportTypedArrayMethod:function(e,t,n){if(i){if(n)for(var o in KM){var a=r[o];a&&I(a.prototype,e)&&delete a.prototype[e]}PM[e]&&!n||oe(PM,e,n?t:VM&&LM[e]||t)}},exportTypedArrayStaticMethod:function(e,t,n){var o,a;if(i){if(In){if(n)for(o in KM)(a=r[o])&&I(a,e)&&delete a[e];if(GM[e]&&!n)return;try{return oe(GM,e,n?t:VM&&RM[e]||t)}catch(YI){}}for(o in KM)!(a=r[o])||a[e]&&!n||oe(a,e,t)}},isView:function(e){var t=Et(e);return"DataView"===t||I(KM,t)},isTypedArray:HM,TypedArray:GM,TypedArrayPrototype:PM},WM=jM.NATIVE_ARRAY_BUFFER_VIEWS,YM=r.ArrayBuffer,zM=r.Int8Array,JM=!WM||!a((function(){zM(1)}))||!a((function(){new zM(-1)}))||!wt((function(e){new zM,new zM(null),new zM(1.5),new zM(e)}),!0)||a((function(){return 1!==new zM(new YM(2),1,void 0).length})),XM=function(e,t){var n=function(e){var t=ce(e);if(t<0)throw RangeError("The argument can't be less than 0");return t}(e);if(n%t)throw RangeError("Wrong offset");return n},QM=jM.aTypedArrayConstructor,$M=function(e){var t,n,o,r,a,s,i=Ge(e),u=arguments.length,c=u>1?arguments[1]:void 0,l=void 0!==c,d=Dt(i);if(null!=d&&!It(d))for(s=(a=d.call(i)).next,i=[];!(r=s.call(a)).done;)i.push(r.value);for(l&&u>2&&(c=rt(c,arguments[2],2)),n=de(i.length),o=new(QM(this))(n),t=0;n>t;t++)o[t]=l?c(i[t],t):i[t];return o};t((function(e){var t=Ie.f,n=it.forEach,o=ne.get,a=ne.set,u=O.f,c=k.f,l=Math.round,p=r.RangeError,g=SM.ArrayBuffer,h=SM.DataView,f=jM.NATIVE_ARRAY_BUFFER_VIEWS,_=jM.TYPED_ARRAY_TAG,m=jM.TypedArray,y=jM.TypedArrayPrototype,T=jM.aTypedArrayConstructor,C=jM.isTypedArray,S=function(e,t){for(var n=0,o=t.length,r=new(T(e))(o);o>n;)r[n]=t[n++];return r},A=function(e,t){u(e,t,{get:function(){return o(this)[t]}})},E=function(e){var t;return e instanceof g||"ArrayBuffer"==(t=Et(e))||"SharedArrayBuffer"==t},D=function(e,t){return C(e)&&"symbol"!=s(t)&&t in e&&String(+t)==String(t)},N=function(e,t){return D(e,t=M(t,!0))?d(2,e[t]):c(e,t)},L=function(e,t,n){return!(D(e,t=M(t,!0))&&v(n)&&I(n,"value"))||I(n,"get")||I(n,"set")||n.configurable||I(n,"writable")&&!n.writable||I(n,"enumerable")&&!n.enumerable?u(e,t,n):(e[t]=n.value,e)};i?(f||(k.f=N,O.f=L,A(y,"buffer"),A(y,"byteOffset"),A(y,"byteLength"),A(y,"length")),be({target:"Object",stat:!0,forced:!f},{getOwnPropertyDescriptor:N,defineProperty:L}),e.exports=function(e,s,i){var c=e.match(/\d+$/)[0]/8,d=e+(i?"Clamped":"")+"Array",M="get"+e,I="set"+e,T=r[d],A=T,k=A&&A.prototype,D={},N=function(e,t){u(e,t,{get:function(){return function(e,t){var n=o(e);return n.view[M](t*c+n.byteOffset,!0)}(this,t)},set:function(e){return function(e,t,n){var r=o(e);i&&(n=(n=l(n))<0?0:n>255?255:255&n),r.view[I](t*c+r.byteOffset,n,!0)}(this,t,e)},enumerable:!0})};f?JM&&(A=s((function(e,t,n,o){return Rr(e,A,d),or(v(t)?E(t)?void 0!==o?new T(t,XM(n,c),o):void 0!==n?new T(t,XM(n,c)):new T(t):C(t)?S(A,t):$M.call(A,t):new T(Bv(t)),e,A)})),In&&In(A,m),n(t(T),(function(e){e in A||R(A,e,T[e])})),A.prototype=k):(A=s((function(e,t,n,o){Rr(e,A,d);var r,s,i,u=0,l=0;if(v(t)){if(!E(t))return C(t)?S(A,t):$M.call(A,t);r=t,l=XM(n,c);var f=t.byteLength;if(void 0===o){if(f%c)throw p("Wrong length");if((s=f-l)<0)throw p("Wrong length")}else if((s=de(o)*c)+l>f)throw p("Wrong length");i=s/c}else i=Bv(t),r=new g(s=i*c);for(a(e,{buffer:r,byteOffset:l,byteLength:s,length:i,view:new h(r)});u<i;)N(e,u++)})),In&&In(A,m),k=A.prototype=Ht(y)),k.constructor!==A&&R(k,"constructor",A),_&&R(k,_,d),D[d]=A,be({global:!0,forced:A!=T,sham:!f},D),"BYTES_PER_ELEMENT"in A||R(A,"BYTES_PER_ELEMENT",c),"BYTES_PER_ELEMENT"in k||R(k,"BYTES_PER_ELEMENT",c),Or(d)}):e.exports=function(){}}))("Uint8",(function(e){return function(t,n,o){return e(this,t,n,o)}}));var ZM=Math.min,ey=[].copyWithin||function(e,t){var n=Ge(this),o=de(n.length),r=he(e,o),a=he(t,o),s=arguments.length>2?arguments[2]:void 0,i=ZM((void 0===s?o:he(s,o))-a,o-r),u=1;for(a<r&&r<a+i&&(u=-1,a+=i-1,r+=i-1);i-- >0;)a in n?n[r]=n[a]:delete n[r],r+=u,a+=u;return n},ty=jM.aTypedArray;(0,jM.exportTypedArrayMethod)("copyWithin",(function(e,t){return ey.call(ty(this),e,t,arguments.length>2?arguments[2]:void 0)}));var ny=it.every,oy=jM.aTypedArray;(0,jM.exportTypedArrayMethod)("every",(function(e){return ny(oy(this),e,arguments.length>1?arguments[1]:void 0)}));var ry=jM.aTypedArray;(0,jM.exportTypedArrayMethod)("fill",(function(e){return Xv.apply(ry(this),arguments)}));var ay=it.filter,sy=jM.aTypedArray,iy=jM.aTypedArrayConstructor;(0,jM.exportTypedArrayMethod)("filter",(function(e){for(var t=ay(sy(this),e,arguments.length>1?arguments[1]:void 0),n=wr(this,this.constructor),o=0,r=t.length,a=new(iy(n))(r);r>o;)a[o]=t[o++];return a}));var uy=it.find,cy=jM.aTypedArray;(0,jM.exportTypedArrayMethod)("find",(function(e){return uy(cy(this),e,arguments.length>1?arguments[1]:void 0)}));var ly=it.findIndex,dy=jM.aTypedArray;(0,jM.exportTypedArrayMethod)("findIndex",(function(e){return ly(dy(this),e,arguments.length>1?arguments[1]:void 0)}));var py=it.forEach,gy=jM.aTypedArray;(0,jM.exportTypedArrayMethod)("forEach",(function(e){py(gy(this),e,arguments.length>1?arguments[1]:void 0)}));var hy=_e.includes,fy=jM.aTypedArray;(0,jM.exportTypedArrayMethod)("includes",(function(e){return hy(fy(this),e,arguments.length>1?arguments[1]:void 0)}));var _y=_e.indexOf,vy=jM.aTypedArray;(0,jM.exportTypedArrayMethod)("indexOf",(function(e){return _y(vy(this),e,arguments.length>1?arguments[1]:void 0)}));var My=Be("iterator"),yy=r.Uint8Array,Iy=Po.values,Ty=Po.keys,Cy=Po.entries,Sy=jM.aTypedArray,Ay=jM.exportTypedArrayMethod,Ey=yy&&yy.prototype[My],ky=!!Ey&&("values"==Ey.name||null==Ey.name),Dy=function(){return Iy.call(Sy(this))};Ay("entries",(function(){return Cy.call(Sy(this))})),Ay("keys",(function(){return Ty.call(Sy(this))})),Ay("values",Dy,!ky),Ay(My,Dy,!ky);var Ny=jM.aTypedArray,Oy=[].join;(0,jM.exportTypedArrayMethod)("join",(function(e){return Oy.apply(Ny(this),arguments)}));var Ry=jM.aTypedArray;(0,jM.exportTypedArrayMethod)("lastIndexOf",(function(e){return em.apply(Ry(this),arguments)}));var Ly=it.map,by=jM.aTypedArray,wy=jM.aTypedArrayConstructor;(0,jM.exportTypedArrayMethod)("map",(function(e){return Ly(by(this),e,arguments.length>1?arguments[1]:void 0,(function(e,t){return new(wy(wr(e,e.constructor)))(t)}))}));var Gy=function(e){return function(t,n,o,r){ot(n);var a=Ge(t),s=f(a),i=de(a.length),u=e?i-1:0,c=e?-1:1;if(o<2)for(;;){if(u in s){r=s[u],u+=c;break}if(u+=c,e?u<0:i<=u)throw TypeError("Reduce of empty array with no initial value")}for(;e?u>=0:i>u;u+=c)u in s&&(r=n(r,s[u],u,a));return r}},Py={left:Gy(!1),right:Gy(!0)},Uy=Py.left,Fy=jM.aTypedArray;(0,jM.exportTypedArrayMethod)("reduce",(function(e){return Uy(Fy(this),e,arguments.length,arguments.length>1?arguments[1]:void 0)}));var qy=Py.right,xy=jM.aTypedArray;(0,jM.exportTypedArrayMethod)("reduceRight",(function(e){return qy(xy(this),e,arguments.length,arguments.length>1?arguments[1]:void 0)}));var Vy=jM.aTypedArray,By=jM.exportTypedArrayMethod,Ky=Math.floor;By("reverse",(function(){for(var e,t=Vy(this).length,n=Ky(t/2),o=0;o<n;)e=this[o],this[o++]=this[--t],this[t]=e;return this}));var Hy=jM.aTypedArray;(0,jM.exportTypedArrayMethod)("set",(function(e){Hy(this);var t=XM(arguments.length>1?arguments[1]:void 0,1),n=this.length,o=Ge(e),r=de(o.length),a=0;if(r+t>n)throw RangeError("Wrong length");for(;a<r;)this[t+a]=o[a++]}),a((function(){new Int8Array(1).set({})})));var jy=jM.aTypedArray,Wy=jM.aTypedArrayConstructor,Yy=[].slice;(0,jM.exportTypedArrayMethod)("slice",(function(e,t){for(var n=Yy.call(jy(this),e,t),o=wr(this,this.constructor),r=0,a=n.length,s=new(Wy(o))(a);a>r;)s[r]=n[r++];return s}),a((function(){new Int8Array(1).slice()})));var zy=it.some,Jy=jM.aTypedArray;(0,jM.exportTypedArrayMethod)("some",(function(e){return zy(Jy(this),e,arguments.length>1?arguments[1]:void 0)}));var Xy=jM.aTypedArray,Qy=[].sort;(0,jM.exportTypedArrayMethod)("sort",(function(e){return Qy.call(Xy(this),e)}));var $y=jM.aTypedArray;(0,jM.exportTypedArrayMethod)("subarray",(function(e,t){var n=$y(this),o=n.length,r=he(e,o);return new(wr(n,n.constructor))(n.buffer,n.byteOffset+r*n.BYTES_PER_ELEMENT,de((void 0===t?o:he(t,o))-r))}));var Zy=r.Int8Array,eI=jM.aTypedArray,tI=jM.exportTypedArrayMethod,nI=[].toLocaleString,oI=[].slice,rI=!!Zy&&a((function(){nI.call(new Zy(1))}));tI("toLocaleString",(function(){return nI.apply(rI?oI.call(eI(this)):eI(this),arguments)}),a((function(){return[1,2].toLocaleString()!=new Zy([1,2]).toLocaleString()}))||!a((function(){Zy.prototype.toLocaleString.call([1,2])})));var aI=jM.exportTypedArrayMethod,sI=r.Uint8Array,iI=sI&&sI.prototype||{},uI=[].toString,cI=[].join;a((function(){uI.call({})}))&&(uI=function(){return cI.call(this)});var lI=iI.toString!=uI;aI("toString",uI,lI);var dI=String.fromCharCode,pI=function(e){var t=0|e.charCodeAt(0);if(55296<=t)if(t<56320){var n=0|e.charCodeAt(1);if(56320<=n&&n<=57343){if((t=(t<<10)+n-56613888|0)>65535)return dI(240|t>>>18,128|t>>>12&63,128|t>>>6&63,128|63&t)}else t=65533}else t<=57343&&(t=65533);return t<=2047?dI(192|t>>>6,128|63&t):dI(224|t>>>12,128|t>>>6&63,128|63&t)},gI=function(e){for(var t=void 0===e?"":(""+e).replace(/[\x80-\uD7ff\uDC00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]?/g,pI),n=0|t.length,o=new Uint8Array(n),r=0;r<n;r=r+1|0)o[r]=0|t.charCodeAt(r);return o},hI=function(e){var t=e.charCodeAt(0),n=1114112,o=0,r=0|e.length,a="";switch(t>>>4){case 12:case 13:o=(n=(31&t)<<6|63&e.charCodeAt(1))<128?0:2;break;case 14:o=(n=(15&t)<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2))<2048?0:3;break;case 15:t>>>3==30&&(o=(n=(7&t)<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|e.charCodeAt(3))<65536?0:4)}for(o&&(r<o?o=0:n<65536?a=dI(n):n<1114112?a=dI(55296+((n=n-65664|0)>>>10)|0,56320+(1023&n)|0):o=0);o<r;o=o+1|0)a+="�";return a},fI=function(e){for(var t=new Uint8Array(e),n="",o=0,r=0|t.length;o<r;o=o+32768|0)n+=dI.apply(0,t.subarray(o,o+32768|0));return n.replace(/[\xc0-\xff][\x80-\xbf]+|[\x80-\xff]/g,hI)},_I=function(){function e(t){Gn(this,e),this._handler=t;var n=t.getURL();this._socket=null,this._id=su(),si?ri?(ui.connectSocket({url:n,header:{"content-type":"application/json"}}),ui.onSocketClose(this._onClose.bind(this)),ui.onSocketOpen(this._onOpen.bind(this)),ui.onSocketMessage(this._onMessage.bind(this)),ui.onSocketError(this._onError.bind(this))):(this._socket=ui.connectSocket({url:n,header:{"content-type":"application/json"},complete:function(){}}),this._socket.onClose(this._onClose.bind(this)),this._socket.onOpen(this._onOpen.bind(this)),this._socket.onMessage(this._onMessage.bind(this)),this._socket.onError(this._onError.bind(this))):ii&&(this._socket=new WebSocket(n),this._socket.binaryType="arraybuffer",this._socket.onopen=this._onOpen.bind(this),this._socket.onmessage=this._onMessage.bind(this),this._socket.onclose=this._onClose.bind(this),this._socket.onerror=this._onError.bind(this))}return Un(e,[{key:"getID",value:function(){return this._id}},{key:"_onOpen",value:function(){this._handler.onOpen({id:this._id})}},{key:"_onClose",value:function(e){this._handler.onClose({id:this._id,e:e})}},{key:"_onMessage",value:function(e){this._handler.onMessage({data:this._handler.canIUseBinaryFrame()?fI(e.data):e.data})}},{key:"_onError",value:function(e){this._handler.onError({id:this._id,e:e})}},{key:"close",value:function(e){if(ri)return ui.offSocketClose(),ui.offSocketMessage(),ui.offSocketOpen(),ui.offSocketError(),void ui.closeSocket();this._socket&&(si?(this._socket.onClose((function(){})),this._socket.onOpen((function(){})),this._socket.onMessage((function(){})),this._socket.onError((function(){}))):ii&&(this._socket.onopen=null,this._socket.onmessage=null,this._socket.onclose=null,this._socket.onerror=null),oi?this._socket.close({code:e}):this._socket.close(e),this._socket=null)}},{key:"send",value:function(e){ri?ui.sendSocketMessage({data:e.data,fail:function(){e.fail&&e.requestID&&e.fail(e.requestID)}}):this._socket&&(si?this._socket.send({data:this._handler.canIUseBinaryFrame()?gI(e.data).buffer:e.data,fail:function(){e.fail&&e.requestID&&e.fail(e.requestID)}}):ii&&this._socket.send(this._handler.canIUseBinaryFrame()?gI(e.data).buffer:e.data))}}]),e}(),mI=4e3,vI=4001,MI="connected",yI="connecting",II="disconnected",TI=function(){function e(t){Gn(this,e),this._channelModule=t,this._className="SocketHandler",this._promiseMap=new Map,this._readyState=II,this._simpleRequestMap=new Map,this.MAX_SIZE=100,this._startSequence=su(),this._startTs=0,this._reConnectFlag=!1,this._nextPingTs=0,this._reConnectCount=0,this.MAX_RECONNECT_COUNT=3,this._socketID=-1,this._random=0,this._socket=null,this._url="",this._onOpenTs=0,this._canIUseBinaryFrame=!0,this._setWebsocketHost(),this._initConnection()}return Un(e,[{key:"_setWebsocketHost",value:function(){var e=this._channelModule.getModule(Yc).getSDKAppID();this._channelModule.isOversea()&&Js.HOST.setCurrent(js),e>=2e7&&e<3e7&&Js.HOST.setCurrent(Ws),e>=3e7&&e<4e7&&Js.HOST.setCurrent(Ys),e>=4e7&&e<5e7&&Js.HOST.setCurrent(zs)}},{key:"_initConnection",value:function(){Ji(Js.HOST.CURRENT.BACKUP)||""===this._url?this._url=Js.HOST.CURRENT.DEFAULT:this._url===Js.HOST.CURRENT.DEFAULT?this._url=Js.HOST.CURRENT.BACKUP:this._url===Js.HOST.CURRENT.BACKUP&&(this._url=Js.HOST.CURRENT.DEFAULT),this._connect(),this._nextPingTs=0}},{key:"onCheckTimer",value:function(e){e%1==0&&this._checkPromiseMap()}},{key:"_checkPromiseMap",value:function(){var e=this;0!==this._promiseMap.size&&this._promiseMap.forEach((function(t,n){var o=t.reject,r=t.timestamp;Date.now()-r>=15e3&&(Bi.log("".concat(e._className,"._checkPromiseMap request timeout, delete requestID:").concat(n)),e._promiseMap.delete(n),o(new fm({code:kd.NETWORK_TIMEOUT,message:Rp})),e._channelModule.onRequestTimeout(n))}))}},{key:"onOpen",value:function(e){this._onOpenTs=Date.now();var t=e.id;this._socketID=t;var n=Date.now()-this._startTs;Bi.log("".concat(this._className,"._onOpen cost ").concat(n," ms. socketID:").concat(t)),new Zp(ug).setMessage(n).setCostTime(n).setMoreMessage("socketID:".concat(t)).end(),e.id===this._socketID&&(this._readyState=MI,this._reConnectCount=0,this._resend(),!0===this._reConnectFlag&&(this._channelModule.onReconnected(),this._reConnectFlag=!1),this._channelModule.onOpen())}},{key:"onClose",value:function(e){var t=new Zp(cg),n=e.id,o=e.e,r="sourceSocketID:".concat(n," currentSocketID:").concat(this._socketID," code:").concat(o.code," reason:").concat(o.reason),a=0;0!==this._onOpenTs&&(a=Date.now()-this._onOpenTs),t.setMessage(a).setCostTime(a).setMoreMessage(r).setCode(o.code).end(),Bi.log("".concat(this._className,"._onClose ").concat(r," onlineTime:").concat(a)),n===this._socketID&&(this._readyState=II,a<1e3?this._channelModule.onReconnectFailed():this._channelModule.onClose())}},{key:"onError",value:function(e){var t=e.id,n=e.e,o="sourceSocketID:".concat(t," currentSocketID:").concat(this._socketID);new Zp(lg).setMessage(n.errMsg||ru(n)).setMoreMessage(o).setLevel("error").end(),Bi.warn("".concat(this._className,"._onError"),n,o),t===this._socketID&&(this._readyState="",this._channelModule.onError())}},{key:"onMessage",value:function(e){var t;try{t=JSON.parse(e.data)}catch(YI){new Zp(Ag).setMessage(e.data).end()}if(t&&t.head){var n=this._getRequestIDFromHead(t.head),o=Su(t.head),r=xv(t.body,this._getResponseKeyMap(o));if(Bi.debug("".concat(this._className,".onMessage ret:").concat(JSON.stringify(r)," requestID:").concat(n," has:").concat(this._promiseMap.has(n))),this._setNextPingTs(),this._promiseMap.has(n)){var a=this._promiseMap.get(n),s=a.resolve,i=a.reject,u=a.timestamp;return this._promiseMap.delete(n),this._calcRTT(u),void(r.errorCode&&0!==r.errorCode?(this._channelModule.onErrorCodeNotZero(r),i(new fm({code:r.errorCode,message:r.errorInfo||""}))):s(cm(r)))}this._channelModule.onMessage({head:t.head,body:r})}}},{key:"_calcRTT",value:function(e){var t=Date.now()-e;this._channelModule.getModule(il).addRTT(t)}},{key:"_connect",value:function(){this._startTs=Date.now(),this._onOpenTs=0,this._socket=new _I(this),this._socketID=this._socket.getID(),this._readyState=yI,Bi.log("".concat(this._className,"._connect socketID:").concat(this._socketID," url:").concat(this.getURL())),new Zp(ig).setMessage("socketID:".concat(this._socketID," url:").concat(this.getURL())).end()}},{key:"getURL",value:function(){var e=this._channelModule.getModule(Yc);return this._canIUseBinaryFrame=!e.isDevMode()&&!ri,this._canIUseBinaryFrame?"".concat(this._url,"/binfo?sdkappid=").concat(e.getSDKAppID(),"&instanceid=").concat(e.getInstanceID(),"&random=").concat(this._getRandom()):"".concat(this._url,"/info?sdkappid=").concat(e.getSDKAppID(),"&instanceid=").concat(e.getInstanceID(),"&random=").concat(this._getRandom())}},{key:"_closeConnection",value:function(e){Bi.log("".concat(this._className,"._closeConnection")),this._socket&&(this._socket.close(e),this._socketID=-1,this._socket=null,this._readyState=II)}},{key:"_resend",value:function(){var e=this;if(Bi.log("".concat(this._className,"._resend reConnectFlag:").concat(this._reConnectFlag),"promiseMap.size:".concat(this._promiseMap.size," simpleRequestMap.size:").concat(this._simpleRequestMap.size)),this._promiseMap.size>0&&this._promiseMap.forEach((function(t,n){var o=t.uplinkData,r=t.resolve,a=t.reject;e._promiseMap.set(n,{resolve:r,reject:a,timestamp:Date.now(),uplinkData:o}),e._execute(n,o)})),this._simpleRequestMap.size>0){var t,n=ro(this._simpleRequestMap);try{for(n.s();!(t=n.n()).done;){var o=Qn(t.value,2),r=o[0],a=o[1];this._execute(r,a)}}catch(u){n.e(u)}finally{n.f()}this._simpleRequestMap.clear()}}},{key:"send",value:function(e){var t=this;e.head.seq=this._getSequence(),e.head.reqtime=Math.floor(Date.now()/1e3),e.keyMap;var n=Yn(e,["keyMap"]),o=this._getRequestIDFromHead(e.head),r=JSON.stringify(n);return new Promise((function(e,a){t._promiseMap.set(o,{resolve:e,reject:a,timestamp:Date.now(),uplinkData:r}),Bi.debug("".concat(t._className,".send uplinkData:").concat(JSON.stringify(n)," requestID:").concat(o," readyState:").concat(t._readyState)),t._readyState!==MI?t._reConnect():(t._execute(o,r),t._channelModule.getModule(il).addRequestCount())}))}},{key:"simplySend",value:function(e){e.head.seq=this._getSequence(),e.head.reqtime=Math.floor(Date.now()/1e3),e.keyMap;var t=Yn(e,["keyMap"]),n=this._getRequestIDFromHead(e.head),o=JSON.stringify(t);this._readyState!==MI?(this._simpleRequestMap.size<this.MAX_SIZE?this._simpleRequestMap.set(n,o):Bi.log("".concat(this._className,".simplySend. simpleRequestMap is full, drop request!")),this._reConnect()):this._execute(n,o)}},{key:"_execute",value:function(e,t){this._socket.send({data:t,fail:si?this._onSendFail.bind(this):void 0,requestID:e})}},{key:"_onSendFail",value:function(e){Bi.log("".concat(this._className,"._onSendFail requestID:").concat(e))}},{key:"_getSequence",value:function(){var e;if(this._startSequence<2415919103)return e=this._startSequence,this._startSequence+=1,2415919103===this._startSequence&&(this._startSequence=su()),e}},{key:"_getRequestIDFromHead",value:function(e){return e.servcmd+e.seq}},{key:"_getResponseKeyMap",value:function(e){var t=this._channelModule.getKeyMap(e);return xn(xn({},Gv.response),t.response)}},{key:"_reConnect",value:function(){this._readyState!==MI&&this._readyState!==yI&&this.forcedReconnect()}},{key:"forcedReconnect",value:function(){var e=this;Bi.log("".concat(this._className,".forcedReconnect count:").concat(this._reConnectCount," readyState:").concat(this._readyState)),this._reConnectFlag=!0,this._resetRandom(),this._reConnectCount<this.MAX_RECONNECT_COUNT?(this._reConnectCount+=1,this._closeConnection(vI),this._initConnection()):this._channelModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0];n[1],o?(Bi.warn("".concat(e._className,".forcedReconnect disconnected from wsserver but network is ok, continue...")),e._reConnectCount=0,e._closeConnection(vI),e._initConnection()):e._channelModule.onReconnectFailed()}))}},{key:"getReconnectFlag",value:function(){return this._reConnectFlag}},{key:"_setNextPingTs",value:function(){this._nextPingTs=Date.now()+1e4}},{key:"getNextPingTs",value:function(){return this._nextPingTs}},{key:"isConnected",value:function(){return this._readyState===MI}},{key:"canIUseBinaryFrame",value:function(){return this._canIUseBinaryFrame}},{key:"_getRandom",value:function(){return 0===this._random&&(this._random=Math.random()),this._random}},{key:"_resetRandom",value:function(){this._random=0}},{key:"close",value:function(){Bi.log("".concat(this._className,".close")),this._closeConnection(mI),this._promiseMap.clear(),this._startSequence=su(),this._readyState=II,this._simpleRequestMap.clear(),this._reConnectFlag=!1,this._reConnectCount=0,this._onOpenTs=0,this._url="",this._random=0,this._canIUseBinaryFrame=!0}}]),e}(),CI=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;if(Gn(this,n),(o=t.call(this,e))._className="ChannelModule",o._socketHandler=new TI(zn(o)),o._probing=!1,o._isAppShowing=!0,o._previousState=so.NET_STATE_CONNECTED,si&&"function"==typeof ui.onAppShow&&"function"==typeof ui.onAppHide){var r=o._onAppHide.bind(zn(o)),a=o._onAppShow.bind(zn(o));"function"==typeof ui.offAppHide&&ui.offAppHide(r),"function"==typeof ui.offAppShow&&ui.offAppShow(a),ui.onAppHide(r),ui.onAppShow(a)}return o._timerForNotLoggedIn=-1,o._timerForNotLoggedIn=setInterval(o.onCheckTimer.bind(zn(o)),1e3),o._fatalErrorFlag=!1,o}return Un(n,[{key:"onCheckTimer",value:function(e){this._socketHandler&&(this.isLoggedIn()?(this._timerForNotLoggedIn>0&&(clearInterval(this._timerForNotLoggedIn),this._timerForNotLoggedIn=-1),this._socketHandler.onCheckTimer(e)):this._socketHandler.onCheckTimer(1),this._checkNextPing())}},{key:"onErrorCodeNotZero",value:function(e){this.getModule(tl).onErrorCodeNotZero(e)}},{key:"onMessage",value:function(e){this.getModule(tl).onMessage(e)}},{key:"send",value:function(e){return this._socketHandler?this._previousState!==so.NET_STATE_CONNECTED&&e.head.servcmd.includes(fd)?this._sendLogViaHTTP(e):this._socketHandler.send(e):Promise.reject()}},{key:"_sendLogViaHTTP",value:function(e){return new Promise((function(t,n){var o="https://webim.tim.qq.com/v4/imopenstat/tim_web_report_v2?sdkappid=".concat(e.head.sdkappid,"&reqtime=").concat(Date.now()),r=JSON.stringify(e.body),a="application/x-www-form-urlencoded;charset=UTF-8";if(si)ui.request({url:o,data:r,method:"POST",timeout:3e3,header:{"content-type":a},success:function(){t()},fail:function(){n(new fm({code:kd.NETWORK_ERROR,message:Op}))}});else{var s=new XMLHttpRequest,i=setTimeout((function(){s.abort(),n(new fm({code:kd.NETWORK_TIMEOUT,message:Rp}))}),3e3);s.onreadystatechange=function(){4===s.readyState&&(clearTimeout(i),200===s.status||304===s.status?t():n(new fm({code:kd.NETWORK_ERROR,message:Op})))},s.open("POST",o,!0),s.setRequestHeader("Content-type",a),s.send(r)}}))}},{key:"simplySend",value:function(e){return this._socketHandler?this._socketHandler.simplySend(e):Promise.reject()}},{key:"onOpen",value:function(){this._ping()}},{key:"onClose",value:function(){this.reConnect()}},{key:"onError",value:function(){si&&Bi.error("".concat(this._className,".onError 从v2.11.2起，SDK 支持了 WebSocket，如您未添加相关受信域名，请先添加！升级指引: https://web.sdk.qcloud.com/im/doc/zh-cn/tutorial-02-upgradeguideline.html"))}},{key:"getKeyMap",value:function(e){return this.getModule(tl).getKeyMap(e)}},{key:"_onAppHide",value:function(){this._isAppShowing=!1}},{key:"_onAppShow",value:function(){this._isAppShowing=!0}},{key:"onRequestTimeout",value:function(e){}},{key:"onReconnected",value:function(){Bi.log("".concat(this._className,".onReconnected")),this.getModule(tl).onReconnected(),this._emitNetStateChangeEvent(so.NET_STATE_CONNECTED)}},{key:"onReconnectFailed",value:function(){Bi.log("".concat(this._className,".onReconnectFailed")),this._emitNetStateChangeEvent(so.NET_STATE_DISCONNECTED)}},{key:"reConnect",value:function(){if(!this._fatalErrorFlag&&this._socketHandler){var e=this._socketHandler.getReconnectFlag();if(Bi.log("".concat(this._className,".reConnect previousState:").concat(this._previousState," reconnectFlag:").concat(e)),this._previousState===so.NET_STATE_CONNECTING&&e)return;this._socketHandler.forcedReconnect(),this._emitNetStateChangeEvent(so.NET_STATE_CONNECTING)}}},{key:"_emitNetStateChangeEvent",value:function(e){this._previousState!==e&&(this._previousState=e,this.emitOuterEvent(ao.NET_STATE_CHANGE,{state:e}))}},{key:"_ping",value:function(){var e=this;if(!0!==this._probing){this._probing=!0;var t=this.getModule(tl).getProtocolData({protocolName:_d});this.send(t).then((function(){e._probing=!1})).catch((function(t){if(Bi.warn("".concat(e._className,"._ping failed. error:"),t),e._probing=!1,t&&60002===t.code)return new Zp(Sh).setMessage("code:".concat(t.code," message:").concat(t.message)).setNetworkType(e.getModule(Xc).getNetworkType()).end(),e._fatalErrorFlag=!0,void e._emitNetStateChangeEvent(so.NET_STATE_DISCONNECTED);e.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];Bi.log("".concat(e._className,"._ping failed. isAppShowing:").concat(e._isAppShowing," online:").concat(o," networkType:").concat(r)),o?e.reConnect():e._emitNetStateChangeEvent(so.NET_STATE_DISCONNECTED)}))}))}}},{key:"_checkNextPing",value:function(){this._socketHandler&&this._socketHandler.isConnected()&&Date.now()>=this._socketHandler.getNextPingTs()&&this._ping()}},{key:"dealloc",value:function(){this._socketHandler&&(this._socketHandler.close(),this._socketHandler=null),this._timerForNotLoggedIn>-1&&clearInterval(this._timerForNotLoggedIn)}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._previousState=so.NET_STATE_CONNECTED,this._probing=!1,this._fatalErrorFlag=!1,this._timerForNotLoggedIn=setInterval(this.onCheckTimer.bind(this),1e3)}}]),n}(cl),SI=function(){function e(t){Gn(this,e),this._className="ProtocolHandler",this._sessionModule=t,this._configMap=new Map,this._fillConfigMap()}return Un(e,[{key:"_fillConfigMap",value:function(){this._configMap.clear();var e=this._sessionModule.genCommonHead(),t=this._sessionModule.genCosSpecifiedHead(),n=this._sessionModule.genSSOReportHead();this._configMap.set(ll,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_OPEN_STATUS,".").concat(Js.CMD.LOGIN)}),body:{state:"Online"},keyMap:{response:{TinyId:"tinyID",InstId:"instanceID",HelloInterval:"helloInterval"}}}}(e)),this._configMap.set(dl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_OPEN_STATUS,".").concat(Js.CMD.LOGOUT)}),body:{type:0},keyMap:{request:{type:"wslogout_type"}}}}(e)),this._configMap.set(pl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_OPEN_STATUS,".").concat(Js.CMD.HELLO)}),body:{},keyMap:{response:{NewInstInfo:"newInstanceInfo"}}}}(e)),this._configMap.set(gl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.STAT_SERVICE,".").concat(Js.CMD.KICK_OTHER)}),body:{}}}(e)),this._configMap.set(gd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_COS_SIGN,".").concat(Js.CMD.COS_SIGN)}),body:{cmd:"open_im_cos_svc",subCmd:"get_cos_token",duration:300,version:2},keyMap:{request:{userSig:"usersig",subCmd:"sub_cmd",cmd:"cmd",duration:"duration",version:"version"},response:{expired_time:"expiredTime",bucket_name:"bucketName",session_token:"sessionToken",tmp_secret_id:"secretId",tmp_secret_key:"secretKey"}}}}(t)),this._configMap.set(hd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.CUSTOM_UPLOAD,".").concat(Js.CMD.COS_PRE_SIG)}),body:{fileType:void 0,fileName:void 0,uploadMethod:0,duration:900},keyMap:{request:{userSig:"usersig",fileType:"file_type",fileName:"file_name",uploadMethod:"upload_method"},response:{expired_time:"expiredTime",request_id:"requestId",head_url:"headUrl",upload_url:"uploadUrl",download_url:"downloadUrl",ci_url:"ciUrl"}}}}(t)),this._configMap.set(Sd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_CONFIG_MANAGER,".").concat(Js.CMD.FETCH_COMMERCIAL_CONFIG)}),body:{SDKAppID:0},keyMap:{request:{SDKAppID:"uint32_sdkappid"},response:{int32_error_code:"errorCode",str_error_message:"errorMessage",str_purchase_bits:"purchaseBits",uint32_expired_time:"expiredTime"}}}}(e)),this._configMap.set(Ad,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_CONFIG_MANAGER,".").concat(Js.CMD.PUSHED_COMMERCIAL_CONFIG)}),body:{},keyMap:{response:{int32_error_code:"errorCode",str_error_message:"errorMessage",str_purchase_bits:"purchaseBits",uint32_expired_time:"expiredTime"}}}}(e)),this._configMap.set(Td,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_CONFIG_MANAGER,".").concat(Js.CMD.FETCH_CLOUD_CONTROL_CONFIG)}),body:{SDKAppID:0,version:0},keyMap:{request:{SDKAppID:"uint32_sdkappid",version:"uint64_version"},response:{int32_error_code:"errorCode",str_error_message:"errorMessage",str_json_config:"cloudControlConfig",uint32_expired_time:"expiredTime",uint32_sdkappid:"SDKAppID",uint64_version:"version"}}}}(e)),this._configMap.set(Cd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_CONFIG_MANAGER,".").concat(Js.CMD.PUSHED_CLOUD_CONTROL_CONFIG)}),body:{},keyMap:{response:{int32_error_code:"errorCode",str_error_message:"errorMessage",str_json_config:"cloudControlConfig",uint32_expired_time:"expiredTime",uint32_sdkappid:"SDKAppID",uint64_version:"version"}}}}(e)),this._configMap.set(Ed,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OVERLOAD_PUSH,".").concat(Js.CMD.OVERLOAD_NOTIFY)}),body:{},keyMap:{response:{OverLoadServCmd:"overloadCommand",DelaySecs:"waitingTime"}}}}(e)),this._configMap.set(hl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.GET_MESSAGES)}),body:{cookie:"",syncFlag:0,needAbstract:1,isOnlineSync:0},keyMap:{request:{fromAccount:"From_Account",toAccount:"To_Account",from:"From_Account",to:"To_Account",time:"MsgTimeStamp",sequence:"MsgSeq",random:"MsgRandom",elements:"MsgBody"},response:{MsgList:"messageList",SyncFlag:"syncFlag",To_Account:"to",From_Account:"from",ClientSeq:"clientSequence",MsgSeq:"sequence",NoticeSeq:"noticeSequence",NotifySeq:"notifySequence",MsgRandom:"random",MsgTimeStamp:"time",MsgContent:"content",ToGroupId:"groupID",MsgKey:"messageKey",GroupTips:"groupTips",MsgBody:"elements",MsgType:"type",C2CRemainingUnreadCount:"C2CRemainingUnreadList",C2CPairUnreadCount:"C2CPairUnreadList"}}}}(e)),this._configMap.set(fl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.BIG_DATA_HALLWAY_AUTH_KEY)}),body:{}}}(e)),this._configMap.set(_l,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.SEND_MESSAGE)}),body:{fromAccount:"",toAccount:"",msgTimeStamp:void 0,msgSeq:0,msgRandom:0,msgBody:[],cloudCustomData:void 0,nick:"",avatar:"",msgLifeTime:void 0,offlinePushInfo:{pushFlag:0,title:"",desc:"",ext:"",apnsInfo:{badgeMode:0},androidInfo:{OPPOChannelID:""}},messageControlInfo:void 0},keyMap:{request:{fromAccount:"From_Account",toAccount:"To_Account",msgTimeStamp:"MsgTimeStamp",msgSeq:"MsgSeq",msgRandom:"MsgRandom",msgBody:"MsgBody",count:"MaxCnt",lastMessageTime:"LastMsgTime",messageKey:"MsgKey",peerAccount:"Peer_Account",data:"Data",description:"Desc",extension:"Ext",type:"MsgType",content:"MsgContent",sizeType:"Type",uuid:"UUID",url:"",imageUrl:"URL",fileUrl:"Url",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",videoUrl:"",downloadFlag:"Download_Flag",nick:"From_AccountNick",avatar:"From_AccountHeadurl",from:"From_Account",time:"MsgTimeStamp",messageRandom:"MsgRandom",messageSequence:"MsgSeq",elements:"MsgBody",clientSequence:"ClientSeq",payload:"MsgContent",messageList:"MsgList",messageNumber:"MsgNum",abstractList:"AbstractList",messageBody:"MsgBody"}}}}(e)),this._configMap.set(ml,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.SEND_GROUP_MESSAGE)}),body:{fromAccount:"",groupID:"",random:0,clientSequence:0,priority:"",msgBody:[],cloudCustomData:void 0,onlineOnlyFlag:0,offlinePushInfo:{pushFlag:0,title:"",desc:"",ext:"",apnsInfo:{badgeMode:0},androidInfo:{OPPOChannelID:""}},groupAtInfo:[],messageControlInfo:void 0},keyMap:{request:{to:"GroupId",extension:"Ext",data:"Data",description:"Desc",random:"Random",sequence:"ReqMsgSeq",count:"ReqMsgNumber",type:"MsgType",priority:"MsgPriority",content:"MsgContent",elements:"MsgBody",sizeType:"Type",uuid:"UUID",url:"",imageUrl:"URL",fileUrl:"Url",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",videoUrl:"",downloadFlag:"Download_Flag",clientSequence:"ClientSeq",from:"From_Account",time:"MsgTimeStamp",messageRandom:"MsgRandom",messageSequence:"MsgSeq",payload:"MsgContent",messageList:"MsgList",messageNumber:"MsgNum",abstractList:"AbstractList",messageBody:"MsgBody"},response:{MsgTime:"time",MsgSeq:"sequence"}}}}(e)),this._configMap.set(Cl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.REVOKE_C2C_MESSAGE)}),body:{msgInfo:{fromAccount:"",toAccount:"",msgTimeStamp:0,msgSeq:0,msgRandom:0}},keyMap:{request:{msgInfo:"MsgInfo",msgTimeStamp:"MsgTimeStamp",msgSeq:"MsgSeq",msgRandom:"MsgRandom"}}}}(e)),this._configMap.set(Yl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.REVOKE_GROUP_MESSAGE)}),body:{to:"",msgSeqList:void 0},keyMap:{request:{to:"GroupId",msgSeqList:"MsgSeqList",msgSeq:"MsgSeq"}}}}(e)),this._configMap.set(kl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.GET_C2C_ROAM_MESSAGES)}),body:{peerAccount:"",count:15,lastMessageTime:0,messageKey:"",withRecalledMessage:1},keyMap:{request:{messageKey:"MsgKey",peerAccount:"Peer_Account",count:"MaxCnt",lastMessageTime:"LastMsgTime",withRecalledMessage:"WithRecalledMsg"},response:{LastMsgTime:"lastMessageTime"}}}}(e)),this._configMap.set(Xl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.GET_GROUP_ROAM_MESSAGES)}),body:{withRecalledMsg:1,groupID:"",count:15,sequence:""},keyMap:{request:{sequence:"ReqMsgSeq",count:"ReqMsgNumber",withRecalledMessage:"WithRecalledMsg"},response:{Random:"random",MsgTime:"time",MsgSeq:"sequence",ReqMsgSeq:"sequence",RspMsgList:"messageList",IsPlaceMsg:"isPlaceMessage",IsSystemMsg:"isSystemMessage",ToGroupId:"to",EnumFrom_AccountType:"fromAccountType",EnumTo_AccountType:"toAccountType",GroupCode:"groupCode",MsgPriority:"priority",MsgBody:"elements",MsgType:"type",MsgContent:"content",IsFinished:"complete",Download_Flag:"downloadFlag",ClientSeq:"clientSequence",ThumbUUID:"thumbUUID",VideoUUID:"videoUUID"}}}}(e)),this._configMap.set(Sl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.SET_C2C_MESSAGE_READ)}),body:{C2CMsgReaded:void 0},keyMap:{request:{lastMessageTime:"LastedMsgTime"}}}}(e)),this._configMap.set(Al,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.SET_C2C_PEER_MUTE_NOTIFICATIONS)}),body:{userIDList:void 0,muteFlag:0},keyMap:{request:{userIDList:"Peer_Account",muteFlag:"Mute_Notifications"}}}}(e)),this._configMap.set(El,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.GET_C2C_PEER_MUTE_NOTIFICATIONS)}),body:{updateSequence:0},keyMap:{response:{MuteNotificationsList:"muteFlagList"}}}}(e)),this._configMap.set(zl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.SET_GROUP_MESSAGE_READ)}),body:{groupID:void 0,messageReadSeq:void 0},keyMap:{request:{messageReadSeq:"MsgReadedSeq"}}}}(e)),this._configMap.set(Jl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.SET_ALL_MESSAGE_READ)}),body:{readAllC2CMessage:0,groupMessageReadInfoList:[]},keyMap:{request:{readAllC2CMessage:"C2CReadAllMsg",groupMessageReadInfoList:"GroupReadInfo",messageSequence:"MsgSeq"},response:{C2CReadAllMsg:"readAllC2CMessage",GroupReadInfoArray:"groupMessageReadInfoList"}}}}(e)),this._configMap.set(Nl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.DELETE_C2C_MESSAGE)}),body:{fromAccount:"",to:"",keyList:void 0},keyMap:{request:{keyList:"MsgKeyList"}}}}(e)),this._configMap.set(nd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.DELETE_GROUP_MESSAGE)}),body:{groupID:"",deleter:"",keyList:void 0},keyMap:{request:{deleter:"Deleter_Account",keyList:"Seqs"}}}}(e)),this._configMap.set(Dl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.GET_PEER_READ_TIME)}),body:{userIDList:void 0},keyMap:{request:{userIDList:"To_Account"},response:{ReadTime:"peerReadTimeList"}}}}(e)),this._configMap.set(Rl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.RECENT_CONTACT,".").concat(Js.CMD.GET_CONVERSATION_LIST)}),body:{fromAccount:void 0,count:0},keyMap:{request:{},response:{SessionItem:"conversations",ToAccount:"groupID",To_Account:"userID",UnreadMsgCount:"unreadCount",MsgGroupReadedSeq:"messageReadSeq",C2cPeerReadTime:"c2cPeerReadTime"}}}}(e)),this._configMap.set(Ol,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.RECENT_CONTACT,".").concat(Js.CMD.PAGING_GET_CONVERSATION_LIST)}),body:{fromAccount:void 0,timeStamp:void 0,startIndex:void 0,pinnedTimeStamp:void 0,pinnedStartIndex:void 0,orderType:void 0,messageAssistFlag:4,assistFlag:7},keyMap:{request:{messageAssistFlag:"MsgAssistFlags",assistFlag:"AssistFlags",pinnedTimeStamp:"TopTimeStamp",pinnedStartIndex:"TopStartIndex"},response:{SessionItem:"conversations",ToAccount:"groupID",To_Account:"userID",UnreadMsgCount:"unreadCount",MsgGroupReadedSeq:"messageReadSeq",C2cPeerReadTime:"c2cPeerReadTime",LastMsgFlags:"lastMessageFlag",TopFlags:"isPinned",TopTimeStamp:"pinnedTimeStamp",TopStartIndex:"pinnedStartIndex"}}}}(e)),this._configMap.set(Ll,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.RECENT_CONTACT,".").concat(Js.CMD.DELETE_CONVERSATION)}),body:{fromAccount:"",toAccount:void 0,type:1,toGroupID:void 0,clearHistoryMessage:1},keyMap:{request:{toGroupID:"ToGroupid",clearHistoryMessage:"ClearRamble"}}}}(e)),this._configMap.set(bl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.RECENT_CONTACT,".").concat(Js.CMD.PIN_CONVERSATION)}),body:{fromAccount:"",operationType:1,itemList:void 0},keyMap:{request:{itemList:"RecentContactItem"}}}}(e)),this._configMap.set(wl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.DELETE_GROUP_AT_TIPS)}),body:{messageListToDelete:void 0},keyMap:{request:{messageListToDelete:"DelMsgList",messageSeq:"MsgSeq",messageRandom:"MsgRandom"}}}}(e)),this._configMap.set(vl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.PROFILE,".").concat(Js.CMD.PORTRAIT_GET)}),body:{fromAccount:"",userItem:[]},keyMap:{request:{toAccount:"To_Account",standardSequence:"StandardSequence",customSequence:"CustomSequence"}}}}(e)),this._configMap.set(Ml,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.PROFILE,".").concat(Js.CMD.PORTRAIT_SET)}),body:{fromAccount:"",profileItem:[{tag:P_.NICK,value:""},{tag:P_.GENDER,value:""},{tag:P_.ALLOWTYPE,value:""},{tag:P_.AVATAR,value:""}]},keyMap:{request:{toAccount:"To_Account",standardSequence:"StandardSequence",customSequence:"CustomSequence"}}}}(e)),this._configMap.set(yl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.FRIEND,".").concat(Js.CMD.GET_BLACKLIST)}),body:{fromAccount:"",startIndex:0,maxLimited:30,lastSequence:0},keyMap:{response:{CurruentSequence:"currentSequence"}}}}(e)),this._configMap.set(Il,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.FRIEND,".").concat(Js.CMD.ADD_BLACKLIST)}),body:{fromAccount:"",toAccount:[]}}}(e)),this._configMap.set(Tl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.FRIEND,".").concat(Js.CMD.DELETE_BLACKLIST)}),body:{fromAccount:"",toAccount:[]}}}(e)),this._configMap.set(Gl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.GET_JOINED_GROUPS)}),body:{memberAccount:"",limit:void 0,offset:void 0,groupType:void 0,responseFilter:{groupBaseInfoFilter:void 0,selfInfoFilter:void 0}},keyMap:{request:{memberAccount:"Member_Account"},response:{GroupIdList:"groups",MsgFlag:"messageRemindType",NoUnreadSeqList:"excludedUnreadSequenceList",MsgSeq:"readedSequence"}}}}(e)),this._configMap.set(Pl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.GET_GROUP_INFO)}),body:{groupIDList:void 0,responseFilter:{groupBaseInfoFilter:["Type","Name","Introduction","Notification","FaceUrl","Owner_Account","CreateTime","InfoSeq","LastInfoTime","LastMsgTime","MemberNum","MaxMemberNum","ApplyJoinOption","NextMsgSeq","ShutUpAllMember"],groupCustomFieldFilter:void 0,memberInfoFilter:void 0,memberCustomFieldFilter:void 0}},keyMap:{request:{groupIDList:"GroupIdList",groupCustomField:"AppDefinedData",memberCustomField:"AppMemberDefinedData",groupCustomFieldFilter:"AppDefinedDataFilter_Group",memberCustomFieldFilter:"AppDefinedDataFilter_GroupMember"},response:{GroupIdList:"groups",MsgFlag:"messageRemindType",AppDefinedData:"groupCustomField",AppMemberDefinedData:"memberCustomField",AppDefinedDataFilter_Group:"groupCustomFieldFilter",AppDefinedDataFilter_GroupMember:"memberCustomFieldFilter",InfoSeq:"infoSequence",MemberList:"members",GroupInfo:"groups",ShutUpUntil:"muteUntil",ShutUpAllMember:"muteAllMembers",ApplyJoinOption:"joinOption"}}}}(e)),this._configMap.set(Ul,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.CREATE_GROUP)}),body:{type:void 0,name:void 0,groupID:void 0,ownerID:void 0,introduction:void 0,notification:void 0,maxMemberNum:void 0,joinOption:void 0,memberList:void 0,groupCustomField:void 0,memberCustomField:void 0,webPushFlag:1,avatar:"FaceUrl"},keyMap:{request:{ownerID:"Owner_Account",userID:"Member_Account",avatar:"FaceUrl",maxMemberNum:"MaxMemberCount",joinOption:"ApplyJoinOption",groupCustomField:"AppDefinedData",memberCustomField:"AppMemberDefinedData"},response:{HugeGroupFlag:"avChatRoomFlag",OverJoinedGroupLimit_Account:"overLimitUserIDList"}}}}(e)),this._configMap.set(Fl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.DESTROY_GROUP)}),body:{groupID:void 0}}}(e)),this._configMap.set(ql,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.MODIFY_GROUP_INFO)}),body:{groupID:void 0,name:void 0,introduction:void 0,notification:void 0,avatar:void 0,maxMemberNum:void 0,joinOption:void 0,groupCustomField:void 0,muteAllMembers:void 0},keyMap:{request:{maxMemberNum:"MaxMemberCount",groupCustomField:"AppDefinedData",muteAllMembers:"ShutUpAllMember",joinOption:"ApplyJoinOption",avatar:"FaceUrl"},response:{AppDefinedData:"groupCustomField",ShutUpAllMember:"muteAllMembers",ApplyJoinOption:"joinOption"}}}}(e)),this._configMap.set(xl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.APPLY_JOIN_GROUP)}),body:{groupID:void 0,applyMessage:void 0,userDefinedField:void 0,webPushFlag:1,historyMessageFlag:void 0},keyMap:{request:{applyMessage:"ApplyMsg",historyMessageFlag:"HugeGroupHistoryMsgFlag"},response:{HugeGroupFlag:"avChatRoomFlag",AVChatRoomKey:"avChatRoomKey",RspMsgList:"messageList",ToGroupId:"to"}}}}(e)),this._configMap.set(Vl,function(e){return e.a2,e.tinyid,{head:xn(xn({},Yn(e,["a2","tinyid"])),{},{servcmd:"".concat(Js.NAME.BIG_GROUP_NO_AUTH,".").concat(Js.CMD.APPLY_JOIN_GROUP)}),body:{groupID:void 0,applyMessage:void 0,userDefinedField:void 0,webPushFlag:1},keyMap:{request:{applyMessage:"ApplyMsg"},response:{HugeGroupFlag:"avChatRoomFlag"}}}}(e)),this._configMap.set(Bl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.QUIT_GROUP)}),body:{groupID:void 0}}}(e)),this._configMap.set(Kl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.SEARCH_GROUP_BY_ID)}),body:{groupIDList:void 0,responseFilter:{groupBasePublicInfoFilter:["Type","Name","Introduction","Notification","FaceUrl","CreateTime","Owner_Account","LastInfoTime","LastMsgTime","NextMsgSeq","MemberNum","MaxMemberNum","ApplyJoinOption"]}},keyMap:{response:{ApplyJoinOption:"joinOption"}}}}(e)),this._configMap.set(Hl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.CHANGE_GROUP_OWNER)}),body:{groupID:void 0,newOwnerID:void 0},keyMap:{request:{newOwnerID:"NewOwner_Account"}}}}(e)),this._configMap.set(jl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.HANDLE_APPLY_JOIN_GROUP)}),body:{groupID:void 0,applicant:void 0,handleAction:void 0,handleMessage:void 0,authentication:void 0,messageKey:void 0,userDefinedField:void 0},keyMap:{request:{applicant:"Applicant_Account",handleAction:"HandleMsg",handleMessage:"ApprovalMsg",messageKey:"MsgKey"}}}}(e)),this._configMap.set(Wl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.HANDLE_GROUP_INVITATION)}),body:{groupID:void 0,inviter:void 0,handleAction:void 0,handleMessage:void 0,authentication:void 0,messageKey:void 0,userDefinedField:void 0},keyMap:{request:{inviter:"Inviter_Account",handleAction:"HandleMsg",handleMessage:"ApprovalMsg",messageKey:"MsgKey"}}}}(e)),this._configMap.set(Ql,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.GET_GROUP_APPLICATION)}),body:{startTime:void 0,limit:void 0,handleAccount:void 0},keyMap:{request:{handleAccount:"Handle_Account"}}}}(e)),this._configMap.set($l,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.DELETE_GROUP_SYSTEM_MESSAGE)}),body:{messageListToDelete:void 0},keyMap:{request:{messageListToDelete:"DelMsgList",messageSeq:"MsgSeq",messageRandom:"MsgRandom"}}}}(e)),this._configMap.set(Zl,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.BIG_GROUP_LONG_POLLING,".").concat(Js.CMD.AVCHATROOM_LONG_POLL)}),body:{USP:1,startSeq:1,holdTime:90,key:void 0},keyMap:{request:{USP:"USP"},response:{ToGroupId:"groupID"}}}}(e)),this._configMap.set(ed,function(e){return e.a2,e.tinyid,{head:xn(xn({},Yn(e,["a2","tinyid"])),{},{servcmd:"".concat(Js.NAME.BIG_GROUP_LONG_POLLING_NO_AUTH,".").concat(Js.CMD.AVCHATROOM_LONG_POLL)}),body:{USP:1,startSeq:1,holdTime:90,key:void 0},keyMap:{request:{USP:"USP"},response:{ToGroupId:"groupID"}}}}(e)),this._configMap.set(td,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.GET_ONLINE_MEMBER_NUM)}),body:{groupID:void 0}}}(e)),this._configMap.set(od,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.SET_GROUP_ATTRIBUTES)}),body:{groupID:void 0,groupAttributeList:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]},keyMap:{request:{key:"key",value:"value"}}}}(e)),this._configMap.set(rd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.MODIFY_GROUP_ATTRIBUTES)}),body:{groupID:void 0,groupAttributeList:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]},keyMap:{request:{key:"key",value:"value"}}}}(e)),this._configMap.set(ad,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.DELETE_GROUP_ATTRIBUTES)}),body:{groupID:void 0,groupAttributeList:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]},keyMap:{request:{key:"key"}}}}(e)),this._configMap.set(sd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.CLEAR_GROUP_ATTRIBUTES)}),body:{groupID:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]}}}(e)),this._configMap.set(id,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP_ATTR,".").concat(Js.CMD.GET_GROUP_ATTRIBUTES)}),body:{groupID:void 0,avChatRoomKey:void 0,groupType:1},keyMap:{request:{avChatRoomKey:"Key",groupType:"GroupType"}}}}(e)),this._configMap.set(ud,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.GET_GROUP_MEMBER_LIST)}),body:{groupID:void 0,limit:0,offset:0,memberRoleFilter:void 0,memberInfoFilter:["Role","NameCard","ShutUpUntil","JoinTime"],memberCustomFieldFilter:void 0},keyMap:{request:{memberCustomFieldFilter:"AppDefinedDataFilter_GroupMember"},response:{AppMemberDefinedData:"memberCustomField",AppDefinedDataFilter_GroupMember:"memberCustomFieldFilter",MemberList:"members",ShutUpUntil:"muteUntil"}}}}(e)),this._configMap.set(cd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.GET_GROUP_MEMBER_INFO)}),body:{groupID:void 0,userIDList:void 0,memberInfoFilter:void 0,memberCustomFieldFilter:void 0},keyMap:{request:{userIDList:"Member_List_Account",memberCustomFieldFilter:"AppDefinedDataFilter_GroupMember"},response:{MemberList:"members",ShutUpUntil:"muteUntil",AppDefinedDataFilter_GroupMember:"memberCustomFieldFilter",AppMemberDefinedData:"memberCustomField"}}}}(e)),this._configMap.set(ld,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.ADD_GROUP_MEMBER)}),body:{groupID:void 0,silence:void 0,userIDList:void 0},keyMap:{request:{userID:"Member_Account",userIDList:"MemberList"},response:{MemberList:"members"}}}}(e)),this._configMap.set(dd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.DELETE_GROUP_MEMBER)}),body:{groupID:void 0,userIDList:void 0,reason:void 0},keyMap:{request:{userIDList:"MemberToDel_Account"}}}}(e)),this._configMap.set(pd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.GROUP,".").concat(Js.CMD.MODIFY_GROUP_MEMBER_INFO)}),body:{groupID:void 0,userID:void 0,messageRemindType:void 0,nameCard:void 0,role:void 0,memberCustomField:void 0,muteTime:void 0},keyMap:{request:{userID:"Member_Account",memberCustomField:"AppMemberDefinedData",muteTime:"ShutUpTime",messageRemindType:"MsgFlag"}}}}(e)),this._configMap.set(fd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_OPEN_STAT,".").concat(Js.CMD.TIM_WEB_REPORT_V2)}),body:{header:{},event:[],quality:[]},keyMap:{request:{SDKType:"sdk_type",SDKVersion:"sdk_version",deviceType:"device_type",platform:"platform",instanceID:"instance_id",traceID:"trace_id",SDKAppID:"sdk_app_id",userID:"user_id",tinyID:"tiny_id",extension:"extension",timestamp:"timestamp",networkType:"network_type",eventType:"event_type",code:"error_code",message:"error_message",moreMessage:"more_message",duplicate:"duplicate",costTime:"cost_time",level:"level",qualityType:"quality_type",reportIndex:"report_index",wholePeriod:"whole_period",totalCount:"total_count",rttCount:"success_count_business",successRateOfRequest:"percent_business",countLessThan1Second:"success_count_business",percentOfCountLessThan1Second:"percent_business",countLessThan3Second:"success_count_platform",percentOfCountLessThan3Second:"percent_platform",successCountOfBusiness:"success_count_business",successRateOfBusiness:"percent_business",successCountOfPlatform:"success_count_platform",successRateOfPlatform:"percent_platform",successCountOfMessageReceived:"success_count_business",successRateOfMessageReceived:"percent_business",avgRTT:"average_value",avgDelay:"average_value",avgValue:"average_value"}}}}(n)),this._configMap.set(_d,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.HEARTBEAT,".").concat(Js.CMD.ALIVE)}),body:{}}}(e)),this._configMap.set(md,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_OPEN_PUSH,".").concat(Js.CMD.MESSAGE_PUSH)}),body:{},keyMap:{response:{C2cMsgArray:"C2CMessageArray",GroupMsgArray:"groupMessageArray",GroupTips:"groupTips",C2cNotifyMsgArray:"C2CNotifyMessageArray",ClientSeq:"clientSequence",MsgPriority:"priority",NoticeSeq:"noticeSequence",MsgContent:"content",MsgType:"type",MsgBody:"elements",ToGroupId:"to",Desc:"description",Ext:"extension",IsSyncMsg:"isSyncMessage",Flag:"needSync",NeedAck:"needAck",PendencyAdd_Account:"userID",ProfileImNick:"nick",PendencyType:"applicationType",C2CReadAllMsg:"readAllC2CMessage"}}}}(e)),this._configMap.set(vd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.OPEN_IM,".").concat(Js.CMD.MESSAGE_PUSH_ACK)}),body:{sessionData:void 0},keyMap:{request:{sessionData:"SessionData"}}}}(e)),this._configMap.set(Md,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_OPEN_STATUS,".").concat(Js.CMD.STATUS_FORCEOFFLINE)}),body:{},keyMap:{response:{C2cNotifyMsgArray:"C2CNotifyMessageArray",NoticeSeq:"noticeSequence",KickoutMsgNotify:"kickoutMsgNotify",NewInstInfo:"newInstanceInfo"}}}}(e)),this._configMap.set(Id,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_LONG_MESSAGE,".").concat(Js.CMD.DOWNLOAD_MERGER_MESSAGE)}),body:{downloadKey:""},keyMap:{response:{Data:"data",Desc:"description",Ext:"extension",Download_Flag:"downloadFlag",ThumbUUID:"thumbUUID",VideoUUID:"videoUUID"}}}}(e)),this._configMap.set(yd,function(e){return{head:xn(xn({},e),{},{servcmd:"".concat(Js.NAME.IM_LONG_MESSAGE,".").concat(Js.CMD.UPLOAD_MERGER_MESSAGE)}),body:{messageList:[]},keyMap:{request:{fromAccount:"From_Account",toAccount:"To_Account",msgTimeStamp:"MsgTimeStamp",msgSeq:"MsgSeq",msgRandom:"MsgRandom",msgBody:"MsgBody",type:"MsgType",content:"MsgContent",data:"Data",description:"Desc",extension:"Ext",sizeType:"Type",uuid:"UUID",url:"",imageUrl:"URL",fileUrl:"Url",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",videoUrl:"",downloadFlag:"Download_Flag",from:"From_Account",time:"MsgTimeStamp",messageRandom:"MsgRandom",messageSequence:"MsgSeq",elements:"MsgBody",clientSequence:"ClientSeq",payload:"MsgContent",messageList:"MsgList",messageNumber:"MsgNum",abstractList:"AbstractList",messageBody:"MsgBody"}}}}(e))}},{key:"has",value:function(e){return this._configMap.has(e)}},{key:"get",value:function(e){return this._configMap.get(e)}},{key:"update",value:function(){this._fillConfigMap()}},{key:"getKeyMap",value:function(e){return this.has(e)?this.get(e).keyMap||{}:(Bi.warn("".concat(this._className,".getKeyMap unknown protocolName:").concat(e)),{})}},{key:"getProtocolData",value:function(e){var t=e.protocolName,n=e.requestData,o=this.get(t),r=null;if(n){var a=this._simpleDeepCopy(o),s=a.body,i=Object.create(null);for(var u in s)if(Object.prototype.hasOwnProperty.call(s,u)){if(i[u]=s[u],void 0===n[u])continue;i[u]=n[u]}a.body=i,r=this._getUplinkData(a)}else r=this._getUplinkData(o);return r}},{key:"_getUplinkData",value:function(e){var t=this._requestDataCleaner(e),n=Su(t.head),o=qv(t.body,this._getRequestKeyMap(n));return t.body=o,t}},{key:"_getRequestKeyMap",value:function(e){var t=this.getKeyMap(e);return xn(xn({},Gv.request),t.request)}},{key:"_requestDataCleaner",value:function(e){var t=Array.isArray(e)?[]:Object.create(null);for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&eu(n)&&null!==e[n]&&void 0!==e[n]&&("object"!==wn(e[n])?t[n]=e[n]:t[n]=this._requestDataCleaner.bind(this)(e[n]));return t}},{key:"_simpleDeepCopy",value:function(e){for(var t,n=Object.keys(e),o={},r=0,a=n.length;r<a;r++)t=n[r],zi(e[t])?o[t]=Array.from(e[t]):Wi(e[t])?o[t]=this._simpleDeepCopy(e[t]):o[t]=e[t];return o}}]),e}(),AI=[vd],EI=function(){function e(t){Gn(this,e),this._sessionModule=t,this._className="DownlinkHandler",this._eventHandlerMap=new Map,this._eventHandlerMap.set("C2CMessageArray",this._c2cMessageArrayHandler.bind(this)),this._eventHandlerMap.set("groupMessageArray",this._groupMessageArrayHandler.bind(this)),this._eventHandlerMap.set("groupTips",this._groupTipsHandler.bind(this)),this._eventHandlerMap.set("C2CNotifyMessageArray",this._C2CNotifyMessageArrayHandler.bind(this)),this._eventHandlerMap.set("profileModify",this._profileHandler.bind(this)),this._eventHandlerMap.set("friendListMod",this._relationChainHandler.bind(this)),this._eventHandlerMap.set("recentContactMod",this._recentContactHandler.bind(this)),this._eventHandlerMap.set("readAllC2CMessage",this._allMessageReadHandler.bind(this)),this._keys=$n(this._eventHandlerMap.keys())}return Un(e,[{key:"_c2cMessageArrayHandler",value:function(e){var t=this._sessionModule.getModule(Bc);t&&(e.dataList.forEach((function(e){if(1===e.isSyncMessage){var t=e.from;e.from=e.to,e.to=t}})),1===e.needSync&&this._sessionModule.getModule(el).startOnlineSync(),t.onNewC2CMessage({dataList:e.dataList,isInstantMessage:!0}))}},{key:"_groupMessageArrayHandler",value:function(e){var t=this._sessionModule.getModule(Kc);t&&t.onNewGroupMessage({event:e.event,dataList:e.dataList,isInstantMessage:!0})}},{key:"_groupTipsHandler",value:function(e){var t=this._sessionModule.getModule(Kc);if(t){var n=e.event,o=e.dataList,r=e.isInstantMessage,a=void 0===r||r,s=e.isSyncingEnded;switch(n){case 4:case 6:t.onNewGroupTips({event:n,dataList:o});break;case 5:o.forEach((function(e){zi(e.elements.revokedInfos)?t.onGroupMessageRevoked({dataList:o}):zi(e.elements.groupMessageReadNotice)?t.onGroupMessageReadNotice({dataList:o}):t.onNewGroupSystemNotice({dataList:o,isInstantMessage:a,isSyncingEnded:s})}));break;case 12:this._sessionModule.getModule(Wc).onNewGroupAtTips({dataList:o});break;default:Bi.log("".concat(this._className,"._groupTipsHandler unknown event:").concat(n," dataList:"),o)}}}},{key:"_C2CNotifyMessageArrayHandler",value:function(e){var t=this,n=e.dataList;if(zi(n)){var o=this._sessionModule.getModule(Bc);n.forEach((function(e){if(Yi(e))if(e.hasOwnProperty("kickoutMsgNotify")){var r=e.kickoutMsgNotify,a=r.kickType,s=r.newInstanceInfo,i=void 0===s?{}:s;1===a?t._sessionModule.onMultipleAccountKickedOut(i):2===a&&t._sessionModule.onMultipleDeviceKickedOut(i)}else e.hasOwnProperty("c2cMessageRevokedNotify")?o&&o.onC2CMessageRevoked({dataList:n}):e.hasOwnProperty("c2cMessageReadReceipt")?o&&o.onC2CMessageReadReceipt({dataList:n}):e.hasOwnProperty("c2cMessageReadNotice")?o&&o.onC2CMessageReadNotice({dataList:n}):e.hasOwnProperty("muteNotificationsSync")&&t._sessionModule.getModule(Wc).onC2CMessageRemindTypeSynced({dataList:n})}))}}},{key:"_profileHandler",value:function(e){this._sessionModule.getModule(Vc).onProfileModified({dataList:e.dataList});var t=this._sessionModule.getModule(Hc);t&&t.onFriendProfileModified({dataList:e.dataList})}},{key:"_relationChainHandler",value:function(e){this._sessionModule.getModule(Vc).onRelationChainModified({dataList:e.dataList});var t=this._sessionModule.getModule(Hc);t&&t.onRelationChainModified({dataList:e.dataList})}},{key:"_recentContactHandler",value:function(e){var t=e.dataList;if(zi(t)){var n=this._sessionModule.getModule(Wc);n&&t.forEach((function(e){var t=e.pushType,o=e.recentContactTopItem,r=e.recentContactDeleteItem;1===t?n.onConversationDeleted(r.recentContactList):2===t?n.onConversationPinned(o.recentContactList):3===t&&n.onConversationUnpinned(o.recentContactList)}))}}},{key:"_allMessageReadHandler",value:function(e){var t=e.dataList,n=this._sessionModule.getModule(Wc);n&&n.onPushedAllMessageRead(t)}},{key:"onMessage",value:function(e){var t=this,n=e.body;if(this._filterMessageFromIMOpenPush(e)){var o=n.eventArray,r=n.isInstantMessage,a=n.isSyncingEnded,s=n.needSync;if(zi(o))for(var i=null,u=null,c=0,l=0,d=o.length;l<d;l++){c=(i=o[l]).event;var p=Object.keys(i).find((function(e){return-1!==t._keys.indexOf(e)}));p?(u=14!==c?i[p]:{readAllC2CMessage:i[p],groupMessageReadInfoList:i.groupMessageReadNotice||[]},this._eventHandlerMap.get(p)({event:c,dataList:u,isInstantMessage:r,isSyncingEnded:a,needSync:s})):Bi.log("".concat(this._className,".onMessage unknown eventItem:").concat(i))}}}},{key:"_filterMessageFromIMOpenPush",value:function(e){var t=e.head,n=e.body,o=t.servcmd,r=!1;if(Ji(o)||(r=o.includes(Js.NAME.IM_CONFIG_MANAGER)||o.includes(Js.NAME.OVERLOAD_PUSH)||o.includes(Js.NAME.STAT_SERVICE)),!r)return!0;if(o.includes(Js.CMD.PUSHED_CLOUD_CONTROL_CONFIG))this._sessionModule.getModule(rl).onPushedCloudControlConfig(n);else if(o.includes(Js.CMD.PUSHED_COMMERCIAL_CONFIG))this._sessionModule.getModule(ul).onPushedConfig(n);else if(o.includes(Js.CMD.OVERLOAD_NOTIFY))this._sessionModule.onPushedServerOverload(n);else if(o.includes(Js.CMD.KICK_OTHER)){var a=Date.now();this._sessionModule.reLoginOnKickOther();var s=new Zp(sg),i=this._sessionModule.getModule(qc).getLastWsHelloTs(),u=a-i;s.setMessage("last wshello time:".concat(i," diff:").concat(u,"ms")).setNetworkType(this._sessionModule.getNetworkType()).end()}return!1}}]),e}(),kI=[{cmd:Js.CMD.GET_GROUP_INFO,interval:1,count:20},{cmd:Js.CMD.SET_GROUP_ATTRIBUTES,interval:5,count:10},{cmd:Js.CMD.MODIFY_GROUP_ATTRIBUTES,interval:5,count:10},{cmd:Js.CMD.DELETE_GROUP_ATTRIBUTES,interval:5,count:10},{cmd:Js.CMD.CLEAR_GROUP_ATTRIBUTES,interval:5,count:10},{cmd:Js.CMD.GET_GROUP_ATTRIBUTES,interval:5,count:20},{cmd:Js.CMD.SET_ALL_MESSAGE_READ,interval:1,count:1}],DI=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="SessionModule",o._platform=o.getPlatform(),o._protocolHandler=new SI(zn(o)),o._messageDispatcher=new EI(zn(o)),o._commandFrequencyLimitMap=new Map,o._commandRequestInfoMap=new Map,o._serverOverloadInfoMap=new Map,o._init(),o.getInnerEmitterInstance().on(Rm,o._onCloudConfigUpdated,zn(o)),o}return Un(n,[{key:"_init",value:function(){this._updateCommandFrequencyLimitMap(kI)}},{key:"_onCloudConfigUpdated",value:function(){var e=this.getCloudConfig("cmd_frequency_limit");Ji(e)||(e=JSON.parse(e),this._updateCommandFrequencyLimitMap(e))}},{key:"_updateCommandFrequencyLimitMap",value:function(e){var t=this;e.forEach((function(e){t._commandFrequencyLimitMap.set(e.cmd,{interval:e.interval,count:e.count})}))}},{key:"updateProtocolConfig",value:function(){this._protocolHandler.update()}},{key:"request",value:function(e){Bi.debug("".concat(this._className,".request options:"),e);var t=e.protocolName,n=e.tjgID;if(!this._protocolHandler.has(t))return Bi.warn("".concat(this._className,".request unknown protocol:").concat(t)),Mm({code:kd.CANNOT_FIND_PROTOCOL,message:wp});var o=this.getProtocolData(e),r=o.head.servcmd;if(this._isFrequencyOverLimit(r))return Mm({code:kd.OVER_FREQUENCY_LIMIT,message:Up});if(this._isServerOverload(r))return Mm({code:kd.OPEN_SERVICE_OVERLOAD_ERROR,message:Fp});Nu(n)||(o.head.tjgID=n);var a=this.getModule(nl);return AI.includes(t)?a.simplySend(o):a.send(o)}},{key:"getKeyMap",value:function(e){return this._protocolHandler.getKeyMap(e)}},{key:"genCommonHead",value:function(){var e=this.getModule(Yc);return{ver:"v4",platform:this._platform,websdkappid:Ks,websdkversion:Bs,a2:e.getA2Key()||void 0,tinyid:e.getTinyID()||void 0,status_instid:e.getStatusInstanceID(),sdkappid:e.getSDKAppID(),contenttype:e.getContentType(),reqtime:0,identifier:e.getA2Key()?void 0:e.getUserID(),usersig:e.getA2Key()?void 0:e.getUserSig(),sdkability:35,tjgID:""}}},{key:"genCosSpecifiedHead",value:function(){var e=this.getModule(Yc);return{ver:"v4",platform:this._platform,websdkappid:Ks,websdkversion:Bs,sdkappid:e.getSDKAppID(),contenttype:e.getContentType(),reqtime:0,identifier:e.getUserID(),usersig:e.getUserSig(),status_instid:e.getStatusInstanceID(),sdkability:35}}},{key:"genSSOReportHead",value:function(){var e=this.getModule(Yc);return{ver:"v4",platform:this._platform,websdkappid:Ks,websdkversion:Bs,sdkappid:e.getSDKAppID(),contenttype:"",reqtime:0,identifier:"",usersig:"",status_instid:e.getStatusInstanceID(),sdkability:35}}},{key:"getProtocolData",value:function(e){return this._protocolHandler.getProtocolData(e)}},{key:"onErrorCodeNotZero",value:function(e){var t=e.errorCode;if(t===kd.HELLO_ANSWER_KICKED_OUT){var n=e.kickType,o=e.newInstanceInfo,r=void 0===o?{}:o;1===n?this.onMultipleAccountKickedOut(r):2===n&&this.onMultipleDeviceKickedOut(r)}t!==kd.MESSAGE_A2KEY_EXPIRED&&t!==kd.ACCOUNT_A2KEY_EXPIRED||(this._onUserSigExpired(),this.getModule(nl).reConnect())}},{key:"onMessage",value:function(e){var t=e.body,n=t.needAck,o=void 0===n?0:n,r=t.sessionData;1===o&&this._sendACK(r),this._messageDispatcher.onMessage(e)}},{key:"onReconnected",value:function(){this._reLoginOnReconnected()}},{key:"reLoginOnKickOther",value:function(){Bi.log("".concat(this._className,".reLoginOnKickOther.")),this._reLogin()}},{key:"_reLoginOnReconnected",value:function(){Bi.log("".concat(this._className,"._reLoginOnReconnected.")),this._reLogin()}},{key:"_reLogin",value:function(){var e=this;this.isLoggedIn()&&this.request({protocolName:ll}).then((function(t){var n=t.data.instanceID;e.getModule(Yc).setStatusInstanceID(n),Bi.log("".concat(e._className,"._reLogin ok. start to sync unread messages.")),e.getModule(el).startSyncOnReconnected(),e.getModule(sl).startPull(),e.getModule(Kc).updateLocalMainSequenceOnReconnected()}))}},{key:"onMultipleAccountKickedOut",value:function(e){this.getModule(qc).onMultipleAccountKickedOut(e)}},{key:"onMultipleDeviceKickedOut",value:function(e){this.getModule(qc).onMultipleDeviceKickedOut(e)}},{key:"_onUserSigExpired",value:function(){this.getModule(qc).onUserSigExpired()}},{key:"_sendACK",value:function(e){this.request({protocolName:vd,requestData:{sessionData:e}})}},{key:"_isFrequencyOverLimit",value:function(e){var t=e.split(".")[1];if(!this._commandFrequencyLimitMap.has(t))return!1;if(!this._commandRequestInfoMap.has(t))return this._commandRequestInfoMap.set(t,{startTime:Date.now(),requestCount:1}),!1;var n=this._commandFrequencyLimitMap.get(t),o=n.count,r=n.interval,a=this._commandRequestInfoMap.get(t),s=a.startTime,i=a.requestCount;if(Date.now()-s>1e3*r)return this._commandRequestInfoMap.set(t,{startTime:Date.now(),requestCount:1}),!1;i+=1,this._commandRequestInfoMap.set(t,{startTime:s,requestCount:i});var u=!1;return i>o&&(u=!0),u}},{key:"_isServerOverload",value:function(e){if(!this._serverOverloadInfoMap.has(e))return!1;var t=this._serverOverloadInfoMap.get(e),n=t.overloadTime,o=t.waitingTime,r=!1;return Date.now()-n<=1e3*o?r=!0:(this._serverOverloadInfoMap.delete(e),r=!1),r}},{key:"onPushedServerOverload",value:function(e){var t=e.overloadCommand,n=e.waitingTime;this._serverOverloadInfoMap.set(t,{overloadTime:Date.now(),waitingTime:n}),Bi.warn("".concat(this._className,".onPushedServerOverload waitingTime:").concat(n,"s"))}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._updateCommandFrequencyLimitMap(kI),this._commandRequestInfoMap.clear(),this._serverOverloadInfoMap.clear()}}]),n}(cl),NI=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="MessageLossDetectionModule",o._maybeLostSequencesMap=new Map,o}return Un(n,[{key:"onMessageMaybeLost",value:function(e,t,n){this._maybeLostSequencesMap.has(e)||this._maybeLostSequencesMap.set(e,[]);for(var o=this._maybeLostSequencesMap.get(e),r=0;r<n;r++)o.push(t+r);Bi.debug("".concat(this._className,".onMessageMaybeLost. maybeLostSequences:").concat(o))}},{key:"detectMessageLoss",value:function(e,t){var n=this._maybeLostSequencesMap.get(e);if(!Nu(n)&&!Nu(t)){var o=t.filter((function(e){return-1!==n.indexOf(e)}));if(Bi.debug("".concat(this._className,".detectMessageLoss. matchedSequences:").concat(o)),n.length===o.length)Bi.info("".concat(this._className,".detectMessageLoss no message loss. conversationID:").concat(e));else{var r,a=n.filter((function(e){return-1===o.indexOf(e)})),s=a.length;s<=5?r=e+"-"+a.join("-"):(a.sort((function(e,t){return e-t})),r=e+" start:"+a[0]+" end:"+a[s-1]+" count:"+s),new Zp(ph).setMessage(r).setNetworkType(this.getNetworkType()).setLevel("warning").end(),Bi.warn("".concat(this._className,".detectMessageLoss message loss detected. conversationID:").concat(e," lostSequences:").concat(a))}n.length=0}}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._maybeLostSequencesMap.clear()}}]),n}(cl),OI=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="CloudControlModule",o._cloudConfig=new Map,o._expiredTime=0,o._version=0,o._isFetching=!1,o}return Un(n,[{key:"getCloudConfig",value:function(e){return Ji(e)?this._cloudConfig:this._cloudConfig.has(e)?this._cloudConfig.get(e):void 0}},{key:"_canFetchConfig",value:function(){return this.isLoggedIn()&&!this._isFetching&&Date.now()>=this._expiredTime}},{key:"fetchConfig",value:function(){var e=this,t=this._canFetchConfig();if(Bi.log("".concat(this._className,".fetchConfig canFetchConfig:").concat(t)),t){var n=new Zp(yh),o=this.getModule(Yc).getSDKAppID();this._isFetching=!0,this.request({protocolName:Td,requestData:{SDKAppID:o,version:this._version}}).then((function(t){e._isFetching=!1,n.setMessage("version:".concat(e._version," newVersion:").concat(t.data.version," config:").concat(t.data.cloudControlConfig)).setNetworkType(e.getNetworkType()).end(),Bi.log("".concat(e._className,".fetchConfig ok")),e._parseCloudControlConfig(t.data)})).catch((function(t){e._isFetching=!1,e.probeNetwork().then((function(e){var o=Qn(e,2),r=o[0],a=o[1];n.setError(t,r,a).end()})),Bi.log("".concat(e._className,".fetchConfig failed. error:"),t),e._setExpiredTimeOnResponseError(12e4)}))}}},{key:"onPushedCloudControlConfig",value:function(e){Bi.log("".concat(this._className,".onPushedCloudControlConfig")),new Zp(Ih).setNetworkType(this.getNetworkType()).setMessage("newVersion:".concat(e.version," config:").concat(e.cloudControlConfig)).end(),this._parseCloudControlConfig(e)}},{key:"onCheckTimer",value:function(e){this._canFetchConfig()&&this.fetchConfig()}},{key:"_parseCloudControlConfig",value:function(e){var t=this,n="".concat(this._className,"._parseCloudControlConfig"),o=e.errorCode,r=e.errorMessage,a=e.cloudControlConfig,s=e.version,i=e.expiredTime;if(0===o){if(this._version!==s){var u=null;try{u=JSON.parse(a)}catch(YI){Bi.error("".concat(n," JSON parse error:").concat(a))}u&&(this._cloudConfig.clear(),Object.keys(u).forEach((function(e){t._cloudConfig.set(e,u[e])})),this._version=s,this.emitInnerEvent(Rm))}this._expiredTime=Date.now()+1e3*i}else Ji(o)?(Bi.log("".concat(n," failed. Invalid message format:"),e),this._setExpiredTimeOnResponseError(36e5)):(Bi.error("".concat(n," errorCode:").concat(o," errorMessage:").concat(r)),this._setExpiredTimeOnResponseError(12e4))}},{key:"_setExpiredTimeOnResponseError",value:function(e){this._expiredTime=Date.now()+e}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._cloudConfig.clear(),this._expiredTime=0,this._version=0,this._isFetching=!1}}]),n}(cl),RI=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="PullGroupMessageModule",o._remoteLastMessageSequenceMap=new Map,o.PULL_LIMIT_COUNT=15,o}return Un(n,[{key:"startPull",value:function(){var e=this,t=this._getNeedPullConversationList();this._getRemoteLastMessageSequenceList().then((function(){var n=e.getModule(Wc);t.forEach((function(t){var o=t.conversationID,r=o.replace(so.CONV_GROUP,""),a=n.getGroupLocalLastMessageSequence(o),s=e._remoteLastMessageSequenceMap.get(r)||0,i=s-a;Bi.log("".concat(e._className,".startPull groupID:").concat(r," localLastMessageSequence:").concat(a," ")+"remoteLastMessageSequence:".concat(s," diff:").concat(i)),a>0&&i>=1&&i<300&&e._pullMissingMessage({groupID:r,localLastMessageSequence:a,remoteLastMessageSequence:s,diff:i})}))}))}},{key:"_getNeedPullConversationList",value:function(){return this.getModule(Wc).getLocalConversationList().filter((function(e){return e.type===so.CONV_GROUP&&e.groupProfile.type!==so.GRP_AVCHATROOM}))}},{key:"_getRemoteLastMessageSequenceList",value:function(){var e=this;return this.getModule(Kc).getGroupList().then((function(t){for(var n=t.data.groupList,o=void 0===n?[]:n,r=0;r<o.length;r++){var a=o[r],s=a.groupID,i=a.nextMessageSeq;if(a.type!==so.GRP_AVCHATROOM){var u=i-1;e._remoteLastMessageSequenceMap.set(s,u)}}}))}},{key:"_pullMissingMessage",value:function(e){var t=this,n=e.localLastMessageSequence,o=e.remoteLastMessageSequence,r=e.diff;e.count=r>this.PULL_LIMIT_COUNT?this.PULL_LIMIT_COUNT:r,e.sequence=r>this.PULL_LIMIT_COUNT?n+this.PULL_LIMIT_COUNT:n+r,this._getGroupMissingMessage(e).then((function(a){a.length>0&&(a[0].sequence+1<=o&&(e.localLastMessageSequence=n+t.PULL_LIMIT_COUNT,e.diff=r-t.PULL_LIMIT_COUNT,t._pullMissingMessage(e)),t.getModule(Kc).onNewGroupMessage({dataList:a,isInstantMessage:!1}))}))}},{key:"_getGroupMissingMessage",value:function(e){var t=this,n=new Zp(zg);return this.request({protocolName:Xl,requestData:{groupID:e.groupID,count:e.count,sequence:e.sequence}}).then((function(o){var r=o.data.messageList,a=void 0===r?[]:r;return n.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(e.groupID," count:").concat(e.count," sequence:").concat(e.sequence," messageList length:").concat(a.length)).end(),a})).catch((function(e){t.probeNetwork().then((function(t){var o=Qn(t,2),r=o[0],a=o[1];n.setError(e,r,a).end()}))}))}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._remoteLastMessageSequenceMap.clear()}}]),n}(cl),LI=function(){function e(){Gn(this,e),this._className="AvgE2EDelay",this._e2eDelayArray=[]}return Un(e,[{key:"addMessageDelay",value:function(e){var t=Au(e.currentTime/1e3-e.time,2);this._e2eDelayArray.push(t)}},{key:"_calcAvg",value:function(e,t){if(0===t)return 0;var n=0;return e.forEach((function(e){n+=e})),Au(n/t,1)}},{key:"_calcTotalCount",value:function(){return this._e2eDelayArray.length}},{key:"_calcCountWithLimit",value:function(e){var t=e.e2eDelayArray,n=e.min,o=e.max;return t.filter((function(e){return n<e&&e<=o})).length}},{key:"_calcPercent",value:function(e,t){var n=Au(e/t*100,2);return n>100&&(n=100),n}},{key:"_checkE2EDelayException",value:function(e,t){var n=e.filter((function(e){return e>t}));if(n.length>0){var o=n.length,r=Math.min.apply(Math,$n(n)),a=Math.max.apply(Math,$n(n)),s=this._calcAvg(n,o),i=Au(o/e.length*100,2);new Zp(Eg).setMessage("message e2e delay exception. count:".concat(o," min:").concat(r," max:").concat(a," avg:").concat(s," percent:").concat(i)).setLevel("warning").end()}}},{key:"getStatResult",value:function(){var e=this._calcTotalCount();if(0===e)return null;var t=$n(this._e2eDelayArray),n=this._calcCountWithLimit({e2eDelayArray:t,min:0,max:1}),o=this._calcCountWithLimit({e2eDelayArray:t,min:1,max:3}),r=this._calcPercent(n,e),a=this._calcPercent(o,e),s=this._calcAvg(t,e);return this._checkE2EDelayException(t,3),this.reset(),{totalCount:e,countLessThan1Second:n,percentOfCountLessThan1Second:r,countLessThan3Second:o,percentOfCountLessThan3Second:a,avgDelay:s}}},{key:"reset",value:function(){this._e2eDelayArray.length=0}}]),e}(),bI=function(){function e(){Gn(this,e),this._className="AvgRTT",this._requestCount=0,this._rttArray=[]}return Un(e,[{key:"addRequestCount",value:function(){this._requestCount+=1}},{key:"addRTT",value:function(e){this._rttArray.push(e)}},{key:"_calcTotalCount",value:function(){return this._requestCount}},{key:"_calcRTTCount",value:function(e){return e.length}},{key:"_calcSuccessRateOfRequest",value:function(e,t){if(0===t)return 0;var n=Au(e/t*100,2);return n>100&&(n=100),n}},{key:"_calcAvg",value:function(e,t){if(0===t)return 0;var n=0;return e.forEach((function(e){n+=e})),parseInt(n/t)}},{key:"_calcMax",value:function(){return Math.max.apply(Math,$n(this._rttArray))}},{key:"_calcMin",value:function(){return Math.min.apply(Math,$n(this._rttArray))}},{key:"getStatResult",value:function(){var e=this._calcTotalCount(),t=$n(this._rttArray);if(0===e)return null;var n=this._calcRTTCount(t),o=this._calcSuccessRateOfRequest(n,e),r=this._calcAvg(t,n);return Bi.log("".concat(this._className,".getStatResult max:").concat(this._calcMax()," min:").concat(this._calcMin()," avg:").concat(r)),this.reset(),{totalCount:e,rttCount:n,successRateOfRequest:o,avgRTT:r}}},{key:"reset",value:function(){this._requestCount=0,this._rttArray.length=0}}]),e}(),wI=function(){function e(){Gn(this,e),this._map=new Map}return Un(e,[{key:"initMap",value:function(e){var t=this;e.forEach((function(e){t._map.set(e,{totalCount:0,successCount:0,failedCountOfUserSide:0,costArray:[],fileSizeArray:[]})}))}},{key:"addTotalCount",value:function(e){return!(Ji(e)||!this._map.has(e))&&(this._map.get(e).totalCount+=1,!0)}},{key:"addSuccessCount",value:function(e){return!(Ji(e)||!this._map.has(e))&&(this._map.get(e).successCount+=1,!0)}},{key:"addFailedCountOfUserSide",value:function(e){return!(Ji(e)||!this._map.has(e))&&(this._map.get(e).failedCountOfUserSide+=1,!0)}},{key:"addCost",value:function(e,t){return!(Ji(e)||!this._map.has(e))&&(this._map.get(e).costArray.push(t),!0)}},{key:"addFileSize",value:function(e,t){return!(Ji(e)||!this._map.has(e))&&(this._map.get(e).fileSizeArray.push(t),!0)}},{key:"_calcSuccessRateOfBusiness",value:function(e){if(Ji(e)||!this._map.has(e))return-1;var t=this._map.get(e),n=Au(t.successCount/t.totalCount*100,2);return n>100&&(n=100),n}},{key:"_calcSuccessRateOfPlatform",value:function(e){if(Ji(e)||!this._map.has(e))return-1;var t=this._map.get(e),n=this._calcSuccessCountOfPlatform(e)/t.totalCount*100;return(n=Au(n,2))>100&&(n=100),n}},{key:"_calcTotalCount",value:function(e){return Ji(e)||!this._map.has(e)?-1:this._map.get(e).totalCount}},{key:"_calcSuccessCountOfBusiness",value:function(e){return Ji(e)||!this._map.has(e)?-1:this._map.get(e).successCount}},{key:"_calcSuccessCountOfPlatform",value:function(e){if(Ji(e)||!this._map.has(e))return-1;var t=this._map.get(e);return t.successCount+t.failedCountOfUserSide}},{key:"_calcAvg",value:function(e){return Ji(e)||!this._map.has(e)?-1:e===jp?this._calcAvgSpeed(e):this._calcAvgCost(e)}},{key:"_calcAvgCost",value:function(e){var t=this._map.get(e).costArray.length;if(0===t)return 0;var n=0;return this._map.get(e).costArray.forEach((function(e){n+=e})),parseInt(n/t)}},{key:"_calcAvgSpeed",value:function(e){var t=0,n=0;return this._map.get(e).costArray.forEach((function(e){t+=e})),this._map.get(e).fileSizeArray.forEach((function(e){n+=e})),parseInt(1e3*n/t)}},{key:"getStatResult",value:function(e){var t=this._calcTotalCount(e);if(0===t)return null;var n=this._calcSuccessCountOfBusiness(e),o=this._calcSuccessRateOfBusiness(e),r=this._calcSuccessCountOfPlatform(e),a=this._calcSuccessRateOfPlatform(e),s=this._calcAvg(e);return this.reset(e),{totalCount:t,successCountOfBusiness:n,successRateOfBusiness:o,successCountOfPlatform:r,successRateOfPlatform:a,avgValue:s}}},{key:"reset",value:function(e){Ji(e)?this._map.clear():this._map.set(e,{totalCount:0,successCount:0,failedCountOfUserSide:0,costArray:[],fileSizeArray:[]})}}]),e}(),GI=function(){function e(){Gn(this,e),this._lastMap=new Map,this._currentMap=new Map}return Un(e,[{key:"initMap",value:function(e){var t=this;e.forEach((function(e){t._lastMap.set(e,new Map),t._currentMap.set(e,new Map)}))}},{key:"addMessageSequence",value:function(e){var t=e.key,n=e.message;if(Ji(t)||!this._lastMap.has(t)||!this._currentMap.has(t))return!1;var o=n.conversationID,r=n.sequence,a=o.replace(so.CONV_GROUP,"");if(0===this._lastMap.get(t).size)this._addCurrentMap(e);else if(this._lastMap.get(t).has(a)){var s=this._lastMap.get(t).get(a),i=s.length-1;r>s[0]&&r<s[i]?(s.push(r),s.sort(),this._lastMap.get(t).set(a,s)):this._addCurrentMap(e)}else this._addCurrentMap(e);return!0}},{key:"_addCurrentMap",value:function(e){var t=e.key,n=e.message,o=n.conversationID,r=n.sequence,a=o.replace(so.CONV_GROUP,"");this._currentMap.get(t).has(a)||this._currentMap.get(t).set(a,[]),this._currentMap.get(t).get(a).push(r)}},{key:"_copyData",value:function(e){if(!Ji(e)){this._lastMap.set(e,new Map);var t,n=this._lastMap.get(e),o=ro(this._currentMap.get(e));try{for(o.s();!(t=o.n()).done;){var r=Qn(t.value,2),a=r[0],s=r[1];n.set(a,s)}}catch(c){o.e(c)}finally{o.f()}n=null,this._currentMap.set(e,new Map)}}},{key:"getStatResult",value:function(e){if(Ji(this._currentMap.get(e))||Ji(this._lastMap.get(e)))return null;if(0===this._lastMap.get(e).size)return this._copyData(e),null;var t=0,n=0;if(this._lastMap.get(e).forEach((function(e,o){var r=$n(e.values()),a=r.length,s=r[a-1]-r[0]+1;t+=s,n+=a})),0===t)return null;var o=Au(n/t*100,2);return o>100&&(o=100),this._copyData(e),{totalCount:t,successCountOfMessageReceived:n,successRateOfMessageReceived:o}}},{key:"reset",value:function(){this._currentMap.clear(),this._lastMap.clear()}}]),e}(),PI=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;Gn(this,n),(o=t.call(this,e))._className="QualityStatModule",o.TAG="im-ssolog-quality-stat",o.reportIndex=0,o.wholePeriod=!1,o._qualityItems=[qp,xp,Vp,Bp,Kp,Hp,jp,Wp,Yp,zp],o._messageSentItems=[Vp,Bp,Kp,Hp,jp],o._messageReceivedItems=[Wp,Yp,zp],o.REPORT_INTERVAL=120,o.REPORT_SDKAPPID_BLACKLIST=[],o.REPORT_TINYID_WHITELIST=[],o._statInfoArr=[],o._avgRTT=new bI,o._avgE2EDelay=new LI,o._rateMessageSent=new wI,o._rateMessageReceived=new GI;var r=o.getInnerEmitterInstance();return r.on(Om,o._onLoginSuccess,zn(o)),r.on(Rm,o._onCloudConfigUpdated,zn(o)),o}return Un(n,[{key:"_onLoginSuccess",value:function(){var e=this;this._rateMessageSent.initMap(this._messageSentItems),this._rateMessageReceived.initMap(this._messageReceivedItems);var t=this.getModule(zc),n=t.getItem(this.TAG,!1);!Nu(n)&&Qi(n.forEach)&&(Bi.log("".concat(this._className,"._onLoginSuccess.get quality stat log in storage, nums=").concat(n.length)),n.forEach((function(t){e._statInfoArr.push(t)})),t.removeItem(this.TAG,!1))}},{key:"_onCloudConfigUpdated",value:function(){var e=this.getCloudConfig("q_rpt_interval"),t=this.getCloudConfig("q_rpt_sdkappid_bl"),n=this.getCloudConfig("q_rpt_tinyid_wl");Ji(e)||(this.REPORT_INTERVAL=Number(e)),Ji(t)||(this.REPORT_SDKAPPID_BLACKLIST=t.split(",").map((function(e){return Number(e)}))),Ji(n)||(this.REPORT_TINYID_WHITELIST=n.split(","))}},{key:"onCheckTimer",value:function(e){this.isLoggedIn()&&e%this.REPORT_INTERVAL==0&&(this.wholePeriod=!0,this._report())}},{key:"addRequestCount",value:function(){this._avgRTT.addRequestCount()}},{key:"addRTT",value:function(e){this._avgRTT.addRTT(e)}},{key:"addMessageDelay",value:function(e){this._avgE2EDelay.addMessageDelay(e)}},{key:"addTotalCount",value:function(e){this._rateMessageSent.addTotalCount(e)||Bi.warn("".concat(this._className,".addTotalCount invalid key:"),e)}},{key:"addSuccessCount",value:function(e){this._rateMessageSent.addSuccessCount(e)||Bi.warn("".concat(this._className,".addSuccessCount invalid key:"),e)}},{key:"addFailedCountOfUserSide",value:function(e){this._rateMessageSent.addFailedCountOfUserSide(e)||Bi.warn("".concat(this._className,".addFailedCountOfUserSide invalid key:"),e)}},{key:"addCost",value:function(e,t){this._rateMessageSent.addCost(e,t)||Bi.warn("".concat(this._className,".addCost invalid key or cost:"),e,t)}},{key:"addFileSize",value:function(e,t){this._rateMessageSent.addFileSize(e,t)||Bi.warn("".concat(this._className,".addFileSize invalid key or size:"),e,t)}},{key:"addMessageSequence",value:function(e){this._rateMessageReceived.addMessageSequence(e)||Bi.warn("".concat(this._className,".addMessageSequence invalid key:"),e.key)}},{key:"_getQualityItem",value:function(e){var t={},n=Qp[this.getNetworkType()];Ji(n)&&(n=8);var o={qualityType:Jp[e],timestamp:Ui(),networkType:n,extension:""};switch(e){case qp:t=this._avgRTT.getStatResult();break;case xp:t=this._avgE2EDelay.getStatResult();break;case Vp:case Bp:case Kp:case Hp:case jp:t=this._rateMessageSent.getStatResult(e);break;case Wp:case Yp:case zp:t=this._rateMessageReceived.getStatResult(e)}return null===t?null:xn(xn({},o),t)}},{key:"_report",value:function(e){var t=this,n=[],o=null;Ji(e)?this._qualityItems.forEach((function(e){null!==(o=t._getQualityItem(e))&&(o.reportIndex=t.reportIndex,o.wholePeriod=t.wholePeriod,n.push(o))})):null!==(o=this._getQualityItem(e))&&(o.reportIndex=this.reportIndex,o.wholePeriod=this.wholePeriod,n.push(o)),Bi.debug("".concat(this._className,"._report"),n),this._statInfoArr.length>0&&(n=n.concat(this._statInfoArr),this._statInfoArr=[]);var r=this.getModule(Yc),a=r.getSDKAppID(),s=r.getTinyID();Eu(this.REPORT_SDKAPPID_BLACKLIST,a)&&!ku(this.REPORT_TINYID_WHITELIST,s)&&(n=[]),n.length>0&&this._doReport(n)}},{key:"_doReport",value:function(e){var t=this,n={header:Iv(this),quality:e};this.request({protocolName:fd,requestData:xn({},n)}).then((function(){t.reportIndex++,t.wholePeriod=!1})).catch((function(n){Bi.warn("".concat(t._className,"._doReport, online:").concat(t.getNetworkType()," error:"),n),t._statInfoArr=t._statInfoArr.concat(e),t._flushAtOnce()}))}},{key:"_flushAtOnce",value:function(){var e=this.getModule(zc),t=e.getItem(this.TAG,!1),n=this._statInfoArr;if(Nu(t))Bi.log("".concat(this._className,"._flushAtOnce count:").concat(n.length)),e.setItem(this.TAG,n,!0,!1);else{var o=n.concat(t);o.length>10&&(o=o.slice(0,10)),Bi.log("".concat(this.className,"._flushAtOnce count:").concat(o.length)),e.setItem(this.TAG,o,!0,!1)}this._statInfoArr=[]}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._report(),this.reportIndex=0,this.wholePeriod=!1,this.REPORT_SDKAPPID_BLACKLIST=[],this.REPORT_TINYID_WHITELIST=[],this._avgRTT.reset(),this._avgE2EDelay.reset(),this._rateMessageSent.reset(),this._rateMessageReceived.reset()}}]),n}(cl),UI=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="WorkerModule",o._isWorkerEnabled=!1,o._workerTimer=null,o._init(),o.getInnerEmitterInstance().on(Rm,o._onCloudConfigUpdated,zn(o)),o}return Un(n,[{key:"isWorkerEnabled",value:function(){return this._isWorkerEnabled&&Si&&this._workerTimer}},{key:"startWorkerTimer",value:function(){Bi.log("".concat(this._className,".startWorkerTimer")),this._workerTimer&&this._workerTimer.postMessage("start")}},{key:"stopWorkerTimer",value:function(){Bi.log("".concat(this._className,".stopWorkerTimer")),this._workerTimer&&this._workerTimer.postMessage("stop")}},{key:"_init",value:function(){if(Si){var e=URL.createObjectURL(new Blob(['let interval = -1;onmessage = function(event) {  if (event.data === "start") {    if (interval > 0) {      clearInterval(interval);    }    interval = setInterval(() => {      postMessage("");    }, 1000)  } else if (event.data === "stop") {    clearInterval(interval);    interval = -1;  }};'],{type:"application/javascript; charset=utf-8"}));this._workerTimer=new Worker(e);var t=this;this._workerTimer.onmessage=function(){t._moduleManager.onCheckTimer()}}}},{key:"_onCloudConfigUpdated",value:function(){"1"===this.getCloudConfig("enable_worker")?!this._isWorkerEnabled&&Si&&(this._isWorkerEnabled=!0,this.startWorkerTimer(),this._moduleManager.onWorkerTimerEnabled()):this._isWorkerEnabled&&Si&&(this._isWorkerEnabled=!1,this.stopWorkerTimer(),this._moduleManager.onWorkerTimerDisabled())}},{key:"terminate",value:function(){Bi.log("".concat(this._className,".terminate")),this._workerTimer&&(this._workerTimer.terminate(),this._workerTimer=null)}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset"))}}]),n}(cl),FI=function(){function e(){Gn(this,e),this._className="PurchasedFeatureHandler",this._purchasedFeatureMap=new Map}return Un(e,[{key:"isValidPurchaseBits",value:function(e){return e&&"string"==typeof e&&e.length>=1&&e.length<=64&&/[01]{1,64}/.test(e)}},{key:"parsePurchaseBits",value:function(e){var t="".concat(this._className,".parsePurchaseBits");if(this.isValidPurchaseBits(e)){this._purchasedFeatureMap.clear();for(var n=Object.values(Xs),o=null,r=e.length-1,a=0;r>=0;r--,a++)o=a<32?new qs(0,Math.pow(2,a)).toString():new qs(Math.pow(2,a-32),0).toString(),-1!==n.indexOf(o)&&("1"===e[r]?this._purchasedFeatureMap.set(o,!0):this._purchasedFeatureMap.set(o,!1))}else Bi.warn("".concat(t," invalid purchase bits:").concat(e))}},{key:"hasPurchasedFeature",value:function(e){return!!this._purchasedFeatureMap.get(e)}},{key:"clear",value:function(){this._purchasedFeatureMap.clear()}}]),e}(),qI=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Gn(this,n),(o=t.call(this,e))._className="CommercialConfigModule",o._expiredTime=0,o._isFetching=!1,o._purchasedFeatureHandler=new FI,o}return Un(n,[{key:"_canFetch",value:function(){return this.isLoggedIn()?!this._isFetching&&Date.now()>=this._expiredTime:(this._expiredTime=Date.now()+2e3,!1)}},{key:"onCheckTimer",value:function(e){this._canFetch()&&this.fetchConfig()}},{key:"fetchConfig",value:function(){var e=this,t=this._canFetch(),n="".concat(this._className,".fetchConfig");if(Bi.log("".concat(n," canFetch:").concat(t)),t){var o=new Zp(Th);o.setNetworkType(this.getNetworkType());var r=this.getModule(Yc).getSDKAppID();this._isFetching=!0,this.request({protocolName:Sd,requestData:{SDKAppID:r}}).then((function(t){o.setMessage("purchaseBits:".concat(t.data.purchaseBits)).end(),Bi.log("".concat(n," ok.")),e._parseConfig(t.data),e._isFetching=!1})).catch((function(t){e.probeNetwork().then((function(e){var n=Qn(e,2),r=n[0],a=n[1];o.setError(t,r,a).end()})),e._isFetching=!1}))}}},{key:"onPushedConfig",value:function(e){var t="".concat(this._className,".onPushedConfig");Bi.log("".concat(t)),new Zp(Ch).setNetworkType(this.getNetworkType()).setMessage("purchaseBits:".concat(e.purchaseBits)).end(),this._parseConfig(e)}},{key:"_parseConfig",value:function(e){var t="".concat(this._className,"._parseConfig"),n=e.errorCode,o=e.errorMessage,r=e.purchaseBits,a=e.expiredTime;0===n?(this._purchasedFeatureHandler.parsePurchaseBits(r),this._expiredTime=Date.now()+1e3*a):Ji(n)?(Bi.log("".concat(t," failed. Invalid message format:"),e),this._setExpiredTimeOnResponseError(36e5)):(Bi.error("".concat(t," errorCode:").concat(n," errorMessage:").concat(o)),this._setExpiredTimeOnResponseError(12e4))}},{key:"_setExpiredTimeOnResponseError",value:function(e){this._expiredTime=Date.now()+e}},{key:"hasPurchasedFeature",value:function(e){return this._purchasedFeatureHandler.hasPurchasedFeature(e)}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),this._expiredTime=0,this._isFetching=!1,this._purchasedFeatureHandler.clear()}}]),n}(cl),xI=function(){function e(t){Gn(this,e);var n=new Zp(eg);this._className="ModuleManager",this._isReady=!1,this._startLoginTs=0,this._moduleMap=new Map,this._innerEmitter=null,this._outerEmitter=null,this._checkCount=0,this._checkTimer=-1,this._moduleMap.set(Yc,new _v(this,t)),this._moduleMap.set(ul,new qI(this)),this._moduleMap.set(rl,new OI(this)),this._moduleMap.set(al,new UI(this)),this._moduleMap.set(il,new PI(this)),this._moduleMap.set(nl,new CI(this)),this._moduleMap.set(tl,new DI(this)),this._moduleMap.set(qc,new mv(this)),this._moduleMap.set(xc,new Lv(this)),this._moduleMap.set(Vc,new fv(this)),this._moduleMap.set(Bc,new ym(this)),this._moduleMap.set(Wc,new Jm(this)),this._moduleMap.set(Kc,new cv(this)),this._moduleMap.set(jc,new dv(this)),this._moduleMap.set(zc,new Mv(this)),this._moduleMap.set(Jc,new Tv(this)),this._moduleMap.set(Xc,new Av(this)),this._moduleMap.set(Qc,new kv(this)),this._moduleMap.set($c,new Dv(this)),this._moduleMap.set(Zc,new bv(this)),this._moduleMap.set(el,new wv(this)),this._moduleMap.set(ol,new NI(this)),this._moduleMap.set(sl,new RI(this));var o=t.instanceID,r=t.oversea,a=t.SDKAppID,s="instanceID:".concat(o," SDKAppID:").concat(a," host:").concat(function(){var e="unknown";if(Ci&&(e="mac"),Ti&&(e="windows"),fi&&(e="ios"),_i&&(e="android"),si)try{var t=ui.getSystemInfoSync().platform;void 0!==t&&(e=t)}catch(YI){}return e}()," oversea:").concat(r," inBrowser:").concat(ii," inMiniApp:").concat(si)+" workerAvailable:".concat(Si," UserAgent:").concat(li);Zp.bindEventStatModule(this._moduleMap.get(Jc)),n.setMessage("".concat(s," ").concat(function(){var e="";if(si)try{var t=ui.getSystemInfoSync(),n=t.model,o=t.version,r=t.system,a=t.platform,s=t.SDKVersion;e="model:".concat(n," version:").concat(o," system:").concat(r," platform:").concat(a," SDKVersion:").concat(s)}catch(YI){e=""}return e}())).end(),Bi.info("SDK ".concat(s)),this._readyList=void 0,this._ssoLogForReady=null,this._initReadyList()}return Un(e,[{key:"_startTimer",value:function(){var e=this._moduleMap.get(al).isWorkerEnabled();Bi.log("".concat(this._className,".startTimer isWorkerEnabled:").concat(e," seed:").concat(this._checkTimer)),e?this._moduleMap.get(al).startWorkerTimer():this._startMainThreadTimer()}},{key:"_startMainThreadTimer",value:function(){Bi.log("".concat(this._className,"._startMainThreadTimer")),this._checkTimer<0&&(this._checkTimer=setInterval(this.onCheckTimer.bind(this),1e3))}},{key:"stopTimer",value:function(){var e=this._moduleMap.get(al).isWorkerEnabled();Bi.log("".concat(this._className,".stopTimer isWorkerEnabled:").concat(e," seed:").concat(this._checkTimer)),e?this._moduleMap.get(al).stopWorkerTimer():this._stopMainThreadTimer()}},{key:"_stopMainThreadTimer",value:function(){Bi.log("".concat(this._className,"._stopMainThreadTimer")),this._checkTimer>0&&(clearInterval(this._checkTimer),this._checkTimer=-1,this._checkCount=0)}},{key:"onWorkerTimerEnabled",value:function(){Bi.log("".concat(this._className,".onWorkerTimerEnabled, disable main thread timer")),this._stopMainThreadTimer()}},{key:"onWorkerTimerDisabled",value:function(){Bi.log("".concat(this._className,".onWorkerTimerDisabled, enable main thread timer")),this._startMainThreadTimer()}},{key:"onCheckTimer",value:function(){this._checkCount+=1;var e,t=ro(this._moduleMap);try{for(t.s();!(e=t.n()).done;){var n=Qn(e.value,2)[1];n.onCheckTimer&&n.onCheckTimer(this._checkCount)}}catch(r){t.e(r)}finally{t.f()}}},{key:"_initReadyList",value:function(){var e=this;this._readyList=[this._moduleMap.get(qc),this._moduleMap.get(Wc)],this._readyList.forEach((function(t){t.ready((function(){return e._onModuleReady()}))}))}},{key:"_onModuleReady",value:function(){var e=!0;if(this._readyList.forEach((function(t){t.isReady()||(e=!1)})),e&&!this._isReady){this._isReady=!0,this._outerEmitter.emit(ao.SDK_READY);var t=Date.now()-this._startLoginTs;Bi.warn("SDK is ready. cost ".concat(t," ms")),this._startLoginTs=Date.now();var n=this._moduleMap.get(Xc).getNetworkType(),o=this._ssoLogForReady.getStartTs()+Pi;this._ssoLogForReady.setNetworkType(n).setMessage(t).start(o).end()}}},{key:"login",value:function(){0===this._startLoginTs&&(Fi(),this._startLoginTs=Date.now(),this._startTimer(),this._moduleMap.get(Xc).start(),this._ssoLogForReady=new Zp(tg))}},{key:"onLoginFailed",value:function(){this._startLoginTs=0}},{key:"getOuterEmitterInstance",value:function(){return null===this._outerEmitter&&(this._outerEmitter=new Ev,mm(this._outerEmitter),this._outerEmitter._emit=this._outerEmitter.emit,this._outerEmitter.emit=function(e,t){var n=arguments[0],o=[n,{name:arguments[0],data:arguments[1]}];this._outerEmitter._emit.apply(this._outerEmitter,o)}.bind(this)),this._outerEmitter}},{key:"getInnerEmitterInstance",value:function(){return null===this._innerEmitter&&(this._innerEmitter=new Ev,this._innerEmitter._emit=this._innerEmitter.emit,this._innerEmitter.emit=function(e,t){var n;Yi(arguments[1])&&arguments[1].data?(Bi.warn("inner eventData has data property, please check!"),n=[e,{name:arguments[0],data:arguments[1].data}]):n=[e,{name:arguments[0],data:arguments[1]}],this._innerEmitter._emit.apply(this._innerEmitter,n)}.bind(this)),this._innerEmitter}},{key:"hasModule",value:function(e){return this._moduleMap.has(e)}},{key:"getModule",value:function(e){return this._moduleMap.get(e)}},{key:"isReady",value:function(){return this._isReady}},{key:"onError",value:function(e){Bi.warn("Oops! code:".concat(e.code," message:").concat(e.message)),new Zp(Sh).setMessage("code:".concat(e.code," message:").concat(e.message)).setNetworkType(this.getModule(Xc).getNetworkType()).setLevel("error").end(),this.getOuterEmitterInstance().emit(ao.ERROR,e)}},{key:"reset",value:function(){Bi.log("".concat(this._className,".reset")),Fi();var e,t=ro(this._moduleMap);try{for(t.s();!(e=t.n()).done;){var n=Qn(e.value,2)[1];n.reset&&n.reset()}}catch(r){t.e(r)}finally{t.f()}this._startLoginTs=0,this._initReadyList(),this._isReady=!1,this.stopTimer(),this._outerEmitter.emit(ao.SDK_NOT_READY)}}]),e}(),VI=function(){function e(){Gn(this,e),this._funcMap=new Map}return Un(e,[{key:"defense",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;if("string"!=typeof e)return null;if(0===e.length)return null;if("function"!=typeof t)return null;if(this._funcMap.has(e)&&this._funcMap.get(e).has(t))return this._funcMap.get(e).get(t);this._funcMap.has(e)||this._funcMap.set(e,new Map);var o=null;return this._funcMap.get(e).has(t)?o=this._funcMap.get(e).get(t):(o=this._pack(e,t,n),this._funcMap.get(e).set(t,o)),o}},{key:"defenseOnce",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;return"function"!=typeof t?null:this._pack(e,t,n)}},{key:"find",value:function(e,t){return"string"!=typeof e||0===e.length||"function"!=typeof t?null:this._funcMap.has(e)?this._funcMap.get(e).has(t)?this._funcMap.get(e).get(t):(Bi.log("SafetyCallback.find: 找不到 func —— ".concat(e,"/").concat(""!==t.name?t.name:"[anonymous]")),null):(Bi.log("SafetyCallback.find: 找不到 eventName-".concat(e," 对应的 func")),null)}},{key:"delete",value:function(e,t){return"function"==typeof t&&!!this._funcMap.has(e)&&!!this._funcMap.get(e).has(t)&&(this._funcMap.get(e).delete(t),0===this._funcMap.get(e).size&&this._funcMap.delete(e),!0)}},{key:"_pack",value:function(e,t,n){return function(){try{t.apply(n,Array.from(arguments))}catch(u){var o=Object.values(ao).indexOf(e);if(-1!==o){var r=Object.keys(ao)[o];Bi.warn("接入侧事件 TIM.EVENT.".concat(r," 对应的回调函数逻辑存在问题，请检查！"),u)}var a=new Zp(Mh);a.setMessage("eventName:".concat(e)).setMoreMessage(u.message).end()}}}}]),e}(),BI=function(){function e(t){Gn(this,e);var n={SDKAppID:t.SDKAppID,unlimitedAVChatRoom:t.unlimitedAVChatRoom||!1,scene:t.scene||"",oversea:t.oversea||!1,instanceID:Iu(),devMode:t.devMode||!1};this._moduleManager=new xI(n),this._safetyCallbackFactory=new VI}return Un(e,[{key:"isReady",value:function(){return this._moduleManager.isReady()}},{key:"onError",value:function(e){this._moduleManager.onError(e)}},{key:"login",value:function(e){return this._moduleManager.login(),this._moduleManager.getModule(qc).login(e)}},{key:"logout",value:function(){var e=this;return this._moduleManager.getModule(qc).logout().then((function(t){return e._moduleManager.reset(),t}))}},{key:"destroy",value:function(){var e=this;return this.logout().finally((function(){e._moduleManager.stopTimer(),e._moduleManager.getModule(al).terminate(),e._moduleManager.getModule(nl).dealloc();var t=e._moduleManager.getOuterEmitterInstance(),n=e._moduleManager.getModule(Yc);t.emit(ao.SDK_DESTROY,{SDKAppID:n.getSDKAppID()})}))}},{key:"on",value:function(e,t,n){e===ao.GROUP_SYSTEM_NOTICE_RECEIVED&&Bi.warn("！！！TIM.EVENT.GROUP_SYSTEM_NOTICE_RECEIVED v2.6.0起弃用，为了更好的体验，请在 TIM.EVENT.MESSAGE_RECEIVED 事件回调内接收处理群系统通知，详细请参考：https://web.sdk.qcloud.com/im/doc/zh-cn/Message.html#.GroupSystemNoticePayload"),Bi.debug("on","eventName:".concat(e)),this._moduleManager.getOuterEmitterInstance().on(e,this._safetyCallbackFactory.defense(e,t,n),n)}},{key:"once",value:function(e,t,n){Bi.debug("once","eventName:".concat(e)),this._moduleManager.getOuterEmitterInstance().once(e,this._safetyCallbackFactory.defenseOnce(e,t,n),n||this)}},{key:"off",value:function(e,t,n,o){Bi.debug("off","eventName:".concat(e));var r=this._safetyCallbackFactory.find(e,t);null!==r&&(this._moduleManager.getOuterEmitterInstance().off(e,r,n,o),this._safetyCallbackFactory.delete(e,t))}},{key:"registerPlugin",value:function(e){this._moduleManager.getModule(Zc).registerPlugin(e)}},{key:"setLogLevel",value:function(e){Bi.setLevel(e)}},{key:"createTextMessage",value:function(e){return this._moduleManager.getModule(xc).createTextMessage(e)}},{key:"createTextAtMessage",value:function(e){return this._moduleManager.getModule(xc).createTextMessage(e)}},{key:"createImageMessage",value:function(e){return this._moduleManager.getModule(xc).createImageMessage(e)}},{key:"createAudioMessage",value:function(e){return this._moduleManager.getModule(xc).createAudioMessage(e)}},{key:"createVideoMessage",value:function(e){return this._moduleManager.getModule(xc).createVideoMessage(e)}},{key:"createCustomMessage",value:function(e){return this._moduleManager.getModule(xc).createCustomMessage(e)}},{key:"createFaceMessage",value:function(e){return this._moduleManager.getModule(xc).createFaceMessage(e)}},{key:"createFileMessage",value:function(e){return this._moduleManager.getModule(xc).createFileMessage(e)}},{key:"createLocationMessage",value:function(e){return this._moduleManager.getModule(xc).createLocationMessage(e)}},{key:"createMergerMessage",value:function(e){return this._moduleManager.getModule(xc).createMergerMessage(e)}},{key:"downloadMergerMessage",value:function(e){return e.type!==so.MSG_MERGER?Mm(new fm({code:kd.MESSAGE_MERGER_TYPE_INVALID,message:op})):Nu(e.payload.downloadKey)?Mm(new fm({code:kd.MESSAGE_MERGER_KEY_INVALID,message:rp})):this._moduleManager.getModule(xc).downloadMergerMessage(e).catch((function(e){return Mm(new fm({code:kd.MESSAGE_MERGER_DOWNLOAD_FAIL,message:ap}))}))}},{key:"createForwardMessage",value:function(e){return this._moduleManager.getModule(xc).createForwardMessage(e)}},{key:"sendMessage",value:function(e,t){return e instanceof um?this._moduleManager.getModule(xc).sendMessageInstance(e,t):Mm(new fm({code:kd.MESSAGE_SEND_NEED_MESSAGE_INSTANCE,message:Fd}))}},{key:"callExperimentalAPI",value:function(e,t){return"handleGroupInvitation"===e?this._moduleManager.getModule(Kc).handleGroupInvitation(t):Mm(new fm({code:kd.INVALID_OPERATION,message:bp}))}},{key:"revokeMessage",value:function(e){return this._moduleManager.getModule(xc).revokeMessage(e)}},{key:"resendMessage",value:function(e){return this._moduleManager.getModule(xc).resendMessage(e)}},{key:"deleteMessage",value:function(e){return this._moduleManager.getModule(xc).deleteMessage(e)}},{key:"getMessageList",value:function(e){return this._moduleManager.getModule(Wc).getMessageList(e)}},{key:"setMessageRead",value:function(e){return this._moduleManager.getModule(Wc).setMessageRead(e)}},{key:"getConversationList",value:function(e){return this._moduleManager.getModule(Wc).getConversationList(e)}},{key:"getConversationProfile",value:function(e){return this._moduleManager.getModule(Wc).getConversationProfile(e)}},{key:"deleteConversation",value:function(e){return this._moduleManager.getModule(Wc).deleteConversation(e)}},{key:"pinConversation",value:function(e){return this._moduleManager.getModule(Wc).pinConversation(e)}},{key:"setAllMessageRead",value:function(e){return this._moduleManager.getModule(Wc).setAllMessageRead(e)}},{key:"setMessageRemindType",value:function(e){return this._moduleManager.getModule(Wc).setMessageRemindType(e)}},{key:"getMyProfile",value:function(){return this._moduleManager.getModule(Vc).getMyProfile()}},{key:"getUserProfile",value:function(e){return this._moduleManager.getModule(Vc).getUserProfile(e)}},{key:"updateMyProfile",value:function(e){return this._moduleManager.getModule(Vc).updateMyProfile(e)}},{key:"getBlacklist",value:function(){return this._moduleManager.getModule(Vc).getLocalBlacklist()}},{key:"addToBlacklist",value:function(e){return this._moduleManager.getModule(Vc).addBlacklist(e)}},{key:"removeFromBlacklist",value:function(e){return this._moduleManager.getModule(Vc).deleteBlacklist(e)}},{key:"getFriendList",value:function(){var e=this._moduleManager.getModule(Hc);return e?e.getLocalFriendList():Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"addFriend",value:function(e){var t=this._moduleManager.getModule(Hc);return t?t.addFriend(e):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"deleteFriend",value:function(e){var t=this._moduleManager.getModule(Hc);return t?t.deleteFriend(e):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"checkFriend",value:function(e){var t=this._moduleManager.getModule(Hc);return t?t.checkFriend(e):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"getFriendProfile",value:function(e){var t=this._moduleManager.getModule(Hc);return t?t.getFriendProfile(e):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"updateFriend",value:function(e){var t=this._moduleManager.getModule(Hc);return t?t.updateFriend(e):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"getFriendApplicationList",value:function(){var e=this._moduleManager.getModule(Hc);return e?e.getLocalFriendApplicationList():Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"acceptFriendApplication",value:function(e){var t=this._moduleManager.getModule(Hc);return t?t.acceptFriendApplication(e):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"refuseFriendApplication",value:function(e){var t=this._moduleManager.getModule(Hc);return t?t.refuseFriendApplication(e):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"deleteFriendApplication",value:function(e){var t=this._moduleManager.getModule(Hc);return t?t.deleteFriendApplication(e):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"setFriendApplicationRead",value:function(){var e=this._moduleManager.getModule(Hc);return e?e.setFriendApplicationRead():Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"getFriendGroupList",value:function(){var e=this._moduleManager.getModule(Hc);return e?e.getLocalFriendGroupList():Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"createFriendGroup",value:function(e){var t=this._moduleManager.getModule(Hc);return t?t.createFriendGroup(e):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"deleteFriendGroup",value:function(e){var t=this._moduleManager.getModule(Hc);return t?t.deleteFriendGroup(e):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"addToFriendGroup",value:function(e){var t=this._moduleManager.getModule(Hc);return t?t.addToFriendGroup(e):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"removeFromFriendGroup",value:function(e){var t=this._moduleManager.getModule(Hc);return t?t.removeFromFriendGroup(e):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"renameFriendGroup",value:function(e){var t=this._moduleManager.getModule(Hc);return t?t.renameFriendGroup(e):Mm({code:kd.CANNOT_FIND_MODULE,message:Gp})}},{key:"getGroupList",value:function(e){return this._moduleManager.getModule(Kc).getGroupList(e)}},{key:"getGroupProfile",value:function(e){return this._moduleManager.getModule(Kc).getGroupProfile(e)}},{key:"createGroup",value:function(e){return this._moduleManager.getModule(Kc).createGroup(e)}},{key:"dismissGroup",value:function(e){return this._moduleManager.getModule(Kc).dismissGroup(e)}},{key:"updateGroupProfile",value:function(e){return this._moduleManager.getModule(Kc).updateGroupProfile(e)}},{key:"joinGroup",value:function(e){return this._moduleManager.getModule(Kc).joinGroup(e)}},{key:"quitGroup",value:function(e){return this._moduleManager.getModule(Kc).quitGroup(e)}},{key:"searchGroupByID",value:function(e){return this._moduleManager.getModule(Kc).searchGroupByID(e)}},{key:"getGroupOnlineMemberCount",value:function(e){return this._moduleManager.getModule(Kc).getGroupOnlineMemberCount(e)}},{key:"changeGroupOwner",value:function(e){return this._moduleManager.getModule(Kc).changeGroupOwner(e)}},{key:"handleGroupApplication",value:function(e){return this._moduleManager.getModule(Kc).handleGroupApplication(e)}},{key:"initGroupAttributes",value:function(e){return this._moduleManager.getModule(Kc).initGroupAttributes(e)}},{key:"setGroupAttributes",value:function(e){return this._moduleManager.getModule(Kc).setGroupAttributes(e)}},{key:"deleteGroupAttributes",value:function(e){return this._moduleManager.getModule(Kc).deleteGroupAttributes(e)}},{key:"getGroupAttributes",value:function(e){return this._moduleManager.getModule(Kc).getGroupAttributes(e)}},{key:"getGroupMemberList",value:function(e){return this._moduleManager.getModule(jc).getGroupMemberList(e)}},{key:"getGroupMemberProfile",value:function(e){return this._moduleManager.getModule(jc).getGroupMemberProfile(e)}},{key:"addGroupMember",value:function(e){return this._moduleManager.getModule(jc).addGroupMember(e)}},{key:"deleteGroupMember",value:function(e){return this._moduleManager.getModule(jc).deleteGroupMember(e)}},{key:"setGroupMemberMuteTime",value:function(e){return this._moduleManager.getModule(jc).setGroupMemberMuteTime(e)}},{key:"setGroupMemberRole",value:function(e){return this._moduleManager.getModule(jc).setGroupMemberRole(e)}},{key:"setGroupMemberNameCard",value:function(e){return this._moduleManager.getModule(jc).setGroupMemberNameCard(e)}},{key:"setGroupMemberCustomField",value:function(e){return this._moduleManager.getModule(jc).setGroupMemberCustomField(e)}}]),e}(),KI={login:"login",logout:"logout",destroy:"destroy",on:"on",off:"off",ready:"ready",setLogLevel:"setLogLevel",joinGroup:"joinGroup",quitGroup:"quitGroup",registerPlugin:"registerPlugin",getGroupOnlineMemberCount:"getGroupOnlineMemberCount"};function HI(e,t){if(e.isReady()||void 0!==KI[t])return!0;var n=new fm({code:kd.SDK_IS_NOT_READY,message:"".concat(t," ").concat(Pp,"，请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/module-EVENT.html#.SDK_READY")});return e.onError(n),!1}var jI={},WI={create:function(e){var t=0;if(Hi(e.SDKAppID))t=e.SDKAppID;else if(Bi.warn("TIM.create SDKAppID 的类型应该为 Number，请修改！"),t=parseInt(e.SDKAppID),isNaN(t))return Bi.error("TIM.create failed. 解析 SDKAppID 失败，请检查传参！"),null;if(t&&jI[t])return jI[t];Bi.log("TIM.create");var n=new BI(xn(xn({},e),{},{SDKAppID:t}));n.on(ao.SDK_DESTROY,(function(e){jI[e.data.SDKAppID]=null,delete jI[e.data.SDKAppID]}));var o=function(e){var t=Object.create(null);return Object.keys(Oc).forEach((function(n){if(e[n]){var o=Oc[n],r=new fo;t[o]=function(){var t=Array.from(arguments);return r.use((function(t,o){return HI(e,n)?o():Mm(new fm({code:kd.SDK_IS_NOT_READY,message:"".concat(n," ").concat(Pp,"。")}))})).use((function(e,t){if(!0===Ou(e,Nc[n],o))return t()})).use((function(t,o){return e[n].apply(e,t)})),r.run(t)}}})),t}(n);return jI[t]=o,Bi.log("TIM.create ok"),o}};return WI.TYPES=so,WI.EVENT=ao,WI.VERSION="2.16.1",Bi.log("TIM.VERSION: ".concat(WI.VERSION)),WI}))}).call(this,n("2409"))}}]);
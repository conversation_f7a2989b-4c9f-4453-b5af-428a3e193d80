(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0ac5dd"],{"18dd":function(e,t,n){(function(o){var r,a,s=n("ada0").default;n("cfd4"),n("8ee7"),n("a9b6"),n("57b6"),n("3fab"),n("89cf"),n("3bdf"),n("3f27"),n("6794"),n("44ad"),n("2580"),n("49e9"),n("ed02"),n("c2e6"),n("742a"),n("2690"),n("d4f9"),n("aaa2"),n("c284"),n("f4c5"),n("9d16"),n("8c44"),n("2f57"),n("05ca"),n("8130"),n("cc02"),n("bc86"),n("3e22"),n("28fd"),n("d382"),n("fa71"),n("92a8"),n("a678"),n("59d7"),n("ad43"),n("c592"),n("dfa8"),n("c2fa"),n("b27e"),n("73f0"),n("d38d"),n("a9d5"),n("688d"),n("4fb6"),n("f3a8"),n("a6e6"),n("b925"),n("a884"),n("16e1"),n("8372"),n("3107"),n("e793"),n("a16b"),n("6ba1"),n("8d1b"),n("814e"),n("696a"),n("6249"),n("debc"),function(o,i){"object"==s(t)&&"undefined"!=typeof e?e.exports=i():(r=i,a="function"===typeof r?r.call(t,n,t,e):r,void 0===a||(e.exports=a))}(0,(function(){var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof o?o:"undefined"!=typeof self?self:{};function t(e,t){return e(t={exports:{}},t.exports),t.exports}var n=function(e){return e&&e.Math==Math&&e},r=n("object"==("undefined"===typeof globalThis?"undefined":s(globalThis))&&globalThis)||n("object"==("undefined"===typeof window?"undefined":s(window))&&window)||n("object"==("undefined"===typeof self?"undefined":s(self))&&self)||n("object"==s(e)&&e)||Function("return this")(),a=function(e){try{return!!e()}catch(t){return!0}},i=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),u={}.propertyIsEnumerable,c=Object.getOwnPropertyDescriptor,l={f:c&&!u.call({1:2},1)?function(e){var t=c(this,e);return!!t&&t.enumerable}:u},d=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},p={}.toString,g=function(e){return p.call(e).slice(8,-1)},h="".split,f=a((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==g(e)?h.call(e,""):Object(e)}:Object,_=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},m=function(e){return f(_(e))},v=function(e){return"object"==s(e)?null!==e:"function"==typeof e},M=function(e,t){if(!v(e))return e;var n,o;if(t&&"function"==typeof(n=e.toString)&&!v(o=n.call(e)))return o;if("function"==typeof(n=e.valueOf)&&!v(o=n.call(e)))return o;if(!t&&"function"==typeof(n=e.toString)&&!v(o=n.call(e)))return o;throw TypeError("Can't convert object to primitive value")},y={}.hasOwnProperty,I=function(e,t){return y.call(e,t)},T=r.document,S=v(T)&&v(T.createElement),E=function(e){return S?T.createElement(e):{}},D=!i&&!a((function(){return 7!=Object.defineProperty(E("div"),"a",{get:function(){return 7}}).a})),k=Object.getOwnPropertyDescriptor,C={f:i?k:function(e,t){if(e=m(e),t=M(t,!0),D)try{return k(e,t)}catch(n){}if(I(e,t))return d(!l.f.call(e,t),e[t])}},A=function(e){if(!v(e))throw TypeError(String(e)+" is not an object");return e},N=Object.defineProperty,O={f:i?N:function(e,t,n){if(A(e),t=M(t,!0),A(n),D)try{return N(e,t,n)}catch(r){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},L=i?function(e,t,n){return O.f(e,t,d(1,n))}:function(e,t,n){return e[t]=n,e},R=function(e,t){try{L(r,e,t)}catch(n){r[e]=t}return t},b=r["__core-js_shared__"]||R("__core-js_shared__",{}),w=Function.toString;"function"!=typeof b.inspectSource&&(b.inspectSource=function(e){return w.call(e)});var P,G,U,F=b.inspectSource,q=r.WeakMap,x="function"==typeof q&&/native code/.test(F(q)),V=t((function(e){(e.exports=function(e,t){return b[e]||(b[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.6.4",mode:"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})})),K=0,B=Math.random(),H=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++K+B).toString(36)},j=V("keys"),Y=function(e){return j[e]||(j[e]=H(e))},W={},z=r.WeakMap;if(x){var J=new z,X=J.get,Q=J.has,Z=J.set;P=function(e,t){return Z.call(J,e,t),t},G=function(e){return X.call(J,e)||{}},U=function(e){return Q.call(J,e)}}else{var $=Y("state");W[$]=!0,P=function(e,t){return L(e,$,t),t},G=function(e){return I(e,$)?e[$]:{}},U=function(e){return I(e,$)}}var ee,te,ne={set:P,get:G,has:U,enforce:function(e){return U(e)?G(e):P(e,{})},getterFor:function(e){return function(t){var n;if(!v(t)||(n=G(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return n}}},oe=t((function(e){var t=ne.get,n=ne.enforce,o=String(String).split("String");(e.exports=function(e,t,a,s){var i=!!s&&!!s.unsafe,u=!!s&&!!s.enumerable,c=!!s&&!!s.noTargetGet;"function"==typeof a&&("string"!=typeof t||I(a,"name")||L(a,"name",t),n(a).source=o.join("string"==typeof t?t:"")),e!==r?(i?!c&&e[t]&&(u=!0):delete e[t],u?e[t]=a:L(e,t,a)):u?e[t]=a:R(t,a)})(Function.prototype,"toString",(function(){return"function"==typeof this&&t(this).source||F(this)}))})),re=r,ae=function(e){return"function"==typeof e?e:void 0},se=function(e,t){return arguments.length<2?ae(re[e])||ae(r[e]):re[e]&&re[e][t]||r[e]&&r[e][t]},ie=Math.ceil,ue=Math.floor,ce=function(e){return isNaN(e=+e)?0:(e>0?ue:ie)(e)},le=Math.min,de=function(e){return e>0?le(ce(e),9007199254740991):0},pe=Math.max,ge=Math.min,he=function(e,t){var n=ce(e);return n<0?pe(n+t,0):ge(n,t)},fe=function(e){return function(t,n,o){var r,a=m(t),s=de(a.length),i=he(o,s);if(e&&n!=n){for(;s>i;)if((r=a[i++])!=r)return!0}else for(;s>i;i++)if((e||i in a)&&a[i]===n)return e||i||0;return!e&&-1}},_e={includes:fe(!0),indexOf:fe(!1)},me=_e.indexOf,ve=function(e,t){var n,o=m(e),r=0,a=[];for(n in o)!I(W,n)&&I(o,n)&&a.push(n);for(;t.length>r;)I(o,n=t[r++])&&(~me(a,n)||a.push(n));return a},Me=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],ye=Me.concat("length","prototype"),Ie={f:Object.getOwnPropertyNames||function(e){return ve(e,ye)}},Te={f:Object.getOwnPropertySymbols},Se=se("Reflect","ownKeys")||function(e){var t=Ie.f(A(e)),n=Te.f;return n?t.concat(n(e)):t},Ee=function(e,t){for(var n=Se(t),o=O.f,r=C.f,a=0;a<n.length;a++){var s=n[a];I(e,s)||o(e,s,r(t,s))}},De=/#|\.prototype\./,ke=function(e,t){var n=Ae[Ce(e)];return n==Oe||n!=Ne&&("function"==typeof t?a(t):!!t)},Ce=ke.normalize=function(e){return String(e).replace(De,".").toLowerCase()},Ae=ke.data={},Ne=ke.NATIVE="N",Oe=ke.POLYFILL="P",Le=ke,Re=C.f,be=function(e,t){var n,o,a,i,u,c=e.target,l=e.global,d=e.stat;if(n=l?r:d?r[c]||R(c,{}):(r[c]||{}).prototype)for(o in t){if(i=t[o],a=e.noTargetGet?(u=Re(n,o))&&u.value:n[o],!Le(l?o:c+(d?".":"#")+o,e.forced)&&void 0!==a){if(s(i)==s(a))continue;Ee(i,a)}(e.sham||a&&a.sham)&&L(i,"sham",!0),oe(n,o,i,e)}},we=Array.isArray||function(e){return"Array"==g(e)},Pe=function(e){return Object(_(e))},Ge=function(e,t,n){var o=M(t);o in e?O.f(e,o,d(0,n)):e[o]=n},Ue=!!Object.getOwnPropertySymbols&&!a((function(){return!String(Symbol())})),Fe=Ue&&!Symbol.sham&&"symbol"==s(Symbol.iterator),qe=V("wks"),xe=r.Symbol,Ve=Fe?xe:xe&&xe.withoutSetter||H,Ke=function(e){return I(qe,e)||(Ue&&I(xe,e)?qe[e]=xe[e]:qe[e]=Ve("Symbol."+e)),qe[e]},Be=Ke("species"),He=function(e,t){var n;return we(e)&&("function"!=typeof(n=e.constructor)||n!==Array&&!we(n.prototype)?v(n)&&null===(n=n[Be])&&(n=void 0):n=void 0),new(void 0===n?Array:n)(0===t?0:t)},je=se("navigator","userAgent")||"",Ye=r.process,We=Ye&&Ye.versions,ze=We&&We.v8;ze?te=(ee=ze.split("."))[0]+ee[1]:je&&(!(ee=je.match(/Edge\/(\d+)/))||ee[1]>=74)&&(ee=je.match(/Chrome\/(\d+)/))&&(te=ee[1]);var Je=te&&+te,Xe=Ke("species"),Qe=function(e){return Je>=51||!a((function(){var t=[];return(t.constructor={})[Xe]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},Ze=Ke("isConcatSpreadable"),$e=Je>=51||!a((function(){var e=[];return e[Ze]=!1,e.concat()[0]!==e})),et=Qe("concat"),nt=function(e){if(!v(e))return!1;var t=e[Ze];return void 0!==t?!!t:we(e)};be({target:"Array",proto:!0,forced:!$e||!et},{concat:function(e){var t,n,o,r,a,s=Pe(this),i=He(s,0),u=0;for(t=-1,o=arguments.length;t<o;t++)if(a=-1===t?s:arguments[t],nt(a)){if(u+(r=de(a.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(n=0;n<r;n++,u++)n in a&&Ge(i,u,a[n])}else{if(u>=9007199254740991)throw TypeError("Maximum allowed index exceeded");Ge(i,u++,a)}return i.length=u,i}});var ot=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e},rt=function(e,t,n){if(ot(e),void 0===t)return e;switch(n){case 0:return function(){return e.call(t)};case 1:return function(n){return e.call(t,n)};case 2:return function(n,o){return e.call(t,n,o)};case 3:return function(n,o,r){return e.call(t,n,o,r)}}return function(){return e.apply(t,arguments)}},at=[].push,st=function(e){var t=1==e,n=2==e,o=3==e,r=4==e,a=6==e,s=5==e||a;return function(i,u,c,l){for(var d,p,g=Pe(i),h=f(g),_=rt(u,c,3),m=de(h.length),v=0,M=l||He,y=t?M(i,m):n?M(i,0):void 0;m>v;v++)if((s||v in h)&&(p=_(d=h[v],v,g),e))if(t)y[v]=p;else if(p)switch(e){case 3:return!0;case 5:return d;case 6:return v;case 2:at.call(y,d)}else if(r)return!1;return a?-1:o||r?r:y}},it={forEach:st(0),map:st(1),filter:st(2),some:st(3),every:st(4),find:st(5),findIndex:st(6)},ut=function(e,t){var n=[][e];return!!n&&a((function(){n.call(null,t||function(){throw 1},1)}))},ct=Object.defineProperty,lt={},dt=function(e){throw e},pt=function(e,t){if(I(lt,e))return lt[e];t||(t={});var n=[][e],o=!!I(t,"ACCESSORS")&&t.ACCESSORS,r=I(t,0)?t[0]:dt,s=I(t,1)?t[1]:void 0;return lt[e]=!!n&&!a((function(){if(o&&!i)return!0;var e={length:-1};o?ct(e,1,{enumerable:!0,get:dt}):e[1]=1,n.call(e,r,s)}))},gt=it.forEach,ht=ut("forEach"),ft=pt("forEach"),_t=ht&&ft?[].forEach:function(e){return gt(this,e,arguments.length>1?arguments[1]:void 0)};be({target:"Array",proto:!0,forced:[].forEach!=_t},{forEach:_t});var mt=function(e,t,n,o){try{return o?t(A(n)[0],n[1]):t(n)}catch(i){var r=e.return;throw void 0!==r&&A(r.call(e)),i}},vt={},Mt=Ke("iterator"),yt=Array.prototype,It=function(e){return void 0!==e&&(vt.Array===e||yt[Mt]===e)},Tt={};Tt[Ke("toStringTag")]="z";var St="[object z]"===String(Tt),Et=Ke("toStringTag"),Dt="Arguments"==g(function(){return arguments}()),kt=St?g:function(e){var t,n,o;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(n){}}(t=Object(e),Et))?n:Dt?g(t):"Object"==(o=g(t))&&"function"==typeof t.callee?"Arguments":o},Ct=Ke("iterator"),At=function(e){if(null!=e)return e[Ct]||e["@@iterator"]||vt[kt(e)]},Nt=function(e){var t,n,o,r,a,s,i=Pe(e),u="function"==typeof this?this:Array,c=arguments.length,l=c>1?arguments[1]:void 0,d=void 0!==l,p=At(i),g=0;if(d&&(l=rt(l,c>2?arguments[2]:void 0,2)),null==p||u==Array&&It(p))for(n=new u(t=de(i.length));t>g;g++)s=d?l(i[g],g):i[g],Ge(n,g,s);else for(a=(r=p.call(i)).next,n=new u;!(o=a.call(r)).done;g++)s=d?mt(r,l,[o.value,g],!0):o.value,Ge(n,g,s);return n.length=g,n},Ot=Ke("iterator"),Lt=!1;try{var Rt=0,bt={next:function(){return{done:!!Rt++}},return:function(){Lt=!0}};bt[Ot]=function(){return this},Array.from(bt,(function(){throw 2}))}catch(Xv){}var wt=function(e,t){if(!t&&!Lt)return!1;var n=!1;try{var o={};o[Ot]=function(){return{next:function(){return{done:n=!0}}}},e(o)}catch(Xv){}return n},Pt=!wt((function(e){Array.from(e)}));be({target:"Array",stat:!0,forced:Pt},{from:Nt});var Gt,Ut=Object.keys||function(e){return ve(e,Me)},Ft=i?Object.defineProperties:function(e,t){A(e);for(var n,o=Ut(t),r=o.length,a=0;r>a;)O.f(e,n=o[a++],t[n]);return e},qt=se("document","documentElement"),xt=Y("IE_PROTO"),Vt=function(){},Kt=function(e){return"<script>"+e+"<\/script>"},Bt=function(){try{Gt=document.domain&&new ActiveXObject("htmlfile")}catch(Xv){}var e,t;Bt=Gt?function(e){e.write(Kt("")),e.close();var t=e.parentWindow.Object;return e=null,t}(Gt):((t=E("iframe")).style.display="none",qt.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(Kt("document.F=Object")),e.close(),e.F);for(var n=Me.length;n--;)delete Bt.prototype[Me[n]];return Bt()};W[xt]=!0;var Ht=Object.create||function(e,t){var n;return null!==e?(Vt.prototype=A(e),n=new Vt,Vt.prototype=null,n[xt]=e):n=Bt(),void 0===t?n:Ft(n,t)};be({target:"Object",stat:!0,sham:!i},{create:Ht});var jt=a((function(){Ut(1)}));be({target:"Object",stat:!0,forced:jt},{keys:function(e){return Ut(Pe(e))}});var Yt="\t\n\v\f\r                　\u2028\u2029\ufeff",Wt="["+Yt+"]",zt=RegExp("^"+Wt+Wt+"*"),Jt=RegExp(Wt+Wt+"*$"),Xt=function(e){return function(t){var n=String(_(t));return 1&e&&(n=n.replace(zt,"")),2&e&&(n=n.replace(Jt,"")),n}},Qt={start:Xt(1),end:Xt(2),trim:Xt(3)},Zt=Qt.trim,$t=r.parseInt,en=/^[+-]?0[Xx]/,tn=8!==$t(Yt+"08")||22!==$t(Yt+"0x16")?function(e,t){var n=Zt(String(e));return $t(n,t>>>0||(en.test(n)?16:10))}:$t;be({global:!0,forced:parseInt!=tn},{parseInt:tn});var nn,on,rn,an=function(e){return function(t,n){var o,r,a=String(_(t)),s=ce(n),i=a.length;return s<0||s>=i?e?"":void 0:(o=a.charCodeAt(s))<55296||o>56319||s+1===i||(r=a.charCodeAt(s+1))<56320||r>57343?e?a.charAt(s):o:e?a.slice(s,s+2):r-56320+(o-55296<<10)+65536}},sn={codeAt:an(!1),charAt:an(!0)},un=!a((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),cn=Y("IE_PROTO"),ln=Object.prototype,dn=un?Object.getPrototypeOf:function(e){return e=Pe(e),I(e,cn)?e[cn]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?ln:null},pn=Ke("iterator"),gn=!1;[].keys&&("next"in(rn=[].keys())?(on=dn(dn(rn)))!==Object.prototype&&(nn=on):gn=!0),null==nn&&(nn={}),I(nn,pn)||L(nn,pn,(function(){return this}));var hn={IteratorPrototype:nn,BUGGY_SAFARI_ITERATORS:gn},fn=O.f,_n=Ke("toStringTag"),mn=function(e,t,n){e&&!I(e=n?e:e.prototype,_n)&&fn(e,_n,{configurable:!0,value:t})},vn=hn.IteratorPrototype,Mn=function(){return this},yn=function(e,t,n){var o=t+" Iterator";return e.prototype=Ht(vn,{next:d(1,n)}),mn(e,o,!1),vt[o]=Mn,e},In=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(n,[]),t=n instanceof Array}catch(Xv){}return function(n,o){return A(n),function(e){if(!v(e)&&null!==e)throw TypeError("Can't set "+String(e)+" as a prototype")}(o),t?e.call(n,o):n.__proto__=o,n}}():void 0),Tn=hn.IteratorPrototype,Sn=hn.BUGGY_SAFARI_ITERATORS,En=Ke("iterator"),Dn=function(){return this},kn=function(e,t,n,o,r,a,s){yn(n,t,o);var i,u,c,l=function(e){if(e===r&&f)return f;if(!Sn&&e in g)return g[e];switch(e){case"keys":case"values":case"entries":return function(){return new n(this,e)}}return function(){return new n(this)}},d=t+" Iterator",p=!1,g=e.prototype,h=g[En]||g["@@iterator"]||r&&g[r],f=!Sn&&h||l(r),_="Array"==t&&g.entries||h;if(_&&(i=dn(_.call(new e)),Tn!==Object.prototype&&i.next&&(dn(i)!==Tn&&(In?In(i,Tn):"function"!=typeof i[En]&&L(i,En,Dn)),mn(i,d,!0))),"values"==r&&h&&"values"!==h.name&&(p=!0,f=function(){return h.call(this)}),g[En]!==f&&L(g,En,f),vt[t]=f,r)if(u={values:l("values"),keys:a?f:l("keys"),entries:l("entries")},s)for(c in u)(Sn||p||!(c in g))&&oe(g,c,u[c]);else be({target:t,proto:!0,forced:Sn||p},u);return u},Cn=sn.charAt,An=ne.set,Nn=ne.getterFor("String Iterator");kn(String,"String",(function(e){An(this,{type:"String Iterator",string:String(e),index:0})}),(function(){var e,t=Nn(this),n=t.string,o=t.index;return o>=n.length?{value:void 0,done:!0}:(e=Cn(n,o),t.index+=e.length,{value:e,done:!1})}));var On={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0};for(var Ln in On){var Rn=r[Ln],bn=Rn&&Rn.prototype;if(bn&&bn.forEach!==_t)try{L(bn,"forEach",_t)}catch(Xv){bn.forEach=_t}}function wn(e){return(wn="function"==typeof Symbol&&"symbol"==s(Symbol.iterator)?function(e){return s(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":s(e)})(e)}function Pn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Gn(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function Un(e,t,n){return t&&Gn(e.prototype,t),n&&Gn(e,n),e}function Fn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function qn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function xn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?qn(Object(n),!0).forEach((function(t){Fn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):qn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Vn(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Bn(e,t)}function Kn(e){return(Kn=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Bn(e,t){return(Bn=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Hn(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function jn(e,t,n){return(jn=Hn()?Reflect.construct:function(e,t,n){var o=[null];o.push.apply(o,t);var r=new(Function.bind.apply(e,o));return n&&Bn(r,n.prototype),r}).apply(null,arguments)}function Yn(e){var t="function"==typeof Map?new Map:void 0;return(Yn=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,o)}function o(){return jn(e,arguments,Kn(this).constructor)}return o.prototype=Object.create(e.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),Bn(o,e)})(e)}function Wn(e,t){if(null==e)return{};var n,o,r=function(e,t){if(null==e)return{};var n,o,r={},a=Object.keys(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function zn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Jn(e,t){return!t||"object"!=s(t)&&"function"!=typeof t?zn(e):t}function Xn(e){return function(){var t,n=Kn(e);if(Hn()){var o=Kn(this).constructor;t=Reflect.construct(n,arguments,o)}else t=n.apply(this,arguments);return Jn(this,t)}}function Qn(e,t){return $n(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var n=[],o=!0,r=!1,a=void 0;try{for(var s,i=e[Symbol.iterator]();!(o=(s=i.next()).done)&&(n.push(s.value),!t||n.length!==t);o=!0);}catch(l){r=!0,a=l}finally{try{o||null==i.return||i.return()}finally{if(r)throw a}}return n}}(e,t)||to(e,t)||oo()}function Zn(e){return function(e){if(Array.isArray(e))return no(e)}(e)||eo(e)||to(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $n(e){if(Array.isArray(e))return e}function eo(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function to(e,t){if(e){if("string"==typeof e)return no(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?no(e,t):void 0}}function no(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function oo(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ro(e){if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(e=to(e))){var t=0,n=function(){};return{s:n,n:function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,r,a=!0,s=!1;return{s:function(){o=e[Symbol.iterator]()},n:function(){var e=o.next();return a=e.done,e},e:function(e){s=!0,r=e},f:function(){try{a||null==o.return||o.return()}finally{if(s)throw r}}}}var ao={SDK_READY:"sdkStateReady",SDK_NOT_READY:"sdkStateNotReady",SDK_DESTROY:"sdkDestroy",MESSAGE_RECEIVED:"onMessageReceived",MESSAGE_MODIFIED:"onMessageModified",MESSAGE_REVOKED:"onMessageRevoked",MESSAGE_READ_BY_PEER:"onMessageReadByPeer",CONVERSATION_LIST_UPDATED:"onConversationListUpdated",GROUP_LIST_UPDATED:"onGroupListUpdated",GROUP_SYSTEM_NOTICE_RECEIVED:"receiveGroupSystemNotice",GROUP_ATTRIBUTES_UPDATED:"groupAttributesUpdated",PROFILE_UPDATED:"onProfileUpdated",BLACKLIST_UPDATED:"blacklistUpdated",FRIEND_LIST_UPDATED:"onFriendListUpdated",FRIEND_GROUP_LIST_UPDATED:"onFriendGroupListUpdated",FRIEND_APPLICATION_LIST_UPDATED:"onFriendApplicationListUpdated",KICKED_OUT:"kickedOut",ERROR:"error",NET_STATE_CHANGE:"netStateChange",SDK_RELOAD:"sdkReload"},so={MSG_TEXT:"TIMTextElem",MSG_IMAGE:"TIMImageElem",MSG_SOUND:"TIMSoundElem",MSG_AUDIO:"TIMSoundElem",MSG_FILE:"TIMFileElem",MSG_FACE:"TIMFaceElem",MSG_VIDEO:"TIMVideoFileElem",MSG_GEO:"TIMLocationElem",MSG_LOCATION:"TIMLocationElem",MSG_GRP_TIP:"TIMGroupTipElem",MSG_GRP_SYS_NOTICE:"TIMGroupSystemNoticeElem",MSG_CUSTOM:"TIMCustomElem",MSG_MERGER:"TIMRelayElem",MSG_PRIORITY_HIGH:"High",MSG_PRIORITY_NORMAL:"Normal",MSG_PRIORITY_LOW:"Low",MSG_PRIORITY_LOWEST:"Lowest",CONV_C2C:"C2C",CONV_GROUP:"GROUP",CONV_SYSTEM:"@TIM#SYSTEM",CONV_AT_ME:1,CONV_AT_ALL:2,CONV_AT_ALL_AT_ME:3,GRP_PRIVATE:"Private",GRP_WORK:"Private",GRP_PUBLIC:"Public",GRP_CHATROOM:"ChatRoom",GRP_MEETING:"ChatRoom",GRP_AVCHATROOM:"AVChatRoom",GRP_MBR_ROLE_OWNER:"Owner",GRP_MBR_ROLE_ADMIN:"Admin",GRP_MBR_ROLE_MEMBER:"Member",GRP_TIP_MBR_JOIN:1,GRP_TIP_MBR_QUIT:2,GRP_TIP_MBR_KICKED_OUT:3,GRP_TIP_MBR_SET_ADMIN:4,GRP_TIP_MBR_CANCELED_ADMIN:5,GRP_TIP_GRP_PROFILE_UPDATED:6,GRP_TIP_MBR_PROFILE_UPDATED:7,MSG_REMIND_ACPT_AND_NOTE:"AcceptAndNotify",MSG_REMIND_ACPT_NOT_NOTE:"AcceptNotNotify",MSG_REMIND_DISCARD:"Discard",GENDER_UNKNOWN:"Gender_Type_Unknown",GENDER_FEMALE:"Gender_Type_Female",GENDER_MALE:"Gender_Type_Male",KICKED_OUT_MULT_ACCOUNT:"multipleAccount",KICKED_OUT_MULT_DEVICE:"multipleDevice",KICKED_OUT_USERSIG_EXPIRED:"userSigExpired",ALLOW_TYPE_ALLOW_ANY:"AllowType_Type_AllowAny",ALLOW_TYPE_NEED_CONFIRM:"AllowType_Type_NeedConfirm",ALLOW_TYPE_DENY_ANY:"AllowType_Type_DenyAny",FORBID_TYPE_NONE:"AdminForbid_Type_None",FORBID_TYPE_SEND_OUT:"AdminForbid_Type_SendOut",JOIN_OPTIONS_FREE_ACCESS:"FreeAccess",JOIN_OPTIONS_NEED_PERMISSION:"NeedPermission",JOIN_OPTIONS_DISABLE_APPLY:"DisableApply",JOIN_STATUS_SUCCESS:"JoinedSuccess",JOIN_STATUS_ALREADY_IN_GROUP:"AlreadyInGroup",JOIN_STATUS_WAIT_APPROVAL:"WaitAdminApproval",GRP_PROFILE_OWNER_ID:"ownerID",GRP_PROFILE_CREATE_TIME:"createTime",GRP_PROFILE_LAST_INFO_TIME:"lastInfoTime",GRP_PROFILE_MEMBER_NUM:"memberNum",GRP_PROFILE_MAX_MEMBER_NUM:"maxMemberNum",GRP_PROFILE_JOIN_OPTION:"joinOption",GRP_PROFILE_INTRODUCTION:"introduction",GRP_PROFILE_NOTIFICATION:"notification",GRP_PROFILE_MUTE_ALL_MBRS:"muteAllMembers",SNS_ADD_TYPE_SINGLE:"Add_Type_Single",SNS_ADD_TYPE_BOTH:"Add_Type_Both",SNS_DELETE_TYPE_SINGLE:"Delete_Type_Single",SNS_DELETE_TYPE_BOTH:"Delete_Type_Both",SNS_APPLICATION_TYPE_BOTH:"Pendency_Type_Both",SNS_APPLICATION_SENT_TO_ME:"Pendency_Type_ComeIn",SNS_APPLICATION_SENT_BY_ME:"Pendency_Type_SendOut",SNS_APPLICATION_AGREE:"Response_Action_Agree",SNS_APPLICATION_AGREE_AND_ADD:"Response_Action_AgreeAndAdd",SNS_CHECK_TYPE_BOTH:"CheckResult_Type_Both",SNS_CHECK_TYPE_SINGLE:"CheckResult_Type_Single",SNS_TYPE_NO_RELATION:"CheckResult_Type_NoRelation",SNS_TYPE_A_WITH_B:"CheckResult_Type_AWithB",SNS_TYPE_B_WITH_A:"CheckResult_Type_BWithA",SNS_TYPE_BOTH_WAY:"CheckResult_Type_BothWay",NET_STATE_CONNECTED:"connected",NET_STATE_CONNECTING:"connecting",NET_STATE_DISCONNECTED:"disconnected",MSG_AT_ALL:"__kImSDK_MesssageAtALL__"},io=it.map,uo=Qe("map"),co=pt("map");be({target:"Array",proto:!0,forced:!uo||!co},{map:function(e){return io(this,e,arguments.length>1?arguments[1]:void 0)}});var lo=[].slice,po={},go=function(e,t,n){if(!(t in po)){for(var o=[],r=0;r<t;r++)o[r]="a["+r+"]";po[t]=Function("C,a","return new C("+o.join(",")+")")}return po[t](e,n)},ho=Function.bind||function(e){var t=ot(this),n=lo.call(arguments,1),o=function o(){var r=n.concat(lo.call(arguments));return this instanceof o?go(t,r.length,r):t.apply(e,r)};return v(t.prototype)&&(o.prototype=t.prototype),o};be({target:"Function",proto:!0},{bind:ho});var fo=function(){function e(){Pn(this,e),this.cache=[],this.options=null}return Un(e,[{key:"use",value:function(e){if("function"!=typeof e)throw"middleware must be a function";return this.cache.push(e),this}},{key:"next",value:function(e){if(this.middlewares&&this.middlewares.length>0)return this.middlewares.shift().call(this,this.options,this.next.bind(this))}},{key:"run",value:function(e){return this.middlewares=this.cache.map((function(e){return e})),this.options=e,this.next()}}]),e}(),_o=O.f,mo=Function.prototype,vo=mo.toString,Mo=/^\s*function ([^ (]*)/;i&&!("name"in mo)&&_o(mo,"name",{configurable:!0,get:function(){try{return vo.call(this).match(Mo)[1]}catch(Xv){return""}}});var yo=t((function(t,n){var o,r,a,i,u,c,l,d,p,g,h,f,_,m,v,M,y,I;t.exports=(o="function"==typeof Promise,r="object"==("undefined"===typeof self?"undefined":s(self))?self:e,a="undefined"!=typeof Symbol,i="undefined"!=typeof Map,u="undefined"!=typeof Set,c="undefined"!=typeof WeakMap,l="undefined"!=typeof WeakSet,d="undefined"!=typeof DataView,p=a&&void 0!==Symbol.iterator,g=a&&void 0!==Symbol.toStringTag,h=u&&"function"==typeof Set.prototype.entries,f=i&&"function"==typeof Map.prototype.entries,_=h&&Object.getPrototypeOf((new Set).entries()),m=f&&Object.getPrototypeOf((new Map).entries()),v=p&&"function"==typeof Array.prototype[Symbol.iterator],M=v&&Object.getPrototypeOf([][Symbol.iterator]()),y=p&&"function"==typeof String.prototype[Symbol.iterator],I=y&&Object.getPrototypeOf(""[Symbol.iterator]()),function(e){var t=s(e);if("object"!==t)return t;if(null===e)return"null";if(e===r)return"global";if(Array.isArray(e)&&(!1===g||!(Symbol.toStringTag in e)))return"Array";if("object"==("undefined"===typeof window?"undefined":s(window))&&null!==window){if("object"==s(window.location)&&e===window.location)return"Location";if("object"==s(window.document)&&e===window.document)return"Document";if("object"==s(window.navigator)){if("object"==s(window.navigator.mimeTypes)&&e===window.navigator.mimeTypes)return"MimeTypeArray";if("object"==s(window.navigator.plugins)&&e===window.navigator.plugins)return"PluginArray"}if(("function"==typeof window.HTMLElement||"object"==s(window.HTMLElement))&&e instanceof window.HTMLElement){if("BLOCKQUOTE"===e.tagName)return"HTMLQuoteElement";if("TD"===e.tagName)return"HTMLTableDataCellElement";if("TH"===e.tagName)return"HTMLTableHeaderCellElement"}}var n=g&&e[Symbol.toStringTag];if("string"==typeof n)return n;var a=Object.getPrototypeOf(e);return a===RegExp.prototype?"RegExp":a===Date.prototype?"Date":o&&a===Promise.prototype?"Promise":u&&a===Set.prototype?"Set":i&&a===Map.prototype?"Map":l&&a===WeakSet.prototype?"WeakSet":c&&a===WeakMap.prototype?"WeakMap":d&&a===DataView.prototype?"DataView":i&&a===m?"Map Iterator":u&&a===_?"Set Iterator":v&&a===M?"Array Iterator":y&&a===I?"String Iterator":null===a?"Object":Object.prototype.toString.call(e).slice(8,-1)})}));be({target:"Array",stat:!0},{isArray:we});var Io=Ke("unscopables"),To=Array.prototype;null==To[Io]&&O.f(To,Io,{configurable:!0,value:Ht(null)});var So=function(e){To[Io][e]=!0},Eo=it.find,Do=!0,ko=pt("find");"find"in[]&&Array(1).find((function(){Do=!1})),be({target:"Array",proto:!0,forced:Do||!ko},{find:function(e){return Eo(this,e,arguments.length>1?arguments[1]:void 0)}}),So("find");var Co=_e.includes,Ao=pt("indexOf",{ACCESSORS:!0,1:0});be({target:"Array",proto:!0,forced:!Ao},{includes:function(e){return Co(this,e,arguments.length>1?arguments[1]:void 0)}}),So("includes");var No=_e.indexOf,Oo=[].indexOf,Lo=!!Oo&&1/[1].indexOf(1,-0)<0,Ro=ut("indexOf"),bo=pt("indexOf",{ACCESSORS:!0,1:0});be({target:"Array",proto:!0,forced:Lo||!Ro||!bo},{indexOf:function(e){return Lo?Oo.apply(this,arguments)||0:No(this,e,arguments.length>1?arguments[1]:void 0)}});var wo=ne.set,Po=ne.getterFor("Array Iterator"),Go=kn(Array,"Array",(function(e,t){wo(this,{type:"Array Iterator",target:m(e),index:0,kind:t})}),(function(){var e=Po(this),t=e.target,n=e.kind,o=e.index++;return!t||o>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:o,done:!1}:"values"==n?{value:t[o],done:!1}:{value:[o,t[o]],done:!1}}),"values");vt.Arguments=vt.Array,So("keys"),So("values"),So("entries");var Uo=[].join,Fo=f!=Object,qo=ut("join",",");be({target:"Array",proto:!0,forced:Fo||!qo},{join:function(e){return Uo.call(m(this),void 0===e?",":e)}});var xo=Qe("slice"),Vo=pt("slice",{ACCESSORS:!0,0:0,1:2}),Ko=Ke("species"),Bo=[].slice,Ho=Math.max;be({target:"Array",proto:!0,forced:!xo||!Vo},{slice:function(e,t){var n,o,r,a=m(this),s=de(a.length),i=he(e,s),u=he(void 0===t?s:t,s);if(we(a)&&("function"!=typeof(n=a.constructor)||n!==Array&&!we(n.prototype)?v(n)&&null===(n=n[Ko])&&(n=void 0):n=void 0,n===Array||void 0===n))return Bo.call(a,i,u);for(o=new(void 0===n?Array:n)(Ho(u-i,0)),r=0;i<u;i++,r++)i in a&&Ge(o,r,a[i]);return o.length=r,o}}),be({target:"Date",stat:!0},{now:function(){return(new Date).getTime()}});var jo="".repeat||function(e){var t=String(_(this)),n="",o=ce(e);if(o<0||1/0==o)throw RangeError("Wrong number of repetitions");for(;o>0;(o>>>=1)&&(t+=t))1&o&&(n+=t);return n},Yo=Math.ceil,Wo=function(e){return function(t,n,o){var r,a,s=String(_(t)),i=s.length,u=void 0===o?" ":String(o),c=de(n);return c<=i||""==u?s:(r=c-i,(a=jo.call(u,Yo(r/u.length))).length>r&&(a=a.slice(0,r)),e?s+a:a+s)}},zo={start:Wo(!1),end:Wo(!0)}.start,Jo=Math.abs,Xo=Date.prototype,Qo=Xo.getTime,Zo=Xo.toISOString,$o=a((function(){return"0385-07-25T07:06:39.999Z"!=Zo.call(new Date(-50000000000001))}))||!a((function(){Zo.call(new Date(NaN))}))?function(){if(!isFinite(Qo.call(this)))throw RangeError("Invalid time value");var e=this.getUTCFullYear(),t=this.getUTCMilliseconds(),n=e<0?"-":e>9999?"+":"";return n+zo(Jo(e),n?6:4,0)+"-"+zo(this.getUTCMonth()+1,2,0)+"-"+zo(this.getUTCDate(),2,0)+"T"+zo(this.getUTCHours(),2,0)+":"+zo(this.getUTCMinutes(),2,0)+":"+zo(this.getUTCSeconds(),2,0)+"."+zo(t,3,0)+"Z"}:Zo;be({target:"Date",proto:!0,forced:Date.prototype.toISOString!==$o},{toISOString:$o});var er=Date.prototype,tr=er.toString,nr=er.getTime;new Date(NaN)+""!="Invalid Date"&&oe(er,"toString",(function(){var e=nr.call(this);return e==e?tr.call(this):"Invalid Date"}));var or=function(e,t,n){var o,r;return In&&"function"==typeof(o=t.constructor)&&o!==n&&v(r=o.prototype)&&r!==n.prototype&&In(e,r),e},rr=Ie.f,ar=C.f,sr=O.f,ir=Qt.trim,ur=r.Number,cr=ur.prototype,lr="Number"==g(Ht(cr)),dr=function(e){var t,n,o,r,a,s,i,u,c=M(e,!1);if("string"==typeof c&&c.length>2)if(43===(t=(c=ir(c)).charCodeAt(0))||45===t){if(88===(n=c.charCodeAt(2))||120===n)return NaN}else if(48===t){switch(c.charCodeAt(1)){case 66:case 98:o=2,r=49;break;case 79:case 111:o=8,r=55;break;default:return+c}for(s=(a=c.slice(2)).length,i=0;i<s;i++)if((u=a.charCodeAt(i))<48||u>r)return NaN;return parseInt(a,o)}return+c};if(Le("Number",!ur(" 0o1")||!ur("0b1")||ur("+0x1"))){for(var pr,gr=function e(t){var n=arguments.length<1?0:t,o=this;return o instanceof e&&(lr?a((function(){cr.valueOf.call(o)})):"Number"!=g(o))?or(new ur(dr(n)),o,e):dr(n)},hr=i?rr(ur):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),fr=0;hr.length>fr;fr++)I(ur,pr=hr[fr])&&!I(gr,pr)&&sr(gr,pr,ar(ur,pr));gr.prototype=cr,cr.constructor=gr,oe(r,"Number",gr)}var _r=Ie.f,mr={}.toString,vr="object"==("undefined"===typeof window?"undefined":s(window))&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],Mr={f:function(e){return vr&&"[object Window]"==mr.call(e)?function(e){try{return _r(e)}catch(Xv){return vr.slice()}}(e):_r(m(e))}},yr=Mr.f,Ir=a((function(){return!Object.getOwnPropertyNames(1)}));be({target:"Object",stat:!0,forced:Ir},{getOwnPropertyNames:yr});var Tr=a((function(){dn(1)}));be({target:"Object",stat:!0,forced:Tr,sham:!un},{getPrototypeOf:function(e){return dn(Pe(e))}});var Sr=St?{}.toString:function(){return"[object "+kt(this)+"]"};St||oe(Object.prototype,"toString",Sr,{unsafe:!0});var Er,Dr,kr,Cr=r.Promise,Ar=function(e,t,n){for(var o in t)oe(e,o,t[o],n);return e},Nr=Ke("species"),Or=function(e){var t=se(e),n=O.f;i&&t&&!t[Nr]&&n(t,Nr,{configurable:!0,get:function(){return this}})},Lr=function(e,t,n){if(!(e instanceof t))throw TypeError("Incorrect "+(n?n+" ":"")+"invocation");return e},Rr=t((function(e){var t=function(e,t){this.stopped=e,this.result=t};(e.exports=function(e,n,o,r,a){var i,u,c,l,d,p,g,h=rt(n,o,r?2:1);if(a)i=e;else{if("function"!=typeof(u=At(e)))throw TypeError("Target is not iterable");if(It(u)){for(c=0,l=de(e.length);l>c;c++)if((d=r?h(A(g=e[c])[0],g[1]):h(e[c]))&&d instanceof t)return d;return new t(!1)}i=u.call(e)}for(p=i.next;!(g=p.call(i)).done;)if("object"==s(d=mt(i,h,g.value,r))&&d&&d instanceof t)return d;return new t(!1)}).stop=function(e){return new t(!0,e)}})),br=Ke("species"),wr=function(e,t){var n,o=A(e).constructor;return void 0===o||null==(n=A(o)[br])?t:ot(n)},Pr=/(iphone|ipod|ipad).*applewebkit/i.test(je),Gr=r.location,Ur=r.setImmediate,Fr=r.clearImmediate,qr=r.process,xr=r.MessageChannel,Vr=r.Dispatch,Kr=0,Br={},Hr=function(e){if(Br.hasOwnProperty(e)){var t=Br[e];delete Br[e],t()}},jr=function(e){return function(){Hr(e)}},Yr=function(e){Hr(e.data)},Wr=function(e){r.postMessage(e+"",Gr.protocol+"//"+Gr.host)};Ur&&Fr||(Ur=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return Br[++Kr]=function(){("function"==typeof e?e:Function(e)).apply(void 0,t)},Er(Kr),Kr},Fr=function(e){delete Br[e]},"process"==g(qr)?Er=function(e){qr.nextTick(jr(e))}:Vr&&Vr.now?Er=function(e){Vr.now(jr(e))}:xr&&!Pr?(kr=(Dr=new xr).port2,Dr.port1.onmessage=Yr,Er=rt(kr.postMessage,kr,1)):!r.addEventListener||"function"!=typeof postMessage||r.importScripts||a(Wr)?Er="onreadystatechange"in E("script")?function(e){qt.appendChild(E("script")).onreadystatechange=function(){qt.removeChild(this),Hr(e)}}:function(e){setTimeout(jr(e),0)}:(Er=Wr,r.addEventListener("message",Yr,!1)));var zr,Jr,Xr,Qr,Zr,$r,ea,ta,na={set:Ur,clear:Fr},oa=C.f,ra=na.set,aa=r.MutationObserver||r.WebKitMutationObserver,sa=r.process,ia=r.Promise,ua="process"==g(sa),ca=oa(r,"queueMicrotask"),la=ca&&ca.value;la||(zr=function(){var e,t;for(ua&&(e=sa.domain)&&e.exit();Jr;){t=Jr.fn,Jr=Jr.next;try{t()}catch(Xv){throw Jr?Qr():Xr=void 0,Xv}}Xr=void 0,e&&e.enter()},ua?Qr=function(){sa.nextTick(zr)}:aa&&!Pr?(Zr=!0,$r=document.createTextNode(""),new aa(zr).observe($r,{characterData:!0}),Qr=function(){$r.data=Zr=!Zr}):ia&&ia.resolve?(ea=ia.resolve(void 0),ta=ea.then,Qr=function(){ta.call(ea,zr)}):Qr=function(){ra.call(r,zr)});var da,pa,ga,ha,fa=la||function(e){var t={fn:e,next:void 0};Xr&&(Xr.next=t),Jr||(Jr=t,Qr()),Xr=t},_a=function(e){var t,n;this.promise=new e((function(e,o){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=o})),this.resolve=ot(t),this.reject=ot(n)},ma={f:function(e){return new _a(e)}},va=function(e,t){if(A(e),v(t)&&t.constructor===e)return t;var n=ma.f(e);return(0,n.resolve)(t),n.promise},Ma=function(e){try{return{error:!1,value:e()}}catch(Xv){return{error:!0,value:Xv}}},ya=na.set,Ia=Ke("species"),Ta="Promise",Sa=ne.get,Ea=ne.set,Da=ne.getterFor(Ta),ka=Cr,Ca=r.TypeError,Aa=r.document,Na=r.process,Oa=se("fetch"),La=ma.f,Ra=La,ba="process"==g(Na),wa=!!(Aa&&Aa.createEvent&&r.dispatchEvent),Pa=Le(Ta,(function(){if(F(ka)===String(ka)){if(66===Je)return!0;if(!ba&&"function"!=typeof PromiseRejectionEvent)return!0}if(Je>=51&&/native code/.test(ka))return!1;var e=ka.resolve(1),t=function(e){e((function(){}),(function(){}))};return(e.constructor={})[Ia]=t,!(e.then((function(){}))instanceof t)})),Ga=Pa||!wt((function(e){ka.all(e).catch((function(){}))})),Ua=function(e){var t;return!(!v(e)||"function"!=typeof(t=e.then))&&t},Fa=function(e,t,n){if(!t.notified){t.notified=!0;var o=t.reactions;fa((function(){for(var r=t.value,a=1==t.state,s=0;o.length>s;){var i,u,c,l=o[s++],d=a?l.ok:l.fail,p=l.resolve,g=l.reject,h=l.domain;try{d?(a||(2===t.rejection&&Ka(e,t),t.rejection=1),!0===d?i=r:(h&&h.enter(),i=d(r),h&&(h.exit(),c=!0)),i===l.promise?g(Ca("Promise-chain cycle")):(u=Ua(i))?u.call(i,p,g):p(i)):g(r)}catch(Xv){h&&!c&&h.exit(),g(Xv)}}t.reactions=[],t.notified=!1,n&&!t.rejection&&xa(e,t)}))}},qa=function(e,t,n){var o,a;wa?((o=Aa.createEvent("Event")).promise=t,o.reason=n,o.initEvent(e,!1,!0),r.dispatchEvent(o)):o={promise:t,reason:n},(a=r["on"+e])?a(o):"unhandledrejection"===e&&function(e,t){var n=r.console;n&&n.error&&(1===arguments.length?n.error(e):n.error(e,t))}("Unhandled promise rejection",n)},xa=function(e,t){ya.call(r,(function(){var n,o=t.value;if(Va(t)&&(n=Ma((function(){ba?Na.emit("unhandledRejection",o,e):qa("unhandledrejection",e,o)})),t.rejection=ba||Va(t)?2:1,n.error))throw n.value}))},Va=function(e){return 1!==e.rejection&&!e.parent},Ka=function(e,t){ya.call(r,(function(){ba?Na.emit("rejectionHandled",e):qa("rejectionhandled",e,t.value)}))},Ba=function(e,t,n,o){return function(r){e(t,n,r,o)}},Ha=function(e,t,n,o){t.done||(t.done=!0,o&&(t=o),t.value=n,t.state=2,Fa(e,t,!0))},ja=function e(t,n,o,r){if(!n.done){n.done=!0,r&&(n=r);try{if(t===o)throw Ca("Promise can't be resolved itself");var a=Ua(o);a?fa((function(){var r={done:!1};try{a.call(o,Ba(e,t,r,n),Ba(Ha,t,r,n))}catch(Xv){Ha(t,r,Xv,n)}})):(n.value=o,n.state=1,Fa(t,n,!1))}catch(Xv){Ha(t,{done:!1},Xv,n)}}};Pa&&(ka=function(e){Lr(this,ka,Ta),ot(e),da.call(this);var t=Sa(this);try{e(Ba(ja,this,t),Ba(Ha,this,t))}catch(Xv){Ha(this,t,Xv)}},(da=function(e){Ea(this,{type:Ta,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=Ar(ka.prototype,{then:function(e,t){var n=Da(this),o=La(wr(this,ka));return o.ok="function"!=typeof e||e,o.fail="function"==typeof t&&t,o.domain=ba?Na.domain:void 0,n.parent=!0,n.reactions.push(o),0!=n.state&&Fa(this,n,!1),o.promise},catch:function(e){return this.then(void 0,e)}}),pa=function(){var e=new da,t=Sa(e);this.promise=e,this.resolve=Ba(ja,e,t),this.reject=Ba(Ha,e,t)},ma.f=La=function(e){return e===ka||e===ga?new pa(e):Ra(e)},"function"==typeof Cr&&(ha=Cr.prototype.then,oe(Cr.prototype,"then",(function(e,t){var n=this;return new ka((function(e,t){ha.call(n,e,t)})).then(e,t)}),{unsafe:!0}),"function"==typeof Oa&&be({global:!0,enumerable:!0,forced:!0},{fetch:function(e){return va(ka,Oa.apply(r,arguments))}}))),be({global:!0,wrap:!0,forced:Pa},{Promise:ka}),mn(ka,Ta,!1),Or(Ta),ga=se(Ta),be({target:Ta,stat:!0,forced:Pa},{reject:function(e){var t=La(this);return t.reject.call(void 0,e),t.promise}}),be({target:Ta,stat:!0,forced:Pa},{resolve:function(e){return va(this,e)}}),be({target:Ta,stat:!0,forced:Ga},{all:function(e){var t=this,n=La(t),o=n.resolve,r=n.reject,a=Ma((function(){var n=ot(t.resolve),a=[],s=0,i=1;Rr(e,(function(e){var u=s++,c=!1;a.push(void 0),i++,n.call(t,e).then((function(e){c||(c=!0,a[u]=e,--i||o(a))}),r)})),--i||o(a)}));return a.error&&r(a.value),n.promise},race:function(e){var t=this,n=La(t),o=n.reject,r=Ma((function(){var r=ot(t.resolve);Rr(e,(function(e){r.call(t,e).then(n.resolve,o)}))}));return r.error&&o(r.value),n.promise}});var Ya=function(){var e=A(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t};function Wa(e,t){return RegExp(e,t)}var za,Ja,Xa={UNSUPPORTED_Y:a((function(){var e=Wa("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),BROKEN_CARET:a((function(){var e=Wa("^r","gy");return e.lastIndex=2,null!=e.exec("str")}))},Qa=RegExp.prototype.exec,Za=String.prototype.replace,$a=Qa,es=(za=/a/,Ja=/b*/g,Qa.call(za,"a"),Qa.call(Ja,"a"),0!==za.lastIndex||0!==Ja.lastIndex),ts=Xa.UNSUPPORTED_Y||Xa.BROKEN_CARET,ns=void 0!==/()??/.exec("")[1];(es||ns||ts)&&($a=function(e){var t,n,o,r,a=this,s=ts&&a.sticky,i=Ya.call(a),u=a.source,c=0,l=e;return s&&(-1===(i=i.replace("y","")).indexOf("g")&&(i+="g"),l=String(e).slice(a.lastIndex),a.lastIndex>0&&(!a.multiline||a.multiline&&"\n"!==e[a.lastIndex-1])&&(u="(?: "+u+")",l=" "+l,c++),n=new RegExp("^(?:"+u+")",i)),ns&&(n=new RegExp("^"+u+"$(?!\\s)",i)),es&&(t=a.lastIndex),o=Qa.call(s?n:a,l),s?o?(o.input=o.input.slice(c),o[0]=o[0].slice(c),o.index=a.lastIndex,a.lastIndex+=o[0].length):a.lastIndex=0:es&&o&&(a.lastIndex=a.global?o.index+o[0].length:t),ns&&o&&o.length>1&&Za.call(o[0],n,(function(){for(r=1;r<arguments.length-2;r++)void 0===arguments[r]&&(o[r]=void 0)})),o});var os=$a;be({target:"RegExp",proto:!0,forced:/./.exec!==os},{exec:os});var rs=RegExp.prototype,as=rs.toString,ss=a((function(){return"/a/b"!=as.call({source:"a",flags:"b"})})),is="toString"!=as.name;(ss||is)&&oe(RegExp.prototype,"toString",(function(){var e=A(this),t=String(e.source),n=e.flags;return"/"+t+"/"+String(void 0===n&&e instanceof RegExp&&!("flags"in rs)?Ya.call(e):n)}),{unsafe:!0});var us=sn.codeAt;be({target:"String",proto:!0},{codePointAt:function(e){return us(this,e)}});var cs=Ke("match"),ls=function(e){var t;return v(e)&&(void 0!==(t=e[cs])?!!t:"RegExp"==g(e))},ds=function(e){if(ls(e))throw TypeError("The method doesn't accept regular expressions");return e},ps=Ke("match"),gs=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[ps]=!1,"/./"[e](t)}catch(r){}}return!1};be({target:"String",proto:!0,forced:!gs("includes")},{includes:function(e){return!!~String(_(this)).indexOf(ds(e),arguments.length>1?arguments[1]:void 0)}});var hs=Ke("species"),fs=!a((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),_s="$0"==="a".replace(/./,"$0"),ms=Ke("replace"),vs=!!/./[ms]&&""===/./[ms]("a","$0"),Ms=!a((function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var n="ab".split(e);return 2!==n.length||"a"!==n[0]||"b"!==n[1]})),ys=function(e,t,n,o){var r=Ke(e),s=!a((function(){var t={};return t[r]=function(){return 7},7!=""[e](t)})),i=s&&!a((function(){var t=!1,n=/a/;return"split"===e&&((n={}).constructor={},n.constructor[hs]=function(){return n},n.flags="",n[r]=/./[r]),n.exec=function(){return t=!0,null},n[r](""),!t}));if(!s||!i||"replace"===e&&(!fs||!_s||vs)||"split"===e&&!Ms){var u=/./[r],c=n(r,""[e],(function(e,t,n,o,r){return t.exec===os?s&&!r?{done:!0,value:u.call(t,n,o)}:{done:!0,value:e.call(n,t,o)}:{done:!1}}),{REPLACE_KEEPS_$0:_s,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:vs}),l=c[0],d=c[1];oe(String.prototype,e,l),oe(RegExp.prototype,r,2==t?function(e,t){return d.call(e,this,t)}:function(e){return d.call(e,this)})}o&&L(RegExp.prototype[r],"sham",!0)},Is=sn.charAt,Ts=function(e,t,n){return t+(n?Is(e,t).length:1)},Ss=function(e,t){var n=e.exec;if("function"==typeof n){var o=n.call(e,t);if("object"!=s(o))throw TypeError("RegExp exec method returned something other than an Object or null");return o}if("RegExp"!==g(e))throw TypeError("RegExp#exec called on incompatible receiver");return os.call(e,t)};ys("match",1,(function(e,t,n){return[function(t){var n=_(this),o=null==t?void 0:t[e];return void 0!==o?o.call(t,n):new RegExp(t)[e](String(n))},function(e){var o=n(t,e,this);if(o.done)return o.value;var r=A(e),a=String(this);if(!r.global)return Ss(r,a);var s=r.unicode;r.lastIndex=0;for(var i,u=[],c=0;null!==(i=Ss(r,a));){var l=String(i[0]);u[c]=l,""===l&&(r.lastIndex=Ts(a,de(r.lastIndex),s)),c++}return 0===c?null:u}]}));var Es=Math.max,Ds=Math.min,ks=Math.floor,Cs=/\$([$&'`]|\d\d?|<[^>]*>)/g,As=/\$([$&'`]|\d\d?)/g;ys("replace",2,(function(e,t,n,o){var r=o.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,a=o.REPLACE_KEEPS_$0,s=r?"$":"$0";return[function(n,o){var r=_(this),a=null==n?void 0:n[e];return void 0!==a?a.call(n,r,o):t.call(String(r),n,o)},function(e,o){if(!r&&a||"string"==typeof o&&-1===o.indexOf(s)){var u=n(t,e,this,o);if(u.done)return u.value}var c=A(e),l=String(this),d="function"==typeof o;d||(o=String(o));var p=c.global;if(p){var g=c.unicode;c.lastIndex=0}for(var h=[];;){var f=Ss(c,l);if(null===f)break;if(h.push(f),!p)break;""===String(f[0])&&(c.lastIndex=Ts(l,de(c.lastIndex),g))}for(var _,m="",v=0,M=0;M<h.length;M++){f=h[M];for(var y=String(f[0]),I=Es(Ds(ce(f.index),l.length),0),T=[],S=1;S<f.length;S++)T.push(void 0===(_=f[S])?_:String(_));var E=f.groups;if(d){var D=[y].concat(T,I,l);void 0!==E&&D.push(E);var k=String(o.apply(void 0,D))}else k=i(y,l,I,T,E,o);I>=v&&(m+=l.slice(v,I)+k,v=I+y.length)}return m+l.slice(v)}];function i(e,n,o,r,a,s){var i=o+e.length,u=r.length,c=As;return void 0!==a&&(a=Pe(a),c=Cs),t.call(s,c,(function(t,s){var c;switch(s.charAt(0)){case"$":return"$";case"&":return e;case"`":return n.slice(0,o);case"'":return n.slice(i);case"<":c=a[s.slice(1,-1)];break;default:var l=+s;if(0===l)return t;if(l>u){var d=ks(l/10);return 0===d?t:d<=u?void 0===r[d-1]?s.charAt(1):r[d-1]+s.charAt(1):t}c=r[l-1]}return void 0===c?"":c}))}}));var Ns=Ke("iterator"),Os=Ke("toStringTag"),Ls=Go.values;for(var Rs in On){var bs=r[Rs],ws=bs&&bs.prototype;if(ws){if(ws[Ns]!==Ls)try{L(ws,Ns,Ls)}catch(Xv){ws[Ns]=Ls}if(ws[Os]||L(ws,Os,Rs),On[Rs])for(var Ps in Go)if(ws[Ps]!==Go[Ps])try{L(ws,Ps,Go[Ps])}catch(Xv){ws[Ps]=Go[Ps]}}}var Gs=Qt.trim,Us=r.parseFloat,Fs=1/Us(Yt+"-0")!=-1/0?function(e){var t=Gs(String(e)),n=Us(t);return 0===n&&"-"==t.charAt(0)?-0:n}:Us;be({global:!0,forced:parseFloat!=Fs},{parseFloat:Fs});var qs={TEST:{CHINA:{DEFAULT:"wss://wss-dev.tim.qq.com"},OVERSEA:{DEFAULT:"wss://wss-dev.tim.qq.com"},SINGAPORE:{DEFAULT:"wss://wsssgp-dev.im.qcloud.com"},KOREA:{DEFAULT:"wss://wsskr-dev.im.qcloud.com"},GERMANY:{DEFAULT:"wss://wssger-dev.im.qcloud.com"}},PRODUCTION:{CHINA:{DEFAULT:"wss://wss.im.qcloud.com",BACKUP:"wss://wss.tim.qq.com"},OVERSEA:{DEFAULT:"wss://wss.im.qcloud.com"},SINGAPORE:{DEFAULT:"wss://wsssgp.im.qcloud.com"},KOREA:{DEFAULT:"wss://wsskr.im.qcloud.com"},GERMANY:{DEFAULT:"wss://wssger.im.qcloud.com"}}},xs={WEB:7,WX_MP:8,QQ_MP:9,TT_MP:10,BAIDU_MP:11,ALI_MP:12,UNI_NATIVE_APP:15},Vs="1.7.3",Ks=537048168,Bs="CHINA",Hs="OVERSEA",js="SINGAPORE",Ys="KOREA",Ws="GERMANY",zs={HOST:{CURRENT:{DEFAULT:"wss://wss.im.qcloud.com"},setCurrent:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Bs;this.CURRENT=qs.PRODUCTION[e]}},NAME:{OPEN_IM:"openim",GROUP:"group_open_http_svc",GROUP_ATTR:"group_open_attr_http_svc",FRIEND:"sns",PROFILE:"profile",RECENT_CONTACT:"recentcontact",PIC:"openpic",BIG_GROUP_NO_AUTH:"group_open_http_noauth_svc",BIG_GROUP_LONG_POLLING:"group_open_long_polling_http_svc",BIG_GROUP_LONG_POLLING_NO_AUTH:"group_open_long_polling_http_noauth_svc",IM_OPEN_STAT:"imopenstat",WEB_IM:"webim",IM_COS_SIGN:"im_cos_sign_svr",CUSTOM_UPLOAD:"im_cos_msg",HEARTBEAT:"heartbeat",IM_OPEN_PUSH:"im_open_push",IM_OPEN_STATUS:"im_open_status",IM_LONG_MESSAGE:"im_long_msg",CLOUD_CONTROL:"im_sdk_config_mgr"},CMD:{ACCESS_LAYER:"accesslayer",LOGIN:"wslogin",LOGOUT_LONG_POLL:"longpollinglogout",LOGOUT:"wslogout",HELLO:"wshello",PORTRAIT_GET:"portrait_get_all",PORTRAIT_SET:"portrait_set",GET_LONG_POLL_ID:"getlongpollingid",LONG_POLL:"longpolling",AVCHATROOM_LONG_POLL:"get_msg",ADD_FRIEND:"friend_add",UPDATE_FRIEND:"friend_update",GET_FRIEND_LIST:"friend_get",GET_FRIEND_PROFILE:"friend_get_list",DELETE_FRIEND:"friend_delete",CHECK_FRIEND:"friend_check",GET_FRIEND_GROUP_LIST:"group_get",RESPOND_FRIEND_APPLICATION:"friend_response",GET_FRIEND_APPLICATION_LIST:"pendency_get",DELETE_FRIEND_APPLICATION:"pendency_delete",REPORT_FRIEND_APPLICATION:"pendency_report",GET_GROUP_APPLICATION:"get_pendency",CREATE_FRIEND_GROUP:"group_add",DELETE_FRIEND_GROUP:"group_delete",UPDATE_FRIEND_GROUP:"group_update",GET_BLACKLIST:"black_list_get",ADD_BLACKLIST:"black_list_add",DELETE_BLACKLIST:"black_list_delete",CREATE_GROUP:"create_group",GET_JOINED_GROUPS:"get_joined_group_list",SET_GROUP_ATTRIBUTES:"set_group_attr",MODIFY_GROUP_ATTRIBUTES:"modify_group_attr",DELETE_GROUP_ATTRIBUTES:"delete_group_attr",CLEAR_GROUP_ATTRIBUTES:"clear_group_attr",GET_GROUP_ATTRIBUTES:"get_group_attr",SEND_MESSAGE:"sendmsg",REVOKE_C2C_MESSAGE:"msgwithdraw",DELETE_C2C_MESSAGE:"delete_c2c_msg_ramble",SEND_GROUP_MESSAGE:"send_group_msg",REVOKE_GROUP_MESSAGE:"group_msg_recall",DELETE_GROUP_MESSAGE:"delete_group_ramble_msg_by_seq",GET_GROUP_INFO:"get_group_info",GET_GROUP_MEMBER_INFO:"get_specified_group_member_info",GET_GROUP_MEMBER_LIST:"get_group_member_info",QUIT_GROUP:"quit_group",CHANGE_GROUP_OWNER:"change_group_owner",DESTROY_GROUP:"destroy_group",ADD_GROUP_MEMBER:"add_group_member",DELETE_GROUP_MEMBER:"delete_group_member",SEARCH_GROUP_BY_ID:"get_group_public_info",APPLY_JOIN_GROUP:"apply_join_group",HANDLE_APPLY_JOIN_GROUP:"handle_apply_join_group",HANDLE_GROUP_INVITATION:"handle_invite_join_group",MODIFY_GROUP_INFO:"modify_group_base_info",MODIFY_GROUP_MEMBER_INFO:"modify_group_member_info",DELETE_GROUP_SYSTEM_MESSAGE:"deletemsg",DELETE_GROUP_AT_TIPS:"deletemsg",GET_CONVERSATION_LIST:"get",PAGING_GET_CONVERSATION_LIST:"page_get",DELETE_CONVERSATION:"delete",PIN_CONVERSATION:"top",GET_MESSAGES:"getmsg",GET_C2C_ROAM_MESSAGES:"getroammsg",GET_GROUP_ROAM_MESSAGES:"group_msg_get",SET_C2C_MESSAGE_READ:"msgreaded",GET_PEER_READ_TIME:"get_peer_read_time",SET_GROUP_MESSAGE_READ:"msg_read_report",FILE_READ_AND_WRITE_AUTHKEY:"authkey",FILE_UPLOAD:"pic_up",COS_SIGN:"cos",COS_PRE_SIG:"pre_sig",TIM_WEB_REPORT_V2:"tim_web_report_v2",BIG_DATA_HALLWAY_AUTH_KEY:"authkey",GET_ONLINE_MEMBER_NUM:"get_online_member_num",ALIVE:"alive",MESSAGE_PUSH:"msg_push",MESSAGE_PUSH_ACK:"ws_msg_push_ack",STATUS_FORCEOFFLINE:"stat_forceoffline",DOWNLOAD_MERGER_MESSAGE:"get_relay_json_msg",UPLOAD_MERGER_MESSAGE:"save_relay_json_msg",FETCH_CLOUD_CONTROL_CONFIG:"fetch_config",PUSHED_CLOUD_CONTROL_CONFIG:"push_configv2"},CHANNEL:{SOCKET:1,XHR:2,AUTO:0},NAME_VERSION:{openim:"v4",group_open_http_svc:"v4",sns:"v4",profile:"v4",recentcontact:"v4",openpic:"v4",group_open_http_noauth_svc:"v4",group_open_long_polling_http_svc:"v4",group_open_long_polling_http_noauth_svc:"v4",imopenstat:"v4",im_cos_sign_svr:"v4",im_cos_msg:"v4",webim:"v4",im_open_push:"v4",im_open_status:"v4"}};zs.HOST.setCurrent(Bs);var Js,Xs,Qs,Zs="undefined"!=typeof wx&&"function"==typeof wx.getSystemInfoSync&&Boolean(wx.getSystemInfoSync().fontSizeSetting),$s="undefined"!=typeof qq&&"function"==typeof qq.getSystemInfoSync&&Boolean(qq.getSystemInfoSync().fontSizeSetting),ei="undefined"!=typeof tt&&"function"==typeof tt.getSystemInfoSync&&Boolean(tt.getSystemInfoSync().fontSizeSetting),ti="undefined"!=typeof swan&&"function"==typeof swan.getSystemInfoSync&&Boolean(swan.getSystemInfoSync().fontSizeSetting),ni="undefined"!=typeof my&&"function"==typeof my.getSystemInfoSync&&Boolean(my.getSystemInfoSync().fontSizeSetting),oi="undefined"!=typeof uni&&"undefined"==typeof window,ri=Zs||$s||ei||ti||ni||oi,ai=("undefined"!=typeof uni||"undefined"!=typeof window)&&!ri,si=$s?qq:ei?tt:ti?swan:ni?my:Zs?wx:oi?uni:{},ii=(Js="WEB",Mi?Js="WEB":$s?Js="QQ_MP":ei?Js="TT_MP":ti?Js="BAIDU_MP":ni?Js="ALI_MP":Zs?Js="WX_MP":oi&&(Js="UNI_NATIVE_APP"),xs[Js]),ui=ai&&window&&window.navigator&&window.navigator.userAgent||"",ci=/AppleWebKit\/([\d.]+)/i.exec(ui),li=(ci&&parseFloat(ci.pop()),/iPad/i.test(ui)),di=/iPhone/i.test(ui)&&!li,pi=/iPod/i.test(ui),gi=di||li||pi,hi=(function(){var e=ui.match(/OS (\d+)_/i);e&&e[1]&&e[1]}(),/Android/i.test(ui)),fi=(function(){var e=ui.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;var t=e[1]&&parseFloat(e[1]),n=e[2]&&parseFloat(e[2]);t&&n&&parseFloat(e[1]+"."+e[2])}(),hi&&/webkit/i.test(ui),/Firefox/i.test(ui),/Edge/i.test(ui)),_i=(!fi&&/Chrome/i.test(ui),function(){var e=ui.match(/Chrome\/(\d+)/);e&&e[1]&&parseFloat(e[1])}(),/MSIE/.test(ui)),mi=(/MSIE\s8\.0/.test(ui),function(){var e=/MSIE\s(\d+)\.\d/.exec(ui),t=e&&parseFloat(e[1]);return!t&&/Trident\/7.0/i.test(ui)&&/rv:11.0/.test(ui)&&(t=11),t}()),vi=(/Safari/i.test(ui),/TBS\/\d+/i.test(ui)),Mi=(function(){var e=ui.match(/TBS\/(\d+)/i);e&&e[1]&&e[1]}(),!vi&&/MQQBrowser\/\d+/i.test(ui),!vi&&/ QQBrowser\/\d+/i.test(ui),/(micromessenger|webbrowser)/i.test(ui)),yi=/Windows/i.test(ui),Ii=/MAC OS X/i.test(ui),Ti=(/MicroMessenger/i.test(ui),ai&&"undefined"!=typeof Worker),Si=!a((function(){return Object.isExtensible(Object.preventExtensions({}))})),Ei=t((function(e){var t=O.f,n=H("meta"),o=0,r=Object.isExtensible||function(){return!0},a=function(e){t(e,n,{value:{objectID:"O"+ ++o,weakData:{}}})},i=e.exports={REQUIRED:!1,fastKey:function(e,t){if(!v(e))return"symbol"==s(e)?e:("string"==typeof e?"S":"P")+e;if(!I(e,n)){if(!r(e))return"F";if(!t)return"E";a(e)}return e[n].objectID},getWeakData:function(e,t){if(!I(e,n)){if(!r(e))return!0;if(!t)return!1;a(e)}return e[n].weakData},onFreeze:function(e){return Si&&i.REQUIRED&&r(e)&&!I(e,n)&&a(e),e}};W[n]=!0})),Di=(Ei.REQUIRED,Ei.fastKey,Ei.getWeakData,Ei.onFreeze,O.f),ki=Ei.fastKey,Ci=ne.set,Ai=ne.getterFor,Ni=(function(e,t,n){var o=-1!==e.indexOf("Map"),s=-1!==e.indexOf("Weak"),i=o?"set":"add",u=r[e],c=u&&u.prototype,l=u,d={},p=function(e){var t=c[e];oe(c,e,"add"==e?function(e){return t.call(this,0===e?0:e),this}:"delete"==e?function(e){return!(s&&!v(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return s&&!v(e)?void 0:t.call(this,0===e?0:e)}:"has"==e?function(e){return!(s&&!v(e))&&t.call(this,0===e?0:e)}:function(e,n){return t.call(this,0===e?0:e,n),this})};if(Le(e,"function"!=typeof u||!(s||c.forEach&&!a((function(){(new u).entries().next()})))))l=n.getConstructor(t,e,o,i),Ei.REQUIRED=!0;else if(Le(e,!0)){var g=new l,h=g[i](s?{}:-0,1)!=g,f=a((function(){g.has(1)})),_=wt((function(e){new u(e)})),m=!s&&a((function(){for(var e=new u,t=5;t--;)e[i](t,t);return!e.has(-0)}));_||((l=t((function(t,n){Lr(t,l,e);var r=or(new u,t,l);return null!=n&&Rr(n,r[i],r,o),r}))).prototype=c,c.constructor=l),(f||m)&&(p("delete"),p("has"),o&&p("get")),(m||h)&&p(i),s&&c.clear&&delete c.clear}d[e]=l,be({global:!0,forced:l!=u},d),mn(l,e),s||n.setStrong(l,e,o)}("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),{getConstructor:function(e,t,n,o){var r=e((function(e,a){Lr(e,r,t),Ci(e,{type:t,index:Ht(null),first:void 0,last:void 0,size:0}),i||(e.size=0),null!=a&&Rr(a,e[o],e,n)})),a=Ai(t),s=function(e,t,n){var o,r,s=a(e),c=u(e,t);return c?c.value=n:(s.last=c={index:r=ki(t,!0),key:t,value:n,previous:o=s.last,next:void 0,removed:!1},s.first||(s.first=c),o&&(o.next=c),i?s.size++:e.size++,"F"!==r&&(s.index[r]=c)),e},u=function(e,t){var n,o=a(e),r=ki(t);if("F"!==r)return o.index[r];for(n=o.first;n;n=n.next)if(n.key==t)return n};return Ar(r.prototype,{clear:function(){for(var e=a(this),t=e.index,n=e.first;n;)n.removed=!0,n.previous&&(n.previous=n.previous.next=void 0),delete t[n.index],n=n.next;e.first=e.last=void 0,i?e.size=0:this.size=0},delete:function(e){var t=a(this),n=u(this,e);if(n){var o=n.next,r=n.previous;delete t.index[n.index],n.removed=!0,r&&(r.next=o),o&&(o.previous=r),t.first==n&&(t.first=o),t.last==n&&(t.last=r),i?t.size--:this.size--}return!!n},forEach:function(e){for(var t,n=a(this),o=rt(e,arguments.length>1?arguments[1]:void 0,3);t=t?t.next:n.first;)for(o(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!u(this,e)}}),Ar(r.prototype,n?{get:function(e){var t=u(this,e);return t&&t.value},set:function(e,t){return s(this,0===e?0:e,t)}}:{add:function(e){return s(this,e=0===e?0:e,e)}}),i&&Di(r.prototype,"size",{get:function(){return a(this).size}}),r},setStrong:function(e,t,n){var o=t+" Iterator",r=Ai(t),a=Ai(o);kn(e,t,(function(e,t){Ci(this,{type:o,target:e,state:r(e),kind:t,last:void 0})}),(function(){for(var e=a(this),t=e.kind,n=e.last;n&&n.removed;)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?"keys"==t?{value:n.key,done:!1}:"values"==t?{value:n.value,done:!1}:{value:[n.key,n.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),n?"entries":"values",!n,!0),Or(t)}}),"undefined"!=typeof o?o:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});Xs="undefined"!=typeof console?console:void 0!==Ni&&Ni.console?Ni.console:"undefined"!=typeof window&&window.console?window.console:{};for(var Oi=function(){},Li=["assert","clear","count","debug","dir","dirxml","error","exception","group","groupCollapsed","groupEnd","info","log","markTimeline","profile","profileEnd","table","time","timeEnd","timeStamp","trace","warn"],Ri=Li.length;Ri--;)Qs=Li[Ri],function(){}||(Xs[Qs]=Oi);Xs.methods=Li;var bi=Xs,wi=0,Pi=function(){return(new Date).getTime()+wi},Gi=function(){wi=0},Ui=0,Fi=new Map;function qi(){var e,t=((e=new Date).setTime(Pi()),e);return"TIM "+t.toLocaleTimeString("en-US",{hour12:!1})+"."+function(e){var t;switch(e.toString().length){case 1:t="00"+e;break;case 2:t="0"+e;break;default:t=e}return t}(t.getMilliseconds())+":"}var xi={arguments2String:function(e){var t;if(1===e.length)t=qi()+e[0];else{t=qi();for(var n=0,o=e.length;n<o;n++)zi(e[n])?Xi(e[n])?t+=nu(e[n]):t+=JSON.stringify(e[n]):t+=e[n],t+=" "}return t},debug:function(){if(Ui<=-1){var e=this.arguments2String(arguments);bi.debug(e)}},log:function(){if(Ui<=0){var e=this.arguments2String(arguments);bi.log(e)}},info:function(){if(Ui<=1){var e=this.arguments2String(arguments);bi.info(e)}},warn:function(){if(Ui<=2){var e=this.arguments2String(arguments);bi.warn(e)}},error:function(){if(Ui<=3){var e=this.arguments2String(arguments);bi.error(e)}},time:function(e){Fi.set(e,eu.now())},timeEnd:function(e){if(Fi.has(e)){var t=eu.now()-Fi.get(e);return Fi.delete(e),t}return bi.warn("未找到对应label: ".concat(e,", 请在调用 logger.timeEnd 前，调用 logger.time")),0},setLevel:function(e){e<4&&bi.log(qi()+"set level from "+Ui+" to "+e),Ui=e},getLevel:function(){return Ui}},Vi=function(e){return"file"===Qi(e)},Ki=function(e){return null!==e&&("number"==typeof e&&!isNaN(e-0)||"object"===wn(e)&&e.constructor===Number)},Bi=function(e){return"string"==typeof e},Hi=function(e){return null!==e&&"object"===wn(e)},ji=function(e){if("object"!==wn(e)||null===e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return t===n},Yi=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"array"===Qi(e)},Wi=function(e){return void 0===e},zi=function(e){return Yi(e)||Hi(e)},Ji=function(e){return"function"==typeof e},Xi=function(e){return e instanceof Error},Qi=function(e){return Object.prototype.toString.call(e).match(/^\[object (.*)\]$/)[1].toLowerCase()},Zi=function(e){if("string"!=typeof e)return!1;var t=e[0];return!/[^a-zA-Z0-9]/.test(t)},$i=0;Date.now||(Date.now=function(){return(new Date).getTime()});var eu={now:function(){0===$i&&($i=Date.now()-1);var e=Date.now()-$i;return e>4294967295?($i+=4294967295,Date.now()-$i):e},utc:function(){return Math.round(Date.now()/1e3)}},tu=function e(t,n,o,r){if(!zi(t)||!zi(n))return 0;for(var a,s=0,i=Object.keys(n),u=0,c=i.length;u<c;u++)if(a=i[u],!(Wi(n[a])||o&&o.includes(a)))if(zi(t[a])&&zi(n[a]))s+=e(t[a],n[a],o,r);else{if(r&&r.includes(n[a]))continue;t[a]!==n[a]&&(t[a]=n[a],s+=1)}return s},nu=function(e){return JSON.stringify(e,["message","code"])},ou=function(e){if(0===e.length)return 0;for(var t=0,n=0,o="undefined"!=typeof document&&void 0!==document.characterSet?document.characterSet:"UTF-8";void 0!==e[t];)n+=e[t++].charCodeAt[t]<=255?1:!1===o?3:2;return n},ru=function(e){var t=e||99999999;return Math.round(Math.random()*t)},au="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",su=au.length,iu=function(e,t){for(var n in e)if(e[n]===t)return!0;return!1},uu={},cu=function(){if(ri)return"https:";if(ai&&"undefined"==typeof window)return"https:";var e=window.location.protocol;return["http:","https:"].indexOf(e)<0&&(e="http:"),e},lu=function(e){return-1===e.indexOf("http://")||-1===e.indexOf("https://")?"https://"+e:e.replace(/https|http/,"https")},du=function e(t){if(0===Object.getOwnPropertyNames(t).length)return Object.create(null);var n=Array.isArray(t)?[]:Object.create(null),o="";for(var r in t)null!==t[r]?void 0!==t[r]?(o=wn(t[r]),["string","number","function","boolean"].indexOf(o)>=0?n[r]=t[r]:n[r]=e(t[r])):n[r]=void 0:n[r]=null;return n};function pu(e,t){Yi(e)&&Yi(t)?t.forEach((function(t){var n=t.key,o=t.value,r=e.find((function(e){return e.key===n}));r?r.value=o:e.push({key:n,value:o})})):xi.warn("updateCustomField target 或 source 不是数组，忽略此次更新。")}var gu=function(e){return e===so.GRP_PUBLIC},hu=function(e){return e===so.GRP_AVCHATROOM},fu=function(e){return Bi(e)&&e.slice(0,3)===so.CONV_C2C},_u=function(e){return Bi(e)&&e.slice(0,5)===so.CONV_GROUP},mu=function(e){return Bi(e)&&e===so.CONV_SYSTEM};function vu(e,t){var n={};return Object.keys(e).forEach((function(o){n[o]=t(e[o],o)})),n}function Mu(){function e(){return(65536*(1+Math.random())|0).toString(16).substring(1)}return"".concat(e()+e()).concat(e()).concat(e()).concat(e()).concat(e()).concat(e()).concat(e())}function yu(){var e="unknown";if(Ii&&(e="mac"),yi&&(e="windows"),gi&&(e="ios"),hi&&(e="android"),ri)try{var t=si.getSystemInfoSync().platform;void 0!==t&&(e=t)}catch(Xv){}return e}function Iu(e){var t=e.originUrl,n=void 0===t?void 0:t,o=e.originWidth,r=e.originHeight,a=e.min,s=void 0===a?198:a,i=parseInt(o),u=parseInt(r),c={url:void 0,width:0,height:0};return(i<=u?i:u)<=s?(c.url=n,c.width=i,c.height=u):(u<=i?(c.width=Math.ceil(i*s/u),c.height=s):(c.width=s,c.height=Math.ceil(u*s/i)),c.url="".concat(n,198===s?"?imageView2/3/w/198/h/198":"?imageView2/3/w/720/h/720")),Wi(n)?Wn(c,["url"]):c}function Tu(e){var t=e[2];e[2]=e[1],e[1]=t;for(var n=0;n<e.length;n++)e[n].setType(n)}function Su(e){var t=e.servcmd;return t.slice(t.indexOf(".")+1)}function Eu(e,t){return Math.round(Number(e)*Math.pow(10,t))/Math.pow(10,t)}function Du(e,t){return e.includes(t)}function ku(e,t){return e.includes(t)}var Cu=Object.prototype.hasOwnProperty;function Au(e){if(null==e)return!0;if("boolean"==typeof e)return!1;if("number"==typeof e)return 0===e;if("string"==typeof e)return 0===e.length;if("function"==typeof e)return 0===e.length;if(Array.isArray(e))return 0===e.length;if(e instanceof Error)return""===e.message;if(ji(e)){for(var t in e)if(Cu.call(e,t))return!1;return!0}return!("map"!==Qi(e)&&!function(e){return"set"===Qi(e)}(e)&&!Vi(e))&&0===e.size}function Nu(e,t,n){if(void 0===t)return!0;var o=!0;if("object"===yo(t).toLowerCase())Object.keys(t).forEach((function(r){var a=1===e.length?e[0][r]:void 0;o=!!Ou(a,t[r],n,r)&&o}));else if("array"===yo(t).toLowerCase())for(var r=0;r<t.length;r++)o=!!Ou(e[r],t[r],n,t[r].name)&&o;if(o)return o;throw new Error("Params validate failed.")}function Ou(e,t,n,o){if(void 0===t)return!0;var r=!0;return t.required&&Au(e)&&(bi.error("TIM [".concat(n,'] Missing required params: "').concat(o,'".')),r=!1),Au(e)||yo(e).toLowerCase()===t.type.toLowerCase()||(bi.error("TIM [".concat(n,'] Invalid params: type check failed for "').concat(o,'".Expected ').concat(t.type,".")),r=!1),t.validator&&!t.validator(e)&&(bi.error("TIM [".concat(n,"] Invalid params: custom validator check failed for params.")),r=!1),r}var Lu={f:Ke},Ru=O.f,bu=it.forEach,wu=Y("hidden"),Pu=Ke("toPrimitive"),Gu=ne.set,Uu=ne.getterFor("Symbol"),Fu=Object.prototype,qu=r.Symbol,xu=se("JSON","stringify"),Vu=C.f,Ku=O.f,Bu=Mr.f,Hu=l.f,ju=V("symbols"),Yu=V("op-symbols"),Wu=V("string-to-symbol-registry"),zu=V("symbol-to-string-registry"),Ju=V("wks"),Xu=r.QObject,Qu=!Xu||!Xu.prototype||!Xu.prototype.findChild,Zu=i&&a((function(){return 7!=Ht(Ku({},"a",{get:function(){return Ku(this,"a",{value:7}).a}})).a}))?function(e,t,n){var o=Vu(Fu,t);o&&delete Fu[t],Ku(e,t,n),o&&e!==Fu&&Ku(Fu,t,o)}:Ku,$u=function(e,t){var n=ju[e]=Ht(qu.prototype);return Gu(n,{type:"Symbol",tag:e,description:t}),i||(n.description=t),n},ec=Fe?function(e){return"symbol"==s(e)}:function(e){return Object(e)instanceof qu},tc=function e(t,n,o){t===Fu&&e(Yu,n,o),A(t);var r=M(n,!0);return A(o),I(ju,r)?(o.enumerable?(I(t,wu)&&t[wu][r]&&(t[wu][r]=!1),o=Ht(o,{enumerable:d(0,!1)})):(I(t,wu)||Ku(t,wu,d(1,{})),t[wu][r]=!0),Zu(t,r,o)):Ku(t,r,o)},nc=function(e,t){A(e);var n=m(t),o=Ut(n).concat(sc(n));return bu(o,(function(t){i&&!oc.call(n,t)||tc(e,t,n[t])})),e},oc=function(e){var t=M(e,!0),n=Hu.call(this,t);return!(this===Fu&&I(ju,t)&&!I(Yu,t))&&(!(n||!I(this,t)||!I(ju,t)||I(this,wu)&&this[wu][t])||n)},rc=function(e,t){var n=m(e),o=M(t,!0);if(n!==Fu||!I(ju,o)||I(Yu,o)){var r=Vu(n,o);return!r||!I(ju,o)||I(n,wu)&&n[wu][o]||(r.enumerable=!0),r}},ac=function(e){var t=Bu(m(e)),n=[];return bu(t,(function(e){I(ju,e)||I(W,e)||n.push(e)})),n},sc=function(e){var t=e===Fu,n=Bu(t?Yu:m(e)),o=[];return bu(n,(function(e){!I(ju,e)||t&&!I(Fu,e)||o.push(ju[e])})),o};if(Ue||(oe((qu=function(){if(this instanceof qu)throw TypeError("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?String(arguments[0]):void 0,t=H(e),n=function e(n){this===Fu&&e.call(Yu,n),I(this,wu)&&I(this[wu],t)&&(this[wu][t]=!1),Zu(this,t,d(1,n))};return i&&Qu&&Zu(Fu,t,{configurable:!0,set:n}),$u(t,e)}).prototype,"toString",(function(){return Uu(this).tag})),oe(qu,"withoutSetter",(function(e){return $u(H(e),e)})),l.f=oc,O.f=tc,C.f=rc,Ie.f=Mr.f=ac,Te.f=sc,Lu.f=function(e){return $u(Ke(e),e)},i&&(Ku(qu.prototype,"description",{configurable:!0,get:function(){return Uu(this).description}}),oe(Fu,"propertyIsEnumerable",oc,{unsafe:!0}))),be({global:!0,wrap:!0,forced:!Ue,sham:!Ue},{Symbol:qu}),bu(Ut(Ju),(function(e){!function(e){var t=re.Symbol||(re.Symbol={});I(t,e)||Ru(t,e,{value:Lu.f(e)})}(e)})),be({target:"Symbol",stat:!0,forced:!Ue},{for:function(e){var t=String(e);if(I(Wu,t))return Wu[t];var n=qu(t);return Wu[t]=n,zu[n]=t,n},keyFor:function(e){if(!ec(e))throw TypeError(e+" is not a symbol");if(I(zu,e))return zu[e]},useSetter:function(){Qu=!0},useSimple:function(){Qu=!1}}),be({target:"Object",stat:!0,forced:!Ue,sham:!i},{create:function(e,t){return void 0===t?Ht(e):nc(Ht(e),t)},defineProperty:tc,defineProperties:nc,getOwnPropertyDescriptor:rc}),be({target:"Object",stat:!0,forced:!Ue},{getOwnPropertyNames:ac,getOwnPropertySymbols:sc}),be({target:"Object",stat:!0,forced:a((function(){Te.f(1)}))},{getOwnPropertySymbols:function(e){return Te.f(Pe(e))}}),xu){var ic=!Ue||a((function(){var e=qu();return"[null]"!=xu([e])||"{}"!=xu({a:e})||"{}"!=xu(Object(e))}));be({target:"JSON",stat:!0,forced:ic},{stringify:function(e,t,n){for(var o,r=[e],a=1;arguments.length>a;)r.push(arguments[a++]);if(o=t,(v(t)||void 0!==e)&&!ec(e))return we(t)||(t=function(e,t){if("function"==typeof o&&(t=o.call(this,e,t)),!ec(t))return t}),r[1]=t,xu.apply(null,r)}})}qu.prototype[Pu]||L(qu.prototype,Pu,qu.prototype.valueOf),mn(qu,"Symbol"),W[wu]=!0;var uc=O.f,cc=r.Symbol;if(i&&"function"==typeof cc&&(!("description"in cc.prototype)||void 0!==cc().description)){var lc={},dc=function e(){var t=arguments.length<1||void 0===arguments[0]?void 0:String(arguments[0]),n=this instanceof e?new cc(t):void 0===t?cc():cc(t);return""===t&&(lc[n]=!0),n};Ee(dc,cc);var pc=dc.prototype=cc.prototype;pc.constructor=dc;var gc=pc.toString,hc="Symbol(test)"==String(cc("test")),fc=/^Symbol\((.*)\)[^)]+$/;uc(pc,"description",{configurable:!0,get:function(){var e=v(this)?this.valueOf():this,t=gc.call(e);if(I(lc,e))return"";var n=hc?t.slice(7,-1):t.replace(fc,"$1");return""===n?void 0:n}}),be({global:!0,forced:!0},{Symbol:dc})}var _c,mc=C.f,vc="".startsWith,Mc=Math.min,yc=gs("startsWith"),Ic=!(yc||(_c=mc(String.prototype,"startsWith"),!_c||_c.writable));be({target:"String",proto:!0,forced:!Ic&&!yc},{startsWith:function(e){var t=String(_(this));ds(e);var n=de(Mc(arguments.length>1?arguments[1]:void 0,t.length)),o=String(e);return vc?vc.call(t,o,n):t.slice(n,n+o.length)===o}});var Tc={UNSEND:"unSend",SUCCESS:"success",FAIL:"fail"},Sc={NOT_START:"notStart",PENDING:"pengding",RESOLVED:"resolved",REJECTED:"rejected"},Ec=function(e){return!!e&&(!!(fu(e)||_u(e)||mu(e))||!1)},Dc={type:"String",required:!0},kc={type:"Array",required:!0},Cc={type:"Object",required:!0},Ac={login:{userID:Dc,userSig:Dc},addToBlacklist:{userIDList:kc},on:[{name:"eventName",type:"String",validator:function(e){return"string"==typeof e&&0!==e.length||!1}},{name:"handler",type:"Function",validator:function(e){return"function"==typeof e&&(e.name,!0)}}],once:[{name:"eventName",type:"String",validator:function(e){return"string"==typeof e&&0!==e.length||!1}},{name:"handler",type:"Function",validator:function(e){return"function"==typeof e&&(e.name,!0)}}],off:[{name:"eventName",type:"String",validator:function(e){return"string"==typeof e&&0!==e.length||!1}},{name:"handler",type:"Function",validator:function(e){return"function"==typeof e&&(e.name,!0)}}],sendMessage:[xn({name:"message"},Cc)],getMessageList:{conversationID:xn({},Dc,{validator:function(e){return Ec(e)}}),nextReqMessageID:{type:"String"},count:{type:"Number",validator:function(e){return!(!Wi(e)&&!/^[1-9][0-9]*$/.test(e))||!1}}},setMessageRead:{conversationID:xn({},Dc,{validator:function(e){return Ec(e)}})},getConversationProfile:[xn({name:"conversationID"},Dc,{validator:function(e){return Ec(e)}})],deleteConversation:[xn({name:"conversationID"},Dc,{validator:function(e){return Ec(e)}})],pinConversation:{conversationID:xn({},Dc,{validator:function(e){return Ec(e)}}),isPinned:xn({},{type:"Boolean",required:!0})},getConversationList:[{name:"options",type:"Array",validator:function(e){return!!Wi(e)||0!==e.length||!1}}],getGroupList:{groupProfileFilter:{type:"Array"}},getGroupProfile:{groupID:Dc,groupCustomFieldFilter:{type:"Array"},memberCustomFieldFilter:{type:"Array"}},getGroupProfileAdvance:{groupIDList:kc},createGroup:{name:Dc},joinGroup:{groupID:Dc,type:{type:"String"},applyMessage:{type:"String"}},quitGroup:[xn({name:"groupID"},Dc)],handleApplication:{message:Cc,handleAction:Dc,handleMessage:{type:"String"}},changeGroupOwner:{groupID:Dc,newOwnerID:Dc},updateGroupProfile:{groupID:Dc,muteAllMembers:{type:"Boolean"}},dismissGroup:[xn({name:"groupID"},Dc)],searchGroupByID:[xn({name:"groupID"},Dc)],initGroupAttributes:{groupID:Dc,groupAttributes:xn({},Cc,{validator:function(e){var t=!0;return Object.keys(e).forEach((function(n){if(!Bi(e[n]))return t=!1})),t}})},setGroupAttributes:{groupID:Dc,groupAttributes:xn({},Cc,{validator:function(e){var t=!0;return Object.keys(e).forEach((function(n){if(!Bi(e[n]))return t=!1})),t}})},deleteGroupAttributes:{groupID:Dc,keyList:{type:"Array",validator:function(e){if(Wi(e))return!1;if(!Yi(e))return!1;if(!Au(e)){var t=!0;return e.forEach((function(e){if(!Bi(e))return t=!1})),t}return!0}}},getGroupAttributes:{groupID:Dc,keyList:{type:"Array",validator:function(e){if(Wi(e))return!1;if(!Yi(e))return!1;if(!Au(e)){var t=!0;return e.forEach((function(e){if(!Bi(e))return t=!1})),t}return!0}}},getGroupMemberList:{groupID:Dc,offset:{type:"Number"},count:{type:"Number"}},getGroupMemberProfile:{groupID:Dc,userIDList:kc,memberCustomFieldFilter:{type:"Array"}},addGroupMember:{groupID:Dc,userIDList:kc},setGroupMemberRole:{groupID:Dc,userID:Dc,role:Dc},setGroupMemberMuteTime:{groupID:Dc,userID:Dc,muteTime:{type:"Number",validator:function(e){return e>=0}}},setGroupMemberNameCard:{groupID:Dc,userID:{type:"String"},nameCard:{type:"String",validator:function(e){return!!Bi(e)&&(e.length,!0)}}},setMessageRemindType:{groupID:Dc,messageRemindType:Dc},setGroupMemberCustomField:{groupID:Dc,userID:{type:"String"},memberCustomField:kc},deleteGroupMember:{groupID:Dc},createTextMessage:{to:Dc,conversationType:Dc,payload:xn({},Cc,{validator:function(e){return ji(e)&&Bi(e.text)&&0!==e.text.length||!1}})},createTextAtMessage:{to:Dc,conversationType:Dc,payload:xn({},Cc,{validator:function(e){return!!ji(e)&&(!!Bi(e.text)&&(0!==e.text.length&&(!(e.atUserList&&!Yi(e.atUserList))||!1)))}})},createCustomMessage:{to:Dc,conversationType:Dc,payload:xn({},Cc,{validator:function(e){return!!ji(e)&&(!(e.data&&!Bi(e.data))&&(!(e.description&&!Bi(e.description))&&(!(e.extension&&!Bi(e.extension))||!1)))}})},createImageMessage:{to:Dc,conversationType:Dc,payload:xn({},Cc,{validator:function(e){if(!ji(e))return!1;if(Wi(e.file))return!1;if(ai){if(!(e.file instanceof HTMLInputElement||Vi(e.file)))return ji(e.file)&&"undefined"!=typeof uni&&0!==e.file.tempFilePaths.length&&0!==e.file.tempFiles.length||!1;if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return!1}return!0},onProgress:{type:"Function",required:!1,validator:function(e){return Wi(e),!0}}})},createAudioMessage:{to:Dc,conversationType:Dc,payload:xn({},Cc,{validator:function(e){return!!ji(e)||!1}}),onProgress:{type:"Function",required:!1,validator:function(e){return Wi(e),!0}}},createVideoMessage:{to:Dc,conversationType:Dc,payload:xn({},Cc,{validator:function(e){if(!ji(e))return!1;if(Wi(e.file))return!1;if(ai){if(!(e.file instanceof HTMLInputElement||Vi(e.file)))return ji(e.file)&&"undefined"!=typeof uni&&!!Vi(e.file.tempFile)||!1;if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return!1}return!0}}),onProgress:{type:"Function",required:!1,validator:function(e){return Wi(e),!0}}},createFaceMessage:{to:Dc,conversationType:Dc,payload:xn({},Cc,{validator:function(e){return ji(e)&&Ki(e.index)&&!!Bi(e.data)||!1}})},createFileMessage:{to:Dc,conversationType:Dc,payload:xn({},Cc,{validator:function(e){if(!ji(e))return!1;if(Wi(e.file))return!1;if(ai){if(!(e.file instanceof HTMLInputElement||Vi(e.file)))return ji(e.file)&&"undefined"!=typeof uni&&0!==e.file.tempFilePaths.length&&0!==e.file.tempFiles.length||!1;if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return!1}return!0}}),onProgress:{type:"Function",required:!1,validator:function(e){return Wi(e),!0}}},createLocationMessage:{to:Dc,conversationType:Dc,payload:xn({},Cc,{validator:function(e){return ji(e)&&Bi(e.description)&&Ki(e.longitude)&&!!Ki(e.latitude)||!1}})},createMergerMessage:{to:Dc,conversationType:Dc,payload:xn({},Cc,{validator:function(e){if(Au(e.messageList))return!1;if(Au(e.compatibleText))return!1;var t=!1;return e.messageList.forEach((function(e){e.status===Tc.FAIL&&(t=!0)})),!t||!1}})},revokeMessage:[xn({name:"message"},Cc,{validator:function(e){return!Au(e)&&(e.conversationType!==so.CONV_SYSTEM&&(!0!==e.isRevoked||!1))}})],deleteMessage:[xn({name:"messageList"},kc,{validator:function(e){return!Au(e)||!1}})],getUserProfile:{userIDList:{type:"Array",validator:function(e){return!!Yi(e)&&(e.length,!0)}}},updateMyProfile:{profileCustomField:{type:"Array",validator:function(e){return!!Wi(e)||!!Yi(e)||!1}}},addFriend:{to:Dc,source:{type:"String",required:!0,validator:function(e){return!!e&&(e.startsWith("AddSource_Type_")&&!(e.replace("AddSource_Type_","").length>8)||!1)}},remark:{type:"String",required:!1,validator:function(e){return!(Bi(e)&&e.length>96)||!1}}},deleteFriend:{userIDList:kc},checkFriend:{userIDList:kc},getFriendProfile:{userIDList:kc},updateFriend:{userID:Dc,remark:{type:"String",required:!1,validator:function(e){return!(Bi(e)&&e.length>96)||!1}},friendCustomField:{type:"Array",required:!1,validator:function(e){if(e){if(!Yi(e))return!1;var t=!0;return e.forEach((function(e){return Bi(e.key)&&-1!==e.key.indexOf("Tag_SNS_Custom")&&Bi(e.value)?e.value.length>8?t=!1:void 0:t=!1})),t}return!0}}},acceptFriendApplication:{userID:Dc},refuseFriendApplication:{userID:Dc},deleteFriendApplication:{userID:Dc},createFriendGroup:{name:Dc},deleteFriendGroup:{name:Dc},addToFriendGroup:{name:Dc,userIDList:kc},removeFromFriendGroup:{name:Dc,userIDList:kc},renameFriendGroup:{oldName:Dc,newName:Dc}},Nc={login:"login",logout:"logout",on:"on",once:"once",off:"off",setLogLevel:"setLogLevel",registerPlugin:"registerPlugin",destroy:"destroy",createTextMessage:"createTextMessage",createTextAtMessage:"createTextAtMessage",createImageMessage:"createImageMessage",createAudioMessage:"createAudioMessage",createVideoMessage:"createVideoMessage",createCustomMessage:"createCustomMessage",createFaceMessage:"createFaceMessage",createFileMessage:"createFileMessage",createLocationMessage:"createLocationMessage",createMergerMessage:"createMergerMessage",downloadMergerMessage:"downloadMergerMessage",createForwardMessage:"createForwardMessage",sendMessage:"sendMessage",resendMessage:"resendMessage",revokeMessage:"revokeMessage",deleteMessage:"deleteMessage",getMessageList:"getMessageList",setMessageRead:"setMessageRead",getConversationList:"getConversationList",getConversationProfile:"getConversationProfile",deleteConversation:"deleteConversation",pinConversation:"pinConversation",getGroupList:"getGroupList",getGroupProfile:"getGroupProfile",createGroup:"createGroup",joinGroup:"joinGroup",updateGroupProfile:"updateGroupProfile",quitGroup:"quitGroup",dismissGroup:"dismissGroup",changeGroupOwner:"changeGroupOwner",searchGroupByID:"searchGroupByID",setMessageRemindType:"setMessageRemindType",handleGroupApplication:"handleGroupApplication",initGroupAttributes:"initGroupAttributes",setGroupAttributes:"setGroupAttributes",deleteGroupAttributes:"deleteGroupAttributes",getGroupAttributes:"getGroupAttributes",getGroupMemberProfile:"getGroupMemberProfile",getGroupMemberList:"getGroupMemberList",addGroupMember:"addGroupMember",deleteGroupMember:"deleteGroupMember",setGroupMemberNameCard:"setGroupMemberNameCard",setGroupMemberMuteTime:"setGroupMemberMuteTime",setGroupMemberRole:"setGroupMemberRole",setGroupMemberCustomField:"setGroupMemberCustomField",getGroupOnlineMemberCount:"getGroupOnlineMemberCount",getMyProfile:"getMyProfile",getUserProfile:"getUserProfile",updateMyProfile:"updateMyProfile",getBlacklist:"getBlacklist",addToBlacklist:"addToBlacklist",removeFromBlacklist:"removeFromBlacklist",getFriendList:"getFriendList",addFriend:"addFriend",deleteFriend:"deleteFriend",checkFriend:"checkFriend",updateFriend:"updateFriend",getFriendProfile:"getFriendProfile",getFriendApplicationList:"getFriendApplicationList",refuseFriendApplication:"refuseFriendApplication",deleteFriendApplication:"deleteFriendApplication",acceptFriendApplication:"acceptFriendApplication",setFriendApplicationRead:"setFriendApplicationRead",getFriendGroupList:"getFriendGroupList",createFriendGroup:"createFriendGroup",renameFriendGroup:"renameFriendGroup",deleteFriendGroup:"deleteFriendGroup",addToFriendGroup:"addToFriendGroup",removeFromFriendGroup:"removeFromFriendGroup",callExperimentalAPI:"callExperimentalAPI"},Oc=!!Cr&&a((function(){Cr.prototype.finally.call({then:function(){}},(function(){}))}));be({target:"Promise",proto:!0,real:!0,forced:Oc},{finally:function(e){var t=wr(this,se("Promise")),n="function"==typeof e;return this.then(n?function(n){return va(t,e()).then((function(){return n}))}:e,n?function(n){return va(t,e()).then((function(){throw n}))}:e)}}),"function"!=typeof Cr||Cr.prototype.finally||oe(Cr.prototype,"finally",se("Promise").prototype.finally);var Lc=[].slice,Rc=/MSIE .\./.test(je),bc=function(e){return function(t,n){var o=arguments.length>2,r=o?Lc.call(arguments,2):void 0;return e(o?function(){("function"==typeof t?t:Function(t)).apply(this,r)}:t,n)}};be({global:!0,bind:!0,forced:Rc},{setTimeout:bc(r.setTimeout),setInterval:bc(r.setInterval)});var wc=it.filter,Pc=Qe("filter"),Gc=pt("filter");be({target:"Array",proto:!0,forced:!Pc||!Gc},{filter:function(e){return wc(this,e,arguments.length>1?arguments[1]:void 0)}});var Uc,Fc="sign",qc="message",xc="user",Vc="c2c",Kc="group",Bc="sns",Hc="groupMember",jc="conversation",Yc="context",Wc="storage",zc="eventStat",Jc="netMonitor",Xc="bigDataChannel",Qc="upload",Zc="plugin",$c="syncUnreadMessage",el="session",tl="channel",nl="message_loss_detection",ol="cloudControl",rl="worker",al="pullGroupMessage",sl="qualityStat",il=function(){function e(t){Pn(this,e),this._moduleManager=t,this._className=""}return Un(e,[{key:"isLoggedIn",value:function(){return this._moduleManager.getModule(Yc).isLoggedIn()}},{key:"isOversea",value:function(){return this._moduleManager.getModule(Yc).isOversea()}},{key:"getMyUserID",value:function(){return this._moduleManager.getModule(Yc).getUserID()}},{key:"getModule",value:function(e){return this._moduleManager.getModule(e)}},{key:"getPlatform",value:function(){return ii}},{key:"getNetworkType",value:function(){return this._moduleManager.getModule(Jc).getNetworkType()}},{key:"probeNetwork",value:function(){return this._moduleManager.getModule(Jc).probe()}},{key:"getCloudConfig",value:function(e){return this._moduleManager.getModule(ol).getCloudConfig(e)}},{key:"emitOuterEvent",value:function(e,t){this._moduleManager.getOuterEmitterInstance().emit(e,t)}},{key:"emitInnerEvent",value:function(e,t){this._moduleManager.getInnerEmitterInstance().emit(e,t)}},{key:"getInnerEmitterInstance",value:function(){return this._moduleManager.getInnerEmitterInstance()}},{key:"generateTjgID",value:function(e){return this._moduleManager.getModule(Yc).getTinyID()+"-"+e.random}},{key:"filterModifiedMessage",value:function(e){if(!Au(e)){var t=e.filter((function(e){return!0===e.isModified}));t.length>0&&this.emitOuterEvent(ao.MESSAGE_MODIFIED,t)}}},{key:"filterUnmodifiedMessage",value:function(e){return Au(e)?[]:e.filter((function(e){return!1===e.isModified}))}},{key:"request",value:function(e){return this._moduleManager.getModule(el).request(e)}}]),e}(),ul="wslogin",cl="wslogout",ll="wshello",dl="getmsg",pl="authkey",gl="sendmsg",hl="send_group_msg",fl="portrait_get_all",_l="portrait_set",ml="black_list_get",vl="black_list_add",Ml="black_list_delete",yl="msgwithdraw",Il="msgreaded",Tl="getroammsg",Sl="get_peer_read_time",El="delete_c2c_msg_ramble",Dl="page_get",kl="get",Cl="delete",Al="top",Nl="deletemsg",Ol="get_joined_group_list",Ll="get_group_info",Rl="create_group",bl="destroy_group",wl="modify_group_base_info",Pl="apply_join_group",Gl="apply_join_group_noauth",Ul="quit_group",Fl="get_group_public_info",ql="change_group_owner",xl="handle_apply_join_group",Vl="handle_invite_join_group",Kl="group_msg_recall",Bl="msg_read_report",Hl="group_msg_get",jl="get_pendency",Yl="deletemsg",Wl="get_msg",zl="get_msg_noauth",Jl="get_online_member_num",Xl="delete_group_ramble_msg_by_seq",Ql="set_group_attr",Zl="modify_group_attr",$l="delete_group_attr",ed="clear_group_attr",td="get_group_attr",nd="get_group_member_info",od="get_specified_group_member_info",rd="add_group_member",ad="delete_group_member",sd="modify_group_member_info",id="cos",ud="pre_sig",cd="tim_web_report_v2",ld="alive",dd="msg_push",pd="ws_msg_push_ack",gd="stat_forceoffline",hd="save_relay_json_msg",fd="get_relay_json_msg",_d="fetch_config",md="push_configv2",vd={NO_SDKAPPID:2e3,NO_ACCOUNT_TYPE:2001,NO_IDENTIFIER:2002,NO_USERSIG:2003,NO_TINYID:2022,NO_A2KEY:2023,USER_NOT_LOGGED_IN:2024,REPEAT_LOGIN:2025,COS_UNDETECTED:2040,COS_GET_SIG_FAIL:2041,MESSAGE_SEND_FAIL:2100,MESSAGE_LIST_CONSTRUCTOR_NEED_OPTIONS:2103,MESSAGE_SEND_NEED_MESSAGE_INSTANCE:2105,MESSAGE_SEND_INVALID_CONVERSATION_TYPE:2106,MESSAGE_FILE_IS_EMPTY:2108,MESSAGE_ONPROGRESS_FUNCTION_ERROR:2109,MESSAGE_REVOKE_FAIL:2110,MESSAGE_DELETE_FAIL:2111,MESSAGE_IMAGE_SELECT_FILE_FIRST:2251,MESSAGE_IMAGE_TYPES_LIMIT:2252,MESSAGE_IMAGE_SIZE_LIMIT:2253,MESSAGE_AUDIO_UPLOAD_FAIL:2300,MESSAGE_AUDIO_SIZE_LIMIT:2301,MESSAGE_VIDEO_UPLOAD_FAIL:2350,MESSAGE_VIDEO_SIZE_LIMIT:2351,MESSAGE_VIDEO_TYPES_LIMIT:2352,MESSAGE_FILE_UPLOAD_FAIL:2400,MESSAGE_FILE_SELECT_FILE_FIRST:2401,MESSAGE_FILE_SIZE_LIMIT:2402,MESSAGE_FILE_URL_IS_EMPTY:2403,MESSAGE_MERGER_TYPE_INVALID:2450,MESSAGE_MERGER_KEY_INVALID:2451,MESSAGE_MERGER_DOWNLOAD_FAIL:2452,MESSAGE_FORWARD_TYPE_INVALID:2453,CONVERSATION_NOT_FOUND:2500,USER_OR_GROUP_NOT_FOUND:2501,CONVERSATION_UN_RECORDED_TYPE:2502,ILLEGAL_GROUP_TYPE:2600,CANNOT_JOIN_WORK:2601,CANNOT_CHANGE_OWNER_IN_AVCHATROOM:2620,CANNOT_CHANGE_OWNER_TO_SELF:2621,CANNOT_DISMISS_Work:2622,MEMBER_NOT_IN_GROUP:2623,CANNOT_USE_GRP_ATTR_NOT_AVCHATROOM:2641,CANNOT_USE_GRP_ATTR_AVCHATROOM_UNJOIN:2642,JOIN_GROUP_FAIL:2660,CANNOT_ADD_MEMBER_IN_AVCHATROOM:2661,CANNOT_JOIN_NON_AVCHATROOM_WITHOUT_LOGIN:2662,CANNOT_KICK_MEMBER_IN_AVCHATROOM:2680,NOT_OWNER:2681,CANNOT_SET_MEMBER_ROLE_IN_WORK_AND_AVCHATROOM:2682,INVALID_MEMBER_ROLE:2683,CANNOT_SET_SELF_MEMBER_ROLE:2684,CANNOT_MUTE_SELF:2685,NOT_MY_FRIEND:2700,ALREADY_MY_FRIEND:2701,FRIEND_GROUP_EXISTED:2710,FRIEND_GROUP_NOT_EXIST:2711,FRIEND_APPLICATION_NOT_EXIST:2716,UPDATE_PROFILE_INVALID_PARAM:2721,UPDATE_PROFILE_NO_KEY:2722,ADD_BLACKLIST_INVALID_PARAM:2740,DEL_BLACKLIST_INVALID_PARAM:2741,CANNOT_ADD_SELF_TO_BLACKLIST:2742,ADD_FRIEND_INVALID_PARAM:2760,NETWORK_ERROR:2800,NETWORK_TIMEOUT:2801,NETWORK_BASE_OPTIONS_NO_URL:2802,NETWORK_UNDEFINED_SERVER_NAME:2803,NETWORK_PACKAGE_UNDEFINED:2804,NO_NETWORK:2805,CONVERTOR_IRREGULAR_PARAMS:2900,NOTICE_RUNLOOP_UNEXPECTED_CONDITION:2901,NOTICE_RUNLOOP_OFFSET_LOST:2902,UNCAUGHT_ERROR:2903,GET_LONGPOLL_ID_FAILED:2904,INVALID_OPERATION:2905,CANNOT_FIND_PROTOCOL:2997,CANNOT_FIND_MODULE:2998,SDK_IS_NOT_READY:2999,LONG_POLL_KICK_OUT:91101,MESSAGE_A2KEY_EXPIRED:20002,ACCOUNT_A2KEY_EXPIRED:70001,LONG_POLL_API_PARAM_ERROR:90001,HELLO_ANSWER_KICKED_OUT:1002},Md="无 SDKAppID",yd="无 userID",Id="无 userSig",Td="无 tinyID",Sd="无 a2key",Ed="用户未登录",Dd="重复登录",kd="未检测到 COS 上传插件",Cd="获取 COS 预签名 URL 失败",Ad="消息发送失败",Nd="需要 Message 的实例",Od='Message.conversationType 只能为 "C2C" 或 "GROUP"',Ld="无法发送空文件",Rd="回调函数运行时遇到错误，请检查接入侧代码",bd="消息撤回失败",wd="消息删除失败",Pd="请先选择一个图片",Gd="只允许上传 jpg png jpeg gif bmp格式的图片",Ud="图片大小超过20M，无法发送",Fd="语音上传失败",qd="语音大小大于20M，无法发送",xd="视频上传失败",Vd="视频大小超过100M，无法发送",Kd="只允许上传 mp4 格式的视频",Bd="文件上传失败",Hd="请先选择一个文件",jd="文件大小超过100M，无法发送 ",Yd="缺少必要的参数文件 URL",Wd="非合并消息",zd="合并消息的 messageKey 无效",Jd="下载合并消息失败",Xd="选择的消息类型（如群提示消息）不可以转发",Qd="没有找到相应的会话，请检查传入参数",Zd="没有找到相应的用户或群组，请检查传入参数",$d="未记录的会话类型",ep="非法的群类型，请检查传入参数",tp="不能加入 Work 类型的群组",np="AVChatRoom 类型的群组不能转让群主",op="不能把群主转让给自己",rp="不能解散 Work 类型的群组",ap="用户不在该群组内",sp="加群失败，请检查传入参数或重试",ip="AVChatRoom 类型的群不支持邀请群成员",up="非 AVChatRoom 类型的群组不允许匿名加群，请先登录后再加群",cp="不能在 AVChatRoom 类型的群组踢人",lp="你不是群主，只有群主才有权限操作",dp="不能在 Work / AVChatRoom 类型的群中设置群成员身份",pp="不合法的群成员身份，请检查传入参数",gp="不能设置自己的群成员身份，请检查传入参数",hp="不能将自己禁言，请检查传入参数",fp="传入 updateMyProfile 接口的参数无效",_p="updateMyProfile 无标配资料字段或自定义资料字段",mp="传入 addToBlacklist 接口的参数无效",vp="传入 removeFromBlacklist 接口的参数无效",Mp="不能拉黑自己",yp="网络错误",Ip="请求超时",Tp="未连接到网络",Sp="无效操作，如调用了未定义或者未实现的方法等",Ep="无法找到协议",Dp="无法找到模块",kp="接口需要 SDK 处于 ready 状态后才能调用",Cp="upload",Ap="networkRTT",Np="messageE2EDelay",Op="sendMessageC2C",Lp="sendMessageGroup",Rp="sendMessageGroupAV",bp="sendMessageRichMedia",wp="cosUpload",Pp="messageReceivedGroup",Gp="messageReceivedGroupAVPush",Up="messageReceivedGroupAVPull",Fp=(Fn(Uc={},Ap,2),Fn(Uc,Np,3),Fn(Uc,Op,4),Fn(Uc,Lp,5),Fn(Uc,Rp,6),Fn(Uc,bp,7),Fn(Uc,Pp,8),Fn(Uc,Gp,9),Fn(Uc,Up,10),Fn(Uc,wp,11),Uc),qp={info:4,warning:5,error:6},xp={wifi:1,"2g":2,"3g":3,"4g":4,"5g":5,unknown:6,none:7,online:8},Vp={login:4},Kp=function(){function e(t){Pn(this,e),this.eventType=Vp[t]||0,this.timestamp=0,this.networkType=8,this.code=0,this.message="",this.moreMessage="",this.extension=t,this.costTime=0,this.duplicate=!1,this.level=4,this._sentFlag=!1,this._startts=Pi()}return Un(e,[{key:"updateTimeStamp",value:function(){this.timestamp=Pi()}},{key:"start",value:function(e){return this._startts=e,this}},{key:"end",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this._sentFlag){var n=Pi();this.costTime=n-this._startts,this.setMoreMessage("host:".concat(yu()," startts:").concat(this._startts," endts:").concat(n)),t?(this._sentFlag=!0,this._eventStatModule&&this._eventStatModule.pushIn(this)):setTimeout((function(){e._sentFlag=!0,e._eventStatModule&&e._eventStatModule.pushIn(e)}),0)}}},{key:"setError",value:function(e,t,n){return e instanceof Error?(this._sentFlag||(this.setNetworkType(n),t?(e.code&&this.setCode(e.code),e.message&&this.setMoreMessage(e.message)):(this.setCode(vd.NO_NETWORK),this.setMoreMessage(Tp)),this.setLevel("error")),this):(xi.warn("SSOLogData.setError value not instanceof Error, please check!"),this)}},{key:"setCode",value:function(e){return Wi(e)||this._sentFlag||("ECONNABORTED"===e&&(this.code=103),Ki(e)?this.code=e:xi.warn("SSOLogData.setCode value not a number, please check!",e,wn(e))),this}},{key:"setMessage",value:function(e){return Wi(e)||this._sentFlag||(Ki(e)&&(this.message=e.toString()),Bi(e)&&(this.message=e)),this}},{key:"setLevel",value:function(e){return Wi(e)||this._sentFlag||(this.level=qp[e]),this}},{key:"setMoreMessage",value:function(e){return Au(this.moreMessage)?this.moreMessage="".concat(e):this.moreMessage+=" ".concat(e),this}},{key:"setNetworkType",value:function(e){return Wi(e)||Wi(xp[e])?xi.warn("SSOLogData.setNetworkType value is undefined, please check!"):this.networkType=xp[e],this}},{key:"getStartTs",value:function(){return this._startts}}],[{key:"bindEventStatModule",value:function(t){e.prototype._eventStatModule=t}}]),e}(),Bp="sdkConstruct",Hp="sdkReady",jp="login",Yp="logout",Wp="kickedOut",zp="registerPlugin",Jp="wsConnect",Xp="wsOnOpen",Qp="wsOnClose",Zp="wsOnError",$p="getCosAuthKey",eg="getCosPreSigUrl",tg="upload",ng="sendMessage",og="getC2CRoamingMessages",rg="getGroupRoamingMessages",ag="revokeMessage",sg="deleteMessage",ig="setC2CMessageRead",ug="setGroupMessageRead",cg="emptyMessageBody",lg="getPeerReadTime",dg="uploadMergerMessage",pg="downloadMergerMessage",gg="jsonParseError",hg="messageE2EDelayException",fg="getConversationList",_g="getConversationProfile",mg="deleteConversation",vg="pinConversation",Mg="getConversationListInStorage",yg="syncConversationList",Ig="createGroup",Tg="applyJoinGroup",Sg="quitGroup",Eg="searchGroupByID",Dg="changeGroupOwner",kg="handleGroupApplication",Cg="handleGroupInvitation",Ag="setMessageRemindType",Ng="dismissGroup",Og="updateGroupProfile",Lg="getGroupList",Rg="getGroupProfile",bg="getGroupListInStorage",wg="getGroupLastSequence",Pg="getGroupMissingMessage",Gg="pagingGetGroupList",Ug="getGroupSimplifiedInfo",Fg="joinWithoutAuth",qg="initGroupAttributes",xg="setGroupAttributes",Vg="deleteGroupAttributes",Kg="getGroupAttributes",Bg="getGroupMemberList",Hg="getGroupMemberProfile",jg="addGroupMember",Yg="deleteGroupMember",Wg="setGroupMemberMuteTime",zg="setGroupMemberNameCard",Jg="setGroupMemberRole",Xg="setGroupMemberCustomField",Qg="getGroupOnlineMemberCount",Zg="longPollingAVError",$g="messageLoss",eh="messageStacked",th="getUserProfile",nh="updateMyProfile",oh="getBlacklist",rh="addToBlacklist",ah="removeFromBlacklist",sh="callbackFunctionError",ih="fetchCloudControlConfig",uh="pushedCloudControlConfig",ch="error",lh=l.f,dh=function(e){return function(t){for(var n,o=m(t),r=Ut(o),a=r.length,s=0,u=[];a>s;)n=r[s++],i&&!lh.call(o,n)||u.push(e?[n,o[n]]:o[n]);return u}},ph={entries:dh(!0),values:dh(!1)}.values;be({target:"Object",stat:!0},{values:function(e){return ph(e)}});var gh=function(){function e(t){Pn(this,e),this.type=so.MSG_TEXT,this.content={text:t.text||""}}return Un(e,[{key:"setText",value:function(e){this.content.text=e}},{key:"sendable",value:function(){return 0!==this.content.text.length}}]),e}(),hh=Object.assign,fh=Object.defineProperty,_h=!hh||a((function(){if(i&&1!==hh({b:1},hh(fh({},"a",{enumerable:!0,get:function(){fh(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol();return e[n]=7,"abcdefghijklmnopqrst".split("").forEach((function(e){t[e]=e})),7!=hh({},e)[n]||"abcdefghijklmnopqrst"!=Ut(hh({},t)).join("")}))?function(e,t){for(var n=Pe(e),o=arguments.length,r=1,a=Te.f,s=l.f;o>r;)for(var u,c=f(arguments[r++]),d=a?Ut(c).concat(a(c)):Ut(c),p=d.length,g=0;p>g;)u=d[g++],i&&!s.call(c,u)||(n[u]=c[u]);return n}:hh;be({target:"Object",stat:!0,forced:Object.assign!==_h},{assign:_h});var mh=Ke("iterator"),vh=!a((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),t=e.searchParams,n="";return e.pathname="c%20d",t.forEach((function(e,o){t.delete("b"),n+=o+e})),!t.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[mh]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==n||"x"!==new URL("http://x",void 0).host})),Mh=/[^\0-\u007E]/,yh=/[.\u3002\uFF0E\uFF61]/g,Ih="Overflow: input needs wider integers to process",Th=Math.floor,Sh=String.fromCharCode,Eh=function(e){return e+22+75*(e<26)},Dh=function(e,t,n){var o=0;for(e=n?Th(e/700):e>>1,e+=Th(e/t);e>455;o+=36)e=Th(e/35);return Th(o+36*e/(e+38))},kh=function(e){var t,n,o=[],r=(e=function(e){for(var t=[],n=0,o=e.length;n<o;){var r=e.charCodeAt(n++);if(r>=55296&&r<=56319&&n<o){var a=e.charCodeAt(n++);56320==(64512&a)?t.push(((1023&r)<<10)+(1023&a)+65536):(t.push(r),n--)}else t.push(r)}return t}(e)).length,a=128,s=0,i=72;for(t=0;t<e.length;t++)(n=e[t])<128&&o.push(Sh(n));var u=o.length,c=u;for(u&&o.push("-");c<r;){var l=2147483647;for(t=0;t<e.length;t++)(n=e[t])>=a&&n<l&&(l=n);var d=c+1;if(l-a>Th((2147483647-s)/d))throw RangeError(Ih);for(s+=(l-a)*d,a=l,t=0;t<e.length;t++){if((n=e[t])<a&&++s>2147483647)throw RangeError(Ih);if(n==a){for(var p=s,g=36;;g+=36){var h=g<=i?1:g>=i+26?26:g-i;if(p<h)break;var f=p-h,_=36-h;o.push(Sh(Eh(h+f%_))),p=Th(f/_)}o.push(Sh(Eh(p))),i=Dh(s,d,c==u),s=0,++c}}++s,++a}return o.join("")},Ch=function(e){var t=At(e);if("function"!=typeof t)throw TypeError(String(e)+" is not iterable");return A(t.call(e))},Ah=se("fetch"),Nh=se("Headers"),Oh=Ke("iterator"),Lh=ne.set,Rh=ne.getterFor("URLSearchParams"),bh=ne.getterFor("URLSearchParamsIterator"),wh=/\+/g,Ph=Array(4),Gh=function(e){return Ph[e-1]||(Ph[e-1]=RegExp("((?:%[\\da-f]{2}){"+e+"})","gi"))},Uh=function(e){try{return decodeURIComponent(e)}catch(Xv){return e}},Fh=function(e){var t=e.replace(wh," "),n=4;try{return decodeURIComponent(t)}catch(Xv){for(;n;)t=t.replace(Gh(n--),Uh);return t}},qh=/[!'()~]|%20/g,xh={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},Vh=function(e){return xh[e]},Kh=function(e){return encodeURIComponent(e).replace(qh,Vh)},Bh=function(e,t){if(t)for(var n,o,r=t.split("&"),a=0;a<r.length;)(n=r[a++]).length&&(o=n.split("="),e.push({key:Fh(o.shift()),value:Fh(o.join("="))}))},Hh=function(e){this.entries.length=0,Bh(this.entries,e)},jh=function(e,t){if(e<t)throw TypeError("Not enough arguments")},Yh=yn((function(e,t){Lh(this,{type:"URLSearchParamsIterator",iterator:Ch(Rh(e).entries),kind:t})}),"Iterator",(function(){var e=bh(this),t=e.kind,n=e.iterator.next(),o=n.value;return n.done||(n.value="keys"===t?o.key:"values"===t?o.value:[o.key,o.value]),n})),Wh=function e(){Lr(this,e,"URLSearchParams");var t,n,o,r,a,s,i,u,c,l=arguments.length>0?arguments[0]:void 0,d=this,p=[];if(Lh(d,{type:"URLSearchParams",entries:p,updateURL:function(){},updateSearchParams:Hh}),void 0!==l)if(v(l))if("function"==typeof(t=At(l)))for(o=(n=t.call(l)).next;!(r=o.call(n)).done;){if((i=(s=(a=Ch(A(r.value))).next).call(a)).done||(u=s.call(a)).done||!s.call(a).done)throw TypeError("Expected sequence with length 2");p.push({key:i.value+"",value:u.value+""})}else for(c in l)I(l,c)&&p.push({key:c,value:l[c]+""});else Bh(p,"string"==typeof l?"?"===l.charAt(0)?l.slice(1):l:l+"")},zh=Wh.prototype;Ar(zh,{append:function(e,t){jh(arguments.length,2);var n=Rh(this);n.entries.push({key:e+"",value:t+""}),n.updateURL()},delete:function(e){jh(arguments.length,1);for(var t=Rh(this),n=t.entries,o=e+"",r=0;r<n.length;)n[r].key===o?n.splice(r,1):r++;t.updateURL()},get:function(e){jh(arguments.length,1);for(var t=Rh(this).entries,n=e+"",o=0;o<t.length;o++)if(t[o].key===n)return t[o].value;return null},getAll:function(e){jh(arguments.length,1);for(var t=Rh(this).entries,n=e+"",o=[],r=0;r<t.length;r++)t[r].key===n&&o.push(t[r].value);return o},has:function(e){jh(arguments.length,1);for(var t=Rh(this).entries,n=e+"",o=0;o<t.length;)if(t[o++].key===n)return!0;return!1},set:function(e,t){jh(arguments.length,1);for(var n,o=Rh(this),r=o.entries,a=!1,s=e+"",i=t+"",u=0;u<r.length;u++)(n=r[u]).key===s&&(a?r.splice(u--,1):(a=!0,n.value=i));a||r.push({key:s,value:i}),o.updateURL()},sort:function(){var e,t,n,o=Rh(this),r=o.entries,a=r.slice();for(r.length=0,n=0;n<a.length;n++){for(e=a[n],t=0;t<n;t++)if(r[t].key>e.key){r.splice(t,0,e);break}t===n&&r.push(e)}o.updateURL()},forEach:function(e){for(var t,n=Rh(this).entries,o=rt(e,arguments.length>1?arguments[1]:void 0,3),r=0;r<n.length;)o((t=n[r++]).value,t.key,this)},keys:function(){return new Yh(this,"keys")},values:function(){return new Yh(this,"values")},entries:function(){return new Yh(this,"entries")}},{enumerable:!0}),oe(zh,Oh,zh.entries),oe(zh,"toString",(function(){for(var e,t=Rh(this).entries,n=[],o=0;o<t.length;)e=t[o++],n.push(Kh(e.key)+"="+Kh(e.value));return n.join("&")}),{enumerable:!0}),mn(Wh,"URLSearchParams"),be({global:!0,forced:!vh},{URLSearchParams:Wh}),vh||"function"!=typeof Ah||"function"!=typeof Nh||be({global:!0,enumerable:!0,forced:!0},{fetch:function(e){var t,n,o,r=[e];return arguments.length>1&&(t=arguments[1],v(t)&&(n=t.body,"URLSearchParams"===kt(n)&&((o=t.headers?new Nh(t.headers):new Nh).has("content-type")||o.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),t=Ht(t,{body:d(0,String(n)),headers:d(0,o)}))),r.push(t)),Ah.apply(this,r)}});var Jh,Xh={URLSearchParams:Wh,getState:Rh},Qh=sn.codeAt,Zh=r.URL,$h=Xh.URLSearchParams,ef=Xh.getState,tf=ne.set,nf=ne.getterFor("URL"),of=Math.floor,rf=Math.pow,af=/[A-Za-z]/,sf=/[\d+\-.A-Za-z]/,uf=/\d/,cf=/^(0x|0X)/,lf=/^[0-7]+$/,df=/^\d+$/,pf=/^[\dA-Fa-f]+$/,gf=/[\u0000\u0009\u000A\u000D #%/:?@[\\]]/,hf=/[\u0000\u0009\u000A\u000D #/:?@[\\]]/,ff=/^[\u0000-\u001F ]+|[\u0000-\u001F ]+$/g,_f=/[\u0009\u000A\u000D]/g,mf=function(e,t){var n,o,r;if("["==t.charAt(0)){if("]"!=t.charAt(t.length-1))return"Invalid host";if(!(n=Mf(t.slice(1,-1))))return"Invalid host";e.host=n}else if(Cf(e)){if(t=function(e){var t,n,o=[],r=e.toLowerCase().replace(yh,".").split(".");for(t=0;t<r.length;t++)n=r[t],o.push(Mh.test(n)?"xn--"+kh(n):n);return o.join(".")}(t),gf.test(t))return"Invalid host";if(null===(n=vf(t)))return"Invalid host";e.host=n}else{if(hf.test(t))return"Invalid host";for(n="",o=Nt(t),r=0;r<o.length;r++)n+=Df(o[r],If);e.host=n}},vf=function(e){var t,n,o,r,a,s,i,u=e.split(".");if(u.length&&""==u[u.length-1]&&u.pop(),(t=u.length)>4)return e;for(n=[],o=0;o<t;o++){if(""==(r=u[o]))return e;if(a=10,r.length>1&&"0"==r.charAt(0)&&(a=cf.test(r)?16:8,r=r.slice(8==a?1:2)),""===r)s=0;else{if(!(10==a?df:8==a?lf:pf).test(r))return e;s=parseInt(r,a)}n.push(s)}for(o=0;o<t;o++)if(s=n[o],o==t-1){if(s>=rf(256,5-t))return null}else if(s>255)return null;for(i=n.pop(),o=0;o<n.length;o++)i+=n[o]*rf(256,3-o);return i},Mf=function(e){var t,n,o,r,a,s,i,u=[0,0,0,0,0,0,0,0],c=0,l=null,d=0,p=function(){return e.charAt(d)};if(":"==p()){if(":"!=e.charAt(1))return;d+=2,l=++c}for(;p();){if(8==c)return;if(":"!=p()){for(t=n=0;n<4&&pf.test(p());)t=16*t+parseInt(p(),16),d++,n++;if("."==p()){if(0==n)return;if(d-=n,c>6)return;for(o=0;p();){if(r=null,o>0){if(!("."==p()&&o<4))return;d++}if(!uf.test(p()))return;for(;uf.test(p());){if(a=parseInt(p(),10),null===r)r=a;else{if(0==r)return;r=10*r+a}if(r>255)return;d++}u[c]=256*u[c]+r,2!=++o&&4!=o||c++}if(4!=o)return;break}if(":"==p()){if(d++,!p())return}else if(p())return;u[c++]=t}else{if(null!==l)return;d++,l=++c}}if(null!==l)for(s=c-l,c=7;0!=c&&s>0;)i=u[c],u[c--]=u[l+s-1],u[l+--s]=i;else if(8!=c)return;return u},yf=function(e){var t,n,o,r;if("number"==typeof e){for(t=[],n=0;n<4;n++)t.unshift(e%256),e=of(e/256);return t.join(".")}if("object"==s(e)){for(t="",o=function(e){for(var t=null,n=1,o=null,r=0,a=0;a<8;a++)0!==e[a]?(r>n&&(t=o,n=r),o=null,r=0):(null===o&&(o=a),++r);return r>n&&(t=o,n=r),t}(e),n=0;n<8;n++)r&&0===e[n]||(r&&(r=!1),o===n?(t+=n?":":"::",r=!0):(t+=e[n].toString(16),n<7&&(t+=":")));return"["+t+"]"}return e},If={},Tf=_h({},If,{" ":1,'"':1,"<":1,">":1,"`":1}),Sf=_h({},Tf,{"#":1,"?":1,"{":1,"}":1}),Ef=_h({},Sf,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),Df=function(e,t){var n=Qh(e,0);return n>32&&n<127&&!I(t,e)?e:encodeURIComponent(e)},kf={ftp:21,file:null,http:80,https:443,ws:80,wss:443},Cf=function(e){return I(kf,e.scheme)},Af=function(e){return""!=e.username||""!=e.password},Nf=function(e){return!e.host||e.cannotBeABaseURL||"file"==e.scheme},Of=function(e,t){var n;return 2==e.length&&af.test(e.charAt(0))&&(":"==(n=e.charAt(1))||!t&&"|"==n)},Lf=function(e){var t;return e.length>1&&Of(e.slice(0,2))&&(2==e.length||"/"===(t=e.charAt(2))||"\\"===t||"?"===t||"#"===t)},Rf=function(e){var t=e.path,n=t.length;!n||"file"==e.scheme&&1==n&&Of(t[0],!0)||t.pop()},bf=function(e){return"."===e||"%2e"===e.toLowerCase()},wf={},Pf={},Gf={},Uf={},Ff={},qf={},xf={},Vf={},Kf={},Bf={},Hf={},jf={},Yf={},Wf={},zf={},Jf={},Xf={},Qf={},Zf={},$f={},e_={},t_=function(e,t,n,o){var r,a,s,i,u,c=n||wf,l=0,d="",p=!1,g=!1,h=!1;for(n||(e.scheme="",e.username="",e.password="",e.host=null,e.port=null,e.path=[],e.query=null,e.fragment=null,e.cannotBeABaseURL=!1,t=t.replace(ff,"")),t=t.replace(_f,""),r=Nt(t);l<=r.length;){switch(a=r[l],c){case wf:if(!a||!af.test(a)){if(n)return"Invalid scheme";c=Gf;continue}d+=a.toLowerCase(),c=Pf;break;case Pf:if(a&&(sf.test(a)||"+"==a||"-"==a||"."==a))d+=a.toLowerCase();else{if(":"!=a){if(n)return"Invalid scheme";d="",c=Gf,l=0;continue}if(n&&(Cf(e)!=I(kf,d)||"file"==d&&(Af(e)||null!==e.port)||"file"==e.scheme&&!e.host))return;if(e.scheme=d,n)return void(Cf(e)&&kf[e.scheme]==e.port&&(e.port=null));d="","file"==e.scheme?c=Wf:Cf(e)&&o&&o.scheme==e.scheme?c=Uf:Cf(e)?c=Vf:"/"==r[l+1]?(c=Ff,l++):(e.cannotBeABaseURL=!0,e.path.push(""),c=Zf)}break;case Gf:if(!o||o.cannotBeABaseURL&&"#"!=a)return"Invalid scheme";if(o.cannotBeABaseURL&&"#"==a){e.scheme=o.scheme,e.path=o.path.slice(),e.query=o.query,e.fragment="",e.cannotBeABaseURL=!0,c=e_;break}c="file"==o.scheme?Wf:qf;continue;case Uf:if("/"!=a||"/"!=r[l+1]){c=qf;continue}c=Kf,l++;break;case Ff:if("/"==a){c=Bf;break}c=Qf;continue;case qf:if(e.scheme=o.scheme,a==Jh)e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,e.path=o.path.slice(),e.query=o.query;else if("/"==a||"\\"==a&&Cf(e))c=xf;else if("?"==a)e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,e.path=o.path.slice(),e.query="",c=$f;else{if("#"!=a){e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,e.path=o.path.slice(),e.path.pop(),c=Qf;continue}e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,e.path=o.path.slice(),e.query=o.query,e.fragment="",c=e_}break;case xf:if(!Cf(e)||"/"!=a&&"\\"!=a){if("/"!=a){e.username=o.username,e.password=o.password,e.host=o.host,e.port=o.port,c=Qf;continue}c=Bf}else c=Kf;break;case Vf:if(c=Kf,"/"!=a||"/"!=d.charAt(l+1))continue;l++;break;case Kf:if("/"!=a&&"\\"!=a){c=Bf;continue}break;case Bf:if("@"==a){p&&(d="%40"+d),p=!0,s=Nt(d);for(var f=0;f<s.length;f++){var _=s[f];if(":"!=_||h){var m=Df(_,Ef);h?e.password+=m:e.username+=m}else h=!0}d=""}else if(a==Jh||"/"==a||"?"==a||"#"==a||"\\"==a&&Cf(e)){if(p&&""==d)return"Invalid authority";l-=Nt(d).length+1,d="",c=Hf}else d+=a;break;case Hf:case jf:if(n&&"file"==e.scheme){c=Jf;continue}if(":"!=a||g){if(a==Jh||"/"==a||"?"==a||"#"==a||"\\"==a&&Cf(e)){if(Cf(e)&&""==d)return"Invalid host";if(n&&""==d&&(Af(e)||null!==e.port))return;if(i=mf(e,d))return i;if(d="",c=Xf,n)return;continue}"["==a?g=!0:"]"==a&&(g=!1),d+=a}else{if(""==d)return"Invalid host";if(i=mf(e,d))return i;if(d="",c=Yf,n==jf)return}break;case Yf:if(!uf.test(a)){if(a==Jh||"/"==a||"?"==a||"#"==a||"\\"==a&&Cf(e)||n){if(""!=d){var v=parseInt(d,10);if(v>65535)return"Invalid port";e.port=Cf(e)&&v===kf[e.scheme]?null:v,d=""}if(n)return;c=Xf;continue}return"Invalid port"}d+=a;break;case Wf:if(e.scheme="file","/"==a||"\\"==a)c=zf;else{if(!o||"file"!=o.scheme){c=Qf;continue}if(a==Jh)e.host=o.host,e.path=o.path.slice(),e.query=o.query;else if("?"==a)e.host=o.host,e.path=o.path.slice(),e.query="",c=$f;else{if("#"!=a){Lf(r.slice(l).join(""))||(e.host=o.host,e.path=o.path.slice(),Rf(e)),c=Qf;continue}e.host=o.host,e.path=o.path.slice(),e.query=o.query,e.fragment="",c=e_}}break;case zf:if("/"==a||"\\"==a){c=Jf;break}o&&"file"==o.scheme&&!Lf(r.slice(l).join(""))&&(Of(o.path[0],!0)?e.path.push(o.path[0]):e.host=o.host),c=Qf;continue;case Jf:if(a==Jh||"/"==a||"\\"==a||"?"==a||"#"==a){if(!n&&Of(d))c=Qf;else if(""==d){if(e.host="",n)return;c=Xf}else{if(i=mf(e,d))return i;if("localhost"==e.host&&(e.host=""),n)return;d="",c=Xf}continue}d+=a;break;case Xf:if(Cf(e)){if(c=Qf,"/"!=a&&"\\"!=a)continue}else if(n||"?"!=a)if(n||"#"!=a){if(a!=Jh&&(c=Qf,"/"!=a))continue}else e.fragment="",c=e_;else e.query="",c=$f;break;case Qf:if(a==Jh||"/"==a||"\\"==a&&Cf(e)||!n&&("?"==a||"#"==a)){if(".."===(u=(u=d).toLowerCase())||"%2e."===u||".%2e"===u||"%2e%2e"===u?(Rf(e),"/"==a||"\\"==a&&Cf(e)||e.path.push("")):bf(d)?"/"==a||"\\"==a&&Cf(e)||e.path.push(""):("file"==e.scheme&&!e.path.length&&Of(d)&&(e.host&&(e.host=""),d=d.charAt(0)+":"),e.path.push(d)),d="","file"==e.scheme&&(a==Jh||"?"==a||"#"==a))for(;e.path.length>1&&""===e.path[0];)e.path.shift();"?"==a?(e.query="",c=$f):"#"==a&&(e.fragment="",c=e_)}else d+=Df(a,Sf);break;case Zf:"?"==a?(e.query="",c=$f):"#"==a?(e.fragment="",c=e_):a!=Jh&&(e.path[0]+=Df(a,If));break;case $f:n||"#"!=a?a!=Jh&&("'"==a&&Cf(e)?e.query+="%27":e.query+="#"==a?"%23":Df(a,If)):(e.fragment="",c=e_);break;case e_:a!=Jh&&(e.fragment+=Df(a,Tf))}l++}},n_=function e(t){var n,o,r=Lr(this,e,"URL"),a=arguments.length>1?arguments[1]:void 0,s=String(t),u=tf(r,{type:"URL"});if(void 0!==a)if(a instanceof e)n=nf(a);else if(o=t_(n={},String(a)))throw TypeError(o);if(o=t_(u,s,null,n))throw TypeError(o);var c=u.searchParams=new $h,l=ef(c);l.updateSearchParams(u.query),l.updateURL=function(){u.query=String(c)||null},i||(r.href=r_.call(r),r.origin=a_.call(r),r.protocol=s_.call(r),r.username=i_.call(r),r.password=u_.call(r),r.host=c_.call(r),r.hostname=l_.call(r),r.port=d_.call(r),r.pathname=p_.call(r),r.search=g_.call(r),r.searchParams=h_.call(r),r.hash=f_.call(r))},o_=n_.prototype,r_=function(){var e=nf(this),t=e.scheme,n=e.username,o=e.password,r=e.host,a=e.port,s=e.path,i=e.query,u=e.fragment,c=t+":";return null!==r?(c+="//",Af(e)&&(c+=n+(o?":"+o:"")+"@"),c+=yf(r),null!==a&&(c+=":"+a)):"file"==t&&(c+="//"),c+=e.cannotBeABaseURL?s[0]:s.length?"/"+s.join("/"):"",null!==i&&(c+="?"+i),null!==u&&(c+="#"+u),c},a_=function(){var e=nf(this),t=e.scheme,n=e.port;if("blob"==t)try{return new URL(t.path[0]).origin}catch(Xv){return"null"}return"file"!=t&&Cf(e)?t+"://"+yf(e.host)+(null!==n?":"+n:""):"null"},s_=function(){return nf(this).scheme+":"},i_=function(){return nf(this).username},u_=function(){return nf(this).password},c_=function(){var e=nf(this),t=e.host,n=e.port;return null===t?"":null===n?yf(t):yf(t)+":"+n},l_=function(){var e=nf(this).host;return null===e?"":yf(e)},d_=function(){var e=nf(this).port;return null===e?"":String(e)},p_=function(){var e=nf(this),t=e.path;return e.cannotBeABaseURL?t[0]:t.length?"/"+t.join("/"):""},g_=function(){var e=nf(this).query;return e?"?"+e:""},h_=function(){return nf(this).searchParams},f_=function(){var e=nf(this).fragment;return e?"#"+e:""},__=function(e,t){return{get:e,set:t,configurable:!0,enumerable:!0}};if(i&&Ft(o_,{href:__(r_,(function(e){var t=nf(this),n=String(e),o=t_(t,n);if(o)throw TypeError(o);ef(t.searchParams).updateSearchParams(t.query)})),origin:__(a_),protocol:__(s_,(function(e){var t=nf(this);t_(t,String(e)+":",wf)})),username:__(i_,(function(e){var t=nf(this),n=Nt(String(e));if(!Nf(t)){t.username="";for(var o=0;o<n.length;o++)t.username+=Df(n[o],Ef)}})),password:__(u_,(function(e){var t=nf(this),n=Nt(String(e));if(!Nf(t)){t.password="";for(var o=0;o<n.length;o++)t.password+=Df(n[o],Ef)}})),host:__(c_,(function(e){var t=nf(this);t.cannotBeABaseURL||t_(t,String(e),Hf)})),hostname:__(l_,(function(e){var t=nf(this);t.cannotBeABaseURL||t_(t,String(e),jf)})),port:__(d_,(function(e){var t=nf(this);Nf(t)||(""==(e=String(e))?t.port=null:t_(t,e,Yf))})),pathname:__(p_,(function(e){var t=nf(this);t.cannotBeABaseURL||(t.path=[],t_(t,e+"",Xf))})),search:__(g_,(function(e){var t=nf(this);""==(e=String(e))?t.query=null:("?"==e.charAt(0)&&(e=e.slice(1)),t.query="",t_(t,e,$f)),ef(t.searchParams).updateSearchParams(t.query)})),searchParams:__(h_),hash:__(f_,(function(e){var t=nf(this);""!=(e=String(e))?("#"==e.charAt(0)&&(e=e.slice(1)),t.fragment="",t_(t,e,e_)):t.fragment=null}))}),oe(o_,"toJSON",(function(){return r_.call(this)}),{enumerable:!0}),oe(o_,"toString",(function(){return r_.call(this)}),{enumerable:!0}),Zh){var m_=Zh.createObjectURL,v_=Zh.revokeObjectURL;m_&&oe(n_,"createObjectURL",(function(e){return m_.apply(Zh,arguments)})),v_&&oe(n_,"revokeObjectURL",(function(e){return v_.apply(Zh,arguments)}))}mn(n_,"URL"),be({global:!0,forced:!vh,sham:!i},{URL:n_});var M_={JSON:{TYPE:{C2C:{NOTICE:1,COMMON:9,EVENT:10},GROUP:{COMMON:3,TIP:4,SYSTEM:5,TIP2:6},FRIEND:{NOTICE:7},PROFILE:{NOTICE:8}},SUBTYPE:{C2C:{COMMON:0,READED:92,KICKEDOUT:96},GROUP:{COMMON:0,LOVEMESSAGE:1,TIP:2,REDPACKET:3}},OPTIONS:{GROUP:{JOIN:1,QUIT:2,KICK:3,SET_ADMIN:4,CANCEL_ADMIN:5,MODIFY_GROUP_INFO:6,MODIFY_MEMBER_INFO:7}}},PROTOBUF:{},IMAGE_TYPES:{ORIGIN:1,LARGE:2,SMALL:3},IMAGE_FORMAT:{JPG:1,JPEG:1,GIF:2,PNG:3,BMP:4,UNKNOWN:255}},y_={NICK:"Tag_Profile_IM_Nick",GENDER:"Tag_Profile_IM_Gender",BIRTHDAY:"Tag_Profile_IM_BirthDay",LOCATION:"Tag_Profile_IM_Location",SELFSIGNATURE:"Tag_Profile_IM_SelfSignature",ALLOWTYPE:"Tag_Profile_IM_AllowType",LANGUAGE:"Tag_Profile_IM_Language",AVATAR:"Tag_Profile_IM_Image",MESSAGESETTINGS:"Tag_Profile_IM_MsgSettings",ADMINFORBIDTYPE:"Tag_Profile_IM_AdminForbidType",LEVEL:"Tag_Profile_IM_Level",ROLE:"Tag_Profile_IM_Role"},I_={UNKNOWN:"Gender_Type_Unknown",FEMALE:"Gender_Type_Female",MALE:"Gender_Type_Male"},T_={NONE:"AdminForbid_Type_None",SEND_OUT:"AdminForbid_Type_SendOut"},S_={NEED_CONFIRM:"AllowType_Type_NeedConfirm",ALLOW_ANY:"AllowType_Type_AllowAny",DENY_ANY:"AllowType_Type_DenyAny"},E_="JoinedSuccess",D_="WaitAdminApproval",k_=function(){function e(t){Pn(this,e),this._imageMemoryURL="",ri?this.createImageDataASURLInWXMiniApp(t.file):this.createImageDataASURLInWeb(t.file),this._initImageInfoModel(),this.type=so.MSG_IMAGE,this._percent=0,this.content={imageFormat:t.imageFormat||M_.IMAGE_FORMAT.UNKNOWN,uuid:t.uuid,imageInfoArray:[]},this.initImageInfoArray(t.imageInfoArray),this._defaultImage="http://imgcache.qq.com/open/qcloud/video/act/webim-images/default.jpg",this._autoFixUrl()}return Un(e,[{key:"_initImageInfoModel",value:function(){var e=this;this._ImageInfoModel=function(t){this.instanceID=ru(9999999),this.sizeType=t.type||0,this.type=0,this.size=t.size||0,this.width=t.width||0,this.height=t.height||0,this.imageUrl=t.url||"",this.url=t.url||e._imageMemoryURL||e._defaultImage},this._ImageInfoModel.prototype={setSizeType:function(e){this.sizeType=e},setType:function(e){this.type=e},setImageUrl:function(e){e&&(this.imageUrl=e)},getImageUrl:function(){return this.imageUrl}}}},{key:"initImageInfoArray",value:function(e){for(var t=0,n=null,o=null;t<=2;)o=Wi(e)||Wi(e[t])?{type:0,size:0,width:0,height:0,url:""}:e[t],(n=new this._ImageInfoModel(o)).setSizeType(t+1),n.setType(t),this.addImageInfo(n),t++;this.updateAccessSideImageInfoArray()}},{key:"updateImageInfoArray",value:function(e){for(var t,n=this.content.imageInfoArray.length,o=0;o<n;o++)t=this.content.imageInfoArray[o],e[o].size&&(t.size=e[o].size),e[o].url&&t.setImageUrl(e[o].url),e[o].width&&(t.width=e[o].width),e[o].height&&(t.height=e[o].height)}},{key:"_autoFixUrl",value:function(){for(var e=this.content.imageInfoArray.length,t="",n="",o=["http","https"],r=null,a=0;a<e;a++)this.content.imageInfoArray[a].url&&""!==(r=this.content.imageInfoArray[a]).imageUrl&&(n=r.imageUrl.slice(0,r.imageUrl.indexOf("://")+1),t=r.imageUrl.slice(r.imageUrl.indexOf("://")+1),o.indexOf(n)<0&&(n="https:"),this.content.imageInfoArray[a].setImageUrl([n,t].join("")))}},{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateImageFormat",value:function(e){this.content.imageFormat=M_.IMAGE_FORMAT[e.toUpperCase()]||M_.IMAGE_FORMAT.UNKNOWN}},{key:"createImageDataASURLInWeb",value:function(e){void 0!==e&&e.files.length>0&&(this._imageMemoryURL=window.URL.createObjectURL(e.files[0]))}},{key:"createImageDataASURLInWXMiniApp",value:function(e){e&&e.url&&(this._imageMemoryURL=e.url)}},{key:"replaceImageInfo",value:function(e,t){this.content.imageInfoArray[t]instanceof this._ImageInfoModel||(this.content.imageInfoArray[t]=e)}},{key:"addImageInfo",value:function(e){this.content.imageInfoArray.length>=3||this.content.imageInfoArray.push(e)}},{key:"updateAccessSideImageInfoArray",value:function(){var e=this.content.imageInfoArray,t=e[0],n=t.width,o=void 0===n?0:n,r=t.height,a=void 0===r?0:r;0!==o&&0!==a&&(Tu(e),Object.assign(e[2],Iu({originWidth:o,originHeight:a,min:720})))}},{key:"sendable",value:function(){return 0!==this.content.imageInfoArray.length&&""!==this.content.imageInfoArray[0].imageUrl&&0!==this.content.imageInfoArray[0].size}}]),e}(),C_=function(){function e(t){Pn(this,e),this.type=so.MSG_FACE,this.content=t||null}return Un(e,[{key:"sendable",value:function(){return null!==this.content}}]),e}(),A_=function(){function e(t){Pn(this,e),this.type=so.MSG_AUDIO,this._percent=0,this.content={downloadFlag:2,second:t.second,size:t.size,url:t.url,remoteAudioUrl:t.url||"",uuid:t.uuid}}return Un(e,[{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateAudioUrl",value:function(e){this.content.remoteAudioUrl=e}},{key:"sendable",value:function(){return""!==this.content.remoteAudioUrl}}]),e}();be({target:"Object",stat:!0,forced:!i,sham:!i},{defineProperty:O.f});var N_={from:!0,groupID:!0,groupName:!0,to:!0},O_=function(){function e(t){Pn(this,e),this.type=so.MSG_GRP_TIP,this.content={},this._initContent(t)}return Un(e,[{key:"_initContent",value:function(e){var t=this;Object.keys(e).forEach((function(n){switch(n){case"remarkInfo":break;case"groupProfile":t.content.groupProfile={},t._initGroupProfile(e[n]);break;case"operatorInfo":case"memberInfoList":break;case"msgMemberInfo":t.content.memberList=e[n],Object.defineProperty(t.content,"msgMemberInfo",{get:function(){return xi.warn("!!! 禁言的群提示消息中的 payload.msgMemberInfo 属性即将废弃，请使用 payload.memberList 属性替代。 \n","msgMemberInfo 中的 shutupTime 属性对应更改为 memberList 中的 muteTime 属性，表示禁言时长。 \n","参考：群提示消息 https://web.sdk.qcloud.com/im/doc/zh-cn/Message.html#.GroupTipPayload"),t.content.memberList.map((function(e){return{userID:e.userID,shutupTime:e.muteTime}}))}});break;case"onlineMemberInfo":break;case"memberNum":t.content[n]=e[n],t.content.memberCount=e[n];break;default:t.content[n]=e[n]}})),this.content.userIDList||(this.content.userIDList=[this.content.operatorID])}},{key:"_initGroupProfile",value:function(e){for(var t=Object.keys(e),n=0;n<t.length;n++){var o=t[n];N_[o]&&(this.content.groupProfile[o]=e[o])}}}]),e}(),L_={from:!0,groupID:!0,groupName:!0,to:!0},R_=function(){function e(t){Pn(this,e),this.type=so.MSG_GRP_SYS_NOTICE,this.content={},this._initContent(t)}return Un(e,[{key:"_initContent",value:function(e){var t=this;Object.keys(e).forEach((function(n){switch(n){case"memberInfoList":break;case"remarkInfo":t.content.handleMessage=e[n];break;case"groupProfile":t.content.groupProfile={},t._initGroupProfile(e[n]);break;default:t.content[n]=e[n]}}))}},{key:"_initGroupProfile",value:function(e){for(var t=Object.keys(e),n=0;n<t.length;n++){var o=t[n];L_[o]&&("groupName"===o?this.content.groupProfile.name=e[o]:this.content.groupProfile[o]=e[o])}}}]),e}(),b_=Math.min,w_=[].lastIndexOf,P_=!!w_&&1/[1].lastIndexOf(1,-0)<0,G_=ut("lastIndexOf"),U_=pt("indexOf",{ACCESSORS:!0,1:0}),F_=!P_&&G_&&U_?w_:function(e){if(P_)return w_.apply(this,arguments)||0;var t=m(this),n=de(t.length),o=n-1;for(arguments.length>1&&(o=b_(o,ce(arguments[1]))),o<0&&(o=n+o);o>=0;o--)if(o in t&&t[o]===e)return o||0;return-1};be({target:"Array",proto:!0,forced:F_!==[].lastIndexOf},{lastIndexOf:F_});var q_=function(){function e(t){Pn(this,e),this.type=so.MSG_FILE,this._percent=0;var n=this._getFileInfo(t);this.content={downloadFlag:2,fileUrl:t.url||"",uuid:t.uuid,fileName:n.name||"",fileSize:n.size||0}}return Un(e,[{key:"_getFileInfo",value:function(e){if(e.fileName&&e.fileSize)return{size:e.fileSize,name:e.fileName};if(ri)return{};var t=e.file.files[0];return{size:t.size,name:t.name,type:t.type.slice(t.type.lastIndexOf("/")+1).toLowerCase()}}},{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateFileUrl",value:function(e){this.content.fileUrl=e}},{key:"sendable",value:function(){return""!==this.content.fileUrl&&""!==this.content.fileName&&0!==this.content.fileSize}}]),e}(),x_=function(){function e(t){Pn(this,e),this.type=so.MSG_CUSTOM,this.content={data:t.data||"",description:t.description||"",extension:t.extension||""}}return Un(e,[{key:"setData",value:function(e){return this.content.data=e,this}},{key:"setDescription",value:function(e){return this.content.description=e,this}},{key:"setExtension",value:function(e){return this.content.extension=e,this}},{key:"sendable",value:function(){return 0!==this.content.data.length||0!==this.content.description.length||0!==this.content.extension.length}}]),e}(),V_=function(){function e(t){Pn(this,e),this.type=so.MSG_VIDEO,this._percent=0,this.content={remoteVideoUrl:t.remoteVideoUrl||t.videoUrl||"",videoFormat:t.videoFormat,videoSecond:parseInt(t.videoSecond,10),videoSize:t.videoSize,videoUrl:t.videoUrl,videoDownloadFlag:2,videoUUID:t.videoUUID,thumbUUID:t.thumbUUID,thumbFormat:t.thumbFormat,thumbWidth:t.thumbWidth,thumbHeight:t.thumbHeight,thumbSize:t.thumbSize,thumbDownloadFlag:2,thumbUrl:t.thumbUrl}}return Un(e,[{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateVideoUrl",value:function(e){e&&(this.content.remoteVideoUrl=e)}},{key:"sendable",value:function(){return""!==this.content.remoteVideoUrl}}]),e}(),K_=function(){function e(t){Pn(this,e),this.type=so.MSG_LOCATION;var n=t.description,o=t.longitude,r=t.latitude;this.content={description:n,longitude:o,latitude:r}}return Un(e,[{key:"sendable",value:function(){return!0}}]),e}(),B_=function(){function e(t){if(Pn(this,e),this.from=t.from,this.messageSender=t.from,this.time=t.time,this.messageSequence=t.sequence,this.clientSequence=t.clientSequence||t.sequence,this.messageRandom=t.random,this.cloudCustomData=t.cloudCustomData||"",t.ID)this.nick=t.nick||"",this.avatar=t.avatar||"",this.messageBody=[{type:t.type,payload:t.payload}],t.conversationType.startsWith(so.CONV_C2C)?this.receiverUserID=t.to:t.conversationType.startsWith(so.CONV_GROUP)&&(this.receiverGroupID=t.to),this.messageReceiver=t.to;else{this.nick=t.nick||"",this.avatar=t.avatar||"",this.messageBody=[];var n=t.elements[0].type,o=t.elements[0].content;this._patchRichMediaPayload(n,o),n===so.MSG_MERGER?this.messageBody.push({type:n,payload:new H_(o).content}):this.messageBody.push({type:n,payload:o}),t.groupID&&(this.receiverGroupID=t.groupID,this.messageReceiver=t.groupID),t.to&&(this.receiverUserID=t.to,this.messageReceiver=t.to)}}return Un(e,[{key:"_patchRichMediaPayload",value:function(e,t){e===so.MSG_IMAGE?t.imageInfoArray.forEach((function(e){!e.imageUrl&&e.url&&(e.imageUrl=e.url,e.sizeType=e.type,1===e.type?e.type=0:3===e.type&&(e.type=1))})):e===so.MSG_VIDEO?!t.remoteVideoUrl&&t.videoUrl&&(t.remoteVideoUrl=t.videoUrl):e===so.MSG_AUDIO?!t.remoteAudioUrl&&t.url&&(t.remoteAudioUrl=t.url):e===so.MSG_FILE&&!t.fileUrl&&t.url&&(t.fileUrl=t.url,t.url=void 0)}}]),e}(),H_=function(){function e(t){if(Pn(this,e),this.type=so.MSG_MERGER,this.content={downloadKey:"",pbDownloadKey:"",messageList:[],title:"",abstractList:[],compatibleText:"",version:0,layersOverLimit:!1},t.downloadKey){var n=t.downloadKey,o=t.pbDownloadKey,r=t.title,a=t.abstractList,s=t.compatibleText,i=t.version;this.content.downloadKey=n,this.content.pbDownloadKey=o,this.content.title=r,this.content.abstractList=a,this.content.compatibleText=s,this.content.version=i||0}else if(Au(t.messageList))1===t.layersOverLimit&&(this.content.layersOverLimit=!0);else{var u=t.messageList,c=t.title,l=t.abstractList,d=t.compatibleText,p=t.version,g=[];u.forEach((function(e){if(!Au(e)){var t=new B_(e);g.push(t)}})),this.content.messageList=g,this.content.title=c,this.content.abstractList=l,this.content.compatibleText=d,this.content.version=p||0}xi.debug("MergerElement.content:",this.content)}return Un(e,[{key:"sendable",value:function(){return!Au(this.content.messageList)||!Au(this.content.downloadKey)}}]),e}(),j_={1:so.MSG_PRIORITY_HIGH,2:so.MSG_PRIORITY_NORMAL,3:so.MSG_PRIORITY_LOW,4:so.MSG_PRIORITY_LOWEST},Y_=function(){function e(t){Pn(this,e),this.ID="",this.conversationID=t.conversationID||null,this.conversationType=t.conversationType||so.CONV_C2C,this.conversationSubType=t.conversationSubType,this.time=t.time||Math.ceil(Date.now()/1e3),this.sequence=t.sequence||0,this.clientSequence=t.clientSequence||t.sequence||0,this.random=t.random||0===t.random?t.random:ru(),this.priority=this._computePriority(t.priority),this.nick=t.nick||"",this.avatar=t.avatar||"",this.isPeerRead=!1,this.nameCard="",this._elements=[],this.isPlaceMessage=t.isPlaceMessage||0,this.isRevoked=2===t.isPlaceMessage||8===t.msgFlagBits,this.from=t.from||null,this.to=t.to||null,this.flow="",this.isSystemMessage=t.isSystemMessage||!1,this.protocol=t.protocol||"JSON",this.isResend=!1,this.isRead=!1,this.status=t.status||Tc.SUCCESS,this._onlineOnlyFlag=!1,this._groupAtInfoList=[],this._relayFlag=!1,this.atUserList=[],this.cloudCustomData=t.cloudCustomData||"",this.isDeleted=!1,this.isModified=!1,this.reInitialize(t.currentUser),this.extractGroupInfo(t.groupProfile||null),this.handleGroupAtInfo(t)}return Un(e,[{key:"getElements",value:function(){return this._elements}},{key:"extractGroupInfo",value:function(e){if(null!==e){Bi(e.nick)&&(this.nick=e.nick),Bi(e.avatar)&&(this.avatar=e.avatar);var t=e.messageFromAccountExtraInformation;ji(t)&&Bi(t.nameCard)&&(this.nameCard=t.nameCard)}}},{key:"handleGroupAtInfo",value:function(e){var t=this;e.payload&&e.payload.atUserList&&e.payload.atUserList.forEach((function(e){e!==so.MSG_AT_ALL?(t._groupAtInfoList.push({groupAtAllFlag:0,groupAtUserID:e}),t.atUserList.push(e)):(t._groupAtInfoList.push({groupAtAllFlag:1}),t.atUserList.push(so.MSG_AT_ALL))})),Yi(e.groupAtInfo)&&e.groupAtInfo.forEach((function(e){1===e.groupAtAllFlag?t.atUserList.push(e.groupAtUserID):2===e.groupAtAllFlag&&t.atUserList.push(so.MSG_AT_ALL)}))}},{key:"getGroupAtInfoList",value:function(){return this._groupAtInfoList}},{key:"_initProxy",value:function(){this._elements[0]&&(this.payload=this._elements[0].content,this.type=this._elements[0].type)}},{key:"reInitialize",value:function(e){e&&(this.status=this.from?Tc.SUCCESS:Tc.UNSEND,!this.from&&(this.from=e)),this._initFlow(e),this._initSequence(e),this._concatConversationID(e),this.generateMessageID(e)}},{key:"isSendable",value:function(){return 0!==this._elements.length&&("function"!=typeof this._elements[0].sendable?(xi.warn("".concat(this._elements[0].type,' need "boolean : sendable()" method')),!1):this._elements[0].sendable())}},{key:"_initTo",value:function(e){this.conversationType===so.CONV_GROUP&&(this.to=e.groupID)}},{key:"_initSequence",value:function(e){0===this.clientSequence&&e&&(this.clientSequence=function(e){if(!e)return xi.error("autoIncrementIndex(string: key) need key parameter"),!1;if(void 0===uu[e]){var t=new Date,n="3".concat(t.getHours()).slice(-2),o="0".concat(t.getMinutes()).slice(-2),r="0".concat(t.getSeconds()).slice(-2);uu[e]=parseInt([n,o,r,"0001"].join("")),n=null,o=null,r=null,xi.log("autoIncrementIndex start index:".concat(uu[e]))}return uu[e]++}(e)),0===this.sequence&&this.conversationType===so.CONV_C2C&&(this.sequence=this.clientSequence)}},{key:"generateMessageID",value:function(e){var t=e===this.from?1:0,n=this.sequence>0?this.sequence:this.clientSequence;this.ID="".concat(this.conversationID,"-").concat(n,"-").concat(this.random,"-").concat(t)}},{key:"_initFlow",value:function(e){""!==e&&(e===this.from?(this.flow="out",this.isRead=!0):this.flow="in")}},{key:"_concatConversationID",value:function(e){var t=this.to,n="",o=this.conversationType;o!==so.CONV_SYSTEM?(n=o===so.CONV_C2C?e===this.from?t:this.from:this.to,this.conversationID="".concat(o).concat(n)):this.conversationID=so.CONV_SYSTEM}},{key:"isElement",value:function(e){return e instanceof gh||e instanceof k_||e instanceof C_||e instanceof A_||e instanceof q_||e instanceof V_||e instanceof O_||e instanceof R_||e instanceof x_||e instanceof K_||e instanceof H_}},{key:"setElement",value:function(e){var t=this;if(this.isElement(e))return this._elements=[e],void this._initProxy();var n=function(e){if(e.type&&e.content)switch(e.type){case so.MSG_TEXT:t.setTextElement(e.content);break;case so.MSG_IMAGE:t.setImageElement(e.content);break;case so.MSG_AUDIO:t.setAudioElement(e.content);break;case so.MSG_FILE:t.setFileElement(e.content);break;case so.MSG_VIDEO:t.setVideoElement(e.content);break;case so.MSG_CUSTOM:t.setCustomElement(e.content);break;case so.MSG_LOCATION:t.setLocationElement(e.content);break;case so.MSG_GRP_TIP:t.setGroupTipElement(e.content);break;case so.MSG_GRP_SYS_NOTICE:t.setGroupSystemNoticeElement(e.content);break;case so.MSG_FACE:t.setFaceElement(e.content);break;case so.MSG_MERGER:t.setMergerElement(e.content);break;default:xi.warn(e.type,e.content,"no operation......")}};if(Yi(e))for(var o=0;o<e.length;o++)n(e[o]);else n(e);this._initProxy()}},{key:"clearElement",value:function(){this._elements.length=0}},{key:"setTextElement",value:function(e){var t="string"==typeof e?e:e.text,n=new gh({text:t});this._elements.push(n)}},{key:"setImageElement",value:function(e){var t=new k_(e);this._elements.push(t)}},{key:"setAudioElement",value:function(e){var t=new A_(e);this._elements.push(t)}},{key:"setFileElement",value:function(e){var t=new q_(e);this._elements.push(t)}},{key:"setVideoElement",value:function(e){var t=new V_(e);this._elements.push(t)}},{key:"setLocationElement",value:function(e){var t=new K_(e);this._elements.push(t)}},{key:"setCustomElement",value:function(e){var t=new x_(e);this._elements.push(t)}},{key:"setGroupTipElement",value:function(e){var t={},n=e.operationType;Au(e.memberInfoList)?e.operatorInfo&&(t=e.operatorInfo):n!==so.GRP_TIP_MBR_JOIN&&n!==so.GRP_TIP_MBR_KICKED_OUT&&n!==so.GRP_TIP_MBR_SET_ADMIN&&n!==so.GRP_TIP_MBR_CANCELED_ADMIN||(t=e.memberInfoList[0]);var o=t,r=o.nick,a=o.avatar;Bi(r)&&(this.nick=r),Bi(a)&&(this.avatar=a);var s=new O_(e);this._elements.push(s)}},{key:"setGroupSystemNoticeElement",value:function(e){var t=new R_(e);this._elements.push(t)}},{key:"setFaceElement",value:function(e){var t=new C_(e);this._elements.push(t)}},{key:"setMergerElement",value:function(e){var t=new H_(e);this._elements.push(t)}},{key:"setIsRead",value:function(e){this.isRead=e}},{key:"setRelayFlag",value:function(e){this._relayFlag=e}},{key:"getRelayFlag",value:function(){return this._relayFlag}},{key:"setOnlineOnlyFlag",value:function(e){this._onlineOnlyFlag=e}},{key:"getOnlineOnlyFlag",value:function(){return this._onlineOnlyFlag}},{key:"_computePriority",value:function(e){if(Wi(e))return so.MSG_PRIORITY_NORMAL;if(Bi(e)&&-1!==Object.values(j_).indexOf(e))return e;if(Ki(e)){var t=""+e;if(-1!==Object.keys(j_).indexOf(t))return j_[t]}return so.MSG_PRIORITY_NORMAL}},{key:"setNickAndAvatar",value:function(e){var t=e.nick,n=e.avatar;Bi(t)&&(this.nick=t),Bi(n)&&(this.avatar=n)}},{key:"setNameCard",value:function(e){Bi(e)&&(this.nameCard=e)}},{key:"elements",get:function(){return xi.warn("！！！Message 实例的 elements 属性即将废弃，请尽快修改。使用 type 和 payload 属性处理单条消息，兼容组合消息使用 _elements 属性！！！"),this._elements}}]),e}(),W_=function(e){return{code:0,data:e||{}}},z_="https://cloud.tencent.com/document/product/",J_="您可以在即时通信 IM 控制台的【开发辅助工具(https://console.cloud.tencent.com/im-detail/tool-usersig)】页面校验 UserSig。",X_="UserSig 非法，请使用官网提供的 API 重新生成 UserSig(".concat(z_,"269/32688)。"),Q_="#.E6.B6.88.E6.81.AF.E5.85.83.E7.B4.A0-timmsgelement",Z_={70001:"UserSig 已过期，请重新生成。建议 UserSig 有效期设置不小于24小时。",70002:"UserSig 长度为0，请检查传入的 UserSig 是否正确。",70003:X_,70005:X_,70009:"UserSig 验证失败，可能因为生成 UserSig 时混用了其他 SDKAppID 的私钥或密钥导致，请使用对应 SDKAppID 下的私钥或密钥重新生成 UserSig(".concat(z_,"269/32688)。"),70013:"请求中的 UserID 与生成 UserSig 时使用的 UserID 不匹配。".concat(J_),70014:"请求中的 SDKAppID 与生成 UserSig 时使用的 SDKAppID 不匹配。".concat(J_),70016:"密钥不存在，UserSig 验证失败，请在即时通信 IM 控制台获取密钥(".concat(z_,"269/32578#.E8.8E.B7.E5.8F.96.E5.AF.86.E9.92.A5)。"),70020:"SDKAppID 未找到，请在即时通信 IM 控制台确认应用信息。",70050:"UserSig 验证次数过于频繁。请检查 UserSig 是否正确，并于1分钟后重新验证。".concat(J_),70051:"帐号被拉入黑名单。",70052:"UserSig 已经失效，请重新生成，再次尝试。",70107:"因安全原因被限制登录，请不要频繁登录。",70169:"请求的用户帐号不存在。",70114:"".concat("服务端内部超时，请稍后重试。"),70202:"".concat("服务端内部超时，请稍后重试。"),70206:"请求中批量数量不合法。",70402:"参数非法，请检查必填字段是否填充，或者字段的填充是否满足协议要求。",70403:"请求失败，需要 App 管理员权限。",70398:"帐号数超限。如需创建多于100个帐号，请将应用升级为专业版，具体操作指引请参见购买指引(".concat(z_,"269/32458)。"),70500:"".concat("服务端内部错误，请重试。"),71e3:"删除帐号失败。仅支持删除体验版帐号，您当前应用为专业版，暂不支持帐号删除。",20001:"请求包非法。",20002:"UserSig 或 A2 失效。",20003:"消息发送方或接收方 UserID 无效或不存在，请检查 UserID 是否已导入即时通信 IM。",20004:"网络异常，请重试。",20005:"".concat("服务端内部错误，请重试。"),20006:"触发发送".concat("单聊消息","之前回调，App 后台返回禁止下发该消息。"),20007:"发送".concat("单聊消息","，被对方拉黑，禁止发送。消息发送状态默认展示为失败，您可以登录控制台修改该场景下的消息发送状态展示结果，具体操作请参见消息保留设置(").concat(z_,"269/38656)。"),20009:"消息发送双方互相不是好友，禁止发送（配置".concat("单聊消息","校验好友关系才会出现）。"),20010:"发送".concat("单聊消息","，自己不是对方的好友（单向关系），禁止发送。"),20011:"发送".concat("单聊消息","，对方不是自己的好友（单向关系），禁止发送。"),20012:"发送方被禁言，该条消息被禁止发送。",20016:"消息撤回超过了时间限制（默认2分钟）。",20018:"删除漫游内部错误。",90001:"JSON 格式解析失败，请检查请求包是否符合 JSON 规范。",90002:"".concat("JSON 格式请求包体","中 MsgBody 不符合消息格式描述，或者 MsgBody 不是 Array 类型，请参考 TIMMsgElement 对象的定义(").concat(z_,"269/2720").concat(Q_,")。"),90003:"".concat("JSON 格式请求包体","中缺少 To_Account 字段或者 To_Account 帐号不存在。"),90005:"".concat("JSON 格式请求包体","中缺少 MsgRandom 字段或者 MsgRandom 字段不是 Integer 类型。"),90006:"".concat("JSON 格式请求包体","中缺少 MsgTimeStamp 字段或者 MsgTimeStamp 字段不是 Integer 类型。"),90007:"".concat("JSON 格式请求包体","中 MsgBody 类型不是 Array 类型，请将其修改为 Array 类型。"),90008:"".concat("JSON 格式请求包体","中缺少 From_Account 字段或者 From_Account 帐号不存在。"),90009:"请求需要 App 管理员权限。",90010:"".concat("JSON 格式请求包体","不符合消息格式描述，请参考 TIMMsgElement 对象的定义(").concat(z_,"269/2720").concat(Q_,")。"),90011:"批量发消息目标帐号超过500，请减少 To_Account 中目标帐号数量。",90012:"To_Account 没有注册或不存在，请确认 To_Account 是否导入即时通信 IM 或者是否拼写错误。",90026:"消息离线存储时间错误（最多不能超过7天）。",90031:"".concat("JSON 格式请求包体","中 SyncOtherMachine 字段不是 Integer 类型。"),90044:"".concat("JSON 格式请求包体","中 MsgLifeTime 字段不是 Integer 类型。"),90048:"请求的用户帐号不存在。",90054:"撤回请求中的 MsgKey 不合法。",90994:"".concat("服务端内部错误，请重试。"),90995:"".concat("服务端内部错误，请重试。"),91e3:"".concat("服务端内部错误，请重试。"),90992:"".concat("服务端内部错误，请重试。","如果所有请求都返回该错误码，且 App 配置了第三方回调，请检查 App 服务端是否正常向即时通信 IM 后台服务端返回回调结果。"),93e3:"JSON 数据包超长，消息包体请不要超过8k。",91101:"Web 端长轮询被踢（Web 端同时在线实例个数超出限制）。",10002:"".concat("服务端内部错误，请重试。"),10003:"请求中的接口名称错误，请核对接口名称并重试。",10004:"参数非法，请根据错误描述检查请求是否正确。",10005:"请求包体中携带的帐号数量过多。",10006:"操作频率限制，请尝试降低调用的频率。",10007:"操作权限不足，例如 Work ".concat("群组","中普通成员尝试执行踢人操作，但只有 App 管理员才有权限。"),10008:"请求非法，可能是请求中携带的签名信息验证不正确，请再次尝试。",10009:"该群不允许群主主动退出。",10010:"".concat("群组","不存在，或者曾经存在过，但是目前已经被解散。"),10011:"解析 JSON 包体失败，请检查包体的格式是否符合 JSON 格式。",10012:"发起操作的 UserID 非法，请检查发起操作的用户 UserID 是否填写正确。",10013:"被邀请加入的用户已经是群成员。",10014:"群已满员，无法将请求中的用户加入".concat("群组","，如果是批量加人，可以尝试减少加入用户的数量。"),10015:"找不到指定 ID 的".concat("群组","。"),10016:"App 后台通过第三方回调拒绝本次操作。",10017:"因被禁言而不能发送消息，请检查发送者是否被设置禁言。",10018:"应答包长度超过最大包长（1MB），请求的内容过多，请尝试减少单次请求的数据量。",10019:"请求的用户帐号不存在。",10021:"".concat("群组"," ID 已被使用，请选择其他的").concat("群组"," ID。"),10023:"发消息的频率超限，请延长两次发消息时间的间隔。",10024:"此邀请或者申请请求已经被处理。",10025:"".concat("群组"," ID 已被使用，并且操作者为群主，可以直接使用。"),10026:"该 SDKAppID 请求的命令字已被禁用。",10030:"请求撤回的消息不存在。",10031:"消息撤回超过了时间限制（默认2分钟）。",10032:"请求撤回的消息不支持撤回操作。",10033:"".concat("群组","类型不支持消息撤回操作。"),10034:"该消息类型不支持删除操作。",10035:"直播群和在线成员广播大群不支持删除消息。",10036:"直播群创建数量超过了限制，请参考价格说明(".concat(z_,"269/11673)购买预付费套餐“IM直播群”。"),10037:"单个用户可创建和加入的".concat("群组","数量超过了限制，请参考价格说明(").concat(z_,"269/11673)购买或升级预付费套餐“单人可创建与加入").concat("群组","数”。"),10038:"群成员数量超过限制，请参考价格说明(".concat(z_,"269/11673)购买或升级预付费套餐“扩展群人数上限”。"),10041:"该应用（SDKAppID）已配置不支持群消息撤回。",10050:"群属性 key 不存在",10056:"请在写入群属性前先使用 getGroupAttributes 接口更新本地群属性，避免冲突。",30001:"请求参数错误，请根据错误描述检查请求参数",30002:"SDKAppID 不匹配",30003:"请求的用户帐号不存在",30004:"请求需要 App 管理员权限",30005:"关系链字段中包含敏感词",30006:"".concat("服务端内部错误，请重试。"),30007:"".concat("网络超时，请稍后重试. "),30008:"并发写导致写冲突，建议使用批量方式",30009:"后台禁止该用户发起加好友请求",30010:"自己的好友数已达系统上限",30011:"分组已达系统上限",30012:"未决数已达系统上限",30014:"对方的好友数已达系统上限",30515:"请求添加好友时，对方在自己的黑名单中，不允许加好友",30516:"请求添加好友时，对方的加好友验证方式是不允许任何人添加自己为好友",30525:"请求添加好友时，自己在对方的黑名单中，不允许加好友",30539:"等待对方同意",30540:"添加好友请求被安全策略打击，请勿频繁发起添加好友请求",31704:"与请求删除的帐号之间不存在好友关系",31707:"删除好友请求被安全策略打击，请勿频繁发起删除好友请求"},$_=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this)).code=e.code,o.message=Z_[e.code]||e.message,o.data=e.data||{},o}return n}(Yn(Error)),em=null,tm=function(e){em=e},nm=function(e){return Promise.resolve(W_(e))},om=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e instanceof $_)return t&&null!==em&&em.emit(ao.ERROR,e),Promise.reject(e);if(e instanceof Error){var n=new $_({code:vd.UNCAUGHT_ERROR,message:e.message});return t&&null!==em&&em.emit(ao.ERROR,n),Promise.reject(n)}if(Wi(e)||Wi(e.code)||Wi(e.message))xi.error("IMPromise.reject 必须指定code(错误码)和message(错误信息)!!!");else{if(Ki(e.code)&&Bi(e.message)){var o=new $_(e);return t&&null!==em&&em.emit(ao.ERROR,o),Promise.reject(o)}xi.error("IMPromise.reject code(错误码)必须为数字，message(错误信息)必须为字符串!!!")}},rm=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="C2CModule",o}return Un(n,[{key:"onNewC2CMessage",value:function(e){var t=e.dataList,n=e.isInstantMessage,o=e.C2CRemainingUnreadList;xi.debug("".concat(this._className,".onNewC2CMessage count:").concat(t.length," isInstantMessage:").concat(n));var r=this._newC2CMessageStoredAndSummary({dataList:t,C2CRemainingUnreadList:o,isInstantMessage:n}),a=r.conversationOptionsList,s=r.messageList;this.filterModifiedMessage(s),a.length>0&&this.getModule(jc).onNewMessage({conversationOptionsList:a,isInstantMessage:n});var i=this.filterUnmodifiedMessage(s);n&&i.length>0&&this.emitOuterEvent(ao.MESSAGE_RECEIVED,i),s.length=0}},{key:"_newC2CMessageStoredAndSummary",value:function(e){for(var t=e.dataList,n=e.C2CRemainingUnreadList,o=e.isInstantMessage,r=null,a=[],s=[],i={},u=this.getModule(Xc),c=0,l=t.length;c<l;c++){var d=t[c];d.currentUser=this.getMyUserID(),d.conversationType=so.CONV_C2C,d.isSystemMessage=!!d.isSystemMessage,r=new Y_(d),d.elements=u.parseElements(d.elements,d.from),r.setElement(d.elements),r.setNickAndAvatar({nick:d.nick,avatar:d.avatar});var p=r.conversationID;if(o){var g=!1,h=this.getModule(jc);if(r.from!==this.getMyUserID()){var f=h.getLatestMessageSentByPeer(p);if(f){var _=f.nick,m=f.avatar;_===r.nick&&m===r.avatar||(g=!0)}}else{var v=h.getLatestMessageSentByMe(p);if(v){var M=v.nick,y=v.avatar;M===r.nick&&y===r.avatar||h.modifyMessageSentByMe({conversationID:p,latestNick:r.nick,latestAvatar:r.avatar})}}var I=1===t[c].isModified;if(h.isMessageSentByCurrentInstance(r)?r.isModified=I:I=!1,0===d.msgLifeTime)r.setOnlineOnlyFlag(!0),s.push(r);else{if(!h.pushIntoMessageList(s,r,I))continue;g&&(h.modifyMessageSentByPeer(p),h.updateUserProfileSpecifiedKey({conversationID:p,nick:r.nick,avatar:r.avatar}))}this.getModule(sl).addMessageDelay({currentTime:Date.now(),time:r.time})}if(0!==d.msgLifeTime){if(!1===r.getOnlineOnlyFlag())if(Wi(i[p]))i[p]=a.push({conversationID:p,unreadCount:"out"===r.flow?0:1,type:r.conversationType,subType:r.conversationSubType,lastMessage:r})-1;else{var T=i[p];a[T].type=r.conversationType,a[T].subType=r.conversationSubType,a[T].lastMessage=r,"in"===r.flow&&a[T].unreadCount++}}else r.setOnlineOnlyFlag(!0)}if(Yi(n))for(var S=function(e,t){var o=a.find((function(t){return t.conversationID==="C2C".concat(n[e].from)}));o?o.unreadCount+=n[e].count:a.push({conversationID:"C2C".concat(n[e].from),unreadCount:n[e].count,type:so.CONV_C2C,lastMsgTime:n[e].lastMsgTime})},E=0,D=n.length;E<D;E++)S(E);return{conversationOptionsList:a,messageList:s}}},{key:"onC2CMessageRevoked",value:function(e){var t=this;xi.debug("".concat(this._className,".onC2CMessageRevoked count:").concat(e.dataList.length));var n=this.getModule(jc),o=[],r=null;e.dataList.forEach((function(e){if(e.c2cMessageRevokedNotify){var a=e.c2cMessageRevokedNotify.revokedInfos;Wi(a)||a.forEach((function(e){var a=t.getMyUserID()===e.from?"".concat(so.CONV_C2C).concat(e.to):"".concat(so.CONV_C2C).concat(e.from);(r=n.revoke(a,e.sequence,e.random))&&o.push(r)}))}})),0!==o.length&&(n.onMessageRevoked(o),this.emitOuterEvent(ao.MESSAGE_REVOKED,o))}},{key:"onC2CMessageReadReceipt",value:function(e){var t=this;e.dataList.forEach((function(e){if(!Au(e.c2cMessageReadReceipt)){var n=e.c2cMessageReadReceipt.to;e.c2cMessageReadReceipt.uinPairReadArray.forEach((function(e){var o=e.peerReadTime;xi.debug("".concat(t._className,"._onC2CMessageReadReceipt to:").concat(n," peerReadTime:").concat(o));var r="".concat(so.CONV_C2C).concat(n),a=t.getModule(jc);a.recordPeerReadTime(r,o),a.updateMessageIsPeerReadProperty(r,o)}))}}))}},{key:"onC2CMessageReadNotice",value:function(e){var t=this;e.dataList.forEach((function(e){if(!Au(e.c2cMessageReadNotice)){var n=t.getModule(jc);e.c2cMessageReadNotice.uinPairReadArray.forEach((function(e){var o=e.from,r=e.peerReadTime;xi.debug("".concat(t._className,".onC2CMessageReadNotice from:").concat(o," lastReadTime:").concat(r));var a="".concat(so.CONV_C2C).concat(o);n.updateIsReadAfterReadReport({conversationID:a,lastMessageTime:r}),n.updateUnreadCount(a)}))}}))}},{key:"sendMessage",value:function(e,t){var n=this._createC2CMessagePack(e,t);return this.request(n)}},{key:"_createC2CMessagePack",value:function(e,t){var n=null;t&&(t.offlinePushInfo&&(n=t.offlinePushInfo),!0===t.onlineUserOnly&&(n?n.disablePush=!0:n={disablePush:!0}));var o="";return Bi(e.cloudCustomData)&&e.cloudCustomData.length>0&&(o=e.cloudCustomData),{protocolName:gl,tjgID:this.generateTjgID(e),requestData:{fromAccount:this.getMyUserID(),toAccount:e.to,msgTimeStamp:Math.ceil(Date.now()/1e3),msgBody:e.getElements(),cloudCustomData:o,msgSeq:e.sequence,msgRandom:e.random,msgLifeTime:this.isOnlineMessage(e,t)?0:void 0,nick:e.nick,avatar:e.avatar,offlinePushInfo:n?{pushFlag:!0===n.disablePush?1:0,title:n.title||"",desc:n.description||"",ext:n.extension||"",apnsInfo:{badgeMode:!0===n.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:n.androidOPPOChannelID||""}}:void 0}}}},{key:"isOnlineMessage",value:function(e,t){return!(!t||!0!==t.onlineUserOnly)}},{key:"revokeMessage",value:function(e){return this.request({protocolName:yl,requestData:{msgInfo:{fromAccount:e.from,toAccount:e.to,msgSeq:e.sequence,msgRandom:e.random,msgTimeStamp:e.time}}})}},{key:"deleteMessage",value:function(e){var t=e.to,n=e.keyList;return xi.log("".concat(this._className,".deleteMessage toAccount:").concat(t," count:").concat(n.length)),this.request({protocolName:El,requestData:{fromAccount:this.getMyUserID(),to:t,keyList:n}})}},{key:"setMessageRead",value:function(e){var t=this,n=e.conversationID,o=e.lastMessageTime,r="".concat(this._className,".setMessageRead");xi.log("".concat(r," conversationID:").concat(n," lastMessageTime:").concat(o)),Ki(o)||xi.warn("".concat(r," 请勿修改 Conversation.lastMessage.lastTime，否则可能会导致已读上报结果不准确"));var a=new Kp(ig);return a.setMessage("conversationID:".concat(n," lastMessageTime:").concat(o)),this.request({protocolName:Il,requestData:{C2CMsgReaded:{cookie:"",C2CMsgReadedItem:[{toAccount:n.replace("C2C",""),lastMessageTime:o,receipt:1}]}}}).then((function(){a.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(r," ok"));var e=t.getModule(jc);return e.updateIsReadAfterReadReport({conversationID:n,lastMessageTime:o}),e.updateUnreadCount(n),W_()})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),xi.log("".concat(r," failed. error:"),e),om(e)}))}},{key:"getRoamingMessage",value:function(e){var t=this,n="".concat(this._className,".getRoamingMessage"),o=e.peerAccount,r=e.conversationID,a=e.count,s=e.lastMessageTime,i=e.messageKey,u="peerAccount:".concat(o," count:").concat(a||15," lastMessageTime:").concat(s||0," messageKey:").concat(i);xi.log("".concat(n," ").concat(u));var c=new Kp(og);return this.request({protocolName:Tl,requestData:{peerAccount:o,count:a||15,lastMessageTime:s||0,messageKey:i}}).then((function(e){var o=e.data,a=o.complete,s=o.messageList,i=o.messageKey,l=o.lastMessageTime;Wi(s)?xi.log("".concat(n," ok. complete:").concat(a," but messageList is undefined!")):xi.log("".concat(n," ok. complete:").concat(a," count:").concat(s.length)),c.setNetworkType(t.getNetworkType()).setMessage("".concat(u," complete:").concat(a," length:").concat(s.length)).end();var d=t.getModule(jc);1===a&&d.setCompleted(r);var p=d.storeRoamingMessage(s,r);d.modifyMessageList(r),d.updateIsRead(r),d.updateRoamingMessageKeyAndTime(r,i,l);var g=d.getPeerReadTime(r);if(xi.log("".concat(n," update isPeerRead property. conversationID:").concat(r," peerReadTime:").concat(g)),g)d.updateMessageIsPeerReadProperty(r,g);else{var h=r.replace(so.CONV_C2C,"");t.getRemotePeerReadTime([h]).then((function(){d.updateMessageIsPeerReadProperty(r,d.getPeerReadTime(r))}))}return p})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];c.setMessage(u).setError(e,o,r).end()})),xi.warn("".concat(n," failed. error:"),e),om(e)}))}},{key:"getRemotePeerReadTime",value:function(e){var t=this,n="".concat(this._className,".getRemotePeerReadTime");if(Au(e))return xi.warn("".concat(n," userIDList is empty!")),Promise.resolve();var o=new Kp(lg);return xi.log("".concat(n," userIDList:").concat(e)),this.request({protocolName:Sl,requestData:{userIDList:e}}).then((function(r){var a=r.data.peerReadTimeList;xi.log("".concat(n," ok. peerReadTimeList:").concat(a));for(var s="",i=t.getModule(jc),u=0;u<e.length;u++)s+="".concat(e[u],"-").concat(a[u]," "),a[u]>0&&i.recordPeerReadTime("C2C".concat(e[u]),a[u]);o.setNetworkType(t.getNetworkType()).setMessage(s).end()})).catch((function(e){t.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),xi.warn("".concat(n," failed. error:"),e)}))}}]),n}(il),am=it.findIndex,sm=!0,im=pt("findIndex");"findIndex"in[]&&Array(1).findIndex((function(){sm=!1})),be({target:"Array",proto:!0,forced:sm||!im},{findIndex:function(e){return am(this,e,arguments.length>1?arguments[1]:void 0)}}),So("findIndex");var um=[],cm=um.sort,lm=a((function(){um.sort(void 0)})),dm=a((function(){um.sort(null)})),pm=ut("sort");be({target:"Array",proto:!0,forced:lm||!dm||!pm},{sort:function(e){return void 0===e?cm.call(Pe(this)):cm.call(Pe(this),ot(e))}});var gm=function(){function e(t){Pn(this,e),this.list=new Map,this._className="MessageListHandler",this._latestMessageSentByPeerMap=new Map,this._latestMessageSentByMeMap=new Map,this._groupLocalLastMessageSequenceMap=new Map}return Un(e,[{key:"getLocalOldestMessageByConversationID",value:function(e){if(!e)return null;if(!this.list.has(e))return null;var t=this.list.get(e).values();return t?t.next().value:null}},{key:"pushIn",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.conversationID,o=e.ID,r=!0;this.list.has(n)||this.list.set(n,new Map);var a=this.list.get(n).has(o);if(a){var s=this.list.get(n).get(o);if(!t||!0===s.isModified)return!1}return this.list.get(n).set(o,e),this._setLatestMessageSentByPeer(n,e),this._setLatestMessageSentByMe(n,e),this._setGroupLocalLastMessageSequence(n,e),r}},{key:"unshift",value:function(e){var t;if(Yi(e)){if(e.length>0){t=e[0].conversationID;var n=e.length;this._unshiftMultipleMessages(e),this._setGroupLocalLastMessageSequence(t,e[n-1])}}else t=e.conversationID,this._unshiftSingleMessage(e),this._setGroupLocalLastMessageSequence(t,e);if(t&&t.startsWith(so.CONV_C2C)){var o=Array.from(this.list.get(t).values()),r=o.length;if(0===r)return;for(var a=r-1;a>=0;a--)if("out"===o[a].flow){this._setLatestMessageSentByMe(t,o[a]);break}for(var s=r-1;s>=0;s--)if("in"===o[s].flow){this._setLatestMessageSentByPeer(t,o[s]);break}}}},{key:"_unshiftSingleMessage",value:function(e){var t=e.conversationID,n=e.ID;if(!this.list.has(t))return this.list.set(t,new Map),void this.list.get(t).set(n,e);var o=Array.from(this.list.get(t));o.unshift([n,e]),this.list.set(t,new Map(o))}},{key:"_unshiftMultipleMessages",value:function(e){for(var t=e.length,n=[],o=e[0].conversationID,r=this.list.has(o)?Array.from(this.list.get(o)):[],a=0;a<t;a++)n.push([e[a].ID,e[a]]);this.list.set(o,new Map(n.concat(r)))}},{key:"remove",value:function(e){var t=e.conversationID,n=e.ID;this.list.has(t)&&this.list.get(t).delete(n)}},{key:"revoke",value:function(e,t,n){if(xi.debug("revoke message",e,t,n),this.list.has(e)){var o,r=ro(this.list.get(e));try{for(r.s();!(o=r.n()).done;){var a=Qn(o.value,2)[1];if(a.sequence===t&&!a.isRevoked&&(Wi(n)||a.random===n))return a.isRevoked=!0,a}}catch(u){r.e(u)}finally{r.f()}}return null}},{key:"removeByConversationID",value:function(e){this.list.has(e)&&(this.list.delete(e),this._latestMessageSentByPeerMap.delete(e),this._latestMessageSentByMeMap.delete(e))}},{key:"updateMessageIsPeerReadProperty",value:function(e,t){var n=[];if(this.list.has(e)){var o,r=ro(this.list.get(e));try{for(r.s();!(o=r.n()).done;){var a=Qn(o.value,2)[1];a.time<=t&&!a.isPeerRead&&"out"===a.flow&&(a.isPeerRead=!0,n.push(a))}}catch(u){r.e(u)}finally{r.f()}xi.log("".concat(this._className,".updateMessageIsPeerReadProperty conversationID:").concat(e," peerReadTime:").concat(t," count:").concat(n.length))}return n}},{key:"updateMessageIsModifiedProperty",value:function(e){var t=e.conversationID,n=e.ID;if(this.list.has(t)){var o=this.list.get(t).get(n);o&&(o.isModified=!0)}}},{key:"hasLocalMessageList",value:function(e){return this.list.has(e)}},{key:"getLocalMessageList",value:function(e){return this.hasLocalMessageList(e)?Zn(this.list.get(e).values()):[]}},{key:"hasLocalMessage",value:function(e,t){return!!this.hasLocalMessageList(e)&&this.list.get(e).has(t)}},{key:"getLocalMessage",value:function(e,t){return this.hasLocalMessage(e,t)?this.list.get(e).get(t):null}},{key:"_setLatestMessageSentByPeer",value:function(e,t){e.startsWith(so.CONV_C2C)&&"in"===t.flow&&this._latestMessageSentByPeerMap.set(e,t)}},{key:"_setLatestMessageSentByMe",value:function(e,t){e.startsWith(so.CONV_C2C)&&"out"===t.flow&&this._latestMessageSentByMeMap.set(e,t)}},{key:"_setGroupLocalLastMessageSequence",value:function(e,t){e.startsWith(so.CONV_GROUP)&&this._groupLocalLastMessageSequenceMap.set(e,t.sequence)}},{key:"getLatestMessageSentByPeer",value:function(e){return this._latestMessageSentByPeerMap.get(e)}},{key:"getLatestMessageSentByMe",value:function(e){return this._latestMessageSentByMeMap.get(e)}},{key:"getGroupLocalLastMessageSequence",value:function(e){return this._groupLocalLastMessageSequenceMap.get(e)||0}},{key:"modifyMessageSentByPeer",value:function(e,t){var n=this.list.get(e);if(!Au(n)){var o=Array.from(n.values()),r=o.length;if(0!==r){var a=null,s=null;t&&(s=t);for(var i=0,u=!1,c=r-1;c>=0;c--)"in"===o[c].flow&&(null===s?s=o[c]:((a=o[c]).nick!==s.nick&&(a.setNickAndAvatar({nick:s.nick}),u=!0),a.avatar!==s.avatar&&(a.setNickAndAvatar({avatar:s.avatar}),u=!0),u&&(i+=1)));xi.log("".concat(this._className,".modifyMessageSentByPeer conversationID:").concat(e," count:").concat(i))}}}},{key:"modifyMessageSentByMe",value:function(e){var t=e.conversationID,n=e.latestNick,o=e.latestAvatar,r=this.list.get(t);if(!Au(r)){var a=Array.from(r.values()),s=a.length;if(0!==s){for(var i=null,u=0,c=!1,l=s-1;l>=0;l--)"out"===a[l].flow&&((i=a[l]).nick!==n&&(i.setNickAndAvatar({nick:n}),c=!0),i.avatar!==o&&(i.setNickAndAvatar({avatar:o}),c=!0),c&&(u+=1));xi.log("".concat(this._className,".modifyMessageSentByMe conversationID:").concat(t," count:").concat(u))}}}},{key:"traversal",value:function(){if(0!==this.list.size&&-1===xi.getLevel()){var e,t=ro(this.list);try{for(t.s();!(e=t.n()).done;){var n=Qn(e.value,2);n[0],n[1]}}catch(i){t.e(i)}finally{t.f()}}}},{key:"reset",value:function(){this.list.clear(),this._latestMessageSentByPeerMap.clear(),this._latestMessageSentByMeMap.clear(),this._groupLocalLastMessageSequenceMap.clear()}}]),e}(),hm={CONTEXT_A2KEY_AND_TINYID_UPDATED:"_a2KeyAndTinyIDUpdated",CLOUD_CONFIG_UPDATED:"_cloudConfigUpdated"};function fm(e){this.mixin(e)}fm.mixin=function(e){var t=e.prototype||e;t._isReady=!1,t.ready=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e)return this._isReady?void(t?e.call(this):setTimeout(e,1)):(this._readyQueue=this._readyQueue||[],void this._readyQueue.push(e))},t.triggerReady=function(){var e=this;this._isReady=!0,setTimeout((function(){var t=e._readyQueue;e._readyQueue=[],t&&t.length>0&&t.forEach((function(e){e.call(this)}),e)}),1)},t.resetReady=function(){this._isReady=!1,this._readyQueue=[]},t.isReady=function(){return this._isReady}};var _m=["jpg","jpeg","gif","png","bmp"],mm=["mp4"],vm=1,Mm=2,ym=3,Im=255,Tm=function(){function e(t){var n=this;Pn(this,e),Au(t)||(this.userID=t.userID||"",this.nick=t.nick||"",this.gender=t.gender||"",this.birthday=t.birthday||0,this.location=t.location||"",this.selfSignature=t.selfSignature||"",this.allowType=t.allowType||so.ALLOW_TYPE_ALLOW_ANY,this.language=t.language||0,this.avatar=t.avatar||"",this.messageSettings=t.messageSettings||0,this.adminForbidType=t.adminForbidType||so.FORBID_TYPE_NONE,this.level=t.level||0,this.role=t.role||0,this.lastUpdatedTime=0,this.profileCustomField=[],Au(t.profileCustomField)||t.profileCustomField.forEach((function(e){n.profileCustomField.push({key:e.key,value:e.value})})))}return Un(e,[{key:"validate",value:function(e){var t=!0,n="";if(Au(e))return{valid:!1,tips:"empty options"};if(e.profileCustomField)for(var o=e.profileCustomField.length,r=null,a=0;a<o;a++){if(r=e.profileCustomField[a],!Bi(r.key)||-1===r.key.indexOf("Tag_Profile_Custom"))return{valid:!1,tips:"自定义资料字段的前缀必须是 Tag_Profile_Custom"};if(!Bi(r.value))return{valid:!1,tips:"自定义资料字段的 value 必须是字符串"}}for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if("profileCustomField"===s)continue;if(Au(e[s])&&!Bi(e[s])&&!Ki(e[s])){n="key:"+s+", invalid value:"+e[s],t=!1;continue}switch(s){case"nick":Bi(e[s])||(n="nick should be a string",t=!1),ou(e[s])>500&&(n="nick name limited: must less than or equal to ".concat(500," bytes, current size: ").concat(ou(e[s])," bytes"),t=!1);break;case"gender":iu(I_,e.gender)||(n="key:gender, invalid value:"+e.gender,t=!1);break;case"birthday":Ki(e.birthday)||(n="birthday should be a number",t=!1);break;case"location":Bi(e.location)||(n="location should be a string",t=!1);break;case"selfSignature":Bi(e.selfSignature)||(n="selfSignature should be a string",t=!1);break;case"allowType":iu(S_,e.allowType)||(n="key:allowType, invalid value:"+e.allowType,t=!1);break;case"language":Ki(e.language)||(n="language should be a number",t=!1);break;case"avatar":Bi(e.avatar)||(n="avatar should be a string",t=!1);break;case"messageSettings":0!==e.messageSettings&&1!==e.messageSettings&&(n="messageSettings should be 0 or 1",t=!1);break;case"adminForbidType":iu(T_,e.adminForbidType)||(n="key:adminForbidType, invalid value:"+e.adminForbidType,t=!1);break;case"level":Ki(e.level)||(n="level should be a number",t=!1);break;case"role":Ki(e.role)||(n="role should be a number",t=!1);break;default:n="unknown key:"+s+"  "+e[s],t=!1}}return{valid:t,tips:n}}}]),e}(),Sm=function e(t){Pn(this,e),this.value=t,this.next=null},Em=function(){function e(t){Pn(this,e),this.MAX_LENGTH=t,this.pTail=null,this.pNodeToDel=null,this.map=new Map,xi.debug("SinglyLinkedList init MAX_LENGTH:".concat(this.MAX_LENGTH))}return Un(e,[{key:"set",value:function(e){var t=new Sm(e);if(this.map.size<this.MAX_LENGTH)null===this.pTail?(this.pTail=t,this.pNodeToDel=t):(this.pTail.next=t,this.pTail=t),this.map.set(e,1);else{var n=this.pNodeToDel;this.pNodeToDel=this.pNodeToDel.next,this.map.delete(n.value),n.next=null,n=null,this.pTail.next=t,this.pTail=t,this.map.set(e,1)}}},{key:"has",value:function(e){return this.map.has(e)}},{key:"delete",value:function(e){this.has(e)&&this.map.delete(e)}},{key:"tail",value:function(){return this.pTail}},{key:"size",value:function(){return this.map.size}},{key:"data",value:function(){return Array.from(this.map.keys())}},{key:"reset",value:function(){for(var e;null!==this.pNodeToDel;)e=this.pNodeToDel,this.pNodeToDel=this.pNodeToDel.next,e.next=null,e=null;this.pTail=null,this.map.clear()}}]),e}(),Dm=["groupID","name","avatar","type","introduction","notification","ownerID","selfInfo","createTime","infoSequence","lastInfoTime","lastMessage","nextMessageSeq","memberNum","maxMemberNum","memberList","joinOption","groupCustomField","muteAllMembers"],km=function(){function e(t){Pn(this,e),this.groupID="",this.name="",this.avatar="",this.type="",this.introduction="",this.notification="",this.ownerID="",this.createTime="",this.infoSequence="",this.lastInfoTime="",this.selfInfo={messageRemindType:"",joinTime:"",nameCard:"",role:""},this.lastMessage={lastTime:"",lastSequence:"",fromAccount:"",messageForShow:""},this.nextMessageSeq="",this.memberNum="",this.memberCount="",this.maxMemberNum="",this.maxMemberCount="",this.joinOption="",this.groupCustomField=[],this.muteAllMembers=void 0,this._initGroup(t)}return Un(e,[{key:"_initGroup",value:function(e){for(var t in e)Dm.indexOf(t)<0||("selfInfo"!==t?("memberNum"===t&&(this.memberCount=e[t]),"maxMemberNum"===t&&(this.maxMemberCount=e[t]),this[t]=e[t]):this.updateSelfInfo(e[t]))}},{key:"updateGroup",value:function(e){var t=JSON.parse(JSON.stringify(e));t.lastMsgTime&&(this.lastMessage.lastTime=t.lastMsgTime),Wi(t.muteAllMembers)||("On"===t.muteAllMembers?t.muteAllMembers=!0:t.muteAllMembers=!1),t.groupCustomField&&pu(this.groupCustomField,t.groupCustomField),Wi(t.memberNum)||(this.memberCount=t.memberNum),Wi(t.maxMemberNum)||(this.maxMemberCount=t.maxMemberNum),tu(this,t,["members","errorCode","lastMsgTime","groupCustomField","memberNum","maxMemberNum"])}},{key:"updateSelfInfo",value:function(e){var t=e.nameCard,n=e.joinTime,o=e.role,r=e.messageRemindType;tu(this.selfInfo,{nameCard:t,joinTime:n,role:o,messageRemindType:r},[],["",null,void 0,0,NaN])}},{key:"setSelfNameCard",value:function(e){this.selfInfo.nameCard=e}},{key:"memberNum",set:function(e){},get:function(){return xi.warn("！！！v2.8.0起弃用memberNum，请使用 memberCount"),this.memberCount}},{key:"maxMemberNum",set:function(e){},get:function(){return xi.warn("！！！v2.8.0起弃用maxMemberNum，请使用 maxMemberCount"),this.maxMemberCount}}]),e}(),Cm=function(e,t){if(Wi(t))return"";switch(e){case so.MSG_TEXT:return t.text;case so.MSG_IMAGE:return"[图片]";case so.MSG_LOCATION:return"[位置]";case so.MSG_AUDIO:return"[语音]";case so.MSG_VIDEO:return"[视频]";case so.MSG_FILE:return"[文件]";case so.MSG_CUSTOM:return"[自定义消息]";case so.MSG_GRP_TIP:return"[群提示消息]";case so.MSG_GRP_SYS_NOTICE:return"[群系统通知]";case so.MSG_FACE:return"[动画表情]";case so.MSG_MERGER:return"[聊天记录]";default:return""}},Am=function(e){return Wi(e)?{lastTime:0,lastSequence:0,fromAccount:0,messageForShow:"",payload:null,type:"",isRevoked:!1,cloudCustomData:"",onlineOnlyFlag:!1,nick:"",nameCard:""}:e instanceof Y_?{lastTime:e.time||0,lastSequence:e.sequence||0,fromAccount:e.from||"",messageForShow:Cm(e.type,e.payload),payload:e.payload||null,type:e.type||null,isRevoked:e.isRevoked||!1,cloudCustomData:e.cloudCustomData||"",onlineOnlyFlag:!!Ji(e.getOnlineOnlyFlag)&&e.getOnlineOnlyFlag(),nick:e.nick||"",nameCard:e.nameCard||""}:xn({},e,{messageForShow:Cm(e.type,e.payload)})},Nm=function(){function e(t){Pn(this,e),this.conversationID=t.conversationID||"",this.unreadCount=t.unreadCount||0,this.type=t.type||"",this.lastMessage=Am(t.lastMessage),t.lastMsgTime&&(this.lastMessage.lastTime=t.lastMsgTime),this._isInfoCompleted=!1,this.peerReadTime=t.peerReadTime||0,this.groupAtInfoList=[],this.remark="",this.isPinned=t.isPinned||!1,this._initProfile(t)}return Un(e,[{key:"_initProfile",value:function(e){var t=this;Object.keys(e).forEach((function(n){switch(n){case"userProfile":t.userProfile=e.userProfile;break;case"groupProfile":t.groupProfile=e.groupProfile}})),Wi(this.userProfile)&&this.type===so.CONV_C2C?this.userProfile=new Tm({userID:e.conversationID.replace("C2C","")}):Wi(this.groupProfile)&&this.type===so.CONV_GROUP&&(this.groupProfile=new km({groupID:e.conversationID.replace("GROUP","")}))}},{key:"updateUnreadCount",value:function(e,t){Wi(e)||(hu(this.subType)?this.unreadCount=0:t&&this.type===so.CONV_GROUP?this.unreadCount=e:this.unreadCount=this.unreadCount+e)}},{key:"updateLastMessage",value:function(e){this.lastMessage=Am(e)}},{key:"updateGroupAtInfoList",value:function(e){var t,n=($n(t=e.groupAtType)||eo(t)||to(t)||oo()).slice(0);-1!==n.indexOf(so.CONV_AT_ME)&&-1!==n.indexOf(so.CONV_AT_ALL)&&(n=[so.CONV_AT_ALL_AT_ME]);var o={from:e.from,groupID:e.groupID,messageSequence:e.sequence,atTypeArray:n,__random:e.__random,__sequence:e.__sequence};this.groupAtInfoList.push(o),xi.debug("Conversation.updateGroupAtInfoList conversationID:".concat(this.conversationID),this.groupAtInfoList)}},{key:"clearGroupAtInfoList",value:function(){this.groupAtInfoList.length=0}},{key:"reduceUnreadCount",value:function(){this.unreadCount>=1&&(this.unreadCount-=1)}},{key:"isLastMessageRevoked",value:function(e){var t=e.sequence,n=e.time;return this.type===so.CONV_C2C&&t===this.lastMessage.lastSequence&&n===this.lastMessage.lastTime||this.type===so.CONV_GROUP&&t===this.lastMessage.lastSequence}},{key:"setLastMessageRevoked",value:function(e){this.lastMessage.isRevoked=e}},{key:"toAccount",get:function(){return this.conversationID.startsWith(so.CONV_C2C)?this.conversationID.replace(so.CONV_C2C,""):this.conversationID.startsWith(so.CONV_GROUP)?this.conversationID.replace(so.CONV_GROUP,""):""}},{key:"subType",get:function(){return this.groupProfile?this.groupProfile.type:""}}]),e}(),Om=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="ConversationModule",fm.mixin(zn(o)),o._messageListHandler=new gm,o.singlyLinkedList=new Em(100),o._pagingStatus=Sc.NOT_START,o._pagingTimeStamp=0,o._pagingStartIndex=0,o._pagingPinnedTimeStamp=0,o._pagingPinnedStartIndex=0,o._conversationMap=new Map,o._tmpGroupList=[],o._tmpGroupAtTipsList=[],o._peerReadTimeMap=new Map,o._completedMap=new Map,o._roamingMessageKeyAndTimeMap=new Map,o._initListeners(),o}return Un(n,[{key:"_initListeners",value:function(){this.getInnerEmitterInstance().on(hm.CONTEXT_A2KEY_AND_TINYID_UPDATED,this._initLocalConversationList,this)}},{key:"onCheckTimer",value:function(e){e%60==0&&this._messageListHandler.traversal()}},{key:"_initLocalConversationList",value:function(){var e=this,t=new Kp(Mg);xi.log("".concat(this._className,"._initLocalConversationList."));var n="",o=this._getStorageConversationList();if(o){for(var r=o.length,a=0;a<r;a++){var s=o[a];if(s&&s.groupProfile){var i=s.groupProfile.type;if(hu(i))continue}this._conversationMap.set(o[a].conversationID,new Nm(o[a]))}this._emitConversationUpdate(!0,!1),n="count:".concat(r)}else n="count:0";t.setNetworkType(this.getNetworkType()).setMessage(n).end(),this.getModule(Vc)||this.triggerReady(),this.ready((function(){e._tmpGroupList.length>0&&(e.updateConversationGroupProfile(e._tmpGroupList),e._tmpGroupList.length=0)})),this._syncConversationList()}},{key:"onMessageSent",value:function(e){this._onSendOrReceiveMessage(e.conversationOptionsList,!0)}},{key:"onNewMessage",value:function(e){this._onSendOrReceiveMessage(e.conversationOptionsList,e.isInstantMessage)}},{key:"_onSendOrReceiveMessage",value:function(e){var t=this,n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this._isReady?0!==e.length&&(this._getC2CPeerReadTime(e),this._updateLocalConversationList(e,!1,n),this._setStorageConversationList(),this._emitConversationUpdate()):this.ready((function(){t._onSendOrReceiveMessage(e,n)}))}},{key:"updateConversationGroupProfile",value:function(e){var t=this;Yi(e)&&0===e.length||(0!==this._conversationMap.size?(e.forEach((function(e){var n="GROUP".concat(e.groupID);if(t._conversationMap.has(n)){var o=t._conversationMap.get(n);o.groupProfile=e,o.lastMessage.lastSequence<e.nextMessageSeq&&(o.lastMessage.lastSequence=e.nextMessageSeq-1),o.subType||(o.subType=e.type)}})),this._emitConversationUpdate(!0,!1)):this._tmpGroupList=e)}},{key:"_updateConversationUserProfile",value:function(e){var t=this;e.data.forEach((function(e){var n="C2C".concat(e.userID);t._conversationMap.has(n)&&(t._conversationMap.get(n).userProfile=e)})),this._emitConversationUpdate(!0,!1)}},{key:"onMessageRevoked",value:function(e){var t=this;if(0!==e.length){var n=null,o=!1;e.forEach((function(e){(n=t._conversationMap.get(e.conversationID))&&n.isLastMessageRevoked(e)&&(o=!0,n.setLastMessageRevoked(!0))})),o&&this._emitConversationUpdate(!0,!1)}}},{key:"onMessageDeleted",value:function(e){if(0!==e.length){e.forEach((function(e){e.isDeleted=!0}));for(var t=e[0].conversationID,n=this._messageListHandler.getLocalMessageList(t),o={},r=n.length-1;r>0;r--)if(!n[r].isDeleted){o=n[r];break}var a=this._conversationMap.get(t);if(a){var s=!1;a.lastMessage.lastSequence!==o.sequence&&a.lastMessage.lastTime!==o.time&&(a.updateLastMessage(o),s=!0,xi.log("".concat(this._className,".onMessageDeleted. update conversationID:").concat(t," with lastMessage:"),a.lastMessage)),t.startsWith(so.CONV_C2C)&&this.updateUnreadCount(t),s&&this._emitConversationUpdate(!0,!1)}}}},{key:"onNewGroupAtTips",value:function(e){var t=this,n=e.dataList,o=null;n.forEach((function(e){e.groupAtTips?o=e.groupAtTips:e.elements&&(o=e.elements),o.__random=e.random,o.__sequence=e.clientSequence,t._tmpGroupAtTipsList.push(o)})),xi.debug("".concat(this._className,".onNewGroupAtTips isReady:").concat(this._isReady),this._tmpGroupAtTipsList),this._isReady&&this._handleGroupAtTipsList()}},{key:"_handleGroupAtTipsList",value:function(){var e=this;if(0!==this._tmpGroupAtTipsList.length){var t=!1;this._tmpGroupAtTipsList.forEach((function(n){var o=n.groupID;if(n.from!==e.getMyUserID()){var r=e._conversationMap.get("".concat(so.CONV_GROUP).concat(o));r&&(r.updateGroupAtInfoList(n),t=!0)}})),t&&this._emitConversationUpdate(!0,!1),this._tmpGroupAtTipsList.length=0}}},{key:"_getC2CPeerReadTime",value:function(e){var t=this,n=[];if(e.forEach((function(e){t._conversationMap.has(e.conversationID)||e.type!==so.CONV_C2C||n.push(e.conversationID.replace(so.CONV_C2C,""))})),n.length>0){xi.debug("".concat(this._className,"._getC2CPeerReadTime userIDList:").concat(n));var o=this.getModule(Vc);o&&o.getRemotePeerReadTime(n)}}},{key:"_getStorageConversationList",value:function(){return this.getModule(Wc).getItem("conversationMap")}},{key:"_setStorageConversationList",value:function(){var e=this.getLocalConversationList().slice(0,20).map((function(e){return{conversationID:e.conversationID,type:e.type,subType:e.subType,lastMessage:e.lastMessage,groupProfile:e.groupProfile,userProfile:e.userProfile}}));this.getModule(Wc).setItem("conversationMap",e)}},{key:"_emitConversationUpdate",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=Zn(this._conversationMap.values());if(t){var o=this.getModule(Kc);o&&o.updateGroupLastMessage(n)}e&&this.emitOuterEvent(ao.CONVERSATION_LIST_UPDATED,n)}},{key:"getLocalConversationList",value:function(){return Zn(this._conversationMap.values())}},{key:"getLocalConversation",value:function(e){return this._conversationMap.get(e)}},{key:"_syncConversationList",value:function(){var e=this,t=new Kp(yg);return this._pagingStatus===Sc.NOT_START&&this._conversationMap.clear(),this._pagingGetConversationList().then((function(n){return e._pagingStatus=Sc.RESOLVED,e._setStorageConversationList(),e._handleC2CPeerReadTime(),e.checkAndPatchRemark(),t.setMessage(e._conversationMap.size).setNetworkType(e.getNetworkType()).end(),n})).catch((function(n){return e._pagingStatus=Sc.REJECTED,t.setMessage(e._pagingTimeStamp),e.probeNetwork().then((function(e){var o=Qn(e,2),r=o[0],a=o[1];t.setError(n,r,a).end()})),om(n)}))}},{key:"_pagingGetConversationList",value:function(){var e=this,t="".concat(this._className,"._pagingGetConversationList");return xi.log("".concat(t," timeStamp:").concat(this._pagingTimeStamp," startIndex:").concat(this._pagingStartIndex)+" pinnedTimeStamp:".concat(this._pagingPinnedTimeStamp," pinnedStartIndex:").concat(this._pagingPinnedStartIndex)),this._pagingStatus=Sc.PENDING,this.request({protocolName:Dl,requestData:{fromAccount:this.getMyUserID(),timeStamp:this._pagingTimeStamp,startIndex:this._pagingStartIndex,pinnedTimeStamp:this._pagingPinnedTimeStamp,pinnedStartIndex:this._pagingStartIndex,orderType:1}}).then((function(n){var o=n.data,r=o.completeFlag,a=o.conversations,s=void 0===a?[]:a,i=o.timeStamp,u=o.startIndex,c=o.pinnedTimeStamp,l=o.pinnedStartIndex;if(xi.log("".concat(t," ok. completeFlag:").concat(r," count:").concat(s.length," isReady:").concat(e._isReady)),s.length>0){var d=e._getConversationOptions(s);e._updateLocalConversationList(d,!0),e.isLoggedIn()&&e._emitConversationUpdate()}if(!e._isReady){if(!e.isLoggedIn())return nm();e.triggerReady()}return e._pagingTimeStamp=i,e._pagingStartIndex=u,e._pagingPinnedTimeStamp=c,e._pagingPinnedStartIndex=l,1!==r?e._pagingGetConversationList():(e._handleGroupAtTipsList(),nm())})).catch((function(n){throw e.isLoggedIn()&&(e._isReady||(xi.warn("".concat(t," failed. error:"),n),e.triggerReady())),n}))}},{key:"_updateLocalConversationList",value:function(e,t,n){var o,r=Date.now();o=this._getTmpConversationListMapping(e,t,n),this._conversationMap=new Map(this._sortConversationList([].concat(Zn(o.toBeUpdatedConversationList),Zn(this._conversationMap)))),t||this._updateUserOrGroupProfile(o.newConversationList),xi.debug("".concat(this._className,"._updateLocalConversationList cost ").concat(Date.now()-r," ms"))}},{key:"_getTmpConversationListMapping",value:function(e,t,n){for(var o=[],r=[],a=this.getModule(Kc),s=this.getModule(Bc),i=0,u=e.length;i<u;i++){var c=new Nm(e[i]),l=c.conversationID;if(this._conversationMap.has(l)){var d=this._conversationMap.get(l),p=["unreadCount","allowType","adminForbidType","payload","isPinned"];n||p.push("lastMessage"),tu(d,c,p,[null,void 0,"",0,NaN]),d.updateUnreadCount(c.unreadCount,t),n&&(d.lastMessage.payload=e[i].lastMessage.payload,d.type===so.CONV_GROUP&&(d.lastMessage.nameCard=e[i].lastMessage.nameCard,d.lastMessage.nick=e[i].lastMessage.nick)),e[i].lastMessage&&d.lastMessage.cloudCustomData!==e[i].lastMessage.cloudCustomData&&(d.lastMessage.cloudCustomData=e[i].lastMessage.cloudCustomData||""),this._conversationMap.delete(l),o.push([l,d])}else{if(c.type===so.CONV_GROUP&&a){var g=c.groupProfile.groupID,h=a.getLocalGroupProfile(g);h&&(c.groupProfile=h,c.updateUnreadCount(0))}else if(c.type===so.CONV_C2C){var f=l.replace(so.CONV_C2C,"");s&&s.isMyFriend(f)&&(c.remark=s.getFriendRemark(f))}r.push(c),o.push([l,c])}}return{toBeUpdatedConversationList:o,newConversationList:r}}},{key:"_sortConversationList",value:function(e){var t=[],n=[];return e.forEach((function(e){!0===e[1].isPinned?t.push(e):n.push(e)})),t.sort((function(e,t){return t[1].lastMessage.lastTime-e[1].lastMessage.lastTime})).concat(n.sort((function(e,t){return t[1].lastMessage.lastTime-e[1].lastMessage.lastTime})))}},{key:"_sortConversationListAndEmitEvent",value:function(){this._conversationMap=new Map(this._sortConversationList(Zn(this._conversationMap))),this._emitConversationUpdate(!0,!1)}},{key:"_updateUserOrGroupProfile",value:function(e){var t=this;if(0!==e.length){var n=[],o=[],r=this.getModule(xc),a=this.getModule(Kc);e.forEach((function(e){if(e.type===so.CONV_C2C)n.push(e.toAccount);else if(e.type===so.CONV_GROUP){var t=e.toAccount;a.hasLocalGroup(t)?e.groupProfile=a.getLocalGroupProfile(t):o.push(t)}})),xi.log("".concat(this._className,"._updateUserOrGroupProfile c2cUserIDList:").concat(n," groupIDList:").concat(o)),n.length>0&&r.getUserProfile({userIDList:n}).then((function(e){var n=e.data;Yi(n)?n.forEach((function(e){t._conversationMap.get("C2C".concat(e.userID)).userProfile=e})):t._conversationMap.get("C2C".concat(n.userID)).userProfile=n})),o.length>0&&a.getGroupProfileAdvance({groupIDList:o,responseFilter:{groupBaseInfoFilter:["Type","Name","FaceUrl"]}}).then((function(e){e.data.successGroupList.forEach((function(e){var n="GROUP".concat(e.groupID);if(t._conversationMap.has(n)){var o=t._conversationMap.get(n);tu(o.groupProfile,e,[],[null,void 0,"",0,NaN]),!o.subType&&e.type&&(o.subType=e.type)}}))}))}}},{key:"_getConversationOptions",value:function(e){var t=[],n=e.filter((function(e){var t=e.lastMsg;return ji(t)})).map((function(e){if(1===e.type){var n={userID:e.userID,nick:e.peerNick,avatar:e.peerAvatar};return t.push(n),{conversationID:"C2C".concat(e.userID),type:"C2C",lastMessage:{lastTime:e.time,lastSequence:e.sequence,fromAccount:e.lastC2CMsgFromAccount,messageForShow:e.messageShow,type:e.lastMsg.elements[0]?e.lastMsg.elements[0].type:null,payload:e.lastMsg.elements[0]?e.lastMsg.elements[0].content:null,cloudCustomData:e.cloudCustomData||"",isRevoked:8===e.lastMessageFlag,onlineOnlyFlag:!1,nick:"",nameCard:""},userProfile:new Tm(n),peerReadTime:e.c2cPeerReadTime,isPinned:1===e.isPinned}}return{conversationID:"GROUP".concat(e.groupID),type:"GROUP",lastMessage:{lastTime:e.time,lastSequence:e.messageReadSeq+e.unreadCount,fromAccount:e.msgGroupFromAccount,messageForShow:e.messageShow,type:e.lastMsg.elements[0]?e.lastMsg.elements[0].type:null,payload:e.lastMsg.elements[0]?e.lastMsg.elements[0].content:null,cloudCustomData:e.cloudCustomData||"",isRevoked:2===e.lastMessageFlag,onlineOnlyFlag:!1,nick:e.senderNick||"",nameCard:e.senderNameCard||""},groupProfile:new km({groupID:e.groupID,name:e.groupNick,avatar:e.groupImage}),unreadCount:e.unreadCount,peerReadTime:0,isPinned:1===e.isPinned}}));return t.length>0&&this.getModule(xc).onConversationsProfileUpdated(t),n}},{key:"getLocalMessageList",value:function(e){return this._messageListHandler.getLocalMessageList(e)}},{key:"deleteLocalMessage",value:function(e){e instanceof Y_&&this._messageListHandler.remove(e)}},{key:"onConversationDeleted",value:function(e){var t=this;xi.log("".concat(this._className,".onConversationDeleted")),Yi(e)&&e.forEach((function(e){var n=e.type,o=e.userID,r=e.groupID,a="";1===n?a="".concat(so.CONV_C2C).concat(o):2===n&&(a="".concat(so.CONV_GROUP).concat(r)),t.deleteLocalConversation(a)}))}},{key:"onConversationPinned",value:function(e){var t=this;if(Yi(e)){var n=!1;e.forEach((function(e){var o,r=e.type,a=e.userID,s=e.groupID;1===r?o=t.getLocalConversation("".concat(so.CONV_C2C).concat(a)):2===r&&(o=t.getLocalConversation("".concat(so.CONV_GROUP).concat(s))),o&&(xi.log("".concat(t._className,".onConversationPinned conversationID:").concat(o.conversationID," isPinned:").concat(o.isPinned)),o.isPinned||(o.isPinned=!0,n=!0))})),n&&this._sortConversationListAndEmitEvent()}}},{key:"onConversationUnpinned",value:function(e){var t=this;if(Yi(e)){var n=!1;e.forEach((function(e){var o,r=e.type,a=e.userID,s=e.groupID;1===r?o=t.getLocalConversation("".concat(so.CONV_C2C).concat(a)):2===r&&(o=t.getLocalConversation("".concat(so.CONV_GROUP).concat(s))),o&&(xi.log("".concat(t._className,".onConversationUnpinned conversationID:").concat(o.conversationID," isPinned:").concat(o.isPinned)),o.isPinned&&(o.isPinned=!1,n=!0))})),n&&this._sortConversationListAndEmitEvent()}}},{key:"getMessageList",value:function(e){var t=this,n=e.conversationID,o=e.nextReqMessageID,r=e.count,a="".concat(this._className,".getMessageList"),s=this.getLocalConversation(n),i="";if(s&&s.groupProfile&&(i=s.groupProfile.type),hu(i))return xi.log("".concat(a," not available in avchatroom. conversationID:").concat(n)),nm({messageList:[],nextReqMessageID:"",isCompleted:!0});(Wi(r)||r>15)&&(r=15);var u=this._computeLeftCount({conversationID:n,nextReqMessageID:o});return xi.log("".concat(a," conversationID:").concat(n," leftCount:").concat(u," count:").concat(r," nextReqMessageID:").concat(o)),this._needGetHistory({conversationID:n,leftCount:u,count:r})?this.getHistoryMessages({conversationID:n,nextReqMessageID:o,count:20}).then((function(){return u=t._computeLeftCount({conversationID:n,nextReqMessageID:o}),W_(t._computeResult({conversationID:n,nextReqMessageID:o,count:r,leftCount:u}))})):(xi.log("".concat(a,".getMessageList get message list from memory")),this.modifyMessageList(n),nm(this._computeResult({conversationID:n,nextReqMessageID:o,count:r,leftCount:u})))}},{key:"_computeLeftCount",value:function(e){var t=e.conversationID,n=e.nextReqMessageID;return n?this._messageListHandler.getLocalMessageList(t).findIndex((function(e){return e.ID===n})):this._getMessageListSize(t)}},{key:"_getMessageListSize",value:function(e){return this._messageListHandler.getLocalMessageList(e).length}},{key:"_needGetHistory",value:function(e){var t=e.conversationID,n=e.leftCount,o=e.count,r=this.getLocalConversation(t),a="";return r&&r.groupProfile&&(a=r.groupProfile.type),!mu(t)&&!hu(a)&&n<o&&!this._completedMap.has(t)}},{key:"_computeResult",value:function(e){var t=e.conversationID,n=e.nextReqMessageID,o=e.count,r=e.leftCount,a=this._computeMessageList({conversationID:t,nextReqMessageID:n,count:o}),s=this._computeIsCompleted({conversationID:t,leftCount:r,count:o}),i=this._computeNextReqMessageID({messageList:a,isCompleted:s,conversationID:t}),u="".concat(this._className,"._computeResult. conversationID:").concat(t);return xi.log("".concat(u," leftCount:").concat(r," count:").concat(o," nextReqMessageID:").concat(i," isCompleted:").concat(s)),{messageList:a,nextReqMessageID:i,isCompleted:s}}},{key:"_computeMessageList",value:function(e){var t=e.conversationID,n=e.nextReqMessageID,o=e.count,r=this._messageListHandler.getLocalMessageList(t),a=this._computeIndexEnd({nextReqMessageID:n,messageList:r}),s=this._computeIndexStart({indexEnd:a,count:o});return r.slice(s,a)}},{key:"_computeNextReqMessageID",value:function(e){var t=e.messageList,n=e.isCompleted,o=e.conversationID;if(!n)return 0===t.length?"":t[0].ID;var r=this._messageListHandler.getLocalMessageList(o);return 0===r.length?"":r[0].ID}},{key:"_computeIndexEnd",value:function(e){var t=e.messageList,n=void 0===t?[]:t,o=e.nextReqMessageID;return o?n.findIndex((function(e){return e.ID===o})):n.length}},{key:"_computeIndexStart",value:function(e){var t=e.indexEnd,n=e.count;return t>n?t-n:0}},{key:"_computeIsCompleted",value:function(e){var t=e.conversationID;return!!(e.leftCount<=e.count&&this._completedMap.has(t))}},{key:"getHistoryMessages",value:function(e){var t=e.conversationID,n=e.nextReqMessageID;if(t===so.CONV_SYSTEM)return nm();e.count?e.count>20&&(e.count=20):e.count=15;var o=this._messageListHandler.getLocalOldestMessageByConversationID(t);o||((o={}).time=0,o.sequence=0,0===t.indexOf(so.CONV_C2C)?(o.to=t.replace(so.CONV_C2C,""),o.conversationType=so.CONV_C2C):0===t.indexOf(so.CONV_GROUP)&&(o.to=t.replace(so.CONV_GROUP,""),o.conversationType=so.CONV_GROUP));var r="",a=null;switch(o.conversationType){case so.CONV_C2C:if(r=t.replace(so.CONV_C2C,""),!(a=this.getModule(Vc)))return om({code:vd.CANNOT_FIND_MODULE,message:Dp});var s=this._roamingMessageKeyAndTimeMap.has(t);return a.getRoamingMessage({conversationID:e.conversationID,peerAccount:r,count:e.count,lastMessageTime:s?this._roamingMessageKeyAndTimeMap.get(t).lastMessageTime:0,messageKey:s?this._roamingMessageKeyAndTimeMap.get(t).messageKey:""});case so.CONV_GROUP:return(a=this.getModule(Kc))?a.getRoamingMessage({conversationID:e.conversationID,groupID:o.to,count:e.count,sequence:n&&!1===o.getOnlineOnlyFlag()?o.sequence-1:o.sequence}):om({code:vd.CANNOT_FIND_MODULE,message:Dp});default:return nm()}}},{key:"patchConversationLastMessage",value:function(e){var t=this.getLocalConversation(e);if(t){var n=t.lastMessage,o=n.messageForShow,r=n.payload;if(Au(o)||Au(r)){var a=this._messageListHandler.getLocalMessageList(e);if(0===a.length)return;var s=a[a.length-1];xi.log("".concat(this._className,".patchConversationLastMessage conversationID:").concat(e," payload:"),s.payload),t.updateLastMessage(s)}}}},{key:"storeRoamingMessage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=t.startsWith(so.CONV_C2C)?so.CONV_C2C:so.CONV_GROUP,o=null,r=[],a=0,s=e.length,i=null,u=n===so.CONV_GROUP,c=this.getModule(Xc),l=function(){a=u?e.length-1:0,s=u?0:e.length},d=function(){u?--a:++a},p=function(){return u?a>=s:a<s};for(l();p();d())if(u&&1===e[a].sequence&&this.setCompleted(t),1!==e[a].isPlaceMessage)if((o=new Y_(e[a])).to=e[a].to,o.isSystemMessage=!!e[a].isSystemMessage,o.conversationType=n,4===e[a].event?i={type:so.MSG_GRP_TIP,content:xn({},e[a].elements,{groupProfile:e[a].groupProfile})}:(e[a].elements=c.parseElements(e[a].elements,e[a].from),i=e[a].elements),u||o.setNickAndAvatar({nick:e[a].nick,avatar:e[a].avatar}),Au(i)){var g=new Kp(cg);g.setMessage("from:".concat(o.from," to:").concat(o.to," sequence:").concat(o.sequence," event:").concat(e[a].event)),g.setNetworkType(this.getNetworkType()).setLevel("warning").end()}else o.setElement(i),o.reInitialize(this.getMyUserID()),r.push(o);return this._messageListHandler.unshift(r),l=d=p=null,r}},{key:"setMessageRead",value:function(e){var t=e.conversationID,n=e.messageID,o=this.getLocalConversation(t);if(xi.log("".concat(this._className,".setMessageRead conversationID:").concat(t," unreadCount:").concat(o?o.unreadCount:0)),!o)return nm();if(o.type!==so.CONV_GROUP||Au(o.groupAtInfoList)||this.deleteGroupAtTips(t),0===o.unreadCount)return nm();var r=this._messageListHandler.getLocalMessage(t,n),a=null;switch(o.type){case so.CONV_C2C:return(a=this.getModule(Vc))?a.setMessageRead({conversationID:t,lastMessageTime:r?r.time:o.lastMessage.lastTime}):om({code:vd.CANNOT_FIND_MODULE,message:Dp});case so.CONV_GROUP:return(a=this._moduleManager.getModule(Kc))?a.setMessageRead({conversationID:t,lastMessageSeq:r?r.sequence:o.lastMessage.lastSequence}):om({code:vd.CANNOT_FIND_MODULE,message:Dp});case so.CONV_SYSTEM:return o.unreadCount=0,this._emitConversationUpdate(!0,!1),nm();default:return nm()}}},{key:"updateIsReadAfterReadReport",value:function(e){var t=e.conversationID,n=e.lastMessageSeq,o=e.lastMessageTime,r=this._messageListHandler.getLocalMessageList(t);if(0!==r.length)for(var a,s=r.length-1;s>=0;s--)if(a=r[s],!(o&&a.time>o||n&&a.sequence>n)){if("in"===a.flow&&a.isRead)break;a.setIsRead(!0)}}},{key:"updateUnreadCount",value:function(e){var t=this.getLocalConversation(e),n=this._messageListHandler.getLocalMessageList(e);if(t){var o=t.unreadCount,r=n.filter((function(e){return!e.isRead&&!e.getOnlineOnlyFlag()&&!e.isDeleted})).length;o!==r&&(t.unreadCount=r,xi.log("".concat(this._className,".updateUnreadCount from ").concat(o," to ").concat(r,", conversationID:").concat(e)),this._emitConversationUpdate(!0,!1))}}},{key:"updateIsRead",value:function(e){var t=this.getLocalConversation(e),n=this.getLocalMessageList(e);if(t&&0!==n.length&&!mu(t.type)){for(var o=[],r=0,a=n.length;r<a;r++)"in"!==n[r].flow?"out"!==n[r].flow||n[r].isRead||n[r].setIsRead(!0):o.push(n[r]);var s=0;if(t.type===so.CONV_C2C){var i=o.slice(-t.unreadCount).filter((function(e){return e.isRevoked})).length;s=o.length-t.unreadCount-i}else s=o.length-t.unreadCount;for(var u=0;u<s&&!o[u].isRead;u++)o[u].setIsRead(!0)}}},{key:"deleteGroupAtTips",value:function(e){var t="".concat(this._className,".deleteGroupAtTips");xi.log("".concat(t));var n=this._conversationMap.get(e);if(!n)return Promise.resolve();var o=n.groupAtInfoList;if(0===o.length)return Promise.resolve();var r=this.getMyUserID();return this.request({protocolName:Nl,requestData:{messageListToDelete:o.map((function(e){return{from:e.from,to:r,messageSeq:e.__sequence,messageRandom:e.__random,groupID:e.groupID}}))}}).then((function(){return xi.log("".concat(t," ok. count:").concat(o.length)),n.clearGroupAtInfoList(),Promise.resolve()})).catch((function(e){return xi.error("".concat(t," failed. error:"),e),om(e)}))}},{key:"appendToMessageList",value:function(e){this._messageListHandler.pushIn(e)}},{key:"setMessageRandom",value:function(e){this.singlyLinkedList.set(e.random)}},{key:"deleteMessageRandom",value:function(e){this.singlyLinkedList.delete(e.random)}},{key:"pushIntoMessageList",value:function(e,t,n){return!(!this._messageListHandler.pushIn(t,n)||this._isMessageFromCurrentInstance(t)&&!n)&&(e.push(t),!0)}},{key:"_isMessageFromCurrentInstance",value:function(e){return this.singlyLinkedList.has(e.random)}},{key:"revoke",value:function(e,t,n){return this._messageListHandler.revoke(e,t,n)}},{key:"getPeerReadTime",value:function(e){return this._peerReadTimeMap.get(e)}},{key:"recordPeerReadTime",value:function(e,t){this._peerReadTimeMap.has(e)?this._peerReadTimeMap.get(e)<t&&this._peerReadTimeMap.set(e,t):this._peerReadTimeMap.set(e,t)}},{key:"updateMessageIsPeerReadProperty",value:function(e,t){if(e.startsWith(so.CONV_C2C)&&t>0){var n=this._messageListHandler.updateMessageIsPeerReadProperty(e,t);n.length>0&&this.emitOuterEvent(ao.MESSAGE_READ_BY_PEER,n)}}},{key:"updateMessageIsReadProperty",value:function(e){var t=this.getLocalConversation(e),n=this._messageListHandler.getLocalMessageList(e);if(t&&0!==n.length&&!mu(t.type)){for(var o=[],r=0;r<n.length;r++)"in"!==n[r].flow?"out"!==n[r].flow||n[r].isRead||n[r].setIsRead(!0):o.push(n[r]);var a=0;if(t.type===so.CONV_C2C){var s=o.slice(-t.unreadCount).filter((function(e){return e.isRevoked})).length;a=o.length-t.unreadCount-s}else a=o.length-t.unreadCount;for(var i=0;i<a&&!o[i].isRead;i++)o[i].setIsRead(!0)}}},{key:"updateMessageIsModifiedProperty",value:function(e){this._messageListHandler.updateMessageIsModifiedProperty(e)}},{key:"setCompleted",value:function(e){xi.log("".concat(this._className,".setCompleted. conversationID:").concat(e)),this._completedMap.set(e,!0)}},{key:"updateRoamingMessageKeyAndTime",value:function(e,t,n){this._roamingMessageKeyAndTimeMap.set(e,{messageKey:t,lastMessageTime:n})}},{key:"getConversationList",value:function(e){var t=this,n="".concat(this._className,".getConversationList"),o="pagingStatus:".concat(this._pagingStatus,", local conversation count:").concat(this._conversationMap.size,", options:").concat(e);if(xi.log("".concat(n,". ").concat(o)),this._pagingStatus===Sc.REJECTED){var r=new Kp(fg);return r.setMessage(o),this._syncConversationList().then((function(){r.setNetworkType(t.getNetworkType()).end();var n=t._getConversationList(e);return W_({conversationList:n})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],a=n[1];r.setError(e,o,a).end()})),xi.error("".concat(n," failed. error:"),e),om(e)}))}if(0===this._conversationMap.size){var a=new Kp(fg);return a.setMessage(o),this._syncConversationList().then((function(){a.setNetworkType(t.getNetworkType()).end();var n=t._getConversationList(e);return W_({conversationList:n})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),xi.error("".concat(n," failed. error:"),e),om(e)}))}var s=this._getConversationList(e);return xi.log("".concat(n,". returned conversation count:").concat(s.length)),nm({conversationList:s})}},{key:"_getConversationList",value:function(e){var t=this;if(Wi(e))return this.getLocalConversationList();if(Yi(e)){var n=[];return e.forEach((function(e){if(t._conversationMap.has(e)){var o=t.getLocalConversation(e);n.push(o)}})),n}}},{key:"_handleC2CPeerReadTime",value:function(){var e,t=ro(this._conversationMap);try{for(t.s();!(e=t.n()).done;){var n=Qn(e.value,2),o=n[0],r=n[1];r.type===so.CONV_C2C&&(xi.debug("".concat(this._className,"._handleC2CPeerReadTime conversationID:").concat(o," peerReadTime:").concat(r.peerReadTime)),this.recordPeerReadTime(o,r.peerReadTime))}}catch(i){t.e(i)}finally{t.f()}}},{key:"getConversationProfile",value:function(e){var t,n=this;if((t=this._conversationMap.has(e)?this._conversationMap.get(e):new Nm({conversationID:e,type:e.slice(0,3)===so.CONV_C2C?so.CONV_C2C:so.CONV_GROUP}))._isInfoCompleted||t.type===so.CONV_SYSTEM)return nm({conversation:t});var o=new Kp(_g),r="".concat(this._className,".getConversationProfile");return xi.log("".concat(r,". conversationID:").concat(e," remark:").concat(t.remark," lastMessage:"),t.lastMessage),this._updateUserOrGroupProfileCompletely(t).then((function(a){o.setNetworkType(n.getNetworkType()).setMessage("conversationID:".concat(e," unreadCount:").concat(a.data.conversation.unreadCount)).end();var s=n.getModule(Bc);if(s&&t.type===so.CONV_C2C){var i=e.replace(so.CONV_C2C,"");if(s.isMyFriend(i)){var u=s.getFriendRemark(i);t.remark!==u&&(t.remark=u,xi.log("".concat(r,". conversationID:").concat(e," patch remark:").concat(t.remark)))}}return xi.log("".concat(r," ok. conversationID:").concat(e)),a})).catch((function(t){return n.probeNetwork().then((function(n){var r=Qn(n,2),a=r[0],s=r[1];o.setError(t,a,s).setMessage("conversationID:".concat(e)).end()})),xi.error("".concat(r," failed. error:"),t),om(t)}))}},{key:"_updateUserOrGroupProfileCompletely",value:function(e){var t=this;return e.type===so.CONV_C2C?this.getModule(xc).getUserProfile({userIDList:[e.toAccount]}).then((function(n){var o=n.data;return 0===o.length?om(new $_({code:vd.USER_OR_GROUP_NOT_FOUND,message:Zd})):(e.userProfile=o[0],e._isInfoCompleted=!0,t._unshiftConversation(e),nm({conversation:e}))})):this.getModule(Kc).getGroupProfile({groupID:e.toAccount}).then((function(n){return e.groupProfile=n.data.group,e._isInfoCompleted=!0,t._unshiftConversation(e),nm({conversation:e})}))}},{key:"_unshiftConversation",value:function(e){e instanceof Nm&&!this._conversationMap.has(e.conversationID)&&(this._conversationMap=new Map([[e.conversationID,e]].concat(Zn(this._conversationMap))),this._setStorageConversationList(),this._emitConversationUpdate(!0,!1))}},{key:"deleteConversation",value:function(e){var t=this,n={fromAccount:this.getMyUserID(),toAccount:void 0,type:void 0};if(!this._conversationMap.has(e)){var o=new $_({code:vd.CONVERSATION_NOT_FOUND,message:Qd});return om(o)}switch(this._conversationMap.get(e).type){case so.CONV_C2C:n.type=1,n.toAccount=e.replace(so.CONV_C2C,"");break;case so.CONV_GROUP:n.type=2,n.toGroupID=e.replace(so.CONV_GROUP,"");break;case so.CONV_SYSTEM:return this.getModule(Kc).deleteGroupSystemNotice({messageList:this._messageListHandler.getLocalMessageList(e)}),this.deleteLocalConversation(e),nm({conversationID:e});default:var r=new $_({code:vd.CONVERSATION_UN_RECORDED_TYPE,message:$d});return om(r)}var a=new Kp(mg);a.setMessage("conversationID:".concat(e));var s="".concat(this._className,".deleteConversation");return xi.log("".concat(s,". conversationID:").concat(e)),this.setMessageRead({conversationID:e}).then((function(){return t.request({protocolName:Cl,requestData:n})})).then((function(){return a.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(s," ok")),t.deleteLocalConversation(e),nm({conversationID:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),xi.error("".concat(s," failed. error:"),e),om(e)}))}},{key:"pinConversation",value:function(e){var t=this,n=e.conversationID,o=e.isPinned;if(!this._conversationMap.has(n))return om({code:vd.CONVERSATION_NOT_FOUND,message:Qd});var r=this.getLocalConversation(n);if(r.isPinned===o)return nm({conversationID:n});var a=new Kp(vg);a.setMessage("conversationID:".concat(n," isPinned:").concat(o));var s="".concat(this._className,".pinConversation");xi.log("".concat(s,". conversationID:").concat(n," isPinned:").concat(o));var i=null;return fu(n)?i={type:1,toAccount:n.replace(so.CONV_C2C,"")}:_u(n)&&(i={type:2,groupID:n.replace(so.CONV_GROUP,"")}),this.request({protocolName:Al,requestData:{fromAccount:this.getMyUserID(),operationType:!0===o?1:2,itemList:[i]}}).then((function(){return a.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(s," ok")),r.isPinned!==o&&(r.isPinned=o,t._sortConversationListAndEmitEvent()),W_({conversationID:n})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),xi.error("".concat(s," failed. error:"),e),om(e)}))}},{key:"deleteLocalConversation",value:function(e){var t=this._conversationMap.has(e);xi.log("".concat(this._className,".deleteLocalConversation conversationID:").concat(e," has:").concat(t)),t&&(this._conversationMap.delete(e),this._roamingMessageKeyAndTimeMap.delete(e),this._setStorageConversationList(),this._messageListHandler.removeByConversationID(e),this._completedMap.delete(e),this._emitConversationUpdate(!0,!1))}},{key:"isMessageSentByCurrentInstance",value:function(e){return!(!this._messageListHandler.hasLocalMessage(e.conversationID,e.ID)&&!this.singlyLinkedList.has(e.random))}},{key:"modifyMessageList",value:function(e){if(e.startsWith(so.CONV_C2C)){var t=Date.now();this._messageListHandler.modifyMessageSentByPeer(e);var n=this.getModule(xc).getNickAndAvatarByUserID(this.getMyUserID());this._messageListHandler.modifyMessageSentByMe({conversationID:e,latestNick:n.nick,latestAvatar:n.avatar}),xi.log("".concat(this._className,".modifyMessageList conversationID:").concat(e," cost ").concat(Date.now()-t," ms"))}}},{key:"updateUserProfileSpecifiedKey",value:function(e){xi.log("".concat(this._className,".updateUserProfileSpecifiedKey options:"),e);var t=e.conversationID,n=e.nick,o=e.avatar;if(this._conversationMap.has(t)){var r=this._conversationMap.get(t).userProfile;Bi(n)&&r.nick!==n&&(r.nick=n),Bi(o)&&r.avatar!==o&&(r.avatar=o),this._emitConversationUpdate(!0,!1)}}},{key:"onMyProfileModified",value:function(e){var t=this,n=this.getLocalConversationList(),o=Date.now();n.forEach((function(n){t.modifyMessageSentByMe(xn({conversationID:n.conversationID},e))})),xi.log("".concat(this._className,".onMyProfileModified. modify all messages sent by me, cost ").concat(Date.now()-o," ms"))}},{key:"modifyMessageSentByMe",value:function(e){this._messageListHandler.modifyMessageSentByMe(e)}},{key:"getLatestMessageSentByMe",value:function(e){return this._messageListHandler.getLatestMessageSentByMe(e)}},{key:"modifyMessageSentByPeer",value:function(e,t){this._messageListHandler.modifyMessageSentByPeer(e,t)}},{key:"getLatestMessageSentByPeer",value:function(e){return this._messageListHandler.getLatestMessageSentByPeer(e)}},{key:"pushIntoNoticeResult",value:function(e,t){return!(!this._messageListHandler.pushIn(t)||this.singlyLinkedList.has(t.random))&&(e.push(t),!0)}},{key:"getGroupLocalLastMessageSequence",value:function(e){return this._messageListHandler.getGroupLocalLastMessageSequence(e)}},{key:"checkAndPatchRemark",value:function(){if(0!==this._conversationMap.size){var e=this.getModule(Bc);if(e){var t=Zn(this._conversationMap.values()).filter((function(e){return e.type===so.CONV_C2C}));if(0!==t.length){var n=!1,o=0;t.forEach((function(t){var r=t.conversationID.replace(so.CONV_C2C,"");if(e.isMyFriend(r)){var a=e.getFriendRemark(r);t.remark!==a&&(t.remark=a,o+=1,n=!0)}})),xi.log("".concat(this._className,".checkAndPatchRemark. c2c conversation count:").concat(t.length,", patched count:").concat(o)),n&&this._emitConversationUpdate(!0,!1)}}}}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset")),this._pagingStatus=Sc.NOT_START,this._messageListHandler.reset(),this._roamingMessageKeyAndTimeMap.clear(),this.singlyLinkedList.reset(),this._peerReadTimeMap.clear(),this._completedMap.clear(),this._conversationMap.clear(),this._pagingTimeStamp=0,this._pagingStartIndex=0,this._pagingPinnedTimeStamp=0,this._pagingPinnedStartIndex=0,this.resetReady()}}]),n}(il),Lm=function(){function e(t){Pn(this,e),this._groupModule=t,this._className="GroupTipsHandler",this._cachedGroupTipsMap=new Map,this._checkCountMap=new Map,this.MAX_CHECK_COUNT=4}return Un(e,[{key:"onCheckTimer",value:function(e){e%1==0&&this._cachedGroupTipsMap.size>0&&this._checkCachedGroupTips()}},{key:"_checkCachedGroupTips",value:function(){var e=this;this._cachedGroupTipsMap.forEach((function(t,n){var o=e._checkCountMap.get(n),r=e._groupModule.hasLocalGroup(n);xi.log("".concat(e._className,"._checkCachedGroupTips groupID:").concat(n," hasLocalGroup:").concat(r," checkCount:").concat(o)),r?(e._notifyCachedGroupTips(n),e._checkCountMap.delete(n),e._groupModule.deleteUnjoinedAVChatRoom(n)):o>=e.MAX_CHECK_COUNT?(e._deleteCachedGroupTips(n),e._checkCountMap.delete(n)):(o++,e._checkCountMap.set(n,o))}))}},{key:"onNewGroupTips",value:function(e){xi.debug("".concat(this._className,".onReceiveGroupTips count:").concat(e.dataList.length));var t=this.newGroupTipsStoredAndSummary(e),n=t.eventDataList,o=t.result,r=t.AVChatRoomMessageList;r.length>0&&this._groupModule.onAVChatRoomMessage(r),n.length>0&&(this._groupModule.getModule(jc).onNewMessage({conversationOptionsList:n,isInstantMessage:!0}),this._groupModule.updateNextMessageSeq(n)),o.length>0&&(this._groupModule.emitOuterEvent(ao.MESSAGE_RECEIVED,o),this.handleMessageList(o))}},{key:"newGroupTipsStoredAndSummary",value:function(e){for(var t=e.event,n=e.dataList,o=null,r=[],a=[],s={},i=[],u=0,c=n.length;u<c;u++){var l=n[u],d=l.groupProfile.groupID,p=this._groupModule.hasLocalGroup(d);if(p||!this._groupModule.isUnjoinedAVChatRoom(d))if(p)if(this._groupModule.isMessageFromAVChatroom(d)){var g=du(l);g.event=t,i.push(g)}else{l.currentUser=this._groupModule.getMyUserID(),l.conversationType=so.CONV_GROUP,(o=new Y_(l)).setElement({type:so.MSG_GRP_TIP,content:xn({},l.elements,{groupProfile:l.groupProfile})}),o.isSystemMessage=!1;var h=this._groupModule.getModule(jc),f=o.conversationID;if(6===t)o.setOnlineOnlyFlag(!0),a.push(o);else if(!h.pushIntoNoticeResult(a,o))continue;if(6!==t||!h.getLocalConversation(f))if(6!==t&&this._groupModule.getModule(sl).addMessageSequence({key:Pp,message:o}),Wi(s[f]))s[f]=r.push({conversationID:f,unreadCount:"in"===o.flow&&o.getOnlineOnlyFlag()?0:1,type:o.conversationType,subType:o.conversationSubType,lastMessage:o})-1;else{var _=s[f];r[_].type=o.conversationType,r[_].subType=o.conversationSubType,r[_].lastMessage=o,"in"!==o.flow||o.getOnlineOnlyFlag()||r[_].unreadCount++}}else this._cacheGroupTipsAndProbe({groupID:d,event:t,item:l})}return{eventDataList:r,result:a,AVChatRoomMessageList:i}}},{key:"handleMessageList",value:function(e){var t=this;e.forEach((function(e){switch(e.payload.operationType){case 1:t._onNewMemberComeIn(e);break;case 2:t._onMemberQuit(e);break;case 3:t._onMemberKickedOut(e);break;case 4:t._onMemberSetAdmin(e);break;case 5:t._onMemberCancelledAdmin(e);break;case 6:t._onGroupProfileModified(e);break;case 7:t._onMemberInfoModified(e);break;default:xi.warn("".concat(t._className,".handleMessageList unknown operationType:").concat(e.payload.operationType))}}))}},{key:"_onNewMemberComeIn",value:function(e){var t=e.payload,n=t.memberNum,o=t.groupProfile.groupID,r=this._groupModule.getLocalGroupProfile(o);r&&Ki(n)&&(r.memberNum=n)}},{key:"_onMemberQuit",value:function(e){var t=e.payload,n=t.memberNum,o=t.groupProfile.groupID,r=this._groupModule.getLocalGroupProfile(o);r&&Ki(n)&&(r.memberNum=n),this._groupModule.deleteLocalGroupMembers(o,e.payload.userIDList)}},{key:"_onMemberKickedOut",value:function(e){var t=e.payload,n=t.memberNum,o=t.groupProfile.groupID,r=this._groupModule.getLocalGroupProfile(o);r&&Ki(n)&&(r.memberNum=n),this._groupModule.deleteLocalGroupMembers(o,e.payload.userIDList)}},{key:"_onMemberSetAdmin",value:function(e){var t=e.payload.groupProfile.groupID,n=e.payload.userIDList,o=this._groupModule.getModule(Hc);n.forEach((function(e){var n=o.getLocalGroupMemberInfo(t,e);n&&n.updateRole(so.GRP_MBR_ROLE_ADMIN)}))}},{key:"_onMemberCancelledAdmin",value:function(e){var t=e.payload.groupProfile.groupID,n=e.payload.userIDList,o=this._groupModule.getModule(Hc);n.forEach((function(e){var n=o.getLocalGroupMemberInfo(t,e);n&&n.updateRole(so.GRP_MBR_ROLE_MEMBER)}))}},{key:"_onGroupProfileModified",value:function(e){var t=this,n=e.payload,o=n.newGroupProfile,r=n.groupProfile.groupID,a=this._groupModule.getLocalGroupProfile(r);Object.keys(o).forEach((function(e){switch(e){case"ownerID":t._ownerChanged(a,o);break;default:a[e]=o[e]}})),this._groupModule.emitGroupListUpdate(!0,!0)}},{key:"_ownerChanged",value:function(e,t){var n=e.groupID,o=this._groupModule.getLocalGroupProfile(n),r=this.tim.context.identifier;if(r===t.ownerID){o.updateGroup({selfInfo:{role:so.GRP_MBR_ROLE_OWNER}});var a=this._groupModule.getModule(Hc),s=a.getLocalGroupMemberInfo(n,r),i=this._groupModule.getLocalGroupProfile(n).ownerID,u=a.getLocalGroupMemberInfo(n,i);s&&s.updateRole(so.GRP_MBR_ROLE_OWNER),u&&u.updateRole(so.GRP_MBR_ROLE_MEMBER)}}},{key:"_onMemberInfoModified",value:function(e){var t=e.payload.groupProfile.groupID,n=this._groupModule.getModule(Hc);e.payload.memberList.forEach((function(e){var o=n.getLocalGroupMemberInfo(t,e.userID);o&&e.muteTime&&o.updateMuteUntil(e.muteTime)}))}},{key:"_cacheGroupTips",value:function(e,t){this._cachedGroupTipsMap.has(e)||this._cachedGroupTipsMap.set(e,[]),this._cachedGroupTipsMap.get(e).push(t)}},{key:"_deleteCachedGroupTips",value:function(e){this._cachedGroupTipsMap.has(e)&&this._cachedGroupTipsMap.delete(e)}},{key:"_notifyCachedGroupTips",value:function(e){var t=this,n=this._cachedGroupTipsMap.get(e)||[];n.forEach((function(e){t.onNewGroupTips(e)})),this._deleteCachedGroupTips(e),xi.log("".concat(this._className,"._notifyCachedGroupTips groupID:").concat(e," count:").concat(n.length))}},{key:"_cacheGroupTipsAndProbe",value:function(e){var t=this,n=e.groupID,o=e.event,r=e.item;this._cacheGroupTips(n,{event:o,dataList:[r]}),this._groupModule.getGroupSimplifiedInfo(n).then((function(e){e.type===so.GRP_AVCHATROOM?t._groupModule.hasLocalGroup(n)?t._notifyCachedGroupTips(n):t._groupModule.setUnjoinedAVChatRoom(n):(t._groupModule.updateGroupMap([e]),t._notifyCachedGroupTips(n))})),this._checkCountMap.has(n)||this._checkCountMap.set(n,0),xi.log("".concat(this._className,"._cacheGroupTipsAndProbe groupID:").concat(n))}},{key:"reset",value:function(){this._cachedGroupTipsMap.clear(),this._checkCountMap.clear()}}]),e}(),Rm=[].push,bm=Math.min,wm=!a((function(){return!RegExp(4294967295,"y")}));ys("split",2,(function(e,t,n){var o;return o="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(e,n){var o=String(_(this)),r=void 0===n?4294967295:n>>>0;if(0===r)return[];if(void 0===e)return[o];if(!ls(e))return t.call(o,e,r);for(var a,s,i,u=[],c=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),l=0,d=new RegExp(e.source,c+"g");(a=os.call(d,o))&&!((s=d.lastIndex)>l&&(u.push(o.slice(l,a.index)),a.length>1&&a.index<o.length&&Rm.apply(u,a.slice(1)),i=a[0].length,l=s,u.length>=r));)d.lastIndex===a.index&&d.lastIndex++;return l===o.length?!i&&d.test("")||u.push(""):u.push(o.slice(l)),u.length>r?u.slice(0,r):u}:"0".split(void 0,0).length?function(e,n){return void 0===e&&0===n?[]:t.call(this,e,n)}:t,[function(t,n){var r=_(this),a=null==t?void 0:t[e];return void 0!==a?a.call(t,r,n):o.call(String(r),t,n)},function(e,r){var a=n(o,e,this,r,o!==t);if(a.done)return a.value;var s=A(e),i=String(this),u=wr(s,RegExp),c=s.unicode,l=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.unicode?"u":"")+(wm?"y":"g"),d=new u(wm?s:"^(?:"+s.source+")",l),p=void 0===r?4294967295:r>>>0;if(0===p)return[];if(0===i.length)return null===Ss(d,i)?[i]:[];for(var g=0,h=0,f=[];h<i.length;){d.lastIndex=wm?h:0;var _,m=Ss(d,wm?i:i.slice(h));if(null===m||(_=bm(de(d.lastIndex+(wm?0:h)),i.length))===g)h=Ts(i,h,c);else{if(f.push(i.slice(g,h)),f.length===p)return f;for(var v=1;v<=m.length-1;v++)if(f.push(m[v]),f.length===p)return f;h=g=_}}return f.push(i.slice(g)),f}]}),!wm);var Pm=function(){function e(t){Pn(this,e),this._groupModule=t,this._className="CommonGroupHandler",this.tempConversationList=null,this._cachedGroupMessageMap=new Map,this._checkCountMap=new Map,this.MAX_CHECK_COUNT=4,t.getInnerEmitterInstance().once(hm.CONTEXT_A2KEY_AND_TINYID_UPDATED,this._initGroupList,this)}return Un(e,[{key:"onCheckTimer",value:function(e){e%1==0&&this._cachedGroupMessageMap.size>0&&this._checkCachedGroupMessage()}},{key:"_checkCachedGroupMessage",value:function(){var e=this;this._cachedGroupMessageMap.forEach((function(t,n){var o=e._checkCountMap.get(n),r=e._groupModule.hasLocalGroup(n);xi.log("".concat(e._className,"._checkCachedGroupMessage groupID:").concat(n," hasLocalGroup:").concat(r," checkCount:").concat(o)),r?(e._notifyCachedGroupMessage(n),e._checkCountMap.delete(n),e._groupModule.deleteUnjoinedAVChatRoom(n)):o>=e.MAX_CHECK_COUNT?(e._deleteCachedGroupMessage(n),e._checkCountMap.delete(n)):(o++,e._checkCountMap.set(n,o))}))}},{key:"_initGroupList",value:function(){var e=this;xi.log("".concat(this._className,"._initGroupList"));var t=new Kp(bg),n=this._groupModule.getStorageGroupList();if(Yi(n)&&n.length>0){n.forEach((function(t){e._groupModule.initGroupMap(t)})),this._groupModule.emitGroupListUpdate(!0,!1);var o=this._groupModule.getLocalGroupList().length;t.setNetworkType(this._groupModule.getNetworkType()).setMessage("group count:".concat(o)).end()}else t.setNetworkType(this._groupModule.getNetworkType()).setMessage("group count:0").end();xi.log("".concat(this._className,"._initGroupList ok")),this.getGroupList()}},{key:"handleUpdateGroupLastMessage",value:function(e){var t="".concat(this._className,".handleUpdateGroupLastMessage");if(xi.debug("".concat(t," conversation count:").concat(e.length,", local group count:").concat(this._groupModule.getLocalGroupList().length)),0!==this._groupModule.getGroupMap().size){for(var n,o,r,a=!1,s=0,i=e.length;s<i;s++)(n=e[s]).type===so.CONV_GROUP&&(o=n.conversationID.split(/^GROUP/)[1],(r=this._groupModule.getLocalGroupProfile(o))&&(r.lastMessage=n.lastMessage,a=!0));a&&(this._groupModule.sortLocalGroupList(),this._groupModule.emitGroupListUpdate(!0,!1))}else this.tempConversationList=e}},{key:"onNewGroupMessage",value:function(e){xi.debug("".concat(this._className,".onNewGroupMessage count:").concat(e.dataList.length));var t=this._newGroupMessageStoredAndSummary(e),n=t.conversationOptionsList,o=t.messageList,r=t.AVChatRoomMessageList;r.length>0&&this._groupModule.onAVChatRoomMessage(r),this._groupModule.filterModifiedMessage(o),n.length>0&&(this._groupModule.getModule(jc).onNewMessage({conversationOptionsList:n,isInstantMessage:!0}),this._groupModule.updateNextMessageSeq(n));var a=this._groupModule.filterUnmodifiedMessage(o);a.length>0&&this._groupModule.emitOuterEvent(ao.MESSAGE_RECEIVED,a),o.length=0}},{key:"_newGroupMessageStoredAndSummary",value:function(e){var t=e.dataList,n=e.event,o=e.isInstantMessage,r=null,a=[],s=[],i=[],u={},c=so.CONV_GROUP,l=this._groupModule.getModule(Xc),d=t.length;d>1&&t.sort((function(e,t){return e.sequence-t.sequence}));for(var p=0;p<d;p++){var g=t[p],h=g.groupProfile.groupID,f=this._groupModule.hasLocalGroup(h);if(f||!this._groupModule.isUnjoinedAVChatRoom(h))if(f)if(this._groupModule.isMessageFromAVChatroom(h)){var _=du(g);_.event=n,i.push(_)}else{g.currentUser=this._groupModule.getMyUserID(),g.conversationType=c,g.isSystemMessage=!!g.isSystemMessage,r=new Y_(g),g.elements=l.parseElements(g.elements,g.from),r.setElement(g.elements);var m=1===t[p].isModified,v=this._groupModule.getModule(jc);v.isMessageSentByCurrentInstance(r)?r.isModified=m:m=!1;var M=this._groupModule.getModule(sl);if(o&&M.addMessageDelay({currentTime:Date.now(),time:r.time}),1===g.onlineOnlyFlag)r.setOnlineOnlyFlag(!0),s.push(r);else{if(!v.pushIntoMessageList(s,r,m))continue;M.addMessageSequence({key:Pp,message:r});var y=r.conversationID;if(Wi(u[y]))u[y]=a.push({conversationID:y,unreadCount:"out"===r.flow?0:1,type:r.conversationType,subType:r.conversationSubType,lastMessage:r})-1;else{var I=u[y];a[I].type=r.conversationType,a[I].subType=r.conversationSubType,a[I].lastMessage=r,"in"===r.flow&&a[I].unreadCount++}}}else this._cacheGroupMessageAndProbe({groupID:h,event:n,item:g})}return{conversationOptionsList:a,messageList:s,AVChatRoomMessageList:i}}},{key:"onGroupMessageRevoked",value:function(e){xi.debug("".concat(this._className,".onGroupMessageRevoked nums:").concat(e.dataList.length));var t=this._groupModule.getModule(jc),n=[],o=null;e.dataList.forEach((function(e){var r=e.elements.revokedInfos;Wi(r)||r.forEach((function(e){(o=t.revoke("GROUP".concat(e.groupID),e.sequence,e.random))&&n.push(o)}))})),0!==n.length&&(t.onMessageRevoked(n),this._groupModule.emitOuterEvent(ao.MESSAGE_REVOKED,n))}},{key:"_groupListTreeShaking",value:function(e){for(var t=new Map(Zn(this._groupModule.getGroupMap())),n=0,o=e.length;n<o;n++)t.delete(e[n].groupID);this._groupModule.hasJoinedAVChatRoom()&&this._groupModule.getJoinedAVChatRoom().forEach((function(e){t.delete(e)}));for(var r=Zn(t.keys()),a=0,s=r.length;a<s;a++)this._groupModule.deleteGroup(r[a])}},{key:"getGroupList",value:function(e){var t=this,n="".concat(this._className,".getGroupList"),o=new Kp(Lg);xi.log("".concat(n));var r={introduction:"Introduction",notification:"Notification",createTime:"CreateTime",ownerID:"Owner_Account",lastInfoTime:"LastInfoTime",memberNum:"MemberNum",maxMemberNum:"MaxMemberNum",joinOption:"ApplyJoinOption",muteAllMembers:"ShutUpAllMember"},a=["Type","Name","FaceUrl","NextMsgSeq","LastMsgTime"],s=[];return e&&e.groupProfileFilter&&e.groupProfileFilter.forEach((function(e){r[e]&&a.push(r[e])})),this._pagingGetGroupList({limit:50,offset:0,groupBaseInfoFilter:a,groupList:s}).then((function(){xi.log("".concat(n," ok. count:").concat(s.length)),t._groupListTreeShaking(s),t._groupModule.updateGroupMap(s);var e=t._groupModule.getLocalGroupList().length;return o.setNetworkType(t._groupModule.getNetworkType()).setMessage("remote count:".concat(s.length,", after tree shaking, local count:").concat(e)).end(),t.tempConversationList&&(xi.log("".concat(n," update last message with tempConversationList, count:").concat(t.tempConversationList.length)),t.handleUpdateGroupLastMessage({data:t.tempConversationList}),t.tempConversationList=null),t._groupModule.emitGroupListUpdate(),W_({groupList:t._groupModule.getLocalGroupList()})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),xi.error("".concat(n," failed. error:"),e),om(e)}))}},{key:"_pagingGetGroupList",value:function(e){var t=this,n="".concat(this._className,"._pagingGetGroupList"),o=e.limit,r=e.offset,a=e.groupBaseInfoFilter,s=e.groupList,i=new Kp(Gg);return this._groupModule.request({protocolName:Ol,requestData:{memberAccount:this._groupModule.getMyUserID(),limit:o,offset:r,responseFilter:{groupBaseInfoFilter:a,selfInfoFilter:["Role","JoinTime","MsgFlag"]}}}).then((function(e){var u=e.data,c=u.groups,l=u.totalCount;s.push.apply(s,Zn(c));var d=r+o,p=!(l>d);return i.setNetworkType(t._groupModule.getNetworkType()).setMessage("offset:".concat(r," totalCount:").concat(l," isCompleted:").concat(p," currentCount:").concat(s.length)).end(),p?(xi.log("".concat(n," ok. totalCount:").concat(l)),W_({groupList:s})):(r=d,t._pagingGetGroupList({limit:o,offset:r,groupBaseInfoFilter:a,groupList:s}))})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];i.setError(e,o,r).end()})),om(e)}))}},{key:"_cacheGroupMessage",value:function(e,t){this._cachedGroupMessageMap.has(e)||this._cachedGroupMessageMap.set(e,[]),this._cachedGroupMessageMap.get(e).push(t)}},{key:"_deleteCachedGroupMessage",value:function(e){this._cachedGroupMessageMap.has(e)&&this._cachedGroupMessageMap.delete(e)}},{key:"_notifyCachedGroupMessage",value:function(e){var t=this,n=this._cachedGroupMessageMap.get(e)||[];n.forEach((function(e){t.onNewGroupMessage(e)})),this._deleteCachedGroupMessage(e),xi.log("".concat(this._className,"._notifyCachedGroupMessage groupID:").concat(e," count:").concat(n.length))}},{key:"_cacheGroupMessageAndProbe",value:function(e){var t=this,n=e.groupID,o=e.event,r=e.item;this._cacheGroupMessage(n,{event:o,dataList:[r]}),this._groupModule.getGroupSimplifiedInfo(n).then((function(e){e.type===so.GRP_AVCHATROOM?t._groupModule.hasLocalGroup(n)?t._notifyCachedGroupMessage(n):t._groupModule.setUnjoinedAVChatRoom(n):(t._groupModule.updateGroupMap([e]),t._notifyCachedGroupMessage(n))})),this._checkCountMap.has(n)||this._checkCountMap.set(n,0),xi.log("".concat(this._className,"._cacheGroupMessageAndProbe groupID:").concat(n))}},{key:"reset",value:function(){this._cachedGroupMessageMap.clear(),this._checkCountMap.clear(),this._groupModule.getInnerEmitterInstance().once(hm.CONTEXT_A2KEY_AND_TINYID_UPDATED,this._initGroupList,this)}}]),e}(),Gm={1:"init",2:"modify",3:"clear",4:"delete"},Um=function(){function e(t){Pn(this,e),this._groupModule=t,this._className="GroupAttributesHandler",this._groupAttributesMap=new Map,this.CACHE_EXPIRE_TIME=3e4,this._groupModule.getInnerEmitterInstance().on(hm.CLOUD_CONFIG_UPDATED,this._onCloudConfigUpdated,this)}return Un(e,[{key:"_onCloudConfigUpdated",value:function(){var e=this._groupModule.getCloudConfig("grp_attr_cache_time");Wi(e)||(this.CACHE_EXPIRE_TIME=Number(e))}},{key:"updateLocalMainSequenceOnReconnected",value:function(){this._groupAttributesMap.forEach((function(e){e.localMainSequence=0}))}},{key:"onGroupAttributesUpdated",value:function(e){var t=this,n=e.groupID,o=e.groupAttributeOption,r=o.mainSequence,a=o.hasChangedAttributeInfo,s=o.groupAttributeList,i=void 0===s?[]:s,u=o.operationType;if(xi.log("".concat(this._className,".onGroupAttributesUpdated. hasChangedAttributeInfo:").concat(a," operationType:").concat(u)),1===a){if(4===u){var c=[];i.forEach((function(e){c.push(e.key)})),i=Zn(c),c=null}return this._refreshCachedGroupAttributes({groupID:n,remoteMainSequence:r,groupAttributeList:i,operationType:Gm[u]}),void this._emitGroupAttributesUpdated(n)}if(this._groupAttributesMap.has(n)){var l=this._groupAttributesMap.get(n).avChatRoomKey;this._getGroupAttributes({groupID:n,avChatRoomKey:l}).then((function(){t._emitGroupAttributesUpdated(n)}))}}},{key:"initGroupAttributesCache",value:function(e){var t=e.groupID,n=e.avChatRoomKey;this._groupAttributesMap.set(t,{lastUpdateTime:0,localMainSequence:0,remoteMainSequence:0,attributes:new Map,avChatRoomKey:n}),xi.log("".concat(this._className,".initGroupAttributesCache groupID:").concat(t," avChatRoomKey:").concat(n))}},{key:"initGroupAttributes",value:function(e){var t=this,n=e.groupID,o=e.groupAttributes,r=this._checkCachedGroupAttributes({groupID:n,funcName:"initGroupAttributes"});if(!0!==r)return om(r);var a=this._groupAttributesMap.get(n),s=a.remoteMainSequence,i=a.avChatRoomKey,u=new Kp(qg);return u.setMessage("groupID:".concat(n," mainSequence:").concat(s," groupAttributes:").concat(JSON.stringify(o))),this._groupModule.request({protocolName:Ql,requestData:{groupID:n,avChatRoomKey:i,mainSequence:s,groupAttributeList:this._transformGroupAttributes(o)}}).then((function(e){var r=e.data,a=r.mainSequence,s=Zn(r.groupAttributeList);return s.forEach((function(e){e.value=o[e.key]})),t._refreshCachedGroupAttributes({groupID:n,remoteMainSequence:a,groupAttributeList:s,operationType:"init"}),u.setNetworkType(t._groupModule.getNetworkType()).end(),xi.log("".concat(t._className,".initGroupAttributes ok. groupID:").concat(n)),W_({groupAttributes:o})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];u.setError(e,o,r).end()})),om(e)}))}},{key:"setGroupAttributes",value:function(e){var t=this,n=e.groupID,o=e.groupAttributes,r=this._checkCachedGroupAttributes({groupID:n,funcName:"setGroupAttributes"});if(!0!==r)return om(r);var a=this._groupAttributesMap.get(n),s=a.remoteMainSequence,i=a.avChatRoomKey,u=a.attributes,c=this._transformGroupAttributes(o);c.forEach((function(e){var t=e.key;e.sequence=0,u.has(t)&&(e.sequence=u.get(t).sequence)}));var l=new Kp(xg);return l.setMessage("groupID:".concat(n," mainSequence:").concat(s," groupAttributes:").concat(JSON.stringify(o))),this._groupModule.request({protocolName:Zl,requestData:{groupID:n,avChatRoomKey:i,mainSequence:s,groupAttributeList:c}}).then((function(e){var r=e.data,a=r.mainSequence,s=Zn(r.groupAttributeList);return s.forEach((function(e){e.value=o[e.key]})),t._refreshCachedGroupAttributes({groupID:n,remoteMainSequence:a,groupAttributeList:s,operationType:"modify"}),l.setNetworkType(t._groupModule.getNetworkType()).end(),xi.log("".concat(t._className,".setGroupAttributes ok. groupID:").concat(n)),W_({groupAttributes:o})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];l.setError(e,o,r).end()})),om(e)}))}},{key:"deleteGroupAttributes",value:function(e){var t=this,n=e.groupID,o=e.keyList,r=void 0===o?[]:o,a=this._checkCachedGroupAttributes({groupID:n,funcName:"deleteGroupAttributes"});if(!0!==a)return om(a);var s=this._groupAttributesMap.get(n),i=s.remoteMainSequence,u=s.avChatRoomKey,c=s.attributes,l=Zn(c.keys()),d=ed,p="clear",g={groupID:n,avChatRoomKey:u,mainSequence:i};if(r.length>0){var h=[];l=[],d=$l,p="delete",r.forEach((function(e){var t=0;c.has(e)&&(t=c.get(e).sequence,l.push(e)),h.push({key:e,sequence:t})})),g.groupAttributeList=h}var f=new Kp(Vg);return f.setMessage("groupID:".concat(n," mainSequence:").concat(i," keyList:").concat(r," protocolName:").concat(d)),this._groupModule.request({protocolName:d,requestData:g}).then((function(e){var o=e.data.mainSequence;return t._refreshCachedGroupAttributes({groupID:n,remoteMainSequence:o,groupAttributeList:r,operationType:p}),f.setNetworkType(t._groupModule.getNetworkType()).end(),xi.log("".concat(t._className,".deleteGroupAttributes ok. groupID:").concat(n)),W_({keyList:l})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];f.setError(e,o,r).end()})),om(e)}))}},{key:"getGroupAttributes",value:function(e){var t=this,n=e.groupID,o=this._checkCachedGroupAttributes({groupID:n,funcName:"getGroupAttributes"});if(!0!==o)return om(o);var r=this._groupAttributesMap.get(n),a=r.avChatRoomKey,s=r.lastUpdateTime,i=r.localMainSequence,u=r.remoteMainSequence,c=new Kp(Kg);if(c.setMessage("groupID:".concat(n," localMainSequence:").concat(i," remoteMainSequence:").concat(u," keyList:").concat(e.keyList)),Date.now()-s>=this.CACHE_EXPIRE_TIME||i<u)return this._getGroupAttributes({groupID:n,avChatRoomKey:a}).then((function(o){c.setMoreMessage("get attributes from remote. count:".concat(o.length)).setNetworkType(t._groupModule.getNetworkType()).end(),xi.log("".concat(t._className,".getGroupAttributes from remote. groupID:").concat(n));var r=t._getLocalGroupAttributes(e);return W_({groupAttributes:r})})).catch((function(e){return t._groupModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];c.setError(e,o,r).end()})),om(e)}));c.setMoreMessage("get attributes from cache").setNetworkType(this._groupModule.getNetworkType()).end(),xi.log("".concat(this._className,".getGroupAttributes from cache. groupID:").concat(n));var l=this._getLocalGroupAttributes(e);return nm({groupAttributes:l})}},{key:"_getGroupAttributes",value:function(e){var t=this;return this._groupModule.request({protocolName:td,requestData:xn({},e)}).then((function(n){var o=n.data,r=o.mainSequence,a=o.groupAttributeList,s=Zn(a);return xi.log("".concat(t._className,"._getGroupAttributes ok. groupID:").concat(e.groupID)),t._refreshCachedGroupAttributes({groupID:e.groupID,remoteMainSequence:r,groupAttributeList:s,operationType:"get"}),a})).catch((function(e){return om(e)}))}},{key:"_getLocalGroupAttributes",value:function(e){var t=e.groupID,n=e.keyList,o=void 0===n?[]:n,r={};if(!this._groupAttributesMap.has(t))return r;var a=this._groupAttributesMap.get(t).attributes;if(o.length>0)o.forEach((function(e){a.has(e)&&(r[e]=a.get(e).value)}));else{var s,i=ro(a.keys());try{for(i.s();!(s=i.n()).done;){var u=s.value;r[u]=a.get(u).value}}catch(d){i.e(d)}finally{i.f()}}return r}},{key:"_refreshCachedGroupAttributes",value:function(e){var t=e.groupID,n=e.remoteMainSequence,o=e.groupAttributeList,r=e.operationType;if(this._groupAttributesMap.has(t)){var a=this._groupAttributesMap.get(t),s=a.localMainSequence;if("get"===r||n-s==1)a.remoteMainSequence=n,a.localMainSequence=n,a.lastUpdateTime=Date.now(),this._updateCachedAttributes({groupAttributes:a,groupAttributeList:o,operationType:r});else{if(s===n)return;a.remoteMainSequence=n}this._groupAttributesMap.set(t,a);var i="operationType:".concat(r," localMainSequence:").concat(s," remoteMainSequence:").concat(n);xi.log("".concat(this._className,"._refreshCachedGroupAttributes. ").concat(i))}}},{key:"_updateCachedAttributes",value:function(e){var t=e.groupAttributes,n=e.groupAttributeList,o=e.operationType;"clear"!==o?"delete"!==o?("init"===o&&t.attributes.clear(),n.forEach((function(e){var n=e.key,o=e.value,r=e.sequence;t.attributes.set(n,{value:o,sequence:r})}))):n.forEach((function(e){t.attributes.delete(e)})):t.attributes.clear()}},{key:"_checkCachedGroupAttributes",value:function(e){var t=e.groupID,n=e.funcName;if(this._groupModule.hasLocalGroup(t)&&this._groupModule.getLocalGroupProfile(t).type!==so.GRP_AVCHATROOM)return xi.warn("".concat(this._className,"._checkCachedGroupAttributes. ").concat("非直播群不能使用群属性 API")),new $_({code:vd.CANNOT_USE_GRP_ATTR_NOT_AVCHATROOM,message:"非直播群不能使用群属性 API"});var o=this._groupAttributesMap.get(t);if(Wi(o)){var r="如果 groupID:".concat(t," 是直播群，使用 ").concat(n," 前先使用 joinGroup 接口申请加入群组，详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#joinGroup");return xi.warn("".concat(this._className,"._checkCachedGroupAttributes. ").concat(r)),new $_({code:vd.CANNOT_USE_GRP_ATTR_AVCHATROOM_UNJOIN,message:r})}return!0}},{key:"_transformGroupAttributes",value:function(e){var t=[];return Object.keys(e).forEach((function(n){t.push({key:n,value:e[n]})})),t}},{key:"_emitGroupAttributesUpdated",value:function(e){var t=this._getLocalGroupAttributes({groupID:e});this._groupModule.emitOuterEvent(ao.GROUP_ATTRIBUTES_UPDATED,{groupID:e,groupAttributes:t})}},{key:"reset",value:function(){this._groupAttributesMap.clear(),this.CACHE_EXPIRE_TIME=3e4}}]),e}(),Fm=function(){function e(t){Pn(this,e);var n=t.manager,o=t.groupID,r=t.onInit,a=t.onSuccess,s=t.onFail;this._className="Polling",this._manager=n,this._groupModule=n._groupModule,this._onInit=r,this._onSuccess=a,this._onFail=s,this._groupID=o,this._timeoutID=-1,this._isRunning=!1}return Un(e,[{key:"start",value:function(){xi.log("".concat(this._className,".start pollingInterval:").concat(this._manager.getPollingInterval())),this._isRunning=!0,this._request()}},{key:"isRunning",value:function(){return this._isRunning}},{key:"_request",value:function(){var e=this,t=this._onInit(this._groupID),n=Wl;this._groupModule.isLoggedIn()||(n=zl),this._groupModule.request({protocolName:n,requestData:t}).then((function(t){e._onSuccess(e._groupID,t),e.isRunning()&&(clearTimeout(e._timeoutID),e._timeoutID=setTimeout(e._request.bind(e),e._manager.getPollingInterval()))})).catch((function(t){e._onFail(e._groupID,t),e.isRunning()&&(clearTimeout(e._timeoutID),e._timeoutID=setTimeout(e._request.bind(e),e._manager.MAX_POLLING_INTERVAL))}))}},{key:"stop",value:function(){xi.log("".concat(this._className,".stop")),this._timeoutID>0&&(clearTimeout(this._timeoutID),this._timeoutID=-1),this._isRunning=!1}}]),e}(),qm={3:!0,4:!0,5:!0,6:!0},xm=function(){function e(t){Pn(this,e),this._groupModule=t,this._className="AVChatRoomHandler",this._joinedGroupMap=new Map,this._pollingRequestInfoMap=new Map,this._pollingInstanceMap=new Map,this.sequencesLinkedList=new Em(100),this.messageIDLinkedList=new Em(100),this.receivedMessageCount=0,this._reportMessageStackedCount=0,this._onlineMemberCountMap=new Map,this.DEFAULT_EXPIRE_TIME=60,this.DEFAULT_POLLING_INTERVAL=300,this.MAX_POLLING_INTERVAL=2e3,this._pollingInterval=this.DEFAULT_POLLING_INTERVAL}return Un(e,[{key:"hasJoinedAVChatRoom",value:function(){return this._joinedGroupMap.size>0}},{key:"checkJoinedAVChatRoomByID",value:function(e){return this._joinedGroupMap.has(e)}},{key:"getJoinedAVChatRoom",value:function(){return this._joinedGroupMap.size>0?Zn(this._joinedGroupMap.keys()):null}},{key:"_updateRequestData",value:function(e){return xn({},this._pollingRequestInfoMap.get(e))}},{key:"_handleSuccess",value:function(e,t){var n=t.data,o=n.key,r=n.nextSeq,a=n.rspMsgList;if(0!==n.errorCode){var s=this._pollingRequestInfoMap.get(e),i=new Kp(Zg),u=s?"".concat(s.key,"-").concat(s.startSeq):"requestInfo is undefined";i.setMessage("".concat(e,"-").concat(u,"-").concat(t.errorInfo)).setCode(t.errorCode).setNetworkType(this._groupModule.getNetworkType()).end(!0)}else{if(!this.checkJoinedAVChatRoomByID(e))return;Bi(o)&&Ki(r)&&this._pollingRequestInfoMap.set(e,{key:o,startSeq:r}),Yi(a)&&a.length>0&&(a.forEach((function(e){e.to=e.groupID})),this.onMessage(a))}}},{key:"_handleFailure",value:function(e,t){}},{key:"onMessage",value:function(e){if(Yi(e)&&0!==e.length){var t=null,n=[],o=this._getModule(jc),r=e.length;r>1&&e.sort((function(e,t){return e.sequence-t.sequence}));for(var a=this._getModule(Yc),s=0;s<r;s++)if(qm[e[s].event]){this.receivedMessageCount+=1,t=this.packMessage(e[s],e[s].event);var i=1===e[s].isModified;if((a.isUnlimitedAVChatRoom()||!this.sequencesLinkedList.has(t.sequence))&&!this.messageIDLinkedList.has(t.ID)){var u=t.conversationID;if(this.receivedMessageCount%40==0&&this._getModule(nl).detectMessageLoss(u,this.sequencesLinkedList.data()),null!==this.sequencesLinkedList.tail()){var c=this.sequencesLinkedList.tail().value,l=t.sequence-c;l>1&&l<=20?this._getModule(nl).onMessageMaybeLost(u,c+1,l-1):l<-1&&l>=-20&&this._getModule(nl).onMessageMaybeLost(u,t.sequence+1,Math.abs(l)-1)}this.sequencesLinkedList.set(t.sequence),this.messageIDLinkedList.set(t.ID);var d=!1;if(this._isMessageSentByCurrentInstance(t)?i&&(d=!0,t.isModified=i,o.updateMessageIsModifiedProperty(t)):d=!0,d){if(t.conversationType,so.CONV_SYSTEM,t.conversationType!==so.CONV_SYSTEM){var p=this._getModule(sl),g=t.conversationID.replace(so.CONV_GROUP,"");this._pollingInstanceMap.has(g)?p.addMessageSequence({key:Up,message:t}):(t.type!==so.MSG_GRP_TIP&&p.addMessageDelay({currentTime:Date.now(),time:t.time}),p.addMessageSequence({key:Gp,message:t}))}n.push(t)}}}else xi.warn("".concat(this._className,".onMessage 未处理的 event 类型: ").concat(e[s].event));if(0!==n.length){this._groupModule.filterModifiedMessage(n);var h=this.packConversationOption(n);h.length>0&&this._getModule(jc).onNewMessage({conversationOptionsList:h,isInstantMessage:!0}),xi.debug("".concat(this._className,".onMessage count:").concat(n.length)),this._checkMessageStacked(n);var f=this._groupModule.filterUnmodifiedMessage(n);f.length>0&&this._groupModule.emitOuterEvent(ao.MESSAGE_RECEIVED,f),n.length=0}}}},{key:"_checkMessageStacked",value:function(e){var t=e.length;t>=100&&(xi.warn("".concat(this._className,"._checkMessageStacked 直播群消息堆积数:").concat(e.length,'！可能会导致微信小程序渲染时遇到 "Dom limit exceeded" 的错误，建议接入侧此时只渲染最近的10条消息')),this._reportMessageStackedCount<5&&(new Kp(eh).setNetworkType(this._groupModule.getNetworkType()).setMessage("count:".concat(t," groupID:").concat(Zn(this._joinedGroupMap.keys()))).setLevel("warning").end(),this._reportMessageStackedCount+=1))}},{key:"_isMessageSentByCurrentInstance",value:function(e){return!!this._getModule(jc).isMessageSentByCurrentInstance(e)}},{key:"packMessage",value:function(e,t){e.currentUser=this._groupModule.getMyUserID(),e.conversationType=5===t?so.CONV_SYSTEM:so.CONV_GROUP,e.isSystemMessage=!!e.isSystemMessage;var n=new Y_(e),o=this.packElements(e,t);return n.setElement(o),n}},{key:"packElements",value:function(e,t){return 4===t||6===t?(this._updateMemberCountByGroupTips(e),this._onGroupAttributesUpdated(e),{type:so.MSG_GRP_TIP,content:xn({},e.elements,{groupProfile:e.groupProfile})}):5===t?{type:so.MSG_GRP_SYS_NOTICE,content:xn({},e.elements,{groupProfile:e.groupProfile})}:this._getModule(Xc).parseElements(e.elements,e.from)}},{key:"packConversationOption",value:function(e){for(var t=new Map,n=0;n<e.length;n++){var o=e[n],r=o.conversationID;if(t.has(r)){var a=t.get(r);a.lastMessage=o,"in"===o.flow&&a.unreadCount++}else t.set(r,{conversationID:o.conversationID,unreadCount:"out"===o.flow?0:1,type:o.conversationType,subType:o.conversationSubType,lastMessage:o})}return Zn(t.values())}},{key:"_updateMemberCountByGroupTips",value:function(e){var t=e.groupProfile.groupID,n=e.elements.onlineMemberInfo,o=void 0===n?void 0:n;if(!Au(o)){var r=o.onlineMemberNum,a=void 0===r?0:r,s=o.expireTime,i=void 0===s?this.DEFAULT_EXPIRE_TIME:s,u=this._onlineMemberCountMap.get(t)||{},c=Date.now();Au(u)?Object.assign(u,{lastReqTime:0,lastSyncTime:0,latestUpdateTime:c,memberCount:a,expireTime:i}):(u.latestUpdateTime=c,u.memberCount=a),xi.debug("".concat(this._className,"._updateMemberCountByGroupTips info:"),u),this._onlineMemberCountMap.set(t,u)}}},{key:"start",value:function(e){if(this._pollingInstanceMap.has(e)){var t=this._pollingInstanceMap.get(e);t.isRunning()||t.start()}else{var n=new Fm({manager:this,groupID:e,onInit:this._updateRequestData.bind(this),onSuccess:this._handleSuccess.bind(this),onFail:this._handleFailure.bind(this)});n.start(),this._pollingInstanceMap.set(e,n),xi.log("".concat(this._className,".start groupID:").concat(e))}}},{key:"handleJoinResult",value:function(e){var t=this;return this._preCheck().then((function(){var n=e.longPollingKey,o=e.group,r=o.groupID;return t._joinedGroupMap.set(r,o),t._groupModule.updateGroupMap([o]),t._groupModule.deleteUnjoinedAVChatRoom(r),t._groupModule.emitGroupListUpdate(!0,!1),Wi(n)?nm({status:E_,group:o}):Promise.resolve()}))}},{key:"startRunLoop",value:function(e){var t=this;return this.handleJoinResult(e).then((function(){var n=e.longPollingKey,o=e.group,r=o.groupID;return t._pollingRequestInfoMap.set(r,{key:n,startSeq:0}),t.start(r),t._groupModule.isLoggedIn()?nm({status:E_,group:o}):nm({status:E_})}))}},{key:"_preCheck",value:function(){if(this._getModule(Yc).isUnlimitedAVChatRoom())return Promise.resolve();if(!this.hasJoinedAVChatRoom())return Promise.resolve();var e=Qn(this._joinedGroupMap.entries().next().value,2),t=e[0],n=e[1];if(this._groupModule.isLoggedIn()){if(n.selfInfo.role!==so.GRP_MBR_ROLE_OWNER&&n.ownerID!==this._groupModule.getMyUserID())return this._groupModule.quitGroup(t);this._groupModule.deleteLocalGroupAndConversation(t)}else this._groupModule.deleteLocalGroupAndConversation(t);return this.reset(t),Promise.resolve()}},{key:"joinWithoutAuth",value:function(e){var t=this,n=e.groupID,o="".concat(this._className,".joinWithoutAuth"),r=new Kp(Fg);return this._groupModule.request({protocolName:Gl,requestData:e}).then((function(e){var a=e.data.longPollingKey;if(r.setNetworkType(t._groupModule.getNetworkType()).setMessage("groupID:".concat(n," longPollingKey:").concat(a)).end(!0),Wi(a))return om(new $_({code:vd.CANNOT_JOIN_NON_AVCHATROOM_WITHOUT_LOGIN,message:up}));xi.log("".concat(o," ok. groupID:").concat(n)),t._getModule(jc).setCompleted("".concat(so.CONV_GROUP).concat(n));var s=new km({groupID:n});return t.startRunLoop({group:s,longPollingKey:a}),W_({status:E_})})).catch((function(e){return xi.error("".concat(o," failed. groupID:").concat(n," error:"),e),t._groupModule.probeNetwork().then((function(t){var o=Qn(t,2),a=o[0],s=o[1];r.setError(e,a,s).setMessage("groupID:".concat(n)).end(!0)})),om(e)})).finally((function(){t._groupModule.getModule(zc).reportAtOnce()}))}},{key:"getGroupOnlineMemberCount",value:function(e){var t=this._onlineMemberCountMap.get(e)||{},n=Date.now();return Au(t)||n-t.lastSyncTime>1e3*t.expireTime&&n-t.latestUpdateTime>1e4&&n-t.lastReqTime>3e3?(t.lastReqTime=n,this._onlineMemberCountMap.set(e,t),this._getGroupOnlineMemberCount(e).then((function(e){return W_({memberCount:e.memberCount})})).catch((function(e){return om(e)}))):nm({memberCount:t.memberCount})}},{key:"_getGroupOnlineMemberCount",value:function(e){var t=this,n="".concat(this._className,"._getGroupOnlineMemberCount");return this._groupModule.request({protocolName:Jl,requestData:{groupID:e}}).then((function(o){var r=t._onlineMemberCountMap.get(e)||{},a=o.data,s=a.onlineMemberNum,i=void 0===s?0:s,u=a.expireTime,c=void 0===u?t.DEFAULT_EXPIRE_TIME:u;xi.log("".concat(n," ok. groupID:").concat(e," memberCount:").concat(i," expireTime:").concat(c));var l=Date.now();return Au(r)&&(r.lastReqTime=l),t._onlineMemberCountMap.set(e,Object.assign(r,{lastSyncTime:l,latestUpdateTime:l,memberCount:i,expireTime:c})),{memberCount:i}})).catch((function(o){return xi.warn("".concat(n," failed. error:"),o),new Kp(Qg).setCode(o.code).setMessage("groupID:".concat(e," error:").concat(JSON.stringify(o))).setNetworkType(t._groupModule.getNetworkType()).end(),Promise.reject(o)}))}},{key:"_onGroupAttributesUpdated",value:function(e){var t=e.groupProfile.groupID,n=e.elements,o=n.operationType,r=n.newGroupProfile;if(6===o){var a=(void 0===r?void 0:r).groupAttributeOption;Wi(a)||this._groupModule.onGroupAttributesUpdated({groupID:t,groupAttributeOption:a})}}},{key:"_getModule",value:function(e){return this._groupModule.getModule(e)}},{key:"setPollingInterval",value:function(e){Wi(e)||Ki(e)||(this._pollingInterval=parseInt(e,10),xi.log("".concat(this._className,".setPollingInterval value:").concat(this._pollingInterval)))}},{key:"getPollingInterval",value:function(){return this._pollingInterval}},{key:"reset",value:function(e){if(e){xi.log("".concat(this._className,".reset groupID:").concat(e));var t=this._pollingInstanceMap.get(e);t&&t.stop(),this._pollingInstanceMap.delete(e),this._joinedGroupMap.delete(e),this._pollingRequestInfoMap.delete(e),this._onlineMemberCountMap.delete(e)}else{xi.log("".concat(this._className,".reset all"));var n,o=ro(this._pollingInstanceMap.values());try{for(o.s();!(n=o.n()).done;)n.value.stop()}catch(a){o.e(a)}finally{o.f()}this._pollingInstanceMap.clear(),this._joinedGroupMap.clear(),this._pollingRequestInfoMap.clear(),this._onlineMemberCountMap.clear()}this.sequencesLinkedList.reset(),this.messageIDLinkedList.reset(),this.receivedMessageCount=0,this._reportMessageStackedCount=0,this._pollingInterval=this.DEFAULT_POLLING_INTERVAL}}]),e}(),Vm=1,Km=15,Bm=function(){function e(t){Pn(this,e),this._groupModule=t,this._className="GroupSystemNoticeHandler",this.pendencyMap=new Map}return Un(e,[{key:"onNewGroupSystemNotice",value:function(e){var t=e.dataList,n=e.isSyncingEnded,o=e.isInstantMessage;xi.debug("".concat(this._className,".onReceiveSystemNotice count:").concat(t.length));var r=this.newSystemNoticeStoredAndSummary({notifiesList:t,isInstantMessage:o}),a=r.eventDataList,s=r.result;a.length>0&&(this._groupModule.getModule(jc).onNewMessage({conversationOptionsList:a,isInstantMessage:o}),this._onReceivedGroupSystemNotice({result:s,isInstantMessage:o})),o?s.length>0&&this._groupModule.emitOuterEvent(ao.MESSAGE_RECEIVED,s):!0===n&&this._clearGroupSystemNotice()}},{key:"newSystemNoticeStoredAndSummary",value:function(e){var t=e.notifiesList,n=e.isInstantMessage,o=null,r=t.length,a=0,s=[],i={conversationID:so.CONV_SYSTEM,unreadCount:0,type:so.CONV_SYSTEM,subType:null,lastMessage:null};for(a=0;a<r;a++){var u=t[a];u.elements.operationType!==Km&&(u.currentUser=this._groupModule.getMyUserID(),u.conversationType=so.CONV_SYSTEM,u.conversationID=so.CONV_SYSTEM,(o=new Y_(u)).setElement({type:so.MSG_GRP_SYS_NOTICE,content:xn({},u.elements,{groupProfile:u.groupProfile})}),o.isSystemMessage=!0,(1===o.sequence&&1===o.random||2===o.sequence&&2===o.random)&&(o.sequence=ru(),o.random=ru(),o.generateMessageID(u.currentUser),xi.log("".concat(this._className,".newSystemNoticeStoredAndSummary sequence and random maybe duplicated, regenerate. ID:").concat(o.ID))),this._groupModule.getModule(jc).pushIntoNoticeResult(s,o)&&(n?i.unreadCount++:o.setIsRead(!0),i.subType=o.conversationSubType))}return i.lastMessage=s[s.length-1],{eventDataList:s.length>0?[i]:[],result:s}}},{key:"_clearGroupSystemNotice",value:function(){var e=this;this.getPendencyList().then((function(t){t.forEach((function(t){e.pendencyMap.set("".concat(t.from,"_").concat(t.groupID,"_").concat(t.to),t)}));var n=e._groupModule.getModule(jc).getLocalMessageList(so.CONV_SYSTEM),o=[];n.forEach((function(t){var n=t.payload,r=n.operatorID,a=n.operationType,s=n.groupProfile;if(a===Vm){var i="".concat(r,"_").concat(s.groupID,"_").concat(s.to),u=e.pendencyMap.get(i);u&&Ki(u.handled)&&0!==u.handled&&o.push(t)}})),e.deleteGroupSystemNotice({messageList:o})}))}},{key:"deleteGroupSystemNotice",value:function(e){var t=this,n="".concat(this._className,".deleteGroupSystemNotice");return Yi(e.messageList)&&0!==e.messageList.length?(xi.log("".concat(n)+e.messageList.map((function(e){return e.ID}))),this._groupModule.request({protocolName:Yl,requestData:{messageListToDelete:e.messageList.map((function(e){return{from:so.CONV_SYSTEM,messageSeq:e.clientSequence,messageRandom:e.random}}))}}).then((function(){xi.log("".concat(n," ok"));var o=t._groupModule.getModule(jc);return e.messageList.forEach((function(e){o.deleteLocalMessage(e)})),W_()})).catch((function(e){return xi.error("".concat(n," error:"),e),om(e)}))):nm()}},{key:"getPendencyList",value:function(e){var t=this;return this._groupModule.request({protocolName:jl,requestData:{startTime:e&&e.startTime?e.startTime:0,limit:e&&e.limit?e.limit:10,handleAccount:this._groupModule.getMyUserID()}}).then((function(e){var n=e.data.pendencyList;return 0!==e.data.nextStartTime?t.getPendencyList({startTime:e.data.nextStartTime}).then((function(e){return[].concat(Zn(n),Zn(e))})):n}))}},{key:"_onReceivedGroupSystemNotice",value:function(e){var t=this,n=e.result;e.isInstantMessage&&n.forEach((function(e){switch(e.payload.operationType){case 1:break;case 2:t._onApplyGroupRequestAgreed(e);break;case 3:break;case 4:t._onMemberKicked(e);break;case 5:t._onGroupDismissed(e);break;case 6:break;case 7:t._onInviteGroup(e);break;case 8:t._onQuitGroup(e);break;case 9:t._onSetManager(e);break;case 10:t._onDeleteManager(e)}}))}},{key:"_onApplyGroupRequestAgreed",value:function(e){var t=this,n=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(n)||this._groupModule.getGroupProfile({groupID:n}).then((function(e){var n=e.data.group;n&&(t._groupModule.updateGroupMap([n]),t._groupModule.emitGroupListUpdate())}))}},{key:"_onMemberKicked",value:function(e){var t=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(t)&&this._groupModule.deleteLocalGroupAndConversation(t)}},{key:"_onGroupDismissed",value:function(e){var t=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(t)&&this._groupModule.deleteLocalGroupAndConversation(t);var n=this._groupModule._AVChatRoomHandler;n&&n.checkJoinedAVChatRoomByID(t)&&n.reset(t)}},{key:"_onInviteGroup",value:function(e){var t=this,n=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(n)||this._groupModule.getGroupProfile({groupID:n}).then((function(e){var n=e.data.group;n&&(t._groupModule.updateGroupMap([n]),t._groupModule.emitGroupListUpdate())}))}},{key:"_onQuitGroup",value:function(e){var t=e.payload.groupProfile.groupID;this._groupModule.hasLocalGroup(t)&&this._groupModule.deleteLocalGroupAndConversation(t)}},{key:"_onSetManager",value:function(e){var t=e.payload.groupProfile,n=t.to,o=t.groupID,r=this._groupModule.getModule(Hc).getLocalGroupMemberInfo(o,n);r&&r.updateRole(so.GRP_MBR_ROLE_ADMIN)}},{key:"_onDeleteManager",value:function(e){var t=e.payload.groupProfile,n=t.to,o=t.groupID,r=this._groupModule.getModule(Hc).getLocalGroupMemberInfo(o,n);r&&r.updateRole(so.GRP_MBR_ROLE_MEMBER)}},{key:"reset",value:function(){this.pendencyMap.clear()}}]),e}(),Hm=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="GroupModule",o._commonGroupHandler=null,o._AVChatRoomHandler=null,o._groupSystemNoticeHandler=null,o._commonGroupHandler=new Pm(zn(o)),o._groupAttributesHandler=new Um(zn(o)),o._AVChatRoomHandler=new xm(zn(o)),o._groupTipsHandler=new Lm(zn(o)),o._groupSystemNoticeHandler=new Bm(zn(o)),o.groupMap=new Map,o._unjoinedAVChatRoomList=new Map,o.getInnerEmitterInstance().on(hm.CLOUD_CONFIG_UPDATED,o._onCloudConfigUpdated,zn(o)),o}return Un(n,[{key:"_onCloudConfigUpdated",value:function(){var e=this.getCloudConfig("polling_interval");this._AVChatRoomHandler&&this._AVChatRoomHandler.setPollingInterval(e)}},{key:"onCheckTimer",value:function(e){this.isLoggedIn()&&(this._commonGroupHandler.onCheckTimer(e),this._groupTipsHandler.onCheckTimer(e))}},{key:"guardForAVChatRoom",value:function(e){var t=this;if(e.conversationType===so.CONV_GROUP){var n=e.to;return this.hasLocalGroup(n)?nm():this.getGroupProfile({groupID:n}).then((function(o){var r=o.data.group.type;if(xi.log("".concat(t._className,".guardForAVChatRoom. groupID:").concat(n," type:").concat(r)),r===so.GRP_AVCHATROOM){var a="userId:".concat(e.from," 未加入群 groupID:").concat(n,"。发消息前先使用 joinGroup 接口申请加群，详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#joinGroup");return xi.warn("".concat(t._className,".guardForAVChatRoom sendMessage not allowed. ").concat(a)),om(new $_({code:vd.MESSAGE_SEND_FAIL,message:a,data:{message:e}}))}return nm()}))}return nm()}},{key:"checkJoinedAVChatRoomByID",value:function(e){return!!this._AVChatRoomHandler&&this._AVChatRoomHandler.checkJoinedAVChatRoomByID(e)}},{key:"onNewGroupMessage",value:function(e){this._commonGroupHandler&&this._commonGroupHandler.onNewGroupMessage(e)}},{key:"updateNextMessageSeq",value:function(e){var t=this;Yi(e)&&e.forEach((function(e){var n=e.conversationID.replace(so.CONV_GROUP,"");t.groupMap.has(n)&&(t.groupMap.get(n).nextMessageSeq=e.lastMessage.sequence+1)}))}},{key:"onNewGroupTips",value:function(e){this._groupTipsHandler&&this._groupTipsHandler.onNewGroupTips(e)}},{key:"onGroupMessageRevoked",value:function(e){this._commonGroupHandler&&this._commonGroupHandler.onGroupMessageRevoked(e)}},{key:"onNewGroupSystemNotice",value:function(e){this._groupSystemNoticeHandler&&this._groupSystemNoticeHandler.onNewGroupSystemNotice(e)}},{key:"onGroupMessageReadNotice",value:function(e){var t=this;e.dataList.forEach((function(e){var n=e.elements.groupMessageReadNotice;if(!Wi(n)){var o=t.getModule(jc);n.forEach((function(e){var n=e.groupID,r=e.lastMessageSeq;xi.debug("".concat(t._className,".onGroupMessageReadNotice groupID:").concat(n," lastMessageSeq:").concat(r));var a="".concat(so.CONV_GROUP).concat(n);o.updateIsReadAfterReadReport({conversationID:a,lastMessageSeq:r}),o.updateUnreadCount(a)}))}}))}},{key:"deleteGroupSystemNotice",value:function(e){this._groupSystemNoticeHandler&&this._groupSystemNoticeHandler.deleteGroupSystemNotice(e)}},{key:"initGroupMap",value:function(e){this.groupMap.set(e.groupID,new km(e))}},{key:"deleteGroup",value:function(e){this.groupMap.delete(e)}},{key:"updateGroupMap",value:function(e){var t=this;e.forEach((function(e){t.groupMap.has(e.groupID)?t.groupMap.get(e.groupID).updateGroup(e):t.groupMap.set(e.groupID,new km(e))})),this._setStorageGroupList()}},{key:"getStorageGroupList",value:function(){return this.getModule(Wc).getItem("groupMap")}},{key:"_setStorageGroupList",value:function(){var e=this.getLocalGroupList().filter((function(e){var t=e.type;return!hu(t)})).slice(0,20).map((function(e){return{groupID:e.groupID,name:e.name,avatar:e.avatar,type:e.type}}));this.getModule(Wc).setItem("groupMap",e)}},{key:"getGroupMap",value:function(){return this.groupMap}},{key:"getLocalGroupList",value:function(){return Zn(this.groupMap.values())}},{key:"getLocalGroupProfile",value:function(e){return this.groupMap.get(e)}},{key:"sortLocalGroupList",value:function(){var e=Zn(this.groupMap).filter((function(e){var t=Qn(e,2);return t[0],!Au(t[1].lastMessage)}));e.sort((function(e,t){return t[1].lastMessage.lastTime-e[1].lastMessage.lastTime})),this.groupMap=new Map(Zn(e))}},{key:"updateGroupLastMessage",value:function(e){this._commonGroupHandler&&this._commonGroupHandler.handleUpdateGroupLastMessage(e)}},{key:"emitGroupListUpdate",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=this.getLocalGroupList();if(e&&this.emitOuterEvent(ao.GROUP_LIST_UPDATED,n),t){var o=JSON.parse(JSON.stringify(n)),r=this.getModule(jc);r.updateConversationGroupProfile(o)}}},{key:"getMyNameCardByGroupID",value:function(e){var t=this.getLocalGroupProfile(e);return t?t.selfInfo.nameCard:""}},{key:"getGroupList",value:function(e){return this._commonGroupHandler?this._commonGroupHandler.getGroupList(e):nm()}},{key:"getGroupProfile",value:function(e){var t=this,n=new Kp(Rg),o="".concat(this._className,".getGroupProfile"),r=e.groupID,a=e.groupCustomFieldFilter;xi.log("".concat(o," groupID:").concat(r));var s={groupIDList:[r],responseFilter:{groupBaseInfoFilter:["Type","Name","Introduction","Notification","FaceUrl","Owner_Account","CreateTime","InfoSeq","LastInfoTime","LastMsgTime","MemberNum","MaxMemberNum","ApplyJoinOption","NextMsgSeq","ShutUpAllMember"],groupCustomFieldFilter:a}};return this.getGroupProfileAdvance(s).then((function(e){var a,s=e.data,i=s.successGroupList,u=s.failureGroupList;return xi.log("".concat(o," ok")),u.length>0?om(u[0]):(hu(i[0].type)&&!t.hasLocalGroup(r)?a=new km(i[0]):(t.updateGroupMap(i),a=t.getLocalGroupProfile(r)),n.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(r," type:").concat(a.type," muteAllMembers:").concat(a.muteAllMembers," ownerID:").concat(a.ownerID)).end(),a&&a.selfInfo&&!a.selfInfo.nameCard?t.updateSelfInfo(a).then((function(e){return W_({group:e})})):W_({group:a}))})).catch((function(r){return t.probeNetwork().then((function(t){var o=Qn(t,2),a=o[0],s=o[1];n.setError(r,a,s).setMessage("groupID:".concat(e.groupID)).end()})),xi.error("".concat(o," failed. error:"),r),om(r)}))}},{key:"getGroupProfileAdvance",value:function(e){var t="".concat(this._className,".getGroupProfileAdvance");return Yi(e.groupIDList)&&e.groupIDList.length>50&&(xi.warn("".concat(t," 获取群资料的数量不能超过50个")),e.groupIDList.length=50),xi.log("".concat(t," groupIDList:").concat(e.groupIDList)),this.request({protocolName:Ll,requestData:e}).then((function(e){xi.log("".concat(t," ok"));var n=e.data.groups,o=n.filter((function(e){return Wi(e.errorCode)||0===e.errorCode})),r=n.filter((function(e){return e.errorCode&&0!==e.errorCode})).map((function(e){return new $_({code:e.errorCode,message:e.errorInfo,data:{groupID:e.groupID}})}));return W_({successGroupList:o,failureGroupList:r})})).catch((function(e){return xi.error("".concat(t," failed. error:"),e),om(e)}))}},{key:"updateSelfInfo",value:function(e){var t="".concat(this._className,".updateSelfInfo"),n=e.groupID;return xi.log("".concat(t," groupID:").concat(n)),this.getModule(Hc).getGroupMemberProfile({groupID:n,userIDList:[this.getMyUserID()]}).then((function(n){var o=n.data.memberList;return xi.log("".concat(t," ok")),e&&0!==o.length&&e.updateSelfInfo(o[0]),e}))}},{key:"createGroup",value:function(e){var t=this,n="".concat(this._className,".createGroup");if(!["Public","Private","ChatRoom","AVChatRoom"].includes(e.type)){var o=new $_({code:vd.ILLEGAL_GROUP_TYPE,message:ep});return om(o)}hu(e.type)&&!Wi(e.memberList)&&e.memberList.length>0&&(xi.warn("".concat(n," 创建 AVChatRoom 时不能添加群成员，自动忽略该字段")),e.memberList=void 0),gu(e.type)||Wi(e.joinOption)||(xi.warn("".concat(n," 创建 Work/Meeting/AVChatRoom 群时不能设置字段 joinOption，自动忽略该字段")),e.joinOption=void 0);var r=new Kp(Ig);xi.log("".concat(n," options:"),e);var a=[];return this.request({protocolName:Rl,requestData:xn({},e,{ownerID:this.getMyUserID(),webPushFlag:1})}).then((function(o){var s=o.data,i=s.groupID,u=s.overLimitUserIDList,c=void 0===u?[]:u;if(a=c,r.setNetworkType(t.getNetworkType()).setMessage("groupType:".concat(e.type," groupID:").concat(i," overLimitUserIDList=").concat(c)).end(),xi.log("".concat(n," ok groupID:").concat(i," overLimitUserIDList:"),c),e.type===so.GRP_AVCHATROOM)return t.getGroupProfile({groupID:i});Au(e.memberList)||Au(c)||(e.memberList=e.memberList.filter((function(e){return-1===c.indexOf(e.userID)}))),t.updateGroupMap([xn({},e,{groupID:i})]);var l=t.getModule(qc),d=l.createCustomMessage({to:i,conversationType:so.CONV_GROUP,payload:{data:"group_create",extension:"".concat(t.getMyUserID(),"创建群组")}});return l.sendMessageInstance(d),t.emitGroupListUpdate(),t.getGroupProfile({groupID:i})})).then((function(e){var t=e.data.group,n=t.selfInfo,o=n.nameCard,r=n.joinTime;return t.updateSelfInfo({nameCard:o,joinTime:r,messageRemindType:so.MSG_REMIND_ACPT_AND_NOTE,role:so.GRP_MBR_ROLE_OWNER}),W_({group:t,overLimitUserIDList:a})})).catch((function(o){return r.setMessage("groupType:".concat(e.type)),t.probeNetwork().then((function(e){var t=Qn(e,2),n=t[0],a=t[1];r.setError(o,n,a).end()})),xi.error("".concat(n," failed. error:"),o),om(o)}))}},{key:"dismissGroup",value:function(e){var t=this,n="".concat(this._className,".dismissGroup");if(this.hasLocalGroup(e)&&this.getLocalGroupProfile(e).type===so.GRP_WORK)return om(new $_({code:vd.CANNOT_DISMISS_WORK,message:rp}));var o=new Kp(Ng);return o.setMessage("groupID:".concat(e)),xi.log("".concat(n," groupID:").concat(e)),this.request({protocolName:bl,requestData:{groupID:e}}).then((function(){return o.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(n," ok")),t.deleteLocalGroupAndConversation(e),t.checkJoinedAVChatRoomByID(e)&&t._AVChatRoomHandler.reset(e),W_({groupID:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),xi.error("".concat(n," failed. error:"),e),om(e)}))}},{key:"updateGroupProfile",value:function(e){var t=this,n="".concat(this._className,".updateGroupProfile");!this.hasLocalGroup(e.groupID)||gu(this.getLocalGroupProfile(e.groupID).type)||Wi(e.joinOption)||(xi.warn("".concat(n," Work/Meeting/AVChatRoom 群不能设置字段 joinOption，自动忽略该字段")),e.joinOption=void 0),Wi(e.muteAllMembers)||(e.muteAllMembers?e.muteAllMembers="On":e.muteAllMembers="Off");var o=new Kp(Og);return o.setMessage(JSON.stringify(e)),xi.log("".concat(n," groupID:").concat(e.groupID)),this.request({protocolName:wl,requestData:e}).then((function(){return o.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(n," ok")),t.hasLocalGroup(e.groupID)&&(t.groupMap.get(e.groupID).updateGroup(e),t._setStorageGroupList()),W_({group:t.groupMap.get(e.groupID)})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),xi.log("".concat(n," failed. error:"),e),om(e)}))}},{key:"joinGroup",value:function(e){var t=this,n=e.groupID,o=e.type,r="".concat(this._className,".joinGroup");if(o===so.GRP_WORK){var a=new $_({code:vd.CANNOT_JOIN_WORK,message:tp});return om(a)}if(this.deleteUnjoinedAVChatRoom(n),this.hasLocalGroup(n)){if(!this.isLoggedIn())return nm({status:so.JOIN_STATUS_ALREADY_IN_GROUP});var s=new Kp(Tg);return this.getGroupProfile({groupID:n}).then((function(){return s.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(n," joinedStatus:").concat(so.JOIN_STATUS_ALREADY_IN_GROUP)).end(),nm({status:so.JOIN_STATUS_ALREADY_IN_GROUP})})).catch((function(o){return s.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(n," unjoined")).end(),xi.warn("".concat(r," ").concat(n," was unjoined, now join!")),t.groupMap.delete(n),t.applyJoinGroup(e)}))}return xi.log("".concat(r," groupID:").concat(n)),this.isLoggedIn()?this.applyJoinGroup(e):this._AVChatRoomHandler.joinWithoutAuth(e)}},{key:"applyJoinGroup",value:function(e){var t=this,n="".concat(this._className,".applyJoinGroup"),o=e.groupID,r=new Kp(Tg);return this.request({protocolName:Pl,requestData:e}).then((function(e){var a=e.data,s=a.joinedStatus,i=a.longPollingKey,u=a.avChatRoomFlag,c=a.avChatRoomKey,l="groupID:".concat(o," joinedStatus:").concat(s," longPollingKey:").concat(i," avChatRoomFlag:").concat(u);switch(r.setNetworkType(t.getNetworkType()).setMessage("".concat(l)).end(),xi.log("".concat(n," ok. ").concat(l)),s){case D_:return W_({status:D_});case E_:return t.getGroupProfile({groupID:o}).then((function(e){var n=e.data.group,r={status:E_,group:n};return 1===u?(t.getModule(jc).setCompleted("".concat(so.CONV_GROUP).concat(o)),t._groupAttributesHandler.initGroupAttributesCache({groupID:o,avChatRoomKey:c}),Wi(i)?t._AVChatRoomHandler.handleJoinResult({group:n}):t._AVChatRoomHandler.startRunLoop({longPollingKey:i,group:n})):(t.emitGroupListUpdate(!0,!1),W_(r))}));default:var d=new $_({code:vd.JOIN_GROUP_FAIL,message:sp});return xi.error("".concat(n," error:"),d),om(d)}})).catch((function(o){return r.setMessage("groupID:".concat(e.groupID)),t.probeNetwork().then((function(e){var t=Qn(e,2),n=t[0],a=t[1];r.setError(o,n,a).end()})),xi.error("".concat(n," error:"),o),om(o)}))}},{key:"quitGroup",value:function(e){var t=this,n="".concat(this._className,".quitGroup");xi.log("".concat(n," groupID:").concat(e));var o=this.checkJoinedAVChatRoomByID(e);if(!o&&!this.hasLocalGroup(e)){var r=new $_({code:vd.MEMBER_NOT_IN_GROUP,message:ap});return om(r)}if(o&&!this.isLoggedIn())return xi.log("".concat(n," anonymously ok. groupID:").concat(e)),this.deleteLocalGroupAndConversation(e),this._AVChatRoomHandler.reset(e),nm({groupID:e});var a=new Kp(Sg);return a.setMessage("groupID:".concat(e)),this.request({protocolName:Ul,requestData:{groupID:e}}).then((function(){return a.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(n," ok")),o&&t._AVChatRoomHandler.reset(e),t.deleteLocalGroupAndConversation(e),W_({groupID:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),xi.error("".concat(n," failed. error:"),e),om(e)}))}},{key:"searchGroupByID",value:function(e){var t=this,n="".concat(this._className,".searchGroupByID"),o={groupIDList:[e]},r=new Kp(Eg);return r.setMessage("groupID:".concat(e)),xi.log("".concat(n," groupID:").concat(e)),this.request({protocolName:Fl,requestData:o}).then((function(e){var o=e.data.groupProfile;if(0!==o[0].errorCode)throw new $_({code:o[0].errorCode,message:o[0].errorInfo});return r.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(n," ok")),W_({group:new km(o[0])})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],a=n[1];r.setError(e,o,a).end()})),xi.warn("".concat(n," failed. error:"),e),om(e)}))}},{key:"changeGroupOwner",value:function(e){var t=this,n="".concat(this._className,".changeGroupOwner");if(this.hasLocalGroup(e.groupID)&&this.getLocalGroupProfile(e.groupID).type===so.GRP_AVCHATROOM)return om(new $_({code:vd.CANNOT_CHANGE_OWNER_IN_AVCHATROOM,message:np}));if(e.newOwnerID===this.getMyUserID())return om(new $_({code:vd.CANNOT_CHANGE_OWNER_TO_SELF,message:op}));var o=new Kp(Dg);return o.setMessage("groupID:".concat(e.groupID," newOwnerID:").concat(e.newOwnerID)),xi.log("".concat(n," groupID:").concat(e.groupID)),this.request({protocolName:ql,requestData:e}).then((function(){o.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(n," ok"));var r=e.groupID,a=e.newOwnerID;t.groupMap.get(r).ownerID=a;var s=t.getModule(Hc).getLocalGroupMemberList(r);if(s instanceof Map){var i=s.get(t.getMyUserID());Wi(i)||(i.updateRole("Member"),t.groupMap.get(r).selfInfo.role="Member");var u=s.get(a);Wi(u)||u.updateRole("Owner")}return t.emitGroupListUpdate(!0,!1),W_({group:t.groupMap.get(r)})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),xi.error("".concat(n," failed. error:"),e),om(e)}))}},{key:"handleGroupApplication",value:function(e){var t=this,n="".concat(this._className,".handleGroupApplication"),o=e.message.payload,r=o.groupProfile.groupID,a=o.authentication,s=o.messageKey,i=o.operatorID,u=new Kp(kg);return u.setMessage("groupID:".concat(r)),xi.log("".concat(n," groupID:").concat(r)),this.request({protocolName:xl,requestData:xn({},e,{applicant:i,groupID:r,authentication:a,messageKey:s})}).then((function(){return u.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(n," ok")),t._groupSystemNoticeHandler.deleteGroupSystemNotice({messageList:[e.message]}),W_({group:t.getLocalGroupProfile(r)})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];u.setError(e,o,r).end()})),xi.error("".concat(n," failed. error"),e),om(e)}))}},{key:"handleGroupInvitation",value:function(e){var t=this,n="".concat(this._className,".handleGroupInvitation"),o=e.message.payload,r=o.groupProfile.groupID,a=o.authentication,s=o.messageKey,i=o.operatorID,u=e.handleAction,c=new Kp(Cg);return c.setMessage("groupID:".concat(r," inviter:").concat(i," handleAction:").concat(u)),xi.log("".concat(n," groupID:").concat(r," inviter:").concat(i," handleAction:").concat(u)),this.request({protocolName:Vl,requestData:xn({},e,{inviter:i,groupID:r,authentication:a,messageKey:s})}).then((function(){return c.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(n," ok")),t._groupSystemNoticeHandler.deleteGroupSystemNotice({messageList:[e.message]}),W_({group:t.getLocalGroupProfile(r)})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];c.setError(e,o,r).end()})),xi.error("".concat(n," failed. error"),e),om(e)}))}},{key:"getGroupOnlineMemberCount",value:function(e){return this._AVChatRoomHandler?this._AVChatRoomHandler.checkJoinedAVChatRoomByID(e)?this._AVChatRoomHandler.getGroupOnlineMemberCount(e):nm({memberCount:0}):om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"hasLocalGroup",value:function(e){return this.groupMap.has(e)}},{key:"deleteLocalGroupAndConversation",value:function(e){this._deleteLocalGroup(e),this.getModule(jc).deleteLocalConversation("GROUP".concat(e)),this.emitGroupListUpdate(!0,!1)}},{key:"_deleteLocalGroup",value:function(e){this.groupMap.delete(e),this.getModule(Hc).deleteGroupMemberList(e),this._setStorageGroupList()}},{key:"sendMessage",value:function(e,t){var n=this.createGroupMessagePack(e,t);return this.request(n)}},{key:"createGroupMessagePack",value:function(e,t){var n=null;t&&t.offlinePushInfo&&(n=t.offlinePushInfo);var o="";Bi(e.cloudCustomData)&&e.cloudCustomData.length>0&&(o=e.cloudCustomData);var r=e.getGroupAtInfoList();return{protocolName:hl,tjgID:this.generateTjgID(e),requestData:{fromAccount:this.getMyUserID(),groupID:e.to,msgBody:e.getElements(),cloudCustomData:o,random:e.random,priority:e.priority,clientSequence:e.clientSequence,groupAtInfo:e.type!==so.MSG_TEXT||Au(r)?void 0:r,onlineOnlyFlag:this.isOnlineMessage(e,t)?1:0,offlinePushInfo:n?{pushFlag:!0===n.disablePush?1:0,title:n.title||"",desc:n.description||"",ext:n.extension||"",apnsInfo:{badgeMode:!0===n.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:n.androidOPPOChannelID||""}}:void 0}}}},{key:"revokeMessage",value:function(e){return this.request({protocolName:Kl,requestData:{to:e.to,msgSeqList:[{msgSeq:e.sequence}]}})}},{key:"deleteMessage",value:function(e){var t=e.to,n=e.keyList;return xi.log("".concat(this._className,".deleteMessage groupID:").concat(t," count:").concat(n.length)),this.request({protocolName:Xl,requestData:{groupID:t,deleter:this.getMyUserID(),keyList:n}})}},{key:"getRoamingMessage",value:function(e){var t=this,n="".concat(this._className,".getRoamingMessage"),o=new Kp(rg),r=0;return this._computeLastSequence(e).then((function(n){return r=n,xi.log("".concat(t._className,".getRoamingMessage groupID:").concat(e.groupID," lastSequence:").concat(r)),t.request({protocolName:Hl,requestData:{groupID:e.groupID,count:21,sequence:r}})})).then((function(a){var s=a.data,i=s.messageList,u=s.complete;Wi(i)?xi.log("".concat(n," ok. complete:").concat(u," but messageList is undefined!")):xi.log("".concat(n," ok. complete:").concat(u," count:").concat(i.length)),o.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(e.groupID," lastSequence:").concat(r," complete:").concat(u," count:").concat(i?i.length:"undefined")).end();var c="GROUP".concat(e.groupID),l=t.getModule(jc);if(2===u||Au(i))return l.setCompleted(c),[];var d=l.storeRoamingMessage(i,c);return l.updateIsRead(c),l.patchConversationLastMessage(c),d})).catch((function(a){return t.probeNetwork().then((function(t){var n=Qn(t,2),s=n[0],i=n[1];o.setError(a,s,i).setMessage("groupID:".concat(e.groupID," lastSequence:").concat(r)).end()})),xi.warn("".concat(n," failed. error:"),a),om(a)}))}},{key:"setMessageRead",value:function(e){var t=this,n=e.conversationID,o=e.lastMessageSeq,r="".concat(this._className,".setMessageRead");xi.log("".concat(r," conversationID:").concat(n," lastMessageSeq:").concat(o)),Ki(o)||xi.warn("".concat(r," 请勿修改 Conversation.lastMessage.lastSequence，否则可能会导致已读上报结果不准确"));var a=new Kp(ug);return a.setMessage("".concat(n,"-").concat(o)),this.request({protocolName:Bl,requestData:{groupID:n.replace("GROUP",""),messageReadSeq:o}}).then((function(){a.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(r," ok."));var e=t.getModule(jc);return e.updateIsReadAfterReadReport({conversationID:n,lastMessageSeq:o}),e.updateUnreadCount(n),W_()})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),xi.log("".concat(r," failed. error:"),e),om(e)}))}},{key:"_computeLastSequence",value:function(e){return e.sequence>0?Promise.resolve(e.sequence):this.getGroupLastSequence(e.groupID)}},{key:"getGroupLastSequence",value:function(e){var t=this,n="".concat(this._className,".getGroupLastSequence"),o=new Kp(wg),r=0,a="";if(this.hasLocalGroup(e)){var s=this.getLocalGroupProfile(e),i=s.lastMessage;if(i.lastSequence>0&&!1===i.onlineOnlyFlag)return r=i.lastSequence,a="got lastSequence:".concat(r," from local group profile[lastMessage.lastSequence]. groupID:").concat(e),xi.log("".concat(n," ").concat(a)),o.setNetworkType(this.getNetworkType()).setMessage("".concat(a)).end(),Promise.resolve(r);if(s.nextMessageSeq>1)return r=s.nextMessageSeq-1,a="got lastSequence:".concat(r," from local group profile[nextMessageSeq]. groupID:").concat(e),xi.log("".concat(n," ").concat(a)),o.setNetworkType(this.getNetworkType()).setMessage("".concat(a)).end(),Promise.resolve(r)}var u="GROUP".concat(e),c=this.getModule(jc).getLocalConversation(u);if(c&&c.lastMessage.lastSequence&&!1===c.lastMessage.onlineOnlyFlag)return r=c.lastMessage.lastSequence,a="got lastSequence:".concat(r," from local conversation profile[lastMessage.lastSequence]. groupID:").concat(e),xi.log("".concat(n," ").concat(a)),o.setNetworkType(this.getNetworkType()).setMessage("".concat(a)).end(),Promise.resolve(r);var l={groupIDList:[e],responseFilter:{groupBaseInfoFilter:["NextMsgSeq"]}};return this.getGroupProfileAdvance(l).then((function(s){var i=s.data.successGroupList;return Au(i)?xi.log("".concat(n," successGroupList is empty. groupID:").concat(e)):(r=i[0].nextMessageSeq-1,a="got lastSequence:".concat(r," from getGroupProfileAdvance. groupID:").concat(e),xi.log("".concat(n," ").concat(a))),o.setNetworkType(t.getNetworkType()).setMessage("".concat(a)).end(),r})).catch((function(r){return t.probeNetwork().then((function(t){var n=Qn(t,2),a=n[0],s=n[1];o.setError(r,a,s).setMessage("get lastSequence failed from getGroupProfileAdvance. groupID:".concat(e)).end()})),xi.warn("".concat(n," failed. error:"),r),om(r)}))}},{key:"isMessageFromAVChatroom",value:function(e){return!!this._AVChatRoomHandler&&this._AVChatRoomHandler.checkJoinedAVChatRoomByID(e)}},{key:"hasJoinedAVChatRoom",value:function(){return this._AVChatRoomHandler?this._AVChatRoomHandler.hasJoinedAVChatRoom():0}},{key:"getJoinedAVChatRoom",value:function(){return this._AVChatRoomHandler?this._AVChatRoomHandler.getJoinedAVChatRoom():[]}},{key:"isOnlineMessage",value:function(e,t){return!(!this._canIUseOnlineOnlyFlag(e)||!t||!0!==t.onlineUserOnly)}},{key:"_canIUseOnlineOnlyFlag",value:function(e){var t=this.getJoinedAVChatRoom();return!t||!t.includes(e.to)||e.conversationType!==so.CONV_GROUP}},{key:"deleteLocalGroupMembers",value:function(e,t){this.getModule(Hc).deleteLocalGroupMembers(e,t)}},{key:"onAVChatRoomMessage",value:function(e){this._AVChatRoomHandler&&this._AVChatRoomHandler.onMessage(e)}},{key:"getGroupSimplifiedInfo",value:function(e){var t=this,n=new Kp(Ug),o={groupIDList:[e],responseFilter:{groupBaseInfoFilter:["Type","Name"]}};return this.getGroupProfileAdvance(o).then((function(o){var r=o.data.successGroupList;return n.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(e," type:").concat(r[0].type)).end(),r[0]})).catch((function(o){t.probeNetwork().then((function(t){var r=Qn(t,2),a=r[0],s=r[1];n.setError(o,a,s).setMessage("groupID:".concat(e)).end()}))}))}},{key:"setUnjoinedAVChatRoom",value:function(e){this._unjoinedAVChatRoomList.set(e,1)}},{key:"deleteUnjoinedAVChatRoom",value:function(e){this._unjoinedAVChatRoomList.has(e)&&this._unjoinedAVChatRoomList.delete(e)}},{key:"isUnjoinedAVChatRoom",value:function(e){return this._unjoinedAVChatRoomList.has(e)}},{key:"onGroupAttributesUpdated",value:function(e){this._groupAttributesHandler&&this._groupAttributesHandler.onGroupAttributesUpdated(e)}},{key:"updateLocalMainSequenceOnReconnected",value:function(){this._groupAttributesHandler&&this._groupAttributesHandler.updateLocalMainSequenceOnReconnected()}},{key:"initGroupAttributes",value:function(e){return this._groupAttributesHandler.initGroupAttributes(e)}},{key:"setGroupAttributes",value:function(e){return this._groupAttributesHandler.setGroupAttributes(e)}},{key:"deleteGroupAttributes",value:function(e){return this._groupAttributesHandler.deleteGroupAttributes(e)}},{key:"getGroupAttributes",value:function(e){return this._groupAttributesHandler.getGroupAttributes(e)}},{key:"reset",value:function(){this.groupMap.clear(),this._unjoinedAVChatRoomList.clear(),this._commonGroupHandler.reset(),this._groupSystemNoticeHandler.reset(),this._groupTipsHandler.reset(),this._AVChatRoomHandler&&this._AVChatRoomHandler.reset()}}]),n}(il),jm=function(){function e(t){Pn(this,e),this.userID="",this.avatar="",this.nick="",this.role="",this.joinTime="",this.lastSendMsgTime="",this.nameCard="",this.muteUntil=0,this.memberCustomField=[],this._initMember(t)}return Un(e,[{key:"_initMember",value:function(e){this.updateMember(e)}},{key:"updateMember",value:function(e){var t=[null,void 0,"",0,NaN];e.memberCustomField&&pu(this.memberCustomField,e.memberCustomField),tu(this,e,["memberCustomField"],t)}},{key:"updateRole",value:function(e){["Owner","Admin","Member"].indexOf(e)<0||(this.role=e)}},{key:"updateMuteUntil",value:function(e){Wi(e)||(this.muteUntil=Math.floor((Date.now()+1e3*e)/1e3))}},{key:"updateNameCard",value:function(e){Wi(e)||(this.nameCard=e)}},{key:"updateMemberCustomField",value:function(e){e&&pu(this.memberCustomField,e)}}]),e}(),Ym=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="GroupMemberModule",o.groupMemberListMap=new Map,o.getInnerEmitterInstance().on(hm.PROFILE_UPDATED,o._onProfileUpdated,zn(o)),o}return Un(n,[{key:"_onProfileUpdated",value:function(e){for(var t=this,n=e.data,o=function(e){var o=n[e];t.groupMemberListMap.forEach((function(e){e.has(o.userID)&&e.get(o.userID).updateMember({nick:o.nick,avatar:o.avatar})}))},r=0;r<n.length;r++)o(r)}},{key:"deleteGroupMemberList",value:function(e){this.groupMemberListMap.delete(e)}},{key:"getGroupMemberList",value:function(e){var t=this,n=e.groupID,o=e.offset,r=void 0===o?0:o,a=e.count,s=void 0===a?15:a,i="".concat(this._className,".getGroupMemberList"),u=new Kp(Bg);xi.log("".concat(i," groupID:").concat(n," offset:").concat(r," count:").concat(s));var c=[];return this.request({protocolName:nd,requestData:{groupID:n,offset:r,limit:s>100?100:s}}).then((function(e){var o=e.data,r=o.members,a=o.memberNum;if(!Yi(r)||0===r.length)return Promise.resolve([]);var s=t.getModule(Kc);return s.hasLocalGroup(n)&&(s.getLocalGroupProfile(n).memberNum=a),c=t._updateLocalGroupMemberMap(n,r),t.getModule(xc).getUserProfile({userIDList:r.map((function(e){return e.userID})),tagList:[y_.NICK,y_.AVATAR]})})).then((function(e){var o=e.data;if(!Yi(o)||0===o.length)return nm({memberList:[]});var a=o.map((function(e){return{userID:e.userID,nick:e.nick,avatar:e.avatar}}));return t._updateLocalGroupMemberMap(n,a),u.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(n," offset:").concat(r," count:").concat(s)).end(),xi.log("".concat(i," ok.")),W_({memberList:c})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];u.setError(e,o,r).end()})),xi.error("".concat(i," failed. error:"),e),om(e)}))}},{key:"getGroupMemberProfile",value:function(e){var t=this,n="".concat(this._className,".getGroupMemberProfile"),o=new Kp(Hg);o.setMessage(e.userIDList.length>5?"userIDList.length:".concat(e.userIDList.length):"userIDList:".concat(e.userIDList)),xi.log("".concat(n," groupID:").concat(e.groupID," userIDList:").concat(e.userIDList.join(","))),e.userIDList.length>50&&(e.userIDList=e.userIDList.slice(0,50));var r=e.groupID,a=e.userIDList;return this._getGroupMemberProfileAdvance(xn({},e,{userIDList:a})).then((function(e){var n=e.data.members;return Yi(n)&&0!==n.length?(t._updateLocalGroupMemberMap(r,n),t.getModule(xc).getUserProfile({userIDList:n.map((function(e){return e.userID})),tagList:[y_.NICK,y_.AVATAR]})):nm([])})).then((function(e){var n=e.data.map((function(e){return{userID:e.userID,nick:e.nick,avatar:e.avatar}}));t._updateLocalGroupMemberMap(r,n);var s=a.filter((function(e){return t.hasLocalGroupMember(r,e)})).map((function(e){return t.getLocalGroupMemberInfo(r,e)}));return o.setNetworkType(t.getNetworkType()).end(),W_({memberList:s})}))}},{key:"addGroupMember",value:function(e){var t=this,n="".concat(this._className,".addGroupMember"),o=e.groupID,r=this.getModule(Kc).getLocalGroupProfile(o),a=r.type,s=new Kp(jg);if(s.setMessage("groupID:".concat(o," groupType:").concat(a)),hu(a)){var i=new $_({code:vd.CANNOT_ADD_MEMBER_IN_AVCHATROOM,message:ip});return s.setCode(vd.CANNOT_ADD_MEMBER_IN_AVCHATROOM).setError(ip).setNetworkType(this.getNetworkType()).end(),om(i)}return e.userIDList=e.userIDList.map((function(e){return{userID:e}})),xi.log("".concat(n," groupID:").concat(o)),this.request({protocolName:rd,requestData:e}).then((function(o){var a=o.data.members;xi.log("".concat(n," ok"));var i=a.filter((function(e){return 1===e.result})).map((function(e){return e.userID})),u=a.filter((function(e){return 0===e.result})).map((function(e){return e.userID})),c=a.filter((function(e){return 2===e.result})).map((function(e){return e.userID})),l=a.filter((function(e){return 4===e.result})).map((function(e){return e.userID})),d="groupID:".concat(e.groupID,", ")+"successUserIDList:".concat(i,", ")+"failureUserIDList:".concat(u,", ")+"existedUserIDList:".concat(c,", ")+"overLimitUserIDList:".concat(l);return s.setNetworkType(t.getNetworkType()).setMoreMessage(d).end(),0===i.length?W_({successUserIDList:i,failureUserIDList:u,existedUserIDList:c,overLimitUserIDList:l}):(r.memberNum+=i.length,W_({successUserIDList:i,failureUserIDList:u,existedUserIDList:c,overLimitUserIDList:l,group:r}))})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];s.setError(e,o,r).end()})),xi.error("".concat(n," failed. error:"),e),om(e)}))}},{key:"deleteGroupMember",value:function(e){var t=this,n="".concat(this._className,".deleteGroupMember"),o=e.groupID,r=e.userIDList,a=new Kp(Yg),s="groupID:".concat(o," ").concat(r.length>5?"userIDList.length:".concat(r.length):"userIDList:".concat(r));a.setMessage(s),xi.log("".concat(n," groupID:").concat(o," userIDList:"),r);var i=this.getModule(Kc).getLocalGroupProfile(o);return hu(i.type)?om(new $_({code:vd.CANNOT_KICK_MEMBER_IN_AVCHATROOM,message:cp})):this.request({protocolName:ad,requestData:e}).then((function(){return a.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(n," ok")),i.memberNum--,t.deleteLocalGroupMembers(o,r),W_({group:i,userIDList:r})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),xi.error("".concat(n," failed. error:"),e),om(e)}))}},{key:"setGroupMemberMuteTime",value:function(e){var t=this,n=e.groupID,o=e.userID,r=e.muteTime,a="".concat(this._className,".setGroupMemberMuteTime");if(o===this.getMyUserID())return om(new $_({code:vd.CANNOT_MUTE_SELF,message:hp}));xi.log("".concat(a," groupID:").concat(n," userID:").concat(o));var s=new Kp(Wg);return s.setMessage("groupID:".concat(n," userID:").concat(o," muteTime:").concat(r)),this._modifyGroupMemberInfo({groupID:n,userID:o,muteTime:r}).then((function(e){s.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(a," ok"));var o=t.getModule(Kc);return W_({group:o.getLocalGroupProfile(n),member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];s.setError(e,o,r).end()})),xi.error("".concat(a," failed. error:"),e),om(e)}))}},{key:"setGroupMemberRole",value:function(e){var t=this,n="".concat(this._className,".setGroupMemberRole"),o=e.groupID,r=e.userID,a=e.role,s=this.getModule(Kc).getLocalGroupProfile(o);if(s.selfInfo.role!==so.GRP_MBR_ROLE_OWNER)return om(new $_({code:vd.NOT_OWNER,message:lp}));if([so.GRP_WORK,so.GRP_AVCHATROOM].includes(s.type))return om(new $_({code:vd.CANNOT_SET_MEMBER_ROLE_IN_WORK_AND_AVCHATROOM,message:dp}));if([so.GRP_MBR_ROLE_ADMIN,so.GRP_MBR_ROLE_MEMBER].indexOf(a)<0)return om(new $_({code:vd.INVALID_MEMBER_ROLE,message:pp}));if(r===this.getMyUserID())return om(new $_({code:vd.CANNOT_SET_SELF_MEMBER_ROLE,message:gp}));var i=new Kp(Jg);return i.setMessage("groupID:".concat(o," userID:").concat(r," role:").concat(a)),xi.log("".concat(n," groupID:").concat(o," userID:").concat(r)),this._modifyGroupMemberInfo({groupID:o,userID:r,role:a}).then((function(e){return i.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(n," ok")),W_({group:s,member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];i.setError(e,o,r).end()})),xi.error("".concat(n," failed. error:"),e),om(e)}))}},{key:"setGroupMemberNameCard",value:function(e){var t=this,n="".concat(this._className,".setGroupMemberNameCard"),o=e.groupID,r=e.userID,a=void 0===r?this.getMyUserID():r,s=e.nameCard;xi.log("".concat(n," groupID:").concat(o," userID:").concat(a));var i=new Kp(zg);return i.setMessage("groupID:".concat(o," userID:").concat(a," nameCard:").concat(s)),this._modifyGroupMemberInfo({groupID:o,userID:a,nameCard:s}).then((function(e){xi.log("".concat(n," ok")),i.setNetworkType(t.getNetworkType()).end();var r=t.getModule(Kc).getLocalGroupProfile(o);return a===t.getMyUserID()&&r&&r.setSelfNameCard(s),W_({group:r,member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];i.setError(e,o,r).end()})),xi.error("".concat(n," failed. error:"),e),om(e)}))}},{key:"setGroupMemberCustomField",value:function(e){var t=this,n="".concat(this._className,".setGroupMemberCustomField"),o=e.groupID,r=e.userID,a=void 0===r?this.getMyUserID():r,s=e.memberCustomField;xi.log("".concat(n," groupID:").concat(o," userID:").concat(a));var i=new Kp(Xg);return i.setMessage("groupID:".concat(o," userID:").concat(a," memberCustomField:").concat(JSON.stringify(s))),this._modifyGroupMemberInfo({groupID:o,userID:a,memberCustomField:s}).then((function(e){i.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(n," ok"));var r=t.getModule(Kc).getLocalGroupProfile(o);return W_({group:r,member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];i.setError(e,o,r).end()})),xi.error("".concat(n," failed. error:"),e),om(e)}))}},{key:"setMessageRemindType",value:function(e){var t=this,n="".concat(this._className,".setMessageRemindType"),o=new Kp(Ag);o.setMessage("groupID:".concat(e.groupID)),xi.log("".concat(n," groupID:").concat(e.groupID));var r=e.groupID,a=e.messageRemindType;return this._modifyGroupMemberInfo({groupID:r,messageRemindType:a,userID:this.getMyUserID()}).then((function(){o.setNetworkType(t.getNetworkType()).end(),xi.log("".concat(n," ok. groupID:").concat(e.groupID));var r=t.getModule(Kc).getLocalGroupProfile(e.groupID);return r&&(r.selfInfo.messageRemindType=a),W_({group:r})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),xi.error("".concat(n," failed. error:"),e),om(e)}))}},{key:"_modifyGroupMemberInfo",value:function(e){var t=this,n=e.groupID,o=e.userID;return this.request({protocolName:sd,requestData:e}).then((function(){if(t.hasLocalGroupMember(n,o)){var r=t.getLocalGroupMemberInfo(n,o);return Wi(e.muteTime)||r.updateMuteUntil(e.muteTime),Wi(e.role)||r.updateRole(e.role),Wi(e.nameCard)||r.updateNameCard(e.nameCard),Wi(e.memberCustomField)||r.updateMemberCustomField(e.memberCustomField),r}return t.getGroupMemberProfile({groupID:n,userIDList:[o]}).then((function(e){return Qn(e.data.memberList,1)[0]}))}))}},{key:"_getGroupMemberProfileAdvance",value:function(e){return this.request({protocolName:od,requestData:xn({},e,{memberInfoFilter:e.memberInfoFilter?e.memberInfoFilter:["Role","JoinTime","NameCard","ShutUpUntil"]})})}},{key:"_updateLocalGroupMemberMap",value:function(e,t){var n=this;return Yi(t)&&0!==t.length?t.map((function(t){return n.hasLocalGroupMember(e,t.userID)?n.getLocalGroupMemberInfo(e,t.userID).updateMember(t):n.setLocalGroupMember(e,new jm(t)),n.getLocalGroupMemberInfo(e,t.userID)})):[]}},{key:"deleteLocalGroupMembers",value:function(e,t){var n=this.groupMemberListMap.get(e);n&&t.forEach((function(e){n.delete(e)}))}},{key:"getLocalGroupMemberInfo",value:function(e,t){return this.groupMemberListMap.has(e)?this.groupMemberListMap.get(e).get(t):null}},{key:"setLocalGroupMember",value:function(e,t){if(this.groupMemberListMap.has(e))this.groupMemberListMap.get(e).set(t.userID,t);else{var n=(new Map).set(t.userID,t);this.groupMemberListMap.set(e,n)}}},{key:"getLocalGroupMemberList",value:function(e){return this.groupMemberListMap.get(e)}},{key:"hasLocalGroupMember",value:function(e,t){return this.groupMemberListMap.has(e)&&this.groupMemberListMap.get(e).has(t)}},{key:"hasLocalGroupMemberMap",value:function(e){return this.groupMemberListMap.has(e)}},{key:"reset",value:function(){this.groupMemberListMap.clear()}}]),n}(il),Wm=function(){function e(t){Pn(this,e),this._userModule=t,this._className="ProfileHandler",this.TAG="profile",this.accountProfileMap=new Map,this.expirationTime=864e5}return Un(e,[{key:"setExpirationTime",value:function(e){this.expirationTime=e}},{key:"getUserProfile",value:function(e){var t=this,n=e.userIDList;e.fromAccount=this._userModule.getMyAccount(),n.length>100&&(xi.warn("".concat(this._className,".getUserProfile 获取用户资料人数不能超过100人")),n.length=100);for(var o,r=[],a=[],s=0,i=n.length;s<i;s++)o=n[s],this._userModule.isMyFriend(o)&&this._containsAccount(o)?a.push(this._getProfileFromMap(o)):r.push(o);if(0===r.length)return nm(a);e.toAccount=r;var u=e.bFromGetMyProfile||!1,c=[];e.toAccount.forEach((function(e){c.push({toAccount:e,standardSequence:0,customSequence:0})})),e.userItem=c;var l=new Kp(th);return l.setMessage(n.length>5?"userIDList.length:".concat(n.length):"userIDList:".concat(n)),this._userModule.request({protocolName:fl,requestData:e}).then((function(e){l.setNetworkType(t._userModule.getNetworkType()).end(),xi.info("".concat(t._className,".getUserProfile ok"));var n=t._handleResponse(e).concat(a);return W_(u?n[0]:n)})).catch((function(e){return t._userModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];l.setError(e,o,r).end()})),xi.error("".concat(t._className,".getUserProfile failed. error:"),e),om(e)}))}},{key:"getMyProfile",value:function(){var e=this._userModule.getMyAccount();if(xi.log("".concat(this._className,".getMyProfile myAccount:").concat(e)),this._fillMap(),this._containsAccount(e)){var t=this._getProfileFromMap(e);return xi.debug("".concat(this._className,".getMyProfile from cache, myProfile:")+JSON.stringify(t)),nm(t)}return this.getUserProfile({fromAccount:e,userIDList:[e],bFromGetMyProfile:!0})}},{key:"_handleResponse",value:function(e){for(var t,n,o=eu.now(),r=e.data.userProfileItem,a=[],s=0,i=r.length;s<i;s++)"@TLS#NOT_FOUND"!==r[s].to&&""!==r[s].to&&(t=r[s].to,n=this._updateMap(t,this._getLatestProfileFromResponse(t,r[s].profileItem)),a.push(n));return xi.log("".concat(this._className,"._handleResponse cost ").concat(eu.now()-o," ms")),a}},{key:"_getLatestProfileFromResponse",value:function(e,t){var n={};if(n.userID=e,n.profileCustomField=[],!Au(t))for(var o=0,r=t.length;o<r;o++)if(t[o].tag.indexOf("Tag_Profile_Custom")>-1)n.profileCustomField.push({key:t[o].tag,value:t[o].value});else switch(t[o].tag){case y_.NICK:n.nick=t[o].value;break;case y_.GENDER:n.gender=t[o].value;break;case y_.BIRTHDAY:n.birthday=t[o].value;break;case y_.LOCATION:n.location=t[o].value;break;case y_.SELFSIGNATURE:n.selfSignature=t[o].value;break;case y_.ALLOWTYPE:n.allowType=t[o].value;break;case y_.LANGUAGE:n.language=t[o].value;break;case y_.AVATAR:n.avatar=t[o].value;break;case y_.MESSAGESETTINGS:n.messageSettings=t[o].value;break;case y_.ADMINFORBIDTYPE:n.adminForbidType=t[o].value;break;case y_.LEVEL:n.level=t[o].value;break;case y_.ROLE:n.role=t[o].value;break;default:xi.warn("".concat(this._className,"._handleResponse unknown tag:"),t[o].tag,t[o].value)}return n}},{key:"updateMyProfile",value:function(e){var t=this,n="".concat(this._className,".updateMyProfile"),o=new Kp(nh);o.setMessage(JSON.stringify(e));var r=(new Tm).validate(e);if(!r.valid)return o.setCode(vd.UPDATE_PROFILE_INVALID_PARAM).setMoreMessage("".concat(n," info:").concat(r.tips)).setNetworkType(this._userModule.getNetworkType()).end(),xi.error("".concat(n," info:").concat(r.tips,"，请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#updateMyProfile")),om({code:vd.UPDATE_PROFILE_INVALID_PARAM,message:fp});var a=[];for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&("profileCustomField"===s?e.profileCustomField.forEach((function(e){a.push({tag:e.key,value:e.value})})):a.push({tag:y_[s.toUpperCase()],value:e[s]}));return 0===a.length?(o.setCode(vd.UPDATE_PROFILE_NO_KEY).setMoreMessage(_p).setNetworkType(this._userModule.getNetworkType()).end(),xi.error("".concat(n," info:").concat(_p,"，请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#updateMyProfile")),om({code:vd.UPDATE_PROFILE_NO_KEY,message:_p})):this._userModule.request({protocolName:_l,requestData:{fromAccount:this._userModule.getMyAccount(),profileItem:a}}).then((function(r){o.setNetworkType(t._userModule.getNetworkType()).end(),xi.info("".concat(n," ok"));var a=t._updateMap(t._userModule.getMyAccount(),e);return t._userModule.emitOuterEvent(ao.PROFILE_UPDATED,[a]),nm(a)})).catch((function(e){return t._userModule.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),xi.error("".concat(n," failed. error:"),e),om(e)}))}},{key:"onProfileModified",value:function(e){var t=e.dataList;if(!Au(t)){var n,o,r=t.length;xi.info("".concat(this._className,".onProfileModified count:").concat(r));for(var a=[],s=this._userModule.getModule(jc),i=0;i<r;i++)n=t[i].userID,o=this._updateMap(n,this._getLatestProfileFromResponse(n,t[i].profileList)),a.push(o),n===this._userModule.getMyAccount()&&s.onMyProfileModified({latestNick:o.nick,latestAvatar:o.avatar});this._userModule.emitInnerEvent(hm.PROFILE_UPDATED,a),this._userModule.emitOuterEvent(ao.PROFILE_UPDATED,a)}}},{key:"_fillMap",value:function(){if(0===this.accountProfileMap.size){for(var e=this._getCachedProfiles(),t=Date.now(),n=0,o=e.length;n<o;n++)t-e[n].lastUpdatedTime<this.expirationTime&&this.accountProfileMap.set(e[n].userID,e[n]);xi.log("".concat(this._className,"._fillMap from cache, map.size:").concat(this.accountProfileMap.size))}}},{key:"_updateMap",value:function(e,t){var n,o=Date.now();return this._containsAccount(e)?(n=this._getProfileFromMap(e),t.profileCustomField&&pu(n.profileCustomField,t.profileCustomField),tu(n,t,["profileCustomField"]),n.lastUpdatedTime=o):(n=new Tm(t),(this._userModule.isMyFriend(e)||e===this._userModule.getMyAccount())&&(n.lastUpdatedTime=o,this.accountProfileMap.set(e,n))),this._flushMap(e===this._userModule.getMyAccount()),n}},{key:"_flushMap",value:function(e){var t=Zn(this.accountProfileMap.values()),n=this._userModule.getStorageModule();xi.debug("".concat(this._className,"._flushMap length:").concat(t.length," flushAtOnce:").concat(e)),n.setItem(this.TAG,t,e)}},{key:"_containsAccount",value:function(e){return this.accountProfileMap.has(e)}},{key:"_getProfileFromMap",value:function(e){return this.accountProfileMap.get(e)}},{key:"_getCachedProfiles",value:function(){var e=this._userModule.getStorageModule().getItem(this.TAG);return Au(e)?[]:e}},{key:"onConversationsProfileUpdated",value:function(e){for(var t,n,o,r=[],a=0,s=e.length;a<s;a++)n=(t=e[a]).userID,this._userModule.isMyFriend(n)||(this._containsAccount(n)?(o=this._getProfileFromMap(n),tu(o,t)>0&&r.push(n)):r.push(t.userID));0!==r.length&&(xi.info("".concat(this._className,".onConversationsProfileUpdated toAccountList:").concat(r)),this.getUserProfile({userIDList:r}))}},{key:"getNickAndAvatarByUserID",value:function(e){if(this._containsAccount(e)){var t=this._getProfileFromMap(e);return{nick:t.nick,avatar:t.avatar}}return{nick:"",avatar:""}}},{key:"reset",value:function(){this._flushMap(!0),this.accountProfileMap.clear()}}]),e}(),zm=function e(t){Pn(this,e),Au||(this.userID=t.userID||"",this.timeStamp=t.timeStamp||0)},Jm=function(){function e(t){Pn(this,e),this._userModule=t,this._className="BlacklistHandler",this._blacklistMap=new Map,this.startIndex=0,this.maxLimited=100,this.currentSequence=0}return Un(e,[{key:"getLocalBlacklist",value:function(){return Zn(this._blacklistMap.keys())}},{key:"getBlacklist",value:function(){var e=this,t="".concat(this._className,".getBlacklist"),n={fromAccount:this._userModule.getMyAccount(),maxLimited:this.maxLimited,startIndex:0,lastSequence:this.currentSequence},o=new Kp(oh);return this._userModule.request({protocolName:ml,requestData:n}).then((function(n){var r=n.data,a=r.blackListItem,s=r.currentSequence,i=Au(a)?0:a.length;o.setNetworkType(e._userModule.getNetworkType()).setMessage("blackList count:".concat(i)).end(),xi.info("".concat(t," ok")),e.currentSequence=s,e._handleResponse(a,!0),e._userModule.emitOuterEvent(ao.BLACKLIST_UPDATED,Zn(e._blacklistMap.keys()))})).catch((function(n){return e._userModule.probeNetwork().then((function(e){var t=Qn(e,2),r=t[0],a=t[1];o.setError(n,r,a).end()})),xi.error("".concat(t," failed. error:"),n),om(n)}))}},{key:"addBlacklist",value:function(e){var t=this,n="".concat(this._className,".addBlacklist"),o=new Kp(rh);if(!Yi(e.userIDList))return o.setCode(vd.ADD_BLACKLIST_INVALID_PARAM).setMessage(mp).setNetworkType(this._userModule.getNetworkType()).end(),xi.error("".concat(n," options.userIDList 必需是数组")),om({code:vd.ADD_BLACKLIST_INVALID_PARAM,message:mp});var r=this._userModule.getMyAccount();return 1===e.userIDList.length&&e.userIDList[0]===r?(o.setCode(vd.CANNOT_ADD_SELF_TO_BLACKLIST).setMessage(Mp).setNetworkType(this._userModule.getNetworkType()).end(),xi.error("".concat(n," 不能把自己拉黑")),om({code:vd.CANNOT_ADD_SELF_TO_BLACKLIST,message:Mp})):(e.userIDList.includes(r)&&(e.userIDList=e.userIDList.filter((function(e){return e!==r})),xi.warn("".concat(n," 不能把自己拉黑，已过滤"))),e.fromAccount=this._userModule.getMyAccount(),e.toAccount=e.userIDList,this._userModule.request({protocolName:vl,requestData:e}).then((function(r){return o.setNetworkType(t._userModule.getNetworkType()).setMessage(e.userIDList.length>5?"userIDList.length:".concat(e.userIDList.length):"userIDList:".concat(e.userIDList)).end(),xi.info("".concat(n," ok")),t._handleResponse(r.resultItem,!0),W_(Zn(t._blacklistMap.keys()))})).catch((function(e){return t._userModule.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),xi.error("".concat(n," failed. error:"),e),om(e)})))}},{key:"_handleResponse",value:function(e,t){if(!Au(e))for(var n,o,r,a=0,s=e.length;a<s;a++)o=e[a].to,r=e[a].resultCode,(Wi(r)||0===r)&&(t?((n=this._blacklistMap.has(o)?this._blacklistMap.get(o):new zm).userID=o,!Au(e[a].addBlackTimeStamp)&&(n.timeStamp=e[a].addBlackTimeStamp),this._blacklistMap.set(o,n)):this._blacklistMap.has(o)&&(n=this._blacklistMap.get(o),this._blacklistMap.delete(o)));xi.log("".concat(this._className,"._handleResponse total:").concat(this._blacklistMap.size," bAdd:").concat(t))}},{key:"deleteBlacklist",value:function(e){var t=this,n="".concat(this._className,".deleteBlacklist"),o=new Kp(ah);return Yi(e.userIDList)?(e.fromAccount=this._userModule.getMyAccount(),e.toAccount=e.userIDList,this._userModule.request({protocolName:Ml,requestData:e}).then((function(r){return o.setNetworkType(t._userModule.getNetworkType()).setMessage(e.userIDList.length>5?"userIDList.length:".concat(e.userIDList.length):"userIDList:".concat(e.userIDList)).end(),xi.info("".concat(n," ok")),t._handleResponse(r.data.resultItem,!1),W_(Zn(t._blacklistMap.keys()))})).catch((function(e){return t._userModule.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),xi.error("".concat(n," failed. error:"),e),om(e)}))):(o.setCode(vd.DEL_BLACKLIST_INVALID_PARAM).setMessage(vp).setNetworkType(this._userModule.getNetworkType()).end(),xi.error("".concat(n," options.userIDList 必需是数组")),om({code:vd.DEL_BLACKLIST_INVALID_PARAM,message:vp}))}},{key:"onAccountDeleted",value:function(e){for(var t,n=[],o=0,r=e.length;o<r;o++)t=e[o],this._blacklistMap.has(t)&&(this._blacklistMap.delete(t),n.push(t));n.length>0&&(xi.log("".concat(this._className,".onAccountDeleted count:").concat(n.length," userIDList:"),n),this._userModule.emitOuterEvent(ao.BLACKLIST_UPDATED,Zn(this._blacklistMap.keys())))}},{key:"onAccountAdded",value:function(e){for(var t,n=[],o=0,r=e.length;o<r;o++)t=e[o],this._blacklistMap.has(t)||(this._blacklistMap.set(t,new zm({userID:t})),n.push(t));n.length>0&&(xi.log("".concat(this._className,".onAccountAdded count:").concat(n.length," userIDList:"),n),this._userModule.emitOuterEvent(ao.BLACKLIST_UPDATED,Zn(this._blacklistMap.keys())))}},{key:"reset",value:function(){this._blacklistMap.clear(),this.startIndex=0,this.maxLimited=100,this.currentSequence=0}}]),e}(),Xm=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="UserModule",o._profileHandler=new Wm(zn(o)),o._blacklistHandler=new Jm(zn(o)),o.getInnerEmitterInstance().on(hm.CONTEXT_A2KEY_AND_TINYID_UPDATED,o.onContextUpdated,zn(o)),o}return Un(n,[{key:"onContextUpdated",value:function(e){this._profileHandler.getMyProfile(),this._blacklistHandler.getBlacklist()}},{key:"onProfileModified",value:function(e){this._profileHandler.onProfileModified(e)}},{key:"onRelationChainModified",value:function(e){var t=e.dataList;if(!Au(t)){var n=[];t.forEach((function(e){e.blackListDelAccount&&n.push.apply(n,Zn(e.blackListDelAccount))})),n.length>0&&this._blacklistHandler.onAccountDeleted(n);var o=[];t.forEach((function(e){e.blackListAddAccount&&o.push.apply(o,Zn(e.blackListAddAccount))})),o.length>0&&this._blacklistHandler.onAccountAdded(o)}}},{key:"onConversationsProfileUpdated",value:function(e){this._profileHandler.onConversationsProfileUpdated(e)}},{key:"getMyAccount",value:function(){return this.getMyUserID()}},{key:"getMyProfile",value:function(){return this._profileHandler.getMyProfile()}},{key:"getStorageModule",value:function(){return this.getModule(Wc)}},{key:"isMyFriend",value:function(e){var t=this.getModule(Bc);return!!t&&t.isMyFriend(e)}},{key:"getUserProfile",value:function(e){return this._profileHandler.getUserProfile(e)}},{key:"updateMyProfile",value:function(e){return this._profileHandler.updateMyProfile(e)}},{key:"getNickAndAvatarByUserID",value:function(e){return this._profileHandler.getNickAndAvatarByUserID(e)}},{key:"getLocalBlacklist",value:function(){var e=this._blacklistHandler.getLocalBlacklist();return nm(e)}},{key:"addBlacklist",value:function(e){return this._blacklistHandler.addBlacklist(e)}},{key:"deleteBlacklist",value:function(e){return this._blacklistHandler.deleteBlacklist(e)}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset")),this._profileHandler.reset(),this._blacklistHandler.reset()}}]),n}(il),Qm=function(){function e(t,n){Pn(this,e),this._moduleManager=t,this._isLoggedIn=!1,this._SDKAppID=n.SDKAppID,this._userID=n.userID||"",this._userSig=n.userSig||"",this._version="2.15.0",this._a2Key="",this._tinyID="",this._contentType="json",this._unlimitedAVChatRoom=n.unlimitedAVChatRoom,this._scene=n.scene||"",this._oversea=n.oversea,this._instanceID=n.instanceID,this._statusInstanceID=0}return Un(e,[{key:"isLoggedIn",value:function(){return this._isLoggedIn}},{key:"isOversea",value:function(){return this._oversea}},{key:"isUnlimitedAVChatRoom",value:function(){return this._unlimitedAVChatRoom}},{key:"getUserID",value:function(){return this._userID}},{key:"setUserID",value:function(e){this._userID=e}},{key:"setUserSig",value:function(e){this._userSig=e}},{key:"getUserSig",value:function(){return this._userSig}},{key:"getSDKAppID",value:function(){return this._SDKAppID}},{key:"getTinyID",value:function(){return this._tinyID}},{key:"setTinyID",value:function(e){this._tinyID=e,this._isLoggedIn=!0}},{key:"getScene",value:function(){return function(){var e=!1,t=[];ri&&(t=Object.keys(si)),ai&&(t=Object.keys(window));for(var n=0,o=t.length;n<o;n++)if(t[n].toLowerCase().includes("uikit")){e=!0;break}return t=null,e}()?"tuikit":this._scene}},{key:"getInstanceID",value:function(){return this._instanceID}},{key:"getStatusInstanceID",value:function(){return this._statusInstanceID}},{key:"setStatusInstanceID",value:function(e){this._statusInstanceID=e}},{key:"getVersion",value:function(){return this._version}},{key:"getA2Key",value:function(){return this._a2Key}},{key:"setA2Key",value:function(e){this._a2Key=e}},{key:"getContentType",value:function(){return this._contentType}},{key:"reset",value:function(){this._isLoggedIn=!1,this._userSig="",this._a2Key="",this._tinyID="",this._statusInstanceID=0}}]),e}(),Zm=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="SignModule",o._helloInterval=120,o._lastLoginTs=0,fm.mixin(zn(o)),o}return Un(n,[{key:"onCheckTimer",value:function(e){this.isLoggedIn()&&e%this._helloInterval==0&&this._hello()}},{key:"login",value:function(e){if(this.isLoggedIn()){var t="您已经登录账号".concat(e.userID,"！如需切换账号登录，请先调用 logout 接口登出，再调用 login 接口登录。");return xi.warn(t),nm({actionStatus:"OK",errorCode:0,errorInfo:t,repeatLogin:!0})}if(Date.now()-this._lastLoginTs<=15e3)return xi.warn("您正在尝试登录账号".concat(e.userID,"！请勿重复登录。")),om({code:vd.REPEAT_LOGIN,message:Dd});xi.log("".concat(this._className,".login userID:").concat(e.userID));var n=this._checkLoginInfo(e);if(0!==n.code)return om(n);var o=this.getModule(Yc),r=e.userID,a=e.userSig;return o.setUserID(r),o.setUserSig(a),this.getModule(el).updateProtocolConfig(),this._login()}},{key:"_login",value:function(){var e=this,t=this.getModule(Yc),n=new Kp(jp);return n.setMessage("".concat(t.getScene())).setMoreMessage("identifier:".concat(this.getMyUserID())),this._lastLoginTs=Date.now(),this.request({protocolName:ul}).then((function(o){e._lastLoginTs=0;var r=Date.now(),a=null,s=o.data,i=s.a2Key,u=s.tinyID,c=s.helloInterval,l=s.instanceID,d=s.timeStamp;xi.log("".concat(e._className,".login ok. helloInterval:").concat(c," instanceID:").concat(l," timeStamp:").concat(d));var p=1e3*d,g=r-n.getStartTs(),h=p+parseInt(g/2)-r,f=n.getStartTs()+h;if(n.start(f),function(e,t){wi=t;var n=new Date;n.setTime(e),xi.info("baseTime from server: ".concat(n," offset: ").concat(wi))}(p,h),!u)throw a=new $_({code:vd.NO_TINYID,message:Td}),n.setError(a,!0,e.getNetworkType()).end(),a;if(!i)throw a=new $_({code:vd.NO_A2KEY,message:Sd}),n.setError(a,!0,e.getNetworkType()).end(),a;return n.setNetworkType(e.getNetworkType()).setMoreMessage("helloInterval:".concat(c," instanceID:").concat(l," offset:").concat(h)).end(),t.setA2Key(i),t.setTinyID(u),t.setStatusInstanceID(l),e.getModule(el).updateProtocolConfig(),e.emitInnerEvent(hm.CONTEXT_A2KEY_AND_TINYID_UPDATED),e._helloInterval=c,e.triggerReady(),e._fetchCloudControlConfig(),o})).catch((function(t){return e.probeNetwork().then((function(e){var o=Qn(e,2),r=o[0],a=o[1];n.setError(t,r,a).end(!0)})),xi.error("".concat(e._className,".login failed. error:"),t),e._moduleManager.onLoginFailed(),om(t)}))}},{key:"logout",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(!this.isLoggedIn())return om({code:vd.USER_NOT_LOGGED_IN,message:Ed});var n=new Kp(Yp);return n.setNetworkType(this.getNetworkType()).setMessage("identifier:".concat(this.getMyUserID())).end(!0),xi.info("".concat(this._className,".logout type:").concat(t)),this.request({protocolName:cl,requestData:{type:t}}).then((function(){return e.resetReady(),nm({})})).catch((function(t){return xi.error("".concat(e._className,"._logout error:"),t),e.resetReady(),nm({})}))}},{key:"_fetchCloudControlConfig",value:function(){this.getModule(ol).fetchConfig()}},{key:"_hello",value:function(){var e=this;this.request({protocolName:ll}).catch((function(t){xi.warn("".concat(e._className,"._hello error:"),t)}))}},{key:"_checkLoginInfo",value:function(e){var t=0,n="";return Au(this.getModule(Yc).getSDKAppID())?(t=vd.NO_SDKAPPID,n=Md):Au(e.userID)?(t=vd.NO_IDENTIFIER,n=yd):Au(e.userSig)&&(t=vd.NO_USERSIG,n=Id),{code:t,message:n}}},{key:"onMultipleAccountKickedOut",value:function(e){var t=this;new Kp(Wp).setNetworkType(this.getNetworkType()).setMessage("type:".concat(so.KICKED_OUT_MULT_ACCOUNT," newInstanceInfo:").concat(JSON.stringify(e))).end(!0),xi.warn("".concat(this._className,".onMultipleAccountKickedOut userID:").concat(this.getMyUserID()," newInstanceInfo:"),e),this.logout(1).then((function(){t.emitOuterEvent(ao.KICKED_OUT,{type:so.KICKED_OUT_MULT_ACCOUNT}),t._moduleManager.reset()}))}},{key:"onMultipleDeviceKickedOut",value:function(e){var t=this;new Kp(Wp).setNetworkType(this.getNetworkType()).setMessage("type:".concat(so.KICKED_OUT_MULT_DEVICE," newInstanceInfo:").concat(JSON.stringify(e))).end(!0),xi.warn("".concat(this._className,".onMultipleDeviceKickedOut userID:").concat(this.getMyUserID()," newInstanceInfo:"),e),this.logout(1).then((function(){t.emitOuterEvent(ao.KICKED_OUT,{type:so.KICKED_OUT_MULT_DEVICE}),t._moduleManager.reset()}))}},{key:"onUserSigExpired",value:function(){new Kp(Wp).setNetworkType(this.getNetworkType()).setMessage(so.KICKED_OUT_USERSIG_EXPIRED).end(!0),xi.warn("".concat(this._className,".onUserSigExpired: userSig 签名过期被踢下线")),0!==this.getModule(Yc).getStatusInstanceID()&&(this.emitOuterEvent(ao.KICKED_OUT,{type:so.KICKED_OUT_USERSIG_EXPIRED}),this._moduleManager.reset())}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset")),this.resetReady(),this._helloInterval=120,this._lastLoginTs=0}}]),n}(il);function $m(){return null}var ev=function(){function e(t){Pn(this,e),this._moduleManager=t,this._className="StorageModule",this._storageQueue=new Map,this._errorTolerantHandle()}return Un(e,[{key:"_errorTolerantHandle",value:function(){ri||!Wi(window)&&!Wi(window.localStorage)||(this.getItem=$m,this.setItem=$m,this.removeItem=$m,this.clear=$m)}},{key:"onCheckTimer",value:function(e){if(e%20==0){if(0===this._storageQueue.size)return;this._doFlush()}}},{key:"_doFlush",value:function(){try{var e,t=ro(this._storageQueue);try{for(t.s();!(e=t.n()).done;){var n=Qn(e.value,2),o=n[0],r=n[1];this._setStorageSync(this._getKey(o),r)}}catch(i){t.e(i)}finally{t.f()}this._storageQueue.clear()}catch(Xv){xi.warn("".concat(this._className,"._doFlush error:"),Xv)}}},{key:"_getPrefix",value:function(){var e=this._moduleManager.getModule(Yc);return"TIM_".concat(e.getSDKAppID(),"_").concat(e.getUserID(),"_")}},{key:"_getKey",value:function(e){return"".concat(this._getPrefix()).concat(e)}},{key:"getItem",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];try{var n=t?this._getKey(e):e;return this._getStorageSync(n)}catch(Xv){return xi.warn("".concat(this._className,".getItem error:"),Xv),{}}}},{key:"setItem",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(n){var r=o?this._getKey(e):e;this._setStorageSync(r,t)}else this._storageQueue.set(e,t)}},{key:"clear",value:function(){try{ri?si.clearStorageSync():localStorage&&localStorage.clear()}catch(Xv){xi.warn("".concat(this._className,".clear error:"),Xv)}}},{key:"removeItem",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];try{var n=t?this._getKey(e):e;this._removeStorageSync(n)}catch(Xv){xi.warn("".concat(this._className,".removeItem error:"),Xv)}}},{key:"getSize",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"b";try{var o={size:0,limitSize:5242880,unit:n};if(Object.defineProperty(o,"leftSize",{enumerable:!0,get:function(){return o.limitSize-o.size}}),ri&&(o.limitSize=1024*si.getStorageInfoSync().limitSize),e)o.size=JSON.stringify(this.getItem(e)).length+this._getKey(e).length;else if(ri){var r=si.getStorageInfoSync(),a=r.keys;a.forEach((function(e){o.size+=JSON.stringify(t._getStorageSync(e)).length+t._getKey(e).length}))}else if(localStorage)for(var s in localStorage)localStorage.hasOwnProperty(s)&&(o.size+=localStorage.getItem(s).length+s.length);return this._convertUnit(o)}catch(Xv){xi.warn("".concat(this._className," error:"),Xv)}}},{key:"_convertUnit",value:function(e){var t={},n=e.unit;for(var o in t.unit=n,e)"number"==typeof e[o]&&("kb"===n.toLowerCase()?t[o]=Math.round(e[o]/1024):"mb"===n.toLowerCase()?t[o]=Math.round(e[o]/1024/1024):t[o]=e[o]);return t}},{key:"_setStorageSync",value:function(e,t){ri?ni?my.setStorageSync({key:e,data:t}):si.setStorageSync(e,t):localStorage&&localStorage.setItem(e,JSON.stringify(t))}},{key:"_getStorageSync",value:function(e){return ri?ni?my.getStorageSync({key:e}).data:si.getStorageSync(e):localStorage?JSON.parse(localStorage.getItem(e)):{}}},{key:"_removeStorageSync",value:function(e){ri?ni?my.removeStorageSync({key:e}):si.removeStorageSync(e):localStorage&&localStorage.removeItem(e)}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset")),this._doFlush()}}]),e}(),tv=function(){function e(t){Pn(this,e),this._className="SSOLogBody",this._report=[]}return Un(e,[{key:"pushIn",value:function(e){xi.debug("".concat(this._className,".pushIn"),this._report.length,e),this._report.push(e)}},{key:"backfill",value:function(e){var t;Yi(e)&&0!==e.length&&(xi.debug("".concat(this._className,".backfill"),this._report.length,e.length),(t=this._report).unshift.apply(t,Zn(e)))}},{key:"getLogsNumInMemory",value:function(){return this._report.length}},{key:"isEmpty",value:function(){return 0===this._report.length}},{key:"_reset",value:function(){this._report.length=0,this._report=[]}},{key:"getLogsInMemory",value:function(){var e=this._report.slice();return this._reset(),e}}]),e}(),nv=function(e){var t=e.getModule(Yc);return{SDKType:10,SDKAppID:t.getSDKAppID(),SDKVersion:t.getVersion(),tinyID:Number(t.getTinyID()),userID:t.getUserID(),platform:e.getPlatform(),instanceID:t.getInstanceID(),traceID:Pi()}},ov=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;Pn(this,n),(o=t.call(this,e))._className="EventStatModule",o.TAG="im-ssolog-event",o._reportBody=new tv,o.MIN_THRESHOLD=20,o.MAX_THRESHOLD=100,o.WAITING_TIME=6e4,o.REPORT_LEVEL=[4,5,6],o.REPORT_SDKAPPID_BLACKLIST=[],o.REPORT_TINYID_WHITELIST=[],o._lastReportTime=Date.now();var r=o.getInnerEmitterInstance();return r.on(hm.CONTEXT_A2KEY_AND_TINYID_UPDATED,o._onLoginSuccess,zn(o)),r.on(hm.CLOUD_CONFIG_UPDATED,o._onCloudConfigUpdated,zn(o)),o}return Un(n,[{key:"reportAtOnce",value:function(){xi.debug("".concat(this._className,".reportAtOnce")),this._report()}},{key:"_onLoginSuccess",value:function(){var e=this,t=this.getModule(Wc),n=t.getItem(this.TAG,!1);!Au(n)&&Ji(n.forEach)&&(xi.log("".concat(this._className,"._onLoginSuccess get ssolog in storage, count:").concat(n.length)),n.forEach((function(t){e._reportBody.pushIn(t)})),t.removeItem(this.TAG,!1))}},{key:"_onCloudConfigUpdated",value:function(){var e=this.getCloudConfig("evt_rpt_threshold"),t=this.getCloudConfig("evt_rpt_waiting"),n=this.getCloudConfig("evt_rpt_level"),o=this.getCloudConfig("evt_rpt_sdkappid_bl"),r=this.getCloudConfig("evt_rpt_tinyid_wl");Wi(e)||(this.MIN_THRESHOLD=Number(e)),Wi(t)||(this.WAITING_TIME=Number(t)),Wi(n)||(this.REPORT_LEVEL=n.split(",").map((function(e){return Number(e)}))),Wi(o)||(this.REPORT_SDKAPPID_BLACKLIST=o.split(",").map((function(e){return Number(e)}))),Wi(r)||(this.REPORT_TINYID_WHITELIST=r.split(","))}},{key:"pushIn",value:function(e){e instanceof Kp&&(e.updateTimeStamp(),this._reportBody.pushIn(e),this._reportBody.getLogsNumInMemory()>=this.MIN_THRESHOLD&&this._report())}},{key:"onCheckTimer",value:function(){Date.now()<this._lastReportTime+this.WAITING_TIME||this._reportBody.isEmpty()||this._report()}},{key:"_filterLogs",value:function(e){var t=this,n=this.getModule(Yc),o=n.getSDKAppID(),r=n.getTinyID();return Du(this.REPORT_SDKAPPID_BLACKLIST,o)&&!ku(this.REPORT_TINYID_WHITELIST,r)?[]:e.filter((function(e){return t.REPORT_LEVEL.includes(e.level)}))}},{key:"_report",value:function(){var e=this;if(!this._reportBody.isEmpty()){var t=this._reportBody.getLogsInMemory(),n=this._filterLogs(t);if(0!==n.length){var o={header:nv(this),event:n};this.request({protocolName:cd,requestData:xn({},o)}).then((function(){e._lastReportTime=Date.now()})).catch((function(n){xi.warn("".concat(e._className,".report failed. networkType:").concat(e.getNetworkType()," error:"),n),e._reportBody.backfill(t),e._reportBody.getLogsNumInMemory()>e.MAX_THRESHOLD&&e._flushAtOnce()}))}else this._lastReportTime=Date.now()}}},{key:"_flushAtOnce",value:function(){var e=this.getModule(Wc),t=e.getItem(this.TAG,!1),n=this._reportBody.getLogsInMemory();if(Au(t))xi.log("".concat(this._className,"._flushAtOnce count:").concat(n.length)),e.setItem(this.TAG,n,!0,!1);else{var o=n.concat(t);o.length>this.MAX_THRESHOLD&&(o=o.slice(0,this.MAX_THRESHOLD)),xi.log("".concat(this._className,"._flushAtOnce count:").concat(o.length)),e.setItem(this.TAG,o,!0,!1)}}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset")),this._lastReportTime=0,this._report(),this.REPORT_SDKAPPID_BLACKLIST=[],this.REPORT_TINYID_WHITELIST=[]}}]),n}(il),rv="none",av="online",sv=function(){function e(t){Pn(this,e),this._moduleManager=t,this._networkType="",this._className="NetMonitorModule",this.MAX_WAIT_TIME=3e3}return Un(e,[{key:"start",value:function(){var e=this;if(ri){si.getNetworkType({success:function(t){e._networkType=t.networkType,t.networkType===rv?xi.warn("".concat(e._className,".start no network, please check!")):xi.info("".concat(e._className,".start networkType:").concat(t.networkType))}});var t=this._onNetworkStatusChange.bind(this);si.offNetworkStatusChange&&(oi||ei?si.offNetworkStatusChange(t):si.offNetworkStatusChange()),si.onNetworkStatusChange(t)}else this._networkType=av}},{key:"_onNetworkStatusChange",value:function(e){e.isConnected?(xi.info("".concat(this._className,"._onNetworkStatusChange previousNetworkType:").concat(this._networkType," currentNetworkType:").concat(e.networkType)),this._networkType!==e.networkType&&this._moduleManager.getModule(tl).reConnect()):xi.warn("".concat(this._className,"._onNetworkStatusChange no network, please check!")),this._networkType=e.networkType}},{key:"probe",value:function(){var e=this;return new Promise((function(t,n){if(ri)si.getNetworkType({success:function(n){e._networkType=n.networkType,n.networkType===rv?(xi.warn("".concat(e._className,".probe no network, please check!")),t([!1,n.networkType])):(xi.info("".concat(e._className,".probe networkType:").concat(n.networkType)),t([!0,n.networkType]))}});else if(window&&window.fetch)fetch("".concat(cu(),"//web.sdk.qcloud.com/im/assets/speed.xml?random=").concat(Math.random())).then((function(e){e.ok?t([!0,av]):t([!1,rv])})).catch((function(e){t([!1,rv])}));else{var o=new XMLHttpRequest,r=setTimeout((function(){xi.warn("".concat(e._className,".probe fetch timeout. Probably no network, please check!")),o.abort(),e._networkType=rv,t([!1,rv])}),e.MAX_WAIT_TIME);o.onreadystatechange=function(){4===o.readyState&&(clearTimeout(r),200===o.status||304===o.status?(this._networkType=av,t([!0,av])):(xi.warn("".concat(this.className,".probe fetch status:").concat(o.status,". Probably no network, please check!")),this._networkType=rv,t([!1,rv])))},o.open("GET","".concat(cu(),"//web.sdk.qcloud.com/im/assets/speed.xml?random=").concat(Math.random())),o.send()}}))}},{key:"getNetworkType",value:function(){return this._networkType}}]),e}(),iv=t((function(e){var t=Object.prototype.hasOwnProperty,n="~";function o(){}function r(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function a(e,t,o,a,s){if("function"!=typeof o)throw new TypeError("The listener must be a function");var i=new r(o,a||e,s),u=n?n+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],i]:e._events[u].push(i):(e._events[u]=i,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new o:delete e._events[t]}function i(){this._events=new o,this._eventsCount=0}Object.create&&(o.prototype=Object.create(null),(new o).__proto__||(n=!1)),i.prototype.eventNames=function(){var e,o,r=[];if(0===this._eventsCount)return r;for(o in e=this._events)t.call(e,o)&&r.push(n?o.slice(1):o);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(e)):r},i.prototype.listeners=function(e){var t=n?n+e:e,o=this._events[t];if(!o)return[];if(o.fn)return[o.fn];for(var r=0,a=o.length,s=new Array(a);r<a;r++)s[r]=o[r].fn;return s},i.prototype.listenerCount=function(e){var t=n?n+e:e,o=this._events[t];return o?o.fn?1:o.length:0},i.prototype.emit=function(e,t,o,r,a,s){var i=n?n+e:e;if(!this._events[i])return!1;var u,c,l=this._events[i],d=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),d){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,o),!0;case 4:return l.fn.call(l.context,t,o,r),!0;case 5:return l.fn.call(l.context,t,o,r,a),!0;case 6:return l.fn.call(l.context,t,o,r,a,s),!0}for(c=1,u=new Array(d-1);c<d;c++)u[c-1]=arguments[c];l.fn.apply(l.context,u)}else{var p,g=l.length;for(c=0;c<g;c++)switch(l[c].once&&this.removeListener(e,l[c].fn,void 0,!0),d){case 1:l[c].fn.call(l[c].context);break;case 2:l[c].fn.call(l[c].context,t);break;case 3:l[c].fn.call(l[c].context,t,o);break;case 4:l[c].fn.call(l[c].context,t,o,r);break;default:if(!u)for(p=1,u=new Array(d-1);p<d;p++)u[p-1]=arguments[p];l[c].fn.apply(l[c].context,u)}}return!0},i.prototype.on=function(e,t,n){return a(this,e,t,n,!1)},i.prototype.once=function(e,t,n){return a(this,e,t,n,!0)},i.prototype.removeListener=function(e,t,o,r){var a=n?n+e:e;if(!this._events[a])return this;if(!t)return s(this,a),this;var i=this._events[a];if(i.fn)i.fn!==t||r&&!i.once||o&&i.context!==o||s(this,a);else{for(var u=0,c=[],l=i.length;u<l;u++)(i[u].fn!==t||r&&!i[u].once||o&&i[u].context!==o)&&c.push(i[u]);c.length?this._events[a]=1===c.length?c[0]:c:s(this,a)}return this},i.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&s(this,t)):(this._events=new o,this._eventsCount=0),this},i.prototype.off=i.prototype.removeListener,i.prototype.addListener=i.prototype.on,i.prefixed=n,i.EventEmitter=i,e.exports=i})),uv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="BigDataChannelModule",o.FILETYPE={SOUND:2106,FILE:2107,VIDEO:2113},o._bdh_download_server="grouptalk.c2c.qq.com",o._BDHBizID=10001,o._authKey="",o._expireTime=0,o.getInnerEmitterInstance().on(hm.CONTEXT_A2KEY_AND_TINYID_UPDATED,o._getAuthKey,zn(o)),o}return Un(n,[{key:"_getAuthKey",value:function(){var e=this;this.request({protocolName:pl}).then((function(t){t.data.authKey&&(e._authKey=t.data.authKey,e._expireTime=parseInt(t.data.expireTime))}))}},{key:"_isFromOlderVersion",value:function(e){return!(!e.content||2===e.content.downloadFlag)}},{key:"parseElements",value:function(e,t){if(!Yi(e)||!t)return[];for(var n=[],o=null,r=0;r<e.length;r++)o=e[r],this._needParse(o)?n.push(this._parseElement(o,t)):n.push(e[r]);return n}},{key:"_needParse",value:function(e){return!e.cloudCustomData&&!(!this._isFromOlderVersion(e)||e.type!==so.MSG_AUDIO&&e.type!==so.MSG_FILE&&e.type!==so.MSG_VIDEO)}},{key:"_parseElement",value:function(e,t){switch(e.type){case so.MSG_AUDIO:return this._parseAudioElement(e,t);case so.MSG_FILE:return this._parseFileElement(e,t);case so.MSG_VIDEO:return this._parseVideoElement(e,t)}}},{key:"_parseAudioElement",value:function(e,t){return e.content.url=this._genAudioUrl(e.content.uuid,t),e}},{key:"_parseFileElement",value:function(e,t){return e.content.url=this._genFileUrl(e.content.uuid,t,e.content.fileName),e}},{key:"_parseVideoElement",value:function(e,t){return e.content.url=this._genVideoUrl(e.content.uuid,t),e}},{key:"_genAudioUrl",value:function(e,t){if(""===this._authKey)return xi.warn("".concat(this._className,"._genAudioUrl no authKey!")),"";var n=this.getModule(Yc).getSDKAppID();return"https://".concat(this._bdh_download_server,"/asn.com/stddownload_common_file?authkey=").concat(this._authKey,"&bid=").concat(this._BDHBizID,"&subbid=").concat(n,"&fileid=").concat(e,"&filetype=").concat(this.FILETYPE.SOUND,"&openid=").concat(t,"&ver=0")}},{key:"_genFileUrl",value:function(e,t,n){if(""===this._authKey)return xi.warn("".concat(this._className,"._genFileUrl no authKey!")),"";n||(n="".concat(Math.floor(1e5*Math.random()),"-").concat(Date.now()));var o=this.getModule(Yc).getSDKAppID();return"https://".concat(this._bdh_download_server,"/asn.com/stddownload_common_file?authkey=").concat(this._authKey,"&bid=").concat(this._BDHBizID,"&subbid=").concat(o,"&fileid=").concat(e,"&filetype=").concat(this.FILETYPE.FILE,"&openid=").concat(t,"&ver=0&filename=").concat(encodeURIComponent(n))}},{key:"_genVideoUrl",value:function(e,t){if(""===this._authKey)return xi.warn("".concat(this._className,"._genVideoUrl no authKey!")),"";var n=this.getModule(Yc).getSDKAppID();return"https://".concat(this._bdh_download_server,"/asn.com/stddownload_common_file?authkey=").concat(this._authKey,"&bid=").concat(this._BDHBizID,"&subbid=").concat(n,"&fileid=").concat(e,"&filetype=").concat(this.FILETYPE.VIDEO,"&openid=").concat(t,"&ver=0")}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset")),this._authKey="",this.expireTime=0}}]),n}(il),cv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="UploadModule",o.TIMUploadPlugin=null,o.timUploadPlugin=null,o.COSSDK=null,o._cosUploadMethod=null,o.expiredTimeLimit=600,o.appid=0,o.bucketName="",o.ciUrl="",o.directory="",o.downloadUrl="",o.uploadUrl="",o.region="ap-shanghai",o.cos=null,o.cosOptions={secretId:"",secretKey:"",sessionToken:"",expiredTime:0},o.uploadFileType="",o.duration=900,o.tryCount=0,o.getInnerEmitterInstance().on(hm.CONTEXT_A2KEY_AND_TINYID_UPDATED,o._init,zn(o)),o}return Un(n,[{key:"_init",value:function(){var e="".concat(this._className,"._init"),t=this.getModule(Zc);if(this.TIMUploadPlugin=t.getPlugin("tim-upload-plugin"),this.TIMUploadPlugin)this._initUploaderMethod();else{var n=ri?"cos-wx-sdk":"cos-js-sdk";this.COSSDK=t.getPlugin(n),this.COSSDK?(this._getAuthorizationKey(),xi.warn("".concat(e," v2.9.2起推荐使用 tim-upload-plugin 代替 ").concat(n,"，上传更快更安全。详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#registerPlugin"))):xi.warn("".concat(e," 没有检测到上传插件，将无法发送图片、音频、视频、文件等类型的消息。详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#registerPlugin"))}}},{key:"_getAuthorizationKey",value:function(){var e=this,t=new Kp($p),n=Math.ceil(Date.now()/1e3);this.request({protocolName:id,requestData:{duration:this.expiredTimeLimit}}).then((function(o){var r=o.data;xi.log("".concat(e._className,"._getAuthorizationKey ok. data:"),r);var a=r.expiredTime-n;t.setMessage("requestId:".concat(r.requestId," requestTime:").concat(n," expiredTime:").concat(r.expiredTime," diff:").concat(a,"s")).setNetworkType(e.getNetworkType()).end(),!ri&&r.region&&(e.region=r.region),e.appid=r.appid,e.bucketName=r.bucketName,e.ciUrl=r.ciUrl,e.directory=r.directory,e.downloadUrl=r.downloadUrl,e.uploadUrl=r.uploadUrl,e.cosOptions={secretId:r.secretId,secretKey:r.secretKey,sessionToken:r.sessionToken,expiredTime:r.expiredTime},xi.log("".concat(e._className,"._getAuthorizationKey ok. region:").concat(e.region," bucketName:").concat(e.bucketName)),e._initUploaderMethod()})).catch((function(n){e.probeNetwork().then((function(e){var o=Qn(e,2),r=o[0],a=o[1];t.setError(n,r,a).end()})),xi.warn("".concat(e._className,"._getAuthorizationKey failed. error:"),n)}))}},{key:"_getCosPreSigUrl",value:function(e){var t=this,n="".concat(this._className,"._getCosPreSigUrl"),o=Math.ceil(Date.now()/1e3),r=new Kp(eg);return this.request({protocolName:ud,requestData:{fileType:e.fileType,fileName:e.fileName,uploadMethod:e.uploadMethod,duration:e.duration}}).then((function(e){t.tryCount=0;var a=e.data||{},s=a.expiredTime-o;return xi.log("".concat(n," ok. data:"),a),r.setMessage("requestId:".concat(a.requestId," expiredTime:").concat(a.expiredTime," diff:").concat(s,"s")).setNetworkType(t.getNetworkType()).end(),a})).catch((function(o){return-1===o.code&&(o.code=vd.COS_GET_SIG_FAIL),t.probeNetwork().then((function(e){var t=Qn(e,2),n=t[0],a=t[1];r.setError(o,n,a).end()})),xi.warn("".concat(n," failed. error:"),o),t.tryCount<1?(t.tryCount++,t._getCosPreSigUrl(e)):(t.tryCount=0,om({code:vd.COS_GET_SIG_FAIL,message:Cd}))}))}},{key:"_initUploaderMethod",value:function(){var e=this;if(this.TIMUploadPlugin)return this.timUploadPlugin=new this.TIMUploadPlugin,void(this._cosUploadMethod=function(t,n){e.timUploadPlugin.uploadFile(t,n)});this.appid&&(this.cos=ri?new this.COSSDK({ForcePathStyle:!0,getAuthorization:this._getAuthorization.bind(this)}):new this.COSSDK({getAuthorization:this._getAuthorization.bind(this)}),this._cosUploadMethod=ri?function(t,n){e.cos.postObject(t,n)}:function(t,n){e.cos.uploadFiles(t,n)})}},{key:"onCheckTimer",value:function(e){this.COSSDK&&(this.TIMUploadPlugin||this.isLoggedIn()&&e%60==0&&Math.ceil(Date.now()/1e3)>=this.cosOptions.expiredTime-120&&this._getAuthorizationKey())}},{key:"_getAuthorization",value:function(e,t){t({TmpSecretId:this.cosOptions.secretId,TmpSecretKey:this.cosOptions.secretKey,XCosSecurityToken:this.cosOptions.sessionToken,ExpiredTime:this.cosOptions.expiredTime})}},{key:"upload",value:function(e){if(!0===e.getRelayFlag())return Promise.resolve();var t=this.getModule(sl);switch(e.type){case so.MSG_IMAGE:return t.addTotalCount(wp),this._uploadImage(e);case so.MSG_FILE:return t.addTotalCount(wp),this._uploadFile(e);case so.MSG_AUDIO:return t.addTotalCount(wp),this._uploadAudio(e);case so.MSG_VIDEO:return t.addTotalCount(wp),this._uploadVideo(e);default:return Promise.resolve()}}},{key:"_uploadImage",value:function(e){var t=this.getModule(qc),n=e.getElements()[0],o=t.getMessageOptionByID(e.ID);return this.doUploadImage({file:o.payload.file,to:o.to,onProgress:function(e){if(n.updatePercent(e),Ji(o.onProgress))try{o.onProgress(e)}catch(t){return om({code:vd.MESSAGE_ONPROGRESS_FUNCTION_ERROR,message:Rd})}}}).then((function(t){var o=t.location,r=t.fileType,a=t.fileSize,s=t.width,i=t.height,u=lu(o);n.updateImageFormat(r);var c=Iu({originUrl:u,originWidth:s,originHeight:i,min:198}),l=Iu({originUrl:u,originWidth:s,originHeight:i,min:720});return n.updateImageInfoArray([{size:a,url:u,width:s,height:i},xn({},l),xn({},c)]),e}))}},{key:"_uploadFile",value:function(e){var t=this.getModule(qc),n=e.getElements()[0],o=t.getMessageOptionByID(e.ID);return this.doUploadFile({file:o.payload.file,to:o.to,onProgress:function(e){if(n.updatePercent(e),Ji(o.onProgress))try{o.onProgress(e)}catch(t){return om({code:vd.MESSAGE_ONPROGRESS_FUNCTION_ERROR,message:Rd})}}}).then((function(t){var o=t.location,r=lu(o);return n.updateFileUrl(r),e}))}},{key:"_uploadAudio",value:function(e){var t=this.getModule(qc),n=e.getElements()[0],o=t.getMessageOptionByID(e.ID);return this.doUploadAudio({file:o.payload.file,to:o.to,onProgress:function(e){if(n.updatePercent(e),Ji(o.onProgress))try{o.onProgress(e)}catch(t){return om({code:vd.MESSAGE_ONPROGRESS_FUNCTION_ERROR,message:Rd})}}}).then((function(t){var o=t.location,r=lu(o);return n.updateAudioUrl(r),e}))}},{key:"_uploadVideo",value:function(e){var t=this.getModule(qc),n=e.getElements()[0],o=t.getMessageOptionByID(e.ID);return this.doUploadVideo({file:o.payload.file,to:o.to,onProgress:function(e){if(n.updatePercent(e),Ji(o.onProgress))try{o.onProgress(e)}catch(t){return om({code:vd.MESSAGE_ONPROGRESS_FUNCTION_ERROR,message:Rd})}}}).then((function(t){var o=lu(t.location);return n.updateVideoUrl(o),e}))}},{key:"doUploadImage",value:function(e){if(!e.file)return om({code:vd.MESSAGE_IMAGE_SELECT_FILE_FIRST,message:Pd});var t=this._checkImageType(e.file);if(!0!==t)return t;var n=this._checkImageSize(e.file);if(!0!==n)return n;var o=null;return this._setUploadFileType(vm),this.uploadByCOS(e).then((function(e){return o=e,t="https://".concat(e.location),ri?new Promise((function(e,n){si.getImageInfo({src:t,success:function(t){e({width:t.width,height:t.height})},fail:function(){e({width:0,height:0})}})})):_i&&9===mi?Promise.resolve({width:0,height:0}):new Promise((function(e,n){var o=new Image;o.onload=function(){e({width:this.width,height:this.height}),o=null},o.onerror=function(){e({width:0,height:0}),o=null},o.src=t}));var t})).then((function(e){return o.width=e.width,o.height=e.height,Promise.resolve(o)}))}},{key:"_checkImageType",value:function(e){var t="";return t=ri?e.url.slice(e.url.lastIndexOf(".")+1):e.files[0].name.slice(e.files[0].name.lastIndexOf(".")+1),_m.indexOf(t.toLowerCase())>=0||om({code:vd.MESSAGE_IMAGE_TYPES_LIMIT,message:Gd})}},{key:"_checkImageSize",value:function(e){var t=0;return 0===(t=ri?e.size:e.files[0].size)?om({code:vd.MESSAGE_FILE_IS_EMPTY,message:"".concat(Ld)}):t<20971520||om({code:vd.MESSAGE_IMAGE_SIZE_LIMIT,message:"".concat(Ud)})}},{key:"doUploadFile",value:function(e){var t=null;return e.file?e.file.files[0].size>104857600?om(t={code:vd.MESSAGE_FILE_SIZE_LIMIT,message:jd}):0===e.file.files[0].size?(t={code:vd.MESSAGE_FILE_IS_EMPTY,message:"".concat(Ld)},om(t)):(this._setUploadFileType(Im),this.uploadByCOS(e)):om(t={code:vd.MESSAGE_FILE_SELECT_FILE_FIRST,message:Hd})}},{key:"doUploadVideo",value:function(e){return e.file.videoFile.size>104857600?om({code:vd.MESSAGE_VIDEO_SIZE_LIMIT,message:"".concat(Vd)}):0===e.file.videoFile.size?om({code:vd.MESSAGE_FILE_IS_EMPTY,message:"".concat(Ld)}):-1===mm.indexOf(e.file.videoFile.type)?om({code:vd.MESSAGE_VIDEO_TYPES_LIMIT,message:"".concat(Kd)}):(this._setUploadFileType(Mm),ri?this.handleVideoUpload({file:e.file.videoFile,onProgress:e.onProgress}):ai?this.handleVideoUpload(e):void 0)}},{key:"handleVideoUpload",value:function(e){var t=this;return new Promise((function(n,o){t.uploadByCOS(e).then((function(e){n(e)})).catch((function(){t.uploadByCOS(e).then((function(e){n(e)})).catch((function(){o(new $_({code:vd.MESSAGE_VIDEO_UPLOAD_FAIL,message:xd}))}))}))}))}},{key:"doUploadAudio",value:function(e){return e.file?e.file.size>20971520?om(new $_({code:vd.MESSAGE_AUDIO_SIZE_LIMIT,message:"".concat(qd)})):0===e.file.size?om(new $_({code:vd.MESSAGE_FILE_IS_EMPTY,message:"".concat(Ld)})):(this._setUploadFileType(ym),this.uploadByCOS(e)):om(new $_({code:vd.MESSAGE_AUDIO_UPLOAD_FAIL,message:Fd}))}},{key:"uploadByCOS",value:function(e){var t=this,n="".concat(this._className,".uploadByCOS");if(!Ji(this._cosUploadMethod))return xi.warn("".concat(n," 没有检测到上传插件，将无法发送图片、音频、视频、文件等类型的消息。详细请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/SDK.html#registerPlugin")),om({code:vd.COS_UNDETECTED,message:kd});if(this.timUploadPlugin)return this._uploadWithPreSigUrl(e);var o=new Kp(tg),r=Date.now(),a=ri?e.file:e.file.files[0];return new Promise((function(s,i){var u=ri?t._createCosOptionsWXMiniApp(e):t._createCosOptionsWeb(e),c=t;t._cosUploadMethod(u,(function(e,u){var l=Object.create(null);if(u){if(e||Yi(u.files)&&u.files[0].error){var d=new $_({code:vd.MESSAGE_FILE_UPLOAD_FAIL,message:Bd});return o.setError(d,!0,t.getNetworkType()).end(),xi.log("".concat(n," failed. error:"),u.files[0].error),403===u.files[0].error.statusCode&&(xi.warn("".concat(n," failed. cos AccessKeyId was invalid, regain auth key!")),t._getAuthorizationKey()),void i(d)}l.fileName=a.name,l.fileSize=a.size,l.fileType=a.type.slice(a.type.indexOf("/")+1).toLowerCase(),l.location=ri?u.Location:u.files[0].data.Location;var p=Date.now()-r,g=c._formatFileSize(a.size),h=c._formatSpeed(1e3*a.size/p),f="size:".concat(g," time:").concat(p,"ms speed:").concat(h);xi.log("".concat(n," success. name:").concat(a.name," ").concat(f)),s(l);var _=t.getModule(sl);return _.addCost(wp,p),_.addFileSize(wp,a.size),void o.setNetworkType(t.getNetworkType()).setMessage(f).end()}var m=new $_({code:vd.MESSAGE_FILE_UPLOAD_FAIL,message:Bd});o.setError(m,!0,c.getNetworkType()).end(),xi.warn("".concat(n," failed. error:"),e),403===e.statusCode&&(xi.warn("".concat(n," failed. cos AccessKeyId was invalid, regain auth key!")),t._getAuthorizationKey()),i(m)}))}))}},{key:"_uploadWithPreSigUrl",value:function(e){var t=this,n="".concat(this._className,"._uploadWithPreSigUrl"),o=ri?e.file:e.file.files[0];return this._createCosOptionsPreSigUrl(e).then((function(e){return new Promise((function(r,a){var s=new Kp(tg);xi.time(Cp),t._cosUploadMethod(e,(function(e,i){var u=Object.create(null);if(e||403===i.statusCode){var c=new $_({code:vd.MESSAGE_FILE_UPLOAD_FAIL,message:Bd});return s.setError(c,!0,t.getNetworkType()).end(),xi.log("".concat(n," failed, error:"),e),void a(c)}var l=i.data.location||"";0!==l.indexOf("https://")&&0!==l.indexOf("http://")||(l=l.split("//")[1]),u.fileName=o.name,u.fileSize=o.size,u.fileType=o.type.slice(o.type.indexOf("/")+1).toLowerCase(),u.location=l;var d=xi.timeEnd(Cp),p=t._formatFileSize(o.size),g=t._formatSpeed(1e3*o.size/d),h="size:".concat(p,",time:").concat(d,"ms,speed:").concat(g);xi.log("".concat(n," success name:").concat(o.name,",").concat(h)),s.setNetworkType(t.getNetworkType()).setMessage(h).end();var f=t.getModule(sl);f.addCost(wp,d),f.addFileSize(wp,o.size),r(u)}))}))}))}},{key:"_formatFileSize",value:function(e){return e<1024?e+"B":e<1048576?Math.floor(e/1024)+"KB":Math.floor(e/1048576)+"MB"}},{key:"_formatSpeed",value:function(e){return e<=1048576?Eu(e/1024,1)+"KB/s":Eu(e/1048576,1)+"MB/s"}},{key:"_createCosOptionsWeb",value:function(e){var t=e.file.files[0].name,n=t.slice(t.lastIndexOf(".")),o=this._genFileName("".concat(ru(999999)).concat(n));return{files:[{Bucket:"".concat(this.bucketName,"-").concat(this.appid),Region:this.region,Key:"".concat(this.directory,"/").concat(o),Body:e.file.files[0]}],SliceSize:1048576,onProgress:function(t){if("function"==typeof e.onProgress)try{e.onProgress(t.percent)}catch(n){xi.warn("onProgress callback error:",n)}},onFileFinish:function(e,t,n){}}}},{key:"_createCosOptionsWXMiniApp",value:function(e){var t=this._genFileName(e.file.name),n=e.file.url;return{Bucket:"".concat(this.bucketName,"-").concat(this.appid),Region:this.region,Key:"".concat(this.directory,"/").concat(t),FilePath:n,onProgress:function(t){if(xi.log(JSON.stringify(t)),"function"==typeof e.onProgress)try{e.onProgress(t.percent)}catch(n){xi.warn("onProgress callback error:",n)}}}}},{key:"_createCosOptionsPreSigUrl",value:function(e){var t=this,n="",o="",r=0;if(ri)n=this._genFileName(e.file.name),o=e.file.url,r=1;else{var a=e.file.files[0].name,s=a.slice(a.lastIndexOf("."));n=this._genFileName("".concat(ru(999999)).concat(s)),o=e.file.files[0],r=0}return this._getCosPreSigUrl({fileType:this.uploadFileType,fileName:n,uploadMethod:r,duration:this.duration}).then((function(r){var a=r.uploadUrl,s=r.downloadUrl;return{url:a,fileType:t.uploadFileType,fileName:n,resources:o,downloadUrl:s,onProgress:function(t){if("function"==typeof e.onProgress)try{e.onProgress(t.percent)}catch(n){xi.warn("onProgress callback error:",n),xi.error(n)}}}}))}},{key:"_genFileName",value:function(e){return"".concat(Mu(),"-").concat(e)}},{key:"_setUploadFileType",value:function(e){this.uploadFileType=e}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset"))}}]),n}(il),lv=function(){function e(t){Pn(this,e),this._className="MergerMessageHandler",this._messageModule=t}return Un(e,[{key:"uploadMergerMessage",value:function(e,t){var n=this;xi.debug("".concat(this._className,".uploadMergerMessage message:"),e,"messageBytes:".concat(t));var o=e.payload.messageList,r=o.length,a=new Kp(dg);return this._messageModule.request({protocolName:hd,requestData:{messageList:o}}).then((function(e){xi.debug("".concat(n._className,".uploadMergerMessage ok. response:"),e.data);var o=e.data,s=o.pbDownloadKey,i=o.downloadKey,u={pbDownloadKey:s,downloadKey:i,messageNumber:r};return a.setNetworkType(n._messageModule.getNetworkType()).setMessage("".concat(r,"-").concat(t,"-").concat(i)).end(),u})).catch((function(e){throw xi.warn("".concat(n._className,".uploadMergerMessage failed. error:"),e),n._messageModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];a.setError(e,o,r).end()})),e}))}},{key:"downloadMergerMessage",value:function(e){var t=this;xi.debug("".concat(this._className,".downloadMergerMessage message:"),e);var n=e.payload.downloadKey,o=new Kp(pg);return o.setMessage("downloadKey:".concat(n)),this._messageModule.request({protocolName:fd,requestData:{downloadKey:n}}).then((function(n){if(xi.debug("".concat(t._className,".downloadMergerMessage ok. response:"),n.data),Ji(e.clearElement)){var r=e.payload,a=(r.downloadKey,r.pbDownloadKey,r.messageList,Wn(r,["downloadKey","pbDownloadKey","messageList"]));e.clearElement(),e.setElement({type:e.type,content:xn({messageList:n.data.messageList},a)})}else{var s=[];n.data.messageList.forEach((function(e){if(!Au(e)){var t=new B_(e);s.push(t)}})),e.payload.messageList=s,e.payload.downloadKey="",e.payload.pbDownloadKey=""}return o.setNetworkType(t._messageModule.getNetworkType()).end(),e})).catch((function(e){throw xi.warn("".concat(t._className,".downloadMergerMessage failed. key:").concat(n," error:"),e),t._messageModule.probeNetwork().then((function(t){var n=Qn(t,2),r=n[0],a=n[1];o.setError(e,r,a).end()})),e}))}},{key:"createMergerMessagePack",value:function(e,t,n){return e.conversationType===so.CONV_C2C?this._createC2CMergerMessagePack(e,t,n):this._createGroupMergerMessagePack(e,t,n)}},{key:"_createC2CMergerMessagePack",value:function(e,t,n){var o=null;t&&(t.offlinePushInfo&&(o=t.offlinePushInfo),!0===t.onlineUserOnly&&(o?o.disablePush=!0:o={disablePush:!0}));var r="";Bi(e.cloudCustomData)&&e.cloudCustomData.length>0&&(r=e.cloudCustomData);var a=n.pbDownloadKey,s=n.downloadKey,i=n.messageNumber,u=e.payload,c=u.title,l=u.abstractList,d=u.compatibleText,p=this._messageModule.getModule(Vc);return{protocolName:gl,tjgID:this._messageModule.generateTjgID(e),requestData:{fromAccount:this._messageModule.getMyUserID(),toAccount:e.to,msgBody:[{msgType:e.type,msgContent:{pbDownloadKey:a,downloadKey:s,title:c,abstractList:l,compatibleText:d,messageNumber:i}}],cloudCustomData:r,msgSeq:e.sequence,msgRandom:e.random,msgLifeTime:p&&p.isOnlineMessage(e,t)?0:void 0,offlinePushInfo:o?{pushFlag:!0===o.disablePush?1:0,title:o.title||"",desc:o.description||"",ext:o.extension||"",apnsInfo:{badgeMode:!0===o.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:o.androidOPPOChannelID||""}}:void 0}}}},{key:"_createGroupMergerMessagePack",value:function(e,t,n){var o=null;t&&t.offlinePushInfo&&(o=t.offlinePushInfo);var r="";Bi(e.cloudCustomData)&&e.cloudCustomData.length>0&&(r=e.cloudCustomData);var a=n.pbDownloadKey,s=n.downloadKey,i=n.messageNumber,u=e.payload,c=u.title,l=u.abstractList,d=u.compatibleText,p=this._messageModule.getModule(Kc);return{protocolName:hl,tjgID:this._messageModule.generateTjgID(e),requestData:{fromAccount:this._messageModule.getMyUserID(),groupID:e.to,msgBody:[{msgType:e.type,msgContent:{pbDownloadKey:a,downloadKey:s,title:c,abstractList:l,compatibleText:d,messageNumber:i}}],random:e.random,priority:e.priority,clientSequence:e.clientSequence,groupAtInfo:void 0,cloudCustomData:r,onlineOnlyFlag:p&&p.isOnlineMessage(e,t)?1:0,offlinePushInfo:o?{pushFlag:!0===o.disablePush?1:0,title:o.title||"",desc:o.description||"",ext:o.extension||"",apnsInfo:{badgeMode:!0===o.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:o.androidOPPOChannelID||""}}:void 0}}}}]),e}(),dv={ERR_SVR_COMM_SENSITIVE_TEXT:80001,ERR_SVR_COMM_BODY_SIZE_LIMIT:80002,ERR_SVR_MSG_PKG_PARSE_FAILED:20001,ERR_SVR_MSG_INTERNAL_AUTH_FAILED:20002,ERR_SVR_MSG_INVALID_ID:20003,ERR_SVR_MSG_PUSH_DENY:20006,ERR_SVR_MSG_IN_PEER_BLACKLIST:20007,ERR_SVR_MSG_BOTH_NOT_FRIEND:20009,ERR_SVR_MSG_NOT_PEER_FRIEND:20010,ERR_SVR_MSG_NOT_SELF_FRIEND:20011,ERR_SVR_MSG_SHUTUP_DENY:20012,ERR_SVR_GROUP_INVALID_PARAMETERS:10004,ERR_SVR_GROUP_PERMISSION_DENY:10007,ERR_SVR_GROUP_NOT_FOUND:10010,ERR_SVR_GROUP_INVALID_GROUPID:10015,ERR_SVR_GROUP_REJECT_FROM_THIRDPARTY:10016,ERR_SVR_GROUP_SHUTUP_DENY:10017,MESSAGE_SEND_FAIL:2100},pv=[vd.MESSAGE_ONPROGRESS_FUNCTION_ERROR,vd.MESSAGE_IMAGE_SELECT_FILE_FIRST,vd.MESSAGE_IMAGE_TYPES_LIMIT,vd.MESSAGE_FILE_IS_EMPTY,vd.MESSAGE_IMAGE_SIZE_LIMIT,vd.MESSAGE_FILE_SELECT_FILE_FIRST,vd.MESSAGE_FILE_SIZE_LIMIT,vd.MESSAGE_VIDEO_SIZE_LIMIT,vd.MESSAGE_VIDEO_TYPES_LIMIT,vd.MESSAGE_AUDIO_UPLOAD_FAIL,vd.MESSAGE_AUDIO_SIZE_LIMIT,vd.COS_UNDETECTED],gv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="MessageModule",o._messageOptionsMap=new Map,o._mergerMessageHandler=new lv(zn(o)),o}return Un(n,[{key:"createTextMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new Y_(e),o="string"==typeof e.payload?e.payload:e.payload.text,r=new gh({text:o}),a=this._getNickAndAvatarByUserID(t);return n.setElement(r),n.setNickAndAvatar(a),n.setNameCard(this._getNameCardByGroupID(n)),n}},{key:"createImageMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new Y_(e);if(ri){var o=e.payload.file;if(Vi(o))return void xi.warn("小程序环境下调用 createImageMessage 接口时，payload.file 不支持传入 File 对象");var r=o.tempFilePaths[0],a={url:r,name:r.slice(r.lastIndexOf("/")+1),size:o.tempFiles&&o.tempFiles[0].size||1,type:r.slice(r.lastIndexOf(".")+1).toLowerCase()};e.payload.file=a}else if(ai)if(Vi(e.payload.file)){var s=e.payload.file;e.payload.file={files:[s]}}else if(ji(e.payload.file)&&"undefined"!=typeof uni){var i=e.payload.file.tempFiles[0];e.payload.file={files:[i]}}var u=new k_({imageFormat:M_.IMAGE_FORMAT.UNKNOWN,uuid:this._generateUUID(),file:e.payload.file}),c=this._getNickAndAvatarByUserID(t);return n.setElement(u),n.setNickAndAvatar(c),n.setNameCard(this._getNameCardByGroupID(n)),this._messageOptionsMap.set(n.ID,e),n}},{key:"createAudioMessage",value:function(e){if(ri){var t=e.payload.file;if(ri){var n={url:t.tempFilePath,name:t.tempFilePath.slice(t.tempFilePath.lastIndexOf("/")+1),size:t.fileSize,second:parseInt(t.duration)/1e3,type:t.tempFilePath.slice(t.tempFilePath.lastIndexOf(".")+1).toLowerCase()};e.payload.file=n}var o=this.getMyUserID();e.currentUser=o;var r=new Y_(e),a=new A_({second:Math.floor(t.duration/1e3),size:t.fileSize,url:t.tempFilePath,uuid:this._generateUUID()}),s=this._getNickAndAvatarByUserID(o);return r.setElement(a),r.setNickAndAvatar(s),r.setNameCard(this._getNameCardByGroupID(r)),this._messageOptionsMap.set(r.ID,e),r}xi.warn("createAudioMessage 目前只支持小程序环境下发语音消息")}},{key:"createVideoMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t,e.payload.file.thumbUrl="https://web.sdk.qcloud.com/im/assets/images/transparent.png",e.payload.file.thumbSize=1668;var n={};if(ri){if(ni)return void xi.warn("createVideoMessage 不支持在支付宝小程序环境下使用");if(Vi(e.payload.file))return void xi.warn("小程序环境下调用 createVideoMessage 接口时，payload.file 不支持传入 File 对象");var o=e.payload.file;n.url=o.tempFilePath,n.name=o.tempFilePath.slice(o.tempFilePath.lastIndexOf("/")+1),n.size=o.size,n.second=o.duration,n.type=o.tempFilePath.slice(o.tempFilePath.lastIndexOf(".")+1).toLowerCase()}else if(ai){if(Vi(e.payload.file)){var r=e.payload.file;e.payload.file.files=[r]}else if(ji(e.payload.file)&&"undefined"!=typeof uni){var a=e.payload.file.tempFile;e.payload.file.files=[a]}var s=e.payload.file;n.url=window.URL.createObjectURL(s.files[0]),n.name=s.files[0].name,n.size=s.files[0].size,n.second=s.files[0].duration||0,n.type=s.files[0].type.split("/")[1]}e.payload.file.videoFile=n;var i=new Y_(e),u=new V_({videoFormat:n.type,videoSecond:Eu(n.second,0),videoSize:n.size,remoteVideoUrl:"",videoUrl:n.url,videoUUID:this._generateUUID(),thumbUUID:this._generateUUID(),thumbWidth:e.payload.file.width||200,thumbHeight:e.payload.file.height||200,thumbUrl:e.payload.file.thumbUrl,thumbSize:e.payload.file.thumbSize,thumbFormat:e.payload.file.thumbUrl.slice(e.payload.file.thumbUrl.lastIndexOf(".")+1).toLowerCase()}),c=this._getNickAndAvatarByUserID(t);return i.setElement(u),i.setNickAndAvatar(c),i.setNameCard(this._getNameCardByGroupID(i)),this._messageOptionsMap.set(i.ID,e),i}},{key:"createCustomMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new Y_(e),o=new x_({data:e.payload.data,description:e.payload.description,extension:e.payload.extension}),r=this._getNickAndAvatarByUserID(t);return n.setElement(o),n.setNickAndAvatar(r),n.setNameCard(this._getNameCardByGroupID(n)),n}},{key:"createFaceMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new Y_(e),o=new C_(e.payload),r=this._getNickAndAvatarByUserID(t);return n.setElement(o),n.setNickAndAvatar(r),n.setNameCard(this._getNameCardByGroupID(n)),n}},{key:"createMergerMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=this._getNickAndAvatarByUserID(t),o=new Y_(e),r=new H_(e.payload);return o.setElement(r),o.setNickAndAvatar(n),o.setNameCard(this._getNameCardByGroupID(o)),o.setRelayFlag(!0),o}},{key:"createForwardMessage",value:function(e){var t=e.to,n=e.conversationType,o=e.priority,r=e.payload,a=this.getMyUserID(),s=this._getNickAndAvatarByUserID(a);if(r.type===so.MSG_GRP_TIP)return om(new $_({code:vd.MESSAGE_FORWARD_TYPE_INVALID,message:Xd}));var i={to:t,conversationType:n,conversationID:"".concat(n).concat(t),priority:o,isPlaceMessage:0,status:Tc.UNSEND,currentUser:a,cloudCustomData:e.cloudCustomData||r.cloudCustomData||""},u=new Y_(i);return u.setElement(r.getElements()[0]),u.setNickAndAvatar(s),u.setNameCard(this._getNameCardByGroupID(r)),u.setRelayFlag(!0),u}},{key:"downloadMergerMessage",value:function(e){return this._mergerMessageHandler.downloadMergerMessage(e)}},{key:"createFileMessage",value:function(e){if(!ri){if(ai)if(Vi(e.payload.file)){var t=e.payload.file;e.payload.file={files:[t]}}else if(ji(e.payload.file)&&"undefined"!=typeof uni){var n=e.payload.file.tempFiles[0];e.payload.file={files:[n]}}var o=this.getMyUserID();e.currentUser=o;var r=new Y_(e),a=new q_({uuid:this._generateUUID(),file:e.payload.file}),s=this._getNickAndAvatarByUserID(o);return r.setElement(a),r.setNickAndAvatar(s),r.setNameCard(this._getNameCardByGroupID(r)),this._messageOptionsMap.set(r.ID,e),r}xi.warn("小程序目前不支持选择文件， createFileMessage 接口不可用！")}},{key:"createLocationMessage",value:function(e){var t=this.getMyUserID();e.currentUser=t;var n=new Y_(e),o=new K_(e.payload),r=this._getNickAndAvatarByUserID(t);return n.setElement(o),n.setNickAndAvatar(r),n.setNameCard(this._getNameCardByGroupID(n)),this._messageOptionsMap.set(n.ID,e),n}},{key:"_onCannotFindModule",value:function(){return om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"sendMessageInstance",value:function(e,t){var n,o=this,r=null;switch(e.conversationType){case so.CONV_C2C:if(!(r=this.getModule(Vc)))return this._onCannotFindModule();break;case so.CONV_GROUP:if(!(r=this.getModule(Kc)))return this._onCannotFindModule();break;default:return om({code:vd.MESSAGE_SEND_INVALID_CONVERSATION_TYPE,message:Od})}var a=this.getModule(Qc),s=this.getModule(Kc);return a.upload(e).then((function(){return o._getSendMessageSpecifiedKey(e)===bp&&o.getModule(sl).addSuccessCount(wp),s.guardForAVChatRoom(e).then((function(){if(!e.isSendable())return om({code:vd.MESSAGE_FILE_URL_IS_EMPTY,message:Yd});o._addSendMessageTotalCount(e),n=Date.now();var a=function(e){var t="utf-8";ai&&document&&(t=document.charset.toLowerCase());var n,o,r=0;if(o=e.length,"utf-8"===t||"utf8"===t)for(var a=0;a<o;a++)(n=e.codePointAt(a))<=127?r+=1:n<=2047?r+=2:n<=65535?r+=3:(r+=4,a++);else if("utf-16"===t||"utf16"===t)for(var s=0;s<o;s++)(n=e.codePointAt(s))<=65535?r+=2:(r+=4,s++);else r=e.replace(/[^\x00-\xff]/g,"aa").length;return r}(JSON.stringify(e));return e.type===so.MSG_MERGER&&a>7e3?o._mergerMessageHandler.uploadMergerMessage(e,a).then((function(n){var r=o._mergerMessageHandler.createMergerMessagePack(e,t,n);return o.request(r)})):(o.getModule(jc).setMessageRandom(e),e.conversationType===so.CONV_C2C||e.conversationType===so.CONV_GROUP?r.sendMessage(e,t):void 0)})).then((function(a){var s=a.data,i=s.time,u=s.sequence;o._addSendMessageSuccessCount(e,n),o._messageOptionsMap.delete(e.ID);var c=o.getModule(jc);e.status=Tc.SUCCESS,e.time=i;var l=!1;if(e.conversationType===so.CONV_GROUP)e.sequence=u,e.generateMessageID(o.getMyUserID());else if(e.conversationType===so.CONV_C2C){var d=c.getLatestMessageSentByMe(e.conversationID);if(d){var p=d.nick,g=d.avatar;p===e.nick&&g===e.avatar||(l=!0)}}return c.appendToMessageList(e),l&&c.modifyMessageSentByMe({conversationID:e.conversationID,latestNick:e.nick,latestAvatar:e.avatar}),r.isOnlineMessage(e,t)?e.setOnlineOnlyFlag(!0):c.onMessageSent({conversationOptionsList:[{conversationID:e.conversationID,unreadCount:0,type:e.conversationType,subType:e.conversationSubType,lastMessage:e}]}),e.getRelayFlag()||"TIMImageElem"!==e.type||Tu(e.payload.imageInfoArray),W_({message:e})}))})).catch((function(t){return o._onSendMessageFailed(e,t)}))}},{key:"_onSendMessageFailed",value:function(e,t){e.status=Tc.FAIL,this.getModule(jc).deleteMessageRandom(e),this._addSendMessageFailCountOnUser(e,t);var n=new Kp(ng);return n.setMessage("tjg_id:".concat(this.generateTjgID(e)," type:").concat(e.type," from:").concat(e.from," to:").concat(e.to)),this.probeNetwork().then((function(e){var o=Qn(e,2),r=o[0],a=o[1];n.setError(t,r,a).end()})),xi.error("".concat(this._className,"._onSendMessageFailed error:"),t),om(new $_({code:t&&t.code?t.code:vd.MESSAGE_SEND_FAIL,message:t&&t.message?t.message:Ad,data:{message:e}}))}},{key:"_getSendMessageSpecifiedKey",value:function(e){if([so.MSG_IMAGE,so.MSG_AUDIO,so.MSG_VIDEO,so.MSG_FILE].includes(e.type))return bp;if(e.conversationType===so.CONV_C2C)return Op;if(e.conversationType===so.CONV_GROUP){var t=this.getModule(Kc).getLocalGroupProfile(e.to);if(!t)return;var n=t.type;return hu(n)?Rp:Lp}}},{key:"_addSendMessageTotalCount",value:function(e){var t=this._getSendMessageSpecifiedKey(e);t&&this.getModule(sl).addTotalCount(t)}},{key:"_addSendMessageSuccessCount",value:function(e,t){var n=Math.abs(Date.now()-t),o=this._getSendMessageSpecifiedKey(e);if(o){var r=this.getModule(sl);r.addSuccessCount(o),r.addCost(o,n)}}},{key:"_addSendMessageFailCountOnUser",value:function(e,t){var n,o,r=t.code,a=void 0===r?-1:r,s=this.getModule(sl),i=this._getSendMessageSpecifiedKey(e);i===bp&&(n=a,o=!1,pv.includes(n)&&(o=!0),o)?s.addFailedCountOfUserSide(wp):function(e){var t=!1;return Object.values(dv).includes(e)&&(t=!0),(e>=120001&&e<=13e4||e>=10100&&e<=10200)&&(t=!0),t}(a)&&i&&s.addFailedCountOfUserSide(i)}},{key:"resendMessage",value:function(e){return e.isResend=!0,e.status=Tc.UNSEND,this.sendMessageInstance(e)}},{key:"revokeMessage",value:function(e){var t=this,n=null;if(e.conversationType===so.CONV_C2C){if(!(n=this.getModule(Vc)))return this._onCannotFindModule()}else if(e.conversationType===so.CONV_GROUP&&!(n=this.getModule(Kc)))return this._onCannotFindModule();var o=new Kp(ag);return o.setMessage("tjg_id:".concat(this.generateTjgID(e)," type:").concat(e.type," from:").concat(e.from," to:").concat(e.to)),n.revokeMessage(e).then((function(n){var r=n.data.recallRetList;if(!Au(r)&&0!==r[0].retCode){var a=new $_({code:r[0].retCode,message:Z_[r[0].retCode]||bd,data:{message:e}});return o.setCode(a.code).setMoreMessage(a.message).end(),om(a)}return xi.info("".concat(t._className,".revokeMessage ok. ID:").concat(e.ID)),e.isRevoked=!0,o.end(),t.getModule(jc).onMessageRevoked([e]),W_({message:e})})).catch((function(n){t.probeNetwork().then((function(e){var t=Qn(e,2),r=t[0],a=t[1];o.setError(n,r,a).end()}));var r=new $_({code:n&&n.code?n.code:vd.MESSAGE_REVOKE_FAIL,message:n&&n.message?n.message:bd,data:{message:e}});return xi.warn("".concat(t._className,".revokeMessage failed. error:"),n),om(r)}))}},{key:"deleteMessage",value:function(e){var t=this,n=null,o=e[0],r=o.conversationID,a="",s=[],i=[];if(o.conversationType===so.CONV_C2C?(n=this.getModule(Vc),a=r.replace(so.CONV_C2C,""),e.forEach((function(e){e&&e.status===Tc.SUCCESS&&e.conversationID===r&&(e.getOnlineOnlyFlag()||s.push("".concat(e.sequence,"_").concat(e.random,"_").concat(e.time)),i.push(e))}))):o.conversationType===so.CONV_GROUP&&(n=this.getModule(Kc),a=r.replace(so.CONV_GROUP,""),e.forEach((function(e){e&&e.status===Tc.SUCCESS&&e.conversationID===r&&(e.getOnlineOnlyFlag()||s.push("".concat(e.sequence)),i.push(e))}))),!n)return this._onCannotFindModule();if(0===s.length)return this._onMessageDeleted(i);s.length>30&&(s=s.slice(0,30),i=i.slice(0,30));var u=new Kp(sg);return u.setMessage("to:".concat(a," count:").concat(s.length)),n.deleteMessage({to:a,keyList:s}).then((function(e){return u.end(),xi.info("".concat(t._className,".deleteMessage ok")),t._onMessageDeleted(i)})).catch((function(e){t.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];u.setError(e,o,r).end()})),xi.warn("".concat(t._className,".deleteMessage failed. error:"),e);var n=new $_({code:e&&e.code?e.code:vd.MESSAGE_DELETE_FAIL,message:e&&e.message?e.message:wd});return om(n)}))}},{key:"_onMessageDeleted",value:function(e){return this.getModule(jc).onMessageDeleted(e),nm({messageList:e})}},{key:"_generateUUID",value:function(){var e=this.getModule(Yc);return"".concat(e.getSDKAppID(),"-").concat(e.getUserID(),"-").concat(function(){for(var e="",t=32;t>0;--t)e+=au[Math.floor(Math.random()*su)];return e}())}},{key:"getMessageOptionByID",value:function(e){return this._messageOptionsMap.get(e)}},{key:"_getNickAndAvatarByUserID",value:function(e){return this.getModule(xc).getNickAndAvatarByUserID(e)}},{key:"_getNameCardByGroupID",value:function(e){if(e.conversationType===so.CONV_GROUP){var t=this.getModule(Kc);if(t)return t.getMyNameCardByGroupID(e.to)}return""}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset")),this._messageOptionsMap.clear()}}]),n}(il),hv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="PluginModule",o.plugins={},o}return Un(n,[{key:"registerPlugin",value:function(e){var t=this;Object.keys(e).forEach((function(n){t.plugins[n]=e[n]})),new Kp(zp).setMessage("key=".concat(Object.keys(e))).end()}},{key:"getPlugin",value:function(e){return this.plugins[e]}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset"))}}]),n}(il),fv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="SyncUnreadMessageModule",o._cookie="",o._onlineSyncFlag=!1,o.getInnerEmitterInstance().on(hm.CONTEXT_A2KEY_AND_TINYID_UPDATED,o._onLoginSuccess,zn(o)),o}return Un(n,[{key:"_onLoginSuccess",value:function(e){this._startSync({cookie:this._cookie,syncFlag:0,isOnlineSync:0})}},{key:"_startSync",value:function(e){var t=this,n=e.cookie,o=e.syncFlag,r=e.isOnlineSync;xi.log("".concat(this._className,"._startSync cookie:").concat(n," syncFlag:").concat(o," isOnlineSync:").concat(r)),this.request({protocolName:dl,requestData:{cookie:n,syncFlag:o,isOnlineSync:r}}).then((function(e){var n=e.data,o=n.cookie,r=n.syncFlag,a=n.eventArray,s=n.messageList,i=n.C2CRemainingUnreadList;t._cookie=o,Au(o)||(0===r||1===r?(a&&t.getModule(el).onMessage({head:{},body:{eventArray:a,isInstantMessage:t._onlineSyncFlag,isSyncingEnded:!1}}),t.getModule(Vc).onNewC2CMessage({dataList:s,isInstantMessage:!1,C2CRemainingUnreadList:i}),t._startSync({cookie:o,syncFlag:r,isOnlineSync:0})):2===r&&(a&&t.getModule(el).onMessage({head:{},body:{eventArray:a,isInstantMessage:t._onlineSyncFlag,isSyncingEnded:!0}}),t.getModule(Vc).onNewC2CMessage({dataList:s,isInstantMessage:t._onlineSyncFlag,C2CRemainingUnreadList:i})))})).catch((function(e){xi.error("".concat(t._className,"._startSync failed. error:"),e)}))}},{key:"startOnlineSync",value:function(){xi.log("".concat(this._className,".startOnlineSync")),this._onlineSyncFlag=!0,this._startSync({cookie:this._cookie,syncFlag:0,isOnlineSync:1})}},{key:"startSyncOnReconnected",value:function(){xi.log("".concat(this._className,".startSyncOnReconnected.")),this._onlineSyncFlag=!0,this._startSync({cookie:this._cookie,syncFlag:0,isOnlineSync:0})}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset")),this._onlineSyncFlag=!1,this._cookie=""}}]),n}(il),_v={request:{toAccount:"To_Account",fromAccount:"From_Account",to:"To_Account",from:"From_Account",groupID:"GroupId",groupAtUserID:"GroupAt_Account",extension:"Ext",data:"Data",description:"Desc",elements:"MsgBody",sizeType:"Type",downloadFlag:"Download_Flag",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",videoUrl:"",imageUrl:"URL",fileUrl:"Url",uuid:"UUID",priority:"MsgPriority",receiverUserID:"To_Account",receiverGroupID:"GroupId",messageSender:"SenderId",messageReceiver:"ReceiverId",nick:"From_AccountNick",avatar:"From_AccountHeadurl",messageNumber:"MsgNum",pbDownloadKey:"PbMsgKey",downloadKey:"JsonMsgKey",applicationType:"PendencyType",userIDList:"To_Account",groupNameList:"GroupName",userID:"To_Account",groupAttributeList:"GroupAttr",mainSequence:"AttrMainSeq",avChatRoomKey:"BytesKey",attributeControl:"AttrControl",sequence:"seq"},response:{MsgPriority:"priority",ThumbUUID:"thumbUUID",VideoUUID:"videoUUID",Download_Flag:"downloadFlag",GroupId:"groupID",Member_Account:"userID",MsgList:"messageList",SyncFlag:"syncFlag",To_Account:"to",From_Account:"from",MsgSeq:"sequence",MsgRandom:"random",MsgTime:"time",MsgTimeStamp:"time",MsgContent:"content",MsgBody:"elements",From_AccountNick:"nick",From_AccountHeadurl:"avatar",GroupWithdrawInfoArray:"revokedInfos",GroupReadInfoArray:"groupMessageReadNotice",LastReadMsgSeq:"lastMessageSeq",WithdrawC2cMsgNotify:"c2cMessageRevokedNotify",C2cWithdrawInfoArray:"revokedInfos",C2cReadedReceipt:"c2cMessageReadReceipt",ReadC2cMsgNotify:"c2cMessageReadNotice",LastReadTime:"peerReadTime",MsgRand:"random",MsgType:"type",MsgShow:"messageShow",NextMsgSeq:"nextMessageSeq",FaceUrl:"avatar",ProfileDataMod:"profileModify",Profile_Account:"userID",ValueBytes:"value",ValueNum:"value",NoticeSeq:"noticeSequence",NotifySeq:"notifySequence",MsgFrom_AccountExtraInfo:"messageFromAccountExtraInformation",Operator_Account:"operatorID",OpType:"operationType",ReportType:"operationType",UserId:"userID",User_Account:"userID",List_Account:"userIDList",MsgOperatorMemberExtraInfo:"operatorInfo",MsgMemberExtraInfo:"memberInfoList",ImageUrl:"avatar",NickName:"nick",MsgGroupNewInfo:"newGroupProfile",MsgAppDefinedData:"groupCustomField",Owner_Account:"ownerID",GroupFaceUrl:"avatar",GroupIntroduction:"introduction",GroupNotification:"notification",GroupApplyJoinOption:"joinOption",MsgKey:"messageKey",GroupInfo:"groupProfile",ShutupTime:"muteTime",Desc:"description",Ext:"extension",GroupAt_Account:"groupAtUserID",MsgNum:"messageNumber",PbMsgKey:"pbDownloadKey",JsonMsgKey:"downloadKey",MsgModifiedFlag:"isModified",PendencyItem:"applicationItem",PendencyType:"applicationType",AddTime:"time",AddSource:"source",AddWording:"wording",ProfileImImage:"avatar",PendencyAdd:"friendApplicationAdded",FrienPencydDel_Account:"friendApplicationDeletedUserIDList",Peer_Account:"userID",GroupAttr:"groupAttributeList",GroupAttrAry:"groupAttributeList",AttrMainSeq:"mainSequence",seq:"sequence",GroupAttrOption:"groupAttributeOption",BytesChangedKeys:"changedKeyList",GroupAttrInfo:"groupAttributeList",GroupAttrSeq:"mainSequence",PushChangedAttrValFlag:"hasChangedAttributeInfo",SubKeySeq:"sequence",Val:"value",MsgGroupFromCardName:"senderNameCard",MsgGroupFromNickName:"senderNick",C2cNick:"peerNick",C2cImage:"peerAvatar"},ignoreKeyWord:["C2C","ID","USP"]},mv=Qt.trim;function vv(e,t){if("string"!=typeof e&&!Array.isArray(e))throw new TypeError("Expected the input to be `string | string[]`");var n;return t=Object.assign({pascalCase:!1},t),0===(e=Array.isArray(e)?e.map((function(e){return e.trim()})).filter((function(e){return e.length})).join("-"):e.trim()).length?"":1===e.length?t.pascalCase?e.toUpperCase():e.toLowerCase():(e!==e.toLowerCase()&&(e=Mv(e)),e=e.replace(/^[_.\- ]+/,"").toLowerCase().replace(/[_.\- ]+(\w|$)/g,(function(e,t){return t.toUpperCase()})).replace(/\d+(\w|$)/g,(function(e){return e.toUpperCase()})),n=e,t.pascalCase?n.charAt(0).toUpperCase()+n.slice(1):n)}be({target:"String",proto:!0,forced:function(e){return a((function(){return!!Yt[e]()||"​᠎"!="​᠎"[e]()||Yt[e].name!==e}))}("trim")},{trim:function(){return mv(this)}});var Mv=function(e){for(var t=!1,n=!1,o=!1,r=0;r<e.length;r++){var a=e[r];t&&/[a-zA-Z]/.test(a)&&a.toUpperCase()===a?(e=e.slice(0,r)+"-"+e.slice(r),t=!1,o=n,n=!0,r++):n&&o&&/[a-zA-Z]/.test(a)&&a.toLowerCase()===a?(e=e.slice(0,r-1)+"-"+e.slice(r-1),o=n,n=!1,t=!0):(t=a.toLowerCase()===a&&a.toUpperCase()!==a,o=n,n=a.toUpperCase()===a&&a.toLowerCase()!==a)}return e};function yv(e,t){var n=0;return function e(t,o){if(++n>100)return n--,t;if(Yi(t)){var r=t.map((function(t){return Hi(t)?e(t,o):t}));return n--,r}if(Hi(t)){var a=(s=t,i=function(e,t){if(!Zi(t))return!1;if((r=t)!==vv(r))for(var n=0;n<_v.ignoreKeyWord.length&&!t.includes(_v.ignoreKeyWord[n]);n++);var r;return Wi(o[t])?function(e){return"OPPOChannelID"===e?e:e[0].toUpperCase()+vv(e).slice(1)}(t):o[t]},u=Object.create(null),Object.keys(s).forEach((function(e){var t=i(s[e],e);t&&(u[t]=s[e])})),u);return a=vu(a,(function(t,n){return Yi(t)||Hi(t)?e(t,o):t})),n--,a}var s,i,u}(e,t)}function Iv(e,t){if(Yi(e))return e.map((function(e){return Hi(e)?Iv(e,t):e}));if(Hi(e)){var n=(o=e,r=function(e,n){return Wi(t[n])?vv(n):t[n]},a={},Object.keys(o).forEach((function(e){a[r(o[e],e)]=o[e]})),a);return vu(n,(function(e){return Yi(e)||Hi(e)?Iv(e,t):e}))}var o,r,a}var Tv=function(){function e(t){Pn(this,e),this._handler=t;var n=t.getURL();this._socket=null,this._id=ru(),ri?ni?(si.connectSocket({url:n,header:{"content-type":"application/json"}}),si.onSocketClose(this._onClose.bind(this)),si.onSocketOpen(this._onOpen.bind(this)),si.onSocketMessage(this._onMessage.bind(this)),si.onSocketError(this._onError.bind(this))):(this._socket=si.connectSocket({url:n,header:{"content-type":"application/json"},complete:function(){}}),this._socket.onClose(this._onClose.bind(this)),this._socket.onOpen(this._onOpen.bind(this)),this._socket.onMessage(this._onMessage.bind(this)),this._socket.onError(this._onError.bind(this))):ai&&(this._socket=new WebSocket(n),this._socket.onopen=this._onOpen.bind(this),this._socket.onmessage=this._onMessage.bind(this),this._socket.onclose=this._onClose.bind(this),this._socket.onerror=this._onError.bind(this))}return Un(e,[{key:"getID",value:function(){return this._id}},{key:"_onOpen",value:function(){this._handler.onOpen({id:this._id})}},{key:"_onClose",value:function(e){this._handler.onClose({id:this._id,e:e})}},{key:"_onMessage",value:function(e){this._handler.onMessage(e)}},{key:"_onError",value:function(e){this._handler.onError({id:this._id,e:e})}},{key:"close",value:function(e){if(ni)return si.offSocketClose(),si.offSocketMessage(),si.offSocketOpen(),si.offSocketError(),void si.closeSocket();this._socket&&(ri?(this._socket.onClose((function(){})),this._socket.onOpen((function(){})),this._socket.onMessage((function(){})),this._socket.onError((function(){}))):ai&&(this._socket.onopen=null,this._socket.onmessage=null,this._socket.onclose=null,this._socket.onerror=null),ti?this._socket.close({code:e}):this._socket.close(e),this._socket=null)}},{key:"send",value:function(e){ni?si.sendSocketMessage({data:e.data,fail:function(){e.fail&&e.requestID&&e.fail(e.requestID)}}):this._socket&&(ri?this._socket.send({data:e.data,fail:function(){e.fail&&e.requestID&&e.fail(e.requestID)}}):ai&&this._socket.send(e.data))}}]),e}(),Sv=4e3,Ev=4001,Dv="connected",kv="connecting",Cv="disconnected",Av=function(){function e(t){Pn(this,e),this._channelModule=t,this._className="SocketHandler",this._promiseMap=new Map,this._readyState=Cv,this._simpleRequestMap=new Map,this.MAX_SIZE=100,this._startSequence=ru(),this._startTs=0,this._reConnectFlag=!1,this._nextPingTs=0,this._reConnectCount=0,this.MAX_RECONNECT_COUNT=3,this._socketID=-1,this._random=0,this._socket=null,this._url="",this._onOpenTs=0,this._setWebsocketHost(),this._initConnection()}return Un(e,[{key:"_setWebsocketHost",value:function(){var e=this._channelModule.getModule(Yc).getSDKAppID();this._channelModule.isOversea()&&zs.HOST.setCurrent(Hs),e>=2e7&&e<3e7&&zs.HOST.setCurrent(js),e>=3e7&&e<4e7&&zs.HOST.setCurrent(Ys),e>=4e7&&e<5e7&&zs.HOST.setCurrent(Ws)}},{key:"_initConnection",value:function(){Wi(zs.HOST.CURRENT.BACKUP)||""===this._url?this._url=zs.HOST.CURRENT.DEFAULT:this._url===zs.HOST.CURRENT.DEFAULT?this._url=zs.HOST.CURRENT.BACKUP:this._url===zs.HOST.CURRENT.BACKUP&&(this._url=zs.HOST.CURRENT.DEFAULT),this._connect(),this._nextPingTs=0}},{key:"onCheckTimer",value:function(e){e%1==0&&this._checkPromiseMap()}},{key:"_checkPromiseMap",value:function(){var e=this;0!==this._promiseMap.size&&this._promiseMap.forEach((function(t,n){var o=t.reject,r=t.timestamp;Date.now()-r>=15e3&&(xi.log("".concat(e._className,"._checkPromiseMap request timeout, delete requestID:").concat(n)),e._promiseMap.delete(n),o(new $_({code:vd.NETWORK_TIMEOUT,message:Ip})),e._channelModule.onRequestTimeout(n))}))}},{key:"onOpen",value:function(e){this._onOpenTs=Date.now();var t=e.id;this._socketID=t,new Kp(Xp).setMessage(n).setMessage("socketID:".concat(t)).end();var n=Date.now()-this._startTs;xi.log("".concat(this._className,"._onOpen cost ").concat(n," ms. socketID:").concat(t)),e.id===this._socketID&&(this._readyState=Dv,this._reConnectCount=0,this._resend(),!0===this._reConnectFlag&&(this._channelModule.onReconnected(),this._reConnectFlag=!1),this._channelModule.onOpen())}},{key:"onClose",value:function(e){var t=new Kp(Qp),n=e.id,o=e.e,r="sourceSocketID:".concat(n," currentSocketID:").concat(this._socketID),a=0;0!==this._onOpenTs&&(a=Date.now()-this._onOpenTs),t.setMessage(a).setMoreMessage(r).setCode(o.code).end(),xi.log("".concat(this._className,"._onClose code:").concat(o.code," reason:").concat(o.reason," ").concat(r," onlineTime:").concat(a)),n===this._socketID&&(this._readyState=Cv,a<1e3?this._channelModule.onReconnectFailed():this._channelModule.onClose())}},{key:"onError",value:function(e){var t=e.id,n=e.e,o="sourceSocketID:".concat(t," currentSocketID:").concat(this._socketID);new Kp(Zp).setMessage(n.errMsg||nu(n)).setMoreMessage(o).setLevel("error").end(),xi.warn("".concat(this._className,"._onError"),n,o),t===this._socketID&&(this._readyState="",this._channelModule.onError())}},{key:"onMessage",value:function(e){var t;try{t=JSON.parse(e.data)}catch(Xv){new Kp(gg).setMessage(e.data).end()}if(t&&t.head){var n=this._getRequestIDFromHead(t.head),o=Su(t.head),r=Iv(t.body,this._getResponseKeyMap(o));if(xi.debug("".concat(this._className,".onMessage ret:").concat(JSON.stringify(r)," requestID:").concat(n," has:").concat(this._promiseMap.has(n))),this._setNextPingTs(),this._promiseMap.has(n)){var a=this._promiseMap.get(n),s=a.resolve,i=a.reject,u=a.timestamp;return this._promiseMap.delete(n),this._calcRTT(u),void(r.errorCode&&0!==r.errorCode?(this._channelModule.onErrorCodeNotZero(r),i(new $_({code:r.errorCode,message:r.errorInfo||""}))):s(W_(r)))}this._channelModule.onMessage({head:t.head,body:r})}}},{key:"_calcRTT",value:function(e){var t=Date.now()-e;this._channelModule.getModule(sl).addRTT(t)}},{key:"_connect",value:function(){new Kp(Jp).setMessage("url:".concat(this.getURL())).end(),xi.log("".concat(this._className,"._connect url:").concat(this.getURL())),this._startTs=Date.now(),this._socket=new Tv(this),this._socketID=this._socket.getID(),this._readyState=kv}},{key:"getURL",value:function(){var e=this._channelModule.getModule(Yc);return"".concat(this._url,"/info?sdkappid=").concat(e.getSDKAppID(),"&instanceid=").concat(e.getInstanceID(),"&random=").concat(this._getRandom())}},{key:"_closeConnection",value:function(e){xi.log("".concat(this._className,"._closeConnection")),this._socket&&(this._socket.close(e),this._socketID=-1,this._socket=null,this._readyState=Cv)}},{key:"_resend",value:function(){var e=this;if(xi.log("".concat(this._className,"._resend reConnectFlag:").concat(this._reConnectFlag),"promiseMap.size:".concat(this._promiseMap.size," simpleRequestMap.size:").concat(this._simpleRequestMap.size)),this._promiseMap.size>0&&this._promiseMap.forEach((function(t,n){var o=t.uplinkData,r=t.resolve,a=t.reject;e._promiseMap.set(n,{resolve:r,reject:a,timestamp:Date.now(),uplinkData:o}),e._execute(n,o)})),this._simpleRequestMap.size>0){var t,n=ro(this._simpleRequestMap);try{for(n.s();!(t=n.n()).done;){var o=Qn(t.value,2),r=o[0],a=o[1];this._execute(r,a)}}catch(u){n.e(u)}finally{n.f()}this._simpleRequestMap.clear()}}},{key:"send",value:function(e){var t=this;e.head.seq=this._getSequence(),e.head.reqtime=Math.floor(Date.now()/1e3),e.keyMap;var n=Wn(e,["keyMap"]),o=this._getRequestIDFromHead(e.head),r=JSON.stringify(n);return new Promise((function(e,a){t._promiseMap.set(o,{resolve:e,reject:a,timestamp:Date.now(),uplinkData:r}),xi.debug("".concat(t._className,".send uplinkData:").concat(JSON.stringify(n)," requestID:").concat(o," readyState:").concat(t._readyState)),t._readyState!==Dv?t._reConnect():(t._execute(o,r),t._channelModule.getModule(sl).addRequestCount())}))}},{key:"simplySend",value:function(e){e.head.seq=this._getSequence(),e.head.reqtime=Math.floor(Date.now()/1e3),e.keyMap;var t=Wn(e,["keyMap"]),n=this._getRequestIDFromHead(e.head),o=JSON.stringify(t);this._readyState!==Dv?(this._simpleRequestMap.size<this.MAX_SIZE?this._simpleRequestMap.set(n,o):xi.log("".concat(this._className,".simplySend. simpleRequestMap is full, drop request!")),this._reConnect()):this._execute(n,o)}},{key:"_execute",value:function(e,t){this._socket.send({data:t,fail:ri?this._onSendFail.bind(this):void 0,requestID:e})}},{key:"_onSendFail",value:function(e){xi.log("".concat(this._className,"._onSendFail requestID:").concat(e))}},{key:"_getSequence",value:function(){var e;if(this._startSequence<2415919103)return e=this._startSequence,this._startSequence+=1,2415919103===this._startSequence&&(this._startSequence=ru()),e}},{key:"_getRequestIDFromHead",value:function(e){return e.servcmd+e.seq}},{key:"_getResponseKeyMap",value:function(e){var t=this._channelModule.getKeyMap(e);return xn({},_v.response,{},t.response)}},{key:"_reConnect",value:function(){this._readyState!==Dv&&this._readyState!==kv&&this.forcedReconnect()}},{key:"forcedReconnect",value:function(){var e=this;xi.log("".concat(this._className,".forcedReconnect count:").concat(this._reConnectCount," readyState:").concat(this._readyState)),this._reConnectFlag=!0,this._resetRandom(),this._reConnectCount<this.MAX_RECONNECT_COUNT?(this._reConnectCount+=1,this._closeConnection(Ev),this._initConnection()):this._channelModule.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0];n[1],o?(xi.warn("".concat(e._className,".forcedReconnect disconnected from wsserver but network is ok, continue...")),e._reConnectCount=0,e._closeConnection(Ev),e._initConnection()):e._channelModule.onReconnectFailed()}))}},{key:"getReconnectFlag",value:function(){return this._reConnectFlag}},{key:"_setNextPingTs",value:function(){this._nextPingTs=Date.now()+1e4}},{key:"getNextPingTs",value:function(){return this._nextPingTs}},{key:"isConnected",value:function(){return this._readyState===Dv}},{key:"_getRandom",value:function(){return 0===this._random&&(this._random=Math.random()),this._random}},{key:"_resetRandom",value:function(){this._random=0}},{key:"close",value:function(){xi.log("".concat(this._className,".close")),this._closeConnection(Sv),this._promiseMap.clear(),this._startSequence=ru(),this._readyState=Cv,this._simpleRequestMap.clear(),this._reConnectFlag=!1,this._reConnectCount=0,this._onOpenTs=0,this._url="",this._random=0}}]),e}(),Nv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;if(Pn(this,n),(o=t.call(this,e))._className="ChannelModule",o._socketHandler=new Av(zn(o)),o._probing=!1,o._isAppShowing=!0,o._previousState=so.NET_STATE_CONNECTED,ri&&"function"==typeof si.onAppShow&&"function"==typeof si.onAppHide){var r=o._onAppHide.bind(zn(o)),a=o._onAppShow.bind(zn(o));"function"==typeof si.offAppHide&&si.offAppHide(r),"function"==typeof si.offAppShow&&si.offAppShow(a),si.onAppHide(r),si.onAppShow(a)}return o._timerForNotLoggedIn=-1,o._timerForNotLoggedIn=setInterval(o.onCheckTimer.bind(zn(o)),1e3),o._fatalErrorFlag=!1,o}return Un(n,[{key:"onCheckTimer",value:function(e){this._socketHandler&&(this.isLoggedIn()?(this._timerForNotLoggedIn>0&&(clearInterval(this._timerForNotLoggedIn),this._timerForNotLoggedIn=-1),this._socketHandler.onCheckTimer(e)):this._socketHandler.onCheckTimer(1),this._checkNextPing())}},{key:"onErrorCodeNotZero",value:function(e){this.getModule(el).onErrorCodeNotZero(e)}},{key:"onMessage",value:function(e){this.getModule(el).onMessage(e)}},{key:"send",value:function(e){return this._socketHandler?this._previousState!==so.NET_STATE_CONNECTED&&e.head.servcmd.includes(cd)?this._sendLogViaHTTP(e):this._socketHandler.send(e):Promise.reject()}},{key:"_sendLogViaHTTP",value:function(e){return new Promise((function(t,n){var o="https://webim.tim.qq.com/v4/imopenstat/tim_web_report_v2?sdkappid=".concat(e.head.sdkappid,"&reqtime=").concat(Date.now()),r=JSON.stringify(e.body),a="application/x-www-form-urlencoded;charset=UTF-8";if(ri)si.request({url:o,data:r,method:"POST",timeout:3e3,header:{"content-type":a},success:function(){t()},fail:function(){n(new $_({code:vd.NETWORK_ERROR,message:yp}))}});else{var s=new XMLHttpRequest,i=setTimeout((function(){s.abort(),n(new $_({code:vd.NETWORK_TIMEOUT,message:Ip}))}),3e3);s.onreadystatechange=function(){4===s.readyState&&(clearTimeout(i),200===s.status||304===s.status?t():n(new $_({code:vd.NETWORK_ERROR,message:yp})))},s.open("POST",o,!0),s.setRequestHeader("Content-type",a),s.send(r)}}))}},{key:"simplySend",value:function(e){return this._socketHandler?this._socketHandler.simplySend(e):Promise.reject()}},{key:"onOpen",value:function(){this._ping()}},{key:"onClose",value:function(){this.reConnect()}},{key:"onError",value:function(){ri&&xi.error("".concat(this._className,".onError 从v2.11.2起，SDK 支持了 WebSocket，如您未添加相关受信域名，请先添加！升级指引: https://web.sdk.qcloud.com/im/doc/zh-cn/tutorial-02-upgradeguideline.html"))}},{key:"getKeyMap",value:function(e){return this.getModule(el).getKeyMap(e)}},{key:"_onAppHide",value:function(){this._isAppShowing=!1}},{key:"_onAppShow",value:function(){this._isAppShowing=!0}},{key:"onRequestTimeout",value:function(e){}},{key:"onReconnected",value:function(){xi.log("".concat(this._className,".onReconnected")),this.getModule(el).onReconnected(),this._emitNetStateChangeEvent(so.NET_STATE_CONNECTED)}},{key:"onReconnectFailed",value:function(){xi.log("".concat(this._className,".onReconnectFailed")),this._emitNetStateChangeEvent(so.NET_STATE_DISCONNECTED)}},{key:"reConnect",value:function(){if(!this._fatalErrorFlag&&this._socketHandler){var e=this._socketHandler.getReconnectFlag();if(xi.log("".concat(this._className,".reConnect previousState:").concat(this._previousState," reconnectFlag:").concat(e)),this._previousState===so.NET_STATE_CONNECTING&&e)return;this._socketHandler.forcedReconnect(),this._emitNetStateChangeEvent(so.NET_STATE_CONNECTING)}}},{key:"_emitNetStateChangeEvent",value:function(e){this._previousState!==e&&(this._previousState=e,this.emitOuterEvent(ao.NET_STATE_CHANGE,{state:e}))}},{key:"_ping",value:function(){var e=this;if(!0!==this._probing){this._probing=!0;var t=this.getModule(el).getProtocolData({protocolName:ld});this.send(t).then((function(){e._probing=!1})).catch((function(t){if(xi.warn("".concat(e._className,"._ping failed. error:"),t),e._probing=!1,t&&60002===t.code)return new Kp(ch).setMessage("code:".concat(t.code," message:").concat(t.message)).setNetworkType(e.getModule(Jc).getNetworkType()).end(),e._fatalErrorFlag=!0,void e._emitNetStateChangeEvent(so.NET_STATE_DISCONNECTED);e.probeNetwork().then((function(t){var n=Qn(t,2),o=n[0],r=n[1];xi.log("".concat(e._className,"._ping failed. isAppShowing:").concat(e._isAppShowing," online:").concat(o," networkType:").concat(r)),o?e.reConnect():e._emitNetStateChangeEvent(so.NET_STATE_DISCONNECTED)}))}))}}},{key:"_checkNextPing",value:function(){this._socketHandler&&this._socketHandler.isConnected()&&Date.now()>=this._socketHandler.getNextPingTs()&&this._ping()}},{key:"dealloc",value:function(){this._socketHandler&&(this._socketHandler.close(),this._socketHandler=null),this._timerForNotLoggedIn>-1&&clearInterval(this._timerForNotLoggedIn)}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset")),this._previousState=so.NET_STATE_CONNECTED,this._probing=!1,this._fatalErrorFlag=!1,this._timerForNotLoggedIn=setInterval(this.onCheckTimer.bind(this),1e3)}}]),n}(il),Ov=function(){function e(t){Pn(this,e),this._className="ProtocolHandler",this._sessionModule=t,this._configMap=new Map,this._fillConfigMap()}return Un(e,[{key:"_fillConfigMap",value:function(){this._configMap.clear();var e=this._sessionModule.genCommonHead(),t=this._sessionModule.genCosSpecifiedHead(),n=this._sessionModule.genSSOReportHead();this._configMap.set(ul,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.IM_OPEN_STATUS,".").concat(zs.CMD.LOGIN)}),body:{state:"Online"},keyMap:{response:{TinyId:"tinyID",InstId:"instanceID",HelloInterval:"helloInterval"}}}}(e)),this._configMap.set(cl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.IM_OPEN_STATUS,".").concat(zs.CMD.LOGOUT)}),body:{type:0},keyMap:{request:{type:"wslogout_type"}}}}(e)),this._configMap.set(ll,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.IM_OPEN_STATUS,".").concat(zs.CMD.HELLO)}),body:{},keyMap:{response:{NewInstInfo:"newInstanceInfo"}}}}(e)),this._configMap.set(id,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.IM_COS_SIGN,".").concat(zs.CMD.COS_SIGN)}),body:{cmd:"open_im_cos_svc",subCmd:"get_cos_token",duration:300,version:2},keyMap:{request:{userSig:"usersig",subCmd:"sub_cmd",cmd:"cmd",duration:"duration",version:"version"},response:{expired_time:"expiredTime",bucket_name:"bucketName",session_token:"sessionToken",tmp_secret_id:"secretId",tmp_secret_key:"secretKey"}}}}(t)),this._configMap.set(ud,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.CUSTOM_UPLOAD,".").concat(zs.CMD.COS_PRE_SIG)}),body:{fileType:void 0,fileName:void 0,uploadMethod:0,duration:900},keyMap:{request:{userSig:"usersig",fileType:"file_type",fileName:"file_name",uploadMethod:"upload_method"},response:{expired_time:"expiredTime",request_id:"requestId",head_url:"headUrl",upload_url:"uploadUrl",download_url:"downloadUrl",ci_url:"ciUrl"}}}}(t)),this._configMap.set(_d,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.CLOUD_CONTROL,".").concat(zs.CMD.FETCH_CLOUD_CONTROL_CONFIG)}),body:{SDKAppID:0,version:0},keyMap:{request:{SDKAppID:"uint32_sdkappid",version:"uint64_version"},response:{int32_error_code:"errorCode",str_error_message:"errorMessage",str_json_config:"cloudControlConfig",uint32_expired_time:"expiredTime",uint32_sdkappid:"SDKAppID",uint64_version:"version"}}}}(e)),this._configMap.set(md,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.CLOUD_CONTROL,".").concat(zs.CMD.PUSHED_CLOUD_CONTROL_CONFIG)}),body:{},keyMap:{response:{int32_error_code:"errorCode",str_error_message:"errorMessage",str_json_config:"cloudControlConfig",uint32_expired_time:"expiredTime",uint32_sdkappid:"SDKAppID",uint64_version:"version"}}}}(e)),this._configMap.set(dl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.OPEN_IM,".").concat(zs.CMD.GET_MESSAGES)}),body:{cookie:"",syncFlag:0,needAbstract:1,isOnlineSync:0},keyMap:{request:{fromAccount:"From_Account",toAccount:"To_Account",from:"From_Account",to:"To_Account",time:"MsgTimeStamp",sequence:"MsgSeq",random:"MsgRandom",elements:"MsgBody"},response:{MsgList:"messageList",SyncFlag:"syncFlag",To_Account:"to",From_Account:"from",ClientSeq:"clientSequence",MsgSeq:"sequence",NoticeSeq:"noticeSequence",NotifySeq:"notifySequence",MsgRandom:"random",MsgTimeStamp:"time",MsgContent:"content",ToGroupId:"groupID",MsgKey:"messageKey",GroupTips:"groupTips",MsgBody:"elements",MsgType:"type",C2CRemainingUnreadCount:"C2CRemainingUnreadList",C2CPairUnreadCount:"C2CPairUnreadList"}}}}(e)),this._configMap.set(pl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.OPEN_IM,".").concat(zs.CMD.BIG_DATA_HALLWAY_AUTH_KEY)}),body:{}}}(e)),this._configMap.set(gl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.OPEN_IM,".").concat(zs.CMD.SEND_MESSAGE)}),body:{fromAccount:"",toAccount:"",msgTimeStamp:void 0,msgSeq:0,msgRandom:0,msgBody:[],cloudCustomData:void 0,nick:"",avatar:"",msgLifeTime:void 0,offlinePushInfo:{pushFlag:0,title:"",desc:"",ext:"",apnsInfo:{badgeMode:0},androidInfo:{OPPOChannelID:""}}},keyMap:{request:{fromAccount:"From_Account",toAccount:"To_Account",msgTimeStamp:"MsgTimeStamp",msgSeq:"MsgSeq",msgRandom:"MsgRandom",msgBody:"MsgBody",count:"MaxCnt",lastMessageTime:"LastMsgTime",messageKey:"MsgKey",peerAccount:"Peer_Account",data:"Data",description:"Desc",extension:"Ext",type:"MsgType",content:"MsgContent",sizeType:"Type",uuid:"UUID",url:"",imageUrl:"URL",fileUrl:"Url",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",videoUrl:"",downloadFlag:"Download_Flag",nick:"From_AccountNick",avatar:"From_AccountHeadurl",from:"From_Account",time:"MsgTimeStamp",messageRandom:"MsgRandom",messageSequence:"MsgSeq",elements:"MsgBody",clientSequence:"ClientSeq",payload:"MsgContent",messageList:"MsgList",messageNumber:"MsgNum",abstractList:"AbstractList",messageBody:"MsgBody"}}}}(e)),this._configMap.set(hl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.SEND_GROUP_MESSAGE)}),body:{fromAccount:"",groupID:"",random:0,clientSequence:0,priority:"",msgBody:[],cloudCustomData:void 0,onlineOnlyFlag:0,offlinePushInfo:{pushFlag:0,title:"",desc:"",ext:"",apnsInfo:{badgeMode:0},androidInfo:{OPPOChannelID:""}},groupAtInfo:[]},keyMap:{request:{to:"GroupId",extension:"Ext",data:"Data",description:"Desc",random:"Random",sequence:"ReqMsgSeq",count:"ReqMsgNumber",type:"MsgType",priority:"MsgPriority",content:"MsgContent",elements:"MsgBody",sizeType:"Type",uuid:"UUID",url:"",imageUrl:"URL",fileUrl:"Url",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",videoUrl:"",downloadFlag:"Download_Flag",clientSequence:"ClientSeq",from:"From_Account",time:"MsgTimeStamp",messageRandom:"MsgRandom",messageSequence:"MsgSeq",payload:"MsgContent",messageList:"MsgList",messageNumber:"MsgNum",abstractList:"AbstractList",messageBody:"MsgBody"},response:{MsgTime:"time",MsgSeq:"sequence"}}}}(e)),this._configMap.set(yl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.OPEN_IM,".").concat(zs.CMD.REVOKE_C2C_MESSAGE)}),body:{msgInfo:{fromAccount:"",toAccount:"",msgTimeStamp:0,msgSeq:0,msgRandom:0}},keyMap:{request:{msgInfo:"MsgInfo",msgTimeStamp:"MsgTimeStamp",msgSeq:"MsgSeq",msgRandom:"MsgRandom"}}}}(e)),this._configMap.set(Kl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.REVOKE_GROUP_MESSAGE)}),body:{to:"",msgSeqList:void 0},keyMap:{request:{to:"GroupId",msgSeqList:"MsgSeqList",msgSeq:"MsgSeq"}}}}(e)),this._configMap.set(Tl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.OPEN_IM,".").concat(zs.CMD.GET_C2C_ROAM_MESSAGES)}),body:{peerAccount:"",count:15,lastMessageTime:0,messageKey:"",withRecalledMessage:1},keyMap:{request:{messageKey:"MsgKey",peerAccount:"Peer_Account",count:"MaxCnt",lastMessageTime:"LastMsgTime",withRecalledMessage:"WithRecalledMsg"},response:{LastMsgTime:"lastMessageTime"}}}}(e)),this._configMap.set(Hl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.GET_GROUP_ROAM_MESSAGES)}),body:{withRecalledMsg:1,groupID:"",count:15,sequence:""},keyMap:{request:{sequence:"ReqMsgSeq",count:"ReqMsgNumber",withRecalledMessage:"WithRecalledMsg"},response:{Random:"random",MsgTime:"time",MsgSeq:"sequence",ReqMsgSeq:"sequence",RspMsgList:"messageList",IsPlaceMsg:"isPlaceMessage",IsSystemMsg:"isSystemMessage",ToGroupId:"to",EnumFrom_AccountType:"fromAccountType",EnumTo_AccountType:"toAccountType",GroupCode:"groupCode",MsgPriority:"priority",MsgBody:"elements",MsgType:"type",MsgContent:"content",IsFinished:"complete",Download_Flag:"downloadFlag",ClientSeq:"clientSequence",ThumbUUID:"thumbUUID",VideoUUID:"videoUUID"}}}}(e)),this._configMap.set(Il,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.OPEN_IM,".").concat(zs.CMD.SET_C2C_MESSAGE_READ)}),body:{C2CMsgReaded:void 0},keyMap:{request:{lastMessageTime:"LastedMsgTime"}}}}(e)),this._configMap.set(Bl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.SET_GROUP_MESSAGE_READ)}),body:{groupID:void 0,messageReadSeq:void 0},keyMap:{request:{messageReadSeq:"MsgReadedSeq"}}}}(e)),this._configMap.set(El,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.OPEN_IM,".").concat(zs.CMD.DELETE_C2C_MESSAGE)}),body:{fromAccount:"",to:"",keyList:void 0},keyMap:{request:{keyList:"MsgKeyList"}}}}(e)),this._configMap.set(Xl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.DELETE_GROUP_MESSAGE)}),body:{groupID:"",deleter:"",keyList:void 0},keyMap:{request:{deleter:"Deleter_Account",keyList:"Seqs"}}}}(e)),this._configMap.set(Sl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.OPEN_IM,".").concat(zs.CMD.GET_PEER_READ_TIME)}),body:{userIDList:void 0},keyMap:{request:{userIDList:"To_Account"},response:{ReadTime:"peerReadTimeList"}}}}(e)),this._configMap.set(kl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.RECENT_CONTACT,".").concat(zs.CMD.GET_CONVERSATION_LIST)}),body:{fromAccount:void 0,count:0},keyMap:{request:{},response:{SessionItem:"conversations",ToAccount:"groupID",To_Account:"userID",UnreadMsgCount:"unreadCount",MsgGroupReadedSeq:"messageReadSeq",C2cPeerReadTime:"c2cPeerReadTime"}}}}(e)),this._configMap.set(Dl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.RECENT_CONTACT,".").concat(zs.CMD.PAGING_GET_CONVERSATION_LIST)}),body:{fromAccount:void 0,timeStamp:void 0,startIndex:void 0,pinnedTimeStamp:void 0,pinnedStartIndex:void 0,orderType:void 0,messageAssistFlag:4,assistFlag:7},keyMap:{request:{messageAssistFlag:"MsgAssistFlags",assistFlag:"AssistFlags",pinnedTimeStamp:"TopTimeStamp",pinnedStartIndex:"TopStartIndex"},response:{SessionItem:"conversations",ToAccount:"groupID",To_Account:"userID",UnreadMsgCount:"unreadCount",MsgGroupReadedSeq:"messageReadSeq",C2cPeerReadTime:"c2cPeerReadTime",LastMsgFlags:"lastMessageFlag",TopFlags:"isPinned",TopTimeStamp:"pinnedTimeStamp",TopStartIndex:"pinnedStartIndex"}}}}(e)),this._configMap.set(Cl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.RECENT_CONTACT,".").concat(zs.CMD.DELETE_CONVERSATION)}),body:{fromAccount:"",toAccount:void 0,type:1,toGroupID:void 0},keyMap:{request:{toGroupID:"ToGroupid"}}}}(e)),this._configMap.set(Al,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.RECENT_CONTACT,".").concat(zs.CMD.PIN_CONVERSATION)}),body:{fromAccount:"",operationType:1,itemList:void 0},keyMap:{request:{itemList:"RecentContactItem"}}}}(e)),this._configMap.set(Nl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.OPEN_IM,".").concat(zs.CMD.DELETE_GROUP_AT_TIPS)}),body:{messageListToDelete:void 0},keyMap:{request:{messageListToDelete:"DelMsgList",messageSeq:"MsgSeq",messageRandom:"MsgRandom"}}}}(e)),this._configMap.set(fl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.PROFILE,".").concat(zs.CMD.PORTRAIT_GET)}),body:{fromAccount:"",userItem:[]},keyMap:{request:{toAccount:"To_Account",standardSequence:"StandardSequence",customSequence:"CustomSequence"}}}}(e)),this._configMap.set(_l,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.PROFILE,".").concat(zs.CMD.PORTRAIT_SET)}),body:{fromAccount:"",profileItem:[{tag:y_.NICK,value:""},{tag:y_.GENDER,value:""},{tag:y_.ALLOWTYPE,value:""},{tag:y_.AVATAR,value:""}]},keyMap:{request:{toAccount:"To_Account",standardSequence:"StandardSequence",customSequence:"CustomSequence"}}}}(e)),this._configMap.set(ml,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.FRIEND,".").concat(zs.CMD.GET_BLACKLIST)}),body:{fromAccount:"",startIndex:0,maxLimited:30,lastSequence:0},keyMap:{response:{CurruentSequence:"currentSequence"}}}}(e)),this._configMap.set(vl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.FRIEND,".").concat(zs.CMD.ADD_BLACKLIST)}),body:{fromAccount:"",toAccount:[]}}}(e)),this._configMap.set(Ml,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.FRIEND,".").concat(zs.CMD.DELETE_BLACKLIST)}),body:{fromAccount:"",toAccount:[]}}}(e)),this._configMap.set(Ol,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.GET_JOINED_GROUPS)}),body:{memberAccount:"",limit:void 0,offset:void 0,groupType:void 0,responseFilter:{groupBaseInfoFilter:void 0,selfInfoFilter:void 0}},keyMap:{request:{memberAccount:"Member_Account"},response:{GroupIdList:"groups",MsgFlag:"messageRemindType"}}}}(e)),this._configMap.set(Ll,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.GET_GROUP_INFO)}),body:{groupIDList:void 0,responseFilter:{groupBaseInfoFilter:["Type","Name","Introduction","Notification","FaceUrl","Owner_Account","CreateTime","InfoSeq","LastInfoTime","LastMsgTime","MemberNum","MaxMemberNum","ApplyJoinOption","NextMsgSeq","ShutUpAllMember"],groupCustomFieldFilter:void 0,memberInfoFilter:void 0,memberCustomFieldFilter:void 0}},keyMap:{request:{groupIDList:"GroupIdList",groupCustomField:"AppDefinedData",memberCustomField:"AppMemberDefinedData",groupCustomFieldFilter:"AppDefinedDataFilter_Group",memberCustomFieldFilter:"AppDefinedDataFilter_GroupMember"},response:{GroupIdList:"groups",MsgFlag:"messageRemindType",AppDefinedData:"groupCustomField",AppMemberDefinedData:"memberCustomField",AppDefinedDataFilter_Group:"groupCustomFieldFilter",AppDefinedDataFilter_GroupMember:"memberCustomFieldFilter",InfoSeq:"infoSequence",MemberList:"members",GroupInfo:"groups",ShutUpUntil:"muteUntil",ShutUpAllMember:"muteAllMembers",ApplyJoinOption:"joinOption"}}}}(e)),this._configMap.set(Rl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.CREATE_GROUP)}),body:{type:void 0,name:void 0,groupID:void 0,ownerID:void 0,introduction:void 0,notification:void 0,maxMemberNum:void 0,joinOption:void 0,memberList:void 0,groupCustomField:void 0,memberCustomField:void 0,webPushFlag:1,avatar:"FaceUrl"},keyMap:{request:{ownerID:"Owner_Account",userID:"Member_Account",avatar:"FaceUrl",maxMemberNum:"MaxMemberCount",joinOption:"ApplyJoinOption",groupCustomField:"AppDefinedData",memberCustomField:"AppMemberDefinedData"},response:{HugeGroupFlag:"avChatRoomFlag",OverJoinedGroupLimit_Account:"overLimitUserIDList"}}}}(e)),this._configMap.set(bl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.DESTROY_GROUP)}),body:{groupID:void 0}}}(e)),this._configMap.set(wl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.MODIFY_GROUP_INFO)}),body:{groupID:void 0,name:void 0,introduction:void 0,notification:void 0,avatar:void 0,maxMemberNum:void 0,joinOption:void 0,groupCustomField:void 0,muteAllMembers:void 0},keyMap:{request:{maxMemberNum:"MaxMemberCount",groupCustomField:"AppDefinedData",muteAllMembers:"ShutUpAllMember",joinOption:"ApplyJoinOption",avatar:"FaceUrl"},response:{AppDefinedData:"groupCustomField",ShutUpAllMember:"muteAllMembers",ApplyJoinOption:"joinOption"}}}}(e)),this._configMap.set(Pl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.APPLY_JOIN_GROUP)}),body:{groupID:void 0,applyMessage:void 0,userDefinedField:void 0,webPushFlag:1},keyMap:{request:{applyMessage:"ApplyMsg"},response:{HugeGroupFlag:"avChatRoomFlag",AVChatRoomKey:"avChatRoomKey"}}}}(e)),this._configMap.set(Gl,function(e){return e.a2,e.tinyid,{head:xn({},Wn(e,["a2","tinyid"]),{servcmd:"".concat(zs.NAME.BIG_GROUP_NO_AUTH,".").concat(zs.CMD.APPLY_JOIN_GROUP)}),body:{groupID:void 0,applyMessage:void 0,userDefinedField:void 0,webPushFlag:1},keyMap:{request:{applyMessage:"ApplyMsg"},response:{HugeGroupFlag:"avChatRoomFlag"}}}}(e)),this._configMap.set(Ul,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.QUIT_GROUP)}),body:{groupID:void 0}}}(e)),this._configMap.set(Fl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.SEARCH_GROUP_BY_ID)}),body:{groupIDList:void 0,responseFilter:{groupBasePublicInfoFilter:["Type","Name","Introduction","Notification","FaceUrl","CreateTime","Owner_Account","LastInfoTime","LastMsgTime","NextMsgSeq","MemberNum","MaxMemberNum","ApplyJoinOption"]}},keyMap:{response:{ApplyJoinOption:"joinOption"}}}}(e)),this._configMap.set(ql,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.CHANGE_GROUP_OWNER)}),body:{groupID:void 0,newOwnerID:void 0},keyMap:{request:{newOwnerID:"NewOwner_Account"}}}}(e)),this._configMap.set(xl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.HANDLE_APPLY_JOIN_GROUP)}),body:{groupID:void 0,applicant:void 0,handleAction:void 0,handleMessage:void 0,authentication:void 0,messageKey:void 0,userDefinedField:void 0},keyMap:{request:{applicant:"Applicant_Account",handleAction:"HandleMsg",handleMessage:"ApprovalMsg",messageKey:"MsgKey"}}}}(e)),this._configMap.set(Vl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.HANDLE_GROUP_INVITATION)}),body:{groupID:void 0,inviter:void 0,handleAction:void 0,handleMessage:void 0,authentication:void 0,messageKey:void 0,userDefinedField:void 0},keyMap:{request:{inviter:"Inviter_Account",handleAction:"HandleMsg",handleMessage:"ApprovalMsg",messageKey:"MsgKey"}}}}(e)),this._configMap.set(jl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.GET_GROUP_APPLICATION)}),body:{startTime:void 0,limit:void 0,handleAccount:void 0},keyMap:{request:{handleAccount:"Handle_Account"}}}}(e)),this._configMap.set(Yl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.OPEN_IM,".").concat(zs.CMD.DELETE_GROUP_SYSTEM_MESSAGE)}),body:{messageListToDelete:void 0},keyMap:{request:{messageListToDelete:"DelMsgList",messageSeq:"MsgSeq",messageRandom:"MsgRandom"}}}}(e)),this._configMap.set(Wl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.BIG_GROUP_LONG_POLLING,".").concat(zs.CMD.AVCHATROOM_LONG_POLL)}),body:{USP:1,startSeq:1,holdTime:90,key:void 0},keyMap:{request:{USP:"USP"},response:{ToGroupId:"groupID"}}}}(e)),this._configMap.set(zl,function(e){return e.a2,e.tinyid,{head:xn({},Wn(e,["a2","tinyid"]),{servcmd:"".concat(zs.NAME.BIG_GROUP_LONG_POLLING_NO_AUTH,".").concat(zs.CMD.AVCHATROOM_LONG_POLL)}),body:{USP:1,startSeq:1,holdTime:90,key:void 0},keyMap:{request:{USP:"USP"},response:{ToGroupId:"groupID"}}}}(e)),this._configMap.set(Jl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.GET_ONLINE_MEMBER_NUM)}),body:{groupID:void 0}}}(e)),this._configMap.set(Ql,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.SET_GROUP_ATTRIBUTES)}),body:{groupID:void 0,groupAttributeList:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]},keyMap:{request:{key:"key",value:"value"}}}}(e)),this._configMap.set(Zl,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.MODIFY_GROUP_ATTRIBUTES)}),body:{groupID:void 0,groupAttributeList:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]},keyMap:{request:{key:"key",value:"value"}}}}(e)),this._configMap.set($l,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.DELETE_GROUP_ATTRIBUTES)}),body:{groupID:void 0,groupAttributeList:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]},keyMap:{request:{key:"key"}}}}(e)),this._configMap.set(ed,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.CLEAR_GROUP_ATTRIBUTES)}),body:{groupID:void 0,mainSequence:void 0,avChatRoomKey:void 0,attributeControl:["RaceConflict"]}}}(e)),this._configMap.set(td,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP_ATTR,".").concat(zs.CMD.GET_GROUP_ATTRIBUTES)}),body:{groupID:void 0,avChatRoomKey:void 0,groupType:1},keyMap:{request:{avChatRoomKey:"Key",groupType:"GroupType"}}}}(e)),this._configMap.set(nd,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.GET_GROUP_MEMBER_LIST)}),body:{groupID:void 0,limit:0,offset:0,memberRoleFilter:void 0,memberInfoFilter:["Role","NameCard","ShutUpUntil","JoinTime"],memberCustomFieldFilter:void 0},keyMap:{request:{memberCustomFieldFilter:"AppDefinedDataFilter_GroupMember"},response:{AppMemberDefinedData:"memberCustomField",AppDefinedDataFilter_GroupMember:"memberCustomFieldFilter",MemberList:"members",ShutUpUntil:"muteUntil"}}}}(e)),this._configMap.set(od,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.GET_GROUP_MEMBER_INFO)}),body:{groupID:void 0,userIDList:void 0,memberInfoFilter:void 0,memberCustomFieldFilter:void 0},keyMap:{request:{userIDList:"Member_List_Account",memberCustomFieldFilter:"AppDefinedDataFilter_GroupMember"},response:{MemberList:"members",ShutUpUntil:"muteUntil",AppDefinedDataFilter_GroupMember:"memberCustomFieldFilter",AppMemberDefinedData:"memberCustomField"}}}}(e)),this._configMap.set(rd,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.ADD_GROUP_MEMBER)}),body:{groupID:void 0,silence:void 0,userIDList:void 0},keyMap:{request:{userID:"Member_Account",userIDList:"MemberList"},response:{MemberList:"members"}}}}(e)),this._configMap.set(ad,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.DELETE_GROUP_MEMBER)}),body:{groupID:void 0,userIDList:void 0,reason:void 0},keyMap:{request:{userIDList:"MemberToDel_Account"}}}}(e)),this._configMap.set(sd,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.GROUP,".").concat(zs.CMD.MODIFY_GROUP_MEMBER_INFO)}),body:{groupID:void 0,userID:void 0,messageRemindType:void 0,nameCard:void 0,role:void 0,memberCustomField:void 0,muteTime:void 0},keyMap:{request:{userID:"Member_Account",memberCustomField:"AppMemberDefinedData",muteTime:"ShutUpTime",messageRemindType:"MsgFlag"}}}}(e)),this._configMap.set(cd,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.IM_OPEN_STAT,".").concat(zs.CMD.TIM_WEB_REPORT_V2)}),body:{header:{},event:[],quality:[]},keyMap:{request:{SDKType:"sdk_type",SDKVersion:"sdk_version",deviceType:"device_type",platform:"platform",instanceID:"instance_id",traceID:"trace_id",SDKAppID:"sdk_app_id",userID:"user_id",tinyID:"tiny_id",extension:"extension",timestamp:"timestamp",networkType:"network_type",eventType:"event_type",code:"error_code",message:"error_message",moreMessage:"more_message",duplicate:"duplicate",costTime:"cost_time",level:"level",qualityType:"quality_type",reportIndex:"report_index",wholePeriod:"whole_period",totalCount:"total_count",rttCount:"success_count_business",successRateOfRequest:"percent_business",countLessThan1Second:"success_count_business",percentOfCountLessThan1Second:"percent_business",countLessThan3Second:"success_count_platform",percentOfCountLessThan3Second:"percent_platform",successCountOfBusiness:"success_count_business",successRateOfBusiness:"percent_business",successCountOfPlatform:"success_count_platform",successRateOfPlatform:"percent_platform",successCountOfMessageReceived:"success_count_business",successRateOfMessageReceived:"percent_business",avgRTT:"average_value",avgDelay:"average_value",avgValue:"average_value"}}}}(n)),this._configMap.set(ld,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.HEARTBEAT,".").concat(zs.CMD.ALIVE)}),body:{}}}(e)),this._configMap.set(dd,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.IM_OPEN_PUSH,".").concat(zs.CMD.MESSAGE_PUSH)}),body:{},keyMap:{response:{C2cMsgArray:"C2CMessageArray",GroupMsgArray:"groupMessageArray",GroupTips:"groupTips",C2cNotifyMsgArray:"C2CNotifyMessageArray",ClientSeq:"clientSequence",MsgPriority:"priority",NoticeSeq:"noticeSequence",MsgContent:"content",MsgType:"type",MsgBody:"elements",ToGroupId:"to",Desc:"description",Ext:"extension",IsSyncMsg:"isSyncMessage",Flag:"needSync",NeedAck:"needAck",PendencyAdd_Account:"userID",ProfileImNick:"nick",PendencyType:"applicationType"}}}}(e)),this._configMap.set(pd,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.OPEN_IM,".").concat(zs.CMD.MESSAGE_PUSH_ACK)}),body:{sessionData:void 0},keyMap:{request:{sessionData:"SessionData"}}}}(e)),this._configMap.set(gd,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.IM_OPEN_STATUS,".").concat(zs.CMD.STATUS_FORCEOFFLINE)}),body:{},keyMap:{response:{C2cNotifyMsgArray:"C2CNotifyMessageArray",NoticeSeq:"noticeSequence",KickoutMsgNotify:"kickoutMsgNotify",NewInstInfo:"newInstanceInfo"}}}}(e)),this._configMap.set(fd,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.IM_LONG_MESSAGE,".").concat(zs.CMD.DOWNLOAD_MERGER_MESSAGE)}),body:{downloadKey:""},keyMap:{response:{Data:"data",Desc:"description",Ext:"extension",Download_Flag:"downloadFlag",ThumbUUID:"thumbUUID",VideoUUID:"videoUUID"}}}}(e)),this._configMap.set(hd,function(e){return{head:xn({},e,{servcmd:"".concat(zs.NAME.IM_LONG_MESSAGE,".").concat(zs.CMD.UPLOAD_MERGER_MESSAGE)}),body:{messageList:[]},keyMap:{request:{fromAccount:"From_Account",toAccount:"To_Account",msgTimeStamp:"MsgTimeStamp",msgSeq:"MsgSeq",msgRandom:"MsgRandom",msgBody:"MsgBody",type:"MsgType",content:"MsgContent",data:"Data",description:"Desc",extension:"Ext",sizeType:"Type",uuid:"UUID",url:"",imageUrl:"URL",fileUrl:"Url",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",videoUrl:"",downloadFlag:"Download_Flag",from:"From_Account",time:"MsgTimeStamp",messageRandom:"MsgRandom",messageSequence:"MsgSeq",elements:"MsgBody",clientSequence:"ClientSeq",payload:"MsgContent",messageList:"MsgList",messageNumber:"MsgNum",abstractList:"AbstractList",messageBody:"MsgBody"}}}}(e))}},{key:"has",value:function(e){return this._configMap.has(e)}},{key:"get",value:function(e){return this._configMap.get(e)}},{key:"update",value:function(){this._fillConfigMap()}},{key:"getKeyMap",value:function(e){return this.has(e)?this.get(e).keyMap||{}:(xi.warn("".concat(this._className,".getKeyMap unknown protocolName:").concat(e)),{})}},{key:"getProtocolData",value:function(e){var t=e.protocolName,n=e.requestData,o=this.get(t),r=null;if(n){var a=this._simpleDeepCopy(o),s=a.body,i=Object.create(null);for(var u in s)if(Object.prototype.hasOwnProperty.call(s,u)){if(i[u]=s[u],void 0===n[u])continue;i[u]=n[u]}a.body=i,r=this._getUplinkData(a)}else r=this._getUplinkData(o);return r}},{key:"_getUplinkData",value:function(e){var t=this._requestDataCleaner(e),n=Su(t.head),o=yv(t.body,this._getRequestKeyMap(n));return t.body=o,t}},{key:"_getRequestKeyMap",value:function(e){var t=this.getKeyMap(e);return xn({},_v.request,{},t.request)}},{key:"_requestDataCleaner",value:function(e){var t=Array.isArray(e)?[]:Object.create(null);for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&Zi(n)&&null!==e[n]&&void 0!==e[n]&&("object"!==wn(e[n])?t[n]=e[n]:t[n]=this._requestDataCleaner.bind(this)(e[n]));return t}},{key:"_simpleDeepCopy",value:function(e){for(var t,n=Object.keys(e),o={},r=0,a=n.length;r<a;r++)t=n[r],Yi(e[t])?o[t]=Array.from(e[t]):Hi(e[t])?o[t]=this._simpleDeepCopy(e[t]):o[t]=e[t];return o}}]),e}(),Lv=[pd],Rv=function(){function e(t){Pn(this,e),this._sessionModule=t,this._className="DownlinkHandler",this._eventHandlerMap=new Map,this._eventHandlerMap.set("C2CMessageArray",this._c2cMessageArrayHandler.bind(this)),this._eventHandlerMap.set("groupMessageArray",this._groupMessageArrayHandler.bind(this)),this._eventHandlerMap.set("groupTips",this._groupTipsHandler.bind(this)),this._eventHandlerMap.set("C2CNotifyMessageArray",this._C2CNotifyMessageArrayHandler.bind(this)),this._eventHandlerMap.set("profileModify",this._profileHandler.bind(this)),this._eventHandlerMap.set("friendListMod",this._relationChainHandler.bind(this)),this._eventHandlerMap.set("recentContactMod",this._recentContactHandler.bind(this)),this._keys=Zn(this._eventHandlerMap.keys())}return Un(e,[{key:"_c2cMessageArrayHandler",value:function(e){var t=this._sessionModule.getModule(Vc);t&&(e.dataList.forEach((function(e){if(1===e.isSyncMessage){var t=e.from;e.from=e.to,e.to=t}})),1===e.needSync&&this._sessionModule.getModule($c).startOnlineSync(),t.onNewC2CMessage({dataList:e.dataList,isInstantMessage:!0}))}},{key:"_groupMessageArrayHandler",value:function(e){var t=this._sessionModule.getModule(Kc);t&&t.onNewGroupMessage({event:e.event,dataList:e.dataList,isInstantMessage:!0})}},{key:"_groupTipsHandler",value:function(e){var t=this._sessionModule.getModule(Kc);if(t){var n=e.event,o=e.dataList,r=e.isInstantMessage,a=void 0===r||r,s=e.isSyncingEnded;switch(n){case 4:case 6:t.onNewGroupTips({event:n,dataList:o});break;case 5:o.forEach((function(e){Yi(e.elements.revokedInfos)?t.onGroupMessageRevoked({dataList:o}):Yi(e.elements.groupMessageReadNotice)?t.onGroupMessageReadNotice({dataList:o}):t.onNewGroupSystemNotice({dataList:o,isInstantMessage:a,isSyncingEnded:s})}));break;case 12:this._sessionModule.getModule(jc).onNewGroupAtTips({dataList:o});break;default:xi.log("".concat(this._className,"._groupTipsHandler unknown event:").concat(n," dataList:"),o)}}}},{key:"_C2CNotifyMessageArrayHandler",value:function(e){var t=this,n=e.dataList;if(Yi(n)){var o=this._sessionModule.getModule(Vc);n.forEach((function(e){if(ji(e))if(e.hasOwnProperty("kickoutMsgNotify")){var r=e.kickoutMsgNotify,a=r.kickType,s=r.newInstanceInfo,i=void 0===s?{}:s;1===a?t._sessionModule.onMultipleAccountKickedOut(i):2===a&&t._sessionModule.onMultipleDeviceKickedOut(i)}else e.hasOwnProperty("c2cMessageRevokedNotify")?o&&o.onC2CMessageRevoked({dataList:n}):e.hasOwnProperty("c2cMessageReadReceipt")?o&&o.onC2CMessageReadReceipt({dataList:n}):e.hasOwnProperty("c2cMessageReadNotice")&&o&&o.onC2CMessageReadNotice({dataList:n})}))}}},{key:"_profileHandler",value:function(e){this._sessionModule.getModule(xc).onProfileModified({dataList:e.dataList});var t=this._sessionModule.getModule(Bc);t&&t.onFriendProfileModified({dataList:e.dataList})}},{key:"_relationChainHandler",value:function(e){this._sessionModule.getModule(xc).onRelationChainModified({dataList:e.dataList});var t=this._sessionModule.getModule(Bc);t&&t.onRelationChainModified({dataList:e.dataList})}},{key:"_recentContactHandler",value:function(e){var t=e.dataList;if(Yi(t)){var n=this._sessionModule.getModule(jc);n&&t.forEach((function(e){var t=e.pushType,o=e.recentContactTopItem,r=e.recentContactDeleteItem;1===t?n.onConversationDeleted(r.recentContactList):2===t?n.onConversationPinned(o.recentContactList):3===t&&n.onConversationUnpinned(o.recentContactList)}))}}},{key:"_cloudControlConfigHandler",value:function(e){this._sessionModule.getModule(ol).onPushedCloudControlConfig(e)}},{key:"onMessage",value:function(e){var t=this,n=e.head,o=e.body;if(this._isPushedCloudControlConfig(n))this._cloudControlConfigHandler(o);else{var r=o.eventArray,a=o.isInstantMessage,s=o.isSyncingEnded,i=o.needSync;if(Yi(r))for(var u=null,c=null,l=0,d=0,p=r.length;d<p;d++){l=(u=r[d]).event;var g=Object.keys(u).find((function(e){return-1!==t._keys.indexOf(e)}));g?(c=u[g],this._eventHandlerMap.get(g)({event:l,dataList:c,isInstantMessage:a,isSyncingEnded:s,needSync:i})):xi.log("".concat(this._className,".onMessage unknown eventItem:").concat(u))}}}},{key:"_isPushedCloudControlConfig",value:function(e){return e.servcmd&&e.servcmd.includes(md)}}]),e}(),bv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="SessionModule",o._platform=o.getPlatform(),o._protocolHandler=new Ov(zn(o)),o._messageDispatcher=new Rv(zn(o)),o}return Un(n,[{key:"updateProtocolConfig",value:function(){this._protocolHandler.update()}},{key:"request",value:function(e){xi.debug("".concat(this._className,".request options:"),e);var t=e.protocolName,n=e.tjgID;if(!this._protocolHandler.has(t))return xi.warn("".concat(this._className,".request unknown protocol:").concat(t)),om({code:vd.CANNOT_FIND_PROTOCOL,message:Ep});var o=this.getProtocolData(e);Au(n)||(o.head.tjgID=n);var r=this.getModule(tl);return Lv.includes(t)?r.simplySend(o):r.send(o)}},{key:"getKeyMap",value:function(e){return this._protocolHandler.getKeyMap(e)}},{key:"genCommonHead",value:function(){var e=this.getModule(Yc);return{ver:"v4",platform:this._platform,websdkappid:Ks,websdkversion:Vs,a2:e.getA2Key()||void 0,tinyid:e.getTinyID()||void 0,status_instid:e.getStatusInstanceID(),sdkappid:e.getSDKAppID(),contenttype:e.getContentType(),reqtime:0,identifier:e.getA2Key()?void 0:e.getUserID(),usersig:e.getA2Key()?void 0:e.getUserSig(),sdkability:2,tjgID:""}}},{key:"genCosSpecifiedHead",value:function(){var e=this.getModule(Yc);return{ver:"v4",platform:this._platform,websdkappid:Ks,websdkversion:Vs,sdkappid:e.getSDKAppID(),contenttype:e.getContentType(),reqtime:0,identifier:e.getUserID(),usersig:e.getUserSig(),status_instid:e.getStatusInstanceID(),sdkability:2}}},{key:"genSSOReportHead",value:function(){var e=this.getModule(Yc);return{ver:"v4",platform:this._platform,websdkappid:Ks,websdkversion:Vs,sdkappid:e.getSDKAppID(),contenttype:"",reqtime:0,identifier:"",usersig:"",status_instid:e.getStatusInstanceID(),sdkability:2}}},{key:"getProtocolData",value:function(e){return this._protocolHandler.getProtocolData(e)}},{key:"onErrorCodeNotZero",value:function(e){var t=e.errorCode;if(t===vd.HELLO_ANSWER_KICKED_OUT){var n=e.kickType,o=e.newInstanceInfo,r=void 0===o?{}:o;1===n?this.onMultipleAccountKickedOut(r):2===n&&this.onMultipleDeviceKickedOut(r)}t!==vd.MESSAGE_A2KEY_EXPIRED&&t!==vd.ACCOUNT_A2KEY_EXPIRED||(this._onUserSigExpired(),this.getModule(tl).reConnect())}},{key:"onMessage",value:function(e){var t=e.body,n=t.needAck,o=void 0===n?0:n,r=t.sessionData;1===o&&this._sendACK(r),this._messageDispatcher.onMessage(e)}},{key:"onReconnected",value:function(){var e=this;this.isLoggedIn()&&this.request({protocolName:ul}).then((function(t){var n=t.data.instanceID;e.getModule(Yc).setStatusInstanceID(n),xi.log("".concat(e._className,".onReconnected, login ok. start to sync unread messages.")),e.getModule($c).startSyncOnReconnected(),e.getModule(al).startPull(),e.getModule(Kc).updateLocalMainSequenceOnReconnected()}))}},{key:"onMultipleAccountKickedOut",value:function(e){this.getModule(Fc).onMultipleAccountKickedOut(e)}},{key:"onMultipleDeviceKickedOut",value:function(e){this.getModule(Fc).onMultipleDeviceKickedOut(e)}},{key:"_onUserSigExpired",value:function(){this.getModule(Fc).onUserSigExpired()}},{key:"_sendACK",value:function(e){this.request({protocolName:pd,requestData:{sessionData:e}})}}]),n}(il),wv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="MessageLossDetectionModule",o._maybeLostSequencesMap=new Map,o}return Un(n,[{key:"onMessageMaybeLost",value:function(e,t,n){this._maybeLostSequencesMap.has(e)||this._maybeLostSequencesMap.set(e,[]);for(var o=this._maybeLostSequencesMap.get(e),r=0;r<n;r++)o.push(t+r);xi.debug("".concat(this._className,".onMessageMaybeLost. maybeLostSequences:").concat(o))}},{key:"detectMessageLoss",value:function(e,t){var n=this._maybeLostSequencesMap.get(e);if(!Au(n)&&!Au(t)){var o=t.filter((function(e){return-1!==n.indexOf(e)}));if(xi.debug("".concat(this._className,".detectMessageLoss. matchedSequences:").concat(o)),n.length===o.length)xi.info("".concat(this._className,".detectMessageLoss no message loss. conversationID:").concat(e));else{var r,a=n.filter((function(e){return-1===o.indexOf(e)})),s=a.length;s<=5?r=e+"-"+a.join("-"):(a.sort((function(e,t){return e-t})),r=e+" start:"+a[0]+" end:"+a[s-1]+" count:"+s),new Kp($g).setMessage(r).setNetworkType(this.getNetworkType()).setLevel("warning").end(),xi.warn("".concat(this._className,".detectMessageLoss message loss detected. conversationID:").concat(e," lostSequences:").concat(a))}n.length=0}}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset")),this._maybeLostSequencesMap.clear()}}]),n}(il),Pv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="CloudControlModule",o._cloudConfig=new Map,o._expiredTime=0,o._version=0,o._isFetching=!1,o}return Un(n,[{key:"getCloudConfig",value:function(e){return Wi(e)?this._cloudConfig:this._cloudConfig.has(e)?this._cloudConfig.get(e):void 0}},{key:"_canFetchConfig",value:function(){return this.isLoggedIn()&&!this._isFetching&&Date.now()>=this._expiredTime}},{key:"fetchConfig",value:function(){var e=this,t=this._canFetchConfig();if(xi.log("".concat(this._className,".fetchConfig canFetchConfig:").concat(t)),t){var n=new Kp(ih),o=this.getModule(Yc).getSDKAppID();this._isFetching=!0,this.request({protocolName:_d,requestData:{SDKAppID:o,version:this._version}}).then((function(t){e._isFetching=!1,n.setMessage("version:".concat(e._version," newVersion:").concat(t.data.version," config:").concat(t.data.cloudControlConfig)).setNetworkType(e.getNetworkType()).end(),xi.log("".concat(e._className,".fetchConfig ok")),e._parseCloudControlConfig(t.data)})).catch((function(t){e._isFetching=!1,e.probeNetwork().then((function(e){var o=Qn(e,2),r=o[0],a=o[1];n.setError(t,r,a).end()})),xi.log("".concat(e._className,".fetchConfig failed. error:"),t),e._setExpiredTimeOnResponseError(12e4)}))}}},{key:"onPushedCloudControlConfig",value:function(e){xi.log("".concat(this._className,".onPushedCloudControlConfig")),new Kp(uh).setNetworkType(this.getNetworkType()).setMessage("newVersion:".concat(e.version," config:").concat(e.cloudControlConfig)).end(),this._parseCloudControlConfig(e)}},{key:"onCheckTimer",value:function(e){this._canFetchConfig()&&this.fetchConfig()}},{key:"_parseCloudControlConfig",value:function(e){var t=this,n="".concat(this._className,"._parseCloudControlConfig"),o=e.errorCode,r=e.errorMessage,a=e.cloudControlConfig,s=e.version,i=e.expiredTime;if(0===o){if(this._version!==s){var u=null;try{u=JSON.parse(a)}catch(Xv){xi.error("".concat(n," JSON parse error:").concat(a))}u&&(this._cloudConfig.clear(),Object.keys(u).forEach((function(e){t._cloudConfig.set(e,u[e])})),this._version=s,this.emitInnerEvent(hm.CLOUD_CONFIG_UPDATED))}this._expiredTime=Date.now()+1e3*i}else Wi(o)?(xi.log("".concat(n," failed. Invalid message format:"),e),this._setExpiredTimeOnResponseError(36e5)):(xi.error("".concat(n," errorCode:").concat(o," errorMessage:").concat(r)),this._setExpiredTimeOnResponseError(12e4))}},{key:"_setExpiredTimeOnResponseError",value:function(e){this._expiredTime=Date.now()+e}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset")),this._cloudConfig.clear(),this._expiredTime=0,this._version=0,this._isFetching=!1}}]),n}(il),Gv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="PullGroupMessageModule",o._remoteLastMessageSequenceMap=new Map,o.PULL_LIMIT_COUNT=15,o}return Un(n,[{key:"startPull",value:function(){var e=this,t=this._getNeedPullConversationList();this._getRemoteLastMessageSequenceList().then((function(){var n=e.getModule(jc);t.forEach((function(t){var o=t.conversationID,r=o.replace(so.CONV_GROUP,""),a=n.getGroupLocalLastMessageSequence(o),s=e._remoteLastMessageSequenceMap.get(r)||0,i=s-a;xi.log("".concat(e._className,".startPull groupID:").concat(r," localLastMessageSequence:").concat(a," ")+"remoteLastMessageSequence:".concat(s," diff:").concat(i)),a>0&&i>=1&&i<300&&e._pullMissingMessage({groupID:r,localLastMessageSequence:a,remoteLastMessageSequence:s,diff:i})}))}))}},{key:"_getNeedPullConversationList",value:function(){return this.getModule(jc).getLocalConversationList().filter((function(e){return e.type===so.CONV_GROUP&&e.groupProfile.type!==so.GRP_AVCHATROOM}))}},{key:"_getRemoteLastMessageSequenceList",value:function(){var e=this;return this.getModule(Kc).getGroupList().then((function(t){for(var n=t.data.groupList,o=void 0===n?[]:n,r=0;r<o.length;r++){var a=o[r],s=a.groupID,i=a.nextMessageSeq;if(a.type!==so.GRP_AVCHATROOM){var u=i-1;e._remoteLastMessageSequenceMap.set(s,u)}}}))}},{key:"_pullMissingMessage",value:function(e){var t=this,n=e.localLastMessageSequence,o=e.remoteLastMessageSequence,r=e.diff;e.count=r>this.PULL_LIMIT_COUNT?this.PULL_LIMIT_COUNT:r,e.sequence=r>this.PULL_LIMIT_COUNT?n+this.PULL_LIMIT_COUNT:n+r,this._getGroupMissingMessage(e).then((function(a){a.length>0&&(a[0].sequence+1<=o&&(e.localLastMessageSequence=n+t.PULL_LIMIT_COUNT,e.diff=r-t.PULL_LIMIT_COUNT,t._pullMissingMessage(e)),t.getModule(Kc).onNewGroupMessage({dataList:a,isInstantMessage:!1}))}))}},{key:"_getGroupMissingMessage",value:function(e){var t=this,n=new Kp(Pg);return this.request({protocolName:Hl,requestData:{groupID:e.groupID,count:e.count,sequence:e.sequence}}).then((function(o){var r=o.data.messageList,a=void 0===r?[]:r;return n.setNetworkType(t.getNetworkType()).setMessage("groupID:".concat(e.groupID," count:").concat(e.count," sequence:").concat(e.sequence," messageList length:").concat(a.length)).end(),a})).catch((function(e){t.probeNetwork().then((function(t){var o=Qn(t,2),r=o[0],a=o[1];n.setError(e,r,a).end()}))}))}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset")),this._remoteLastMessageSequenceMap.clear()}}]),n}(il),Uv=function(){function e(){Pn(this,e),this._className="AvgE2EDelay",this._e2eDelayArray=[]}return Un(e,[{key:"addMessageDelay",value:function(e){var t=Eu(e.currentTime/1e3-e.time,2);this._e2eDelayArray.push(t)}},{key:"_calcAvg",value:function(e,t){if(0===t)return 0;var n=0;return e.forEach((function(e){n+=e})),Eu(n/t,1)}},{key:"_calcTotalCount",value:function(){return this._e2eDelayArray.length}},{key:"_calcCountWithLimit",value:function(e){var t=e.e2eDelayArray,n=e.min,o=e.max;return t.filter((function(e){return n<e&&e<=o})).length}},{key:"_calcPercent",value:function(e,t){var n=Eu(e/t*100,2);return n>100&&(n=100),n}},{key:"_checkE2EDelayException",value:function(e,t){var n=e.filter((function(e){return e>t}));if(n.length>0){var o=n.length,r=Math.min.apply(Math,Zn(n)),a=Math.max.apply(Math,Zn(n)),s=this._calcAvg(n,o),i=Eu(o/e.length*100,2);new Kp(hg).setMessage("message e2e delay exception. count:".concat(o," min:").concat(r," max:").concat(a," avg:").concat(s," percent:").concat(i)).setLevel("warning").end()}}},{key:"getStatResult",value:function(){var e=this._calcTotalCount();if(0===e)return null;var t=Zn(this._e2eDelayArray),n=this._calcCountWithLimit({e2eDelayArray:t,min:0,max:1}),o=this._calcCountWithLimit({e2eDelayArray:t,min:1,max:3}),r=this._calcPercent(n,e),a=this._calcPercent(o,e),s=this._calcAvg(t,e);return this._checkE2EDelayException(t,3),this.reset(),{totalCount:e,countLessThan1Second:n,percentOfCountLessThan1Second:r,countLessThan3Second:o,percentOfCountLessThan3Second:a,avgDelay:s}}},{key:"reset",value:function(){this._e2eDelayArray.length=0}}]),e}(),Fv=function(){function e(){Pn(this,e),this._className="AvgRTT",this._requestCount=0,this._rttArray=[]}return Un(e,[{key:"addRequestCount",value:function(){this._requestCount+=1}},{key:"addRTT",value:function(e){this._rttArray.push(e)}},{key:"_calcTotalCount",value:function(){return this._requestCount}},{key:"_calcRTTCount",value:function(e){return e.length}},{key:"_calcSuccessRateOfRequest",value:function(e,t){if(0===t)return 0;var n=Eu(e/t*100,2);return n>100&&(n=100),n}},{key:"_calcAvg",value:function(e,t){if(0===t)return 0;var n=0;return e.forEach((function(e){n+=e})),parseInt(n/t)}},{key:"_calcMax",value:function(){return Math.max.apply(Math,Zn(this._rttArray))}},{key:"_calcMin",value:function(){return Math.min.apply(Math,Zn(this._rttArray))}},{key:"getStatResult",value:function(){var e=this._calcTotalCount(),t=Zn(this._rttArray);if(0===e)return null;var n=this._calcRTTCount(t),o=this._calcSuccessRateOfRequest(n,e),r=this._calcAvg(t,n);return xi.log("".concat(this._className,".getStatResult max:").concat(this._calcMax()," min:").concat(this._calcMin()," avg:").concat(r)),this.reset(),{totalCount:e,rttCount:n,successRateOfRequest:o,avgRTT:r}}},{key:"reset",value:function(){this._requestCount=0,this._rttArray.length=0}}]),e}(),qv=function(){function e(){Pn(this,e),this._map=new Map}return Un(e,[{key:"initMap",value:function(e){var t=this;e.forEach((function(e){t._map.set(e,{totalCount:0,successCount:0,failedCountOfUserSide:0,costArray:[],fileSizeArray:[]})}))}},{key:"addTotalCount",value:function(e){return!(Wi(e)||!this._map.has(e))&&(this._map.get(e).totalCount+=1,!0)}},{key:"addSuccessCount",value:function(e){return!(Wi(e)||!this._map.has(e))&&(this._map.get(e).successCount+=1,!0)}},{key:"addFailedCountOfUserSide",value:function(e){return!(Wi(e)||!this._map.has(e))&&(this._map.get(e).failedCountOfUserSide+=1,!0)}},{key:"addCost",value:function(e,t){return!(Wi(e)||!this._map.has(e))&&(this._map.get(e).costArray.push(t),!0)}},{key:"addFileSize",value:function(e,t){return!(Wi(e)||!this._map.has(e))&&(this._map.get(e).fileSizeArray.push(t),!0)}},{key:"_calcSuccessRateOfBusiness",value:function(e){if(Wi(e)||!this._map.has(e))return-1;var t=this._map.get(e),n=Eu(t.successCount/t.totalCount*100,2);return n>100&&(n=100),n}},{key:"_calcSuccessRateOfPlatform",value:function(e){if(Wi(e)||!this._map.has(e))return-1;var t=this._map.get(e),n=this._calcSuccessCountOfPlatform(e)/t.totalCount*100;return(n=Eu(n,2))>100&&(n=100),n}},{key:"_calcTotalCount",value:function(e){return Wi(e)||!this._map.has(e)?-1:this._map.get(e).totalCount}},{key:"_calcSuccessCountOfBusiness",value:function(e){return Wi(e)||!this._map.has(e)?-1:this._map.get(e).successCount}},{key:"_calcSuccessCountOfPlatform",value:function(e){if(Wi(e)||!this._map.has(e))return-1;var t=this._map.get(e);return t.successCount+t.failedCountOfUserSide}},{key:"_calcAvg",value:function(e){return Wi(e)||!this._map.has(e)?-1:e===wp?this._calcAvgSpeed(e):this._calcAvgCost(e)}},{key:"_calcAvgCost",value:function(e){var t=this._map.get(e).costArray.length;if(0===t)return 0;var n=0;return this._map.get(e).costArray.forEach((function(e){n+=e})),parseInt(n/t)}},{key:"_calcAvgSpeed",value:function(e){var t=0,n=0;return this._map.get(e).costArray.forEach((function(e){t+=e})),this._map.get(e).fileSizeArray.forEach((function(e){n+=e})),parseInt(1e3*n/t)}},{key:"getStatResult",value:function(e){var t=this._calcTotalCount(e);if(0===t)return null;var n=this._calcSuccessCountOfBusiness(e),o=this._calcSuccessRateOfBusiness(e),r=this._calcSuccessCountOfPlatform(e),a=this._calcSuccessRateOfPlatform(e),s=this._calcAvg(e);return this.reset(e),{totalCount:t,successCountOfBusiness:n,successRateOfBusiness:o,successCountOfPlatform:r,successRateOfPlatform:a,avgValue:s}}},{key:"reset",value:function(e){Wi(e)?this._map.clear():this._map.set(e,{totalCount:0,successCount:0,failedCountOfUserSide:0,costArray:[],fileSizeArray:[]})}}]),e}(),xv=function(){function e(){Pn(this,e),this._lastMap=new Map,this._currentMap=new Map}return Un(e,[{key:"initMap",value:function(e){var t=this;e.forEach((function(e){t._lastMap.set(e,new Map),t._currentMap.set(e,new Map)}))}},{key:"addMessageSequence",value:function(e){var t=e.key,n=e.message;if(Wi(t)||!this._lastMap.has(t)||!this._currentMap.has(t))return!1;var o=n.conversationID,r=n.sequence,a=o.replace(so.CONV_GROUP,"");if(0===this._lastMap.get(t).size)this._addCurrentMap(e);else if(this._lastMap.get(t).has(a)){var s=this._lastMap.get(t).get(a),i=s.length-1;r>s[0]&&r<s[i]?(s.push(r),s.sort(),this._lastMap.get(t).set(a,s)):this._addCurrentMap(e)}else this._addCurrentMap(e);return!0}},{key:"_addCurrentMap",value:function(e){var t=e.key,n=e.message,o=n.conversationID,r=n.sequence,a=o.replace(so.CONV_GROUP,"");this._currentMap.get(t).has(a)||this._currentMap.get(t).set(a,[]),this._currentMap.get(t).get(a).push(r)}},{key:"_copyData",value:function(e){if(!Wi(e)){this._lastMap.set(e,new Map);var t,n=this._lastMap.get(e),o=ro(this._currentMap.get(e));try{for(o.s();!(t=o.n()).done;){var r=Qn(t.value,2),a=r[0],s=r[1];n.set(a,s)}}catch(c){o.e(c)}finally{o.f()}n=null,this._currentMap.set(e,new Map)}}},{key:"getStatResult",value:function(e){if(Wi(this._currentMap.get(e))||Wi(this._lastMap.get(e)))return null;if(0===this._lastMap.get(e).size)return this._copyData(e),null;var t=0,n=0;if(this._lastMap.get(e).forEach((function(e,o){var r=Zn(e.values()),a=r.length,s=r[a-1]-r[0]+1;t+=s,n+=a})),0===t)return null;var o=Eu(n/t*100,2);return o>100&&(o=100),this._copyData(e),{totalCount:t,successCountOfMessageReceived:n,successRateOfMessageReceived:o}}},{key:"reset",value:function(){this._currentMap.clear(),this._lastMap.clear()}}]),e}(),Vv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;Pn(this,n),(o=t.call(this,e))._className="QualityStatModule",o.TAG="im-ssolog-quality-stat",o.reportIndex=0,o.wholePeriod=!1,o._qualityItems=[Ap,Np,Op,Lp,Rp,bp,wp,Pp,Gp,Up],o._messageSentItems=[Op,Lp,Rp,bp,wp],o._messageReceivedItems=[Pp,Gp,Up],o.REPORT_INTERVAL=120,o.REPORT_SDKAPPID_BLACKLIST=[],o.REPORT_TINYID_WHITELIST=[],o._statInfoArr=[],o._avgRTT=new Fv,o._avgE2EDelay=new Uv,o._rateMessageSent=new qv,o._rateMessageReceived=new xv;var r=o.getInnerEmitterInstance();return r.on(hm.CONTEXT_A2KEY_AND_TINYID_UPDATED,o._onLoginSuccess,zn(o)),r.on(hm.CLOUD_CONFIG_UPDATED,o._onCloudConfigUpdated,zn(o)),o}return Un(n,[{key:"_onLoginSuccess",value:function(){var e=this;this._rateMessageSent.initMap(this._messageSentItems),this._rateMessageReceived.initMap(this._messageReceivedItems);var t=this.getModule(Wc),n=t.getItem(this.TAG,!1);!Au(n)&&Ji(n.forEach)&&(xi.log("".concat(this._className,"._onLoginSuccess.get quality stat log in storage, nums=").concat(n.length)),n.forEach((function(t){e._statInfoArr.push(t)})),t.removeItem(this.TAG,!1))}},{key:"_onCloudConfigUpdated",value:function(){var e=this.getCloudConfig("q_rpt_interval"),t=this.getCloudConfig("q_rpt_sdkappid_bl"),n=this.getCloudConfig("q_rpt_tinyid_wl");Wi(e)||(this.REPORT_INTERVAL=Number(e)),Wi(t)||(this.REPORT_SDKAPPID_BLACKLIST=t.split(",").map((function(e){return Number(e)}))),Wi(n)||(this.REPORT_TINYID_WHITELIST=n.split(","))}},{key:"onCheckTimer",value:function(e){this.isLoggedIn()&&e%this.REPORT_INTERVAL==0&&(this.wholePeriod=!0,this._report())}},{key:"addRequestCount",value:function(){this._avgRTT.addRequestCount()}},{key:"addRTT",value:function(e){this._avgRTT.addRTT(e)}},{key:"addMessageDelay",value:function(e){this._avgE2EDelay.addMessageDelay(e)}},{key:"addTotalCount",value:function(e){this._rateMessageSent.addTotalCount(e)||xi.warn("".concat(this._className,".addTotalCount invalid key:"),e)}},{key:"addSuccessCount",value:function(e){this._rateMessageSent.addSuccessCount(e)||xi.warn("".concat(this._className,".addSuccessCount invalid key:"),e)}},{key:"addFailedCountOfUserSide",value:function(e){this._rateMessageSent.addFailedCountOfUserSide(e)||xi.warn("".concat(this._className,".addFailedCountOfUserSide invalid key:"),e)}},{key:"addCost",value:function(e,t){this._rateMessageSent.addCost(e,t)||xi.warn("".concat(this._className,".addCost invalid key or cost:"),e,t)}},{key:"addFileSize",value:function(e,t){this._rateMessageSent.addFileSize(e,t)||xi.warn("".concat(this._className,".addFileSize invalid key or size:"),e,t)}},{key:"addMessageSequence",value:function(e){this._rateMessageReceived.addMessageSequence(e)||xi.warn("".concat(this._className,".addMessageSequence invalid key:"),e.key)}},{key:"_getQualityItem",value:function(e){var t={},n=xp[this.getNetworkType()];Wi(n)&&(n=8);var o={qualityType:Fp[e],timestamp:Pi(),networkType:n,extension:""};switch(e){case Ap:t=this._avgRTT.getStatResult();break;case Np:t=this._avgE2EDelay.getStatResult();break;case Op:case Lp:case Rp:case bp:case wp:t=this._rateMessageSent.getStatResult(e);break;case Pp:case Gp:case Up:t=this._rateMessageReceived.getStatResult(e)}return null===t?null:xn({},o,{},t)}},{key:"_report",value:function(e){var t=this,n=[],o=null;Wi(e)?this._qualityItems.forEach((function(e){null!==(o=t._getQualityItem(e))&&(o.reportIndex=t.reportIndex,o.wholePeriod=t.wholePeriod,n.push(o))})):null!==(o=this._getQualityItem(e))&&(o.reportIndex=this.reportIndex,o.wholePeriod=this.wholePeriod,n.push(o)),xi.debug("".concat(this._className,"._report"),n),this._statInfoArr.length>0&&(n=n.concat(this._statInfoArr),this._statInfoArr=[]);var r=this.getModule(Yc),a=r.getSDKAppID(),s=r.getTinyID();Du(this.REPORT_SDKAPPID_BLACKLIST,a)&&!ku(this.REPORT_TINYID_WHITELIST,s)&&(n=[]),n.length>0&&this._doReport(n)}},{key:"_doReport",value:function(e){var t=this,n={header:nv(this),quality:e};this.request({protocolName:cd,requestData:xn({},n)}).then((function(){t.reportIndex++,t.wholePeriod=!1})).catch((function(n){xi.warn("".concat(t._className,"._doReport, online:").concat(t.getNetworkType()," error:"),n),t._statInfoArr=t._statInfoArr.concat(e),t._flushAtOnce()}))}},{key:"_flushAtOnce",value:function(){var e=this.getModule(Wc),t=e.getItem(this.TAG,!1),n=this._statInfoArr;if(Au(t))xi.log("".concat(this._className,"._flushAtOnce count:").concat(n.length)),e.setItem(this.TAG,n,!0,!1);else{var o=n.concat(t);o.length>10&&(o=o.slice(0,10)),xi.log("".concat(this.className,"._flushAtOnce count:").concat(o.length)),e.setItem(this.TAG,o,!0,!1)}this._statInfoArr=[]}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset")),this._report(),this.reportIndex=0,this.wholePeriod=!1,this.REPORT_SDKAPPID_BLACKLIST=[],this.REPORT_TINYID_WHITELIST=[],this._avgRTT.reset(),this._avgE2EDelay.reset(),this._rateMessageSent.reset(),this._rateMessageReceived.reset()}}]),n}(il),Kv=function(e){Vn(n,e);var t=Xn(n);function n(e){var o;return Pn(this,n),(o=t.call(this,e))._className="WorkerModule",o._isWorkerEnabled=!1,o._workerTimer=null,o._init(),o.getInnerEmitterInstance().on(hm.CLOUD_CONFIG_UPDATED,o._onCloudConfigUpdated,zn(o)),o}return Un(n,[{key:"isWorkerEnabled",value:function(){return this._isWorkerEnabled&&Ti&&this._workerTimer}},{key:"startWorkerTimer",value:function(){xi.log("".concat(this._className,".startWorkerTimer")),this._workerTimer&&this._workerTimer.postMessage("start")}},{key:"stopWorkerTimer",value:function(){xi.log("".concat(this._className,".stopWorkerTimer")),this._workerTimer&&this._workerTimer.postMessage("stop")}},{key:"_init",value:function(){if(Ti){var e=URL.createObjectURL(new Blob(['let interval = -1;onmessage = function(event) {  if (event.data === "start") {    if (interval > 0) {      clearInterval(interval);    }    interval = setInterval(() => {      postMessage("");    }, 1000)  } else if (event.data === "stop") {    clearInterval(interval);    interval = -1;  }};'],{type:"application/javascript; charset=utf-8"}));this._workerTimer=new Worker(e);var t=this;this._workerTimer.onmessage=function(){t._moduleManager.onCheckTimer()}}}},{key:"_onCloudConfigUpdated",value:function(){"1"===this.getCloudConfig("enable_worker")?!this._isWorkerEnabled&&Ti&&(this._isWorkerEnabled=!0,this.startWorkerTimer(),this._moduleManager.onWorkerTimerEnabled()):this._isWorkerEnabled&&Ti&&(this._isWorkerEnabled=!1,this.stopWorkerTimer(),this._moduleManager.onWorkerTimerDisabled())}},{key:"terminate",value:function(){xi.log("".concat(this._className,".terminate")),this._workerTimer&&(this._workerTimer.terminate(),this._workerTimer=null)}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset"))}}]),n}(il),Bv=function(){function e(t){Pn(this,e);var n=new Kp(Bp);this._className="ModuleManager",this._isReady=!1,this._startLoginTs=0,this._moduleMap=new Map,this._innerEmitter=null,this._outerEmitter=null,this._checkCount=0,this._checkTimer=-1,this._moduleMap.set(Yc,new Qm(this,t)),this._moduleMap.set(ol,new Pv(this)),this._moduleMap.set(rl,new Kv(this)),this._moduleMap.set(sl,new Vv(this)),this._moduleMap.set(tl,new Nv(this)),this._moduleMap.set(el,new bv(this)),this._moduleMap.set(Fc,new Zm(this)),this._moduleMap.set(qc,new gv(this)),this._moduleMap.set(xc,new Xm(this)),this._moduleMap.set(Vc,new rm(this)),this._moduleMap.set(jc,new Om(this)),this._moduleMap.set(Kc,new Hm(this)),this._moduleMap.set(Hc,new Ym(this)),this._moduleMap.set(Wc,new ev(this)),this._moduleMap.set(zc,new ov(this)),this._moduleMap.set(Jc,new sv(this)),this._moduleMap.set(Xc,new uv(this)),this._moduleMap.set(Qc,new cv(this)),this._moduleMap.set(Zc,new hv(this)),this._moduleMap.set($c,new fv(this)),this._moduleMap.set(nl,new wv(this)),this._moduleMap.set(al,new Gv(this));var o=t.instanceID,r=t.oversea,a=t.SDKAppID,s="instanceID:".concat(o," oversea:").concat(r," workerAvailable:").concat(Ti," host:").concat(yu()," ")+"inBrowser:".concat(ai," inMiniApp:").concat(ri," SDKAppID:").concat(a," UserAgent:").concat(ui);Kp.bindEventStatModule(this._moduleMap.get(zc)),n.setMessage("".concat(s)).end(),xi.info("SDK ".concat(s)),this._readyList=void 0,this._ssoLogForReady=null,this._initReadyList()}return Un(e,[{key:"_startTimer",value:function(){var e=this._moduleMap.get(rl).isWorkerEnabled();xi.log("".concat(this._className,".startTimer isWorkerEnabled:").concat(e," seed:").concat(this._checkTimer)),e?this._moduleMap.get(rl).startWorkerTimer():this._startMainThreadTimer()}},{key:"_startMainThreadTimer",value:function(){xi.log("".concat(this._className,"._startMainThreadTimer")),this._checkTimer<0&&(this._checkTimer=setInterval(this.onCheckTimer.bind(this),1e3))}},{key:"stopTimer",value:function(){var e=this._moduleMap.get(rl).isWorkerEnabled();xi.log("".concat(this._className,".stopTimer isWorkerEnabled:").concat(e," seed:").concat(this._checkTimer)),e?this._moduleMap.get(rl).stopWorkerTimer():this._stopMainThreadTimer()}},{key:"_stopMainThreadTimer",value:function(){xi.log("".concat(this._className,"._stopMainThreadTimer")),this._checkTimer>0&&(clearInterval(this._checkTimer),this._checkTimer=-1,this._checkCount=0)}},{key:"onWorkerTimerEnabled",value:function(){xi.log("".concat(this._className,".onWorkerTimerEnabled, disable main thread timer")),this._stopMainThreadTimer()}},{key:"onWorkerTimerDisabled",value:function(){xi.log("".concat(this._className,".onWorkerTimerDisabled, enable main thread timer")),this._startMainThreadTimer()}},{key:"onCheckTimer",value:function(){this._checkCount+=1;var e,t=ro(this._moduleMap);try{for(t.s();!(e=t.n()).done;){var n=Qn(e.value,2)[1];n.onCheckTimer&&n.onCheckTimer(this._checkCount)}}catch(r){t.e(r)}finally{t.f()}}},{key:"_initReadyList",value:function(){var e=this;this._readyList=[this._moduleMap.get(Fc),this._moduleMap.get(jc)],this._readyList.forEach((function(t){t.ready((function(){return e._onModuleReady()}))}))}},{key:"_onModuleReady",value:function(){var e=!0;if(this._readyList.forEach((function(t){t.isReady()||(e=!1)})),e&&!this._isReady){this._isReady=!0,this._outerEmitter.emit(ao.SDK_READY);var t=Date.now()-this._startLoginTs;xi.warn("SDK is ready. cost ".concat(t," ms")),this._startLoginTs=Date.now();var n=this._moduleMap.get(Jc).getNetworkType(),o=this._ssoLogForReady.getStartTs()+wi;this._ssoLogForReady.setNetworkType(n).setMessage(t).start(o).end()}}},{key:"login",value:function(){0===this._startLoginTs&&(Gi(),this._startLoginTs=Date.now(),this._startTimer(),this._moduleMap.get(Jc).start(),this._ssoLogForReady=new Kp(Hp))}},{key:"onLoginFailed",value:function(){this._startLoginTs=0}},{key:"getOuterEmitterInstance",value:function(){return null===this._outerEmitter&&(this._outerEmitter=new iv,tm(this._outerEmitter),this._outerEmitter._emit=this._outerEmitter.emit,this._outerEmitter.emit=function(e,t){var n=arguments[0],o=[n,{name:arguments[0],data:arguments[1]}];this._outerEmitter._emit.apply(this._outerEmitter,o)}.bind(this)),this._outerEmitter}},{key:"getInnerEmitterInstance",value:function(){return null===this._innerEmitter&&(this._innerEmitter=new iv,this._innerEmitter._emit=this._innerEmitter.emit,this._innerEmitter.emit=function(e,t){var n;ji(arguments[1])&&arguments[1].data?(xi.warn("inner eventData has data property, please check!"),n=[e,{name:arguments[0],data:arguments[1].data}]):n=[e,{name:arguments[0],data:arguments[1]}],this._innerEmitter._emit.apply(this._innerEmitter,n)}.bind(this)),this._innerEmitter}},{key:"hasModule",value:function(e){return this._moduleMap.has(e)}},{key:"getModule",value:function(e){return this._moduleMap.get(e)}},{key:"isReady",value:function(){return this._isReady}},{key:"onError",value:function(e){xi.warn("Oops! code:".concat(e.code," message:").concat(e.message)),new Kp(ch).setMessage("code:".concat(e.code," message:").concat(e.message)).setNetworkType(this.getModule(Jc).getNetworkType()).setLevel("error").end(),this.getOuterEmitterInstance().emit(ao.ERROR,e)}},{key:"reset",value:function(){xi.log("".concat(this._className,".reset")),Gi();var e,t=ro(this._moduleMap);try{for(t.s();!(e=t.n()).done;){var n=Qn(e.value,2)[1];n.reset&&n.reset()}}catch(r){t.e(r)}finally{t.f()}this._startLoginTs=0,this._initReadyList(),this._isReady=!1,this.stopTimer(),this._outerEmitter.emit(ao.SDK_NOT_READY)}}]),e}(),Hv=function(){function e(){Pn(this,e),this._funcMap=new Map}return Un(e,[{key:"defense",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;if("string"!=typeof e)return null;if(0===e.length)return null;if("function"!=typeof t)return null;if(this._funcMap.has(e)&&this._funcMap.get(e).has(t))return this._funcMap.get(e).get(t);this._funcMap.has(e)||this._funcMap.set(e,new Map);var o=null;return this._funcMap.get(e).has(t)?o=this._funcMap.get(e).get(t):(o=this._pack(e,t,n),this._funcMap.get(e).set(t,o)),o}},{key:"defenseOnce",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;return"function"!=typeof t?null:this._pack(e,t,n)}},{key:"find",value:function(e,t){return"string"!=typeof e||0===e.length||"function"!=typeof t?null:this._funcMap.has(e)?this._funcMap.get(e).has(t)?this._funcMap.get(e).get(t):(xi.log("SafetyCallback.find: 找不到 func —— ".concat(e,"/").concat(""!==t.name?t.name:"[anonymous]")),null):(xi.log("SafetyCallback.find: 找不到 eventName-".concat(e," 对应的 func")),null)}},{key:"delete",value:function(e,t){return"function"==typeof t&&!!this._funcMap.has(e)&&!!this._funcMap.get(e).has(t)&&(this._funcMap.get(e).delete(t),0===this._funcMap.get(e).size&&this._funcMap.delete(e),!0)}},{key:"_pack",value:function(e,t,n){return function(){try{t.apply(n,Array.from(arguments))}catch(u){var o=Object.values(ao).indexOf(e);if(-1!==o){var r=Object.keys(ao)[o];xi.warn("接入侧事件 TIM.EVENT.".concat(r," 对应的回调函数逻辑存在问题，请检查！"),u)}var a=new Kp(sh);a.setMessage("eventName:".concat(e)).setMoreMessage(u.message).end()}}}}]),e}(),jv=function(){function e(t){Pn(this,e);var n={SDKAppID:t.SDKAppID,unlimitedAVChatRoom:t.unlimitedAVChatRoom||!1,scene:t.scene||"",oversea:t.oversea||!1,instanceID:Mu()};this._moduleManager=new Bv(n),this._safetyCallbackFactory=new Hv}return Un(e,[{key:"isReady",value:function(){return this._moduleManager.isReady()}},{key:"onError",value:function(e){this._moduleManager.onError(e)}},{key:"login",value:function(e){return this._moduleManager.login(),this._moduleManager.getModule(Fc).login(e)}},{key:"logout",value:function(){var e=this;return this._moduleManager.getModule(Fc).logout().then((function(t){return e._moduleManager.reset(),t}))}},{key:"destroy",value:function(){var e=this;return this.logout().finally((function(){e._moduleManager.stopTimer(),e._moduleManager.getModule(rl).terminate(),e._moduleManager.getModule(tl).dealloc();var t=e._moduleManager.getOuterEmitterInstance(),n=e._moduleManager.getModule(Yc);t.emit(ao.SDK_DESTROY,{SDKAppID:n.getSDKAppID()})}))}},{key:"on",value:function(e,t,n){e===ao.GROUP_SYSTEM_NOTICE_RECEIVED&&xi.warn("！！！TIM.EVENT.GROUP_SYSTEM_NOTICE_RECEIVED v2.6.0起弃用，为了更好的体验，请在 TIM.EVENT.MESSAGE_RECEIVED 事件回调内接收处理群系统通知，详细请参考：https://web.sdk.qcloud.com/im/doc/zh-cn/Message.html#.GroupSystemNoticePayload"),xi.debug("on","eventName:".concat(e)),this._moduleManager.getOuterEmitterInstance().on(e,this._safetyCallbackFactory.defense(e,t,n),n)}},{key:"once",value:function(e,t,n){xi.debug("once","eventName:".concat(e)),this._moduleManager.getOuterEmitterInstance().once(e,this._safetyCallbackFactory.defenseOnce(e,t,n),n||this)}},{key:"off",value:function(e,t,n,o){xi.debug("off","eventName:".concat(e));var r=this._safetyCallbackFactory.find(e,t);null!==r&&(this._moduleManager.getOuterEmitterInstance().off(e,r,n,o),this._safetyCallbackFactory.delete(e,t))}},{key:"registerPlugin",value:function(e){this._moduleManager.getModule(Zc).registerPlugin(e)}},{key:"setLogLevel",value:function(e){xi.setLevel(e)}},{key:"createTextMessage",value:function(e){return this._moduleManager.getModule(qc).createTextMessage(e)}},{key:"createTextAtMessage",value:function(e){return this._moduleManager.getModule(qc).createTextMessage(e)}},{key:"createImageMessage",value:function(e){return this._moduleManager.getModule(qc).createImageMessage(e)}},{key:"createAudioMessage",value:function(e){return this._moduleManager.getModule(qc).createAudioMessage(e)}},{key:"createVideoMessage",value:function(e){return this._moduleManager.getModule(qc).createVideoMessage(e)}},{key:"createCustomMessage",value:function(e){return this._moduleManager.getModule(qc).createCustomMessage(e)}},{key:"createFaceMessage",value:function(e){return this._moduleManager.getModule(qc).createFaceMessage(e)}},{key:"createFileMessage",value:function(e){return this._moduleManager.getModule(qc).createFileMessage(e)}},{key:"createLocationMessage",value:function(e){return this._moduleManager.getModule(qc).createLocationMessage(e)}},{key:"createMergerMessage",value:function(e){return this._moduleManager.getModule(qc).createMergerMessage(e)}},{key:"downloadMergerMessage",value:function(e){return e.type!==so.MSG_MERGER?om(new $_({code:vd.MESSAGE_MERGER_TYPE_INVALID,message:Wd})):Au(e.payload.downloadKey)?om(new $_({code:vd.MESSAGE_MERGER_KEY_INVALID,message:zd})):this._moduleManager.getModule(qc).downloadMergerMessage(e).catch((function(e){return om(new $_({code:vd.MESSAGE_MERGER_DOWNLOAD_FAIL,message:Jd}))}))}},{key:"createForwardMessage",value:function(e){return this._moduleManager.getModule(qc).createForwardMessage(e)}},{key:"sendMessage",value:function(e,t){return e instanceof Y_?this._moduleManager.getModule(qc).sendMessageInstance(e,t):om(new $_({code:vd.MESSAGE_SEND_NEED_MESSAGE_INSTANCE,message:Nd}))}},{key:"callExperimentalAPI",value:function(e,t){return"handleGroupInvitation"===e?this._moduleManager.getModule(Kc).handleGroupInvitation(t):om(new $_({code:vd.INVALID_OPERATION,message:Sp}))}},{key:"revokeMessage",value:function(e){return this._moduleManager.getModule(qc).revokeMessage(e)}},{key:"resendMessage",value:function(e){return this._moduleManager.getModule(qc).resendMessage(e)}},{key:"deleteMessage",value:function(e){return this._moduleManager.getModule(qc).deleteMessage(e)}},{key:"getMessageList",value:function(e){return this._moduleManager.getModule(jc).getMessageList(e)}},{key:"setMessageRead",value:function(e){return this._moduleManager.getModule(jc).setMessageRead(e)}},{key:"getConversationList",value:function(e){return this._moduleManager.getModule(jc).getConversationList(e)}},{key:"getConversationProfile",value:function(e){return this._moduleManager.getModule(jc).getConversationProfile(e)}},{key:"deleteConversation",value:function(e){return this._moduleManager.getModule(jc).deleteConversation(e)}},{key:"pinConversation",value:function(e){return this._moduleManager.getModule(jc).pinConversation(e)}},{key:"getMyProfile",value:function(){return this._moduleManager.getModule(xc).getMyProfile()}},{key:"getUserProfile",value:function(e){return this._moduleManager.getModule(xc).getUserProfile(e)}},{key:"updateMyProfile",value:function(e){return this._moduleManager.getModule(xc).updateMyProfile(e)}},{key:"getBlacklist",value:function(){return this._moduleManager.getModule(xc).getLocalBlacklist()}},{key:"addToBlacklist",value:function(e){return this._moduleManager.getModule(xc).addBlacklist(e)}},{key:"removeFromBlacklist",value:function(e){return this._moduleManager.getModule(xc).deleteBlacklist(e)}},{key:"getFriendList",value:function(){var e=this._moduleManager.getModule(Bc);return e?e.getLocalFriendList():om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"addFriend",value:function(e){var t=this._moduleManager.getModule(Bc);return t?t.addFriend(e):om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"deleteFriend",value:function(e){var t=this._moduleManager.getModule(Bc);return t?t.deleteFriend(e):om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"checkFriend",value:function(e){var t=this._moduleManager.getModule(Bc);return t?t.checkFriend(e):om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"getFriendProfile",value:function(e){var t=this._moduleManager.getModule(Bc);return t?t.getFriendProfile(e):om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"updateFriend",value:function(e){var t=this._moduleManager.getModule(Bc);return t?t.updateFriend(e):om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"getFriendApplicationList",value:function(){var e=this._moduleManager.getModule(Bc);return e?e.getLocalFriendApplicationList():om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"acceptFriendApplication",value:function(e){var t=this._moduleManager.getModule(Bc);return t?t.acceptFriendApplication(e):om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"refuseFriendApplication",value:function(e){var t=this._moduleManager.getModule(Bc);return t?t.refuseFriendApplication(e):om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"deleteFriendApplication",value:function(e){var t=this._moduleManager.getModule(Bc);return t?t.deleteFriendApplication(e):om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"setFriendApplicationRead",value:function(){var e=this._moduleManager.getModule(Bc);return e?e.setFriendApplicationRead():om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"getFriendGroupList",value:function(){var e=this._moduleManager.getModule(Bc);return e?e.getLocalFriendGroupList():om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"createFriendGroup",value:function(e){var t=this._moduleManager.getModule(Bc);return t?t.createFriendGroup(e):om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"deleteFriendGroup",value:function(e){var t=this._moduleManager.getModule(Bc);return t?t.deleteFriendGroup(e):om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"addToFriendGroup",value:function(e){var t=this._moduleManager.getModule(Bc);return t?t.addToFriendGroup(e):om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"removeFromFriendGroup",value:function(e){var t=this._moduleManager.getModule(Bc);return t?t.removeFromFriendGroup(e):om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"renameFriendGroup",value:function(e){var t=this._moduleManager.getModule(Bc);return t?t.renameFriendGroup(e):om({code:vd.CANNOT_FIND_MODULE,message:Dp})}},{key:"getGroupList",value:function(e){return this._moduleManager.getModule(Kc).getGroupList(e)}},{key:"getGroupProfile",value:function(e){return this._moduleManager.getModule(Kc).getGroupProfile(e)}},{key:"createGroup",value:function(e){return this._moduleManager.getModule(Kc).createGroup(e)}},{key:"dismissGroup",value:function(e){return this._moduleManager.getModule(Kc).dismissGroup(e)}},{key:"updateGroupProfile",value:function(e){return this._moduleManager.getModule(Kc).updateGroupProfile(e)}},{key:"joinGroup",value:function(e){return this._moduleManager.getModule(Kc).joinGroup(e)}},{key:"quitGroup",value:function(e){return this._moduleManager.getModule(Kc).quitGroup(e)}},{key:"searchGroupByID",value:function(e){return this._moduleManager.getModule(Kc).searchGroupByID(e)}},{key:"getGroupOnlineMemberCount",value:function(e){return this._moduleManager.getModule(Kc).getGroupOnlineMemberCount(e)}},{key:"changeGroupOwner",value:function(e){return this._moduleManager.getModule(Kc).changeGroupOwner(e)}},{key:"handleGroupApplication",value:function(e){return this._moduleManager.getModule(Kc).handleGroupApplication(e)}},{key:"initGroupAttributes",value:function(e){return this._moduleManager.getModule(Kc).initGroupAttributes(e)}},{key:"setGroupAttributes",value:function(e){return this._moduleManager.getModule(Kc).setGroupAttributes(e)}},{key:"deleteGroupAttributes",value:function(e){return this._moduleManager.getModule(Kc).deleteGroupAttributes(e)}},{key:"getGroupAttributes",value:function(e){return this._moduleManager.getModule(Kc).getGroupAttributes(e)}},{key:"getGroupMemberList",value:function(e){return this._moduleManager.getModule(Hc).getGroupMemberList(e)}},{key:"getGroupMemberProfile",value:function(e){return this._moduleManager.getModule(Hc).getGroupMemberProfile(e)}},{key:"addGroupMember",value:function(e){return this._moduleManager.getModule(Hc).addGroupMember(e)}},{key:"deleteGroupMember",value:function(e){return this._moduleManager.getModule(Hc).deleteGroupMember(e)}},{key:"setGroupMemberMuteTime",value:function(e){return this._moduleManager.getModule(Hc).setGroupMemberMuteTime(e)}},{key:"setGroupMemberRole",value:function(e){return this._moduleManager.getModule(Hc).setGroupMemberRole(e)}},{key:"setGroupMemberNameCard",value:function(e){return this._moduleManager.getModule(Hc).setGroupMemberNameCard(e)}},{key:"setGroupMemberCustomField",value:function(e){return this._moduleManager.getModule(Hc).setGroupMemberCustomField(e)}},{key:"setMessageRemindType",value:function(e){return this._moduleManager.getModule(Hc).setMessageRemindType(e)}}]),e}(),Yv={login:"login",logout:"logout",destroy:"destroy",on:"on",off:"off",ready:"ready",setLogLevel:"setLogLevel",joinGroup:"joinGroup",quitGroup:"quitGroup",registerPlugin:"registerPlugin",getGroupOnlineMemberCount:"getGroupOnlineMemberCount"};function Wv(e,t){if(e.isReady()||void 0!==Yv[t])return!0;var n=new $_({code:vd.SDK_IS_NOT_READY,message:"".concat(t," ").concat(kp,"，请参考 https://web.sdk.qcloud.com/im/doc/zh-cn/module-EVENT.html#.SDK_READY")});return e.onError(n),!1}var zv={},Jv={create:function(e){var t=0;if(Ki(e.SDKAppID))t=e.SDKAppID;else if(xi.warn("TIM.create SDKAppID 的类型应该为 Number，请修改！"),t=parseInt(e.SDKAppID),isNaN(t))return xi.error("TIM.create failed. 解析 SDKAppID 失败，请检查传参！"),null;if(t&&zv[t])return zv[t];xi.log("TIM.create");var n=new jv(xn({},e,{SDKAppID:t}));n.on(ao.SDK_DESTROY,(function(e){zv[e.data.SDKAppID]=null,delete zv[e.data.SDKAppID]}));var o=function(e){var t=Object.create(null);return Object.keys(Nc).forEach((function(n){if(e[n]){var o=Nc[n],r=new fo;t[o]=function(){var t=Array.from(arguments);return r.use((function(t,o){return Wv(e,n)?o():om(new $_({code:vd.SDK_IS_NOT_READY,message:"".concat(n," ").concat(kp,"。")}))})).use((function(e,t){if(!0===Nu(e,Ac[n],o))return t()})).use((function(t,o){return e[n].apply(e,t)})),r.run(t)}}})),t}(n);return zv[t]=o,xi.log("TIM.create ok"),o}};return Jv.TYPES=so,Jv.EVENT=ao,Jv.VERSION="2.15.0",xi.log("TIM.VERSION: ".concat(Jv.VERSION)),Jv}))}).call(this,n("2409"))}}]);
(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0f0662"],{"9bcf":function(e,t,n){(function(t){!function(t,n){e.exports=n()}(0,(function(){var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof t?t:"undefined"!=typeof self?self:{};function n(e,t){return e(t={exports:{}},t.exports),t.exports}var r=function(e){return e&&e.Math==Math&&e},o=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof e&&e)||Function("return this")(),i=function(e){try{return!!e()}catch(n){return!0}},s=!i((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),a={}.propertyIsEnumerable,u=Object.getOwnPropertyDescriptor,c={f:u&&!a.call({1:2},1)?function(e){var t=u(this,e);return!!t&&t.enumerable}:a},l=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},p={}.toString,f=function(e){return p.call(e).slice(8,-1)},h="".split,d=i((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==f(e)?h.call(e,""):Object(e)}:Object,g=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},m=function(e){return d(g(e))},v=function(e){return"object"==typeof e?null!==e:"function"==typeof e},y=function(e,t){if(!v(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!v(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!v(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!v(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")},_={}.hasOwnProperty,C=function(e,t){return _.call(e,t)},I=o.document,M=v(I)&&v(I.createElement),S=function(e){return M?I.createElement(e):{}},T=!s&&!i((function(){return 7!=Object.defineProperty(S("div"),"a",{get:function(){return 7}}).a})),E=Object.getOwnPropertyDescriptor,D={f:s?E:function(e,t){if(e=m(e),t=y(t,!0),T)try{return E(e,t)}catch(r){}if(C(e,t))return l(!c.f.call(e,t),e[t])}},k=function(e){if(!v(e))throw TypeError(String(e)+" is not an object");return e},w=Object.defineProperty,A={f:s?w:function(e,t,n){if(k(e),t=y(t,!0),k(n),T)try{return w(e,t,n)}catch(o){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},b=s?function(e,t,n){return A.f(e,t,l(1,n))}:function(e,t,n){return e[t]=n,e},R=function(e,t){try{b(o,e,t)}catch(r){o[e]=t}return t},O=o["__core-js_shared__"]||R("__core-js_shared__",{}),L=Function.toString;"function"!=typeof O.inspectSource&&(O.inspectSource=function(e){return L.call(e)});var N,P,G,x=O.inspectSource,U=o.WeakMap,q="function"==typeof U&&/native code/.test(x(U)),F=n((function(e){(e.exports=function(e,t){return O[e]||(O[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.6.4",mode:"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})})),j=0,B=Math.random(),H=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++j+B).toString(36)},V=F("keys"),K=function(e){return V[e]||(V[e]=H(e))},$={},Y=o.WeakMap;if(q){var z=new Y,W=z.get,X=z.has,J=z.set;N=function(e,t){return J.call(z,e,t),t},P=function(e){return W.call(z,e)||{}},G=function(e){return X.call(z,e)}}else{var Q=K("state");$[Q]=!0,N=function(e,t){return b(e,Q,t),t},P=function(e){return C(e,Q)?e[Q]:{}},G=function(e){return C(e,Q)}}var Z={set:N,get:P,has:G,enforce:function(e){return G(e)?P(e):N(e,{})},getterFor:function(e){return function(t){var n;if(!v(t)||(n=P(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return n}}},ee=n((function(e){var t=Z.get,n=Z.enforce,r=String(String).split("String");(e.exports=function(e,t,i,s){var a=!!s&&!!s.unsafe,u=!!s&&!!s.enumerable,c=!!s&&!!s.noTargetGet;"function"==typeof i&&("string"!=typeof t||C(i,"name")||b(i,"name",t),n(i).source=r.join("string"==typeof t?t:"")),e!==o?(a?!c&&e[t]&&(u=!0):delete e[t],u?e[t]=i:b(e,t,i)):u?e[t]=i:R(t,i)})(Function.prototype,"toString",(function(){return"function"==typeof this&&t(this).source||x(this)}))})),te=o,ne=function(e){return"function"==typeof e?e:void 0},re=function(e,t){return arguments.length<2?ne(te[e])||ne(o[e]):te[e]&&te[e][t]||o[e]&&o[e][t]},oe=Math.ceil,ie=Math.floor,se=function(e){return isNaN(e=+e)?0:(e>0?ie:oe)(e)},ae=Math.min,ue=function(e){return e>0?ae(se(e),9007199254740991):0},ce=Math.max,le=Math.min,pe=function(e,t){var n=se(e);return n<0?ce(n+t,0):le(n,t)},fe=function(e){return function(t,n,r){var o,i=m(t),s=ue(i.length),a=pe(r,s);if(e&&n!=n){for(;s>a;)if((o=i[a++])!=o)return!0}else for(;s>a;a++)if((e||a in i)&&i[a]===n)return e||a||0;return!e&&-1}},he={includes:fe(!0),indexOf:fe(!1)},de=he.indexOf,ge=function(e,t){var n,r=m(e),o=0,i=[];for(n in r)!C($,n)&&C(r,n)&&i.push(n);for(;t.length>o;)C(r,n=t[o++])&&(~de(i,n)||i.push(n));return i},me=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],ve=me.concat("length","prototype"),ye={f:Object.getOwnPropertyNames||function(e){return ge(e,ve)}},_e={f:Object.getOwnPropertySymbols},Ce=re("Reflect","ownKeys")||function(e){var t=ye.f(k(e)),n=_e.f;return n?t.concat(n(e)):t},Ie=function(e,t){for(var n=Ce(t),r=A.f,o=D.f,i=0;i<n.length;i++){var s=n[i];C(e,s)||r(e,s,o(t,s))}},Me=/#|\.prototype\./,Se=function(e,t){var n=Ee[Te(e)];return n==ke||n!=De&&("function"==typeof t?i(t):!!t)},Te=Se.normalize=function(e){return String(e).replace(Me,".").toLowerCase()},Ee=Se.data={},De=Se.NATIVE="N",ke=Se.POLYFILL="P",we=Se,Ae=D.f,be=function(e,t){var n,r,i,s,a,u=e.target,c=e.global,l=e.stat;if(n=c?o:l?o[u]||R(u,{}):(o[u]||{}).prototype)for(r in t){if(s=t[r],i=e.noTargetGet?(a=Ae(n,r))&&a.value:n[r],!we(c?r:u+(l?".":"#")+r,e.forced)&&void 0!==i){if(typeof s==typeof i)continue;Ie(s,i)}(e.sham||i&&i.sham)&&b(s,"sham",!0),ee(n,r,s,e)}},Re=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e},Oe=function(e,t,n){if(Re(e),void 0===t)return e;switch(n){case 0:return function(){return e.call(t)};case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,o){return e.call(t,n,r,o)}}return function(){return e.apply(t,arguments)}},Le=function(e){return Object(g(e))},Ne=Array.isArray||function(e){return"Array"==f(e)},Pe=!!Object.getOwnPropertySymbols&&!i((function(){return!String(Symbol())})),Ge=Pe&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,xe=F("wks"),Ue=o.Symbol,qe=Ge?Ue:Ue&&Ue.withoutSetter||H,Fe=function(e){return C(xe,e)||(Pe&&C(Ue,e)?xe[e]=Ue[e]:xe[e]=qe("Symbol."+e)),xe[e]},je=Fe("species"),Be=function(e,t){var n;return Ne(e)&&("function"!=typeof(n=e.constructor)||n!==Array&&!Ne(n.prototype)?v(n)&&null===(n=n[je])&&(n=void 0):n=void 0),new(void 0===n?Array:n)(0===t?0:t)},He=[].push,Ve=function(e){var t=1==e,n=2==e,r=3==e,o=4==e,i=6==e,s=5==e||i;return function(a,u,c,l){for(var p,f,h=Le(a),g=d(h),m=Oe(u,c,3),v=ue(g.length),y=0,_=l||Be,C=t?_(a,v):n?_(a,0):void 0;v>y;y++)if((s||y in g)&&(f=m(p=g[y],y,h),e))if(t)C[y]=f;else if(f)switch(e){case 3:return!0;case 5:return p;case 6:return y;case 2:He.call(C,p)}else if(o)return!1;return i?-1:r||o?o:C}},Ke={forEach:Ve(0),map:Ve(1),filter:Ve(2),some:Ve(3),every:Ve(4),find:Ve(5),findIndex:Ve(6)},$e=function(e,t){var n=[][e];return!!n&&i((function(){n.call(null,t||function(){throw 1},1)}))},Ye=Object.defineProperty,ze={},We=function(e){throw e},Xe=function(e,t){if(C(ze,e))return ze[e];t||(t={});var n=[][e],r=!!C(t,"ACCESSORS")&&t.ACCESSORS,o=C(t,0)?t[0]:We,a=C(t,1)?t[1]:void 0;return ze[e]=!!n&&!i((function(){if(r&&!s)return!0;var e={length:-1};r?Ye(e,1,{enumerable:!0,get:We}):e[1]=1,n.call(e,o,a)}))},Je=Ke.forEach,Qe=$e("forEach"),Ze=Xe("forEach"),et=Qe&&Ze?[].forEach:function(e){return Je(this,e,arguments.length>1?arguments[1]:void 0)};be({target:"Array",proto:!0,forced:[].forEach!=et},{forEach:et});var tt=function(e,t,n,r){try{return r?t(k(n)[0],n[1]):t(n)}catch(s){var o=e.return;throw void 0!==o&&k(o.call(e)),s}},nt={},rt=Fe("iterator"),ot=Array.prototype,it=function(e){return void 0!==e&&(nt.Array===e||ot[rt]===e)},st=function(e,t,n){var r=y(t);r in e?A.f(e,r,l(0,n)):e[r]=n},at={};at[Fe("toStringTag")]="z";var ut="[object z]"===String(at),ct=Fe("toStringTag"),lt="Arguments"==f(function(){return arguments}()),pt=ut?f:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(n){}}(t=Object(e),ct))?n:lt?f(t):"Object"==(r=f(t))&&"function"==typeof t.callee?"Arguments":r},ft=Fe("iterator"),ht=function(e){if(null!=e)return e[ft]||e["@@iterator"]||nt[pt(e)]},dt=function(e){var t,n,r,o,i,s,a=Le(e),u="function"==typeof this?this:Array,c=arguments.length,l=c>1?arguments[1]:void 0,p=void 0!==l,f=ht(a),h=0;if(p&&(l=Oe(l,c>2?arguments[2]:void 0,2)),null==f||u==Array&&it(f))for(n=new u(t=ue(a.length));t>h;h++)s=p?l(a[h],h):a[h],st(n,h,s);else for(i=(o=f.call(a)).next,n=new u;!(r=i.call(o)).done;h++)s=p?tt(o,l,[r.value,h],!0):r.value,st(n,h,s);return n.length=h,n},gt=Fe("iterator"),mt=!1;try{var vt=0,yt={next:function(){return{done:!!vt++}},return:function(){mt=!0}};yt[gt]=function(){return this},Array.from(yt,(function(){throw 2}))}catch(dC){}var _t=function(e,t){if(!t&&!mt)return!1;var n=!1;try{var r={};r[gt]=function(){return{next:function(){return{done:n=!0}}}},e(r)}catch(dC){}return n},Ct=!_t((function(e){Array.from(e)}));be({target:"Array",stat:!0,forced:Ct},{from:dt});var It,Mt=Object.keys||function(e){return ge(e,me)},St=s?Object.defineProperties:function(e,t){k(e);for(var n,r=Mt(t),o=r.length,i=0;o>i;)A.f(e,n=r[i++],t[n]);return e},Tt=re("document","documentElement"),Et=K("IE_PROTO"),Dt=function(){},kt=function(e){return"<script>"+e+"<\/script>"},wt=function(){try{It=document.domain&&new ActiveXObject("htmlfile")}catch(dC){}var e,t;wt=It?function(e){e.write(kt("")),e.close();var t=e.parentWindow.Object;return e=null,t}(It):((t=S("iframe")).style.display="none",Tt.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(kt("document.F=Object")),e.close(),e.F);for(var n=me.length;n--;)delete wt.prototype[me[n]];return wt()};$[Et]=!0;var At=Object.create||function(e,t){var n;return null!==e?(Dt.prototype=k(e),n=new Dt,Dt.prototype=null,n[Et]=e):n=wt(),void 0===t?n:St(n,t)};be({target:"Object",stat:!0,sham:!s},{create:At});var bt=i((function(){Mt(1)}));be({target:"Object",stat:!0,forced:bt},{keys:function(e){return Mt(Le(e))}});var Rt,Ot,Lt,Nt=function(e){return function(t,n){var r,o,i=String(g(t)),s=se(n),a=i.length;return s<0||s>=a?e?"":void 0:(r=i.charCodeAt(s))<55296||r>56319||s+1===a||(o=i.charCodeAt(s+1))<56320||o>57343?e?i.charAt(s):r:e?i.slice(s,s+2):o-56320+(r-55296<<10)+65536}},Pt={codeAt:Nt(!1),charAt:Nt(!0)},Gt=!i((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),xt=K("IE_PROTO"),Ut=Object.prototype,qt=Gt?Object.getPrototypeOf:function(e){return e=Le(e),C(e,xt)?e[xt]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?Ut:null},Ft=Fe("iterator"),jt=!1;[].keys&&("next"in(Lt=[].keys())?(Ot=qt(qt(Lt)))!==Object.prototype&&(Rt=Ot):jt=!0),null==Rt&&(Rt={}),C(Rt,Ft)||b(Rt,Ft,(function(){return this}));var Bt={IteratorPrototype:Rt,BUGGY_SAFARI_ITERATORS:jt},Ht=A.f,Vt=Fe("toStringTag"),Kt=function(e,t,n){e&&!C(e=n?e:e.prototype,Vt)&&Ht(e,Vt,{configurable:!0,value:t})},$t=Bt.IteratorPrototype,Yt=function(){return this},zt=function(e,t,n){var r=t+" Iterator";return e.prototype=At($t,{next:l(1,n)}),Kt(e,r,!1),nt[r]=Yt,e},Wt=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(n,[]),t=n instanceof Array}catch(dC){}return function(n,r){return k(n),function(e){if(!v(e)&&null!==e)throw TypeError("Can't set "+String(e)+" as a prototype")}(r),t?e.call(n,r):n.__proto__=r,n}}():void 0),Xt=Bt.IteratorPrototype,Jt=Bt.BUGGY_SAFARI_ITERATORS,Qt=Fe("iterator"),Zt=function(){return this},en=function(e,t,n,r,o,i,s){zt(n,t,r);var a,u,c,l=function(e){if(e===o&&g)return g;if(!Jt&&e in h)return h[e];switch(e){case"keys":case"values":case"entries":return function(){return new n(this,e)}}return function(){return new n(this)}},p=t+" Iterator",f=!1,h=e.prototype,d=h[Qt]||h["@@iterator"]||o&&h[o],g=!Jt&&d||l(o),m="Array"==t&&h.entries||d;if(m&&(a=qt(m.call(new e)),Xt!==Object.prototype&&a.next&&(qt(a)!==Xt&&(Wt?Wt(a,Xt):"function"!=typeof a[Qt]&&b(a,Qt,Zt)),Kt(a,p,!0))),"values"==o&&d&&"values"!==d.name&&(f=!0,g=function(){return d.call(this)}),h[Qt]!==g&&b(h,Qt,g),nt[t]=g,o)if(u={values:l("values"),keys:i?g:l("keys"),entries:l("entries")},s)for(c in u)(Jt||f||!(c in h))&&ee(h,c,u[c]);else be({target:t,proto:!0,forced:Jt||f},u);return u},tn=Pt.charAt,nn=Z.set,rn=Z.getterFor("String Iterator");en(String,"String",(function(e){nn(this,{type:"String Iterator",string:String(e),index:0})}),(function(){var e,t=rn(this),n=t.string,r=t.index;return r>=n.length?{value:void 0,done:!0}:(e=tn(n,r),t.index+=e.length,{value:e,done:!1})}));var on={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0};for(var sn in on){var an=o[sn],un=an&&an.prototype;if(un&&un.forEach!==et)try{b(un,"forEach",et)}catch(dC){un.forEach=et}}var cn,ln,pn={SDK_READY:"sdkStateReady",SDK_NOT_READY:"sdkStateNotReady",SDK_DESTROY:"sdkDestroy",MESSAGE_RECEIVED:"onMessageReceived",MESSAGE_REVOKED:"onMessageRevoked",CONVERSATION_LIST_UPDATED:"onConversationListUpdated",GROUP_LIST_UPDATED:"onGroupListUpdated",GROUP_SYSTEM_NOTICE_RECEIVED:"receiveGroupSystemNotice",PROFILE_UPDATED:"onProfileUpdated",BLACKLIST_UPDATED:"blacklistUpdated",KICKED_OUT:"kickedOut",ERROR:"error",NET_STATE_CHANGE:"netStateChange"},fn={MSG_TEXT:"TIMTextElem",MSG_IMAGE:"TIMImageElem",MSG_SOUND:"TIMSoundElem",MSG_AUDIO:"TIMSoundElem",MSG_FILE:"TIMFileElem",MSG_FACE:"TIMFaceElem",MSG_VIDEO:"TIMVideoFileElem",MSG_GEO:"TIMLocationElem",MSG_GRP_TIP:"TIMGroupTipElem",MSG_GRP_SYS_NOTICE:"TIMGroupSystemNoticeElem",MSG_CUSTOM:"TIMCustomElem",MSG_PRIORITY_HIGH:"High",MSG_PRIORITY_NORMAL:"Normal",MSG_PRIORITY_LOW:"Low",MSG_PRIORITY_LOWEST:"Lowest",CONV_C2C:"C2C",CONV_GROUP:"GROUP",CONV_SYSTEM:"@TIM#SYSTEM",GRP_PRIVATE:"Private",GRP_PUBLIC:"Public",GRP_CHATROOM:"ChatRoom",GRP_AVCHATROOM:"AVChatRoom",GRP_MBR_ROLE_OWNER:"Owner",GRP_MBR_ROLE_ADMIN:"Admin",GRP_MBR_ROLE_MEMBER:"Member",GRP_TIP_MBR_JOIN:1,GRP_TIP_MBR_QUIT:2,GRP_TIP_MBR_KICKED_OUT:3,GRP_TIP_MBR_SET_ADMIN:4,GRP_TIP_MBR_CANCELED_ADMIN:5,GRP_TIP_GRP_PROFILE_UPDATED:6,GRP_TIP_MBR_PROFILE_UPDATED:7,MSG_REMIND_ACPT_AND_NOTE:"AcceptAndNotify",MSG_REMIND_ACPT_NOT_NOTE:"AcceptNotNotify",MSG_REMIND_DISCARD:"Discard",GENDER_UNKNOWN:"Gender_Type_Unknown",GENDER_FEMALE:"Gender_Type_Female",GENDER_MALE:"Gender_Type_Male",KICKED_OUT_MULT_ACCOUNT:"multipleAccount",KICKED_OUT_MULT_DEVICE:"multipleDevice",KICKED_OUT_USERSIG_EXPIRED:"userSigExpired",ALLOW_TYPE_ALLOW_ANY:"AllowType_Type_AllowAny",ALLOW_TYPE_NEED_CONFIRM:"AllowType_Type_NeedConfirm",ALLOW_TYPE_DENY_ANY:"AllowType_Type_DenyAny",FORBID_TYPE_NONE:"AdminForbid_Type_None",FORBID_TYPE_SEND_OUT:"AdminForbid_Type_SendOut",JOIN_OPTIONS_FREE_ACCESS:"FreeAccess",JOIN_OPTIONS_NEED_PERMISSION:"NeedPermission",JOIN_OPTIONS_DISABLE_APPLY:"DisableApply",JOIN_STATUS_SUCCESS:"JoinedSuccess",JOIN_STATUS_ALREADY_IN_GROUP:"AlreadyInGroup",JOIN_STATUS_WAIT_APPROVAL:"WaitAdminApproval",GRP_PROFILE_OWNER_ID:"ownerID",GRP_PROFILE_CREATE_TIME:"createTime",GRP_PROFILE_LAST_INFO_TIME:"lastInfoTime",GRP_PROFILE_MEMBER_NUM:"memberNum",GRP_PROFILE_MAX_MEMBER_NUM:"maxMemberNum",GRP_PROFILE_JOIN_OPTION:"joinOption",GRP_PROFILE_INTRODUCTION:"introduction",GRP_PROFILE_NOTIFICATION:"notification",GRP_PROFILE_MUTE_ALL_MBRS:"muteAllMembers",NET_STATE_CONNECTED:"connected",NET_STATE_CONNECTING:"connecting",NET_STATE_DISCONNECTED:"disconnected"},hn=re("navigator","userAgent")||"",dn=o.process,gn=dn&&dn.versions,mn=gn&&gn.v8;mn?ln=(cn=mn.split("."))[0]+cn[1]:hn&&(!(cn=hn.match(/Edge\/(\d+)/))||cn[1]>=74)&&(cn=hn.match(/Chrome\/(\d+)/))&&(ln=cn[1]);var vn=ln&&+ln,yn=Fe("species"),_n=function(e){return vn>=51||!i((function(){var t=[];return(t.constructor={})[yn]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},Cn=Ke.map,In=_n("map"),Mn=Xe("map");be({target:"Array",proto:!0,forced:!In||!Mn},{map:function(e){return Cn(this,e,arguments.length>1?arguments[1]:void 0)}});var Sn=[].slice,Tn={},En=function(e,t,n){if(!(t in Tn)){for(var r=[],o=0;o<t;o++)r[o]="a["+o+"]";Tn[t]=Function("C,a","return new C("+r.join(",")+")")}return Tn[t](e,n)},Dn=Function.bind||function(e){var t=Re(this),n=Sn.call(arguments,1),r=function(){var o=n.concat(Sn.call(arguments));return this instanceof r?En(t,o.length,o):t.apply(e,o)};return v(t.prototype)&&(r.prototype=t.prototype),r};function kn(e){return(kn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function wn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function An(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function bn(e,t,n){return t&&An(e.prototype,t),n&&An(e,n),e}function Rn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function On(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ln(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?On(Object(n),!0).forEach((function(t){Rn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):On(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Nn(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Gn(e,t)}function Pn(e){return(Pn=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Gn(e,t){return(Gn=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function xn(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function Un(e,t,n){return(Un=xn()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var o=new(Function.bind.apply(e,r));return n&&Gn(o,n.prototype),o}).apply(null,arguments)}function qn(e){var t="function"==typeof Map?new Map:void 0;return(qn=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return Un(e,arguments,Pn(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),Gn(r,e)})(e)}function Fn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function jn(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?Fn(e):t}function Bn(e){return function(){var t,n=Pn(e);if(xn()){var r=Pn(this).constructor;t=Reflect.construct(n,arguments,r)}else t=n.apply(this,arguments);return jn(this,t)}}function Hn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var n=[],r=!0,o=!1,i=void 0;try{for(var s,a=e[Symbol.iterator]();!(r=(s=a.next()).done)&&(n.push(s.value),!t||n.length!==t);r=!0);}catch(c){o=!0,i=c}finally{try{r||null==a.return||a.return()}finally{if(o)throw i}}return n}}(e,t)||Kn(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Vn(e){return function(e){if(Array.isArray(e))return $n(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||Kn(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Kn(e,t){if(e){if("string"==typeof e)return $n(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?$n(e,t):void 0}}function $n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Yn(e){if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(e=Kn(e))){var t=0,n=function(){};return{s:n,n:function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,o,i=!0,s=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return i=e.done,e},e:function(e){s=!0,o=e},f:function(){try{i||null==r.return||r.return()}finally{if(s)throw o}}}}be({target:"Function",proto:!0},{bind:Dn});var zn=function(){function e(){wn(this,e),this.cache=[],this.options=null}return bn(e,[{key:"use",value:function(e){if("function"!=typeof e)throw"middleware must be a function";return this.cache.push(e),this}},{key:"next",value:function(e){if(this.middlewares&&this.middlewares.length>0)return this.middlewares.shift().call(this,this.options,this.next.bind(this))}},{key:"run",value:function(e){return this.middlewares=this.cache.map((function(e){return e})),this.options=e,this.next()}}]),e}(),Wn=Fe("isConcatSpreadable"),Xn=vn>=51||!i((function(){var e=[];return e[Wn]=!1,e.concat()[0]!==e})),Jn=_n("concat"),Qn=function(e){if(!v(e))return!1;var t=e[Wn];return void 0!==t?!!t:Ne(e)};be({target:"Array",proto:!0,forced:!Xn||!Jn},{concat:function(e){var t,n,r,o,i,s=Le(this),a=Be(s,0),u=0;for(t=-1,r=arguments.length;t<r;t++)if(i=-1===t?s:arguments[t],Qn(i)){if(u+(o=ue(i.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(n=0;n<o;n++,u++)n in i&&st(a,u,i[n])}else{if(u>=9007199254740991)throw TypeError("Maximum allowed index exceeded");st(a,u++,i)}return a.length=u,a}});var Zn=A.f,er=Function.prototype,tr=er.toString,nr=/^\s*function ([^ (]*)/;s&&!("name"in er)&&Zn(er,"name",{configurable:!0,get:function(){try{return tr.call(this).match(nr)[1]}catch(dC){return""}}});var rr=n((function(t,n){var r,o,i,s,a,u,c,l,p,f,h,d,g,m,v,y,_,C;t.exports=(r="function"==typeof Promise,o="object"==typeof self?self:e,i="undefined"!=typeof Symbol,s="undefined"!=typeof Map,a="undefined"!=typeof Set,u="undefined"!=typeof WeakMap,c="undefined"!=typeof WeakSet,l="undefined"!=typeof DataView,p=i&&void 0!==Symbol.iterator,f=i&&void 0!==Symbol.toStringTag,h=a&&"function"==typeof Set.prototype.entries,d=s&&"function"==typeof Map.prototype.entries,g=h&&Object.getPrototypeOf((new Set).entries()),m=d&&Object.getPrototypeOf((new Map).entries()),v=p&&"function"==typeof Array.prototype[Symbol.iterator],y=v&&Object.getPrototypeOf([][Symbol.iterator]()),_=p&&"function"==typeof String.prototype[Symbol.iterator],C=_&&Object.getPrototypeOf(""[Symbol.iterator]()),function(e){var t=typeof e;if("object"!==t)return t;if(null===e)return"null";if(e===o)return"global";if(Array.isArray(e)&&(!1===f||!(Symbol.toStringTag in e)))return"Array";if("object"==typeof window&&null!==window){if("object"==typeof window.location&&e===window.location)return"Location";if("object"==typeof window.document&&e===window.document)return"Document";if("object"==typeof window.navigator){if("object"==typeof window.navigator.mimeTypes&&e===window.navigator.mimeTypes)return"MimeTypeArray";if("object"==typeof window.navigator.plugins&&e===window.navigator.plugins)return"PluginArray"}if(("function"==typeof window.HTMLElement||"object"==typeof window.HTMLElement)&&e instanceof window.HTMLElement){if("BLOCKQUOTE"===e.tagName)return"HTMLQuoteElement";if("TD"===e.tagName)return"HTMLTableDataCellElement";if("TH"===e.tagName)return"HTMLTableHeaderCellElement"}}var n=f&&e[Symbol.toStringTag];if("string"==typeof n)return n;var i=Object.getPrototypeOf(e);return i===RegExp.prototype?"RegExp":i===Date.prototype?"Date":r&&i===Promise.prototype?"Promise":a&&i===Set.prototype?"Set":s&&i===Map.prototype?"Map":c&&i===WeakSet.prototype?"WeakSet":u&&i===WeakMap.prototype?"WeakMap":l&&i===DataView.prototype?"DataView":s&&i===m?"Map Iterator":a&&i===g?"Set Iterator":v&&i===y?"Array Iterator":_&&i===C?"String Iterator":null===i?"Object":Object.prototype.toString.call(e).slice(8,-1)})}));be({target:"Array",stat:!0},{isArray:Ne});var or=Fe("unscopables"),ir=Array.prototype;null==ir[or]&&A.f(ir,or,{configurable:!0,value:At(null)});var sr=function(e){ir[or][e]=!0},ar=Ke.find,ur=!0,cr=Xe("find");"find"in[]&&Array(1).find((function(){ur=!1})),be({target:"Array",proto:!0,forced:ur||!cr},{find:function(e){return ar(this,e,arguments.length>1?arguments[1]:void 0)}}),sr("find");var lr=he.includes,pr=Xe("indexOf",{ACCESSORS:!0,1:0});be({target:"Array",proto:!0,forced:!pr},{includes:function(e){return lr(this,e,arguments.length>1?arguments[1]:void 0)}}),sr("includes");var fr=he.indexOf,hr=[].indexOf,dr=!!hr&&1/[1].indexOf(1,-0)<0,gr=$e("indexOf"),mr=Xe("indexOf",{ACCESSORS:!0,1:0});be({target:"Array",proto:!0,forced:dr||!gr||!mr},{indexOf:function(e){return dr?hr.apply(this,arguments)||0:fr(this,e,arguments.length>1?arguments[1]:void 0)}});var vr=Z.set,yr=Z.getterFor("Array Iterator"),_r=en(Array,"Array",(function(e,t){vr(this,{type:"Array Iterator",target:m(e),index:0,kind:t})}),(function(){var e=yr(this),t=e.target,n=e.kind,r=e.index++;return!t||r>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:t[r],done:!1}:{value:[r,t[r]],done:!1}}),"values");nt.Arguments=nt.Array,sr("keys"),sr("values"),sr("entries");var Cr=[].join,Ir=d!=Object,Mr=$e("join",",");be({target:"Array",proto:!0,forced:Ir||!Mr},{join:function(e){return Cr.call(m(this),void 0===e?",":e)}});var Sr=_n("slice"),Tr=Xe("slice",{ACCESSORS:!0,0:0,1:2}),Er=Fe("species"),Dr=[].slice,kr=Math.max;be({target:"Array",proto:!0,forced:!Sr||!Tr},{slice:function(e,t){var n,r,o,i=m(this),s=ue(i.length),a=pe(e,s),u=pe(void 0===t?s:t,s);if(Ne(i)&&("function"!=typeof(n=i.constructor)||n!==Array&&!Ne(n.prototype)?v(n)&&null===(n=n[Er])&&(n=void 0):n=void 0,n===Array||void 0===n))return Dr.call(i,a,u);for(r=new(void 0===n?Array:n)(kr(u-a,0)),o=0;a<u;a++,o++)a in i&&st(r,o,i[a]);return r.length=o,r}}),be({target:"Date",stat:!0},{now:function(){return(new Date).getTime()}});var wr="".repeat||function(e){var t=String(g(this)),n="",r=se(e);if(r<0||1/0==r)throw RangeError("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(t+=t))1&r&&(n+=t);return n},Ar=Math.ceil,br=function(e){return function(t,n,r){var o,i,s=String(g(t)),a=s.length,u=void 0===r?" ":String(r),c=ue(n);return c<=a||""==u?s:(o=c-a,(i=wr.call(u,Ar(o/u.length))).length>o&&(i=i.slice(0,o)),e?s+i:i+s)}},Rr={start:br(!1),end:br(!0)}.start,Or=Math.abs,Lr=Date.prototype,Nr=Lr.getTime,Pr=Lr.toISOString,Gr=i((function(){return"0385-07-25T07:06:39.999Z"!=Pr.call(new Date(-50000000000001))}))||!i((function(){Pr.call(new Date(NaN))}))?function(){if(!isFinite(Nr.call(this)))throw RangeError("Invalid time value");var e=this.getUTCFullYear(),t=this.getUTCMilliseconds(),n=e<0?"-":e>9999?"+":"";return n+Rr(Or(e),n?6:4,0)+"-"+Rr(this.getUTCMonth()+1,2,0)+"-"+Rr(this.getUTCDate(),2,0)+"T"+Rr(this.getUTCHours(),2,0)+":"+Rr(this.getUTCMinutes(),2,0)+":"+Rr(this.getUTCSeconds(),2,0)+"."+Rr(t,3,0)+"Z"}:Pr;be({target:"Date",proto:!0,forced:Date.prototype.toISOString!==Gr},{toISOString:Gr});var xr=Date.prototype,Ur=xr.toString,qr=xr.getTime;new Date(NaN)+""!="Invalid Date"&&ee(xr,"toString",(function(){var e=qr.call(this);return e==e?Ur.call(this):"Invalid Date"}));var Fr=function(e,t,n){var r,o;return Wt&&"function"==typeof(r=t.constructor)&&r!==n&&v(o=r.prototype)&&o!==n.prototype&&Wt(e,o),e},jr="\t\n\v\f\r                　\u2028\u2029\ufeff",Br="["+jr+"]",Hr=RegExp("^"+Br+Br+"*"),Vr=RegExp(Br+Br+"*$"),Kr=function(e){return function(t){var n=String(g(t));return 1&e&&(n=n.replace(Hr,"")),2&e&&(n=n.replace(Vr,"")),n}},$r={start:Kr(1),end:Kr(2),trim:Kr(3)},Yr=ye.f,zr=D.f,Wr=A.f,Xr=$r.trim,Jr=o.Number,Qr=Jr.prototype,Zr="Number"==f(At(Qr)),eo=function(e){var t,n,r,o,i,s,a,u,c=y(e,!1);if("string"==typeof c&&c.length>2)if(43===(t=(c=Xr(c)).charCodeAt(0))||45===t){if(88===(n=c.charCodeAt(2))||120===n)return NaN}else if(48===t){switch(c.charCodeAt(1)){case 66:case 98:r=2,o=49;break;case 79:case 111:r=8,o=55;break;default:return+c}for(s=(i=c.slice(2)).length,a=0;a<s;a++)if((u=i.charCodeAt(a))<48||u>o)return NaN;return parseInt(i,r)}return+c};if(we("Number",!Jr(" 0o1")||!Jr("0b1")||Jr("+0x1"))){for(var to,no=function(e){var t=arguments.length<1?0:e,n=this;return n instanceof no&&(Zr?i((function(){Qr.valueOf.call(n)})):"Number"!=f(n))?Fr(new Jr(eo(t)),n,no):eo(t)},ro=s?Yr(Jr):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),oo=0;ro.length>oo;oo++)C(Jr,to=ro[oo])&&!C(no,to)&&Wr(no,to,zr(Jr,to));no.prototype=Qr,Qr.constructor=no,ee(o,"Number",no)}var io=ye.f,so={}.toString,ao="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],uo={f:function(e){return ao&&"[object Window]"==so.call(e)?function(e){try{return io(e)}catch(dC){return ao.slice()}}(e):io(m(e))}},co=uo.f,lo=i((function(){return!Object.getOwnPropertyNames(1)}));be({target:"Object",stat:!0,forced:lo},{getOwnPropertyNames:co});var po=i((function(){qt(1)}));be({target:"Object",stat:!0,forced:po,sham:!Gt},{getPrototypeOf:function(e){return qt(Le(e))}});var fo=ut?{}.toString:function(){return"[object "+pt(this)+"]"};ut||ee(Object.prototype,"toString",fo,{unsafe:!0});var ho=$r.trim,go=o.parseInt,mo=/^[+-]?0[Xx]/,vo=8!==go(jr+"08")||22!==go(jr+"0x16")?function(e,t){var n=ho(String(e));return go(n,t>>>0||(mo.test(n)?16:10))}:go;be({global:!0,forced:parseInt!=vo},{parseInt:vo});var yo,_o,Co,Io=o.Promise,Mo=function(e,t,n){for(var r in t)ee(e,r,t[r],n);return e},So=Fe("species"),To=function(e){var t=re(e),n=A.f;s&&t&&!t[So]&&n(t,So,{configurable:!0,get:function(){return this}})},Eo=function(e,t,n){if(!(e instanceof t))throw TypeError("Incorrect "+(n?n+" ":"")+"invocation");return e},Do=n((function(e){var t=function(e,t){this.stopped=e,this.result=t};(e.exports=function(e,n,r,o,i){var s,a,u,c,l,p,f,h=Oe(n,r,o?2:1);if(i)s=e;else{if("function"!=typeof(a=ht(e)))throw TypeError("Target is not iterable");if(it(a)){for(u=0,c=ue(e.length);c>u;u++)if((l=o?h(k(f=e[u])[0],f[1]):h(e[u]))&&l instanceof t)return l;return new t(!1)}s=a.call(e)}for(p=s.next;!(f=p.call(s)).done;)if("object"==typeof(l=tt(s,h,f.value,o))&&l&&l instanceof t)return l;return new t(!1)}).stop=function(e){return new t(!0,e)}})),ko=Fe("species"),wo=function(e,t){var n,r=k(e).constructor;return void 0===r||null==(n=k(r)[ko])?t:Re(n)},Ao=/(iphone|ipod|ipad).*applewebkit/i.test(hn),bo=o.location,Ro=o.setImmediate,Oo=o.clearImmediate,Lo=o.process,No=o.MessageChannel,Po=o.Dispatch,Go=0,xo={},Uo=function(e){if(xo.hasOwnProperty(e)){var t=xo[e];delete xo[e],t()}},qo=function(e){return function(){Uo(e)}},Fo=function(e){Uo(e.data)},jo=function(e){o.postMessage(e+"",bo.protocol+"//"+bo.host)};Ro&&Oo||(Ro=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return xo[++Go]=function(){("function"==typeof e?e:Function(e)).apply(void 0,t)},yo(Go),Go},Oo=function(e){delete xo[e]},"process"==f(Lo)?yo=function(e){Lo.nextTick(qo(e))}:Po&&Po.now?yo=function(e){Po.now(qo(e))}:No&&!Ao?(Co=(_o=new No).port2,_o.port1.onmessage=Fo,yo=Oe(Co.postMessage,Co,1)):!o.addEventListener||"function"!=typeof postMessage||o.importScripts||i(jo)?yo="onreadystatechange"in S("script")?function(e){Tt.appendChild(S("script")).onreadystatechange=function(){Tt.removeChild(this),Uo(e)}}:function(e){setTimeout(qo(e),0)}:(yo=jo,o.addEventListener("message",Fo,!1)));var Bo,Ho,Vo,Ko,$o,Yo,zo,Wo,Xo={set:Ro,clear:Oo},Jo=D.f,Qo=Xo.set,Zo=o.MutationObserver||o.WebKitMutationObserver,ei=o.process,ti=o.Promise,ni="process"==f(ei),ri=Jo(o,"queueMicrotask"),oi=ri&&ri.value;oi||(Bo=function(){var e,t;for(ni&&(e=ei.domain)&&e.exit();Ho;){t=Ho.fn,Ho=Ho.next;try{t()}catch(dC){throw Ho?Ko():Vo=void 0,dC}}Vo=void 0,e&&e.enter()},ni?Ko=function(){ei.nextTick(Bo)}:Zo&&!Ao?($o=!0,Yo=document.createTextNode(""),new Zo(Bo).observe(Yo,{characterData:!0}),Ko=function(){Yo.data=$o=!$o}):ti&&ti.resolve?(zo=ti.resolve(void 0),Wo=zo.then,Ko=function(){Wo.call(zo,Bo)}):Ko=function(){Qo.call(o,Bo)});var ii,si,ai,ui,ci=oi||function(e){var t={fn:e,next:void 0};Vo&&(Vo.next=t),Ho||(Ho=t,Ko()),Vo=t},li=function(e){var t,n;this.promise=new e((function(e,r){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=r})),this.resolve=Re(t),this.reject=Re(n)},pi={f:function(e){return new li(e)}},fi=function(e,t){if(k(e),v(t)&&t.constructor===e)return t;var n=pi.f(e);return(0,n.resolve)(t),n.promise},hi=function(e){try{return{error:!1,value:e()}}catch(dC){return{error:!0,value:dC}}},di=Xo.set,gi=Fe("species"),mi="Promise",vi=Z.get,yi=Z.set,_i=Z.getterFor(mi),Ci=Io,Ii=o.TypeError,Mi=o.document,Si=o.process,Ti=re("fetch"),Ei=pi.f,Di=Ei,ki="process"==f(Si),wi=!!(Mi&&Mi.createEvent&&o.dispatchEvent),Ai=we(mi,(function(){if(x(Ci)===String(Ci)){if(66===vn)return!0;if(!ki&&"function"!=typeof PromiseRejectionEvent)return!0}if(vn>=51&&/native code/.test(Ci))return!1;var e=Ci.resolve(1),t=function(e){e((function(){}),(function(){}))};return(e.constructor={})[gi]=t,!(e.then((function(){}))instanceof t)})),bi=Ai||!_t((function(e){Ci.all(e).catch((function(){}))})),Ri=function(e){var t;return!(!v(e)||"function"!=typeof(t=e.then))&&t},Oi=function(e,t,n){if(!t.notified){t.notified=!0;var r=t.reactions;ci((function(){for(var o=t.value,i=1==t.state,s=0;r.length>s;){var a,u,c,l=r[s++],p=i?l.ok:l.fail,f=l.resolve,h=l.reject,d=l.domain;try{p?(i||(2===t.rejection&&Gi(e,t),t.rejection=1),!0===p?a=o:(d&&d.enter(),a=p(o),d&&(d.exit(),c=!0)),a===l.promise?h(Ii("Promise-chain cycle")):(u=Ri(a))?u.call(a,f,h):f(a)):h(o)}catch(dC){d&&!c&&d.exit(),h(dC)}}t.reactions=[],t.notified=!1,n&&!t.rejection&&Ni(e,t)}))}},Li=function(e,t,n){var r,i;wi?((r=Mi.createEvent("Event")).promise=t,r.reason=n,r.initEvent(e,!1,!0),o.dispatchEvent(r)):r={promise:t,reason:n},(i=o["on"+e])?i(r):"unhandledrejection"===e&&function(e,t){var n=o.console;n&&n.error&&(1===arguments.length?n.error(e):n.error(e,t))}("Unhandled promise rejection",n)},Ni=function(e,t){di.call(o,(function(){var n,r=t.value;if(Pi(t)&&(n=hi((function(){ki?Si.emit("unhandledRejection",r,e):Li("unhandledrejection",e,r)})),t.rejection=ki||Pi(t)?2:1,n.error))throw n.value}))},Pi=function(e){return 1!==e.rejection&&!e.parent},Gi=function(e,t){di.call(o,(function(){ki?Si.emit("rejectionHandled",e):Li("rejectionhandled",e,t.value)}))},xi=function(e,t,n,r){return function(o){e(t,n,o,r)}},Ui=function(e,t,n,r){t.done||(t.done=!0,r&&(t=r),t.value=n,t.state=2,Oi(e,t,!0))},qi=function(e,t,n,r){if(!t.done){t.done=!0,r&&(t=r);try{if(e===n)throw Ii("Promise can't be resolved itself");var o=Ri(n);o?ci((function(){var r={done:!1};try{o.call(n,xi(qi,e,r,t),xi(Ui,e,r,t))}catch(dC){Ui(e,r,dC,t)}})):(t.value=n,t.state=1,Oi(e,t,!1))}catch(dC){Ui(e,{done:!1},dC,t)}}};Ai&&(Ci=function(e){Eo(this,Ci,mi),Re(e),ii.call(this);var t=vi(this);try{e(xi(qi,this,t),xi(Ui,this,t))}catch(dC){Ui(this,t,dC)}},(ii=function(e){yi(this,{type:mi,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=Mo(Ci.prototype,{then:function(e,t){var n=_i(this),r=Ei(wo(this,Ci));return r.ok="function"!=typeof e||e,r.fail="function"==typeof t&&t,r.domain=ki?Si.domain:void 0,n.parent=!0,n.reactions.push(r),0!=n.state&&Oi(this,n,!1),r.promise},catch:function(e){return this.then(void 0,e)}}),si=function(){var e=new ii,t=vi(e);this.promise=e,this.resolve=xi(qi,e,t),this.reject=xi(Ui,e,t)},pi.f=Ei=function(e){return e===Ci||e===ai?new si(e):Di(e)},"function"==typeof Io&&(ui=Io.prototype.then,ee(Io.prototype,"then",(function(e,t){var n=this;return new Ci((function(e,t){ui.call(n,e,t)})).then(e,t)}),{unsafe:!0}),"function"==typeof Ti&&be({global:!0,enumerable:!0,forced:!0},{fetch:function(e){return fi(Ci,Ti.apply(o,arguments))}}))),be({global:!0,wrap:!0,forced:Ai},{Promise:Ci}),Kt(Ci,mi,!1),To(mi),ai=re(mi),be({target:mi,stat:!0,forced:Ai},{reject:function(e){var t=Ei(this);return t.reject.call(void 0,e),t.promise}}),be({target:mi,stat:!0,forced:Ai},{resolve:function(e){return fi(this,e)}}),be({target:mi,stat:!0,forced:bi},{all:function(e){var t=this,n=Ei(t),r=n.resolve,o=n.reject,i=hi((function(){var n=Re(t.resolve),i=[],s=0,a=1;Do(e,(function(e){var u=s++,c=!1;i.push(void 0),a++,n.call(t,e).then((function(e){c||(c=!0,i[u]=e,--a||r(i))}),o)})),--a||r(i)}));return i.error&&o(i.value),n.promise},race:function(e){var t=this,n=Ei(t),r=n.reject,o=hi((function(){var o=Re(t.resolve);Do(e,(function(e){o.call(t,e).then(n.resolve,r)}))}));return o.error&&r(o.value),n.promise}});var Fi=function(){var e=k(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t};function ji(e,t){return RegExp(e,t)}var Bi,Hi,Vi={UNSUPPORTED_Y:i((function(){var e=ji("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),BROKEN_CARET:i((function(){var e=ji("^r","gy");return e.lastIndex=2,null!=e.exec("str")}))},Ki=RegExp.prototype.exec,$i=String.prototype.replace,Yi=Ki,zi=(Bi=/a/,Hi=/b*/g,Ki.call(Bi,"a"),Ki.call(Hi,"a"),0!==Bi.lastIndex||0!==Hi.lastIndex),Wi=Vi.UNSUPPORTED_Y||Vi.BROKEN_CARET,Xi=void 0!==/()??/.exec("")[1];(zi||Xi||Wi)&&(Yi=function(e){var t,n,r,o,i=this,s=Wi&&i.sticky,a=Fi.call(i),u=i.source,c=0,l=e;return s&&(-1===(a=a.replace("y","")).indexOf("g")&&(a+="g"),l=String(e).slice(i.lastIndex),i.lastIndex>0&&(!i.multiline||i.multiline&&"\n"!==e[i.lastIndex-1])&&(u="(?: "+u+")",l=" "+l,c++),n=new RegExp("^(?:"+u+")",a)),Xi&&(n=new RegExp("^"+u+"$(?!\\s)",a)),zi&&(t=i.lastIndex),r=Ki.call(s?n:i,l),s?r?(r.input=r.input.slice(c),r[0]=r[0].slice(c),r.index=i.lastIndex,i.lastIndex+=r[0].length):i.lastIndex=0:zi&&r&&(i.lastIndex=i.global?r.index+r[0].length:t),Xi&&r&&r.length>1&&$i.call(r[0],n,(function(){for(o=1;o<arguments.length-2;o++)void 0===arguments[o]&&(r[o]=void 0)})),r});var Ji=Yi;be({target:"RegExp",proto:!0,forced:/./.exec!==Ji},{exec:Ji});var Qi=RegExp.prototype,Zi=Qi.toString,es=i((function(){return"/a/b"!=Zi.call({source:"a",flags:"b"})})),ts="toString"!=Zi.name;(es||ts)&&ee(RegExp.prototype,"toString",(function(){var e=k(this),t=String(e.source),n=e.flags;return"/"+t+"/"+String(void 0===n&&e instanceof RegExp&&!("flags"in Qi)?Fi.call(e):n)}),{unsafe:!0});var ns=Fe("match"),rs=function(e){var t;return v(e)&&(void 0!==(t=e[ns])?!!t:"RegExp"==f(e))},os=function(e){if(rs(e))throw TypeError("The method doesn't accept regular expressions");return e},is=Fe("match");be({target:"String",proto:!0,forced:!function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[is]=!1,"/./"[e](t)}catch(o){}}return!1}("includes")},{includes:function(e){return!!~String(g(this)).indexOf(os(e),arguments.length>1?arguments[1]:void 0)}});var ss=Fe("species"),as=!i((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),us="$0"==="a".replace(/./,"$0"),cs=Fe("replace"),ls=!!/./[cs]&&""===/./[cs]("a","$0"),ps=!i((function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var n="ab".split(e);return 2!==n.length||"a"!==n[0]||"b"!==n[1]})),fs=function(e,t,n,r){var o=Fe(e),s=!i((function(){var t={};return t[o]=function(){return 7},7!=""[e](t)})),a=s&&!i((function(){var t=!1,n=/a/;return"split"===e&&((n={}).constructor={},n.constructor[ss]=function(){return n},n.flags="",n[o]=/./[o]),n.exec=function(){return t=!0,null},n[o](""),!t}));if(!s||!a||"replace"===e&&(!as||!us||ls)||"split"===e&&!ps){var u=/./[o],c=n(o,""[e],(function(e,t,n,r,o){return t.exec===Ji?s&&!o?{done:!0,value:u.call(t,n,r)}:{done:!0,value:e.call(n,t,r)}:{done:!1}}),{REPLACE_KEEPS_$0:us,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:ls}),l=c[0],p=c[1];ee(String.prototype,e,l),ee(RegExp.prototype,o,2==t?function(e,t){return p.call(e,this,t)}:function(e){return p.call(e,this)})}r&&b(RegExp.prototype[o],"sham",!0)},hs=Pt.charAt,ds=function(e,t,n){return t+(n?hs(e,t).length:1)},gs=function(e,t){var n=e.exec;if("function"==typeof n){var r=n.call(e,t);if("object"!=typeof r)throw TypeError("RegExp exec method returned something other than an Object or null");return r}if("RegExp"!==f(e))throw TypeError("RegExp#exec called on incompatible receiver");return Ji.call(e,t)};fs("match",1,(function(e,t,n){return[function(t){var n=g(this),r=null==t?void 0:t[e];return void 0!==r?r.call(t,n):new RegExp(t)[e](String(n))},function(e){var r=n(t,e,this);if(r.done)return r.value;var o=k(e),i=String(this);if(!o.global)return gs(o,i);var s=o.unicode;o.lastIndex=0;for(var a,u=[],c=0;null!==(a=gs(o,i));){var l=String(a[0]);u[c]=l,""===l&&(o.lastIndex=ds(i,ue(o.lastIndex),s)),c++}return 0===c?null:u}]}));var ms=Math.max,vs=Math.min,ys=Math.floor,_s=/\$([$&'`]|\d\d?|<[^>]*>)/g,Cs=/\$([$&'`]|\d\d?)/g;fs("replace",2,(function(e,t,n,r){var o=r.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,i=r.REPLACE_KEEPS_$0,s=o?"$":"$0";return[function(n,r){var o=g(this),i=null==n?void 0:n[e];return void 0!==i?i.call(n,o,r):t.call(String(o),n,r)},function(e,r){if(!o&&i||"string"==typeof r&&-1===r.indexOf(s)){var u=n(t,e,this,r);if(u.done)return u.value}var c=k(e),l=String(this),p="function"==typeof r;p||(r=String(r));var f=c.global;if(f){var h=c.unicode;c.lastIndex=0}for(var d=[];;){var g=gs(c,l);if(null===g)break;if(d.push(g),!f)break;""===String(g[0])&&(c.lastIndex=ds(l,ue(c.lastIndex),h))}for(var m,v="",y=0,_=0;_<d.length;_++){g=d[_];for(var C=String(g[0]),I=ms(vs(se(g.index),l.length),0),M=[],S=1;S<g.length;S++)M.push(void 0===(m=g[S])?m:String(m));var T=g.groups;if(p){var E=[C].concat(M,I,l);void 0!==T&&E.push(T);var D=String(r.apply(void 0,E))}else D=a(C,l,I,M,T,r);I>=y&&(v+=l.slice(y,I)+D,y=I+C.length)}return v+l.slice(y)}];function a(e,n,r,o,i,s){var a=r+e.length,u=o.length,c=Cs;return void 0!==i&&(i=Le(i),c=_s),t.call(s,c,(function(t,s){var c;switch(s.charAt(0)){case"$":return"$";case"&":return e;case"`":return n.slice(0,r);case"'":return n.slice(a);case"<":c=i[s.slice(1,-1)];break;default:var l=+s;if(0===l)return t;if(l>u){var p=ys(l/10);return 0===p?t:p<=u?void 0===o[p-1]?s.charAt(1):o[p-1]+s.charAt(1):t}c=o[l-1]}return void 0===c?"":c}))}}));var Is=Fe("iterator"),Ms=Fe("toStringTag"),Ss=_r.values;for(var Ts in on){var Es=o[Ts],Ds=Es&&Es.prototype;if(Ds){if(Ds[Is]!==Ss)try{b(Ds,Is,Ss)}catch(dC){Ds[Is]=Ss}if(Ds[Ms]||b(Ds,Ms,Ts),on[Ts])for(var ks in _r)if(Ds[ks]!==_r[ks])try{b(Ds,ks,_r[ks])}catch(dC){Ds[ks]=_r[ks]}}}var ws=$r.trim,As=o.parseFloat,bs=1/As(jr+"-0")!=-1/0?function(e){var t=ws(String(e)),n=As(t);return 0===n&&"-"==t.charAt(0)?-0:n}:As;be({global:!0,forced:parseFloat!=bs},{parseFloat:bs});var Rs="undefined"!=typeof window,Os="undefined"!=typeof wx&&"function"==typeof wx.getSystemInfoSync,Ls=Rs&&window.navigator&&window.navigator.userAgent||"",Ns=/AppleWebKit\/([\d.]+)/i.exec(Ls),Ps=(Ns&&parseFloat(Ns.pop()),/iPad/i.test(Ls),/iPhone/i.test(Ls),/iPod/i.test(Ls),function(){var e=Ls.match(/OS (\d+)_/i);e&&e[1]&&e[1]}(),/Android/i.test(Ls)),Gs=(function(){var e=Ls.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;var t=e[1]&&parseFloat(e[1]),n=e[2]&&parseFloat(e[2]);t&&n&&parseFloat(e[1]+"."+e[2])}(),Ps&&/webkit/i.test(Ls),/Firefox/i.test(Ls),/Edge/i.test(Ls)),xs=(!Gs&&/Chrome/i.test(Ls),function(){var e=Ls.match(/Chrome\/(\d+)/);e&&e[1]&&parseFloat(e[1])}(),/MSIE/.test(Ls)),Us=(/MSIE\s8\.0/.test(Ls),function(){var e=/MSIE\s(\d+)\.\d/.exec(Ls),t=e&&parseFloat(e[1]);return!t&&/Trident\/7.0/i.test(Ls)&&/rv:11.0/.test(Ls)&&(t=11),t}()),qs=(/Safari/i.test(Ls),/TBS\/\d+/i.test(Ls)),Fs=(function(){var e=Ls.match(/TBS\/(\d+)/i);e&&e[1]&&e[1]}(),!qs&&/MQQBrowser\/\d+/i.test(Ls),!qs&&/ QQBrowser\/\d+/i.test(Ls),/(micromessenger|webbrowser)/i.test(Ls)),js=(/Windows/i.test(Ls),/MAC OS X/i.test(Ls),/MicroMessenger/i.test(Ls),_n("splice")),Bs=Xe("splice",{ACCESSORS:!0,0:0,1:2}),Hs=Math.max,Vs=Math.min;be({target:"Array",proto:!0,forced:!js||!Bs},{splice:function(e,t){var n,r,o,i,s,a,u=Le(this),c=ue(u.length),l=pe(e,c),p=arguments.length;if(0===p?n=r=0:1===p?(n=0,r=c-l):(n=p-2,r=Vs(Hs(se(t),0),c-l)),c+n-r>9007199254740991)throw TypeError("Maximum allowed length exceeded");for(o=Be(u,r),i=0;i<r;i++)(s=l+i)in u&&st(o,i,u[s]);if(o.length=r,n<r){for(i=l;i<c-r;i++)a=i+n,(s=i+r)in u?u[a]=u[s]:delete u[a];for(i=c;i>c-r+n;i--)delete u[i-1]}else if(n>r)for(i=c-r;i>l;i--)a=i+n-1,(s=i+r-1)in u?u[a]=u[s]:delete u[a];for(i=0;i<n;i++)u[i+l]=arguments[i+2];return u.length=c-r+n,o}});var Ks,$s,Ys=!i((function(){return Object.isExtensible(Object.preventExtensions({}))})),zs=n((function(e){var t=A.f,n=H("meta"),r=0,o=Object.isExtensible||function(){return!0},i=function(e){t(e,n,{value:{objectID:"O"+ ++r,weakData:{}}})},s=e.exports={REQUIRED:!1,fastKey:function(e,t){if(!v(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!C(e,n)){if(!o(e))return"F";if(!t)return"E";i(e)}return e[n].objectID},getWeakData:function(e,t){if(!C(e,n)){if(!o(e))return!0;if(!t)return!1;i(e)}return e[n].weakData},onFreeze:function(e){return Ys&&s.REQUIRED&&o(e)&&!C(e,n)&&i(e),e}};$[n]=!0})),Ws=(zs.REQUIRED,zs.fastKey,zs.getWeakData,zs.onFreeze,A.f),Xs=zs.fastKey,Js=Z.set,Qs=Z.getterFor,Zs=(function(e,t,n){var r=-1!==e.indexOf("Map"),s=-1!==e.indexOf("Weak"),a=r?"set":"add",u=o[e],c=u&&u.prototype,l=u,p={},f=function(e){var t=c[e];ee(c,e,"add"==e?function(e){return t.call(this,0===e?0:e),this}:"delete"==e?function(e){return!(s&&!v(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return s&&!v(e)?void 0:t.call(this,0===e?0:e)}:"has"==e?function(e){return!(s&&!v(e))&&t.call(this,0===e?0:e)}:function(e,n){return t.call(this,0===e?0:e,n),this})};if(we(e,"function"!=typeof u||!(s||c.forEach&&!i((function(){(new u).entries().next()})))))l=n.getConstructor(t,e,r,a),zs.REQUIRED=!0;else if(we(e,!0)){var h=new l,d=h[a](s?{}:-0,1)!=h,g=i((function(){h.has(1)})),m=_t((function(e){new u(e)})),y=!s&&i((function(){for(var e=new u,t=5;t--;)e[a](t,t);return!e.has(-0)}));m||((l=t((function(t,n){Eo(t,l,e);var o=Fr(new u,t,l);return null!=n&&Do(n,o[a],o,r),o}))).prototype=c,c.constructor=l),(g||y)&&(f("delete"),f("has"),r&&f("get")),(y||d)&&f(a),s&&c.clear&&delete c.clear}p[e]=l,be({global:!0,forced:l!=u},p),Kt(l,e),s||n.setStrong(l,e,r)}("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),{getConstructor:function(e,t,n,r){var o=e((function(e,i){Eo(e,o,t),Js(e,{type:t,index:At(null),first:void 0,last:void 0,size:0}),s||(e.size=0),null!=i&&Do(i,e[r],e,n)})),i=Qs(t),a=function(e,t,n){var r,o,a=i(e),c=u(e,t);return c?c.value=n:(a.last=c={index:o=Xs(t,!0),key:t,value:n,previous:r=a.last,next:void 0,removed:!1},a.first||(a.first=c),r&&(r.next=c),s?a.size++:e.size++,"F"!==o&&(a.index[o]=c)),e},u=function(e,t){var n,r=i(e),o=Xs(t);if("F"!==o)return r.index[o];for(n=r.first;n;n=n.next)if(n.key==t)return n};return Mo(o.prototype,{clear:function(){for(var e=i(this),t=e.index,n=e.first;n;)n.removed=!0,n.previous&&(n.previous=n.previous.next=void 0),delete t[n.index],n=n.next;e.first=e.last=void 0,s?e.size=0:this.size=0},delete:function(e){var t=i(this),n=u(this,e);if(n){var r=n.next,o=n.previous;delete t.index[n.index],n.removed=!0,o&&(o.next=r),r&&(r.previous=o),t.first==n&&(t.first=r),t.last==n&&(t.last=o),s?t.size--:this.size--}return!!n},forEach:function(e){for(var t,n=i(this),r=Oe(e,arguments.length>1?arguments[1]:void 0,3);t=t?t.next:n.first;)for(r(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!u(this,e)}}),Mo(o.prototype,n?{get:function(e){var t=u(this,e);return t&&t.value},set:function(e,t){return a(this,0===e?0:e,t)}}:{add:function(e){return a(this,e=0===e?0:e,e)}}),s&&Ws(o.prototype,"size",{get:function(){return i(this).size}}),o},setStrong:function(e,t,n){var r=t+" Iterator",o=Qs(t),i=Qs(r);en(e,t,(function(e,t){Js(this,{type:r,target:e,state:o(e),kind:t,last:void 0})}),(function(){for(var e=i(this),t=e.kind,n=e.last;n&&n.removed;)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?"keys"==t?{value:n.key,done:!1}:"values"==t?{value:n.value,done:!1}:{value:[n.key,n.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),n?"entries":"values",!n,!0),To(t)}}),"undefined"!=typeof t?t:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});Ks="undefined"!=typeof console?console:void 0!==Zs&&Zs.console?Zs.console:"undefined"!=typeof window&&window.console?window.console:{};for(var ea=function(){},ta=["assert","clear","count","debug","dir","dirxml","error","exception","group","groupCollapsed","groupEnd","info","log","markTimeline","profile","profileEnd","table","time","timeEnd","timeStamp","trace","warn"],na=ta.length;na--;)$s=ta[na],console[$s]||(Ks[$s]=ea);Ks.methods=ta;var ra=Ks,oa=0,ia=new Map;function sa(){var e=new Date;return"TIM "+e.toLocaleTimeString("en-US",{hour12:!1})+"."+function(e){var t;switch(e.toString().length){case 1:t="00"+e;break;case 2:t="0"+e;break;default:t=e}return t}(e.getMilliseconds())+":"}var aa={_data:[],_length:0,_visible:!1,arguments2String:function(e){var t;if(1===e.length)t=sa()+e[0];else{t=sa();for(var n=0,r=e.length;n<r;n++)ga(e[n])?va(e[n])?t+=Sa(e[n]):t+=JSON.stringify(e[n]):t+=e[n],t+=" "}return t},debug:function(){if(oa<=-1){var e=this.arguments2String(arguments);aa.record(e,"debug"),ra.debug(e)}},log:function(){if(oa<=0){var e=this.arguments2String(arguments);aa.record(e,"log"),ra.log(e)}},info:function(){if(oa<=1){var e=this.arguments2String(arguments);aa.record(e,"info"),ra.info(e)}},warn:function(){if(oa<=2){var e=this.arguments2String(arguments);aa.record(e,"warn"),ra.warn(e)}},error:function(){if(oa<=3){var e=this.arguments2String(arguments);aa.record(e,"error"),ra.error(e)}},time:function(e){ia.set(e,Ia.now())},timeEnd:function(e){if(ia.has(e)){var t=Ia.now()-ia.get(e);return ia.delete(e),t}return ra.warn("未找到对应label: ".concat(e,", 请在调用 logger.timeEnd 前，调用 logger.time")),0},setLevel:function(e){e<4&&ra.log(sa()+"set level from "+oa+" to "+e),oa=e},record:function(e,t){1100===aa._length&&(aa._data.splice(0,100),aa._length=1e3),aa._length++,aa._data.push("".concat(e," [").concat(t,"] \n"))},getLog:function(){return aa._data}},ua=function(e){return"file"===ya(e)},ca=function(e){return null!==e&&("number"==typeof e&&!isNaN(e-0)||"object"===kn(e)&&e.constructor===Number)},la=function(e){return"string"==typeof e},pa=function(e){return null!==e&&"object"===kn(e)},fa=function(e){if("object"!==kn(e)||null===e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return t===n},ha=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"array"===ya(e)},da=function(e){return void 0===e},ga=function(e){return ha(e)||pa(e)},ma=function(e){return"function"==typeof e},va=function(e){return e instanceof Error},ya=function(e){return Object.prototype.toString.call(e).match(/^\[object (.*)\]$/)[1].toLowerCase()},_a=function(e){if("string"!=typeof e)return!1;var t=e[0];return!/[^a-zA-Z0-9]/.test(t)},Ca=0;Date.now||(Date.now=function(){return(new Date).getTime()});var Ia={now:function(){0===Ca&&(Ca=Date.now()-1);var e=Date.now()-Ca;return e>4294967295?(Ca+=4294967295,Date.now()-Ca):e},utc:function(){return Math.round(Date.now()/1e3)}},Ma=function e(t,n,r,o){if(!ga(t)||!ga(n))return 0;for(var i,s=0,a=Object.keys(n),u=0,c=a.length;u<c;u++)if(i=a[u],!(da(n[i])||r&&r.includes(i)))if(ga(t[i])&&ga(n[i]))s+=e(t[i],n[i],r,o);else{if(o&&o.includes(n[i]))continue;t[i]!==n[i]&&(t[i]=n[i],s+=1)}return s},Sa=function(e){return JSON.stringify(e,["message","code"])},Ta=function(){var e=new Date,t=e.toISOString(),n=e.getTimezoneOffset()/60,r="";return r=n<0?n>-10?"+0"+Math.abs(100*n):"+"+Math.abs(100*n):n>=10?"-"+100*n:"-0"+100*n,t.replace("Z",r)},Ea=function(e){if(0===e.length)return 0;for(var t=0,n=0,r="undefined"!=typeof document&&void 0!==document.characterSet?document.characterSet:"UTF-8";void 0!==e[t];)n+=e[t++].charCodeAt[t]<=255?1:!1===r?3:2;return n},Da=function(e){var t=e||99999999;return Math.round(Math.random()*t)},ka="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",wa=ka.length,Aa=function(e,t){for(var n in e)if(e[n]===t)return!0;return!1},ba={},Ra=function(){if(Os)return"https:";var e=window.location.protocol;return["http:","https:"].indexOf(e)<0&&(e="http:"),e},Oa=function(e){return-1===e.indexOf("http://")||-1===e.indexOf("https://")?"https://"+e:e.replace(/https|http/,"https")};function La(e,t){ha(e)&&ha(t)?t.forEach((function(t){var n=t.key,r=t.value,o=e.find((function(e){return e.key===n}));o?o.value=r:e.push({key:n,value:r})})):aa.warn("updateCustomField target 或 source 不是数组，忽略此次更新。")}var Na=function(e){return e===fn.GRP_PUBLIC},Pa=function(e){return e===fn.GRP_AVCHATROOM},Ga=function(e){return la(e)&&e===fn.CONV_SYSTEM};function xa(e,t){var n={};return Object.keys(e).forEach((function(r){n[r]=t(e[r],r)})),n}var Ua=Object.prototype.hasOwnProperty;function qa(e){if(null==e)return!0;if("boolean"==typeof e)return!1;if("number"==typeof e)return 0===e;if("string"==typeof e)return 0===e.length;if("function"==typeof e)return 0===e.length;if(Array.isArray(e))return 0===e.length;if(e instanceof Error)return""===e.message;if(fa(e)){for(var t in e)if(Ua.call(e,t))return!1;return!0}return!("map"!==ya(e)&&!function(e){return"set"===ya(e)}(e)&&!ua(e))&&0===e.size}function Fa(e,t,n){if(void 0===t)return!0;var r=!0;if("object"===rr(t).toLowerCase())Object.keys(t).forEach((function(o){var i=1===e.length?e[0][o]:void 0;r=!!ja(i,t[o],n,o)&&r}));else if("array"===rr(t).toLowerCase())for(var o=0;o<t.length;o++)r=!!ja(e[o],t[o],n,t[o].name)&&r;if(r)return r;throw new Error("Params validate failed.")}function ja(e,t,n,r){if(void 0===t)return!0;var o=!0;return t.required&&qa(e)&&(ra.error("TIM [".concat(n,'] Missing required params: "').concat(r,'".')),o=!1),qa(e)||rr(e).toLowerCase()===t.type.toLowerCase()||(ra.error("TIM [".concat(n,'] Invalid params: type check failed for "').concat(r,'".Expected ').concat(t.type,".")),o=!1),t.validator&&!t.validator(e)&&(ra.error("TIM [".concat(n,"] Invalid params: custom validator check failed for params.")),o=!1),o}var Ba={f:Fe},Ha=A.f,Va=Ke.forEach,Ka=K("hidden"),$a=Fe("toPrimitive"),Ya=Z.set,za=Z.getterFor("Symbol"),Wa=Object.prototype,Xa=o.Symbol,Ja=re("JSON","stringify"),Qa=D.f,Za=A.f,eu=uo.f,tu=c.f,nu=F("symbols"),ru=F("op-symbols"),ou=F("string-to-symbol-registry"),iu=F("symbol-to-string-registry"),su=F("wks"),au=o.QObject,uu=!au||!au.prototype||!au.prototype.findChild,cu=s&&i((function(){return 7!=At(Za({},"a",{get:function(){return Za(this,"a",{value:7}).a}})).a}))?function(e,t,n){var r=Qa(Wa,t);r&&delete Wa[t],Za(e,t,n),r&&e!==Wa&&Za(Wa,t,r)}:Za,lu=function(e,t){var n=nu[e]=At(Xa.prototype);return Ya(n,{type:"Symbol",tag:e,description:t}),s||(n.description=t),n},pu=Ge?function(e){return"symbol"==typeof e}:function(e){return Object(e)instanceof Xa},fu=function(e,t,n){e===Wa&&fu(ru,t,n),k(e);var r=y(t,!0);return k(n),C(nu,r)?(n.enumerable?(C(e,Ka)&&e[Ka][r]&&(e[Ka][r]=!1),n=At(n,{enumerable:l(0,!1)})):(C(e,Ka)||Za(e,Ka,l(1,{})),e[Ka][r]=!0),cu(e,r,n)):Za(e,r,n)},hu=function(e,t){k(e);var n=m(t),r=Mt(n).concat(vu(n));return Va(r,(function(t){s&&!du.call(n,t)||fu(e,t,n[t])})),e},du=function(e){var t=y(e,!0),n=tu.call(this,t);return!(this===Wa&&C(nu,t)&&!C(ru,t))&&(!(n||!C(this,t)||!C(nu,t)||C(this,Ka)&&this[Ka][t])||n)},gu=function(e,t){var n=m(e),r=y(t,!0);if(n!==Wa||!C(nu,r)||C(ru,r)){var o=Qa(n,r);return!o||!C(nu,r)||C(n,Ka)&&n[Ka][r]||(o.enumerable=!0),o}},mu=function(e){var t=eu(m(e)),n=[];return Va(t,(function(e){C(nu,e)||C($,e)||n.push(e)})),n},vu=function(e){var t=e===Wa,n=eu(t?ru:m(e)),r=[];return Va(n,(function(e){!C(nu,e)||t&&!C(Wa,e)||r.push(nu[e])})),r};if(Pe||(ee((Xa=function(){if(this instanceof Xa)throw TypeError("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?String(arguments[0]):void 0,t=H(e),n=function(e){this===Wa&&n.call(ru,e),C(this,Ka)&&C(this[Ka],t)&&(this[Ka][t]=!1),cu(this,t,l(1,e))};return s&&uu&&cu(Wa,t,{configurable:!0,set:n}),lu(t,e)}).prototype,"toString",(function(){return za(this).tag})),ee(Xa,"withoutSetter",(function(e){return lu(H(e),e)})),c.f=du,A.f=fu,D.f=gu,ye.f=uo.f=mu,_e.f=vu,Ba.f=function(e){return lu(Fe(e),e)},s&&(Za(Xa.prototype,"description",{configurable:!0,get:function(){return za(this).description}}),ee(Wa,"propertyIsEnumerable",du,{unsafe:!0}))),be({global:!0,wrap:!0,forced:!Pe,sham:!Pe},{Symbol:Xa}),Va(Mt(su),(function(e){!function(e){var t=te.Symbol||(te.Symbol={});C(t,e)||Ha(t,e,{value:Ba.f(e)})}(e)})),be({target:"Symbol",stat:!0,forced:!Pe},{for:function(e){var t=String(e);if(C(ou,t))return ou[t];var n=Xa(t);return ou[t]=n,iu[n]=t,n},keyFor:function(e){if(!pu(e))throw TypeError(e+" is not a symbol");if(C(iu,e))return iu[e]},useSetter:function(){uu=!0},useSimple:function(){uu=!1}}),be({target:"Object",stat:!0,forced:!Pe,sham:!s},{create:function(e,t){return void 0===t?At(e):hu(At(e),t)},defineProperty:fu,defineProperties:hu,getOwnPropertyDescriptor:gu}),be({target:"Object",stat:!0,forced:!Pe},{getOwnPropertyNames:mu,getOwnPropertySymbols:vu}),be({target:"Object",stat:!0,forced:i((function(){_e.f(1)}))},{getOwnPropertySymbols:function(e){return _e.f(Le(e))}}),Ja){var yu=!Pe||i((function(){var e=Xa();return"[null]"!=Ja([e])||"{}"!=Ja({a:e})||"{}"!=Ja(Object(e))}));be({target:"JSON",stat:!0,forced:yu},{stringify:function(e,t,n){for(var r,o=[e],i=1;arguments.length>i;)o.push(arguments[i++]);if(r=t,(v(t)||void 0!==e)&&!pu(e))return Ne(t)||(t=function(e,t){if("function"==typeof r&&(t=r.call(this,e,t)),!pu(t))return t}),o[1]=t,Ja.apply(null,o)}})}Xa.prototype[$a]||b(Xa.prototype,$a,Xa.prototype.valueOf),Kt(Xa,"Symbol"),$[Ka]=!0;var _u=A.f,Cu=o.Symbol;if(s&&"function"==typeof Cu&&(!("description"in Cu.prototype)||void 0!==Cu().description)){var Iu={},Mu=function(){var e=arguments.length<1||void 0===arguments[0]?void 0:String(arguments[0]),t=this instanceof Mu?new Cu(e):void 0===e?Cu():Cu(e);return""===e&&(Iu[t]=!0),t};Ie(Mu,Cu);var Su=Mu.prototype=Cu.prototype;Su.constructor=Mu;var Tu=Su.toString,Eu="Symbol(test)"==String(Cu("test")),Du=/^Symbol\((.*)\)[^)]+$/;_u(Su,"description",{configurable:!0,get:function(){var e=v(this)?this.valueOf():this,t=Tu.call(e);if(C(Iu,e))return"";var n=Eu?t.slice(7,-1):t.replace(Du,"$1");return""===n?void 0:n}}),be({global:!0,forced:!0},{Symbol:Mu})}var ku=c.f,wu=function(e){return function(t){for(var n,r=m(t),o=Mt(r),i=o.length,a=0,u=[];i>a;)n=o[a++],s&&!ku.call(r,n)||u.push(e?[n,r[n]]:r[n]);return u}},Au={entries:wu(!0),values:wu(!1)}.values;be({target:"Object",stat:!0},{values:function(e){return Au(e)}});var bu={SUCCESS:"JoinedSuccess",WAIT_APPROVAL:"WaitAdminApproval"},Ru={SUCCESS:0},Ou={IS_LOGIN:1,IS_NOT_LOGIN:0},Lu={UNSEND:"unSend",SUCCESS:"success",FAIL:"fail"},Nu={NOT_START:"notStart",PENDING:"pengding",RESOLVED:"resolved",REJECTED:"rejected"},Pu=function(){function e(t){wn(this,e),this.type=fn.MSG_TEXT,this.content={text:t.text||""}}return bn(e,[{key:"setText",value:function(e){this.content.text=e}},{key:"sendable",value:function(){return 0!==this.content.text.length}}]),e}(),Gu=Fe("iterator"),xu=!i((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),t=e.searchParams,n="";return e.pathname="c%20d",t.forEach((function(e,r){t.delete("b"),n+=r+e})),!t.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[Gu]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==n||"x"!==new URL("http://x",void 0).host})),Uu=Object.assign,qu=Object.defineProperty,Fu=!Uu||i((function(){if(s&&1!==Uu({b:1},Uu(qu({},"a",{enumerable:!0,get:function(){qu(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol();return e[n]=7,"abcdefghijklmnopqrst".split("").forEach((function(e){t[e]=e})),7!=Uu({},e)[n]||"abcdefghijklmnopqrst"!=Mt(Uu({},t)).join("")}))?function(e,t){for(var n=Le(e),r=arguments.length,o=1,i=_e.f,a=c.f;r>o;)for(var u,l=d(arguments[o++]),p=i?Mt(l).concat(i(l)):Mt(l),f=p.length,h=0;f>h;)u=p[h++],s&&!a.call(l,u)||(n[u]=l[u]);return n}:Uu,ju=/[^\0-\u007E]/,Bu=/[.\u3002\uFF0E\uFF61]/g,Hu="Overflow: input needs wider integers to process",Vu=Math.floor,Ku=String.fromCharCode,$u=function(e){return e+22+75*(e<26)},Yu=function(e,t,n){var r=0;for(e=n?Vu(e/700):e>>1,e+=Vu(e/t);e>455;r+=36)e=Vu(e/35);return Vu(r+36*e/(e+38))},zu=function(e){var t,n,r=[],o=(e=function(e){for(var t=[],n=0,r=e.length;n<r;){var o=e.charCodeAt(n++);if(o>=55296&&o<=56319&&n<r){var i=e.charCodeAt(n++);56320==(64512&i)?t.push(((1023&o)<<10)+(1023&i)+65536):(t.push(o),n--)}else t.push(o)}return t}(e)).length,i=128,s=0,a=72;for(t=0;t<e.length;t++)(n=e[t])<128&&r.push(Ku(n));var u=r.length,c=u;for(u&&r.push("-");c<o;){var l=2147483647;for(t=0;t<e.length;t++)(n=e[t])>=i&&n<l&&(l=n);var p=c+1;if(l-i>Vu((2147483647-s)/p))throw RangeError(Hu);for(s+=(l-i)*p,i=l,t=0;t<e.length;t++){if((n=e[t])<i&&++s>2147483647)throw RangeError(Hu);if(n==i){for(var f=s,h=36;;h+=36){var d=h<=a?1:h>=a+26?26:h-a;if(f<d)break;var g=f-d,m=36-d;r.push(Ku($u(d+g%m))),f=Vu(g/m)}r.push(Ku($u(f))),a=Yu(s,p,c==u),s=0,++c}}++s,++i}return r.join("")},Wu=function(e){var t=ht(e);if("function"!=typeof t)throw TypeError(String(e)+" is not iterable");return k(t.call(e))},Xu=re("fetch"),Ju=re("Headers"),Qu=Fe("iterator"),Zu=Z.set,ec=Z.getterFor("URLSearchParams"),tc=Z.getterFor("URLSearchParamsIterator"),nc=/\+/g,rc=Array(4),oc=function(e){return rc[e-1]||(rc[e-1]=RegExp("((?:%[\\da-f]{2}){"+e+"})","gi"))},ic=function(e){try{return decodeURIComponent(e)}catch(dC){return e}},sc=function(e){var t=e.replace(nc," "),n=4;try{return decodeURIComponent(t)}catch(dC){for(;n;)t=t.replace(oc(n--),ic);return t}},ac=/[!'()~]|%20/g,uc={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},cc=function(e){return uc[e]},lc=function(e){return encodeURIComponent(e).replace(ac,cc)},pc=function(e,t){if(t)for(var n,r,o=t.split("&"),i=0;i<o.length;)(n=o[i++]).length&&(r=n.split("="),e.push({key:sc(r.shift()),value:sc(r.join("="))}))},fc=function(e){this.entries.length=0,pc(this.entries,e)},hc=function(e,t){if(e<t)throw TypeError("Not enough arguments")},dc=zt((function(e,t){Zu(this,{type:"URLSearchParamsIterator",iterator:Wu(ec(e).entries),kind:t})}),"Iterator",(function(){var e=tc(this),t=e.kind,n=e.iterator.next(),r=n.value;return n.done||(n.value="keys"===t?r.key:"values"===t?r.value:[r.key,r.value]),n})),gc=function(){Eo(this,gc,"URLSearchParams");var e,t,n,r,o,i,s,a,u,c=arguments.length>0?arguments[0]:void 0,l=this,p=[];if(Zu(l,{type:"URLSearchParams",entries:p,updateURL:function(){},updateSearchParams:fc}),void 0!==c)if(v(c))if("function"==typeof(e=ht(c)))for(n=(t=e.call(c)).next;!(r=n.call(t)).done;){if((s=(i=(o=Wu(k(r.value))).next).call(o)).done||(a=i.call(o)).done||!i.call(o).done)throw TypeError("Expected sequence with length 2");p.push({key:s.value+"",value:a.value+""})}else for(u in c)C(c,u)&&p.push({key:u,value:c[u]+""});else pc(p,"string"==typeof c?"?"===c.charAt(0)?c.slice(1):c:c+"")},mc=gc.prototype;Mo(mc,{append:function(e,t){hc(arguments.length,2);var n=ec(this);n.entries.push({key:e+"",value:t+""}),n.updateURL()},delete:function(e){hc(arguments.length,1);for(var t=ec(this),n=t.entries,r=e+"",o=0;o<n.length;)n[o].key===r?n.splice(o,1):o++;t.updateURL()},get:function(e){hc(arguments.length,1);for(var t=ec(this).entries,n=e+"",r=0;r<t.length;r++)if(t[r].key===n)return t[r].value;return null},getAll:function(e){hc(arguments.length,1);for(var t=ec(this).entries,n=e+"",r=[],o=0;o<t.length;o++)t[o].key===n&&r.push(t[o].value);return r},has:function(e){hc(arguments.length,1);for(var t=ec(this).entries,n=e+"",r=0;r<t.length;)if(t[r++].key===n)return!0;return!1},set:function(e,t){hc(arguments.length,1);for(var n,r=ec(this),o=r.entries,i=!1,s=e+"",a=t+"",u=0;u<o.length;u++)(n=o[u]).key===s&&(i?o.splice(u--,1):(i=!0,n.value=a));i||o.push({key:s,value:a}),r.updateURL()},sort:function(){var e,t,n,r=ec(this),o=r.entries,i=o.slice();for(o.length=0,n=0;n<i.length;n++){for(e=i[n],t=0;t<n;t++)if(o[t].key>e.key){o.splice(t,0,e);break}t===n&&o.push(e)}r.updateURL()},forEach:function(e){for(var t,n=ec(this).entries,r=Oe(e,arguments.length>1?arguments[1]:void 0,3),o=0;o<n.length;)r((t=n[o++]).value,t.key,this)},keys:function(){return new dc(this,"keys")},values:function(){return new dc(this,"values")},entries:function(){return new dc(this,"entries")}},{enumerable:!0}),ee(mc,Qu,mc.entries),ee(mc,"toString",(function(){for(var e,t=ec(this).entries,n=[],r=0;r<t.length;)e=t[r++],n.push(lc(e.key)+"="+lc(e.value));return n.join("&")}),{enumerable:!0}),Kt(gc,"URLSearchParams"),be({global:!0,forced:!xu},{URLSearchParams:gc}),xu||"function"!=typeof Xu||"function"!=typeof Ju||be({global:!0,enumerable:!0,forced:!0},{fetch:function(e){var t,n,r,o=[e];return arguments.length>1&&(t=arguments[1],v(t)&&(n=t.body,"URLSearchParams"===pt(n)&&((r=t.headers?new Ju(t.headers):new Ju).has("content-type")||r.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),t=At(t,{body:l(0,String(n)),headers:l(0,r)}))),o.push(t)),Xu.apply(this,o)}});var vc,yc={URLSearchParams:gc,getState:ec},_c=Pt.codeAt,Cc=o.URL,Ic=yc.URLSearchParams,Mc=yc.getState,Sc=Z.set,Tc=Z.getterFor("URL"),Ec=Math.floor,Dc=Math.pow,kc=/[A-Za-z]/,wc=/[\d+\-.A-Za-z]/,Ac=/\d/,bc=/^(0x|0X)/,Rc=/^[0-7]+$/,Oc=/^\d+$/,Lc=/^[\dA-Fa-f]+$/,Nc=/[\u0000\u0009\u000A\u000D #%/:?@[\\]]/,Pc=/[\u0000\u0009\u000A\u000D #/:?@[\\]]/,Gc=/^[\u0000-\u001F ]+|[\u0000-\u001F ]+$/g,xc=/[\u0009\u000A\u000D]/g,Uc=function(e,t){var n,r,o;if("["==t.charAt(0)){if("]"!=t.charAt(t.length-1))return"Invalid host";if(!(n=Fc(t.slice(1,-1))))return"Invalid host";e.host=n}else if(zc(e)){if(t=function(e){var t,n,r=[],o=e.toLowerCase().replace(Bu,".").split(".");for(t=0;t<o.length;t++)n=o[t],r.push(ju.test(n)?"xn--"+zu(n):n);return r.join(".")}(t),Nc.test(t))return"Invalid host";if(null===(n=qc(t)))return"Invalid host";e.host=n}else{if(Pc.test(t))return"Invalid host";for(n="",r=dt(t),o=0;o<r.length;o++)n+=$c(r[o],Bc);e.host=n}},qc=function(e){var t,n,r,o,i,s,a,u=e.split(".");if(u.length&&""==u[u.length-1]&&u.pop(),(t=u.length)>4)return e;for(n=[],r=0;r<t;r++){if(""==(o=u[r]))return e;if(i=10,o.length>1&&"0"==o.charAt(0)&&(i=bc.test(o)?16:8,o=o.slice(8==i?1:2)),""===o)s=0;else{if(!(10==i?Oc:8==i?Rc:Lc).test(o))return e;s=parseInt(o,i)}n.push(s)}for(r=0;r<t;r++)if(s=n[r],r==t-1){if(s>=Dc(256,5-t))return null}else if(s>255)return null;for(a=n.pop(),r=0;r<n.length;r++)a+=n[r]*Dc(256,3-r);return a},Fc=function(e){var t,n,r,o,i,s,a,u=[0,0,0,0,0,0,0,0],c=0,l=null,p=0,f=function(){return e.charAt(p)};if(":"==f()){if(":"!=e.charAt(1))return;p+=2,l=++c}for(;f();){if(8==c)return;if(":"!=f()){for(t=n=0;n<4&&Lc.test(f());)t=16*t+parseInt(f(),16),p++,n++;if("."==f()){if(0==n)return;if(p-=n,c>6)return;for(r=0;f();){if(o=null,r>0){if(!("."==f()&&r<4))return;p++}if(!Ac.test(f()))return;for(;Ac.test(f());){if(i=parseInt(f(),10),null===o)o=i;else{if(0==o)return;o=10*o+i}if(o>255)return;p++}u[c]=256*u[c]+o,2!=++r&&4!=r||c++}if(4!=r)return;break}if(":"==f()){if(p++,!f())return}else if(f())return;u[c++]=t}else{if(null!==l)return;p++,l=++c}}if(null!==l)for(s=c-l,c=7;0!=c&&s>0;)a=u[c],u[c--]=u[l+s-1],u[l+--s]=a;else if(8!=c)return;return u},jc=function(e){var t,n,r,o;if("number"==typeof e){for(t=[],n=0;n<4;n++)t.unshift(e%256),e=Ec(e/256);return t.join(".")}if("object"==typeof e){for(t="",r=function(e){for(var t=null,n=1,r=null,o=0,i=0;i<8;i++)0!==e[i]?(o>n&&(t=r,n=o),r=null,o=0):(null===r&&(r=i),++o);return o>n&&(t=r,n=o),t}(e),n=0;n<8;n++)o&&0===e[n]||(o&&(o=!1),r===n?(t+=n?":":"::",o=!0):(t+=e[n].toString(16),n<7&&(t+=":")));return"["+t+"]"}return e},Bc={},Hc=Fu({},Bc,{" ":1,'"':1,"<":1,">":1,"`":1}),Vc=Fu({},Hc,{"#":1,"?":1,"{":1,"}":1}),Kc=Fu({},Vc,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),$c=function(e,t){var n=_c(e,0);return n>32&&n<127&&!C(t,e)?e:encodeURIComponent(e)},Yc={ftp:21,file:null,http:80,https:443,ws:80,wss:443},zc=function(e){return C(Yc,e.scheme)},Wc=function(e){return""!=e.username||""!=e.password},Xc=function(e){return!e.host||e.cannotBeABaseURL||"file"==e.scheme},Jc=function(e,t){var n;return 2==e.length&&kc.test(e.charAt(0))&&(":"==(n=e.charAt(1))||!t&&"|"==n)},Qc=function(e){var t;return e.length>1&&Jc(e.slice(0,2))&&(2==e.length||"/"===(t=e.charAt(2))||"\\"===t||"?"===t||"#"===t)},Zc=function(e){var t=e.path,n=t.length;!n||"file"==e.scheme&&1==n&&Jc(t[0],!0)||t.pop()},el=function(e){return"."===e||"%2e"===e.toLowerCase()},tl={},nl={},rl={},ol={},il={},sl={},al={},ul={},cl={},ll={},pl={},fl={},hl={},dl={},gl={},ml={},vl={},yl={},_l={},Cl={},Il={},Ml=function(e,t,n,r){var o,i,s,a,u,c=n||tl,l=0,p="",f=!1,h=!1,d=!1;for(n||(e.scheme="",e.username="",e.password="",e.host=null,e.port=null,e.path=[],e.query=null,e.fragment=null,e.cannotBeABaseURL=!1,t=t.replace(Gc,"")),t=t.replace(xc,""),o=dt(t);l<=o.length;){switch(i=o[l],c){case tl:if(!i||!kc.test(i)){if(n)return"Invalid scheme";c=rl;continue}p+=i.toLowerCase(),c=nl;break;case nl:if(i&&(wc.test(i)||"+"==i||"-"==i||"."==i))p+=i.toLowerCase();else{if(":"!=i){if(n)return"Invalid scheme";p="",c=rl,l=0;continue}if(n&&(zc(e)!=C(Yc,p)||"file"==p&&(Wc(e)||null!==e.port)||"file"==e.scheme&&!e.host))return;if(e.scheme=p,n)return void(zc(e)&&Yc[e.scheme]==e.port&&(e.port=null));p="","file"==e.scheme?c=dl:zc(e)&&r&&r.scheme==e.scheme?c=ol:zc(e)?c=ul:"/"==o[l+1]?(c=il,l++):(e.cannotBeABaseURL=!0,e.path.push(""),c=_l)}break;case rl:if(!r||r.cannotBeABaseURL&&"#"!=i)return"Invalid scheme";if(r.cannotBeABaseURL&&"#"==i){e.scheme=r.scheme,e.path=r.path.slice(),e.query=r.query,e.fragment="",e.cannotBeABaseURL=!0,c=Il;break}c="file"==r.scheme?dl:sl;continue;case ol:if("/"!=i||"/"!=o[l+1]){c=sl;continue}c=cl,l++;break;case il:if("/"==i){c=ll;break}c=yl;continue;case sl:if(e.scheme=r.scheme,i==vc)e.username=r.username,e.password=r.password,e.host=r.host,e.port=r.port,e.path=r.path.slice(),e.query=r.query;else if("/"==i||"\\"==i&&zc(e))c=al;else if("?"==i)e.username=r.username,e.password=r.password,e.host=r.host,e.port=r.port,e.path=r.path.slice(),e.query="",c=Cl;else{if("#"!=i){e.username=r.username,e.password=r.password,e.host=r.host,e.port=r.port,e.path=r.path.slice(),e.path.pop(),c=yl;continue}e.username=r.username,e.password=r.password,e.host=r.host,e.port=r.port,e.path=r.path.slice(),e.query=r.query,e.fragment="",c=Il}break;case al:if(!zc(e)||"/"!=i&&"\\"!=i){if("/"!=i){e.username=r.username,e.password=r.password,e.host=r.host,e.port=r.port,c=yl;continue}c=ll}else c=cl;break;case ul:if(c=cl,"/"!=i||"/"!=p.charAt(l+1))continue;l++;break;case cl:if("/"!=i&&"\\"!=i){c=ll;continue}break;case ll:if("@"==i){f&&(p="%40"+p),f=!0,s=dt(p);for(var g=0;g<s.length;g++){var m=s[g];if(":"!=m||d){var v=$c(m,Kc);d?e.password+=v:e.username+=v}else d=!0}p=""}else if(i==vc||"/"==i||"?"==i||"#"==i||"\\"==i&&zc(e)){if(f&&""==p)return"Invalid authority";l-=dt(p).length+1,p="",c=pl}else p+=i;break;case pl:case fl:if(n&&"file"==e.scheme){c=ml;continue}if(":"!=i||h){if(i==vc||"/"==i||"?"==i||"#"==i||"\\"==i&&zc(e)){if(zc(e)&&""==p)return"Invalid host";if(n&&""==p&&(Wc(e)||null!==e.port))return;if(a=Uc(e,p))return a;if(p="",c=vl,n)return;continue}"["==i?h=!0:"]"==i&&(h=!1),p+=i}else{if(""==p)return"Invalid host";if(a=Uc(e,p))return a;if(p="",c=hl,n==fl)return}break;case hl:if(!Ac.test(i)){if(i==vc||"/"==i||"?"==i||"#"==i||"\\"==i&&zc(e)||n){if(""!=p){var y=parseInt(p,10);if(y>65535)return"Invalid port";e.port=zc(e)&&y===Yc[e.scheme]?null:y,p=""}if(n)return;c=vl;continue}return"Invalid port"}p+=i;break;case dl:if(e.scheme="file","/"==i||"\\"==i)c=gl;else{if(!r||"file"!=r.scheme){c=yl;continue}if(i==vc)e.host=r.host,e.path=r.path.slice(),e.query=r.query;else if("?"==i)e.host=r.host,e.path=r.path.slice(),e.query="",c=Cl;else{if("#"!=i){Qc(o.slice(l).join(""))||(e.host=r.host,e.path=r.path.slice(),Zc(e)),c=yl;continue}e.host=r.host,e.path=r.path.slice(),e.query=r.query,e.fragment="",c=Il}}break;case gl:if("/"==i||"\\"==i){c=ml;break}r&&"file"==r.scheme&&!Qc(o.slice(l).join(""))&&(Jc(r.path[0],!0)?e.path.push(r.path[0]):e.host=r.host),c=yl;continue;case ml:if(i==vc||"/"==i||"\\"==i||"?"==i||"#"==i){if(!n&&Jc(p))c=yl;else if(""==p){if(e.host="",n)return;c=vl}else{if(a=Uc(e,p))return a;if("localhost"==e.host&&(e.host=""),n)return;p="",c=vl}continue}p+=i;break;case vl:if(zc(e)){if(c=yl,"/"!=i&&"\\"!=i)continue}else if(n||"?"!=i)if(n||"#"!=i){if(i!=vc&&(c=yl,"/"!=i))continue}else e.fragment="",c=Il;else e.query="",c=Cl;break;case yl:if(i==vc||"/"==i||"\\"==i&&zc(e)||!n&&("?"==i||"#"==i)){if(".."===(u=(u=p).toLowerCase())||"%2e."===u||".%2e"===u||"%2e%2e"===u?(Zc(e),"/"==i||"\\"==i&&zc(e)||e.path.push("")):el(p)?"/"==i||"\\"==i&&zc(e)||e.path.push(""):("file"==e.scheme&&!e.path.length&&Jc(p)&&(e.host&&(e.host=""),p=p.charAt(0)+":"),e.path.push(p)),p="","file"==e.scheme&&(i==vc||"?"==i||"#"==i))for(;e.path.length>1&&""===e.path[0];)e.path.shift();"?"==i?(e.query="",c=Cl):"#"==i&&(e.fragment="",c=Il)}else p+=$c(i,Vc);break;case _l:"?"==i?(e.query="",c=Cl):"#"==i?(e.fragment="",c=Il):i!=vc&&(e.path[0]+=$c(i,Bc));break;case Cl:n||"#"!=i?i!=vc&&("'"==i&&zc(e)?e.query+="%27":e.query+="#"==i?"%23":$c(i,Bc)):(e.fragment="",c=Il);break;case Il:i!=vc&&(e.fragment+=$c(i,Hc))}l++}},Sl=function(e){var t,n,r=Eo(this,Sl,"URL"),o=arguments.length>1?arguments[1]:void 0,i=String(e),a=Sc(r,{type:"URL"});if(void 0!==o)if(o instanceof Sl)t=Tc(o);else if(n=Ml(t={},String(o)))throw TypeError(n);if(n=Ml(a,i,null,t))throw TypeError(n);var u=a.searchParams=new Ic,c=Mc(u);c.updateSearchParams(a.query),c.updateURL=function(){a.query=String(u)||null},s||(r.href=El.call(r),r.origin=Dl.call(r),r.protocol=kl.call(r),r.username=wl.call(r),r.password=Al.call(r),r.host=bl.call(r),r.hostname=Rl.call(r),r.port=Ol.call(r),r.pathname=Ll.call(r),r.search=Nl.call(r),r.searchParams=Pl.call(r),r.hash=Gl.call(r))},Tl=Sl.prototype,El=function(){var e=Tc(this),t=e.scheme,n=e.username,r=e.password,o=e.host,i=e.port,s=e.path,a=e.query,u=e.fragment,c=t+":";return null!==o?(c+="//",Wc(e)&&(c+=n+(r?":"+r:"")+"@"),c+=jc(o),null!==i&&(c+=":"+i)):"file"==t&&(c+="//"),c+=e.cannotBeABaseURL?s[0]:s.length?"/"+s.join("/"):"",null!==a&&(c+="?"+a),null!==u&&(c+="#"+u),c},Dl=function(){var e=Tc(this),t=e.scheme,n=e.port;if("blob"==t)try{return new URL(t.path[0]).origin}catch(dC){return"null"}return"file"!=t&&zc(e)?t+"://"+jc(e.host)+(null!==n?":"+n:""):"null"},kl=function(){return Tc(this).scheme+":"},wl=function(){return Tc(this).username},Al=function(){return Tc(this).password},bl=function(){var e=Tc(this),t=e.host,n=e.port;return null===t?"":null===n?jc(t):jc(t)+":"+n},Rl=function(){var e=Tc(this).host;return null===e?"":jc(e)},Ol=function(){var e=Tc(this).port;return null===e?"":String(e)},Ll=function(){var e=Tc(this),t=e.path;return e.cannotBeABaseURL?t[0]:t.length?"/"+t.join("/"):""},Nl=function(){var e=Tc(this).query;return e?"?"+e:""},Pl=function(){return Tc(this).searchParams},Gl=function(){var e=Tc(this).fragment;return e?"#"+e:""},xl=function(e,t){return{get:e,set:t,configurable:!0,enumerable:!0}};if(s&&St(Tl,{href:xl(El,(function(e){var t=Tc(this),n=String(e),r=Ml(t,n);if(r)throw TypeError(r);Mc(t.searchParams).updateSearchParams(t.query)})),origin:xl(Dl),protocol:xl(kl,(function(e){var t=Tc(this);Ml(t,String(e)+":",tl)})),username:xl(wl,(function(e){var t=Tc(this),n=dt(String(e));if(!Xc(t)){t.username="";for(var r=0;r<n.length;r++)t.username+=$c(n[r],Kc)}})),password:xl(Al,(function(e){var t=Tc(this),n=dt(String(e));if(!Xc(t)){t.password="";for(var r=0;r<n.length;r++)t.password+=$c(n[r],Kc)}})),host:xl(bl,(function(e){var t=Tc(this);t.cannotBeABaseURL||Ml(t,String(e),pl)})),hostname:xl(Rl,(function(e){var t=Tc(this);t.cannotBeABaseURL||Ml(t,String(e),fl)})),port:xl(Ol,(function(e){var t=Tc(this);Xc(t)||(""==(e=String(e))?t.port=null:Ml(t,e,hl))})),pathname:xl(Ll,(function(e){var t=Tc(this);t.cannotBeABaseURL||(t.path=[],Ml(t,e+"",vl))})),search:xl(Nl,(function(e){var t=Tc(this);""==(e=String(e))?t.query=null:("?"==e.charAt(0)&&(e=e.slice(1)),t.query="",Ml(t,e,Cl)),Mc(t.searchParams).updateSearchParams(t.query)})),searchParams:xl(Pl),hash:xl(Gl,(function(e){var t=Tc(this);""!=(e=String(e))?("#"==e.charAt(0)&&(e=e.slice(1)),t.fragment="",Ml(t,e,Il)):t.fragment=null}))}),ee(Tl,"toJSON",(function(){return El.call(this)}),{enumerable:!0}),ee(Tl,"toString",(function(){return El.call(this)}),{enumerable:!0}),Cc){var Ul=Cc.createObjectURL,ql=Cc.revokeObjectURL;Ul&&ee(Sl,"createObjectURL",(function(e){return Ul.apply(Cc,arguments)})),ql&&ee(Sl,"revokeObjectURL",(function(e){return ql.apply(Cc,arguments)}))}Kt(Sl,"URL"),be({global:!0,forced:!xu,sham:!s},{URL:Sl});var Fl={JSON:{TYPE:{C2C:{NOTICE:1,COMMON:9,EVENT:10},GROUP:{COMMON:3,TIP:4,SYSTEM:5,TIP2:6},FRIEND:{NOTICE:7},PROFILE:{NOTICE:8}},SUBTYPE:{C2C:{COMMON:0,READED:92,KICKEDOUT:96},GROUP:{COMMON:0,LOVEMESSAGE:1,TIP:2,REDPACKET:3}},OPTIONS:{GROUP:{JOIN:1,QUIT:2,KICK:3,SET_ADMIN:4,CANCEL_ADMIN:5,MODIFY_GROUP_INFO:6,MODIFY_MEMBER_INFO:7}}},PROTOBUF:{},IMAGE_TYPES:{ORIGIN:1,LARGE:2,SMALL:3},IMAGE_FORMAT:{JPG:1,JPEG:1,GIF:2,PNG:3,BMP:4,UNKNOWN:255}},jl=1,Bl=2,Hl=3,Vl=4,Kl=5,$l=7,Yl=8,zl=9,Wl=10,Xl=15,Jl=255,Ql=2,Zl=0,ep=1,tp={NICK:"Tag_Profile_IM_Nick",GENDER:"Tag_Profile_IM_Gender",BIRTHDAY:"Tag_Profile_IM_BirthDay",LOCATION:"Tag_Profile_IM_Location",SELFSIGNATURE:"Tag_Profile_IM_SelfSignature",ALLOWTYPE:"Tag_Profile_IM_AllowType",LANGUAGE:"Tag_Profile_IM_Language",AVATAR:"Tag_Profile_IM_Image",MESSAGESETTINGS:"Tag_Profile_IM_MsgSettings",ADMINFORBIDTYPE:"Tag_Profile_IM_AdminForbidType",LEVEL:"Tag_Profile_IM_Level",ROLE:"Tag_Profile_IM_Role"},np={UNKNOWN:"Gender_Type_Unknown",FEMALE:"Gender_Type_Female",MALE:"Gender_Type_Male"},rp={NONE:"AdminForbid_Type_None",SEND_OUT:"AdminForbid_Type_SendOut"},op={NEED_CONFIRM:"AllowType_Type_NeedConfirm",ALLOW_ANY:"AllowType_Type_AllowAny",DENY_ANY:"AllowType_Type_DenyAny"},ip=function(){function e(t){wn(this,e),this._imageMemoryURL="",this._file=t.file,Os?this.createImageDataASURLInWXMiniApp(t.file):this.createImageDataASURLInWeb(t.file),this._initImageInfoModel(),this.type=fn.MSG_IMAGE,this._percent=0,this.content={imageFormat:Fl.IMAGE_FORMAT[t.imageFormat]||Fl.IMAGE_FORMAT.UNKNOWN,uuid:t.uuid,imageInfoArray:[]},this.initImageInfoArray(t.imageInfoArray),this._defaultImage="http://imgcache.qq.com/open/qcloud/video/act/webim-images/default.jpg",this._autoFixUrl()}return bn(e,[{key:"_initImageInfoModel",value:function(){var e=this;this._ImageInfoModel=function(t){this.instanceID=Da(9999999),this.sizeType=t.type||0,this.size=t.size||0,this.width=t.width||0,this.height=t.height||0,this.imageUrl=t.url||"",this.url=t.url||e._imageMemoryURL||e._defaultImage},this._ImageInfoModel.prototype={setSizeType:function(e){this.sizeType=e},setImageUrl:function(e){e&&(this.imageUrl=e)},getImageUrl:function(){return this.imageUrl}}}},{key:"initImageInfoArray",value:function(e){for(var t=2,n=null,r=null;t>=0;)r=void 0===e||void 0===e[t]?{type:0,size:0,width:0,height:0,url:""}:e[t],(n=new this._ImageInfoModel(r)).setSizeType(t+1),this.addImageInfo(n),t--}},{key:"updateImageInfoArray",value:function(e){for(var t,n=this.content.imageInfoArray.length,r=0;r<n;r++)t=this.content.imageInfoArray[r],e.size&&(t.size=e.size),e.url&&t.setImageUrl(e.url),e.width&&(t.width=e.width),e.height&&(t.height=e.height)}},{key:"_autoFixUrl",value:function(){for(var e=this.content.imageInfoArray.length,t="",n="",r=["http","https"],o=null,i=0;i<e;i++)this.content.imageInfoArray[i].url&&""!==(o=this.content.imageInfoArray[i]).imageUrl&&(n=o.imageUrl.slice(0,o.imageUrl.indexOf("://")+1),t=o.imageUrl.slice(o.imageUrl.indexOf("://")+1),r.indexOf(n)<0&&(n="https:"),this.content.imageInfoArray[i].setImageUrl([n,t].join("")))}},{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateImageFormat",value:function(e){this.content.imageFormat=e}},{key:"createImageDataASURLInWeb",value:function(e){void 0!==e&&e.files.length>0&&(this._imageMemoryURL=window.URL.createObjectURL(e.files[0]))}},{key:"createImageDataASURLInWXMiniApp",value:function(e){e&&e.url&&(this._imageMemoryURL=e.url)}},{key:"replaceImageInfo",value:function(e,t){this.content.imageInfoArray[t]instanceof this._ImageInfoModel||(this.content.imageInfoArray[t]=e)}},{key:"addImageInfo",value:function(e){this.content.imageInfoArray.length>=3||this.content.imageInfoArray.push(e)}},{key:"sendable",value:function(){return 0!==this.content.imageInfoArray.length&&""!==this.content.imageInfoArray[0].imageUrl&&0!==this.content.imageInfoArray[0].size}}]),e}(),sp=function(){function e(t){wn(this,e),this.type=fn.MSG_FACE,this.content=t||null}return bn(e,[{key:"sendable",value:function(){return null!==this.content}}]),e}(),ap=function(){function e(t){wn(this,e),this.type=fn.MSG_AUDIO,this._percent=0,this.content={downloadFlag:2,second:t.second,size:t.size,url:t.url,remoteAudioUrl:"",uuid:t.uuid}}return bn(e,[{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateAudioUrl",value:function(e){this.content.remoteAudioUrl=e}},{key:"sendable",value:function(){return""!==this.content.remoteAudioUrl}}]),e}();be({target:"Object",stat:!0,forced:!s,sham:!s},{defineProperty:A.f});var up={from:!0,groupID:!0,groupName:!0,to:!0},cp=function(){function e(t){wn(this,e),this.type=fn.MSG_GRP_TIP,this.content={},this._initContent(t)}return bn(e,[{key:"_initContent",value:function(e){var t=this;Object.keys(e).forEach((function(n){switch(n){case"remarkInfo":break;case"groupProfile":t.content.groupProfile={},t._initGroupProfile(e[n]);break;case"operatorInfo":case"memberInfoList":break;case"msgMemberInfo":t.content.memberList=e[n],Object.defineProperty(t.content,"msgMemberInfo",{get:function(){return aa.warn("!!! 禁言的群提示消息中的 payload.msgMemberInfo 属性即将废弃，请使用 payload.memberList 属性替代。 \n","msgMemberInfo 中的 shutupTime 属性对应更改为 memberList 中的 muteTime 属性，表示禁言时长。 \n","参考：群提示消息 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/Message.html#.GroupTipPayload"),t.content.memberList.map((function(e){return{userID:e.userID,shutupTime:e.muteTime}}))}});break;default:t.content[n]=e[n]}})),this.content.userIDList||(this.content.userIDList=[this.content.operatorID])}},{key:"_initGroupProfile",value:function(e){for(var t=Object.keys(e),n=0;n<t.length;n++){var r=t[n];up[r]&&(this.content.groupProfile[r]=e[r])}}}]),e}(),lp={from:!0,groupID:!0,name:!0,to:!0},pp=function(){function e(t){wn(this,e),this.type=fn.MSG_GRP_SYS_NOTICE,this.content={},this._initContent(t)}return bn(e,[{key:"_initContent",value:function(e){var t=this;Object.keys(e).forEach((function(n){switch(n){case"memberInfoList":break;case"remarkInfo":t.content.handleMessage=e[n];break;case"groupProfile":t.content.groupProfile={},t._initGroupProfile(e[n]);break;default:t.content[n]=e[n]}}))}},{key:"_initGroupProfile",value:function(e){for(var t=Object.keys(e),n=0;n<t.length;n++){var r=t[n];lp[r]&&(this.content.groupProfile[r]=e[r])}}}]),e}(),fp=Math.min,hp=[].lastIndexOf,dp=!!hp&&1/[1].lastIndexOf(1,-0)<0,gp=$e("lastIndexOf"),mp=Xe("indexOf",{ACCESSORS:!0,1:0}),vp=!dp&&gp&&mp?hp:function(e){if(dp)return hp.apply(this,arguments)||0;var t=m(this),n=ue(t.length),r=n-1;for(arguments.length>1&&(r=fp(r,se(arguments[1]))),r<0&&(r=n+r);r>=0;r--)if(r in t&&t[r]===e)return r||0;return-1};be({target:"Array",proto:!0,forced:vp!==[].lastIndexOf},{lastIndexOf:vp});var yp={70001:"UserSig 已过期，请重新生成。建议 UserSig 有效期设置不小于24小时。",70002:"UserSig 长度为0，请检查传入的 UserSig 是否正确。",70003:"UserSig 非法，请使用官网提供的 API 重新生成 UserSig(https://cloud.tencent.com/document/product/269/32688)。",70005:"UserSig 非法，请使用官网提供的 API 重新生成 UserSig(https://cloud.tencent.com/document/product/269/32688)。",70009:"UserSig 验证失败，可能因为生成 UserSig 时混用了其他 SDKAppID 的私钥或密钥导致，请使用对应 SDKAppID 下的私钥或密钥重新生成 UserSig(https://cloud.tencent.com/document/product/269/32688)。",70013:"请求中的 UserID 与生成 UserSig 时使用的 UserID 不匹配，您可以在即时通信 IM 控制台的【开发辅助工具(https://console.cloud.tencent.com/im-detail/tool-usersig)】页面校验 UserSig。",70014:"请求中的 SDKAppID 与生成 UserSig 时使用的 SDKAppID 不匹配，您可以在即时通信 IM 控制台的【开发辅助工具(https://console.cloud.tencent.com/im-detail/tool-usersig)】页面校验 UserSig。",70016:"密钥不存在，UserSig 验证失败，请在即时通信 IM 控制台获取密钥(https://cloud.tencent.com/document/product/269/32578#.E8.8E.B7.E5.8F.96.E5.AF.86.E9.92.A5)。",70020:"SDKAppID 未找到，请在即时通信 IM 控制台确认应用信息。",70050:"UserSig 验证次数过于频繁。请检查 UserSig 是否正确，并于1分钟后重新验证。您可以在即时通信 IM 控制台的【开发辅助工具(https://console.cloud.tencent.com/im-detail/tool-usersig)】页面校验 UserSig。",70051:"帐号被拉入黑名单。",70052:"UserSig 已经失效，请重新生成，再次尝试。",70107:"因安全原因被限制登录，请不要频繁登录。",70169:"请求的用户帐号不存在。",70114:"服务端内部超时，请稍后重试。",70202:"服务端内部超时，请稍后重试。",70206:"请求中批量数量不合法。",70402:"参数非法，请检查必填字段是否填充，或者字段的填充是否满足协议要求。",70403:"请求失败，需要 App 管理员权限。",70398:"帐号数超限。如需创建多于100个帐号，请将应用升级为专业版，具体操作指引请参见购买指引(https://cloud.tencent.com/document/product/269/32458)。",70500:"服务端内部错误，请稍后重试。",71e3:"删除帐号失败。仅支持删除体验版帐号，您当前应用为专业版，暂不支持帐号删除。",20001:"请求包非法。",20002:"UserSig 或 A2 失效。",20003:"消息发送方或接收方 UserID 无效或不存在，请检查 UserID 是否已导入即时通信 IM。",20004:"网络异常，请重试。",20005:"服务端内部错误，请重试。",20006:"触发发送单聊消息之前回调，App 后台返回禁止下发该消息。",20007:"发送单聊消息，被对方拉黑，禁止发送。消息发送状态默认展示为失败，您可以登录控制台修改该场景下的消息发送状态展示结果，具体操作请参见消息保留设置(https://cloud.tencent.com/document/product/269/38656)。",20009:"消息发送双方互相不是好友，禁止发送（配置单聊消息校验好友关系才会出现）。",20010:"发送单聊消息，自己不是对方的好友（单向关系），禁止发送。",20011:"发送单聊消息，对方不是自己的好友（单向关系），禁止发送。",20012:"发送方被禁言，该条消息被禁止发送。",20016:"消息撤回超过了时间限制（默认2分钟）。",20018:"删除漫游内部错误。",90001:"JSON 格式解析失败，请检查请求包是否符合 JSON 规范。",90002:"JSON 格式请求包中 MsgBody 不符合消息格式描述，或者 MsgBody 不是 Array 类型，请参考 TIMMsgElement 对象的定义(https://cloud.tencent.com/document/product/269/2720#.E6.B6.88.E6.81.AF.E5.85.83.E7.B4.A0-timmsgelement)。",90003:"JSON 格式请求包体中缺少 To_Account 字段或者 To_Account 帐号不存在。",90005:"JSON 格式请求包体中缺少 MsgRandom 字段或者 MsgRandom 字段不是 Integer 类型。",90006:"JSON 格式请求包体中缺少 MsgTimeStamp 字段或者 MsgTimeStamp 字段不是 Integer 类型。",90007:"JSON 格式请求包体中 MsgBody 类型不是 Array 类型，请将其修改为 Array 类型。",90008:"JSON 格式请求包体中缺少 From_Account 字段或者 From_Account 帐号不存在。",90009:"请求需要 App 管理员权限。",90010:"JSON 格式请求包不符合消息格式描述，请参考 TIMMsgElement 对象的定义(https://cloud.tencent.com/document/product/269/2720#.E6.B6.88.E6.81.AF.E5.85.83.E7.B4.A0-timmsgelement)。",90011:"批量发消息目标帐号超过500，请减少 To_Account 中目标帐号数量。",90012:"To_Account 没有注册或不存在，请确认 To_Account 是否导入即时通信 IM 或者是否拼写错误。",90026:"消息离线存储时间错误（最多不能超过7天）。",90031:"JSON 格式请求包体中 SyncOtherMachine 字段不是 Integer 类型。",90044:"JSON 格式请求包体中 MsgLifeTime 字段不是 Integer 类型。",90048:"请求的用户帐号不存在。",90054:"撤回请求中的 MsgKey 不合法。",90994:"服务内部错误，请重试。",90995:"服务内部错误，请重试。",91e3:"服务内部错误，请重试。",90992:"服务内部错误，请重试；如果所有请求都返回该错误码，且 App 配置了第三方回调，请检查 App 服务端是否正常向即时通信 IM 后台服务端返回回调结果。",93e3:"JSON 数据包超长，消息包体请不要超过8k。",91101:"Web 端长轮询被踢（Web 端同时在线实例个数超出限制）。",10002:"服务端内部错误，请重试。",10003:"请求中的接口名称错误，请核对接口名称并重试。",10004:"参数非法，请根据错误描述检查请求是否正确。",10005:"请求包体中携带的帐号数量过多。",10006:"操作频率限制，请尝试降低调用的频率。",10007:"操作权限不足，例如 Public 群组中普通成员尝试执行踢人操作，但只有 App 管理员才有权限。",10008:"请求非法，可能是请求中携带的签名信息验证不正确，请再次尝试。",10009:"该群不允许群主主动退出。",10010:"群组不存在，或者曾经存在过，但是目前已经被解散。",10011:"解析 JSON 包体失败，请检查包体的格式是否符合 JSON 格式。",10012:"发起操作的 UserID 非法，请检查发起操作的用户 UserID 是否填写正确。",10013:"被邀请加入的用户已经是群成员。",10014:"群已满员，无法将请求中的用户加入群组，如果是批量加人，可以尝试减少加入用户的数量。",10015:"找不到指定 ID 的群组。",10016:"App 后台通过第三方回调拒绝本次操作。",10017:"因被禁言而不能发送消息，请检查发送者是否被设置禁言。",10018:"应答包长度超过最大包长（1MB），请求的内容过多，请尝试减少单次请求的数据量。",10019:"请求的用户帐号不存在。",10021:"群组 ID 已被使用，请选择其他的群组 ID。",10023:"发消息的频率超限，请延长两次发消息时间的间隔。",10024:"此邀请或者申请请求已经被处理。",10025:"群组 ID 已被使用，并且操作者为群主，可以直接使用。",10026:"该 SDKAppID 请求的命令字已被禁用。",10030:"请求撤回的消息不存在。",10031:"消息撤回超过了时间限制（默认2分钟）。",10032:"请求撤回的消息不支持撤回操作。",10033:"群组类型不支持消息撤回操作。",10034:"该消息类型不支持删除操作。",10035:"音视频聊天室和在线成员广播大群不支持删除消息。",10036:"音视频聊天室创建数量超过了限制，请参考价格说明(https://cloud.tencent.com/document/product/269/11673)购买预付费套餐“IM音视频聊天室”。",10037:"单个用户可创建和加入的群组数量超过了限制，请参考价格说明(https://cloud.tencent.com/document/product/269/11673)购买或升级预付费套餐“单人可创建与加入群组数”。",10038:"群成员数量超过限制，请参考价格说明(https://cloud.tencent.com/document/product/269/11673)购买或升级预付费套餐“扩展群人数上限”。",10041:"该应用（SDKAppID）已配置不支持群消息撤回。"},_p=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;return wn(this,n),(r=t.call(this)).code=e.code,r.message=yp[e.code]||e.message,r.data=e.data||{},r}return n}(qn(Error)),Cp=2e3,Ip=2001,Mp=2002,Sp=2003,Tp=2022,Ep=2023,Dp=2040,kp=2100,wp=2103,Ap=2105,bp=2106,Rp=2108,Op=2109,Lp=2110,Np=2251,Pp=2252,Gp=2253,xp=2300,Up=2301,qp=2350,Fp=2351,jp=2352,Bp=2400,Hp=2401,Vp=2402,Kp=2403,$p=2500,Yp=2501,zp=2502,Wp=2600,Xp=2601,Jp=2620,Qp=2621,Zp=2622,ef=2660,tf=2661,nf=2662,rf=2680,of=2681,sf=2682,af=2683,uf=2684,cf=2685,lf=2700,pf=2721,ff=2722,hf=2740,df=2741,gf=2742,mf=2800,vf=2801,yf=2802,_f=2803,Cf=2804,If=2805,Mf=2900,Sf=2901,Tf=2902,Ef=2903,Df=2904,kf=2999,wf=91101,Af=20002,bf=70001,Rf="无 SDKAppID",Of="无 accountType",Lf="无 userID",Nf="无 userSig",Pf="无 tinyID",Gf="无 a2key",xf="未检测到 COS 上传插件",Uf="消息发送失败",qf="MessageController.constructor() 需要参数 options",Ff="需要 Message 的实例",jf='Message.conversationType 只能为 "C2C" 或 "GROUP"',Bf="无法发送空文件",Hf="回调函数运行时遇到错误，请检查接入侧代码",Vf="消息撤回失败",Kf="请先选择一个图片",$f="只允许上传 jpg png jpeg gif 格式的图片",Yf="图片大小超过20M，无法发送",zf="语音上传失败",Wf="语音大小大于20M，无法发送",Xf="视频上传失败",Jf="视频大小超过100M，无法发送",Qf="只允许上传 mp4 格式的视频",Zf="文件上传失败",eh="请先选择一个文件",th="文件大小超过100M，无法发送 ",nh="缺少必要的参数文件 URL",rh="没有找到相应的会话，请检查传入参数",oh="没有找到相应的用户或群组，请检查传入参数",ih="未记录的会话类型",sh="非法的群类型，请检查传入参数",ah="不能加入 Private 类型的群组",uh="AVChatRoom 类型的群组不能转让群主",ch="不能把群主转让给自己",lh="不能解散 Private 类型的群组",ph="加群失败，请检查传入参数或重试",fh="AVChatRoom 类型的群不支持邀请群成员",hh="非 AVChatRoom 类型的群组不允许匿名加群，请先登录后再加群",dh="不能在 AVChatRoom 类型的群组踢人",gh="你不是群主，只有群主才有权限操作",mh="不能在 Private / AVChatRoom 类型的群中设置群成员身份",vh="不合法的群成员身份，请检查传入参数",yh="不能设置自己的群成员身份，请检查传入参数",_h="不能将自己禁言，请检查传入参数",Ch="传入 deleteFriend 接口的参数无效",Ih="传入 updateMyProfile 接口的参数无效",Mh="updateMyProfile 无标配资料字段或自定义资料字段",Sh="传入 addToBlacklist 接口的参数无效",Th="传入 removeFromBlacklist 接口的参数无效",Eh="不能拉黑自己",Dh="网络层初始化错误，缺少 URL 参数",kh="打包错误，未定义的 serverName",wh="未定义的 packageConfig",Ah="未连接到网络",bh="不规范的参数名称",Rh="意料外的通知条件",Oh="_syncOffset 丢失",Lh="获取 longpolling id 失败",Nh="接口需要 SDK 处于 ready 状态后才能调用",Ph=["jpg","jpeg","gif","png"],Gh=["mp4"],xh=function(){function e(t){wn(this,e);var n=this._check(t);if(n instanceof _p)throw n;this.type=fn.MSG_FILE,this._percent=0;var r=this._getFileInfo(t);this.content={downloadFlag:2,fileUrl:t.url||"",uuid:t.uuid,fileName:r.name||"",fileSize:r.size||0}}return bn(e,[{key:"_getFileInfo",value:function(e){if(e.fileName&&e.fileSize)return{size:e.fileSize,name:e.fileName};if(Os)return{};var t=e.file.files[0];return{size:t.size,name:t.name,type:t.type.slice(t.type.lastIndexOf("/")+1).toLowerCase()}}},{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateFileUrl",value:function(e){this.content.fileUrl=e}},{key:"_check",value:function(e){if(e.size>104857600)return new _p({code:Vp,message:"".concat(th,": ").concat(104857600," bytes")})}},{key:"sendable",value:function(){return""!==this.content.fileUrl&&""!==this.content.fileName&&0!==this.content.fileSize}}]),e}(),Uh=function(){function e(t){wn(this,e),this.type=fn.MSG_CUSTOM,this.content={data:t.data||"",description:t.description||"",extension:t.extension||""}}return bn(e,[{key:"setData",value:function(e){return this.content.data=e,this}},{key:"setDescription",value:function(e){return this.content.description=e,this}},{key:"setExtension",value:function(e){return this.content.extension=e,this}},{key:"sendable",value:function(){return 0!==this.content.data.length||0!==this.content.description.length||0!==this.content.extension.length}}]),e}(),qh=function(){function e(t){wn(this,e),this.type=fn.MSG_VIDEO,this._percent=0,this.content={remoteVideoUrl:t.remoteVideoUrl,videoFormat:t.videoFormat,videoSecond:parseInt(t.videoSecond,10),videoSize:t.videoSize,videoUrl:t.videoUrl,videoDownloadFlag:2,videoUUID:t.videoUUID,thumbUUID:t.thumbUUID,thumbFormat:t.thumbFormat,thumbWidth:t.thumbWidth,thumbHeight:t.thumbHeight,thumbSize:t.thumbSize,thumbDownloadFlag:2,thumbUrl:t.thumbUrl}}return bn(e,[{key:"updatePercent",value:function(e){this._percent=e,this._percent>1&&(this._percent=1)}},{key:"updateVideoUrl",value:function(e){e&&(this.content.remoteVideoUrl=e)}},{key:"sendable",value:function(){return""!==this.content.remoteVideoUrl}}]),e}(),Fh=function e(t){wn(this,e),this.type=fn.MSG_GEO,this.content=t},jh={1:fn.MSG_PRIORITY_HIGH,2:fn.MSG_PRIORITY_NORMAL,3:fn.MSG_PRIORITY_LOW,4:fn.MSG_PRIORITY_LOWEST},Bh=function(){function e(t){wn(this,e),this.ID="",this.conversationID=t.conversationID||null,this.conversationType=t.conversationType||fn.CONV_C2C,this.conversationSubType=t.conversationSubType,this.time=t.time||Math.ceil(Date.now()/1e3),this.sequence=t.sequence||0,this.clientSequence=t.clientSequence||t.sequence||0,this.random=t.random||Da(),this.priority=this._computePriority(t.priority),this.nick="",this.avatar="",this._elements=[],this.isPlaceMessage=t.isPlaceMessage||0,this.isRevoked=2===t.isPlaceMessage||8===t.msgFlagBits,this.geo={},this.from=t.from||null,this.to=t.to||null,this.flow="",this.isSystemMessage=t.isSystemMessage||!1,this.protocol=t.protocol||"JSON",this.isResend=!1,this.isRead=!1,this.status=t.status||Lu.SUCCESS,this.reInitialize(t.currentUser),this.extractGroupInfo(t.groupProfile||null)}return bn(e,[{key:"getElements",value:function(){return this._elements}},{key:"extractGroupInfo",value:function(e){null!==e&&(la(e.fromAccountNick)&&(this.nick=e.fromAccountNick),la(e.fromAccountHeadurl)&&(this.avatar=e.fromAccountHeadurl))}},{key:"_initProxy",value:function(){this.payload=this._elements[0].content,this.type=this._elements[0].type}},{key:"reInitialize",value:function(e){e&&(this.status=this.from?Lu.SUCCESS:Lu.UNSEND,!this.from&&(this.from=e)),this._initFlow(e),this._initielizeSequence(e),this._concactConversationID(e),this.generateMessageID(e)}},{key:"isSendable",value:function(){return 0!==this._elements.length&&("function"!=typeof this._elements[0].sendable?(aa.warn("".concat(this._elements[0].type,' need "boolean : sendable()" method')),!1):this._elements[0].sendable())}},{key:"_initTo",value:function(e){this.conversationType===fn.CONV_GROUP&&(this.to=e.groupID)}},{key:"_initielizeSequence",value:function(e){0===this.clientSequence&&e&&(this.clientSequence=function(e){if(!e)return aa.error("autoincrementIndex(string: key) need key parameter"),!1;if(void 0===ba[e]){var t=new Date,n="3".concat(t.getHours()).slice(-2),r="0".concat(t.getMinutes()).slice(-2),o="0".concat(t.getSeconds()).slice(-2);ba[e]=parseInt([n,r,o,"0001"].join("")),n=null,r=null,o=null,aa.warn("utils.autoincrementIndex() create new sequence : ".concat(e," = ").concat(ba[e]))}return ba[e]++}(e)),0===this.sequence&&this.conversationType===fn.CONV_C2C&&(this.sequence=this.clientSequence)}},{key:"generateMessageID",value:function(e){var t=e===this.from?1:0,n=this.sequence>0?this.sequence:this.clientSequence;this.ID="".concat(this.conversationID,"-").concat(n,"-").concat(this.random,"-").concat(t)}},{key:"_initFlow",value:function(e){""!==e&&(e===this.from?(this.flow="out",this.isRead=!0):this.flow="in")}},{key:"_concactConversationID",value:function(e){var t=this.to,n="",r=this.conversationType;r!==fn.CONV_SYSTEM?(n=r===fn.CONV_C2C?e===this.from?t:this.from:this.to,this.conversationID="".concat(r).concat(n)):this.conversationID=fn.CONV_SYSTEM}},{key:"isElement",value:function(e){return e instanceof Pu||e instanceof ip||e instanceof sp||e instanceof ap||e instanceof xh||e instanceof qh||e instanceof cp||e instanceof pp||e instanceof Uh||e instanceof Fh}},{key:"setElement",value:function(e){var t=this;if(this.isElement(e))return this._elements=[e],void this._initProxy();var n=function(e){switch(e.type){case fn.MSG_TEXT:t.setTextElement(e.content);break;case fn.MSG_IMAGE:t.setImageElement(e.content);break;case fn.MSG_AUDIO:t.setAudioElement(e.content);break;case fn.MSG_FILE:t.setFileElement(e.content);break;case fn.MSG_VIDEO:t.setVideoElement(e.content);break;case fn.MSG_CUSTOM:t.setCustomElement(e.content);break;case fn.MSG_GEO:t.setGEOElement(e.content);break;case fn.MSG_GRP_TIP:t.setGroupTipElement(e.content);break;case fn.MSG_GRP_SYS_NOTICE:t.setGroupSystemNoticeElement(e.content);break;case fn.MSG_FACE:t.setFaceElement(e.content);break;default:aa.warn(e.type,e.content,"no operation......")}};if(Array.isArray(e))for(var r=0;r<e.length;r++)n(e[r]);else n(e);this._initProxy()}},{key:"setTextElement",value:function(e){var t="string"==typeof e?e:e.text,n=new Pu({text:t});this._elements.push(n)}},{key:"setImageElement",value:function(e){var t=new ip(e);this._elements.push(t)}},{key:"setAudioElement",value:function(e){var t=new ap(e);this._elements.push(t)}},{key:"setFileElement",value:function(e){var t=new xh(e);this._elements.push(t)}},{key:"setVideoElement",value:function(e){var t=new qh(e);this._elements.push(t)}},{key:"setGEOElement",value:function(e){var t=new Fh(e);this._elements.push(t)}},{key:"setCustomElement",value:function(e){var t=new Uh(e);this._elements.push(t)}},{key:"setGroupTipElement",value:function(e){if(e.operatorInfo){var t=e.operatorInfo,n=t.nick,r=t.avatar;la(n)&&(this.nick=n),la(r)&&(this.avatar=r)}var o=new cp(e);this._elements.push(o)}},{key:"setGroupSystemNoticeElement",value:function(e){var t=new pp(e);this._elements.push(t)}},{key:"setFaceElement",value:function(e){var t=new sp(e);this._elements.push(t)}},{key:"setIsRead",value:function(e){this.isRead=e}},{key:"_computePriority",value:function(e){if(da(e))return fn.MSG_PRIORITY_NORMAL;if(la(e)&&-1!==Object.values(jh).indexOf(e))return e;if(ca(e)){var t=""+e;if(-1!==Object.keys(jh).indexOf(t))return jh[t]}return fn.MSG_PRIORITY_NORMAL}},{key:"elements",get:function(){return aa.warn("！！！Message 实例的 elements 属性即将废弃，请尽快修改。使用 type 和 payload 属性处理单条消息，兼容组合消息使用 _elements 属性！！！"),this._elements}}]),e}(),Hh=function(e){return!!e&&(!!(function(e){return la(e)&&e.slice(0,3)===fn.CONV_C2C}(e)||function(e){return la(e)&&e.slice(0,5)===fn.CONV_GROUP}(e)||Ga(e))||(console.warn("非法的会话 ID:".concat(e,"。会话 ID 组成方式：\n  C2C + userID（单聊）\n  GROUP + groupID（群聊）\n  @TIM#SYSTEM（系统通知会话）")),!1))},Vh={login:{userID:{type:"String",required:!0},userSig:{type:"String",required:!0}},addToBlacklist:{userIDList:{type:"Array",required:!0}},mutilParam:[{name:"paramName",type:"Number",required:!0},{name:"paramName",type:"String",required:!0}],on:[{name:"eventName",type:"String",validator:function(e){return"string"==typeof e&&0!==e.length||(console.warn("on 接口的 eventName 参数必须是 String 类型，且不能为空。"),!1)}},{name:"handler",type:"Function",validator:function(e){return"function"!=typeof e?(console.warn("on 接口的 handler 参数必须是 Function 类型。"),!1):(""===e.name&&console.warn("on 接口的 handler 参数推荐使用具名函数。具名函数可以使用 off 接口取消订阅，匿名函数无法取消订阅。"),!0)}}],once:[{name:"eventName",type:"String",validator:function(e){return"string"==typeof e&&0!==e.length||(console.warn("once 接口的 eventName 参数必须是 String 类型，且不能为空。"),!1)}},{name:"handler",type:"Function",validator:function(e){return"function"!=typeof e?(console.warn("once 接口的 handler 参数必须是 Function 类型。"),!1):(""===e.name&&console.warn("once 接口的 handler 参数推荐使用具名函数。"),!0)}}],off:[{name:"eventName",type:"String",validator:function(e){return"string"==typeof e&&0!==e.length||(console.warn("off 接口的 eventName 参数必须是 String 类型，且不能为空。"),!1)}},{name:"handler",type:"Function",validator:function(e){return"function"!=typeof e?(console.warn("off 接口的 handler 参数必须是 Function 类型。"),!1):(""===e.name&&console.warn("off 接口的 handler 参数为匿名函数，无法取消订阅。"),!0)}}],sendMessage:[{name:"message",type:"Object",required:!0}],getMessageList:{conversationID:{type:"String",required:!0,validator:function(e){return Hh(e)}},nextReqMessageID:{type:"String"},count:{type:"Number",validator:function(e){return!(!da(e)&&!/^[1-9][0-9]*$/.test(e))||(console.warn("getMessageList 接口的 count 参数必须为正整数"),!1)}}},setMessageRead:{conversationID:{type:"String",required:!0,validator:function(e){return Hh(e)}}},getConversationProfile:[{name:"conversationID",type:"String",required:!0,validator:function(e){return Hh(e)}}],deleteConversation:[{name:"conversationID",type:"String",required:!0,validator:function(e){return Hh(e)}}],getGroupList:{groupProfileFilter:{type:"Array"}},getGroupProfile:{groupID:{type:"String",required:!0},groupCustomFieldFilter:{type:"Array"},memberCustomFieldFilter:{type:"Array"}},getGroupProfileAdvance:{groupIDList:{type:"Array",required:!0}},createGroup:{name:{type:"String",required:!0}},joinGroup:{groupID:{type:"String",required:!0},type:{type:"String"},applyMessage:{type:"String"}},quitGroup:[{name:"groupID",type:"String",required:!0}],handleApplication:{message:{type:"Object",required:!0},handleAction:{type:"String",required:!0},handleMessage:{type:"String"}},changeGroupOwner:{groupID:{type:"String",required:!0},newOwnerID:{type:"String",required:!0}},updateGroupProfile:{groupID:{type:"String",required:!0},muteAllMembers:{type:"Boolean"}},dismissGroup:[{name:"groupID",type:"String",required:!0}],searchGroupByID:[{name:"groupID",type:"String",required:!0}],getGroupMemberList:{groupID:{type:"String",required:!0},offset:{type:"Number"},count:{type:"Number"}},getGroupMemberProfile:{groupID:{type:"String",required:!0},userIDList:{type:"Array",required:!0},memberCustomFieldFilter:{type:"Array"}},addGroupMemeber:{groupID:{type:"String",required:!0},userIDList:{type:"Array",required:!0}},setGroupMemberRole:{groupID:{type:"String",required:!0},userID:{type:"String",required:!0},role:{type:"String",required:!0}},setGroupMemberMuteTime:{groupID:{type:"String",required:!0},userID:{type:"String",required:!0},muteTime:{type:"Number",validator:function(e){return e>=0}}},setGroupMemberNameCard:{groupID:{type:"String",required:!0},userID:{type:"String"},nameCard:{type:"String",required:!0,validator:function(e){return!0!==/^\s+$/.test(e)}}},setMessageRemindType:{groupID:{type:"String",required:!0},messageRemindType:{type:"String",required:!0}},setGroupMemberCustomField:{groupID:{type:"String",required:!0},userID:{type:"String"},memberCustomField:{type:"Array",required:!0}},deleteGroupMember:{groupID:{type:"String",required:!0}},createTextMessage:{to:{type:"String",required:!0},conversationType:{type:"String",required:!0},payload:{type:"Object",required:!0,validator:function(e){return la(e.text)?0!==e.text.length||(console.warn("createTextMessage 消息内容不能为空。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createTextMessage"),!1):(console.warn("createTextMessage payload.text 类型必须为字符串。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createTextMessage"),!1)}}},createCustomMessage:{to:{type:"String",required:!0},conversationType:{type:"String",required:!0},payload:{type:"Object",required:!0,validator:function(e){return e.data&&!la(e.data)?(console.warn("createCustomMessage payload.data 类型必须为 String。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createCustomMessage"),!1):e.description&&!la(e.description)?(console.warn("createCustomMessage payload.description 类型必须为 String。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createCustomMessage"),!1):!(e.extension&&!la(e.extension))||(console.warn("createCustomMessage payload.extension 类型必须为 String。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createCustomMessage"),!1)}}},createImageMessage:{to:{type:"String",required:!0},conversationType:{type:"String",required:!0},payload:{type:"Object",required:!0,validator:function(e){if(da(e.file))return console.warn("createImageMessage payload.file 不能为 undefined。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createImageMessage"),!1;if(Rs){if(!(e.file instanceof HTMLInputElement||ua(e.file)))return console.warn("createImageMessage payload.file 的类型必须是 HTMLInputElement 或 File。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createImageMessage"),!1;if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return console.warn("createImageMessage 您没有选择文件，无法发送。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createImageMessage"),!1}return!0},onProgress:{type:"Function",required:!1,validator:function(e){return da(e)&&console.warn("createImageMessage 没有 onProgress 回调，您将无法获取图片上传进度。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createImageMessage"),!0}}}},createAudioMessage:{to:{type:"String",required:!0},conversationType:{type:"String",required:!0},payload:{type:"Object",required:!0},onProgress:{type:"Function",required:!1,validator:function(e){return da(e)&&console.warn("createAudioMessage 没有 onProgress 回调，您将无法获取音频上传进度。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createAudioMessage"),!0}}},createVideoMessage:{to:{type:"String",required:!0},conversationType:{type:"String",required:!0},payload:{type:"Object",required:!0,validator:function(e){if(da(e.file))return console.warn("createVideoMessage payload.file 不能为 undefined。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createVideoMessage"),!1;if(Rs){if(!(e.file instanceof HTMLInputElement||ua(e.file)))return console.warn("createVideoMessage payload.file 的类型必须是 HTMLInputElement 或 File。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createVideoMessage"),!1;if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return console.warn("createVideoMessage 您没有选择文件，无法发送。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createVideoMessage"),!1}return!0}},onProgress:{type:"Function",required:!1,validator:function(e){return da(e)&&console.warn("createVideoMessage 没有 onProgress 回调，您将无法获取视频上传进度。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createVideoMessage"),!0}}},createFaceMessage:{to:{type:"String",required:!0},conversationType:{type:"String",required:!0},payload:{type:"Object",required:!0,validator:function(e){return!!fa(e)&&(ca(e.index)?!!la(e.data)||(console.warn("createFaceMessage payload.data 类型必须为 String！"),!1):(console.warn("createFaceMessage payload.index 类型必须为 Number！"),!1))}}},createFileMessage:{to:{type:"String",required:!0},conversationType:{type:"String",required:!0},payload:{type:"Object",required:!0,validator:function(e){if(da(e.file))return console.warn("createFileMessage payload.file 不能为 undefined。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createFileMessage"),!1;if(Rs){if(!(e.file instanceof HTMLInputElement||ua(e.file)))return console.warn("createFileMessage payload.file 的类型必须是 HTMLInputElement 或 File。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createFileMessage"),!1;if(e.file instanceof HTMLInputElement&&0===e.file.files.length)return console.warn("createFileMessage 您没有选择文件，无法发送。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createFileMessage"),!1}return!0}},onProgress:{type:"Function",required:!1,validator:function(e){return da(e)&&console.warn("createFileMessage 没有 onProgress 回调，您将无法获取文件上传进度。请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#createFileMessage"),!0}}},revokeMessage:[{name:"message",type:"Object",required:!0,validator:function(e){return e instanceof Bh?e.conversationType===fn.CONV_SYSTEM?(console.warn("revokeMessage 不能撤回系统会话消息，只能撤回单聊消息或群消息"),!1):!0!==e.isRevoked||(console.warn("revokeMessage 消息已经被撤回，请勿重复操作"),!1):(console.warn("revokeMessage 参数 message 必须为 Message(https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/Message.html) 实例。"),!1)}}],getUserProfile:{userIDList:{type:"Array",validator:function(e){return ha(e)?(0===e.length&&console.warn("getUserProfile userIDList 不能为空数组，请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#getUserProfile"),!0):(console.warn("getUserProfile userIDList 必须为数组，请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#getUserProfile"),!1)}}},updateMyProfile:{profileCustomField:{type:"Array",validator:function(e){return!!da(e)||!!ha(e)||(console.warn("updateMyProfile profileCustomField 必须为数组，请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#updateMyProfile"),!1)}}}},Kh={login:"login",logout:"logout",on:"on",once:"once",off:"off",setLogLevel:"setLogLevel",downloadLog:"downloadLog",registerPlugin:"registerPlugin",destroy:"destroy",createTextMessage:"createTextMessage",createImageMessage:"createImageMessage",createAudioMessage:"createAudioMessage",createVideoMessage:"createVideoMessage",createCustomMessage:"createCustomMessage",createFaceMessage:"createFaceMessage",createFileMessage:"createFileMessage",sendMessage:"sendMessage",resendMessage:"resendMessage",getMessageList:"getMessageList",setMessageRead:"setMessageRead",revokeMessage:"revokeMessage",getConversationList:"getConversationList",getConversationProfile:"getConversationProfile",deleteConversation:"deleteConversation",getGroupList:"getGroupList",getGroupProfile:"getGroupProfile",createGroup:"createGroup",joinGroup:"joinGroup",updateGroupProfile:"updateGroupProfile",quitGroup:"quitGroup",dismissGroup:"dismissGroup",changeGroupOwner:"changeGroupOwner",searchGroupByID:"searchGroupByID",setMessageRemindType:"setMessageRemindType",handleGroupApplication:"handleGroupApplication",getGroupMemberProfile:"getGroupMemberProfile",getGroupMemberList:"getGroupMemberList",addGroupMember:"addGroupMember",deleteGroupMember:"deleteGroupMember",setGroupMemberNameCard:"setGroupMemberNameCard",setGroupMemberMuteTime:"setGroupMemberMuteTime",setGroupMemberRole:"setGroupMemberRole",setGroupMemberCustomField:"setGroupMemberCustomField",getMyProfile:"getMyProfile",getUserProfile:"getUserProfile",updateMyProfile:"updateMyProfile",getBlacklist:"getBlacklist",addToBlacklist:"addToBlacklist",removeFromBlacklist:"removeFromBlacklist",getFriendList:"getFriendList"},$h="1.7.3",Yh="537048168",zh="10",Wh="protobuf",Xh="json",Jh={HOST:{TYPE:3,ACCESS_LAYER_TYPES:{SANDBOX:1,TEST:2,PRODUCTION:3},CURRENT:{COMMON:"https://webim.tim.qq.com",PIC:"https://pic.tim.qq.com",COS:"https://yun.tim.qq.com"},PRODUCTION:{COMMON:"https://webim.tim.qq.com",PIC:"https://pic.tim.qq.com",COS:"https://yun.tim.qq.com"},SANDBOX:{COMMON:"https://events.tim.qq.com",PIC:"https://pic.tim.qq.com",COS:"https://yun.tim.qq.com"},TEST:{COMMON:"https://test.tim.qq.com",PIC:"https://pic.tim.qq.com",COS:"https://test.tim.qq.com"},setCurrent:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3;switch(e){case this.ACCESS_LAYER_TYPES.SANDBOX:this.CURRENT=this.SANDBOX,this.TYPE=this.ACCESS_LAYER_TYPES.SANDBOX;break;case this.ACCESS_LAYER_TYPES.TEST:this.CURRENT=this.TEST,this.TYPE=this.ACCESS_LAYER_TYPES.TEST;break;default:this.CURRENT=this.PRODUCTION,this.TYPE=this.ACCESS_LAYER_TYPES.PRODUCTION}}},NAME:{OPEN_IM:"openim",GROUP:"group_open_http_svc",FRIEND:"sns",PROFILE:"profile",RECENT_CONTACT:"recentcontact",PIC:"openpic",BIG_GROUP_NO_AUTH:"group_open_http_noauth_svc",BIG_GROUP_LONG_POLLING_NO_AUTH:"group_open_long_polling_http_noauth_svc",IM_OPEN_STAT:"imopenstat",WEB_IM:"webim",IM_COS_SIGN:"im_cos_sign_svr"},CMD:{ACCESS_LAYER:"accesslayer",LOGIN:"login",LOGOUT_LONG_POLL:"longpollinglogout",LOGOUT_ALL:"logout",PORTRAIT_GET:"portrait_get_all",PORTRAIT_SET:"portrait_set",GET_LONG_POLL_ID:"getlongpollingid",LONG_POLL:"longpolling",AVCHATROOM_LONG_POLL:"get_msg",FRIEND_ADD:"friend_add",FRIEND_GET_ALL:"friend_get_all",FRIEND_DELETE:"friend_delete",RESPONSE_PENDENCY:"friend_response",GET_PENDENCY:"pendency_get",DELETE_PENDENCY:"pendency_delete",GET_GROUP_PENDENCY:"get_pendency",GET_BLACKLIST:"black_list_get",ADD_BLACKLIST:"black_list_add",DELETE_BLACKLIST:"black_list_delete",CREATE_GROUP:"create_group",GET_JOINED_GROUPS:"get_joined_group_list",SEND_MESSAGE:"sendmsg",REVOKE_C2C_MESSAGE:"msgwithdraw",SEND_GROUP_MESSAGE:"send_group_msg",REVOKE_GROUP_MESSAGE:"group_msg_recall",GET_GROUP_INFO:"get_group_info",GET_GROUP_MEMBER_INFO:"get_specified_group_member_info",GET_GROUP_MEMBER_LIST:"get_group_member_info",QUIT_GROUP:"quit_group",CHANGE_GROUP_OWNER:"change_group_owner",DESTROY_GROUP:"destroy_group",ADD_GROUP_MEMBER:"add_group_member",DELETE_GROUP_MEMBER:"delete_group_member",SEARCH_GROUP_BY_ID:"get_group_public_info",APPLY_JOIN_GROUP:"apply_join_group",HANDLE_APPLY_JOIN_GROUP:"handle_apply_join_group",MODIFY_GROUP_INFO:"modify_group_base_info",MODIFY_GROUP_MEMBER_INFO:"modify_group_member_info",DELETE_GROUP_SYSTEM_MESSAGE:"deletemsg",GET_CONVERSATION_LIST:"get",PAGING_GET_CONVERSATION_LIST:"page_get",DELETE_CONVERSATION:"delete",GET_MESSAGES:"getmsg",GET_C2C_ROAM_MESSAGES:"getroammsg",GET_GROUP_ROAM_MESSAGES:"group_msg_get",SET_C2C_MESSAGE_READ:"msgreaded",SET_GROUP_MESSAGE_READ:"msg_read_report",FILE_READ_AND_WRITE_AUTHKEY:"authkey",FILE_UPLOAD:"pic_up",COS_SIGN:"cos",TIM_WEB_REPORT:"tim_web_report",BIG_DATA_HALLWAY_AUTH_KEY:"authkey"},CHANNEL:{SOCKET:1,XHR:2,AUTO:0},NAME_VERSION:{openim:"v4",group_open_http_svc:"v4",sns:"v4",profile:"v4",recentcontact:"v4",openpic:"v4",group_open_http_noauth_svc:"v1",group_open_long_polling_http_noauth_svc:"v1",imopenstat:"v4",im_cos_sign_svr:"v4",webim:"v4"}};Jh.HOST.setCurrent(Jh.HOST.ACCESS_LAYER_TYPES.PRODUCTION);var Qh={request:{toAccount:"To_Account",fromAccount:"From_Account",to:"To_Account",from:"From_Account",groupID:"GroupId",avatar:"FaceUrl"},response:{GroupId:"groupID",Member_Account:"userID",MsgList:"messageList",SyncFlag:"syncFlag",To_Account:"to",From_Account:"from",MsgSeq:"sequence",MsgRandom:"random",MsgTimeStamp:"time",MsgContent:"content",MsgBody:"elements",GroupWithdrawInfoArray:"revokedInfos",WithdrawC2cMsgNotify:"c2cMessageRevokedNotify",C2cWithdrawInfoArray:"revokedInfos",MsgRand:"random",MsgType:"type",MsgShow:"messageShow",NextMsgSeq:"nextMessageSeq",FaceUrl:"avatar",ProfileDataMod:"profileModify",Profile_Account:"userID",ValueBytes:"value",ValueNum:"value",NoticeSeq:"noticeSequence",NotifySeq:"notifySequence",MsgFrom_AccountExtraInfo:"messageFromAccountExtraInformation",Operator_Account:"operatorID",OpType:"operationType",ReportType:"operationType",UserId:"userID",User_Account:"userID",List_Account:"userIDList",MsgOperatorMemberExtraInfo:"operatorInfo",MsgMemberExtraInfo:"memberInfoList",ImageUrl:"avatar",NickName:"nick",MsgGroupNewInfo:"newGroupProfile",MsgAppDefinedData:"groupCustomField",Owner_Account:"ownerID",GroupName:"name",GroupFaceUrl:"avatar",GroupIntroduction:"introduction",GroupNotification:"notification",GroupApplyJoinOption:"joinOption",MsgKey:"messageKey",GroupInfo:"groupProfile",ShutupTime:"muteTime",Desc:"description",Ext:"extension"},ignoreKeyWord:["C2C","ID","USP"]},Zh="_contextWasUpdated",ed="_contextWasReset",td="_a2KeyAndTinyIDUpdated",nd="_specifiedConfigUpdated",rd="_noticeIsSynchronizing",od="_noticeIsSynchronized",id="_messageSent",sd="_syncMessageProcessing",ad="_syncMessageFinished",ud="_receiveInstantMessage",cd="_receiveGroupInstantMessage",ld="_receveGroupSystemNotice",pd="_messageRevoked",fd="_longPollGetIDFailed",hd="_longPollRequestFailed",dd="_longPollResponseOK",gd="_longPollFastStart",md="_longPollSlowStart",vd="_longPollKickedOut",yd="_longPollMitipuleDeviceKickedOut",_d="_longPollGetNewC2CNotice",Cd="_longPollGetNewGroupMessages",Id="_longPollGetNewGroupTips",Md="_longPollGetNewGroupNotice",Sd="_longPollGetNewFriendMessages",Td="_longPollProfileModified",Ed="_longPollNoticeReceiveSystemOrders",Dd=" _longpollGroupMessageRevoked",kd="_longpollC2CMessageRevoked",wd="_avlongPollRequestFailed",Ad="_avlongPollResponseOK",bd="_onGroupListUpdated",Rd="_loginSuccess",Od="_signLogoutExcuting",Ld="_logoutSuccess",Nd="_a2keyExpired",Pd="_errorHasBeenDetected",Gd="_onConversationListUpdated",xd="_onConversationListProfileUpdated",Ud="_conversationDeleted",qd="onProfileUpdated",Fd="joinAVChatRoomSuccess",jd="joinAVChatRoomSuccessNoAuth",Bd="_sdkStateReady",Hd=Ke.filter,Vd=_n("filter"),Kd=Xe("filter");be({target:"Array",proto:!0,forced:!Vd||!Kd},{filter:function(e){return Hd(this,e,arguments.length>1?arguments[1]:void 0)}}),be({target:"Object",stat:!0,forced:Object.assign!==Fu},{assign:Fu});var $d=$r.trim;function Yd(e,t){if("string"!=typeof e&&!Array.isArray(e))throw new TypeError("Expected the input to be `string | string[]`");var n;return t=Object.assign({pascalCase:!1},t),0===(e=Array.isArray(e)?e.map((function(e){return e.trim()})).filter((function(e){return e.length})).join("-"):e.trim()).length?"":1===e.length?t.pascalCase?e.toUpperCase():e.toLowerCase():(e!==e.toLowerCase()&&(e=zd(e)),e=e.replace(/^[_.\- ]+/,"").toLowerCase().replace(/[_.\- ]+(\w|$)/g,(function(e,t){return t.toUpperCase()})).replace(/\d+(\w|$)/g,(function(e){return e.toUpperCase()})),n=e,t.pascalCase?n.charAt(0).toUpperCase()+n.slice(1):n)}be({target:"String",proto:!0,forced:function(e){return i((function(){return!!jr[e]()||"​᠎"!="​᠎"[e]()||jr[e].name!==e}))}("trim")},{trim:function(){return $d(this)}});var zd=function(e){for(var t=!1,n=!1,r=!1,o=0;o<e.length;o++){var i=e[o];t&&/[a-zA-Z]/.test(i)&&i.toUpperCase()===i?(e=e.slice(0,o)+"-"+e.slice(o),t=!1,r=n,n=!0,o++):n&&r&&/[a-zA-Z]/.test(i)&&i.toLowerCase()===i?(e=e.slice(0,o-1)+"-"+e.slice(o-1),r=n,n=!1,t=!0):(t=i.toLowerCase()===i&&i.toUpperCase()!==i,r=n,n=i.toUpperCase()===i&&i.toLowerCase()!==i)}return e};function Wd(e,t,n){var r=[],o=0,i=function e(t,n){if(++o>10)return o--,t;if(ha(t)){var i=t.map((function(t){return pa(t)?e(t,n):t}));return o--,i}if(pa(t)){var s=(a=t,u=function(e,t){if(!_a(t))return!1;if((s=t)!==Yd(s)){for(var o=!0,i=0;i<Qh.ignoreKeyWord.length;i++)if(t.includes(Qh.ignoreKeyWord[i])){o=!1;break}o&&r.push(t)}var s;return da(n[t])?function(e){return"OPPOChannelID"===e?e:e[0].toUpperCase()+Yd(e).slice(1)}(t):n[t]},c=Object.create(null),Object.keys(a).forEach((function(e){var t=u(a[e],e);t&&(c[t]=a[e])})),c);return s=xa(s,(function(t,r){return ha(t)||pa(t)?e(t,n):t})),o--,s}var a,u,c}(e,t=Ln({},Qh.request,{},t));return r.length>0&&n.innerEmitter.emit(Pd,{code:Mf,message:bh}),i}function Xd(e,t){if(t=Ln({},Qh.response,{},t),ha(e))return e.map((function(e){return pa(e)?Xd(e,t):e}));if(pa(e)){var n=(r=e,o=function(e,n){return da(t[n])?Yd(n):t[n]},i={},Object.keys(r).forEach((function(e){i[o(r[e],e)]=r[e]})),i);return xa(n,(function(e){return ha(e)||pa(e)?Xd(e,t):e}))}var r,o,i}var Jd=function(){function e(t){var n=this;wn(this,e),this.url="",this.requestData=null,this.method=t.method||"POST",this.callback=function(e){return Xd(e=t.decode(e),n._getResponseMap(t))},this._initializeServerMap(),this._initializeURL(t),this._initializeRequestData(t)}return bn(e,[{key:"_initializeServerMap",value:function(){this._serverMap=Object.create(null);var e="";for(var t in Jh.NAME)if(Object.prototype.hasOwnProperty.call(Jh.NAME,t))switch(e=Jh.NAME[t]){case Jh.NAME.PIC:this._serverMap[e]=Jh.HOST.CURRENT.PIC;break;case Jh.NAME.IM_COS_SIGN:this._serverMap[e]=Jh.HOST.CURRENT.COS;break;default:this._serverMap[e]=Jh.HOST.CURRENT.COMMON}}},{key:"_getHost",value:function(e){if(void 0!==this._serverMap[e])return this._serverMap[e];throw new _p({code:_f,message:kh})}},{key:"_initializeURL",value:function(e){var t=e.serverName,n=e.cmd,r=this._getHost(t),o="".concat(r,"/").concat(Jh.NAME_VERSION[t],"/").concat(t,"/").concat(n);o+="?".concat(this._getQueryString(e.queryString)),this.url=o}},{key:"getUrl",value:function(){return this.url.replace(/&reqtime=(\d+)/,"&reqtime=".concat(Math.ceil(+new Date/1e3)))}},{key:"_initializeRequestData",value:function(e){var t,n=e.requestData;t=this._requestDataCleaner(n),this.requestData=e.encode(t)}},{key:"_requestDataCleaner",value:function(e){var t=Array.isArray(e)?[]:Object.create(null);for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&_a(n)&&null!==e[n]&&("object"!==kn(e[n])?t[n]=e[n]:t[n]=this._requestDataCleaner.bind(this)(e[n]));return t}},{key:"_getQueryString",value:function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&("function"!=typeof e[n]?t.push("".concat(n,"=").concat(e[n])):t.push("".concat(n,"=").concat(e[n]())));return t.join("&")}},{key:"_getResponseMap",value:function(e){if(e.keyMaps&&e.keyMaps.response&&Object.keys(e.keyMaps.response).length>0)return e.keyMaps.response}}]),e}(),Qd=[].slice,Zd=/MSIE .\./.test(hn),eg=function(e){return function(t,n){var r=arguments.length>2,o=r?Qd.call(arguments,2):void 0;return e(r?function(){("function"==typeof t?t:Function(t)).apply(this,o)}:t,n)}};function tg(e){this.mixin(e)}be({global:!0,bind:!0,forced:Zd},{setTimeout:eg(o.setTimeout),setInterval:eg(o.setInterval)}),tg.mixin=function(e){var t=e.prototype||e;t._isReady=!1,t.ready=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e)return this._isReady?void(t?e.call(this):setTimeout(e,1)):(this._readyQueue=this._readyQueue||[],void this._readyQueue.push(e))},t.triggerReady=function(){var e=this;this._isReady=!0,setTimeout((function(){var t=e._readyQueue;e._readyQueue=[],t&&t.length>0&&t.forEach((function(e){e.call(this)}),e)}),1)},t.resetReady=function(){this._isReady=!1,this._readyQueue=[]},t.isReady=function(){return this._isReady}};var ng=function(){function e(t){wn(this,e),tg.mixin(this),this.tim=t}return bn(e,[{key:"isLoggedIn",value:function(){return this.tim.context.login===Ou.IS_LOGIN||!!this.tim.context.a2Key}},{key:"createTransportCapsule",value:function(e){var t=this.tim.packageConfig.get(e);return t?new Jd(t):null}},{key:"request",value:function(e){var t=this.createTransportCapsule(e);return t||aa.error("unknown transport capsule, please check!",e),this.tim.connectionController.request(t)}},{key:"emitInnerEvent",value:function(e,t){this.tim.innerEmitter.emit(e,t)}},{key:"emitOuterEvent",value:function(e,t){this.tim.outerEmitter.emit(e,t)}},{key:"reset",value:function(){aa.warn(["method: IMController.reset() method must be implemented"].join())}},{key:"probeNetwork",value:function(){return this.tim.netMonitor.probe()}},{key:"getNetworkType",value:function(){return this.tim.netMonitor.getNetworkType()}},{key:"getPlatform",value:function(){var e="web";return Fs?e="wechat":Os&&(e="wxmp"),e}}]),e}(),rg=function(){function e(t,n){wn(this,e),this.data=t,this._innerEmitter=n,this.defaultData={},Object.assign(this.defaultData,t),this.initGetterAndSetter()}return bn(e,[{key:"initGetterAndSetter",value:function(){var e=this,t=function(t){Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){return e.data[t]},set:function(n){e.data[t]!==n&&(e.data[t]=n,e.onChange.bind(e)(t,n))}})};for(var n in e.data)Object.prototype.hasOwnProperty.call(e.data,n)&&t(n)}},{key:"onChange",value:function(e,t){this._innerEmitter.emit(Zh,{key:e,value:t})}},{key:"reset",value:function(){for(var e in aa.log("Context.reset"),this.data)Object.prototype.hasOwnProperty.call(this.data,e)&&(this.data[e]=this.defaultData.hasOwnProperty(e)?this.defaultData[e]:null)}}]),e}(),og=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;wn(this,n);var o=(r=t.call(this,e)).tim.loginInfo;return r._context=new rg({login:Ou.IS_NOT_LOGIN,SDKAppID:o.SDKAppID,appIDAt3rd:null,accountType:o.accountType,identifier:o.identifier,tinyID:null,identifierNick:o.identifierNick,userSig:o.userSig,a2Key:null,contentType:"json",apn:1},r.tim.innerEmitter),r._initListener(),r}return bn(n,[{key:"reset",value:function(){this._context.reset(),this.emitInnerEvent(ed)}},{key:"_initListener",value:function(){this.tim.innerEmitter.on(Zh,this._onContextMemberChange,this),this.tim.innerEmitter.on(Rd,this._updateA2KeyAndTinyID,this)}},{key:"_updateA2KeyAndTinyID",value:function(e){var t=e.data,n=t.a2Key,r=t.tinyID;this._context.a2Key=n,this._context.tinyID=r,this.emitInnerEvent(td),this.triggerReady()}},{key:"getContext",value:function(){return this._context}},{key:"_onContextMemberChange",value:function(e){var t=e.data,n=t.key,r=t.value;("tinyID"===n||"a2Key"===n)&&(r.length<=0?this._context.login=Ou.IS_NOT_LOGIN:this._context.login=null!==this._context.a2Key?Ou.IS_LOGIN:Ou.IS_NOT_LOGIN)}}]),n}(ng),ig=function e(t){wn(this,e),this.code=0,this.data=t||{}},sg=null,ag=function(e){sg=e},ug=function(e){return e instanceof ig?(aa.warn("IMPromise.resolve 此函数会自动用options创建IMResponse实例，调用侧不需创建，建议修改！"),Promise.resolve(e)):Promise.resolve(new ig(e))},cg=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e instanceof _p)return t&&null!==sg&&sg.emit(pn.ERROR,e),Promise.reject(e);if(e instanceof Error){var n=new _p({code:Ef,message:e.message});return t&&null!==sg&&sg.emit(pn.ERROR,n),Promise.reject(n)}if(da(e)||da(e.code)||da(e.message))aa.error("IMPromise.reject 必须指定code(错误码)和message(错误信息)!!!");else{if(ca(e.code)&&la(e.message)){var r=new _p(e);return t&&null!==sg&&sg.emit(pn.ERROR,r),Promise.reject(r)}aa.error("IMPromise.reject code(错误码)必须为数字，message(错误信息)必须为字符串!!!")}},lg="sdkReady",pg="login",fg="longpolling",hg="longpollingAV",dg="sendMessage",gg="messageReceived",mg="messageReceivedAV",vg="initConversationList",yg="initGroupList",_g="upload",Cg=function(){function e(){wn(this,e),this.SDKAppID="",this.version="",this.tinyID="",this.userID="",this.platform="",this.method="",this.time="",this.startts=0,this.endts=0,this.timespan=0,this.codeint=0,this.message="",this.text="",this.msgType="",this.networkType="",this.platform="",this._sentFlag=!1}return bn(e,[{key:"setCommonInfo",value:function(e,t,n,r,o){this.SDKAppID="".concat(e),this.version="".concat(t),this.tinyID=n,this.userID=r,this.platform=o,this.time=Ta(),this.startts&&this.endts&&!this.timespan&&(this.timespan=Math.abs(this.endts-this.startts))}},{key:"setMethod",value:function(e){return this.method=e,this}},{key:"setStart",value:function(){this.startts=Date.now()}},{key:"setEnd",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this._sentFlag||(this.endts=Date.now(),t?(this._sentFlag=!0,this._eventStatController.pushIn(this)):setTimeout((function(){e._sentFlag=!0,e._eventStatController.pushIn(e)}),0))}},{key:"setError",value:function(e,t,n){return e instanceof Error?(this._sentFlag||(this.setNetworkType(n),t?(e.code&&this.setCode(e.code),e.message&&this.setMessage(e.message)):(this.setCode(If),this.setMessage(Ah))),this):(aa.warn("SSOLogData.setError value not instanceof Error, please check!"),this)}},{key:"setCode",value:function(e){return da(e)||this._sentFlag||("ECONNABORTED"===e&&(this.codeint=103),ca(e)?this.codeint=e:aa.warn("SSOLogData.setCode value not a number, please check!",e,kn(e))),this}},{key:"setMessage",value:function(e){return da(e)||this._sentFlag?this:la(e)?(this.message=e,this):this}},{key:"setText",value:function(e){return ca(e)?this.text=e.toString():la(e)&&(this.text=e),this}},{key:"setMessageType",value:function(e){return this.msgType=e,this}},{key:"setNetworkType",value:function(e){return da(e)?aa.warn("SSOLogData.setNetworkType value is undefined, please check!"):this.networkType=e,this}}],[{key:"bindController",value:function(t){e.prototype._eventStatController=t}}]),e}(),Ig="sdkConstruct",Mg="sdkReady",Sg="accessLayer",Tg="login",Eg="logout",Dg="kickedOut",kg="registerPlugin",wg="getCosAuthKey",Ag="upload",bg="sendMessage",Rg="getC2CRoamingMessages",Og="getGroupRoamingMessages",Lg="revokeMessage",Ng="setC2CMessageRead",Pg="setGroupMessageRead",Gg="emptyMessageBody",xg="getConversationList",Ug="getConversationProfile",qg="deleteConversation",Fg="getConversationListInStorage",jg="syncConversationList",Bg="createGroup",Hg="applyJoinGroup",Vg="quitGroup",Kg="searchGroupByID",$g="changeGroupOwner",Yg="handleGroupApplication",zg="setMessageRemindType",Wg="dismissGroup",Xg="updateGroupProfile",Jg="getGroupList",Qg="getGroupProfile",Zg="getGroupListInStorage",em="getGroupLastSequence",tm="getGroupMemberList",nm="getGroupMemberProfile",rm="addGroupMember",om="deleteGroupMember",im="setGroupMemberMuteTime",sm="setGroupMemberNameCard",am="setGroupMemberRole",um="setGroupMemberCustomField",cm="getLongPollID",lm="longPollingError",pm="networkJitter",fm="fastStart",hm="slowStart",dm="messageLoss",gm="getUserProfile",mm="updateMyProfile",vm="getBlacklist",ym="addToBlacklist",_m="removeFromBlacklist",Cm="mpHideToShow",Im="callbackFunctionError",Mm="exceptionError",Sm=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;return wn(this,n),(r=t.call(this,e))._initializeListener(),r}return bn(n,[{key:"login",value:function(e){if(this.isLoggedIn()){var t="您已经登录账号".concat(e.identifier,"！如需切换账号登录，请先调用 logout 接口登出，再调用 login 接口登录。");return aa.warn(t),ug({actionStatus:"OK",errorCode:0,errorInfo:t,repeatLogin:!0})}aa.log("SignController.login userID=",e.identifier),aa.time(pg);var n=this._checkLoginInfo(e);return qa(n)?(this.tim.context.identifier=e.identifier,this.tim.context.userSig=e.userSig,this.tim.context.identifier&&this.tim.context.userSig?this._accessLayer():void 0):cg(n)}},{key:"_isLoginCurrentUser",value:function(e){return this.tim.context.identifier===e}},{key:"_initializeListener",value:function(){var e=this.tim.innerEmitter;e.on(vd,this._onMultipleAccountKickedOut,this),e.on(yd,this._onMultipleDeviceKickedOut,this),e.on(Nd,this._onUserSigExpired,this)}},{key:"_accessLayer",value:function(){var e=this,t=new Cg;return t.setMethod(Sg).setStart(),aa.log("SignController._accessLayer."),this.request({name:"accessLayer",action:"query"}).then((function(n){return t.setCode(0).setNetworkType(e.getNetworkType()).setText(n.data.webImAccessLayer).setEnd(),aa.log("SignController._accessLayer ok. webImAccessLayer=".concat(n.data.webImAccessLayer)),1===n.data.webImAccessLayer&&Jh.HOST.setCurrent(n.data.webImAccessLayer),e._login()})).catch((function(n){return e.probeNetwork().then((function(r){var o=Hn(r,2),i=o[0],s=o[1];t.setError(n,i,s).setEnd(!0),e.tim.eventStatController.reportAtOnce()})),aa.error("SignController._accessLayer failed. error:".concat(n)),cg(n)}))}},{key:"_login",value:function(){var e=this,t=new Cg;return t.setMethod(Tg).setStart(),this.request({name:"login",action:"query"}).then((function(n){var r=null;if(!n.data.tinyID)throw r=new _p({code:Tp,message:Pf}),t.setError(r,!0,e.getNetworkType()).setEnd(),r;if(!n.data.a2Key)throw r=new _p({code:Ep,message:Gf}),t.setError(r,!0,e.getNetworkType()).setEnd(),r;return t.setCode(0).setNetworkType(e.getNetworkType()).setText("".concat(e.tim.loginInfo.identifier)).setEnd(),aa.log("SignController.login ok. userID=".concat(e.tim.loginInfo.identifier," loginCost=").concat(aa.timeEnd(pg),"ms")),e.emitInnerEvent(Rd,{a2Key:n.data.a2Key,tinyID:n.data.tinyID}),ug(n.data)})).catch((function(n){return e.probeNetwork().then((function(e){var r=Hn(e,2),o=r[0],i=r[1];t.setError(n,o,i).setEnd(!0)})),aa.error("SignController.login failed. error:".concat(n)),cg(n)}))}},{key:"logout",value:function(){var e=new Cg;return e.setMethod(Eg).setStart(),e.setCode(0).setNetworkType(this.getNetworkType()).setText("userID=".concat(this.tim.loginInfo.identifier," type=").concat("longPollLogout")).setEnd(!0),aa.info("SignController.logout"),this.emitInnerEvent(Od),this._logout(ep).then(this._emitLogoutSuccess.bind(this)).catch(this._emitLogoutSuccess.bind(this))}},{key:"_logout",value:function(e){var t=this.tim.notificationController,n=e===Zl?"logout":"longPollLogout",r=e===Zl?{name:n,action:"query"}:{name:n,action:"query",param:{longPollID:t.getLongPollID()}};return this.request(r).catch((function(e){return aa.error("SignController._logout error:",e),cg(e)}))}},{key:"_checkLoginInfo",value:function(e){var t=0,n="";return null===e.SDKAppID?(t=Cp,n=Rf):null===e.accountType?(t=Ip,n=Of):null===e.identifier?(t=Mp,n=Lf):null===e.userSig&&(t=Sp,n=Nf),qa(t)||qa(n)?{}:{code:t,message:n}}},{key:"_emitLogoutSuccess",value:function(){return this.emitInnerEvent(Ld),ug({})}},{key:"_onMultipleAccountKickedOut",value:function(){var e=this,t=new Cg;t.setMethod(Dg).setStart(),t.setCode(0).setNetworkType(this.getNetworkType()).setText(fn.KICKED_OUT_MULT_ACCOUNT).setEnd(!0),aa.warn("SignController._onMultipleAccountKickedOut kicked out. userID=".concat(this.tim.loginInfo.identifier)),this.tim.logout().then((function(){e.emitOuterEvent(pn.KICKED_OUT,{type:fn.KICKED_OUT_MULT_ACCOUNT})}))}},{key:"_onMultipleDeviceKickedOut",value:function(){var e=this,t=new Cg;t.setMethod(Dg).setStart(),t.setCode(0).setNetworkType(this.getNetworkType()).setText(fn.KICKED_OUT_MULT_DEVICE).setEnd(!0),aa.warn("SignController._onMultipleDeviceKickedOut kicked out. userID=".concat(this.tim.loginInfo.identifier)),this.tim.logout().then((function(){e.emitOuterEvent(pn.KICKED_OUT,{type:fn.KICKED_OUT_MULT_DEVICE})}))}},{key:"_onUserSigExpired",value:function(){var e=new Cg;e.setMethod(Dg).setStart(),e.setCode(0).setNetworkType(this.getNetworkType()).setText(fn.KICKED_OUT_USERSIG_EXPIRED).setEnd(!0),aa.warn("SignController._onUserSigExpired: userSig 签名过期被踢下线"),this.emitOuterEvent(pn.KICKED_OUT,{type:fn.KICKED_OUT_USERSIG_EXPIRED}),this.tim.resetSDK()}},{key:"reset",value:function(){aa.info("SignController.reset")}}]),n}(ng),Tm=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}},Em=Object.prototype.toString;function Dm(e){return"[object Array]"===Em.call(e)}function km(e){return void 0===e}function wm(e){return null!==e&&"object"==typeof e}function Am(e){return"[object Function]"===Em.call(e)}function bm(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),Dm(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}var Rm={isArray:Dm,isArrayBuffer:function(e){return"[object ArrayBuffer]"===Em.call(e)},isBuffer:function(e){return null!==e&&!km(e)&&null!==e.constructor&&!km(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:wm,isUndefined:km,isDate:function(e){return"[object Date]"===Em.call(e)},isFile:function(e){return"[object File]"===Em.call(e)},isBlob:function(e){return"[object Blob]"===Em.call(e)},isFunction:Am,isStream:function(e){return wm(e)&&Am(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:bm,merge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]=n}for(var r=0,o=arguments.length;r<o;r++)bm(arguments[r],n);return t},deepMerge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]="object"==typeof n?e({},n):n}for(var r=0,o=arguments.length;r<o;r++)bm(arguments[r],n);return t},extend:function(e,t,n){return bm(t,(function(t,r){e[r]=n&&"function"==typeof t?Tm(t,n):t})),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}};function Om(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var Lm=function(e,t,n){if(!t)return e;var r;if(n)r=n(t);else if(Rm.isURLSearchParams(t))r=t.toString();else{var o=[];Rm.forEach(t,(function(e,t){null!=e&&(Rm.isArray(e)?t+="[]":e=[e],Rm.forEach(e,(function(e){Rm.isDate(e)?e=e.toISOString():Rm.isObject(e)&&(e=JSON.stringify(e)),o.push(Om(t)+"="+Om(e))})))})),r=o.join("&")}if(r){var i=e.indexOf("#");-1!==i&&(e=e.slice(0,i)),e+=(-1===e.indexOf("?")?"?":"&")+r}return e};function Nm(){this.handlers=[]}Nm.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},Nm.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},Nm.prototype.forEach=function(e){Rm.forEach(this.handlers,(function(t){null!==t&&e(t)}))};var Pm=Nm,Gm=function(e,t,n){return Rm.forEach(n,(function(n){e=n(e,t)})),e},xm=function(e){return!(!e||!e.__CANCEL__)};function Um(){throw new Error("setTimeout has not been defined")}function qm(){throw new Error("clearTimeout has not been defined")}var Fm=Um,jm=qm;function Bm(e){if(Fm===setTimeout)return setTimeout(e,0);if((Fm===Um||!Fm)&&setTimeout)return Fm=setTimeout,setTimeout(e,0);try{return Fm(e,0)}catch(n){try{return Fm.call(null,e,0)}catch(n){return Fm.call(this,e,0)}}}"function"==typeof Zs.setTimeout&&(Fm=setTimeout),"function"==typeof Zs.clearTimeout&&(jm=clearTimeout);var Hm,Vm=[],Km=!1,$m=-1;function Ym(){Km&&Hm&&(Km=!1,Hm.length?Vm=Hm.concat(Vm):$m=-1,Vm.length&&zm())}function zm(){if(!Km){var e=Bm(Ym);Km=!0;for(var t=Vm.length;t;){for(Hm=Vm,Vm=[];++$m<t;)Hm&&Hm[$m].run();$m=-1,t=Vm.length}Hm=null,Km=!1,function(e){if(jm===clearTimeout)return clearTimeout(e);if((jm===qm||!jm)&&clearTimeout)return jm=clearTimeout,clearTimeout(e);try{jm(e)}catch(t){try{return jm.call(null,e)}catch(t){return jm.call(this,e)}}}(e)}}function Wm(e,t){this.fun=e,this.array=t}function Xm(){}Wm.prototype.run=function(){this.fun.apply(null,this.array)};var Jm=Xm,Qm=Xm,Zm=Xm,ev=Xm,tv=Xm,nv=Xm,rv=Xm,ov=Zs.performance||{},iv=ov.now||ov.mozNow||ov.msNow||ov.oNow||ov.webkitNow||function(){return(new Date).getTime()},sv=new Date,av={nextTick:function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];Vm.push(new Wm(e,t)),1!==Vm.length||Km||Bm(zm)},title:"browser",browser:!0,env:{},argv:[],version:"",versions:{},on:Jm,addListener:Qm,once:Zm,off:ev,removeListener:tv,removeAllListeners:nv,emit:rv,binding:function(e){throw new Error("process.binding is not supported")},cwd:function(){return"/"},chdir:function(e){throw new Error("process.chdir is not supported")},umask:function(){return 0},hrtime:function(e){var t=.001*iv.call(ov),n=Math.floor(t),r=Math.floor(t%1*1e9);return e&&(n-=e[0],(r-=e[1])<0&&(n--,r+=1e9)),[n,r]},platform:"browser",release:{},config:{},uptime:function(){return(new Date-sv)/1e3}},uv=function(e,t){Rm.forEach(e,(function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])}))},cv=function(e,t,n,r,o){return function(e,t,n,r,o){return e.config=t,n&&(e.code=n),e.request=r,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}(new Error(e),t,n,r,o)},lv=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"],pv=Rm.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function r(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=r(window.location.href),function(t){var n=Rm.isString(t)?r(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0},fv=Rm.isStandardBrowserEnv()?{write:function(e,t,n,r,o,i){var s=[];s.push(e+"="+encodeURIComponent(t)),Rm.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),Rm.isString(r)&&s.push("path="+r),Rm.isString(o)&&s.push("domain="+o),!0===i&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}},hv=function(e){return new Promise((function(t,n){var r=e.data,o=e.headers;Rm.isFormData(r)&&delete o["Content-Type"];var i=new XMLHttpRequest;if(e.auth){var s=e.auth.username||"",a=e.auth.password||"";o.Authorization="Basic "+btoa(s+":"+a)}var u,c,l=(u=e.baseURL,c=e.url,u&&!/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(c)?function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}(u,c):c);if(i.open(e.method.toUpperCase(),Lm(l,e.params,e.paramsSerializer),!0),i.timeout=e.timeout,i.onreadystatechange=function(){if(i&&4===i.readyState&&(0!==i.status||i.responseURL&&0===i.responseURL.indexOf("file:"))){var r="getAllResponseHeaders"in i?function(e){var t,n,r,o={};return e?(Rm.forEach(e.split("\n"),(function(e){if(r=e.indexOf(":"),t=Rm.trim(e.substr(0,r)).toLowerCase(),n=Rm.trim(e.substr(r+1)),t){if(o[t]&&lv.indexOf(t)>=0)return;o[t]="set-cookie"===t?(o[t]?o[t]:[]).concat([n]):o[t]?o[t]+", "+n:n}})),o):o}(i.getAllResponseHeaders()):null,o={data:e.responseType&&"text"!==e.responseType?i.response:i.responseText,status:i.status,statusText:i.statusText,headers:r,config:e,request:i};!function(e,t,n){var r=n.config.validateStatus;!r||r(n.status)?e(n):t(cv("Request failed with status code "+n.status,n.config,null,n.request,n))}(t,n,o),i=null}},i.onabort=function(){i&&(n(cv("Request aborted",e,"ECONNABORTED",i)),i=null)},i.onerror=function(){n(cv("Network Error",e,null,i)),i=null},i.ontimeout=function(){var t="timeout of "+e.timeout+"ms exceeded";e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(cv(t,e,"ECONNABORTED",i)),i=null},Rm.isStandardBrowserEnv()){var p=fv,f=(e.withCredentials||pv(l))&&e.xsrfCookieName?p.read(e.xsrfCookieName):void 0;f&&(o[e.xsrfHeaderName]=f)}if("setRequestHeader"in i&&Rm.forEach(o,(function(e,t){void 0===r&&"content-type"===t.toLowerCase()?delete o[t]:i.setRequestHeader(t,e)})),Rm.isUndefined(e.withCredentials)||(i.withCredentials=!!e.withCredentials),e.responseType)try{i.responseType=e.responseType}catch(d){if("json"!==e.responseType)throw d}"function"==typeof e.onDownloadProgress&&i.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&i.upload&&i.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){i&&(i.abort(),n(e),i=null)})),void 0===r&&(r=null),i.send(r)}))},dv={"Content-Type":"application/x-www-form-urlencoded"};function gv(e,t){!Rm.isUndefined(e)&&Rm.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var mv,vv={adapter:(("undefined"!=typeof XMLHttpRequest||void 0!==av&&"[object process]"===Object.prototype.toString.call(av))&&(mv=hv),mv),transformRequest:[function(e,t){return uv(t,"Accept"),uv(t,"Content-Type"),Rm.isFormData(e)||Rm.isArrayBuffer(e)||Rm.isBuffer(e)||Rm.isStream(e)||Rm.isFile(e)||Rm.isBlob(e)?e:Rm.isArrayBufferView(e)?e.buffer:Rm.isURLSearchParams(e)?(gv(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):Rm.isObject(e)?(gv(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(n){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};Rm.forEach(["delete","get","head"],(function(e){vv.headers[e]={}})),Rm.forEach(["post","put","patch"],(function(e){vv.headers[e]=Rm.merge(dv)}));var yv=vv;function _v(e){e.cancelToken&&e.cancelToken.throwIfRequested()}var Cv=function(e){return _v(e),e.headers=e.headers||{},e.data=Gm(e.data,e.headers,e.transformRequest),e.headers=Rm.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),Rm.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||yv.adapter)(e).then((function(t){return _v(e),t.data=Gm(t.data,t.headers,e.transformResponse),t}),(function(t){return xm(t)||(_v(e),t&&t.response&&(t.response.data=Gm(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))},Iv=function(e,t){t=t||{};var n={},r=["url","method","params","data"],o=["headers","auth","proxy"],i=["baseURL","url","transformRequest","transformResponse","paramsSerializer","timeout","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","maxContentLength","validateStatus","maxRedirects","httpAgent","httpsAgent","cancelToken","socketPath"];Rm.forEach(r,(function(e){void 0!==t[e]&&(n[e]=t[e])})),Rm.forEach(o,(function(r){Rm.isObject(t[r])?n[r]=Rm.deepMerge(e[r],t[r]):void 0!==t[r]?n[r]=t[r]:Rm.isObject(e[r])?n[r]=Rm.deepMerge(e[r]):void 0!==e[r]&&(n[r]=e[r])})),Rm.forEach(i,(function(r){void 0!==t[r]?n[r]=t[r]:void 0!==e[r]&&(n[r]=e[r])}));var s=r.concat(o).concat(i),a=Object.keys(t).filter((function(e){return-1===s.indexOf(e)}));return Rm.forEach(a,(function(r){void 0!==t[r]?n[r]=t[r]:void 0!==e[r]&&(n[r]=e[r])})),n};function Mv(e){this.defaults=e,this.interceptors={request:new Pm,response:new Pm}}Mv.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=Iv(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=[Cv,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n},Mv.prototype.getUri=function(e){return e=Iv(this.defaults,e),Lm(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},Rm.forEach(["delete","get","head","options"],(function(e){Mv.prototype[e]=function(t,n){return this.request(Rm.merge(n||{},{method:e,url:t}))}})),Rm.forEach(["post","put","patch"],(function(e){Mv.prototype[e]=function(t,n,r){return this.request(Rm.merge(r||{},{method:e,url:t,data:n}))}}));var Sv=Mv;function Tv(e){this.message=e}Tv.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},Tv.prototype.__CANCEL__=!0;var Ev=Tv;function Dv(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var n=this;e((function(e){n.reason||(n.reason=new Ev(e),t(n.reason))}))}Dv.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},Dv.source=function(){var e;return{token:new Dv((function(t){e=t})),cancel:e}};var kv=Dv;function wv(e){var t=new Sv(e),n=Tm(Sv.prototype.request,t);return Rm.extend(n,Sv.prototype,t),Rm.extend(n,t),n}var Av=wv(yv);Av.Axios=Sv,Av.create=function(e){return wv(Iv(Av.defaults,e))},Av.Cancel=Ev,Av.CancelToken=kv,Av.isCancel=xm,Av.all=function(e){return Promise.all(e)},Av.spread=function(e){return function(t){return e.apply(null,t)}};var bv=Av,Rv=Av;bv.default=Rv;var Ov=bv,Lv=Ov.create({timeout:3e4,headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}});Lv.interceptors.response.use((function(e){var t=e.data,n=t.error_code,r=t.ErrorCode;return ca(n)&&(r=n),r!==Ru.SUCCESS&&(e.data.ErrorCode=Number(r)),e}),(function(e){return"Network Error"===e.message&&(!0===Lv.defaults.withCredentials&&aa.warn("Network Error, try to close `IMAxios.defaults.withCredentials` to false. (IMAxios.js)"),Lv.defaults.withCredentials=!1),Promise.reject(e)}));var Nv=function(){function e(){wn(this,e)}return bn(e,[{key:"request",value:function(e){console.warn("请注意： ConnectionBase.request() 方法必须被派生类重写:"),console.log("参数如下：\n    * @param {String} options.url string 是 开发者服务器接口地址\n    * @param {*} options.data - string/object/ArrayBuffer 否 请求的参数\n    * @param {Object} options.header - Object 否 设置请求的 header，\n    * @param {String} options.method - string GET 否 HTTP 请求方法\n    * @param {String} options.dataType - string json 否 返回的数据格式\n    * @param {String} options.responseType - string text 否 响应的数据类型\n    * @param {Boolean} isRetry - string text false 是否为重试的请求\n   ")}},{key:"_checkOptions",value:function(e){if(0==!!e.url)throw new _p({code:yf,message:Dh})}},{key:"_initOptions",value:function(e){e.method=["POST","GET","PUT","DELETE","OPTION"].indexOf(e.method)>=0?e.method:"POST",e.dataType=e.dataType||"json",e.responseType=e.responseType||"json"}}]),e}(),Pv=function(e){Nn(n,e);var t=Bn(n);function n(){var e;return wn(this,n),(e=t.call(this)).retry=2,e}return bn(n,[{key:"request",value:function(e){return this._checkOptions(e),this._initOptions(e),this._requestWithRetry({url:e.url,data:e.data,method:e.method})}},{key:"_requestWithRetry",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Lv(e).catch((function(r){return t.retry&&n<t.retry?t._requestWithRetry(e,++n):cg(new _p({code:r.code||"",message:r.message||""}))}))}}]),n}(Nv),Gv=[],xv=[],Uv="undefined"!=typeof Uint8Array?Uint8Array:Array,qv=!1;function Fv(){qv=!0;for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",t=0,n=e.length;t<n;++t)Gv[t]=e[t],xv[e.charCodeAt(t)]=t;xv["-".charCodeAt(0)]=62,xv["_".charCodeAt(0)]=63}function jv(e,t,n){for(var r,o,i=[],s=t;s<n;s+=3)r=(e[s]<<16)+(e[s+1]<<8)+e[s+2],i.push(Gv[(o=r)>>18&63]+Gv[o>>12&63]+Gv[o>>6&63]+Gv[63&o]);return i.join("")}function Bv(e){var t;qv||Fv();for(var n=e.length,r=n%3,o="",i=[],s=0,a=n-r;s<a;s+=16383)i.push(jv(e,s,s+16383>a?a:s+16383));return 1===r?(t=e[n-1],o+=Gv[t>>2],o+=Gv[t<<4&63],o+="=="):2===r&&(t=(e[n-2]<<8)+e[n-1],o+=Gv[t>>10],o+=Gv[t>>4&63],o+=Gv[t<<2&63],o+="="),i.push(o),i.join("")}function Hv(e,t,n,r,o){var i,s,a=8*o-r-1,u=(1<<a)-1,c=u>>1,l=-7,p=n?o-1:0,f=n?-1:1,h=e[t+p];for(p+=f,i=h&(1<<-l)-1,h>>=-l,l+=a;l>0;i=256*i+e[t+p],p+=f,l-=8);for(s=i&(1<<-l)-1,i>>=-l,l+=r;l>0;s=256*s+e[t+p],p+=f,l-=8);if(0===i)i=1-c;else{if(i===u)return s?NaN:1/0*(h?-1:1);s+=Math.pow(2,r),i-=c}return(h?-1:1)*s*Math.pow(2,i-r)}function Vv(e,t,n,r,o,i){var s,a,u,c=8*i-o-1,l=(1<<c)-1,p=l>>1,f=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,h=r?0:i-1,d=r?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||1/0===t?(a=isNaN(t)?1:0,s=l):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),(t+=s+p>=1?f/u:f*Math.pow(2,1-p))*u>=2&&(s++,u/=2),s+p>=l?(a=0,s=l):s+p>=1?(a=(t*u-1)*Math.pow(2,o),s+=p):(a=t*Math.pow(2,p-1)*Math.pow(2,o),s=0));o>=8;e[n+h]=255&a,h+=d,a/=256,o-=8);for(s=s<<o|a,c+=o;c>0;e[n+h]=255&s,h+=d,s/=256,c-=8);e[n+h-d]|=128*g}var Kv={}.toString,$v=Array.isArray||function(e){return"[object Array]"==Kv.call(e)};function Yv(){return Wv.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function zv(e,t){if(Yv()<t)throw new RangeError("Invalid typed array length");return Wv.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=Wv.prototype:(null===e&&(e=new Wv(t)),e.length=t),e}function Wv(e,t,n){if(!(Wv.TYPED_ARRAY_SUPPORT||this instanceof Wv))return new Wv(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return Qv(this,e)}return Xv(this,e,t,n)}function Xv(e,t,n,r){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,n,r){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");return t=void 0===n&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,n):new Uint8Array(t,n,r),Wv.TYPED_ARRAY_SUPPORT?(e=t).__proto__=Wv.prototype:e=Zv(e,t),e}(e,t,n,r):"string"==typeof t?function(e,t,n){if("string"==typeof n&&""!==n||(n="utf8"),!Wv.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|ny(t,n),o=(e=zv(e,r)).write(t,n);return o!==r&&(e=e.slice(0,o)),e}(e,t,n):function(e,t){if(ty(t)){var n=0|ey(t.length);return 0===(e=zv(e,n)).length||t.copy(e,0,0,n),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(r=t.length)!=r?zv(e,0):Zv(e,t);if("Buffer"===t.type&&$v(t.data))return Zv(e,t.data)}var r;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function Jv(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function Qv(e,t){if(Jv(t),e=zv(e,t<0?0:0|ey(t)),!Wv.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function Zv(e,t){var n=t.length<0?0:0|ey(t.length);e=zv(e,n);for(var r=0;r<n;r+=1)e[r]=255&t[r];return e}function ey(e){if(e>=Yv())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+Yv().toString(16)+" bytes");return 0|e}function ty(e){return!(null==e||!e._isBuffer)}function ny(e,t){if(ty(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return wy(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return Ay(e).length;default:if(r)return wy(e).length;t=(""+t).toLowerCase(),r=!0}}function ry(e,t,n){var r=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return vy(this,t,n);case"utf8":case"utf-8":return dy(this,t,n);case"ascii":return gy(this,t,n);case"latin1":case"binary":return my(this,t,n);case"base64":return hy(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return yy(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function oy(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function iy(e,t,n,r,o){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=o?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(o)return-1;n=e.length-1}else if(n<0){if(!o)return-1;n=0}if("string"==typeof t&&(t=Wv.from(t,r)),ty(t))return 0===t.length?-1:sy(e,t,n,r,o);if("number"==typeof t)return t&=255,Wv.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):sy(e,[t],n,r,o);throw new TypeError("val must be string, number or Buffer")}function sy(e,t,n,r,o){var i,s=1,a=e.length,u=t.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;s=2,a/=2,u/=2,n/=2}function c(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(o){var l=-1;for(i=n;i<a;i++)if(c(e,i)===c(t,-1===l?0:i-l)){if(-1===l&&(l=i),i-l+1===u)return l*s}else-1!==l&&(i-=i-l),l=-1}else for(n+u>a&&(n=a-u),i=n;i>=0;i--){for(var p=!0,f=0;f<u;f++)if(c(e,i+f)!==c(t,f)){p=!1;break}if(p)return i}return-1}function ay(e,t,n,r){n=Number(n)||0;var o=e.length-n;r?(r=Number(r))>o&&(r=o):r=o;var i=t.length;if(i%2!=0)throw new TypeError("Invalid hex string");r>i/2&&(r=i/2);for(var s=0;s<r;++s){var a=parseInt(t.substr(2*s,2),16);if(isNaN(a))return s;e[n+s]=a}return s}function uy(e,t,n,r){return by(wy(t,e.length-n),e,n,r)}function cy(e,t,n,r){return by(function(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}(t),e,n,r)}function ly(e,t,n,r){return cy(e,t,n,r)}function py(e,t,n,r){return by(Ay(t),e,n,r)}function fy(e,t,n,r){return by(function(e,t){for(var n,r,o,i=[],s=0;s<e.length&&!((t-=2)<0);++s)n=e.charCodeAt(s),r=n>>8,o=n%256,i.push(o),i.push(r);return i}(t,e.length-n),e,n,r)}function hy(e,t,n){return 0===t&&n===e.length?Bv(e):Bv(e.slice(t,n))}function dy(e,t,n){n=Math.min(e.length,n);for(var r=[],o=t;o<n;){var i,s,a,u,c=e[o],l=null,p=c>239?4:c>223?3:c>191?2:1;if(o+p<=n)switch(p){case 1:c<128&&(l=c);break;case 2:128==(192&(i=e[o+1]))&&(u=(31&c)<<6|63&i)>127&&(l=u);break;case 3:i=e[o+1],s=e[o+2],128==(192&i)&&128==(192&s)&&(u=(15&c)<<12|(63&i)<<6|63&s)>2047&&(u<55296||u>57343)&&(l=u);break;case 4:i=e[o+1],s=e[o+2],a=e[o+3],128==(192&i)&&128==(192&s)&&128==(192&a)&&(u=(15&c)<<18|(63&i)<<12|(63&s)<<6|63&a)>65535&&u<1114112&&(l=u)}null===l?(l=65533,p=1):l>65535&&(l-=65536,r.push(l>>>10&1023|55296),l=56320|1023&l),r.push(l),o+=p}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);for(var n="",r=0;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=4096));return n}(r)}function gy(e,t,n){var r="";n=Math.min(e.length,n);for(var o=t;o<n;++o)r+=String.fromCharCode(127&e[o]);return r}function my(e,t,n){var r="";n=Math.min(e.length,n);for(var o=t;o<n;++o)r+=String.fromCharCode(e[o]);return r}function vy(e,t,n){var r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);for(var o="",i=t;i<n;++i)o+=ky(e[i]);return o}function yy(e,t,n){for(var r=e.slice(t,n),o="",i=0;i<r.length;i+=2)o+=String.fromCharCode(r[i]+256*r[i+1]);return o}function _y(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function Cy(e,t,n,r,o,i){if(!ty(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>o||t<i)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function Iy(e,t,n,r){t<0&&(t=65535+t+1);for(var o=0,i=Math.min(e.length-n,2);o<i;++o)e[n+o]=(t&255<<8*(r?o:1-o))>>>8*(r?o:1-o)}function My(e,t,n,r){t<0&&(t=4294967295+t+1);for(var o=0,i=Math.min(e.length-n,4);o<i;++o)e[n+o]=t>>>8*(r?o:3-o)&255}function Sy(e,t,n,r,o,i){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function Ty(e,t,n,r,o){return o||Sy(e,0,n,4),Vv(e,t,n,r,23,4),n+4}function Ey(e,t,n,r,o){return o||Sy(e,0,n,8),Vv(e,t,n,r,52,8),n+8}Wv.TYPED_ARRAY_SUPPORT=void 0===Zs.TYPED_ARRAY_SUPPORT||Zs.TYPED_ARRAY_SUPPORT,Wv.poolSize=8192,Wv._augment=function(e){return e.__proto__=Wv.prototype,e},Wv.from=function(e,t,n){return Xv(null,e,t,n)},Wv.TYPED_ARRAY_SUPPORT&&(Wv.prototype.__proto__=Uint8Array.prototype,Wv.__proto__=Uint8Array),Wv.alloc=function(e,t,n){return function(e,t,n,r){return Jv(t),t<=0?zv(e,t):void 0!==n?"string"==typeof r?zv(e,t).fill(n,r):zv(e,t).fill(n):zv(e,t)}(null,e,t,n)},Wv.allocUnsafe=function(e){return Qv(null,e)},Wv.allocUnsafeSlow=function(e){return Qv(null,e)},Wv.isBuffer=function(e){return null!=e&&(!!e._isBuffer||Ry(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&Ry(e.slice(0,0))}(e))},Wv.compare=function(e,t){if(!ty(e)||!ty(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,o=0,i=Math.min(n,r);o<i;++o)if(e[o]!==t[o]){n=e[o],r=t[o];break}return n<r?-1:r<n?1:0},Wv.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},Wv.concat=function(e,t){if(!$v(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return Wv.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var r=Wv.allocUnsafe(t),o=0;for(n=0;n<e.length;++n){var i=e[n];if(!ty(i))throw new TypeError('"list" argument must be an Array of Buffers');i.copy(r,o),o+=i.length}return r},Wv.byteLength=ny,Wv.prototype._isBuffer=!0,Wv.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)oy(this,t,t+1);return this},Wv.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)oy(this,t,t+3),oy(this,t+1,t+2);return this},Wv.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)oy(this,t,t+7),oy(this,t+1,t+6),oy(this,t+2,t+5),oy(this,t+3,t+4);return this},Wv.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?dy(this,0,e):ry.apply(this,arguments)},Wv.prototype.equals=function(e){if(!ty(e))throw new TypeError("Argument must be a Buffer");return this===e||0===Wv.compare(this,e)},Wv.prototype.inspect=function(){var e="";return this.length>0&&(e=this.toString("hex",0,50).match(/.{2}/g).join(" "),this.length>50&&(e+=" ... ")),"<Buffer "+e+">"},Wv.prototype.compare=function(e,t,n,r,o){if(!ty(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===o&&(o=this.length),t<0||n>e.length||r<0||o>this.length)throw new RangeError("out of range index");if(r>=o&&t>=n)return 0;if(r>=o)return-1;if(t>=n)return 1;if(this===e)return 0;for(var i=(o>>>=0)-(r>>>=0),s=(n>>>=0)-(t>>>=0),a=Math.min(i,s),u=this.slice(r,o),c=e.slice(t,n),l=0;l<a;++l)if(u[l]!==c[l]){i=u[l],s=c[l];break}return i<s?-1:s<i?1:0},Wv.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},Wv.prototype.indexOf=function(e,t,n){return iy(this,e,t,n,!0)},Wv.prototype.lastIndexOf=function(e,t,n){return iy(this,e,t,n,!1)},Wv.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var o=this.length-t;if((void 0===n||n>o)&&(n=o),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var i=!1;;)switch(r){case"hex":return ay(this,e,t,n);case"utf8":case"utf-8":return uy(this,e,t,n);case"ascii":return cy(this,e,t,n);case"latin1":case"binary":return ly(this,e,t,n);case"base64":return py(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return fy(this,e,t,n);default:if(i)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),i=!0}},Wv.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},Wv.prototype.slice=function(e,t){var n,r=this.length;if((e=~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),(t=void 0===t?r:~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e),Wv.TYPED_ARRAY_SUPPORT)(n=this.subarray(e,t)).__proto__=Wv.prototype;else{var o=t-e;n=new Wv(o,void 0);for(var i=0;i<o;++i)n[i]=this[i+e]}return n},Wv.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||_y(e,t,this.length);for(var r=this[e],o=1,i=0;++i<t&&(o*=256);)r+=this[e+i]*o;return r},Wv.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||_y(e,t,this.length);for(var r=this[e+--t],o=1;t>0&&(o*=256);)r+=this[e+--t]*o;return r},Wv.prototype.readUInt8=function(e,t){return t||_y(e,1,this.length),this[e]},Wv.prototype.readUInt16LE=function(e,t){return t||_y(e,2,this.length),this[e]|this[e+1]<<8},Wv.prototype.readUInt16BE=function(e,t){return t||_y(e,2,this.length),this[e]<<8|this[e+1]},Wv.prototype.readUInt32LE=function(e,t){return t||_y(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},Wv.prototype.readUInt32BE=function(e,t){return t||_y(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},Wv.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||_y(e,t,this.length);for(var r=this[e],o=1,i=0;++i<t&&(o*=256);)r+=this[e+i]*o;return r>=(o*=128)&&(r-=Math.pow(2,8*t)),r},Wv.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||_y(e,t,this.length);for(var r=t,o=1,i=this[e+--r];r>0&&(o*=256);)i+=this[e+--r]*o;return i>=(o*=128)&&(i-=Math.pow(2,8*t)),i},Wv.prototype.readInt8=function(e,t){return t||_y(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},Wv.prototype.readInt16LE=function(e,t){t||_y(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},Wv.prototype.readInt16BE=function(e,t){t||_y(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},Wv.prototype.readInt32LE=function(e,t){return t||_y(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},Wv.prototype.readInt32BE=function(e,t){return t||_y(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},Wv.prototype.readFloatLE=function(e,t){return t||_y(e,4,this.length),Hv(this,e,!0,23,4)},Wv.prototype.readFloatBE=function(e,t){return t||_y(e,4,this.length),Hv(this,e,!1,23,4)},Wv.prototype.readDoubleLE=function(e,t){return t||_y(e,8,this.length),Hv(this,e,!0,52,8)},Wv.prototype.readDoubleBE=function(e,t){return t||_y(e,8,this.length),Hv(this,e,!1,52,8)},Wv.prototype.writeUIntLE=function(e,t,n,r){e=+e,t|=0,n|=0,r||Cy(this,e,t,n,Math.pow(2,8*n)-1,0);var o=1,i=0;for(this[t]=255&e;++i<n&&(o*=256);)this[t+i]=e/o&255;return t+n},Wv.prototype.writeUIntBE=function(e,t,n,r){e=+e,t|=0,n|=0,r||Cy(this,e,t,n,Math.pow(2,8*n)-1,0);var o=n-1,i=1;for(this[t+o]=255&e;--o>=0&&(i*=256);)this[t+o]=e/i&255;return t+n},Wv.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||Cy(this,e,t,1,255,0),Wv.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},Wv.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||Cy(this,e,t,2,65535,0),Wv.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):Iy(this,e,t,!0),t+2},Wv.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||Cy(this,e,t,2,65535,0),Wv.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):Iy(this,e,t,!1),t+2},Wv.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||Cy(this,e,t,4,4294967295,0),Wv.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):My(this,e,t,!0),t+4},Wv.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||Cy(this,e,t,4,4294967295,0),Wv.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):My(this,e,t,!1),t+4},Wv.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t|=0,!r){var o=Math.pow(2,8*n-1);Cy(this,e,t,n,o-1,-o)}var i=0,s=1,a=0;for(this[t]=255&e;++i<n&&(s*=256);)e<0&&0===a&&0!==this[t+i-1]&&(a=1),this[t+i]=(e/s>>0)-a&255;return t+n},Wv.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t|=0,!r){var o=Math.pow(2,8*n-1);Cy(this,e,t,n,o-1,-o)}var i=n-1,s=1,a=0;for(this[t+i]=255&e;--i>=0&&(s*=256);)e<0&&0===a&&0!==this[t+i+1]&&(a=1),this[t+i]=(e/s>>0)-a&255;return t+n},Wv.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||Cy(this,e,t,1,127,-128),Wv.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},Wv.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||Cy(this,e,t,2,32767,-32768),Wv.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):Iy(this,e,t,!0),t+2},Wv.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||Cy(this,e,t,2,32767,-32768),Wv.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):Iy(this,e,t,!1),t+2},Wv.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||Cy(this,e,t,4,2147483647,-2147483648),Wv.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):My(this,e,t,!0),t+4},Wv.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||Cy(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),Wv.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):My(this,e,t,!1),t+4},Wv.prototype.writeFloatLE=function(e,t,n){return Ty(this,e,t,!0,n)},Wv.prototype.writeFloatBE=function(e,t,n){return Ty(this,e,t,!1,n)},Wv.prototype.writeDoubleLE=function(e,t,n){return Ey(this,e,t,!0,n)},Wv.prototype.writeDoubleBE=function(e,t,n){return Ey(this,e,t,!1,n)},Wv.prototype.copy=function(e,t,n,r){if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var o,i=r-n;if(this===e&&n<t&&t<r)for(o=i-1;o>=0;--o)e[o+t]=this[o+n];else if(i<1e3||!Wv.TYPED_ARRAY_SUPPORT)for(o=0;o<i;++o)e[o+t]=this[o+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+i),t);return i},Wv.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===e.length){var o=e.charCodeAt(0);o<256&&(e=o)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!Wv.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;var i;if(t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0),"number"==typeof e)for(i=t;i<n;++i)this[i]=e;else{var s=ty(e)?e:wy(new Wv(e,r).toString()),a=s.length;for(i=0;i<n-t;++i)this[i+t]=s[i%a]}return this};var Dy=/[^+\/0-9A-Za-z-_]/g;function ky(e){return e<16?"0"+e.toString(16):e.toString(16)}function wy(e,t){var n;t=t||1/0;for(var r=e.length,o=null,i=[],s=0;s<r;++s){if((n=e.charCodeAt(s))>55295&&n<57344){if(!o){if(n>56319){(t-=3)>-1&&i.push(239,191,189);continue}if(s+1===r){(t-=3)>-1&&i.push(239,191,189);continue}o=n;continue}if(n<56320){(t-=3)>-1&&i.push(239,191,189),o=n;continue}n=65536+(o-55296<<10|n-56320)}else o&&(t-=3)>-1&&i.push(239,191,189);if(o=null,n<128){if((t-=1)<0)break;i.push(n)}else if(n<2048){if((t-=2)<0)break;i.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;i.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;i.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return i}function Ay(e){return function(e){var t,n,r,o,i,s;qv||Fv();var a=e.length;if(a%4>0)throw new Error("Invalid string. Length must be a multiple of 4");i="="===e[a-2]?2:"="===e[a-1]?1:0,s=new Uv(3*a/4-i),r=i>0?a-4:a;var u=0;for(t=0,n=0;t<r;t+=4,n+=3)o=xv[e.charCodeAt(t)]<<18|xv[e.charCodeAt(t+1)]<<12|xv[e.charCodeAt(t+2)]<<6|xv[e.charCodeAt(t+3)],s[u++]=o>>16&255,s[u++]=o>>8&255,s[u++]=255&o;return 2===i?(o=xv[e.charCodeAt(t)]<<2|xv[e.charCodeAt(t+1)]>>4,s[u++]=255&o):1===i&&(o=xv[e.charCodeAt(t)]<<10|xv[e.charCodeAt(t+1)]<<4|xv[e.charCodeAt(t+2)]>>2,s[u++]=o>>8&255,s[u++]=255&o),s}(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(Dy,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function by(e,t,n,r){for(var o=0;o<r&&!(o+n>=t.length||o>=e.length);++o)t[o+n]=e[o];return o}function Ry(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}var Oy=function(e){Nn(n,e);var t=Bn(n);function n(){var e;return wn(this,n),(e=t.call(this)).retry=2,e._request=e.promisify(wx.request),e}return bn(n,[{key:"request",value:function(e){return this._checkOptions(e),this._initOptions(e),e=Ln({},e,{responseType:"text"}),this._requestWithRetry(e)}},{key:"_requestWithRetry",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._request(e).then(this._handleResolve).catch((function(r){if(la(r.errMsg)){if(r.errMsg.includes("abort"))return ug({});if(r.errMsg.includes("timeout"))return t.retry>0&&n<t.retry?t._requestWithRetry(e,++n):cg(new _p({code:vf,message:r.errMsg}));if(r.errMsg.includes("fail"))return t.retry>0&&n<t.retry?t._requestWithRetry(e,++n):cg(new _p({code:mf,message:r.errMsg}))}return cg(new _p(Ln({code:Ef,message:r.message},r)))}))}},{key:"_handleResolve",value:function(e){var t=e.data,n=t.error_code,r=t.ErrorCode;return"number"==typeof n&&(r=n),r!==Ru.SUCCESS&&(e.data.ErrorCode=Number("".concat(r))),e}},{key:"promisify",value:function(e){return function(t){return new Promise((function(n,r){var o=e(Object.assign({},t,{success:n,fail:r}));t.updateAbort&&t.updateAbort((function(){o&&ma(o.abort)&&o.abort()}))}))}}}]),n}(Nv),Ly=function(){function e(){wn(this,e),this.request=0,this.success=0,this.fail=0,this.reportRate=10,this.requestTimeCost=[]}return bn(e,[{key:"report",value:function(){if(1!==this.request){if(this.request%this.reportRate!=0)return null;var e=this.avgRequestTime(),t="runLoop reports: success=".concat(this.success,",fail=").concat(this.fail,",total=").concat(this.request,",avg=").concat(e,",cur=").concat(this.requestTimeCost[this.requestTimeCost.length-1],",max=").concat(Math.max.apply(null,this.requestTimeCost),",min=").concat(Math.min.apply(null,this.requestTimeCost));aa.log(t)}}},{key:"setRequestTime",value:function(e,t){var n=Math.abs(t-e);100===this.requestTimeCost.length&&this.requestTimeCost.shift(),this.requestTimeCost.push(n)}},{key:"avgRequestTime",value:function(){for(var e,t=this.requestTimeCost.length,n=0,r=0;r<t;r++)n+=this.requestTimeCost[r];return e=n/t,Math.round(100*e)/100}}]),e}(),Ny=Ov.create({timeout:6e3,headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}});Ny.interceptors.response.use((function(e){var t=e.data,n=t.error_code,r=t.ErrorCode;return ca(n)&&(r=n),r!==Ru.SUCCESS&&(e.data.ErrorCode=Number(r)),e}),(function(e){return"Network Error"===e.message&&(!0===Ny.defaults.withCredentials&&aa.warn("Network Error, try to close `IMAxiosAVChatroom.defaults.withCredentials` to false. (IMAxiosAVChatroom.js)"),Ny.defaults.withCredentials=!1),Promise.reject(e)}));var Py=Ov.CancelToken,Gy=function(){function e(t){wn(this,e),this._initializeOptions(t),this._initializeMembers(),this.status=new Ly}return bn(e,[{key:"destructor",value:function(){clearTimeout(this._seedID);var e=this._index();for(var t in this)Object.prototype.hasOwnProperty.call(this,t)&&(this[t]=null);return e}},{key:"setIndex",value:function(e){this._index=e}},{key:"getIndex",value:function(){return this._index}},{key:"isRunning",value:function(){return!this._stoped}},{key:"_initializeOptions",value:function(e){this.options=e}},{key:"_initializeMembers",value:function(){this._index=-1,this._seedID=0,this._requestStatus=!1,this._stoped=!1,this._intervalTime=0,this._intervalIncreaseStep=1e3,this._intervalDecreaseStep=1e3,this._intervalTimeMax=5e3,this._protectTimeout=3e3,this._getNoticeSeq=this.options.getNoticeSeq,this._retryCount=0,this._responseTime=Date.now(),this._responseTimeThreshold=2e3,this.options.isAVChatRoomLoop?this.requestor=Ny:this.requestor=Lv,aa.log("XHRRunLoop._initializeMembers isAVChatRoomLoop=".concat(!!this.options.isAVChatRoomLoop)),this.abort=null}},{key:"start",value:function(){0===this._seedID?(this._stoped=!1,this._send()):aa.log('XHRRunLoop.start(), XHRRunLoop is running now, if you want to restart runLoop , please run "stop()" first.')}},{key:"_reset",value:function(){aa.log("XHRRunLoop._reset(), reset long poll _intervalTime",this._intervalTime),this.stop(),this.start()}},{key:"_intervalTimeIncrease",value:function(){this._intervalTime!==this._responseTimeThreshold&&(this._intervalTime<this._responseTimeThreshold&&(this._intervalTime+=this._intervalIncreaseStep),this._intervalTime>this._responseTimeThreshold&&(this._intervalTime=this._responseTimeThreshold))}},{key:"_intervalTimeDecrease",value:function(){0!==this._intervalTime&&(this._intervalTime>0&&(this._intervalTime-=this._intervalDecreaseStep),this._intervalTime<0&&(this._intervalTime=0))}},{key:"_intervalTimeAdjustment",value:function(){var e=Date.now();100*Math.floor((e-this._responseTime)/100)<=this._responseTimeThreshold?this._intervalTimeIncrease():this._intervalTimeDecrease(),this._responseTime=e}},{key:"_intervalTimeAdjustmentBaseOnResponseData",value:function(e){e.ErrorCode===Ru.SUCCESS?this._intervalTimeDecrease():this._intervalTimeIncrease()}},{key:"_send",value:function(){var e=this;if(!0!==this._requestStatus){this._requestStatus=!0,this.status.request++,"function"==typeof this.options.before&&this.options.before(this.options.pack.requestData);var t=Date.now(),n=0;this.requestor.request({url:this.options.pack.getUrl(),data:this.options.pack.requestData,method:this.options.pack.method,cancelToken:new Py((function(t){e.abort=t}))}).then((function(r){if(e._intervalTimeAdjustmentBaseOnResponseData.bind(e)(r.data),e._retryCount>0&&(e._retryCount=0),e.status.success++,n=Date.now(),e.status.setRequestTime(t,n),r.data.timecost=n-t,"function"==typeof e.options.success)try{e.options.success({pack:e.options.pack,error:!1,data:e.options.pack.callback(r.data)})}catch(i){aa.warn("XHRRunLoop._send(), error:",i)}e._requestStatus=!1,!1===e._stoped&&(e._seedID=setTimeout(e._send.bind(e),e._intervalTime)),e.status.report()})).catch((function(r){if(e.status.fail++,e._retryCount++,e._intervalTimeAdjustment.bind(e)(),!1===e._stoped&&(e._seedID=setTimeout(e._send.bind(e),e._intervalTime)),e._requestStatus=!1,"function"==typeof e.options.fail&&void 0!==r.request)try{e.options.fail({pack:e.options.pack,error:r,data:!1})}catch(i){aa.warn("XHRRunLoop._send(), fail callback error:"),aa.error(i)}n=Date.now(),e.status.setRequestTime(t,n),e.status.report()}))}}},{key:"stop",value:function(){this._clearAllTimeOut(),this._stoped=!0}},{key:"_clearAllTimeOut",value:function(){clearTimeout(this._seedID),this._seedID=0}}]),e}(),xy=function(){function e(t){wn(this,e),this._initializeOptions(t),this._initializeMembers(),this.status=new Ly}return bn(e,[{key:"destructor",value:function(){clearTimeout(this._seedID);var e=this._index();for(var t in this)Object.prototype.hasOwnProperty.call(this,t)&&(this[t]=null);return e}},{key:"setIndex",value:function(e){this._index=e}},{key:"isRunning",value:function(){return!this._stoped}},{key:"getIndex",value:function(){return this._index}},{key:"_initializeOptions",value:function(e){this.options=e}},{key:"_initializeMembers",value:function(){this._index=-1,this._seedID=0,this._requestStatus=!1,this._stoped=!1,this._intervalTime=0,this._intervalIncreaseStep=1e3,this._intervalDecreaseStep=1e3,this._intervalTimeMax=5e3,this._protectTimeout=3e3,this._getNoticeSeq=this.options.getNoticeSeq,this._retryCount=0,this._responseTime=Date.now(),this._responseTimeThreshold=2e3,this.requestor=new Oy,this.abort=null}},{key:"start",value:function(){0===this._seedID?(this._stoped=!1,this._send()):aa.log('WXRunLoop.start(): WXRunLoop is running now, if you want to restart runLoop , please run "stop()" first.')}},{key:"_reset",value:function(){aa.log("WXRunLoop.reset(), long poll _intervalMaxRate",this._intervalMaxRate),this.stop(),this.start()}},{key:"_intervalTimeIncrease",value:function(){this._intervalTime!==this._responseTimeThreshold&&(this._intervalTime<this._responseTimeThreshold&&(this._intervalTime+=this._intervalIncreaseStep),this._intervalTime>this._responseTimeThreshold&&(this._intervalTime=this._responseTimeThreshold))}},{key:"_intervalTimeDecrease",value:function(){0!==this._intervalTime&&(this._intervalTime>0&&(this._intervalTime-=this._intervalDecreaseStep),this._intervalTime<0&&(this._intervalTime=0))}},{key:"_intervalTimeAdjustment",value:function(){var e=Date.now();100*Math.floor((e-this._responseTime)/100)<=this._responseTimeThreshold?this._intervalTimeIncrease():this._intervalTimeDecrease(),this._responseTime=e}},{key:"_intervalTimeAdjustmentBaseOnResponseData",value:function(e){e.ErrorCode===Ru.SUCCESS?this._intervalTimeDecrease():this._intervalTimeIncrease()}},{key:"_send",value:function(){var e=this;if(!0!==this._requestStatus){var t=this;this._requestStatus=!0,this.status.request++,"function"==typeof this.options.before&&this.options.before(t.options.pack.requestData);var n=Date.now(),r=0;this.requestor.request({url:t.options.pack.getUrl(),data:t.options.pack.requestData,method:t.options.pack.method,updateAbort:function(t){e.abort=t}}).then((function(o){if(t._intervalTimeAdjustmentBaseOnResponseData.bind(e)(o.data),t._retryCount>0&&(t._retryCount=0),e.status.success++,r=Date.now(),e.status.setRequestTime(n,r),o.data.timecost=r-n,"function"==typeof t.options.success)try{e.options.success({pack:e.options.pack,error:!1,data:e.options.pack.callback(o.data)})}catch(s){aa.warn("WXRunLoop._send(), error:",s)}t._requestStatus=!1,!1===t._stoped&&(t._seedID=setTimeout(t._send.bind(t),t._intervalTime)),e.status.report()})).catch((function(o){if(e.status.fail++,t._retryCount++,t._intervalTimeAdjustment.bind(e)(),!1===t._stoped&&(t._seedID=setTimeout(t._send.bind(t),t._intervalTime)),t._requestStatus=!1,"function"==typeof t.options.fail)try{e.options.fail({pack:e.options.pack,error:o,data:!1})}catch(s){aa.warn("WXRunLoop._send(), fail callback error:"),aa.error(s)}r=Date.now(),e.status.setRequestTime(n,r),e.status.report()}))}}},{key:"stop",value:function(){this._clearAllTimeOut(),this._stoped=!0}},{key:"_clearAllTimeOut",value:function(){clearTimeout(this._seedID),this._seedID=0}}]),e}(),Uy=function(){function e(t){wn(this,e),this.tim=t,this.httpConnection=Os?new Oy:new Pv,this.keepAliveConnections=[]}return bn(e,[{key:"initializeListener",value:function(){this.tim.innerEmitter.on(Od,this._stopAllRunLoop,this)}},{key:"request",value:function(e){var t={url:e.url,data:e.requestData,method:e.method,callback:e.callback};return this.httpConnection.request(t).then((function(t){return t.data=e.callback(t.data),t.data.errorCode!==Ru.SUCCESS?cg(new _p({code:t.data.errorCode,message:t.data.errorInfo})):t}))}},{key:"createRunLoop",value:function(e){var t=this.createKeepAliveConnection(e);return t.setIndex(this.keepAliveConnections.push(t)-1),t}},{key:"stopRunLoop",value:function(e){e.stop()}},{key:"_stopAllRunLoop",value:function(){for(var e=this.keepAliveConnections.length,t=0;t<e;t++)this.keepAliveConnections[t].stop()}},{key:"destroyRunLoop",value:function(e){e.stop();var t=e.destructor();this.keepAliveConnections.slice(t,1)}},{key:"startRunLoopExclusive",value:function(e){for(var t=e.getIndex(),n=0;n<this.keepAliveConnections.length;n++)n!==t&&this.keepAliveConnections[n].stop();e.start()}},{key:"createKeepAliveConnection",value:function(e){return Os?new xy(e):(this.tim.options.runLoopNetType===Ql||this.tim.options.runLoopNetType,new Gy(e))}},{key:"clearAll",value:function(){this.conn.cancelAll()}},{key:"reset",value:function(){this.keepAliveConnections=[]}}]),e}(),qy=function(){function e(t){wn(this,e),this.tim=t,this.tim.innerEmitter.on(Pd,this._onErrorDetected,this)}return bn(e,[{key:"_onErrorDetected",value:function(e){var t=e.data,n=new Cg;n.setMethod(Mm).setStart(),n.setCode(0).setText("code=".concat(t.code," message=").concat(t.message)).setNetworkType(this.tim.netMonitor.getNetworkType()).setEnd(),t.code?aa.warn("Oops! code:".concat(t.code," message:").concat(t.message)):aa.warn("Oops! message:".concat(t.message," stack:").concat(t.stack)),this.tim.outerEmitter.emit(pn.ERROR,t)}}]),e}(),Fy=function(){function e(t){var n=this;wn(this,e),qa(t)||(this.userID=t.userID||"",this.nick=t.nick||"",this.gender=t.gender||"",this.birthday=t.birthday||0,this.location=t.location||"",this.selfSignature=t.selfSignature||"",this.allowType=t.allowType||fn.ALLOW_TYPE_ALLOW_ANY,this.language=t.language||0,this.avatar=t.avatar||"",this.messageSettings=t.messageSettings||0,this.adminForbidType=t.adminForbidType||fn.FORBID_TYPE_NONE,this.level=t.level||0,this.role=t.role||0,this.lastUpdatedTime=0,this.profileCustomField=[],qa(t.profileCustomField)||t.profileCustomField.forEach((function(e){n.profileCustomField.push({key:e.key,value:e.value})})))}return bn(e,[{key:"validate",value:function(e){var t=!0,n="";if(qa(e))return{valid:!1,tips:"empty options"};if(e.profileCustomField)for(var r=e.profileCustomField.length,o=null,i=0;i<r;i++){if(o=e.profileCustomField[i],!la(o.key)||-1===o.key.indexOf("Tag_Profile_Custom"))return{valid:!1,tips:"自定义资料字段的前缀必须是 Tag_Profile_Custom"};if(!la(o.value))return{valid:!1,tips:"自定义资料字段的 value 必须是字符串"}}for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if("profileCustomField"===s)continue;if(qa(e[s])&&!la(e[s])&&!ca(e[s])){n="key:"+s+", invalid value:"+e[s],t=!1;continue}switch(s){case"nick":la(e[s])||(n="nick should be a string",t=!1),Ea(e[s])>500&&(n="nick name limited: must less than or equal to ".concat(500," bytes, current size: ").concat(Ea(e[s])," bytes"),t=!1);break;case"gender":Aa(np,e.gender)||(n="key:gender, invalid value:"+e.gender,t=!1);break;case"birthday":ca(e.birthday)||(n="birthday should be a number",t=!1);break;case"location":la(e.location)||(n="location should be a string",t=!1);break;case"selfSignature":la(e.selfSignature)||(n="selfSignature should be a string",t=!1);break;case"allowType":Aa(op,e.allowType)||(n="key:allowType, invalid value:"+e.allowType,t=!1);break;case"language":ca(e.language)||(n="language should be a number",t=!1);break;case"avatar":la(e.avatar)||(n="avatar should be a string",t=!1);break;case"messageSettings":0!==e.messageSettings&&1!==e.messageSettings&&(n="messageSettings should be 0 or 1",t=!1);break;case"adminForbidType":Aa(rp,e.adminForbidType)||(n="key:adminForbidType, invalid value:"+e.adminForbidType,t=!1);break;case"level":ca(e.level)||(n="level should be a number",t=!1);break;case"role":ca(e.role)||(n="role should be a number",t=!1);break;default:n="unknown key:"+s+"  "+e[s],t=!1}}return{valid:t,tips:n}}}]),e}(),jy=function(){function e(t){wn(this,e),this.userController=t,this.TAG="profile",this.Actions={Q:"query",U:"update"},this.accountProfileMap=new Map,this.expirationTime=864e5}return bn(e,[{key:"setExpirationTime",value:function(e){this.expirationTime=e}},{key:"getUserProfile",value:function(e){var t=this,n=e.userIDList;e.fromAccount=this.userController.getMyAccount(),n.length>100&&(aa.warn("ProfileHandler.getUserProfile 获取用户资料人数不能超过100人"),n.length=100);for(var r,o=[],i=[],s=0,a=n.length;s<a;s++)r=n[s],this.userController.isMyFriend(r)&&this._containsAccount(r)?i.push(this._getProfileFromMap(r)):o.push(r);if(0===o.length)return ug(i);e.toAccount=o;var u=e.bFromGetMyProfile||!1,c=[];e.toAccount.forEach((function(e){c.push({toAccount:e,standardSequence:0,customSequence:0})})),e.userItem=c;var l=new Cg;l.setMethod(gm).setText(n.length>5?"userIDList.length=".concat(n.length):"userIDList=".concat(n)).setStart();var p=this.userController.generateConfig(this.TAG,this.Actions.Q,e);return this.userController.request(p).then((function(e){l.setCode(0).setNetworkType(t.userController.getNetworkType()).setEnd(),aa.info("ProfileHandler.getUserProfile ok");var n=t._handleResponse(e).concat(i);return u?(t.userController.onGotMyProfile(),new ig(n[0])):new ig(n)})).catch((function(e){return t.userController.probeNetwork().then((function(t){var n=Hn(t,2),r=n[0],o=n[1];l.setError(e,r,o).setEnd()})),aa.error("ProfileHandler.getUserProfile error:",e),cg(e)}))}},{key:"getMyProfile",value:function(){var e=this.userController.getMyAccount();if(aa.log("ProfileHandler.getMyProfile myAccount="+e),this._fillMap(),this._containsAccount(e)){var t=this._getProfileFromMap(e);return aa.debug("ProfileHandler.getMyProfile from cache, myProfile:"+JSON.stringify(t)),this.userController.onGotMyProfile(),ug(t)}return this.getUserProfile({fromAccount:e,userIDList:[e],bFromGetMyProfile:!0})}},{key:"_handleResponse",value:function(e){for(var t,n,r=Ia.now(),o=e.data.userProfileItem,i=[],s=0,a=o.length;s<a;s++)"@TLS#NOT_FOUND"!==o[s].to&&""!==o[s].to&&(t=o[s].to,n=this._updateMap(t,this._getLatestProfileFromResponse(t,o[s].profileItem)),i.push(n));return aa.log("ProfileHandler._handleResponse cost "+(Ia.now()-r)+" ms"),i}},{key:"_getLatestProfileFromResponse",value:function(e,t){var n={};if(n.userID=e,n.profileCustomField=[],!qa(t))for(var r=0,o=t.length;r<o;r++)if(t[r].tag.indexOf("Tag_Profile_Custom")>-1)n.profileCustomField.push({key:t[r].tag,value:t[r].value});else switch(t[r].tag){case tp.NICK:n.nick=t[r].value;break;case tp.GENDER:n.gender=t[r].value;break;case tp.BIRTHDAY:n.birthday=t[r].value;break;case tp.LOCATION:n.location=t[r].value;break;case tp.SELFSIGNATURE:n.selfSignature=t[r].value;break;case tp.ALLOWTYPE:n.allowType=t[r].value;break;case tp.LANGUAGE:n.language=t[r].value;break;case tp.AVATAR:n.avatar=t[r].value;break;case tp.MESSAGESETTINGS:n.messageSettings=t[r].value;break;case tp.ADMINFORBIDTYPE:n.adminForbidType=t[r].value;break;case tp.LEVEL:n.level=t[r].value;break;case tp.ROLE:n.role=t[r].value;break;default:aa.warn("ProfileHandler._handleResponse unkown tag->",t[r].tag,t[r].value)}return n}},{key:"updateMyProfile",value:function(e){var t=this,n=new Cg;n.setMethod(mm).setText(JSON.stringify(e)).setStart();var r=(new Fy).validate(e);if(!r.valid)return n.setCode(pf).setMessage("ProfileHandler.updateMyProfile info:".concat(r.tips)).setNetworkType(this.userController.getNetworkType()).setEnd(),aa.error("ProfileHandler.updateMyProfile info:".concat(r.tips,"，请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#updateMyProfile")),cg({code:pf,message:Ih});var o=[];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&("profileCustomField"===i?e.profileCustomField.forEach((function(e){o.push({tag:e.key,value:e.value})})):o.push({tag:tp[i.toUpperCase()],value:e[i]}));if(0===o.length)return n.setCode(ff).setMessage(Mh).setNetworkType(this.userController.getNetworkType()).setEnd(),aa.error("ProfileHandler.updateMyProfile info:".concat(Mh,"，请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#updateMyProfile")),cg({code:ff,message:Mh});var s=this.userController.generateConfig(this.TAG,this.Actions.U,{fromAccount:this.userController.getMyAccount(),profileItem:o});return this.userController.request(s).then((function(r){n.setCode(0).setNetworkType(t.userController.getNetworkType()).setEnd(),aa.info("ProfileHandler.updateMyProfile ok");var o=t._updateMap(t.userController.getMyAccount(),e);return t.userController.emitOuterEvent(pn.PROFILE_UPDATED,[o]),ug(o)})).catch((function(e){return t.userController.probeNetwork().then((function(t){var r=Hn(t,2),o=r[0],i=r[1];n.setError(e,o,i).setEnd()})),aa.error("ProfileHandler.updateMyProfile error:",e),cg(e)}))}},{key:"onProfileModified",value:function(e){var t=e.data;if(!qa(t)){var n,r,o=t.length;aa.info("ProfileHandler.onProfileModified length="+o);for(var i=[],s=0;s<o;s++)n=t[s].userID,r=this._updateMap(n,this._getLatestProfileFromResponse(n,t[s].profileList)),i.push(r);this.userController.emitInnerEvent(qd,i),this.userController.emitOuterEvent(pn.PROFILE_UPDATED,i)}}},{key:"_fillMap",value:function(){if(0===this.accountProfileMap.size){for(var e=this._getCachedProfiles(),t=Date.now(),n=0,r=e.length;n<r;n++)t-e[n].lastUpdatedTime<this.expirationTime&&this.accountProfileMap.set(e[n].userID,e[n]);aa.log("ProfileHandler._fillMap from cache, map.size="+this.accountProfileMap.size)}}},{key:"_updateMap",value:function(e,t){var n,r=Date.now();return this._containsAccount(e)?(n=this._getProfileFromMap(e),t.profileCustomField&&La(n.profileCustomField,t.profileCustomField),Ma(n,t,["profileCustomField"]),n.lastUpdatedTime=r):(n=new Fy(t),(this.userController.isMyFriend(e)||e===this.userController.getMyAccount())&&(n.lastUpdatedTime=r,this.accountProfileMap.set(e,n))),this._flushMap(e===this.userController.getMyAccount()),n}},{key:"_flushMap",value:function(e){var t=Vn(this.accountProfileMap.values()),n=this.userController.tim.storage;aa.debug("ProfileHandler._flushMap length=".concat(t.length," flushAtOnce=").concat(e)),n.setItem(this.TAG,t,e)}},{key:"_containsAccount",value:function(e){return this.accountProfileMap.has(e)}},{key:"_getProfileFromMap",value:function(e){return this.accountProfileMap.get(e)}},{key:"_getCachedProfiles",value:function(){var e=this.userController.tim.storage.getItem(this.TAG);return qa(e)?[]:e}},{key:"onConversationsProfileUpdated",value:function(e){for(var t,n,r,o=[],i=0,s=e.length;i<s;i++)n=(t=e[i]).userID,this.userController.isMyFriend(n)&&(this._containsAccount(n)?(r=this._getProfileFromMap(n),Ma(r,t)>0&&o.push(n)):o.push(t.userID));0!==o.length&&(aa.info("ProfileHandler.onConversationsProfileUpdated toAccount:",o),this.getUserProfile({userIDList:o}))}},{key:"reset",value:function(){this._flushMap(!0),this.accountProfileMap.clear()}}]),e}();be({target:"String",proto:!0},{repeat:wr});var By=function(){function e(t){wn(this,e),this.options=t?t.options:{enablePointer:!0},this.pointsList={},this.reportText={},this.maxNameLen=0,this.gapChar="-",this.log=console.log,this.currentTask=""}return bn(e,[{key:"newTask",value:function(e){!1!==this.options.enablePointer&&(e||(e=["task",this._timeFormat()].join("-")),this.pointsList[e]=[],this.currentTask=e,console.log("Pointer new Task : ".concat(this.currentTask)))}},{key:"deleteTask",value:function(e){!1!==this.options.enablePointer&&(e||(e=this.currentTask),this.pointsList[e].length=0,delete this.pointsList[e])}},{key:"dot",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1?arguments[1]:void 0;if(!1!==this.options.enablePointer){t=t||this.currentTask;var n=+new Date;this.maxNameLen=this.maxNameLen<e.length?e.length:this.maxNameLen,this.flen=this.maxNameLen+10,this.pointsList[t].push({pointerName:e,time:n})}}},{key:"_analisys",value:function(e){if(!1!==this.options.enablePointer){e=e||this.currentTask;for(var t=this.pointsList[e],n=t.length,r=[],o=[],i=0;i<n;i++)0!==i&&(o=this._analisysTowPoints(t[i-1],t[i]),r.push(o.join("")));return o=this._analisysTowPoints(t[0],t[n-1],!0),r.push(o.join("")),r.join("")}}},{key:"_analisysTowPoints",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!1!==this.options.enablePointer){var r=this.flen,o=t.time-e.time,i=o.toString(),s=e.pointerName+this.gapChar.repeat(r-e.pointerName.length),a=t.pointerName+this.gapChar.repeat(r-t.pointerName.length),u=this.gapChar.repeat(4-i.length)+i,c=n?["%c",s,a,u,"ms\n%c"]:[s,a,u,"ms\n"];return c}}},{key:"report",value:function(e){if(!1!==this.options.enablePointer){e=e||this.currentTask;var t=this._analisys(e);this.pointsList=[];var n=this._timeFormat(),r="Pointer[".concat(e,"(").concat(n,")]"),o=4*this.maxNameLen,i=(o-r.length)/2;console.log(["-".repeat(i),r,"-".repeat(i)].join("")),console.log("%c"+t,"color:#66a","color:red","color:#66a"),console.log("-".repeat(o))}}},{key:"_timeFormat",value:function(){var e=new Date,t=this.zeroFix(e.getMonth()+1,2),n=this.zeroFix(e.getDate(),2);return"".concat(t,"-").concat(n," ").concat(e.getHours(),":").concat(e.getSeconds(),":").concat(e.getMinutes(),"~").concat(e.getMilliseconds())}},{key:"zeroFix",value:function(e,t){return("000000000"+e).slice(-t)}},{key:"reportAll",value:function(){if(!1!==this.options.enablePointer)for(var e in this.pointsList)Object.prototype.hasOwnProperty.call(this.pointsList,e)&&this.eport(e)}}]),e}(),Hy=function e(t,n){wn(this,e),this.userID=t;var r={};if(r.userID=t,!qa(n))for(var o=0,i=n.length;o<i;o++)switch(n[o].tag){case tp.NICK:r.nick=n[o].value;break;case tp.GENDER:r.gender=n[o].value;break;case tp.BIRTHDAY:r.birthday=n[o].value;break;case tp.LOCATION:r.location=n[o].value;break;case tp.SELFSIGNATURE:r.selfSignature=n[o].value;break;case tp.ALLOWTYPE:r.allowType=n[o].value;break;case tp.LANGUAGE:r.language=n[o].value;break;case tp.AVATAR:r.avatar=n[o].value;break;case tp.MESSAGESETTINGS:r.messageSettings=n[o].value;break;case tp.ADMINFORBIDTYPE:r.adminForbidType=n[o].value;break;case tp.LEVEL:r.level=n[o].value;break;case tp.ROLE:r.role=n[o].value;break;default:aa.debug("snsProfileItem unkown tag->",n[o].tag)}this.profile=new Fy(r)},Vy=function(){function e(t){wn(this,e),this.userController=t,this.TAG="friend",this.Actions={G:"get",D:"delete"},this.friends=new Map,this.pointer=new By}return bn(e,[{key:"isMyFriend",value:function(e){var t=this.friends.has(e);return t||aa.debug("FriendHandler.isMyFriend "+e+" is not my friend"),t}},{key:"_transformFriendList",value:function(e){if(!qa(e)&&!qa(e.infoItem)){aa.info("FriendHandler._transformFriendList friendNum="+e.friendNum);for(var t,n,r=e.infoItem,o=0,i=r.length;o<i;o++)n=r[o].infoAccount,t=new Hy(n,r[o].snsProfileItem),this.friends.set(n,t)}}},{key:"_friends2map",value:function(e){var t=new Map;for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.set(n,e[n]);return t}},{key:"getFriendList",value:function(){var e=this,t={};t.fromAccount=this.userController.getMyAccount(),aa.info("FriendHandler.getFriendList myAccount="+t.fromAccount);var n=this.userController.generateConfig(this.TAG,this.Actions.G,t);return this.userController.request(n).then((function(t){aa.info("FriendHandler.getFriendList ok"),e._transformFriendList(t.data);var n=Vn(e.friends.values());return ug(n)})).catch((function(e){return aa.error("FriendHandler.getFriendList error:",JSON.stringify(e)),cg(e)}))}},{key:"deleteFriend",value:function(e){if(!Array.isArray(e.toAccount))return aa.error("FriendHandler.deleteFriend options.toAccount 必需是数组"),cg({code:lf,message:Ch});e.toAccount.length>1e3&&(aa.warn("FriendHandler.deleteFriend 删除好友人数不能超过1000人"),e.toAccount.length=1e3);var t=this.userController.generateConfig(this.TAG,this.Actions.D,e);return this.userController.request(t).then((function(e){return aa.info("FriendHandler.deleteFriend ok"),ug()})).catch((function(e){return aa.error("FriendHandler.deleteFriend error:",e),cg(e)}))}}]),e}(),Ky=function e(t){wn(this,e),qa||(this.userID=t.userID||"",this.timeStamp=t.timeStamp||0)},$y=function(){function e(t){wn(this,e),this.userController=t,this.TAG="blacklist",this.Actions={G:"get",C:"create",D:"delete"},this.blacklistMap=new Map,this.startIndex=0,this.maxLimited=100,this.curruentSequence=0}return bn(e,[{key:"getBlacklist",value:function(){var e=this,t={};t.fromAccount=this.userController.getMyAccount(),t.maxLimited=this.maxLimited,t.startIndex=0,t.lastSequence=this.curruentSequence;var n=new Cg;n.setMethod(vm).setStart();var r=this.userController.generateConfig(this.TAG,this.Actions.G,t);return this.userController.request(r).then((function(t){var r=qa(t.data.blackListItem)?0:t.data.blackListItem.length;return n.setCode(0).setNetworkType(e.userController.getNetworkType()).setText(r).setEnd(),aa.info("BlacklistHandler.getBlacklist ok"),e.curruentSequence=t.data.curruentSequence,e._handleResponse(t.data.blackListItem,!0),e._onBlacklistUpdated()})).catch((function(t){return e.userController.probeNetwork().then((function(e){var r=Hn(e,2),o=r[0],i=r[1];n.setError(t,o,i).setEnd()})),aa.error("BlacklistHandler.getBlacklist error:",t),cg(t)}))}},{key:"addBlacklist",value:function(e){var t=this,n=new Cg;if(n.setMethod(ym).setStart(),!ha(e.userIDList))return n.setCode(hf).setMessage("BlacklistHandler.addBlacklist options.userIDList 必需是数组").setNetworkType(this.userController.getNetworkType()).setEnd(),aa.error("BlacklistHandler.addBlacklist options.userIDList 必需是数组"),cg({code:hf,message:Sh});var r=this.userController.tim.loginInfo.identifier;if(1===e.userIDList.length&&e.userIDList[0]===r)return n.setCode(gf).setMessage(Eh).setNetworkType(this.userController.getNetworkType()).setEnd(),aa.error("BlacklistHandler.addBlacklist 不能把自己拉黑"),cg({code:gf,message:Eh});e.userIDList.includes(r)&&(e.userIDList=e.userIDList.filter((function(e){return e!==r})),aa.warn("BlacklistHandler.addBlacklist 不能把自己拉黑，已过滤")),e.fromAccount=this.userController.getMyAccount(),e.toAccount=e.userIDList;var o=this.userController.generateConfig(this.TAG,this.Actions.C,e);return this.userController.request(o).then((function(r){return n.setCode(0).setNetworkType(t.userController.getNetworkType()).setText(e.userIDList.length>5?"userIDList.length=".concat(e.userIDList.length):"userIDList=".concat(e.userIDList)).setEnd(),aa.info("BlacklistHandler.addBlacklist ok"),t._handleResponse(r.data.resultItem,!0),t._onBlacklistUpdated()})).catch((function(e){return t.userController.probeNetwork().then((function(t){var r=Hn(t,2),o=r[0],i=r[1];n.setError(e,o,i).setEnd()})),aa.error("BlacklistHandler.addBlacklist error:",e),cg(e)}))}},{key:"_handleResponse",value:function(e,t){if(!qa(e))for(var n,r,o,i=0,s=e.length;i<s;i++)r=e[i].to,o=e[i].resultCode,(da(o)||0===o)&&(t?((n=this.blacklistMap.has(r)?this.blacklistMap.get(r):new Ky).userID=r,!qa(e[i].addBlackTimeStamp)&&(n.timeStamp=e[i].addBlackTimeStamp),this.blacklistMap.set(r,n)):this.blacklistMap.has(r)&&(n=this.blacklistMap.get(r),this.blacklistMap.delete(r)));aa.log("BlacklistHandler._handleResponse total="+this.blacklistMap.size+" bAdd="+t)}},{key:"deleteBlacklist",value:function(e){var t=this,n=new Cg;if(n.setMethod(_m).setStart(),!ha(e.userIDList))return n.setCode(df).setMessage("BlacklistHandler.deleteBlacklist options.userIDList 必需是数组").setNetworkType(this.userController.getNetworkType()).setEnd(),aa.error("BlacklistHandler.deleteBlacklist options.userIDList 必需是数组"),cg({code:df,message:Th});e.fromAccount=this.userController.getMyAccount(),e.toAccount=e.userIDList;var r=this.userController.generateConfig(this.TAG,this.Actions.D,e);return this.userController.request(r).then((function(r){return n.setCode(0).setNetworkType(t.userController.getNetworkType()).setText(e.userIDList.length>5?"userIDList.length=".concat(e.userIDList.length):"userIDList=".concat(e.userIDList)).setEnd(),aa.info("BlacklistHandler.deleteBlacklist ok"),t._handleResponse(r.data.resultItem,!1),t._onBlacklistUpdated()})).catch((function(e){return t.userController.probeNetwork().then((function(t){var r=Hn(t,2),o=r[0],i=r[1];n.setError(e,o,i).setEnd()})),aa.error("BlacklistHandler.deleteBlacklist error:",e),cg(e)}))}},{key:"_onBlacklistUpdated",value:function(){var e=Vn(this.blacklistMap.keys());return this.userController.emitOuterEvent(pn.BLACKLIST_UPDATED,e),ug(e)}},{key:"handleBlackListDelAccount",value:function(e){for(var t,n=[],r=0,o=e.length;r<o;r++)t=e[r],this.blacklistMap.has(t)&&(this.blacklistMap.delete(t),n.push(t));n.length>0&&(aa.log("BlacklistHandler.handleBlackListDelAccount delCount="+n.length+" : "+n.join(",")),this.userController.emitOuterEvent(pn.BLACKLIST_UPDATED,Vn(this.blacklistMap.keys())))}},{key:"handleBlackListAddAccount",value:function(e){for(var t,n=[],r=0,o=e.length;r<o;r++)t=e[r],this.blacklistMap.has(t)||(this.blacklistMap.set(t,new Ky({userID:t})),n.push(t));n.length>0&&(aa.log("BlacklistHandler.handleBlackListAddAccount addCount="+n.length+" : "+n.join(",")),this.userController.emitOuterEvent(pn.BLACKLIST_UPDATED,Vn(this.blacklistMap.keys())))}},{key:"reset",value:function(){this.blacklistMap.clear(),this.startIndex=0,this.maxLimited=100,this.curruentSequence=0}}]),e}(),Yy=function(){function e(t){wn(this,e),this.userController=t,this.TAG="applyC2C",this.Actions={C:"create",G:"get",D:"delete",U:"update"}}return bn(e,[{key:"applyAddFriend",value:function(e){var t=this,n=this.userController.generateConfig(this.TAG,this.Actions.C,e),r=this.userController.request(n);return r.then((function(e){t.userController.isActionSuccessful("applyAddFriend",t.Actions.C,e)})).catch((function(e){})),r}},{key:"getPendency",value:function(e){var t=this,n=this.userController.generateConfig(this.TAG,this.Actions.G,e),r=this.userController.request(n);return r.then((function(e){t.userController.isActionSuccessful("getPendency",t.Actions.G,e)})).catch((function(e){})),r}},{key:"deletePendency",value:function(e){var t=this,n=this.userController.generateConfig(this.TAG,this.Actions.D,e),r=this.userController.request(n);return r.then((function(e){t.userController.isActionSuccessful("deletePendency",t.Actions.D,e)})).catch((function(e){})),r}},{key:"replyPendency",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=this.userController.generateConfig(this.TAG,this.Actions.U,t),r=this.userController.request(n);return r.then((function(t){e.userController.isActionSuccessful("replyPendency",e.Actions.U,t)})).catch((function(e){})),r}}]),e}(),zy=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;return wn(this,n),(r=t.call(this,e)).profileHandler=new jy(Fn(r)),r.friendHandler=new Vy(Fn(r)),r.blacklistHandler=new $y(Fn(r)),r.applyC2CHandler=new Yy(Fn(r)),r._initializeListener(),r}return bn(n,[{key:"_initializeListener",value:function(e){var t=this.tim.innerEmitter;t.on(td,this.onContextUpdated,this),t.on(Td,this.onProfileModified,this),t.on(Sd,this.onNewFriendMessages,this),t.on(xd,this.onConversationsProfileUpdated,this)}},{key:"onContextUpdated",value:function(e){var t=this.tim.context;0!=!!t.a2Key&&0!=!!t.tinyID&&(this.profileHandler.getMyProfile(),this.friendHandler.getFriendList(),this.blacklistHandler.getBlacklist())}},{key:"onGotMyProfile",value:function(){this.triggerReady()}},{key:"onProfileModified",value:function(e){this.profileHandler.onProfileModified(e)}},{key:"onNewFriendMessages",value:function(e){aa.debug("onNewFriendMessages",JSON.stringify(e.data)),qa(e.data.blackListDelAccount)||this.blacklistHandler.handleBlackListDelAccount(e.data.blackListDelAccount),qa(e.data.blackListAddAccount)||this.blacklistHandler.handleBlackListAddAccount(e.data.blackListAddAccount)}},{key:"onConversationsProfileUpdated",value:function(e){this.profileHandler.onConversationsProfileUpdated(e.data)}},{key:"getMyAccount",value:function(){return this.tim.context.identifier}},{key:"isMyFriend",value:function(e){return this.friendHandler.isMyFriend(e)}},{key:"generateConfig",value:function(e,t,n){return{name:e,action:t,param:n}}},{key:"getMyProfile",value:function(){return this.profileHandler.getMyProfile()}},{key:"getUserProfile",value:function(e){return this.profileHandler.getUserProfile(e)}},{key:"updateMyProfile",value:function(e){return this.profileHandler.updateMyProfile(e)}},{key:"getFriendList",value:function(){return this.friendHandler.getFriendList()}},{key:"deleteFriend",value:function(e){return this.friendHandler.deleteFriend(e)}},{key:"getBlacklist",value:function(){return this.blacklistHandler.getBlacklist()}},{key:"addBlacklist",value:function(e){return this.blacklistHandler.addBlacklist(e)}},{key:"deleteBlacklist",value:function(e){return this.blacklistHandler.deleteBlacklist(e)}},{key:"applyAddFriend",value:function(e){return this.applyC2CHandler.applyAddFriend(e)}},{key:"getPendency",value:function(e){return this.applyC2CHandler.getPendency(e)}},{key:"deletePendency",value:function(e){return this.applyC2CHandler.deletePendency(e)}},{key:"replyPendency",value:function(e){return this.applyC2CHandler.replyPendency(e)}},{key:"reset",value:function(){aa.info("UserController.reset"),this.resetReady(),this.profileHandler.reset(),this.blacklistHandler.reset(),this.checkTimes=0}}]),n}(ng),Wy=[],Xy=Wy.sort,Jy=i((function(){Wy.sort(void 0)})),Qy=i((function(){Wy.sort(null)})),Zy=$e("sort");be({target:"Array",proto:!0,forced:Jy||!Qy||!Zy},{sort:function(e){return void 0===e?Xy.call(Le(this)):Xy.call(Le(this),Re(e))}});var e_=["groupID","name","avatar","type","introduction","notification","ownerID","selfInfo","createTime","infoSequence","lastInfoTime","lastMessage","nextMessageSeq","memberNum","maxMemberNum","memberList","joinOption","groupCustomField","muteAllMembers"],t_=function(){function e(t){wn(this,e),this.groupID="",this.name="",this.avatar="",this.type="",this.introduction="",this.notification="",this.ownerID="",this.createTime="",this.infoSequence="",this.lastInfoTime="",this.selfInfo={messageRemindType:"",joinTime:"",nameCard:"",role:""},this.lastMessage={lastTime:"",lastSequence:"",fromAccount:"",messageForShow:""},this.nextMessageSeq="",this.memberNum="",this.maxMemberNum="",this.joinOption="",this.groupCustomField=[],this.muteAllMembers=void 0,this._initGroup(t)}return bn(e,[{key:"_initGroup",value:function(e){for(var t in e)e_.indexOf(t)<0||("selfInfo"!==t?this[t]=e[t]:this.updateSelfInfo(e[t]))}},{key:"updateGroup",value:function(e){e.lastMsgTime&&(this.lastMessage.lastTime=e.lastMsgTime),da(e.muteAllMembers)||("On"===e.muteAllMembers?e.muteAllMembers=!0:e.muteAllMembers=!1),e.groupCustomField&&La(this.groupCustomField,e.groupCustomField),Ma(this,e,["members","errorCode","lastMsgTime","groupCustomField"])}},{key:"updateSelfInfo",value:function(e){var t=e.nameCard,n=e.joinTime,r=e.role,o=e.messageRemindType;Ma(this.selfInfo,{nameCard:t,joinTime:n,role:r,messageRemindType:o},[],["",null,void 0,0,NaN])}},{key:"setSelfNameCard",value:function(e){this.selfInfo.nameCard=e}}]),e}(),n_=function(e,t){if(da(t))return"";switch(e){case fn.MSG_TEXT:return t.text;case fn.MSG_IMAGE:return"[图片]";case fn.MSG_GEO:return"[位置]";case fn.MSG_AUDIO:return"[语音]";case fn.MSG_VIDEO:return"[视频]";case fn.MSG_FILE:return"[文件]";case fn.MSG_CUSTOM:return"[自定义消息]";case fn.MSG_GRP_TIP:return"[群提示消息]";case fn.MSG_GRP_SYS_NOTICE:return"[群系统通知]";case fn.MSG_FACE:return"[动画表情]";default:return""}},r_=function(){function e(t){var n;wn(this,e),this.conversationID=t.conversationID||"",this.unreadCount=t.unreadCount||0,this.type=t.type||"",this.lastMessage=(n=t.lastMessage,da(n)?{lastTime:0,lastSequence:0,fromAccount:0,messageForShow:"",payload:null,type:"",isRevoked:!1}:n instanceof Bh?{lastTime:n.time||0,lastSequence:n.sequence||0,fromAccount:n.from||"",messageForShow:n_(n.type,n.payload),payload:n.payload||null,type:n.type||null,isRevoked:!1}:Ln({},n,{isRevoked:!1,messageForShow:n_(n.type,n.payload)})),t.lastMsgTime&&(this.lastMessage.lastTime=t.lastMsgTime),this._isInfoCompleted=!1,this._initProfile(t)}return bn(e,[{key:"_initProfile",value:function(e){var t=this;Object.keys(e).forEach((function(n){switch(n){case"userProfile":t.userProfile=e.userProfile;break;case"groupProfile":t.groupProfile=e.groupProfile}})),da(this.userProfile)&&this.type===fn.CONV_C2C?this.userProfile=new Fy({userID:e.conversationID.replace("C2C","")}):da(this.groupProfile)&&this.type===fn.CONV_GROUP&&(this.groupProfile=new t_({groupID:e.conversationID.replace("GROUP","")}))}},{key:"updateUnreadCount",value:function(e,t){da(e)||(this.subType===fn.GRP_CHATROOM||Pa(this.subType)?this.unreadCount=0:t&&this.type===fn.CONV_GROUP?this.unreadCount=e:this.unreadCount=this.unreadCount+e)}},{key:"reduceUnreadCount",value:function(){this.unreadCount>=1&&(this.unreadCount-=1)}},{key:"isLastMessageRevoked",value:function(e){var t=e.sequence,n=e.time;return this.type===fn.CONV_C2C&&t===this.lastMessage.lastSequence&&n===this.lastMessage.lastTime||this.type===fn.CONV_GROUP&&t===this.lastMessage.lastSequence}},{key:"setLastMessageRevoked",value:function(e){this.lastMessage.isRevoked=e}},{key:"toAccount",get:function(){return this.conversationID.replace("C2C","").replace("GROUP","")}},{key:"subType",get:function(){return this.groupProfile?this.groupProfile.type:""}}]),e}(),o_=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;return wn(this,n),(r=t.call(this,e)).pagingStatus=Nu.NOT_START,r.pagingTimeStamp=0,r.conversationMap=new Map,r.tempGroupList=[],r._initListeners(),r}return bn(n,[{key:"hasLocalConversationMap",value:function(){return this.conversationMap.size>0}},{key:"createLocalConversation",value:function(e){return this.conversationMap.has(e)?this.conversationMap.get(e):new r_({conversationID:e,type:e.slice(0,3)===fn.CONV_C2C?fn.CONV_C2C:fn.CONV_GROUP})}},{key:"hasLocalConversation",value:function(e){return this.conversationMap.has(e)}},{key:"getConversationList",value:function(){var e=this;aa.log("ConversationController.getConversationList."),this.pagingStatus===Nu.REJECTED&&(aa.log("ConversationController.getConversationList. continue to sync conversationList"),this._syncConversationList());var t=new Cg;return t.setMethod(xg).setStart(),this.request({name:"conversation",action:"query"}).then((function(n){var r=n.data.conversations,o=void 0===r?[]:r,i=e._getConversationOptions(o);return e._updateLocalConversationList(i,!0),e._setStorageConversationList(),t.setCode(0).setText(o.length).setNetworkType(e.getNetworkType()).setEnd(),aa.log("ConversationController.getConversationList ok."),ug({conversationList:e.getLocalConversationList()})})).catch((function(n){return e.probeNetwork().then((function(e){var r=Hn(e,2),o=r[0],i=r[1];t.setError(n,o,i).setEnd()})),aa.error("ConversationController.getConversationList error:",n),cg(n)}))}},{key:"_syncConversationList",value:function(){var e=this,t=new Cg;return t.setMethod(jg).setStart(),this.pagingStatus===Nu.NOT_START&&this.conversationMap.clear(),this._autoPagingSyncConversationList().then((function(n){return e.pagingStatus=Nu.RESOLVED,e._setStorageConversationList(),t.setCode(0).setText("".concat(e.conversationMap.size)).setNetworkType(e.getNetworkType()).setEnd(),n})).catch((function(n){return e.pagingStatus=Nu.REJECTED,t.setText(e.pagingTimeStamp),e.probeNetwork().then((function(e){var r=Hn(e,2),o=r[0],i=r[1];t.setError(n,o,i).setEnd()})),cg(n)}))}},{key:"_autoPagingSyncConversationList",value:function(){var e=this;return this.pagingStatus=Nu.PENDING,this.request({name:"conversation",action:"pagingQuery",param:{fromAccount:this.tim.context.identifier,timeStamp:this.pagingTimeStamp,orderType:1}}).then((function(t){var n=t.data,r=n.completeFlag,o=n.conversations,i=void 0===o?[]:o,s=n.timeStamp;if(aa.log("ConversationController._autoPagingSyncConversationList completeFlag=".concat(r," nums=").concat(i.length)),i.length>0){var a=e._getConversationOptions(i);e._updateLocalConversationList(a,!0)}return e._isReady?e._emitConversationUpdate():e.triggerReady(),e.pagingTimeStamp=s,1!==r?e._autoPagingSyncConversationList():ug()}))}},{key:"getConversationProfile",value:function(e){var t=this,n=this.conversationMap.has(e)?this.conversationMap.get(e):this.createLocalConversation(e);if(n._isInfoCompleted||n.type===fn.CONV_SYSTEM)return ug({conversation:n});var r=new Cg;return r.setMethod(Ug).setStart(),aa.log("ConversationController.getConversationProfile. conversationID:",e),this._updateUserOrGroupProfileCompletely(n).then((function(n){return r.setCode(0).setNetworkType(t.getNetworkType()).setText("conversationID=".concat(e," unreadCount=").concat(n.data.conversation.unreadCount)).setEnd(),aa.log("ConversationController.getConversationProfile ok. conversationID:",e),n})).catch((function(n){return t.probeNetwork().then((function(t){var o=Hn(t,2),i=o[0],s=o[1];r.setError(n,i,s).setText("conversationID=".concat(e)).setEnd()})),aa.error("ConversationController.getConversationProfile error:",n),cg(n)}))}},{key:"deleteConversation",value:function(e){var t=this,n={};if(!this.conversationMap.has(e)){var r=new _p({code:$p,message:rh});return cg(r)}switch(this.conversationMap.get(e).type){case fn.CONV_C2C:n.type=1,n.toAccount=e.slice(3);break;case fn.CONV_GROUP:n.type=2,n.toGroupID=e.slice(5);break;case fn.CONV_SYSTEM:return this.tim.groupController.deleteGroupSystemNotice({messageList:this.tim.messageController.getLocalMessageList(e)}),this.deleteLocalConversation(e),ug({conversationID:e});default:var o=new _p({code:zp,message:ih});return cg(o)}var i=new Cg;return i.setMethod(qg).setText("conversationID=".concat(e)).setStart(),aa.log("ConversationController.deleteConversation. conversationID:",e),this.tim.setMessageRead({conversationID:e}).then((function(){return t.request({name:"conversation",action:"delete",param:n})})).then((function(){return i.setCode(0).setNetworkType(t.getNetworkType()).setEnd(),aa.log("ConversationController.deleteConversation ok. conversationID:",e),t.deleteLocalConversation(e),ug({conversationID:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Hn(t,2),r=n[0],o=n[1];i.setError(e,r,o).setEnd()})),aa.error("ConversationController.deleteConversation error:",e),cg(e)}))}},{key:"getLocalConversationList",value:function(){return Vn(this.conversationMap.values())}},{key:"getLocalConversation",value:function(e){return this.conversationMap.get(e)}},{key:"_initLocalConversationList",value:function(){var e=new Cg;e.setMethod(Fg).setStart(),aa.time(vg),aa.log("ConversationController._initLocalConversationList init");var t=this._getStorageConversationList();if(t){for(var n=t.length,r=0;r<n;r++)this.conversationMap.set(t[r].conversationID,new r_(t[r]));this._emitConversationUpdate(!0,!1),e.setCode(0).setNetworkType(this.getNetworkType()).setText(n).setEnd()}else e.setCode(0).setNetworkType(this.getNetworkType()).setText(0).setEnd();this._syncConversationList()}},{key:"_getStorageConversationList",value:function(){return this.tim.storage.getItem("conversationMap")}},{key:"_setStorageConversationList",value:function(){var e=this.getLocalConversationList().slice(0,20).map((function(e){return{conversationID:e.conversationID,type:e.type,subType:e.subType,lastMessage:e.lastMessage,groupProfile:e.groupProfile,userProfile:e.userProfile}}));this.tim.storage.setItem("conversationMap",e)}},{key:"_initListeners",value:function(){var e=this;this.tim.innerEmitter.once(td,this._initLocalConversationList,this),this.tim.innerEmitter.on(id,this._onSendOrReceiveMessage,this),this.tim.innerEmitter.on(sd,this._handleSyncMessages,this),this.tim.innerEmitter.on(ad,this._handleSyncMessages,this),this.tim.innerEmitter.on(ud,this._onSendOrReceiveMessage,this),this.tim.innerEmitter.on(cd,this._onSendOrReceiveMessage,this),this.tim.innerEmitter.on(ld,this._onSendOrReceiveMessage,this),this.tim.innerEmitter.on(bd,this._onGroupListUpdated,this),this.tim.innerEmitter.on(qd,this._updateConversationUserProfile,this),this.tim.innerEmitter.on(pd,this._onMessageRevoked,this),this.ready((function(){e.tempGroupList.length>0&&(e._updateConversationGroupProfile(e.tempGroupList),e.tempGroupList.length=0)}))}},{key:"_onGroupListUpdated",value:function(e){this._updateConversationGroupProfile(e.data)}},{key:"_updateConversationGroupProfile",value:function(e){var t=this;ha(e)&&0===e.length||(this.hasLocalConversationMap()?(e.forEach((function(e){var n="GROUP".concat(e.groupID);if(t.conversationMap.has(n)){var r=t.conversationMap.get(n);r.groupProfile=e,r.lastMessage.lastSequence<e.nextMessageSeq&&(r.lastMessage.lastSequence=e.nextMessageSeq-1),r.subType||(r.subType=e.type)}})),this._emitConversationUpdate(!0,!1)):this.tempGroupList=e)}},{key:"_updateConversationUserProfile",value:function(e){var t=this;e.data.forEach((function(e){var n="C2C".concat(e.userID);t.conversationMap.has(n)&&(t.conversationMap.get(n).userProfile=e)})),this._emitConversationUpdate(!0,!1)}},{key:"_onMessageRevoked",value:function(e){var t=this,n=e.data;if(0!==n.length){var r=null,o=!1;n.forEach((function(e){(r=t.conversationMap.get(e.conversationID))&&r.isLastMessageRevoked(e)&&(o=!0,r.setLastMessageRevoked(!0))})),o&&this._emitConversationUpdate(!0,!1)}}},{key:"_handleSyncMessages",value:function(e){this._onSendOrReceiveMessage(e,!0)}},{key:"_onSendOrReceiveMessage",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=e.data.eventDataList;this._isReady?0!==r.length&&(this._updateLocalConversationList(r,!1,n),this._setStorageConversationList(),this._emitConversationUpdate()):this.ready((function(){t._onSendOrReceiveMessage(e,n)}))}},{key:"_updateLocalConversationList",value:function(e,t,n){var r;r=this._updateTempConversations(e,t,n),this.conversationMap=new Map(this._sortConversations([].concat(Vn(r.conversations),Vn(this.conversationMap)))),t||this._updateUserOrGroupProfile(r.newerConversations)}},{key:"_updateTempConversations",value:function(e,t,n){for(var r=[],o=[],i=0,s=e.length;i<s;i++){var a=new r_(e[i]),u=this.conversationMap.get(a.conversationID);if(this.conversationMap.has(a.conversationID)){var c=["unreadCount","allowType","adminForbidType","payload"];n&&c.push("lastMessage"),Ma(u,a,c,[null,void 0,"",0,NaN]),u.updateUnreadCount(a.unreadCount,t),n||(u.lastMessage.payload=e[i].lastMessage.payload),this.conversationMap.delete(u.conversationID),r.push([u.conversationID,u])}else{if(a.type===fn.CONV_GROUP){var l=a.groupProfile.groupID,p=this.tim.groupController.getLocalGroupProfile(l);p&&(a.groupProfile=p,a.updateUnreadCount(0))}o.push(a),r.push([a.conversationID,a])}}return{conversations:r,newerConversations:o}}},{key:"_sortConversations",value:function(e){return e.sort((function(e,t){return t[1].lastMessage.lastTime-e[1].lastMessage.lastTime}))}},{key:"_updateUserOrGroupProfile",value:function(e){var t=this;if(0!==e.length){var n=[],r=[];e.forEach((function(e){if(e.type===fn.CONV_C2C)n.push(e.toAccount);else if(e.type===fn.CONV_GROUP){var o=e.toAccount;t.tim.groupController.hasLocalGroup(o)?e.groupProfile=t.tim.groupController.getLocalGroupProfile(o):r.push(o)}})),n.length>0&&this.tim.getUserProfile({userIDList:n}).then((function(e){var n=e.data;ha(n)?n.forEach((function(e){t.conversationMap.get("C2C".concat(e.userID)).userProfile=e})):t.conversationMap.get("C2C".concat(n.userID)).userProfile=n})),r.length>0&&this.tim.groupController.getGroupProfileAdvance({groupIDList:r,responseFilter:{groupBaseInfoFilter:["Type","Name","FaceUrl"]}}).then((function(e){e.data.successGroupList.forEach((function(e){var n="GROUP".concat(e.groupID);if(t.conversationMap.has(n)){var r=t.conversationMap.get(n);Ma(r.groupProfile,e,[],[null,void 0,"",0,NaN]),!r.subType&&e.type&&(r.subType=e.type)}}))}))}}},{key:"_updateUserOrGroupProfileCompletely",value:function(e){var t=this;return e.type===fn.CONV_C2C?this.tim.getUserProfile({userIDList:[e.toAccount]}).then((function(n){var r=n.data;return 0===r.length?cg(new _p({code:Yp,message:oh})):(e.userProfile=r[0],e._isInfoCompleted=!0,t._unshiftConversation(e),ug({conversation:e}))})):this.tim.getGroupProfile({groupID:e.toAccount}).then((function(n){return e.groupProfile=n.data.group,e._isInfoCompleted=!0,t._unshiftConversation(e),ug({conversation:e})}))}},{key:"_unshiftConversation",value:function(e){e instanceof r_&&!this.conversationMap.has(e.conversationID)&&(this.conversationMap=new Map([[e.conversationID,e]].concat(Vn(this.conversationMap))),this._setStorageConversationList(),this._emitConversationUpdate(!0,!1))}},{key:"deleteLocalConversation",value:function(e){return this.conversationMap.delete(e),this._setStorageConversationList(),this.emitInnerEvent(Ud,e),this._emitConversationUpdate(!0,!1),this.conversationMap.has(e)}},{key:"_getConversationOptions",value:function(e){var t=[],n=e.filter((function(e){var t=e.lastMsg;return fa(t)})).map((function(e){if(1===e.type){var n={userID:e.userID,nick:e.c2CNick,avatar:e.c2CImage};return t.push(n),{conversationID:"C2C".concat(e.userID),type:"C2C",lastMessage:{lastTime:e.time,lastSequence:e.sequence,fromAccount:e.lastC2CMsgFromAccount,messageForShow:e.messageShow,type:e.lastMsg.elements[0]?e.lastMsg.elements[0].type:null,payload:e.lastMsg.elements[0]?e.lastMsg.elements[0].content:null},userProfile:new Fy(n)}}return{conversationID:"GROUP".concat(e.groupID),type:"GROUP",lastMessage:{lastTime:e.time,lastSequence:e.messageReadSeq+e.unreadCount,fromAccount:e.msgGroupFromAccount,messageForShow:e.messageShow,type:e.lastMsg.elements[0]?e.lastMsg.elements[0].type:null,payload:e.lastMsg.elements[0]?e.lastMsg.elements[0].content:null},groupProfile:new t_({groupID:e.groupID,name:e.groupNick,avatar:e.groupImage}),unreadCount:e.unreadCount}}));return t.length>0&&this.emitInnerEvent(xd,t),n}},{key:"_emitConversationUpdate",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=Vn(this.conversationMap.values());t&&this.emitInnerEvent(Gd,n),e&&this.emitOuterEvent(pn.CONVERSATION_LIST_UPDATED,n)}},{key:"_conversationMapTreeShaking",value:function(e){var t=this,n=new Map(Vn(this.conversationMap));e.forEach((function(e){return n.delete(e.conversationID)})),n.has(fn.CONV_SYSTEM)&&n.delete(fn.CONV_SYSTEM);var r=this.tim.groupController.getJoinedAVChatRoom();r&&n.delete("".concat(fn.CONV_GROUP).concat(r.groupID)),Vn(n.keys()).forEach((function(e){return t.conversationMap.delete(e)}))}},{key:"reset",value:function(){this.pagingStatus=Nu.NOT_START,this.pagingTimeStamp=0,this.conversationMap.clear(),this.resetReady(),this.tim.innerEmitter.once(td,this._initLocalConversationList,this)}}]),n}(ng),i_=1..toFixed,s_=Math.floor,a_=function(e,t,n){return 0===t?n:t%2==1?a_(e,t-1,n*e):a_(e*e,t/2,n)},u_=i_&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0))||!i((function(){i_.call({})}));be({target:"Number",proto:!0,forced:u_},{toFixed:function(e){var t,n,r,o,i=function(e){if("number"!=typeof e&&"Number"!=f(e))throw TypeError("Incorrect invocation");return+e}(this),s=se(e),a=[0,0,0,0,0,0],u="",c="0",l=function(e,t){for(var n=-1,r=t;++n<6;)r+=e*a[n],a[n]=r%1e7,r=s_(r/1e7)},p=function(e){for(var t=6,n=0;--t>=0;)n+=a[t],a[t]=s_(n/e),n=n%e*1e7},h=function(){for(var e=6,t="";--e>=0;)if(""!==t||0===e||0!==a[e]){var n=String(a[e]);t=""===t?n:t+wr.call("0",7-n.length)+n}return t};if(s<0||s>20)throw RangeError("Incorrect fraction digits");if(i!=i)return"NaN";if(i<=-1e21||i>=1e21)return String(i);if(i<0&&(u="-",i=-i),i>1e-21)if(n=(t=function(e){for(var t=0,n=e;n>=4096;)t+=12,n/=4096;for(;n>=2;)t+=1,n/=2;return t}(i*a_(2,69,1))-69)<0?i*a_(2,-t,1):i/a_(2,t,1),n*=4503599627370496,(t=52-t)>0){for(l(0,n),r=s;r>=7;)l(1e7,0),r-=7;for(l(a_(10,r,1),0),r=t-1;r>=23;)p(1<<23),r-=23;p(1<<r),l(1,1),p(2),c=h()}else l(0,n),l(1<<-t,0),c=h()+wr.call("0",s);return s>0?u+((o=c.length)<=s?"0."+wr.call("0",s-o)+c:c.slice(0,o-s)+"."+c.slice(o-s)):u+c}});var c_=[].push,l_=Math.min,p_=!i((function(){return!RegExp(4294967295,"y")}));fs("split",2,(function(e,t,n){var r;return r="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(e,n){var r=String(g(this)),o=void 0===n?4294967295:n>>>0;if(0===o)return[];if(void 0===e)return[r];if(!rs(e))return t.call(r,e,o);for(var i,s,a,u=[],c=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),l=0,p=new RegExp(e.source,c+"g");(i=Ji.call(p,r))&&!((s=p.lastIndex)>l&&(u.push(r.slice(l,i.index)),i.length>1&&i.index<r.length&&c_.apply(u,i.slice(1)),a=i[0].length,l=s,u.length>=o));)p.lastIndex===i.index&&p.lastIndex++;return l===r.length?!a&&p.test("")||u.push(""):u.push(r.slice(l)),u.length>o?u.slice(0,o):u}:"0".split(void 0,0).length?function(e,n){return void 0===e&&0===n?[]:t.call(this,e,n)}:t,[function(t,n){var o=g(this),i=null==t?void 0:t[e];return void 0!==i?i.call(t,o,n):r.call(String(o),t,n)},function(e,o){var i=n(r,e,this,o,r!==t);if(i.done)return i.value;var s=k(e),a=String(this),u=wo(s,RegExp),c=s.unicode,l=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.unicode?"u":"")+(p_?"y":"g"),p=new u(p_?s:"^(?:"+s.source+")",l),f=void 0===o?4294967295:o>>>0;if(0===f)return[];if(0===a.length)return null===gs(p,a)?[a]:[];for(var h=0,d=0,g=[];d<a.length;){p.lastIndex=p_?d:0;var m,v=gs(p,p_?a:a.slice(d));if(null===v||(m=l_(ue(p.lastIndex+(p_?0:d)),a.length))===h)d=ds(a,d,c);else{if(g.push(a.slice(h,d)),g.length===f)return g;for(var y=1;y<=v.length-1;y++)if(g.push(v[y]),g.length===f)return g;d=h=m}}return g.push(a.slice(h)),g}]}),!p_);var f_=function(){function e(t){if(wn(this,e),void 0===t)throw new _p({code:wp,message:qf});if(void 0===t.tim)throw new _p({code:wp,message:"".concat(qf,".tim")});this.list=new Map,this.tim=t.tim,this._initializeOptions(t)}return bn(e,[{key:"getLocalOldestMessageByConversationID",value:function(e){if(!e)return null;if(!this.list.has(e))return null;var t=this.list.get(e).values();return t?t.next().value:null}},{key:"_initializeOptions",value:function(e){this.options={};var t={memory:{maxDatasPerKey:100,maxBytesPerData:256,maxKeys:0},cache:{maxDatasPerKey:10,maxBytesPerData:256,maxKeys:0}};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(void 0===e[n]){this.options[n]=t[n];continue}var r=t[n];for(var o in r)if(Object.prototype.hasOwnProperty.call(r,o)){if(void 0===e[n][o]){this.options[n][o]=r[o];continue}this.options[n][o]=e[n][o]}}}},{key:"pushIn",value:function(e){var t=e.conversationID,n=e.ID,r=!0;return this.list.has(t)||this.list.set(t,new Map),this.list.has(t)&&this.list.get(t).has(n)?r=!1:this.list.get(t).set(n,e),r}},{key:"unshift",value:function(e){ha(e)?e.length>0&&this._unshiftMultipleMessages(e):this._unshiftSingleMessage(e)}},{key:"_unshiftSingleMessage",value:function(e){var t=e.conversationID,n=e.ID;if(!this.list.has(t))return this.list.set(t,new Map),void this.list.get(t).set(n,e);var r=Array.from(this.list.get(t));r.unshift([n,e]),this.list.set(t,new Map(r))}},{key:"_unshiftMultipleMessages",value:function(e){for(var t=e.length,n=[],r=e[0].conversationID,o=this.list.has(r)?Array.from(this.list.get(r)):[],i=0;i<t;i++)n.push([e[i].ID,e[i]]);this.list.set(r,new Map(n.concat(o)))}},{key:"remove",value:function(e){var t=e.conversationID,n=e.ID;this.list.has(t)&&this.list.get(t).delete(n)}},{key:"revoke",value:function(e,t,n){if(aa.debug("revoke message",e,t,n),this.list.has(e)){var r,o=Yn(this.list.get(e));try{for(o.s();!(r=o.n()).done;){var i=Hn(r.value,2)[1];if(i.sequence===t&&!i.isRevoked&&(da(n)||i.random===n))return i.isRevoked=!0,i}}catch(a){o.e(a)}finally{o.f()}}return null}},{key:"removeByConversationID",value:function(e){this.list.has(e)&&this.list.delete(e)}},{key:"hasLocalMessageList",value:function(e){return this.list.has(e)}},{key:"getLocalMessageList",value:function(e){return this.hasLocalMessageList(e)?Vn(this.list.get(e).values()):[]}},{key:"hasLocalMessage",value:function(e,t){return!!this.hasLocalMessageList(e)&&this.list.get(e).has(t)}},{key:"getLocalMessage",value:function(e,t){return this.hasLocalMessage(e,t)?this.list.get(e).get(t):null}},{key:"reset",value:function(){this.list.clear()}}]),e}(),h_=function(){function e(t){wn(this,e),this.tim=t}return bn(e,[{key:"setMessageRead",value:function(e){var t=e.conversationID,n=e.messageID,r=this.tim.conversationController.getLocalConversation(t);if(aa.log("ReadReportHandler.setMessageRead conversationID=".concat(t," unreadCount=").concat(r?r.unreadCount:0)),!r||0===r.unreadCount)return ug();var o=n?this.tim.messageController.getLocalMessage(t,n):null;switch(r.type){case fn.CONV_C2C:return this._setC2CMessageRead({conversationID:t,lastMessageTime:o?o.time:r.lastMessage.lastTime});case fn.CONV_GROUP:return this._setGroupMessageRead({conversationID:t,lastMessageSeq:o?o.sequence:r.lastMessage.lastSequence});case fn.CONV_SYSTEM:return r.unreadCount=0,ug();default:return ug()}}},{key:"_setC2CMessageRead",value:function(e){var t=this,n=e.conversationID,r=e.lastMessageTime;aa.log("ReadReportHandler._setC2CMessageRead conversationID=".concat(n," lastMessageTime=").concat(r)),ca(r)||aa.warn("ReadReportHandler._setC2CMessageRead 请勿修改 Conversation.lastMessage.lastTime，否则可能会导致已读上报结果不准确");var o=new Cg;return o.setMethod(Ng).setText("".concat(n,"-").concat(r)).setStart(),this.tim.messageController.request({name:"conversation",action:"setC2CMessageRead",param:{C2CMsgReaded:{cookie:"",C2CMsgReadedItem:[{toAccount:n.replace("C2C",""),lastMessageTime:r}]}}}).then((function(){return o.setCode(0).setNetworkType(t.tim.netMonitor.getNetworkType()).setEnd(),aa.log("ReadReportHandler._setC2CMessageRead ok."),t._updateIsReadAfterReadReport({conversationID:n,lastMessageTime:r}),t._updateUnreadCount(n),new ig})).catch((function(e){return t.tim.netMonitor.probe().then((function(t){var n=Hn(t,2),r=n[0],i=n[1];o.setError(e,r,i).setEnd()})),aa.log("ReadReportHandler._setC2CMessageRead failed. ".concat(Sa(e))),cg(e)}))}},{key:"_setGroupMessageRead",value:function(e){var t=this,n=e.conversationID,r=e.lastMessageSeq;aa.log("ReadReportHandler._setGroupMessageRead conversationID=".concat(n," lastMessageSeq=").concat(r)),ca(r)||aa.warn("ReadReportHandler._setGroupMessageRead 请勿修改 Conversation.lastMessage.lastSequence，否则可能会导致已读上报结果不准确");var o=new Cg;return o.setMethod(Pg).setText("".concat(n,"-").concat(r)).setStart(),this.tim.messageController.request({name:"conversation",action:"setGroupMessageRead",param:{groupID:n.replace("GROUP",""),messageReadSeq:r}}).then((function(){return o.setCode(0).setNetworkType(t.tim.netMonitor.getNetworkType()).setEnd(),aa.log("ReadReportHandler._setGroupMessageRead ok."),t._updateIsReadAfterReadReport({conversationID:n,lastMessageSeq:r}),t._updateUnreadCount(n),new ig})).catch((function(e){return t.tim.netMonitor.probe().then((function(t){var n=Hn(t,2),r=n[0],i=n[1];o.setError(e,r,i).setEnd()})),aa.log("ReadReportHandler._setGroupMessageRead failed. ".concat(Sa(e))),cg(e)}))}},{key:"_updateUnreadCount",value:function(e){var t=this.tim,n=t.conversationController,r=t.messageController,o=n.getLocalConversation(e),i=r.getLocalMessageList(e);o&&(o.unreadCount=i.filter((function(e){return!e.isRead})).length,aa.log("ReadReportHandler._updateUnreadCount conversationID=".concat(o.conversationID," unreadCount=").concat(o.unreadCount)))}},{key:"_updateIsReadAfterReadReport",value:function(e){var t=e.conversationID,n=e.lastMessageSeq,r=e.lastMessageTime,o=this.tim.messageController.getLocalMessageList(t);if(0!==o.length)for(var i,s=o.length-1;s>=0;s--)if(i=o[s],!(r&&i.time>r||n&&i.sequence>n)){if("in"===i.flow&&i.isRead)break;i.setIsRead(!0)}}},{key:"updateIsRead",value:function(e){var t=this.tim,n=t.conversationController,r=t.messageController,o=n.getLocalConversation(e),i=r.getLocalMessageList(e);if(o&&0!==i.length&&!Ga(o.type)){for(var s=[],a=0;a<i.length;a++)"in"!==i[a].flow?"out"!==i[a].flow||i[a].isRead||i[a].setIsRead(!0):s.push(i[a]);var u=0;if(o.type===fn.CONV_C2C){var c=s.slice(-o.unreadCount).filter((function(e){return e.isRevoked})).length;u=s.length-o.unreadCount-c}else u=s.length-o.unreadCount;for(var l=0;l<u&&!s[l].isRead;l++)s[l].setIsRead(!0)}}}]),e}(),d_=Ke.findIndex,g_=!0,m_=Xe("findIndex");"findIndex"in[]&&Array(1).findIndex((function(){g_=!1})),be({target:"Array",proto:!0,forced:g_||!m_},{findIndex:function(e){return d_(this,e,arguments.length>1?arguments[1]:void 0)}}),sr("findIndex");var v_=function(){function e(t){var n=t.tim,r=t.messageController;wn(this,e),this.tim=n,this.messageController=r,this.completedMap=new Map,this._initListener()}return bn(e,[{key:"getMessageList",value:function(e){var t=this,n=e.conversationID,r=e.nextReqMessageID,o=e.count;if(this.tim.groupController.checkJoinedAVChatRoomByID(n.replace("GROUP","")))return aa.log("GetMessageHandler.getMessageList not available in avchatroom. conversationID=".concat(n)),ug({messageList:[],nextReqMessageID:"",isCompleted:!0});(da(o)||o>15)&&(o=15);var i=this._computeLeftCount({conversationID:n,nextReqMessageID:r});return aa.log("GetMessageHandler.getMessageList. conversationID=".concat(n," leftCount=").concat(i," count=").concat(o," nextReqMessageID=").concat(r)),this._needGetHistory({conversationID:n,leftCount:i,count:o})?this.messageController.getHistoryMessages({conversationID:n,count:20}).then((function(){return i=t._computeLeftCount({conversationID:n,nextReqMessageID:r}),new ig(t._computeResult({conversationID:n,nextReqMessageID:r,count:o,leftCount:i}))})):(aa.log("GetMessageHandler.getMessageList. get messagelist from memory"),ug(this._computeResult({conversationID:n,nextReqMessageID:r,count:o,leftCount:i})))}},{key:"setCompleted",value:function(e){aa.log("GetMessageHandler.setCompleted. conversationID=".concat(e)),this.completedMap.set(e,!0)}},{key:"deleteCompletedItem",value:function(e){aa.log("GetMessageHandler.deleteCompletedItem. conversationID=".concat(e)),this.completedMap.delete(e)}},{key:"_initListener",value:function(){var e=this;this.tim.innerEmitter.on(Bd,(function(){e.setCompleted(fn.CONV_SYSTEM)})),this.tim.innerEmitter.on(Fd,(function(t){var n=t.data;e.setCompleted("".concat(fn.CONV_GROUP).concat(n))}))}},{key:"_getMessageListSize",value:function(e){return this.messageController.getLocalMessageList(e).length}},{key:"_needGetHistory",value:function(e){var t=e.conversationID,n=e.leftCount,r=e.count,o=this.tim.conversationController.getLocalConversation(t),i=!!o&&o.type===fn.CONV_SYSTEM,s=!!o&&o.subType===fn.GRP_AVCHATROOM;return!i&&!s&&n<r&&!this.completedMap.has(t)}},{key:"_computeResult",value:function(e){var t=e.conversationID,n=e.nextReqMessageID,r=e.count,o=e.leftCount,i=this._computeMessageList({conversationID:t,nextReqMessageID:n,count:r}),s=this._computeIsCompleted({conversationID:t,leftCount:o,count:r}),a=this._computeNextReqMessageID({messageList:i,isCompleted:s,conversationID:t});return aa.log("GetMessageHandler._computeResult. conversationID=".concat(t," leftCount=").concat(o," count=").concat(r," nextReqMessageID=").concat(a," nums=").concat(i.length," isCompleted=").concat(s)),{messageList:i,nextReqMessageID:a,isCompleted:s}}},{key:"_computeNextReqMessageID",value:function(e){var t=e.messageList,n=e.isCompleted,r=e.conversationID;if(!n)return 0===t.length?"":t[0].ID;var o=this.messageController.getLocalMessageList(r);return 0===o.length?"":o[0].ID}},{key:"_computeMessageList",value:function(e){var t=e.conversationID,n=e.nextReqMessageID,r=e.count,o=this.messageController.getLocalMessageList(t),i=this._computeIndexEnd({nextReqMessageID:n,messageList:o}),s=this._computeIndexStart({indexEnd:i,count:r});return o.slice(s,i)}},{key:"_computeIndexEnd",value:function(e){var t=e.messageList,n=void 0===t?[]:t,r=e.nextReqMessageID;return r?n.findIndex((function(e){return e.ID===r})):n.length}},{key:"_computeIndexStart",value:function(e){var t=e.indexEnd,n=e.count;return t>n?t-n:0}},{key:"_computeLeftCount",value:function(e){var t=e.conversationID,n=e.nextReqMessageID;return n?this.messageController.getLocalMessageList(t).findIndex((function(e){return e.ID===n})):this._getMessageListSize(t)}},{key:"_computeIsCompleted",value:function(e){var t=e.conversationID;return!!(e.leftCount<=e.count&&this.completedMap.has(t))}},{key:"reset",value:function(){aa.log("GetMessageHandler.reset"),this.completedMap.clear()}}]),e}(),y_=function e(t){wn(this,e),this.value=t,this.next=null},__=function(){function e(t){wn(this,e),this.MAX_LENGTH=t,this.pTail=null,this.pNodeToDel=null,this.map=new Map,aa.log("SinglyLinkedList init MAX_LENGTH=".concat(this.MAX_LENGTH))}return bn(e,[{key:"pushIn",value:function(e){var t=new y_(e);if(this.map.size<this.MAX_LENGTH)null===this.pTail?(this.pTail=t,this.pNodeToDel=t):(this.pTail.next=t,this.pTail=t),this.map.set(e,1);else{var n=this.pNodeToDel;this.pNodeToDel=this.pNodeToDel.next,this.map.delete(n.value),n.next=null,n=null,this.pTail.next=t,this.pTail=t,this.map.set(e,1)}}},{key:"has",value:function(e){return this.map.has(e)}},{key:"tail",value:function(){return this.pTail}},{key:"size",value:function(){return this.map.size}},{key:"data",value:function(){return Array.from(this.map.keys())}},{key:"reset",value:function(){for(var e;null!==this.pNodeToDel;)e=this.pNodeToDel,this.pNodeToDel=this.pNodeToDel.next,e.next=null,e=null;this.pTail=null,this.map.clear()}}]),e}(),C_=function(){function e(t){wn(this,e),this.tim=t}return bn(e,[{key:"upload",value:function(e){switch(e.type){case fn.MSG_IMAGE:return this._uploadImage(e);case fn.MSG_FILE:return this._uploadFile(e);case fn.MSG_AUDIO:return this._uploadAudio(e);case fn.MSG_VIDEO:return this._uploadVideo(e);default:return Promise.resolve()}}},{key:"_uploadImage",value:function(e){var t=this.tim,n=t.uploadController,r=t.messageController,o=e.getElements()[0],i=r.getMessageOptionByID(e.messageID);return n.uploadImage({file:i.payload.file,to:i.to,onProgress:function(e){if(o.updatePercent(e),ma(i.onProgress))try{i.onProgress(e)}catch(t){return cg(new _p({code:Op,message:"".concat(Hf)}))}}}).then((function(e){var t,n=e.location,r=e.fileType,i=e.fileSize,s=Oa(n);return o.updateImageFormat(r),o.updateImageInfoArray({size:i,url:s}),t=o._imageMemoryURL,Os?new Promise((function(e,n){wx.getImageInfo({src:t,success:function(t){e({width:t.width,height:t.height})},fail:function(){e({width:0,height:0})}})})):xs&&9===Us?Promise.resolve({width:0,height:0}):new Promise((function(e,n){var r=new Image;r.onload=function(){e({width:this.width,height:this.height}),r=null},r.onerror=function(){e({width:0,height:0}),r=null},r.src=t}))})).then((function(t){var n=t.width,r=t.height;return o.updateImageInfoArray({width:n,height:r}),e}))}},{key:"_uploadFile",value:function(e){var t=this.tim,n=t.uploadController,r=t.messageController,o=e.getElements()[0],i=r.getMessageOptionByID(e.messageID);return n.uploadFile({file:i.payload.file,to:i.to,onProgress:function(e){if(o.updatePercent(e),ma(i.onProgress))try{i.onProgress(e)}catch(t){return cg(new _p({code:Op,message:"".concat(Hf)}))}}}).then((function(t){var n=t.location,r=Oa(n);return o.updateFileUrl(r),e}))}},{key:"_uploadAudio",value:function(e){var t=this.tim,n=t.uploadController,r=t.messageController,o=e.getElements()[0],i=r.getMessageOptionByID(e.messageID);return n.uploadAudio({file:i.payload.file,to:i.to,onProgress:function(e){if(o.updatePercent(e),ma(i.onProgress))try{i.onProgress(e)}catch(t){return cg(new _p({code:Op,message:"".concat(Hf)}))}}}).then((function(t){var n=t.location,r=Oa(n);return o.updateAudioUrl(r),e}))}},{key:"_uploadVideo",value:function(e){var t=this.tim,n=t.uploadController,r=t.messageController,o=e.getElements()[0],i=r.getMessageOptionByID(e.messageID);return n.uploadVideo({file:i.payload.file,to:i.to,onProgress:function(e){if(o.updatePercent(e),ma(i.onProgress))try{i.onProgress(e)}catch(t){return cg(new _p({code:Op,message:"".concat(Hf)}))}}}).then((function(t){var n=Oa(t.location);return o.updateVideoUrl(n),e}))}}]),e}(),I_=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;return wn(this,n),(r=t.call(this,e))._initializeMembers(),r._initializeListener(),r._initialzeHandlers(),r.messageOptionMap=new Map,r}return bn(n,[{key:"_initializeMembers",value:function(){this.messagesList=new f_({tim:this.tim}),this.currentMessageKey={},this.singlyLinkedList=new __(100)}},{key:"_initialzeHandlers",value:function(){this.readReportHandler=new h_(this.tim,this),this.getMessageHandler=new v_({messageController:this,tim:this.tim}),this.uploadFileHandler=new C_(this.tim)}},{key:"reset",value:function(){this.messagesList.reset(),this.currentMessageKey={},this.getMessageHandler.reset(),this.singlyLinkedList.reset(),this.messageOptionMap.clear()}},{key:"_initializeListener",value:function(){var e=this.tim.innerEmitter;e.on(_d,this._onReceiveC2CMessage,this),e.on(rd,this._onSyncMessagesProcessing,this),e.on(od,this._onSyncMessagesFinished,this),e.on(Cd,this._onReceiveGroupMessage,this),e.on(Id,this._onReceiveGroupTips,this),e.on(Md,this._onReceiveSystemNotice,this),e.on(Dd,this._onReceiveGroupMessageRevokedNotice,this),e.on(kd,this._onReceiveC2CMessageRevokedNotice,this),e.on(Ud,this._clearConversationMessages,this)}},{key:"sendMessageInstance",value:function(e,t){var n,r=this,o=this.tim.sumStatController,i=null;switch(e.conversationType){case fn.CONV_C2C:i=this._handleOnSendC2CMessageSuccess.bind(this);break;case fn.CONV_GROUP:i=this._handleOnSendGroupMessageSuccess.bind(this);break;default:return cg(new _p({code:bp,message:jf}))}return this.singlyLinkedList.pushIn(e.random),this.uploadFileHandler.upload(e).then((function(){var i=null;return e.isSendable()?(o.addTotalCount(dg),n=Date.now(),e.conversationType===fn.CONV_C2C?i=r._createC2CMessagePack(e,t):e.conversationType===fn.CONV_GROUP&&(i=r._createGroupMessagePack(e,t)),r.request(i)):cg({code:Kp,message:nh})})).then((function(t){return o.addSuccessCount(dg),o.addCost(dg,Math.abs(Date.now()-n)),e.conversationType===fn.CONV_GROUP&&(e.sequence=t.data.sequence,e.time=t.data.time,e.generateMessageID(r.tim.context.identifier)),r.messagesList.pushIn(e),i(e,t.data),r.messageOptionMap.delete(e.messageID),r.emitInnerEvent(id,{eventDataList:[{conversationID:e.conversationID,unreadCount:0,type:e.conversationType,subType:e.conversationSubType,lastMessage:e}]}),new ig({message:e})})).catch((function(t){e.status=Lu.FAIL;var n=new Cg;return n.setMethod(bg).setMessageType(e.type).setText("".concat(r._generateTjgID(e),"-").concat(e.type,"-").concat(e.from,"-").concat(e.to)).setStart(),r.probeNetwork().then((function(e){var r=Hn(e,2),o=r[0],i=r[1];n.setError(t,o,i).setEnd()})),aa.error("MessageController.sendMessageInstance error:",t),cg(new _p({code:t&&t.code?t.code:kp,message:t&&t.message?t.message:Uf,data:{message:e}}))}))}},{key:"resendMessage",value:function(e){return e.isResend=!0,e.status=Lu.UNSEND,this.sendMessageInstance(e)}},{key:"_isFileLikeMessage",value:function(e){return[fn.MSG_IMAGE,fn.MSG_FILE,fn.MSG_AUDIO,fn.MSG_VIDEO].indexOf(e.type)>=0}},{key:"_resendBinaryTypeMessage",value:function(){}},{key:"_canIUseOnlineOnlyFlag",value:function(e){var t=this.tim.groupController.getJoinedAVChatRoom();return!t||t.groupID!==e.to||e.conversationType!==fn.CONV_GROUP}},{key:"_createC2CMessagePack",value:function(e,t){var n=0,r=null;return t&&(t.offlinePushInfo&&(r=t.offlinePushInfo),!0===t.onlineUserOnly&&(n=1,r?r.disablePush=!0:r={disablePush:!0})),{name:"c2cMessage",action:"create",tjgID:this._generateTjgID(e),param:{toAccount:e.to,msgBody:e.getElements(),msgSeq:e.sequence,msgRandom:e.random,msgLifeTime:this._canIUseOnlineOnlyFlag(e)&&n?0:void 0,offlinePushInfo:r?{pushFlag:!0===r.disablePush?1:0,title:r.title||"",desc:r.description||"",ext:r.extension||"",apnsInfo:{badgeMode:!0===r.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:r.androidOPPOChannelID||""}}:void 0}}}},{key:"_handleOnSendC2CMessageSuccess",value:function(e,t){e.status=Lu.SUCCESS,e.time=t.time}},{key:"_createGroupMessagePack",value:function(e,t){var n=0,r=null;return t&&(!0===t.onlineUserOnly&&(n=1),t.offlinePushInfo&&(r=t.offlinePushInfo)),{name:"groupMessage",action:"create",tjgID:this._generateTjgID(e),param:{groupID:e.to,msgBody:e.getElements(),random:e.random,priority:e.priority,clientSequence:e.clientSequence,onlineOnlyFlag:this._canIUseOnlineOnlyFlag(e)?n:0,offlinePushInfo:r?{pushFlag:!0===r.disablePush?1:0,title:r.title||"",desc:r.description||"",ext:r.extension||"",apnsInfo:{badgeMode:!0===r.ignoreIOSBadge?1:0},androidInfo:{OPPOChannelID:r.androidOPPOChannelID||""}}:void 0}}}},{key:"_handleOnSendGroupMessageSuccess",value:function(e,t){e.sequence=t.sequence,e.time=t.time,e.status=Lu.SUCCESS}},{key:"_onReceiveC2CMessage",value:function(e){aa.debug("MessageController._onReceiveC2CMessage nums=".concat(e.data.length));var t=Date.now(),n=this._newC2CMessageStoredAndSummary({notifiesList:e.data,type:fn.CONV_C2C,C2CRemainingUnreadList:e.C2CRemainingUnreadList}),r=n.eventDataList,o=n.result;if(r.length>0&&this.emitInnerEvent(ud,{eventDataList:r,result:o}),o.length>0){var i=this.tim.sumStatController;i.addTotalCount(gg),i.addSuccessCount(gg),i.addCost(gg,Date.now()-t),this.emitOuterEvent(pn.MESSAGE_RECEIVED,o)}}},{key:"_onReceiveGroupMessage",value:function(e){aa.debug("MessageController._onReceiveGroupMessage nums=".concat(e.data.length));var t=Date.now(),n=this.newGroupMessageStoredAndSummary(e.data),r=n.eventDataList,o=n.result;if(r.length>0&&this.emitInnerEvent(cd,{eventDataList:r,result:o,isGroupTip:!1}),o.length>0){var i=this.tim.sumStatController;i.addTotalCount(gg),i.addSuccessCount(gg),i.addCost(gg,Date.now()-t),this.emitOuterEvent(pn.MESSAGE_RECEIVED,o)}}},{key:"_onReceiveGroupTips",value:function(e){var t=Date.now(),n=e.data;aa.debug("MessageController._onReceiveGroupTips nums=".concat(n.length));var r=this.newGroupTipsStoredAndSummary(n),o=r.eventDataList,i=r.result;if(o.length>0&&this.emitInnerEvent(cd,{eventDataList:o,result:i,isGroupTip:!0}),i.length>0){var s=this.tim.sumStatController;s.addTotalCount(gg),s.addSuccessCount(gg),s.addCost(gg,Date.now()-t),this.emitOuterEvent(pn.MESSAGE_RECEIVED,i)}}},{key:"_onReceiveSystemNotice",value:function(e){var t=Date.now(),n=e.data,r=n.groupSystemNotices,o=n.type;aa.debug("MessageController._onReceiveSystemNotice nums=".concat(r.length));var i=this.newSystemNoticeStoredAndSummary({notifiesList:r,type:o}),s=i.eventDataList,a=i.result;if(s.length>0&&this.emitInnerEvent(ld,{eventDataList:s,result:a,type:o}),a.length>0&&"poll"===o){var u=this.tim.sumStatController;u.addTotalCount(gg),u.addSuccessCount(gg),u.addCost(gg,Date.now()-t),this.emitOuterEvent(pn.MESSAGE_RECEIVED,a)}}},{key:"_onReceiveGroupMessageRevokedNotice",value:function(e){var t=this;aa.debug("MessageController._onReceiveGroupMessageRevokedNotice nums=".concat(e.data.length));var n=[],r=null;e.data.forEach((function(e){e.elements.revokedInfos.forEach((function(e){(r=t.messagesList.revoke("GROUP".concat(e.groupID),e.sequence))&&n.push(r)}))})),0!==n.length&&(this.emitInnerEvent(pd,n),this.emitOuterEvent(pn.MESSAGE_REVOKED,n))}},{key:"_onReceiveC2CMessageRevokedNotice",value:function(e){var t=this;aa.debug("MessageController._onReceiveC2CMessageRevokedNotice nums=".concat(e.data.length));var n=[],r=null;e.data.forEach((function(e){e.c2cMessageRevokedNotify.revokedInfos.forEach((function(e){var o=t.tim.context.identifier===e.from?"C2C".concat(e.to):"C2C".concat(e.from);(r=t.messagesList.revoke(o,e.sequence,e.random))&&n.push(r)}))})),0!==n.length&&(this.emitInnerEvent(pd,n),this.emitOuterEvent(pn.MESSAGE_REVOKED,n))}},{key:"_clearConversationMessages",value:function(e){var t=e.data;this.messagesList.removeByConversationID(t),this.getMessageHandler.deleteCompletedItem(t)}},{key:"_pushIntoNoticeResult",value:function(e,t){return!(!this.messagesList.pushIn(t)||this.singlyLinkedList.has(t.random))&&(e.push(t),!0)}},{key:"_newC2CMessageStoredAndSummary",value:function(e){for(var t=e.notifiesList,n=e.type,r=e.C2CRemainingUnreadList,o=e.isFromSync,i=null,s=[],a=[],u={},c=this.tim.bigDataHallwayController,l=0,p=t.length;l<p;l++){var f=t[l];(f.currentUser=this.tim.context.identifier,f.conversationType=n,f.isSystemMessage=!!f.isSystemMessage,i=new Bh(f),f.elements=c.parseElements(f.elements,f.from),i.setElement(f.elements),o||this._pushIntoNoticeResult(a,i))&&(void 0===u[i.conversationID]?u[i.conversationID]=s.push({conversationID:i.conversationID,unreadCount:"out"===i.flow?0:1,type:i.conversationType,subType:i.conversationSubType,lastMessage:i})-1:(s[u[i.conversationID]].type=i.conversationType,s[u[i.conversationID]].subType=i.conversationSubType,s[u[i.conversationID]].lastMessage=i,"in"===i.flow&&s[u[i.conversationID]].unreadCount++))}if(ha(r))for(var h=function(e,t){var n=s.find((function(t){return t.conversationID==="C2C".concat(r[e].from)}));n?n.unreadCount+=r[e].count:s.push({conversationID:"C2C".concat(r[e].from),unreadCount:r[e].count,type:fn.CONV_C2C,lastMsgTime:r[e].lastMsgTime})},d=0,g=r.length;d<g;d++)h(d);return{eventDataList:s,result:a}}},{key:"newGroupMessageStoredAndSummary",value:function(e){var t=null,n=[],r={},o=[],i=fn.CONV_GROUP,s=this.tim.bigDataHallwayController,a=e.length;a>1&&e.sort((function(e,t){return e.sequence-t.sequence}));for(var u=0;u<a;u++){var c=e[u];c.currentUser=this.tim.context.identifier,c.conversationType=i,c.isSystemMessage=!!c.isSystemMessage,t=new Bh(c),c.elements=s.parseElements(c.elements,c.from),t.setElement(c.elements),this._isMessageFromAVChatroom(t)||this._pushIntoNoticeResult(o,t)&&(void 0===r[t.conversationID]?r[t.conversationID]=n.push({conversationID:t.conversationID,unreadCount:"out"===t.flow?0:1,type:t.conversationType,subType:t.conversationSubType,lastMessage:t})-1:(n[r[t.conversationID]].type=t.conversationType,n[r[t.conversationID]].subType=t.conversationSubType,n[r[t.conversationID]].lastMessage=t,"in"===t.flow&&n[r[t.conversationID]].unreadCount++))}return{eventDataList:n,result:o}}},{key:"_isMessageFromAVChatroom",value:function(e){var t=e.conversationID.slice(5);return this.tim.groupController.checkJoinedAVChatRoomByID(t)}},{key:"newGroupTipsStoredAndSummary",value:function(e){for(var t=null,n=[],r=[],o={},i=0,s=e.length;i<s;i++){var a=e[i];a.currentUser=this.tim.context.identifier,a.conversationType=fn.CONV_GROUP,(t=new Bh(a)).setElement({type:fn.MSG_GRP_TIP,content:Ln({},a.elements,{groupProfile:a.groupProfile})}),t.isSystemMessage=!1,this._isMessageFromAVChatroom(t)||this._pushIntoNoticeResult(r,t)&&(void 0===o[t.conversationID]?o[t.conversationID]=n.push({conversationID:t.conversationID,unreadCount:"out"===t.flow?0:1,type:t.conversationType,subType:t.conversationSubType,lastMessage:t})-1:(n[o[t.conversationID]].type=t.conversationType,n[o[t.conversationID]].subType=t.conversationSubType,n[o[t.conversationID]].lastMessage=t,"in"===t.flow&&n[o[t.conversationID]].unreadCount++))}return{eventDataList:n,result:r}}},{key:"newSystemNoticeStoredAndSummary",value:function(e){var t=e.notifiesList,n=e.type,r=null,o=t.length,i=0,s=[],a={conversationID:fn.CONV_SYSTEM,unreadCount:0,type:fn.CONV_SYSTEM,subType:null,lastMessage:null};for(i=0;i<o;i++){var u=t[i];u.elements.operationType!==Xl&&(u.currentUser=this.tim.context.identifier,u.conversationType=fn.CONV_SYSTEM,u.conversationID=fn.CONV_SYSTEM,(r=new Bh(u)).setElement({type:fn.MSG_GRP_SYS_NOTICE,content:Ln({},u.elements,{groupProfile:u.groupProfile})}),r.isSystemMessage=!0,(1===r.sequence&&1===r.random||2===r.sequence&&2===r.random)&&(r.sequence=Da(),r.random=Da(),r.generateMessageID(u.currentUser),aa.log("MessageController.newSystemNoticeStoredAndSummary sequence and random maybe duplicated, regenerate. ID=".concat(r.ID))),this._pushIntoNoticeResult(s,r)&&("poll"===n?a.unreadCount++:"sync"===n&&r.setIsRead(!0),a.subType=r.conversationSubType))}return a.lastMessage=s[s.length-1],{eventDataList:s.length>0?[a]:[],result:s}}},{key:"_onSyncMessagesProcessing",value:function(e){var t=this._newC2CMessageStoredAndSummary({notifiesList:e.data,type:fn.CONV_C2C,isFromSync:!0,C2CRemainingUnreadList:e.C2CRemainingUnreadList}),n=t.eventDataList,r=t.result;this.emitInnerEvent(sd,{eventDataList:n,result:r})}},{key:"_onSyncMessagesFinished",value:function(e){this.triggerReady();var t=this._newC2CMessageStoredAndSummary({notifiesList:e.data.messageList,type:fn.CONV_C2C,isFromSync:!0,C2CRemainingUnreadList:e.data.C2CRemainingUnreadList}),n=t.eventDataList,r=t.result;this.emitInnerEvent(ad,{eventDataList:n,result:r})}},{key:"getHistoryMessages",value:function(e){if(e.conversationID===fn.CONV_SYSTEM)return ug();!e.count&&(e.count=15),e.count>20&&(e.count=20);var t=this.messagesList.getLocalOldestMessageByConversationID(e.conversationID);t||((t={}).time=0,t.sequence=0,0===e.conversationID.indexOf(fn.CONV_C2C)?(t.to=e.conversationID.replace(fn.CONV_C2C,""),t.conversationType=fn.CONV_C2C):0===e.conversationID.indexOf(fn.CONV_GROUP)&&(t.to=e.conversationID.replace(fn.CONV_GROUP,""),t.conversationType=fn.CONV_GROUP));var n="";switch(t.conversationType){case fn.CONV_C2C:return n=e.conversationID.replace(fn.CONV_C2C,""),this.getC2CRoamMessages({conversationID:e.conversationID,peerAccount:n,count:e.count,lastMessageTime:void 0===this.currentMessageKey[e.conversationID]?0:t.time});case fn.CONV_GROUP:return this.getGroupRoamMessages({conversationID:e.conversationID,groupID:t.to,count:e.count,sequence:t.sequence-1});default:return ug()}}},{key:"getC2CRoamMessages",value:function(e){var t=this,n=void 0!==this.currentMessageKey[e.conversationID]?this.currentMessageKey[e.conversationID]:"";aa.log("MessageController.getC2CRoamMessages toAccount=".concat(e.peerAccount," count=").concat(e.count||15," lastMessageTime=").concat(e.lastMessageTime||0," messageKey=").concat(n));var r=new Cg;return r.setMethod(Rg).setStart(),this.request({name:"c2cMessage",action:"query",param:{peerAccount:e.peerAccount,count:e.count||15,lastMessageTime:e.lastMessageTime||0,messageKey:n}}).then((function(o){var i=o.data,s=i.complete,a=i.messageList;da(a)?aa.log("MessageController.getC2CRoamMessages ok. complete=".concat(s," but messageList is undefined!")):aa.log("MessageController.getC2CRoamMessages ok. complete=".concat(s," nums=").concat(a.length)),r.setCode(0).setNetworkType(t.getNetworkType()).setText("".concat(e.peerAccount,"-").concat(e.count||15,"-").concat(e.lastMessageTime||0,"-").concat(n,"-").concat(s,"-").concat(a?a.length:"undefined")).setEnd(),1===s&&t.getMessageHandler.setCompleted(e.conversationID);var u=t._roamMessageStore(a,fn.CONV_C2C,e.conversationID);return t.readReportHandler.updateIsRead(e.conversationID),t.currentMessageKey[e.conversationID]=o.data.messageKey,u})).catch((function(o){return t.probeNetwork().then((function(t){var i=Hn(t,2),s=i[0],a=i[1];r.setError(o,s,a).setText("".concat(e.peerAccount,"-").concat(e.count||15,"-").concat(e.lastMessageTime||0,"-").concat(n)).setEnd()})),aa.warn("MessageController.getC2CRoamMessages failed. ".concat(o)),cg(o)}))}},{key:"_computeLastSequence",value:function(e){return e.sequence>=0?Promise.resolve(e.sequence):this.tim.groupController.getGroupLastSequence(e.groupID)}},{key:"getGroupRoamMessages",value:function(e){var t=this,n=new Cg,r=0;return this._computeLastSequence(e).then((function(o){return r=o,aa.log("MessageController.getGroupRoamMessages groupID=".concat(e.groupID," lastSequence=").concat(r)),n.setMethod(Og).setStart(),t.request({name:"groupMessage",action:"query",param:{groupID:e.groupID,count:21,sequence:r}})})).then((function(o){var i=o.data,s=i.messageList,a=i.complete;da(s)?aa.log("MessageController.getGroupRoamMessages ok. complete=".concat(a," but messageList is undefined!")):aa.log("MessageController.getGroupRoamMessages ok. complete=".concat(a," nums=").concat(s.length)),n.setCode(0).setNetworkType(t.getNetworkType()).setText("".concat(e.groupID,"-").concat(r,"-").concat(a,"-").concat(s?s.length:"undefined")).setEnd();var u="GROUP".concat(e.groupID);if(2===a||qa(s))return t.getMessageHandler.setCompleted(u),[];var c=t._roamMessageStore(s,fn.CONV_GROUP,u);return t.readReportHandler.updateIsRead(u),c})).catch((function(o){return t.probeNetwork().then((function(t){var i=Hn(t,2),s=i[0],a=i[1];n.setError(o,s,a).setText("".concat(e.groupID,"-").concat(r)).setEnd()})),aa.warn("MessageController.getGroupRoamMessages failed. ".concat(o)),cg(o)}))}},{key:"_roamMessageStore",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0,r=null,o=[],i=0,s=e.length,a=null,u=t===fn.CONV_GROUP,c=this.tim.bigDataHallwayController,l=function(){i=u?e.length-1:0,s=u?0:e.length},p=function(){u?--i:++i},f=function(){return u?i>=s:i<s};for(l();f();p())if(u&&1===e[i].sequence&&this.getMessageHandler.setCompleted(n),1!==e[i].isPlaceMessage)if((r=new Bh(e[i])).to=e[i].to,r.isSystemMessage=!!e[i].isSystemMessage,r.conversationType=t,e[i].event===Fl.JSON.TYPE.GROUP.TIP?a={type:fn.MSG_GRP_TIP,content:Ln({},e[i].elements,{groupProfile:e[i].groupProfile})}:(e[i].elements=c.parseElements(e[i].elements,e[i].from),a=e[i].elements),qa(a)){var h=new Cg;h.setMethod(Gg).setText("from:".concat(r.from," to:").concat(r.to," sequence:").concat(r.sequence," event:").concat(e[i].event)).setStart(),h.setCode(0).setNetworkType(this.getNetworkType()).setEnd()}else r.setElement(a),r.reInitialize(this.tim.context.identifier),o.push(r);return this.messagesList.unshift(o),l=p=f=null,o}},{key:"getLocalMessageList",value:function(e){return this.messagesList.getLocalMessageList(e)}},{key:"getLocalMessage",value:function(e,t){return this.messagesList.getLocalMessage(e,t)}},{key:"hasLocalMessage",value:function(e,t){return this.messagesList.hasLocalMessage(e,t)}},{key:"deleteLocalMessage",value:function(e){e instanceof Bh&&this.messagesList.remove(e)}},{key:"revokeMessage",value:function(e){var t,n=this;e.conversationType===fn.CONV_C2C?t={name:"c2cMessageWillBeRevoked",action:"create",param:{msgInfo:{fromAccount:e.from,toAccount:e.to,msgSeq:e.sequence,msgRandom:e.random,msgTimeStamp:e.time}}}:e.conversationType===fn.CONV_GROUP&&(t={name:"groupMessageWillBeRevoked",action:"create",param:{to:e.to,msgSeqList:[{msgSeq:e.sequence}]}});var r=new Cg;return r.setMethod(Lg).setMessageType(e.type).setText("".concat(this._generateTjgID(e),"-").concat(e.type,"-").concat(e.from,"-").concat(e.to)).setStart(),this.request(t).then((function(t){var o=t.data.recallRetList;if(!qa(o)&&0!==o[0].retCode){var i=new _p({code:o[0].retCode,message:yp[o[0].retCode]||Vf,data:{message:e}});return r.setCode(i.code).setMessage(i.message).setEnd(),cg(i)}return aa.info("MessageController.revokeMessage ok. ID=".concat(e.ID)),e.isRevoked=!0,r.setCode(0).setEnd(),n.emitInnerEvent(pd,[e]),new ig({message:e})})).catch((function(t){n.probeNetwork().then((function(e){var n=Hn(e,2),o=n[0],i=n[1];r.setError(t,o,i).setEnd()}));var o=new _p({code:t&&t.code?t.code:Lp,message:t&&t.message?t.message:Vf,data:{message:e}});return aa.warn("MessageController.revokeMessage failed. ID=".concat(e.ID," code=").concat(o.code," message=").concat(o.message)),cg(o)}))}},{key:"setMessageRead",value:function(e){var t=this;return new Promise((function(n,r){t.ready((function(){t.readReportHandler.setMessageRead(e).then(n).catch(r)}))}))}},{key:"getMessageList",value:function(e){return this.getMessageHandler.getMessageList(e)}},{key:"createTextMessage",value:function(e){e.currentUser=this.tim.context.identifier;var t=new Bh(e),n="string"==typeof e.payload?e.payload:e.payload.text,r=new Pu({text:n});return t.setElement(r),t}},{key:"createCustomMessage",value:function(e){e.currentUser=this.tim.context.identifier;var t=new Bh(e),n=new Uh({data:e.payload.data,description:e.payload.description,extension:e.payload.extension});return t.setElement(n),t}},{key:"createImageMessage",value:function(e){e.currentUser=this.tim.context.identifier;var t=new Bh(e);if(Os){var n=e.payload.file;if(ua(n))return void aa.warn("微信小程序环境下调用 createImageMessage 接口时，payload.file 不支持传入 File 对象");var r=n.tempFilePaths[0],o={url:r,name:r.slice(r.lastIndexOf("/")+1),size:n.tempFiles[0].size,type:r.slice(r.lastIndexOf(".")+1).toLowerCase()};e.payload.file=o}else if(Rs&&ua(e.payload.file)){var i=e.payload.file;e.payload.file={files:[i]}}var s=new ip({imageFormat:"UNKNOWN",uuid:this._generateUUID(),file:e.payload.file});return t.setElement(s),this.messageOptionMap.set(t.messageID,e),t}},{key:"createFileMessage",value:function(e){if(!Os){if(Rs&&ua(e.payload.file)){var t=e.payload.file;e.payload.file={files:[t]}}e.currentUser=this.tim.context.identifier;var n=new Bh(e),r=new xh({uuid:this._generateUUID(),file:e.payload.file});return n.setElement(r),this.messageOptionMap.set(n.messageID,e),n}aa.warn("微信小程序目前不支持选择文件， createFileMessage 接口不可用！")}},{key:"createAudioMessage",value:function(e){if(Os){var t=e.payload.file;if(Os){var n={url:t.tempFilePath,name:t.tempFilePath.slice(t.tempFilePath.lastIndexOf("/")+1),size:t.fileSize,second:parseInt(t.duration)/1e3,type:t.tempFilePath.slice(t.tempFilePath.lastIndexOf(".")+1).toLowerCase()};e.payload.file=n}e.currentUser=this.tim.context.identifier;var r=new Bh(e),o=new ap({second:Math.floor(t.duration/1e3),size:t.fileSize,url:t.tempFilePath,uuid:this._generateUUID()});return r.setElement(o),this.messageOptionMap.set(r.messageID,e),r}aa.warn("createAudioMessage 目前只支持微信小程序发语音消息")}},{key:"createVideoMessage",value:function(e){e.currentUser=this.tim.context.identifier,e.payload.file.thumbUrl="https://webim-**********.cos.ap-shanghai.myqcloud.com/assets/images/transparent.png",e.payload.file.thumbSize=1668;var t={};if(Os){if(ua(e.payload.file))return void aa.warn("微信小程序环境下调用 createVideoMessage 接口时，payload.file 不支持传入 File 对象");var n=e.payload.file;t.url=n.tempFilePath,t.name=n.tempFilePath.slice(n.tempFilePath.lastIndexOf("/")+1),t.size=n.size,t.second=n.duration,t.type=n.tempFilePath.slice(n.tempFilePath.lastIndexOf(".")+1).toLowerCase()}else if(Rs){if(ua(e.payload.file)){var r=e.payload.file;e.payload.file.files=[r]}var o=e.payload.file;t.url=window.URL.createObjectURL(o.files[0]),t.name=o.files[0].name,t.size=o.files[0].size,t.second=o.files[0].duration||0,t.type=o.files[0].type.split("/")[1]}e.payload.file.videoFile=t;var i=new Bh(e),s=new qh({videoFormat:t.type,videoSecond:Number(t.second.toFixed(0)),videoSize:t.size,remoteVideoUrl:"",videoUrl:t.url,videoUUID:this._generateUUID(),thumbUUID:this._generateUUID(),thumbWidth:e.payload.file.width||200,thumbHeight:e.payload.file.height||200,thumbUrl:e.payload.file.thumbUrl,thumbSize:e.payload.file.thumbSize,thumbFormat:e.payload.file.thumbUrl.slice(e.payload.file.thumbUrl.lastIndexOf(".")+1).toLowerCase()});return i.setElement(s),this.messageOptionMap.set(i.messageID,e),i}},{key:"createFaceMessage",value:function(e){e.currentUser=this.tim.context.identifier;var t=new Bh(e),n=new sp(e.payload);return t.setElement(n),t}},{key:"_generateUUID",value:function(){var e=this.tim.context;return"".concat(e.SDKAppID,"-").concat(e.identifier,"-").concat(function(){for(var e="",t=32;t>0;--t)e+=ka[Math.floor(Math.random()*wa)];return e}())}},{key:"_generateTjgID",value:function(e){return this.tim.context.tinyID+"-"+e.random}},{key:"getMessageOptionByID",value:function(e){return this.messageOptionMap.get(e)}},{key:"isMessageSentByCurrentInstance",value:function(e){return!(!this.messagesList.hasLocalMessage(e.conversationID,e.ID)&&!this.singlyLinkedList.has(e.random))}}]),n}(ng),M_=function(){function e(t){wn(this,e),this.userID="",this.avatar="",this.nick="",this.role="",this.joinTime="",this.lastSendMsgTime="",this.nameCard="",this.muteUntil=0,this.memberCustomField=[],this._initMember(t)}return bn(e,[{key:"_initMember",value:function(e){this.updateMember(e)}},{key:"updateMember",value:function(e){var t=[null,void 0,"",0,NaN];e.memberCustomField&&La(this.memberCustomField,e.memberCustomField),Ma(this,e,["memberCustomField"],t)}},{key:"updateRole",value:function(e){["Owner","Admin","Member"].indexOf(e)<0||(this.role=e)}},{key:"updateMuteUntil",value:function(e){da(e)||(this.muteUntil=Math.floor((Date.now()+1e3*e)/1e3))}},{key:"updateNameCard",value:function(e){da(e)||(this.nameCard=e)}},{key:"updateMemberCustomField",value:function(e){e&&La(this.memberCustomField,e)}}]),e}(),S_=function(){function e(t){wn(this,e),this.tim=t.tim,this.groupController=t.groupController,this._initListeners()}return bn(e,[{key:"_initListeners",value:function(){this.tim.innerEmitter.on(cd,this._onReceivedGroupTips,this)}},{key:"_onReceivedGroupTips",value:function(e){var t=this,n=e.data,r=n.result;n.isGroupTip&&r.forEach((function(e){switch(e.payload.operationType){case 1:t._onNewMemberComeIn(e);break;case 2:t._onMemberQuit(e);break;case 3:t._onMemberKickedOut(e);break;case 4:t._onMemberSetAdmin(e);break;case 5:t._onMemberCancelledAdmin(e);break;case 6:t._onGroupProfileModified(e);break;case 7:t._onMemberInfoModified(e);break;default:aa.warn("GroupTipsHandler._onReceivedGroupTips Unhandled groupTips. operationType=",e.payload.operationType)}}))}},{key:"_onNewMemberComeIn",value:function(e){var t=e.payload,n=t.memberNum,r=t.groupProfile.groupID,o=this.groupController.getLocalGroupProfile(r);o&&ca(n)&&(o.memberNum=n)}},{key:"_onMemberQuit",value:function(e){var t=e.payload,n=t.memberNum,r=t.groupProfile.groupID,o=this.groupController.getLocalGroupProfile(r);o&&ca(n)&&(o.memberNum=n),this.groupController.deleteLocalGroupMembers(r,e.payload.userIDList)}},{key:"_onMemberKickedOut",value:function(e){var t=e.payload,n=t.memberNum,r=t.groupProfile.groupID,o=this.groupController.getLocalGroupProfile(r);o&&ca(n)&&(o.memberNum=n),this.groupController.deleteLocalGroupMembers(r,e.payload.userIDList)}},{key:"_onMemberSetAdmin",value:function(e){var t=this,n=e.payload.groupProfile.groupID;e.payload.userIDList.forEach((function(e){var r=t.groupController.getLocalGroupMemberInfo(n,e);r&&r.updateRole(fn.GRP_MBR_ROLE_ADMIN)}))}},{key:"_onMemberCancelledAdmin",value:function(e){var t=this,n=e.payload.groupProfile.groupID;e.payload.userIDList.forEach((function(e){var r=t.groupController.getLocalGroupMemberInfo(n,e);r&&r.updateRole(fn.GRP_MBR_ROLE_MEMBER)}))}},{key:"_onGroupProfileModified",value:function(e){var t=this,n=e.payload.newGroupProfile,r=e.payload.groupProfile.groupID,o=this.groupController.getLocalGroupProfile(r);Object.keys(n).forEach((function(e){switch(e){case"ownerID":t._ownerChaged(o,n);break;default:o[e]=n[e]}})),this.groupController.emitGroupListUpdate(!0,!0)}},{key:"_ownerChaged",value:function(e,t){var n=e.groupID,r=this.groupController.getLocalGroupProfile(n),o=this.tim.context.identifier;if(o===t.ownerID){r.updateGroup({selfInfo:{role:fn.GRP_MBR_ROLE_OWNER}});var i=this.groupController.getLocalGroupMemberInfo(n,o),s=this.groupController.getLocalGroupProfile(n).ownerID,a=this.groupController.getLocalGroupMemberInfo(n,s);i&&i.updateRole(fn.GRP_MBR_ROLE_OWNER),a&&a.updateRole(fn.GRP_MBR_ROLE_MEMBER)}}},{key:"_onMemberInfoModified",value:function(e){var t=this,n=e.payload.groupProfile.groupID;e.payload.memberList.forEach((function(e){var r=t.groupController.getLocalGroupMemberInfo(n,e.userID);r&&e.muteTime&&r.updateMuteUntil(e.muteTime)}))}}]),e}(),T_=function(){function e(t){wn(this,e),this.groupController=t.groupController,this.tim=t.tim,this.pendencyMap=new Map,this._initLiceners()}return bn(e,[{key:"_initLiceners",value:function(){this.tim.innerEmitter.on(ld,this._onReceivedGroupSystemNotice,this),this.tim.innerEmitter.on(od,this._clearGroupSystemNotice,this)}},{key:"_clearGroupSystemNotice",value:function(){var e=this;this.getPendencyList().then((function(t){t.forEach((function(t){e.pendencyMap.set("".concat(t.from,"_").concat(t.groupID,"_").concat(t.to),t)}));var n=e.tim.messageController.getLocalMessageList(fn.CONV_SYSTEM),r=[];n.forEach((function(t){var n=t.payload,o=n.operatorID,i=n.operationType,s=n.groupProfile;if(i===jl){var a="".concat(o,"_").concat(s.groupID,"_").concat(s.to),u=e.pendencyMap.get(a);u&&ca(u.handled)&&0!==u.handled&&r.push(t)}})),e.groupController.deleteGroupSystemNotice({messageList:r})}))}},{key:"getPendencyList",value:function(e){var t=this;return this.groupController.request({name:"group",action:"getGroupPendency",param:{startTime:e&&e.startTime?e.startTime:0,limit:e&&e.limit?e.limit:10,handleAccount:this.tim.context.identifier}}).then((function(e){var n=e.data,r=n.pendencyList;return 0!==n.nextStartTime?t.getPendencyList({startTime:n.nextStartTime}).then((function(e){return[].concat(Vn(r),Vn(e))})):r}))}},{key:"_onReceivedGroupSystemNotice",value:function(e){var t=this,n=e.data,r=n.result;"sync"!==n.type&&r.forEach((function(e){switch(e.payload.operationType){case 1:t._onApplyGroupRequest(e);break;case 2:t._onApplyGroupRequestAgreed(e);break;case 3:t._onApplyGroupRequestRefused(e);break;case 4:t._onMemberKicked(e);break;case 5:t._onGroupDismissed(e);break;case 6:break;case 7:t._onInviteGroup(e);break;case 8:t._onQuitGroup(e);break;case 9:t._onSetManager(e);break;case 10:t._onDeleteManager(e);break;case 11:case 12:case 15:break;case 255:t.groupController.emitOuterEvent(pn.GROUP_SYSTEM_NOTICE_RECEIVED,{message:e,type:Jl})}}))}},{key:"_onApplyGroupRequest",value:function(e){this.groupController.emitOuterEvent(pn.GROUP_SYSTEM_NOTICE_RECEIVED,{message:e,type:jl})}},{key:"_onApplyGroupRequestAgreed",value:function(e){var t=this,n=e.payload.groupProfile.groupID;this.groupController.hasLocalGroup(n)||this.groupController.getGroupProfile({groupID:n}).then((function(e){var n=e.data.group;n&&(t.groupController.updateGroupMap([n]),t.groupController.emitGroupListUpdate())})),this.groupController.emitOuterEvent(pn.GROUP_SYSTEM_NOTICE_RECEIVED,{message:e,type:Bl})}},{key:"_onApplyGroupRequestRefused",value:function(e){this.groupController.emitOuterEvent(pn.GROUP_SYSTEM_NOTICE_RECEIVED,{message:e,type:Hl})}},{key:"_onMemberKicked",value:function(e){var t=e.payload.groupProfile.groupID;this.groupController.hasLocalGroup(t)&&this.groupController.deleteLocalGroupAndConversation(t),this.groupController.emitOuterEvent(pn.GROUP_SYSTEM_NOTICE_RECEIVED,{message:e,type:Vl})}},{key:"_onGroupDismissed",value:function(e){var t=e.payload.groupProfile.groupID,n=this.groupController.hasLocalGroup(t),r=this.groupController.AVChatRoomHandler;n&&this.groupController.deleteLocalGroupAndConversation(t),r.checkJoinedAVChatRoomByID(t)&&r.reset(),this.groupController.emitOuterEvent(pn.GROUP_SYSTEM_NOTICE_RECEIVED,{message:e,type:Kl})}},{key:"_onInviteGroup",value:function(e){var t=this,n=e.payload.groupProfile.groupID;this.groupController.hasLocalGroup(n)||this.groupController.getGroupProfile({groupID:n}).then((function(e){var n=e.data.group;n&&(t.groupController.updateGroupMap([n]),t.groupController.emitGroupListUpdate())})),this.groupController.emitOuterEvent(pn.GROUP_SYSTEM_NOTICE_RECEIVED,{message:e,type:$l})}},{key:"_onQuitGroup",value:function(e){var t=e.payload.groupProfile.groupID;this.groupController.hasLocalGroup(t)&&this.groupController.deleteLocalGroupAndConversation(t),this.groupController.emitOuterEvent(pn.GROUP_SYSTEM_NOTICE_RECEIVED,{message:e,type:Yl})}},{key:"_onSetManager",value:function(e){var t=e.payload.groupProfile,n=t.to,r=t.groupID,o=this.groupController.getLocalGroupMemberInfo(r,n);o&&o.updateRole(fn.GRP_MBR_ROLE_ADMIN),this.groupController.emitOuterEvent(pn.GROUP_SYSTEM_NOTICE_RECEIVED,{message:e,type:zl})}},{key:"_onDeleteManager",value:function(e){var t=e.payload.groupProfile,n=t.to,r=t.groupID,o=this.groupController.getLocalGroupMemberInfo(r,n);o&&o.updateRole(fn.GRP_MBR_ROLE_MEMBER),this.groupController.emitOuterEvent(pn.GROUP_SYSTEM_NOTICE_RECEIVED,{message:e,type:Wl})}},{key:"reset",value:function(){this.pendencyMap.clear()}}]),e}(),E_={3:!0,4:!0,5:!0,6:!0},D_=function(){function e(t){var n=t.tim,r=t.groupController;wn(this,e),this.tim=n,this.groupController=r,this.AVChatRoomLoop=null,this.key="",this.startSeq=0,this.group={},this.sequencesLinkedList=new __(100),this.receivedMessageCount=0}return bn(e,[{key:"hasJoinedAVChatRoom",value:function(){return!(!this.group||da(this.group.groupID))}},{key:"checkJoinedAVChatRoomByID",value:function(e){return!(!this.group&&da(this.group.groupID))&&e===this.group.groupID}},{key:"getJoinedAVChatRoom",value:function(){return this.hasJoinedAVChatRoom()?this.group:null}},{key:"_updateProperties",value:function(e){var t=this;Object.keys(e).forEach((function(n){t[n]=e[n]}))}},{key:"start",value:function(){var e={key:this.key,startSeq:this.startSeq};if(null===this.AVChatRoomLoop){var t=this.groupController.createTransportCapsule({name:"AVChatRoom",action:"startLongPoll",param:e});this.AVChatRoomLoop=this.tim.connectionController.createRunLoop({pack:t,before:this._updateRequestData.bind(this),success:this._handleSuccess.bind(this),fail:this._handleFailure.bind(this),isAVChatRoomLoop:!0}),this.AVChatRoomLoop.start(),aa.log("AVChatRoomHandler.start message channel started")}else this.AVChatRoomLoop.isRunning()||this.AVChatRoomLoop.start()}},{key:"stop",value:function(){null!==this.AVChatRoomLoop&&this.AVChatRoomLoop.isRunning()&&(this.AVChatRoomLoop.abort(),this.AVChatRoomLoop.stop(),aa.log("AVChatRoomHandler.stop message channel stopped"))}},{key:"startRunLoop",value:function(e){var t=this;return this._precheck().then((function(){var n=e.longPollingKey,r=e.group;return t._updateProperties({key:n,startSeq:0,group:r||{}}),t.groupController.updateGroupMap([r]),t.groupController.emitGroupListUpdate(!0,!1),t.start(),t.groupController.isLoggedIn()?ug({status:bu.SUCCESS,group:r}):ug({status:bu.SUCCESS})}))}},{key:"joinWithoutAuth",value:function(e){var t=this;return this.groupController.request({name:"group",action:"applyJoinAVChatRoom",param:e}).then((function(n){var r=n.data.longPollingKey;if(da(r))return cg(new _p({code:nf,message:hh}));aa.log("AVChatRoomHandler.joinWithoutAuth ok. groupID:",e.groupID),t.groupController.emitInnerEvent(jd),t.groupController.emitInnerEvent(Fd,e.groupID);var o=new t_({groupID:e.groupID});return t.startRunLoop({group:o,longPollingKey:r}),new ig({status:bu.SUCCESS})})).catch((function(t){return aa.error("AVChatRoomHandler.joinWithoutAuth error:".concat(t.message,". groupID:").concat(e.groupID)),cg(t)}))}},{key:"_precheck",value:function(){if(!this.hasJoinedAVChatRoom())return Promise.resolve();if(this.groupController.isLoggedIn()){if(this.group.selfInfo.role!==fn.GRP_MBR_ROLE_OWNER&&this.group.ownerID!==this.tim.loginInfo.identifier)return this.groupController.quitGroup(this.group.groupID);this.groupController.deleteLocalGroupAndConversation(this.group.groupID)}else this.groupController.deleteLocalGroupAndConversation(this.group.groupID);return this.reset(),Promise.resolve()}},{key:"_updateRequestData",value:function(e){e.StartSeq=this.startSeq,e.Key=this.key,this.tim.sumStatController.addTotalCount(hg)}},{key:"_handleSuccess",value:function(e){this.tim.sumStatController.addSuccessCount(hg),this.tim.sumStatController.addCost(hg,e.data.timecost),this.startSeq=e.data.nextSeq,this.key=e.data.key,Array.isArray(e.data.rspMsgList)&&e.data.rspMsgList.forEach((function(e){e.to=e.groupID})),e.data.rspMsgList&&e.data.rspMsgList.length>0&&this._dispatchNotice(e.data.rspMsgList),this.groupController.emitInnerEvent(Ad)}},{key:"_handleFailure",value:function(e){if(e.error)if("ECONNABORTED"===e.error.code||e.error.code===vf)if(e.error.config){var t=e.error.config.url,n=e.error.config.data;aa.log("AVChatRoomHandler._handleFailure request timed out. url=".concat(t," data=").concat(n))}else aa.log("AVChatRoomHandler._handleFailure request timed out");else aa.log("AVChatRoomHandler._handleFailure request failed due to network error");this.groupController.emitInnerEvent(wd)}},{key:"_dispatchNotice",value:function(e){if(ha(e)&&0!==e.length){var t=Date.now(),n=null,r=[],o=[],i=e.length;i>1&&e.sort((function(e,t){return e.sequence-t.sequence}));for(var s=0;s<i;s++)if(E_[e[s].event]){this.receivedMessageCount+=1;var a=(n=this.packMessage(e[s],e[s].event)).conversationID;if(this.receivedMessageCount%40==0&&this.tim.messageLossController.detectMessageLoss(a,this.sequencesLinkedList.data()),null!==this.sequencesLinkedList.tail()){var u=this.sequencesLinkedList.tail().value,c=n.sequence-u;c>1&&c<=20?this.tim.messageLossController.onMessageMaybeLost(a,u+1,c-1):c<-1&&c>=-20&&this.tim.messageLossController.onMessageMaybeLost(a,n.sequence+1,Math.abs(c)-1)}this.sequencesLinkedList.pushIn(n.sequence),this._isMessageSentByCurrentInstance(n)||(n.conversationType===fn.CONV_SYSTEM&&o.push(n),r.push(n))}else aa.warn("AVChatRoomHandler._dispatchMessage 未处理的 event 类型：",e[s].event);if(o.length>0&&this.groupController.emitInnerEvent(ld,{result:o,eventDataList:[],type:"poll"}),0!==r.length){var l=this.packConversationOption(r);l.length>0&&this.groupController.emitInnerEvent(cd,{eventDataList:l,type:"poll"}),aa.debug("AVChatRoomHandler._dispatchNotice nums=".concat(r.length));var p=this.tim.sumStatController;p.addTotalCount(mg),p.addSuccessCount(mg),p.addCost(mg,Date.now()-t),this.groupController.emitOuterEvent(pn.MESSAGE_RECEIVED,r)}}}},{key:"_isMessageSentByCurrentInstance",value:function(e){return!!this.tim.messageController.isMessageSentByCurrentInstance(e)}},{key:"packMessage",value:function(e,t){e.currentUser=this.tim.context.identifier,e.conversationType=5===t?fn.CONV_SYSTEM:fn.CONV_GROUP,e.isSystemMessage=!!e.isSystemMessage;var n=new Bh(e),r=this.packElements(e,t);return n.setElement(r),n}},{key:"packElements",value:function(e,t){return 4===t||6===t?{type:fn.MSG_GRP_TIP,content:Ln({},e.elements,{groupProfile:e.groupProfile})}:5===t?{type:fn.MSG_GRP_SYS_NOTICE,content:Ln({},e.elements,{groupProfile:e.groupProfile})}:this.tim.bigDataHallwayController.parseElements(e.elements,e.from)}},{key:"packConversationOption",value:function(e){for(var t=new Map,n=0;n<e.length;n++){var r=e[n],o=r.conversationID;if(t.has(o)){var i=t.get(o);i.lastMessage=r,"in"===r.flow&&i.unreadCount++}else t.set(o,{conversationID:r.conversationID,unreadCount:"out"===r.flow?0:1,type:r.conversationType,subType:r.conversationSubType,lastMessage:r})}return Vn(t.values())}},{key:"reset",value:function(){null!==this.AVChatRoomLoop&&(aa.log("AVChatRoomHandler.reset"),this.stop(),this.AVChatRoomLoop=null,this.key="",this.startSeq=0,this.group={},this.sequencesLinkedList.reset(),this.receivedMessageCount=0)}}]),e}(),k_=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;return wn(this,n),(r=t.call(this,e)).groupMap=new Map,r.groupMemberListMap=new Map,r.groupNoticeHandler=new T_({tim:e,groupController:Fn(r)}),r.groupTipsHandler=new S_({tim:e,groupController:Fn(r)}),r.AVChatRoomHandler=new D_({tim:e,groupController:Fn(r)}),r._initListeners(),r}return bn(n,[{key:"createGroup",value:function(e){var t=this;if(!["Public","Private","ChatRoom","AVChatRoom"].includes(e.type)){var n=new _p({code:Wp,message:sh});return cg(n)}Pa(e.type)&&!da(e.memberList)&&e.memberList.length>0&&(aa.warn("GroupController.createGroup 创建AVChatRoom时不能添加群成员，自动忽略该字段"),e.memberList=void 0),Na(e.type)||da(e.joinOption)||(aa.warn("GroupController.createGroup 创建Private/ChatRoom/AVChatRoom群时不能设置字段：joinOption，自动忽略该字段"),e.joinOption=void 0);var r=new Cg;return r.setMethod(Bg).setStart(),aa.log("GroupController.createGroup."),this.request({name:"group",action:"create",param:e}).then((function(n){if(r.setCode(0).setNetworkType(t.getNetworkType()).setText("groupType=".concat(e.type," groupID=").concat(n.data.groupID)).setEnd(),aa.log("GroupController.createGroup ok. groupID:",n.data.groupID),e.type===fn.GRP_AVCHATROOM)return t.getGroupProfile({groupID:n.data.groupID});t.updateGroupMap([Ln({},e,{groupID:n.data.groupID})]);var o=t.tim.createCustomMessage({to:n.data.groupID,conversationType:fn.CONV_GROUP,payload:{data:"group_create",extension:"".concat(t.tim.context.identifier,"创建群组")}});return t.tim.sendMessage(o),t.emitGroupListUpdate(),t.getGroupProfile({groupID:n.data.groupID})})).then((function(e){var t=e.data.group;return t.selfInfo.messageRemindType=fn.MSG_REMIND_ACPT_AND_NOTE,t.selfInfo.role=fn.GRP_MBR_ROLE_OWNER,e})).catch((function(n){return r.setText("groupType=".concat(e.type)),t.probeNetwork().then((function(e){var t=Hn(e,2),o=t[0],i=t[1];r.setError(n,o,i).setEnd()})),aa.error("GroupController.createGroup error:",n),cg(n)}))}},{key:"joinGroup",value:function(e){if(this.hasLocalGroup(e.groupID)){var t={status:fn.JOIN_STATUS_ALREADY_IN_GROUP};return ug(t)}if(e.type===fn.GRP_PRIVATE){var n=new _p({code:Xp,message:ah});return cg(n)}return aa.log("GroupController.joinGroup. groupID:",e.groupID),this.isLoggedIn()?this.applyJoinGroup(e):this.AVChatRoomHandler.joinWithoutAuth(e)}},{key:"quitGroup",value:function(e){var t=this;aa.log("GroupController.quitGroup. groupID:",e);var n=this.AVChatRoomHandler.checkJoinedAVChatRoomByID(e);if(n&&!this.isLoggedIn())return aa.log("GroupController.quitGroup anonymously ok. groupID:",e),this.deleteLocalGroupAndConversation(e),this.AVChatRoomHandler.reset(),ug({groupID:e});var r=new Cg;return r.setMethod(Vg).setStart(),this.request({name:"group",action:"quitGroup",param:{groupID:e}}).then((function(){return r.setCode(0).setNetworkType(t.getNetworkType()).setText("groupID=".concat(e)).setEnd(),aa.log("GroupController.quitGroup ok. groupID:",e),n&&t.AVChatRoomHandler.reset(),t.deleteLocalGroupAndConversation(e),new ig({groupID:e})})).catch((function(n){return r.setText("groupID=".concat(e)),t.probeNetwork().then((function(e){var t=Hn(e,2),o=t[0],i=t[1];r.setError(n,o,i).setEnd()})),aa.error("GroupController.quitGroup error.  error:".concat(n,". groupID:").concat(e)),cg(n)}))}},{key:"changeGroupOwner",value:function(e){var t=this;if(this.hasLocalGroup(e.groupID)&&this.getLocalGroupProfile(e.groupID).type===fn.GRP_AVCHATROOM)return cg(new _p({code:Jp,message:uh}));if(e.newOwnerID===this.tim.loginInfo.identifier)return cg(new _p({code:Qp,message:ch}));var n=new Cg;return n.setMethod($g).setStart(),aa.log("GroupController.changeGroupOwner. groupID:",e.groupID),this.request({name:"group",action:"changeGroupOwner",param:e}).then((function(){n.setCode(0).setNetworkType(t.getNetworkType()).setText("groupID=".concat(e.groupID)).setEnd(),aa.log("GroupController.changeGroupOwner ok. groupID:",e.groupID);var r=e.groupID,o=e.newOwnerID;t.groupMap.get(r).ownerID=o;var i=t.groupMemberListMap.get(r);if(i instanceof Map){var s=i.get(t.tim.loginInfo.identifier);da(s)||(s.updateRole("Member"),t.groupMap.get(r).selfInfo.role="Member");var a=i.get(o);da(a)||a.updateRole("Owner")}return t.emitGroupListUpdate(!0,!1),new ig({group:t.groupMap.get(r)})})).catch((function(r){return n.setText("groupID=".concat(e.groupID)),t.probeNetwork().then((function(e){var t=Hn(e,2),o=t[0],i=t[1];n.setError(r,o,i).setEnd()})),aa.error("GroupController.changeGroupOwner error:".concat(r,". groupID:").concat(e.groupID)),cg(r)}))}},{key:"dismissGroup",value:function(e){var t=this;if(this.hasLocalGroup(e)&&this.getLocalGroupProfile(e).type===fn.GRP_PRIVATE)return cg(new _p({code:Zp,message:lh}));var n=new Cg;return n.setMethod(Wg).setStart(),aa.log("GroupController.dismissGroup. groupID:".concat(e)),this.request({name:"group",action:"destroyGroup",param:{groupID:e}}).then((function(){return n.setCode(0).setNetworkType(t.getNetworkType()).setText("groupID=".concat(e)).setEnd(),aa.log("GroupController.dismissGroup ok. groupID:".concat(e)),t.deleteLocalGroupAndConversation(e),t.checkJoinedAVChatRoomByID(e)&&t.AVChatRoomHandler.reset(),new ig({groupID:e})})).catch((function(r){return n.setText("groupID=".concat(e)),t.probeNetwork().then((function(e){var t=Hn(e,2),o=t[0],i=t[1];n.setError(r,o,i).setEnd()})),aa.error("GroupController.dismissGroup error:".concat(r,". groupID:").concat(e)),cg(r)}))}},{key:"updateGroupProfile",value:function(e){var t=this;!this.hasLocalGroup(e.groupID)||Na(this.getLocalGroupProfile(e.groupID).type)||da(e.joinOption)||(aa.warn("GroupController.updateGroupProfile Private/ChatRoom/AVChatRoom群不能设置字段：joinOption，自动忽略该字段"),e.joinOption=void 0),da(e.muteAllMembers)||(e.muteAllMembers?e.muteAllMembers="On":e.muteAllMembers="Off");var n=new Cg;return n.setMethod(Xg).setStart(),n.setText(JSON.stringify(e)),aa.log("GroupController.updateGroupProfile. groupID:",e.groupID),this.request({name:"group",action:"updateGroupProfile",param:e}).then((function(){return n.setCode(0).setNetworkType(t.getNetworkType()).setEnd(),aa.log("GroupController.updateGroupProfile ok. groupID:",e.groupID),t.hasLocalGroup(e.groupID)&&(t.groupMap.get(e.groupID).updateGroup(e),t._setStorageGroupList()),new ig({group:t.groupMap.get(e.groupID)})})).catch((function(r){return t.probeNetwork().then((function(e){var t=Hn(e,2),o=t[0],i=t[1];n.setError(r,o,i).setEnd()})),aa.log("GroupController.updateGroupProfile failed. error:".concat(Sa(r)," groupID:").concat(e.groupID)),cg(r)}))}},{key:"setGroupMemberRole",value:function(e){var t=this,n=e.groupID,r=e.userID,o=e.role,i=this.groupMap.get(n);if(i.selfInfo.role!==fn.GRP_MBR_ROLE_OWNER)return cg(new _p({code:of,message:gh}));if([fn.GRP_PRIVATE,fn.GRP_AVCHATROOM].includes(i.type))return cg(new _p({code:sf,message:mh}));if([fn.GRP_MBR_ROLE_ADMIN,fn.GRP_MBR_ROLE_MEMBER].indexOf(o)<0)return cg(new _p({code:af,message:vh}));if(r===this.tim.loginInfo.identifier)return cg(new _p({code:uf,message:yh}));var s=new Cg;return s.setMethod(am).setStart(),s.setText("groupID=".concat(n," userID=").concat(r," role=").concat(o)),aa.log("GroupController.setGroupMemberRole. groupID:".concat(n,". userID: ").concat(r)),this._modifyGroupMemberInfo({groupID:n,userID:r,role:o}).then((function(e){return s.setCode(0).setNetworkType(t.getNetworkType()).setEnd(),aa.log("GroupController.setGroupMemberRole ok. groupID:".concat(n,". userID: ").concat(r)),new ig({group:i,member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Hn(t,2),r=n[0],o=n[1];s.setError(e,r,o).setEnd()})),aa.error("GroupController.setGroupMemberRole error:".concat(e,". groupID:").concat(n,". userID:").concat(r)),cg(e)}))}},{key:"setGroupMemberMuteTime",value:function(e){var t=this,n=e.groupID,r=e.userID,o=e.muteTime;if(r===this.tim.loginInfo.identifier)return cg(new _p({code:cf,message:_h}));aa.log("GroupController.setGroupMemberMuteTime. groupID:".concat(n,". userID: ").concat(r));var i=new Cg;return i.setMethod(im).setStart(),i.setText("groupID=".concat(n," userID=").concat(r," muteTime=").concat(o)),this._modifyGroupMemberInfo({groupID:n,userID:r,muteTime:o}).then((function(e){return i.setCode(0).setNetworkType(t.getNetworkType()).setEnd(),aa.log("GroupController.setGroupMemberMuteTime ok. groupID:".concat(n,". userID: ").concat(r)),new ig({group:t.getLocalGroupProfile(n),member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Hn(t,2),r=n[0],o=n[1];i.setError(e,r,o).setEnd()})),aa.error("GroupController.setGroupMemberMuteTime error:".concat(e,". groupID:").concat(n,". userID:").concat(r)),cg(e)}))}},{key:"setMessageRemindType",value:function(e){var t=this,n=new Cg;n.setMethod(zg).setStart(),n.setText("groupID=".concat(e.groupID," userID=").concat(e.userID||this.tim.loginInfo.identifier)),aa.log("GroupController.setMessageRemindType. groupID:".concat(e.groupID,". userID: ").concat(e.userID||this.tim.loginInfo.identifier));var r=e.groupID,o=e.messageRemindType;return this._modifyGroupMemberInfo({groupID:r,messageRemindType:o,userID:this.tim.loginInfo.identifier}).then((function(){n.setCode(0).setNetworkType(t.getNetworkType()).setEnd(),aa.log("GroupController.setMessageRemindType ok. groupID:".concat(e.groupID,". userID: ").concat(e.userID||t.tim.loginInfo.identifier));var r=t.getLocalGroupProfile(e.groupID);return r&&(r.selfInfo.messageRemindType=o),new ig({group:r})})).catch((function(r){return t.probeNetwork().then((function(e){var t=Hn(e,2),o=t[0],i=t[1];n.setError(r,o,i).setEnd()})),aa.error("GroupController.setMessageRemindType error:".concat(r,". groupID:").concat(e.groupID,". userID:").concat(e.userID||t.tim.loginInfo.identifier)),cg(r)}))}},{key:"setGroupMemberNameCard",value:function(e){var t=this,n=e.groupID,r=e.userID,o=void 0===r?this.tim.loginInfo.identifier:r,i=e.nameCard;aa.log("GroupController.setGroupMemberNameCard. groupID:".concat(n,". userID: ").concat(o));var s=new Cg;return s.setMethod(sm).setStart(),s.setText("groupID=".concat(n," userID=").concat(o," nameCard=").concat(i)),this._modifyGroupMemberInfo({groupID:n,userID:o,nameCard:i}).then((function(e){aa.log("GroupController.setGroupMemberNameCard ok. groupID:".concat(n,". userID: ").concat(o)),s.setCode(0).setNetworkType(t.getNetworkType()).setEnd();var r=t.getLocalGroupProfile(n);return o===t.tim.loginInfo.identifier&&r&&r.setSelfNameCard(i),new ig({group:r,member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Hn(t,2),r=n[0],o=n[1];s.setError(e,r,o).setEnd()})),aa.error("GroupController.setGroupMemberNameCard error:".concat(e,". groupID:").concat(n,". userID:").concat(o)),cg(e)}))}},{key:"setGroupMemberCustomField",value:function(e){var t=this,n=e.groupID,r=e.userID,o=void 0===r?this.tim.loginInfo.identifier:r,i=e.memberCustomField;aa.log("GroupController.setGroupMemberCustomField. groupID:".concat(n,". userID: ").concat(o));var s=new Cg;return s.setMethod(um).setStart(),s.setText("groupID=".concat(n," userID=").concat(o," memberCustomField=").concat(i)),this._modifyGroupMemberInfo({groupID:n,userID:o,memberCustomField:i}).then((function(e){return s.setCode(0).setNetworkType(t.getNetworkType()).setEnd(),aa.log("GroupController.setGroupMemberCustomField ok. groupID:".concat(n,". userID: ").concat(o)),new ig({group:t.groupMap.get(n),member:e})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Hn(t,2),r=n[0],o=n[1];s.setError(e,r,o).setEnd()})),aa.error("GroupController.setGroupMemberCustomField error:".concat(e,". groupID:").concat(n,". userID:").concat(o)),cg(e)}))}},{key:"getGroupList",value:function(e){var t=this,n=new Cg;n.setMethod(Jg).setStart(),aa.log("GroupController.getGroupList");var r={introduction:"Introduction",notification:"Notification",createTime:"CreateTime",ownerID:"Owner_Account",lastInfoTime:"LastInfoTime",memberNum:"MemberNum",maxMemberNum:"MaxMemberNum",joinOption:"ApplyJoinOption",muteAllMembers:"ShutUpAllMember"},o=["Type","Name","FaceUrl","NextMsgSeq","LastMsgTime"];return e&&e.groupProfileFilter&&e.groupProfileFilter.forEach((function(e){r[e]&&o.push(r[e])})),this.request({name:"group",action:"list",param:{responseFilter:{groupBaseInfoFilter:o,selfInfoFilter:["Role","JoinTime","MsgFlag"]}}}).then((function(e){var r=e.data.groups;return n.setCode(0).setNetworkType(t.getNetworkType()).setText(r.length).setEnd(),aa.log("GroupController.getGroupList ok. nums=".concat(r.length)),t._groupListTreeShaking(r),t.updateGroupMap(r),t.tempConversationList&&(aa.log("GroupController.getGroupList update last message with tempConversationList, nums=".concat(t.tempConversationList.length)),t._handleUpdateGroupLastMessage({data:t.tempConversationList}),t.tempConversationList=null),t.emitGroupListUpdate(),new ig({groupList:t.getLocalGroups()})})).catch((function(e){return t.probeNetwork().then((function(t){var r=Hn(t,2),o=r[0],i=r[1];n.setError(e,o,i).setEnd()})),aa.error("GroupController.getGroupList error: ",e),cg(e)}))}},{key:"getGroupMemberList",value:function(e){var t=this,n=e.groupID,r=e.offset,o=void 0===r?0:r,i=e.count,s=void 0===i?15:i,a=new Cg;a.setMethod(tm).setStart(),aa.log("GroupController.getGroupMemberList groupID: ".concat(n," offset: ").concat(o," count: ").concat(s));var u=[];return this.request({name:"group",action:"getGroupMemberList",param:{groupID:n,offset:o,limit:s>100?100:s,memberInfoFilter:["Role","NameCard","ShutUpUntil"]}}).then((function(e){var r=e.data,o=r.members,i=r.memberNum;return ha(o)&&0!==o.length?(t.hasLocalGroup(n)&&(t.getLocalGroupProfile(n).memberNum=i),u=t._updateLocalGroupMemberMap(n,o),t.tim.getUserProfile({userIDList:o.map((function(e){return e.userID})),tagList:[tp.NICK,tp.AVATAR]})):Promise.resolve([])})).then((function(e){var r=e.data;if(!ha(r)||0===r.length)return ug({memberList:[]});var i=r.map((function(e){return{userID:e.userID,nick:e.nick,avatar:e.avatar}}));return t._updateLocalGroupMemberMap(n,i),a.setCode(0).setNetworkType(t.getNetworkType()).setText("groupID=".concat(n," offset=").concat(o," count=").concat(s)).setEnd(),aa.log("GroupController.getGroupMemberList ok."),new ig({memberList:u})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Hn(t,2),r=n[0],o=n[1];a.setError(e,r,o).setEnd()})),aa.error("GroupController.getGroupMemberList error: ",e),cg(e)}))}},{key:"getLocalGroups",value:function(){return Vn(this.groupMap.values())}},{key:"getLocalGroupProfile",value:function(e){return this.groupMap.get(e)}},{key:"hasLocalGroup",value:function(e){return this.groupMap.has(e)}},{key:"getLocalGroupMemberInfo",value:function(e,t){return this.groupMemberListMap.has(e)?this.groupMemberListMap.get(e).get(t):null}},{key:"setLocalGroupMember",value:function(e,t){if(this.groupMemberListMap.has(e))this.groupMemberListMap.get(e).set(t.userID,t);else{var n=(new Map).set(t.userID,t);this.groupMemberListMap.set(e,n)}}},{key:"hasLocalGroupMember",value:function(e,t){return this.groupMemberListMap.has(e)&&this.groupMemberListMap.get(e).has(t)}},{key:"hasLocalGroupMemberMap",value:function(e){return this.groupMemberListMap.has(e)}},{key:"getGroupProfile",value:function(e){var t=this,n=new Cg;n.setMethod(Qg).setStart(),aa.log("GroupController.getGroupProfile. groupID:",e.groupID);var r=e.groupID,o=e.groupCustomFieldFilter,i={groupIDList:[r],responseFilter:{groupBaseInfoFilter:["Type","Name","Introduction","Notification","FaceUrl","Owner_Account","CreateTime","InfoSeq","LastInfoTime","LastMsgTime","MemberNum","MaxMemberNum","ApplyJoinOption","NextMsgSeq","ShutUpAllMember"],groupCustomFieldFilter:o}};return this.getGroupProfileAdvance(i).then((function(o){var i,s=o.data,a=s.successGroupList,u=s.failureGroupList;return aa.log("GroupController.getGroupProfile ok. groupID:",e.groupID),u.length>0?cg(u[0]):(Pa(a[0].type)&&!t.hasLocalGroup(r)?i=new t_(a[0]):(t.updateGroupMap(a),i=t.getLocalGroupProfile(r)),n.setCode(0).setNetworkType(t.getNetworkType()).setText("groupID=".concat(i.groupID," type=").concat(i.type," muteAllMembers=").concat(i.muteAllMembers," ownerID=").concat(i.ownerID)).setEnd(),i&&i.selfInfo&&!i.selfInfo.nameCard?t.updateSelfInfo(i).then((function(e){return new ig({group:e})})):new ig({group:i}))})).catch((function(r){return t.probeNetwork().then((function(t){var o=Hn(t,2),i=o[0],s=o[1];n.setError(r,i,s).setText("groupID=".concat(e.groupID)).setEnd()})),aa.error("GroupController.getGroupProfile error:".concat(Sa(r),". groupID:").concat(e.groupID)),cg(r)}))}},{key:"getGroupMemberProfile",value:function(e){var t=this,n=new Cg;n.setMethod(nm).setText(e.userIDList.length>5?"userIDList.length=".concat(e.userIDList.length):"userIDList=".concat(e.userIDList)).setStart(),aa.log("GroupController.getGroupMemberProfile groupID:".concat(e.groupID," userIDList:").concat(e.userIDList.join(","))),e.userIDList.length>50&&(e.userIDList=e.userIDList.slice(0,50));var r=e.groupID,o=e.userIDList;return this._getGroupMemberProfileAdvance(Ln({},e,{userIDList:o})).then((function(e){var n=e.data.members;return ha(n)&&0!==n.length?(t._updateLocalGroupMemberMap(r,n),t.tim.getUserProfile({userIDList:n.map((function(e){return e.userID})),tagList:[tp.NICK,tp.AVATAR]})):ug([])})).then((function(e){var i=e.data.map((function(e){return{userID:e.userID,nick:e.nick,avatar:e.avatar}}));t._updateLocalGroupMemberMap(r,i);var s=o.filter((function(e){return t.hasLocalGroupMember(r,e)})).map((function(e){return t.getLocalGroupMemberInfo(r,e)}));return n.setCode(0).setNetworkType(t.getNetworkType()).setEnd(),new ig({memberList:s})}))}},{key:"_getGroupMemberProfileAdvance",value:function(e){return this.request({name:"group",action:"getGroupMemberProfile",param:Ln({},e,{memberInfoFilter:e.memberInfoFilter?e.memberInfoFilter:["Role","JoinTime","NameCard","ShutUpUntil"]})})}},{key:"updateSelfInfo",value:function(e){var t=e.groupID;aa.log("GroupController.updateSelfInfo groupID:",t);var n={groupID:t,userIDList:[this.tim.loginInfo.identifier]};return this.getGroupMemberProfile(n).then((function(n){var r=n.data.memberList;return aa.log("GroupController.updateSelfInfo ok. groupID:",t),e&&0!==r.length&&e.updateSelfInfo(r[0]),e}))}},{key:"addGroupMember",value:function(e){var t=this,n=new Cg;n.setMethod(rm).setText("groupID=".concat(e.groupID)).setStart();var r=this.getLocalGroupProfile(e.groupID);if(Pa(r.type)){var o=new _p({code:tf,message:fh});return n.setCode(tf).setMessage(fh).setNetworkType(this.getNetworkType()).setText("groupID=".concat(e.groupID," groupType=").concat(r.type)).setEnd(),cg(o)}return e.userIDList=e.userIDList.map((function(e){return{userID:e}})),aa.log("GroupController.addGroupMember. groupID:",e.groupID),this.request({name:"group",action:"addGroupMember",param:e}).then((function(o){var i=o.data.members;n.setCode(0).setNetworkType(t.getNetworkType()).setText("groupID=".concat(e.groupID)).setEnd(),aa.log("GroupController.addGroupMember ok. groupID:",e.groupID);var s=i.filter((function(e){return 1===e.result})).map((function(e){return e.userID})),a=i.filter((function(e){return 0===e.result})).map((function(e){return e.userID})),u=i.filter((function(e){return 2===e.result})).map((function(e){return e.userID}));return 0===s.length?new ig({successUserIDList:s,failureUserIDList:a,existedUserIDList:u}):(r.memberNum+=s.length,new ig({successUserIDList:s,failureUserIDList:a,existedUserIDList:u,group:r}))})).catch((function(r){return t.probeNetwork().then((function(t){var o=Hn(t,2),i=o[0],s=o[1];n.setError(r,i,s).setText("groupID=".concat(e.groupID)).setEnd()})),aa.error("GroupController.addGroupMember error:".concat(r,", groupID:").concat(e.groupID)),cg(r)}))}},{key:"deleteGroupMember",value:function(e){var t=this,n=new Cg;n.setMethod(om).setText(e.userIDList.length>5?"userIDList.length=".concat(e.userIDList.length):"userIDList=".concat(e.userIDList)).setStart(),aa.log("GroupController.deleteGroupMember groupID:".concat(e.groupID," userIDList:").concat(e.userIDList));var r=this.getLocalGroupProfile(e.groupID);return r.type===fn.GRP_AVCHATROOM?cg(new _p({code:rf,message:dh})):this.request({name:"group",action:"deleteGroupMember",param:e}).then((function(){return n.setCode(0).setNetworkType(t.getNetworkType()).setEnd(),aa.log("GroupController.deleteGroupMember ok"),r.memberNum--,t.deleteLocalGroupMembers(e.groupID,e.userIDList),new ig({group:r,userIDList:e.userIDList})})).catch((function(r){return t.probeNetwork().then((function(t){var o=Hn(t,2),i=o[0],s=o[1];n.setError(r,i,s).setText("groupID=".concat(e.groupID)).setEnd()})),aa.error("GroupController.deleteGroupMember error:".concat(r.code,", groupID:").concat(e.groupID)),cg(r)}))}},{key:"searchGroupByID",value:function(e){var t=this,n={groupIDList:[e]},r=new Cg;return r.setMethod(Kg).setText("groupID=".concat(e)).setStart(),aa.log("GroupController.searchGroupByID. groupID:".concat(e)),this.request({name:"group",action:"searchGroupByID",param:n}).then((function(n){var o=n.data.groupProfile;if(o[0].errorCode!==Ru.SUCCESS)throw new _p({code:o[0].errorCode,message:o[0].errorInfo});return r.setCode(0).setNetworkType(t.getNetworkType()).setEnd(),aa.log("GroupController.searchGroupByID ok. groupID:".concat(e)),new ig({group:new t_(o[0])})})).catch((function(n){return t.probeNetwork().then((function(e){var t=Hn(e,2),o=t[0],i=t[1];r.setError(n,o,i).setEnd()})),aa.warn("GroupController.searchGroupByID error:".concat(Sa(n),", groupID:").concat(e)),cg(n)}))}},{key:"applyJoinGroup",value:function(e){var t=this,n=new Cg;return n.setMethod(Hg).setStart(),this.request({name:"group",action:"applyJoinGroup",param:e}).then((function(r){var o=r.data,i=o.joinedStatus,s=o.longPollingKey;switch(n.setCode(0).setNetworkType(t.getNetworkType()).setText("groupID=".concat(e.groupID," joinedStatus=").concat(i)).setEnd(),aa.log("GroupController.joinGroup ok. groupID:",e.groupID),i){case bu.WAIT_APPROVAL:return new ig({status:bu.WAIT_APPROVAL});case bu.SUCCESS:return t.getGroupProfile({groupID:e.groupID}).then((function(n){var r=n.data.group,o={status:bu.SUCCESS,group:r};return da(s)?new ig(o):(t.emitInnerEvent(Fd,e.groupID),t.AVChatRoomHandler.startRunLoop({longPollingKey:s,group:r}))}));default:var a=new _p({code:ef,message:ph});return aa.error("GroupController.joinGroup error:".concat(a,". groupID:").concat(e.groupID)),cg(a)}})).catch((function(r){return n.setText("groupID=".concat(e.groupID)),t.probeNetwork().then((function(e){var t=Hn(e,2),o=t[0],i=t[1];n.setError(r,o,i).setEnd()})),aa.error("GroupController.joinGroup error:".concat(r,". groupID:").concat(e.groupID)),cg(r)}))}},{key:"applyJoinAVChatRoom",value:function(e){return this.AVChatRoomHandler.applyJoinAVChatRoom(e)}},{key:"handleGroupApplication",value:function(e){var t=this,n=e.message.payload,r=n.groupProfile.groupID,o=n.authentication,i=n.messageKey,s=n.operatorID,a=new Cg;return a.setMethod(Yg).setText("groupID=".concat(r)).setStart(),aa.log("GroupController.handleApplication. groupID:",r),this.request({name:"group",action:"handleApplyJoinGroup",param:Ln({},e,{applicant:s,groupID:r,authentication:o,messageKey:i})}).then((function(){return a.setCode(0).setNetworkType(t.getNetworkType()).setEnd(),aa.log("GroupController.handleApplication ok. groupID:",r),t.deleteGroupSystemNotice({messageList:[e.message]}),new ig({group:t.getLocalGroupProfile(r)})})).catch((function(e){return t.probeNetwork().then((function(t){var n=Hn(t,2),r=n[0],o=n[1];a.setError(e,r,o).setEnd()})),aa.error("GroupController.handleApplication error.  error:".concat(e,". groupID:").concat(r)),cg(e)}))}},{key:"deleteGroupSystemNotice",value:function(e){var t=this;return ha(e.messageList)&&0!==e.messageList.length?(aa.log("GroupController.deleteGroupSystemNotice "+e.messageList.map((function(e){return e.ID}))),this.request({name:"group",action:"deleteGroupSystemNotice",param:{messageListToDelete:e.messageList.map((function(e){return{from:fn.CONV_SYSTEM,messageSeq:e.clientSequence,messageRandom:e.random}}))}}).then((function(){return aa.log("GroupController.deleteGroupSystemNotice ok"),e.messageList.forEach((function(e){t.tim.messageController.deleteLocalMessage(e)})),new ig})).catch((function(e){return aa.error("GroupController.deleteGroupSystemNotice error:",e),cg(e)}))):ug()}},{key:"getGroupProfileAdvance",value:function(e){return ha(e.groupIDList)&&e.groupIDList.length>50&&(aa.warn("GroupController.getGroupProfileAdvance 获取群资料的数量不能超过50个"),e.groupIDList.length=50),aa.log("GroupController.getGroupProfileAdvance. groupIDList:",e.groupIDList),this.request({name:"group",action:"query",param:e}).then((function(e){aa.log("GroupController.getGroupProfileAdvance ok.");var t=e.data.groups,n=t.filter((function(e){return da(e.errorCode)||e.errorCode===Ru.SUCCESS})),r=t.filter((function(e){return e.errorCode&&e.errorCode!==Ru.SUCCESS})).map((function(e){return new _p({code:Number("500".concat(e.errorCode)),message:e.errorInfo,data:{groupID:e.groupID}})}));return new ig({successGroupList:n,failureGroupList:r})})).catch((function(t){return aa.error("GroupController.getGroupProfileAdvance error:".concat(Sa(t),". groupIDList:").concat(e.groupIDList)),cg(t)}))}},{key:"_deleteLocalGroup",value:function(e){return this.groupMap.delete(e),this.groupMemberListMap.delete(e),this._setStorageGroupList(),this.groupMap.has(e)&&this.groupMemberListMap.has(e)}},{key:"_initGroupList",value:function(){var e=this,t=new Cg;t.setMethod(Zg).setStart(),aa.time(yg),aa.log("GroupController._initGroupList");var n=this._getStorageGroupList();ha(n)&&n.length>0?(n.forEach((function(t){e.groupMap.set(t.groupID,new t_(t))})),this.emitGroupListUpdate(!0,!1),t.setCode(0).setNetworkType(this.getNetworkType()).setText(this.groupMap.size).setEnd()):t.setCode(0).setNetworkType(this.getNetworkType()).setText(0).setEnd(),this.triggerReady(),aa.log("GroupController._initGroupList ok. initCost=".concat(aa.timeEnd(yg),"ms")),this.getGroupList()}},{key:"_initListeners",value:function(){var e=this.tim.innerEmitter;e.once(td,this._initGroupList,this),e.on(Gd,this._handleUpdateGroupLastMessage,this),e.on(cd,this._handleReceivedGroupMessage,this),e.on(qd,this._handleProfileUpdated,this)}},{key:"emitGroupListUpdate",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=this.getLocalGroups();t&&this.emitInnerEvent(bd,n),e&&this.emitOuterEvent(pn.GROUP_LIST_UPDATED,n)}},{key:"_handleReceivedGroupMessage",value:function(e){var t=this,n=e.data.eventDataList;Array.isArray(n)&&n.forEach((function(e){var n=e.conversationID.replace(fn.CONV_GROUP,"");t.groupMap.has(n)&&(t.groupMap.get(n).nextMessageSeq=e.lastMessage.sequence+1)}))}},{key:"_onReceivedGroupSystemNotice",value:function(e){var t=e.data;this.groupNoticeHandler._onReceivedGroupNotice(t)}},{key:"_handleUpdateGroupLastMessage",value:function(e){var t=e.data;if(aa.log("GroupController._handleUpdateGroupLastMessage convNums=".concat(t.length," groupNums=").concat(this.groupMap.size)),0!==this.groupMap.size){for(var n,r,o,i=!1,s=0,a=t.length;s<a;s++)(n=t[s]).conversationID&&n.type!==fn.CONV_GROUP&&(r=n.conversationID.split(/^GROUP/)[1],(o=this.getLocalGroupProfile(r))&&(o.lastMessage=n.lastMessage,i=!0));i&&(this.groupMap=this._sortLocalGroupList(this.groupMap),this.emitGroupListUpdate(!0,!1))}else this.tempConversationList=t}},{key:"_sortLocalGroupList",value:function(e){var t=Vn(e).filter((function(e){var t=Hn(e,2);return t[0],!qa(t[1].lastMessage)}));return t.sort((function(e,t){return t[1].lastMessage.lastTime-e[1].lastMessage.lastTime})),new Map([].concat(Vn(t),Vn(e)))}},{key:"_getStorageGroupList",value:function(){return this.tim.storage.getItem("groupMap")}},{key:"_setStorageGroupList",value:function(){var e=this.getLocalGroups().filter((function(e){var t=e.type;return!Pa(t)})).slice(0,20).map((function(e){return{groupID:e.groupID,name:e.name,avatar:e.avatar,type:e.type}}));this.tim.storage.setItem("groupMap",e)}},{key:"updateGroupMap",value:function(e){var t=this;e.forEach((function(e){t.groupMap.has(e.groupID)?t.groupMap.get(e.groupID).updateGroup(e):t.groupMap.set(e.groupID,new t_(e))})),this._setStorageGroupList()}},{key:"_updateLocalGroupMemberMap",value:function(e,t){var n=this;return ha(t)&&0!==t.length?t.map((function(t){return n.hasLocalGroupMember(e,t.userID)?n.getLocalGroupMemberInfo(e,t.userID).updateMember(t):n.setLocalGroupMember(e,new M_(t)),n.getLocalGroupMemberInfo(e,t.userID)})):[]}},{key:"deleteLocalGroupMembers",value:function(e,t){var n=this.groupMemberListMap.get(e);n&&t.forEach((function(e){n.delete(e)}))}},{key:"_modifyGroupMemberInfo",value:function(e){var t=this,n=e.groupID,r=e.userID;return this.request({name:"group",action:"modifyGroupMemberInfo",param:e}).then((function(){if(t.hasLocalGroupMember(n,r)){var o=t.getLocalGroupMemberInfo(n,r);return da(e.muteTime)||o.updateMuteUntil(e.muteTime),da(e.role)||o.updateRole(e.role),da(e.nameCard)||o.updateNameCard(e.nameCard),da(e.memberCustomField)||o.updateMemberCustomField(e.memberCustomField),o}return t.getGroupMemberProfile({groupID:n,userIDList:[r]}).then((function(e){return Hn(e.data.memberList,1)[0]}))}))}},{key:"_groupListTreeShaking",value:function(e){for(var t=new Map(Vn(this.groupMap)),n=0,r=e.length;n<r;n++)t.delete(e[n].groupID);this.AVChatRoomHandler.hasJoinedAVChatRoom()&&t.delete(this.AVChatRoomHandler.group.groupID);for(var o=Vn(t.keys()),i=0,s=o.length;i<s;i++)this.groupMap.delete(o[i])}},{key:"_handleProfileUpdated",value:function(e){for(var t=this,n=e.data,r=function(e){var r=n[e];t.groupMemberListMap.forEach((function(e){e.has(r.userID)&&e.get(r.userID).updateMember({nick:r.nick,avatar:r.avatar})}))},o=0;o<n.length;o++)r(o)}},{key:"getJoinedAVChatRoom",value:function(){return this.AVChatRoomHandler.getJoinedAVChatRoom()}},{key:"deleteLocalGroupAndConversation",value:function(e){this._deleteLocalGroup(e),this.tim.conversationController.deleteLocalConversation("GROUP".concat(e)),this.emitGroupListUpdate(!0,!1)}},{key:"checkJoinedAVChatRoomByID",value:function(e){return this.AVChatRoomHandler.checkJoinedAVChatRoomByID(e)}},{key:"getGroupLastSequence",value:function(e){var t=this,n=new Cg;n.setMethod(em).setStart();var r=0;if(this.hasLocalGroup(e)){var o=this.getLocalGroupProfile(e);if(o.lastMessage.lastSequence>0)return r=o.lastMessage.lastSequence,aa.log("GroupController.getGroupLastSequence got lastSequence=".concat(r," from local group profile[lastMessage.lastSequence]. groupID=").concat(e)),n.setCode(0).setNetworkType(this.getNetworkType()).setText("got lastSequence=".concat(r," from local group profile[lastMessage.lastSequence]. groupID=").concat(e)).setEnd(),Promise.resolve(r);if(o.nextMessageSeq>1)return r=o.nextMessageSeq-1,aa.log("GroupController.getGroupLastSequence got lastSequence=".concat(r," from local group profile[nextMessageSeq]. groupID=").concat(e)),n.setCode(0).setNetworkType(this.getNetworkType()).setText("got lastSequence=".concat(r," from local group profile[nextMessageSeq]. groupID=").concat(e)).setEnd(),Promise.resolve(r)}var i="GROUP".concat(e),s=this.tim.conversationController.getLocalConversation(i);if(s&&s.lastMessage.lastSequence)return r=s.lastMessage.lastSequence,aa.log("GroupController.getGroupLastSequence got lastSequence=".concat(r," from local conversation profile[lastMessage.lastSequence]. groupID=").concat(e)),n.setCode(0).setNetworkType(this.getNetworkType()).setText("got lastSequence=".concat(r," from local conversation profile[lastMessage.lastSequence]. groupID=").concat(e)).setEnd(),Promise.resolve(r);var a={groupIDList:[e],responseFilter:{groupBaseInfoFilter:["NextMsgSeq"]}};return this.getGroupProfileAdvance(a).then((function(o){var i=o.data.successGroupList;return qa(i)?aa.log("GroupController.getGroupLastSequence successGroupList is empty. groupID=".concat(e)):(r=i[0].nextMessageSeq-1,aa.log("GroupController.getGroupLastSequence got lastSequence=".concat(r," from getGroupProfileAdvance. groupID=").concat(e))),n.setCode(0).setNetworkType(t.getNetworkType()).setText("got lastSequence=".concat(r," from getGroupProfileAdvance. groupID=").concat(e)).setEnd(),r})).catch((function(r){return t.probeNetwork().then((function(t){var o=Hn(t,2),i=o[0],s=o[1];n.setError(r,i,s).setText("get lastSequence failed from getGroupProfileAdvance. groupID=".concat(e)).setEnd()})),aa.warn("GroupController.getGroupLastSequence failed. ".concat(r)),cg(r)}))}},{key:"reset",value:function(){this.groupMap.clear(),this.groupMemberListMap.clear(),this.resetReady(),this.groupNoticeHandler.reset(),this.AVChatRoomHandler.reset(),this.tim.innerEmitter.once(td,this._initGroupList,this)}}]),n}(ng),w_=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;wn(this,n),(r=t.call(this,e)).REALTIME_MESSAGE_TIMEOUT=11e4,r.LONGPOLLING_ID_TIMEOUT=3e5,r._currentState=fn.NET_STATE_CONNECTED,r._status={OPENIM:{lastResponseReceivedTime:0,jitterCount:0,failedCount:0},AVCHATROOM:{lastResponseReceivedTime:0,jitterCount:0,failedCount:0}};var o=r.tim.innerEmitter;return o.on(fd,r._onGetLongPollIDFailed,Fn(r)),o.on(dd,r._onOpenIMResponseOK,Fn(r)),o.on(hd,r._onOpenIMRequestFailed,Fn(r)),o.on(Ad,r._onAVChatroomResponseOK,Fn(r)),o.on(wd,r._onAVChatroomRequestFailed,Fn(r)),r}return bn(n,[{key:"_onGetLongPollIDFailed",value:function(){this._currentState!==fn.NET_STATE_DISCONNECTED&&this._emitNetStateChangeEvent(fn.NET_STATE_DISCONNECTED)}},{key:"_onOpenIMResponseOK",value:function(){this._onResponseOK("OPENIM")}},{key:"_onOpenIMRequestFailed",value:function(){this._onRequestFailed("OPENIM")}},{key:"_onAVChatroomResponseOK",value:function(){this.isLoggedIn()||this._onResponseOK("AVCHATROOM")}},{key:"_onAVChatroomRequestFailed",value:function(){this.isLoggedIn()||this._onRequestFailed("AVCHATROOM")}},{key:"_onResponseOK",value:function(e){var t=this._status[e],n=Date.now();if(0!==t.lastResponseReceivedTime){var r=n-t.lastResponseReceivedTime;if(aa.debug("StatusController._onResponseOK key=".concat(e," currentState=").concat(this._currentState," interval=").concat(r," failedCount=").concat(t.failedCount," jitterCount=").concat(t.jitterCount)),t.failedCount>0&&(t.failedCount=0,t.jitterCount+=1,this._currentState!==fn.NET_STATE_CONNECTED&&this._emitNetStateChangeEvent(fn.NET_STATE_CONNECTED)),r<=this.REALTIME_MESSAGE_TIMEOUT){if(t.jitterCount>=3){var o=new Cg;o.setMethod(pm).setStart(),o.setCode(0).setText("".concat(e,"-").concat(r,"-").concat(t.jitterCount)).setNetworkType(this.getNetworkType()).setEnd(),t.jitterCount=0}}else if(r>=this.REALTIME_MESSAGE_TIMEOUT&&r<this.LONGPOLLING_ID_TIMEOUT){var i=new Cg;i.setMethod(fm).setStart(),i.setCode(0).setText("".concat(e,"-").concat(r)).setNetworkType(this.getNetworkType()).setEnd(),aa.warn("StatusController._onResponseOK, fast start. key=".concat(e," interval=").concat(r," ms")),this.emitInnerEvent(gd)}else if(r>=this.LONGPOLLING_ID_TIMEOUT){var s=new Cg;s.setMethod(hm).setStart(),s.setCode(0).setText("".concat(e,"-").concat(r)).setNetworkType(this.getNetworkType()).setEnd(),aa.warn("StatusController._onResponseOK, slow start. key=".concat(e," interval=").concat(r," ms")),this.emitInnerEvent(md)}t.lastResponseReceivedTime=n}else t.lastResponseReceivedTime=n}},{key:"_onRequestFailed",value:function(e){var t=this,n=this._status[e];Date.now()-n.lastResponseReceivedTime>=this.LONGPOLLING_ID_TIMEOUT?this._currentState!==fn.NET_STATE_DISCONNECTED&&(aa.warn("StatusController._onRequestFailed, disconnected, longpolling unavailable more than 5min. key=".concat(e," networkType=").concat(this.getNetworkType())),this._emitNetStateChangeEvent(fn.NET_STATE_DISCONNECTED)):(n.failedCount+=1,n.failedCount>5?this.probeNetwork().then((function(r){var o=Hn(r,2),i=o[0],s=o[1];i?(t._currentState!==fn.NET_STATE_CONNECTING&&t._emitNetStateChangeEvent(fn.NET_STATE_CONNECTING),aa.warn("StatusController._onRequestFailed, connecting, network jitter. key=".concat(e," networkType=").concat(s))):(t._currentState!==fn.NET_STATE_DISCONNECTED&&t._emitNetStateChangeEvent(fn.NET_STATE_DISCONNECTED),aa.warn("StatusController._onRequestFailed, disconnected, longpolling unavailable. key=".concat(e," networkType=").concat(s))),n.failedCount=0,n.jitterCount=0})):this._currentState===fn.NET_STATE_CONNECTED&&this._emitNetStateChangeEvent(fn.NET_STATE_CONNECTING))}},{key:"_emitNetStateChangeEvent",value:function(e){aa.log("StatusController._emitNetStateChangeEvent net state changed from ".concat(this._currentState," to ").concat(e)),this._currentState=e,this.emitOuterEvent(pn.NET_STATE_CHANGE,{state:e})}},{key:"reset",value:function(){aa.log("StatusController.reset"),this._currentState=fn.NET_STATE_CONNECTED,this._status={OPENIM:{lastResponseReceivedTime:0,jitterCount:0,failedCount:0},AVCHATROOM:{lastResponseReceivedTime:0,jitterCount:0,failedCount:0}}}}]),n}(ng);function A_(){return null}var b_=function(){function e(t){wn(this,e),this.tim=t,this.isWX=Os,this.storageQueue=new Map,this.checkTimes=0,this.checkTimer=setInterval(this._onCheckTimer.bind(this),1e3),this._errorTolerantHandle()}return bn(e,[{key:"_errorTolerantHandle",value:function(){!this.isWX&&da(window.localStorage)&&(this.getItem=A_,this.setItem=A_,this.removeItem=A_,this.clear=A_)}},{key:"_onCheckTimer",value:function(){if(this.checkTimes++,this.checkTimes%20==0){if(0===this.storageQueue.size)return;this._doFlush()}}},{key:"_doFlush",value:function(){try{var e,t=Yn(this.storageQueue);try{for(t.s();!(e=t.n()).done;){var n=Hn(e.value,2),r=n[0],o=n[1];this.isWX?wx.setStorageSync(this._getKey(r),o):localStorage.setItem(this._getKey(r),JSON.stringify(o))}}catch(s){t.e(s)}finally{t.f()}this.storageQueue.clear()}catch(dC){aa.warn("Storage._doFlush error",dC)}}},{key:"_getPrefix",value:function(){var e=this.tim.loginInfo,t=e.SDKAppID,n=e.identifier;return"TIM_".concat(t,"_").concat(n,"_")}},{key:"getItem",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];try{var n=t?this._getKey(e):e;return this.isWX?wx.getStorageSync(n):JSON.parse(localStorage.getItem(n))}catch(dC){aa.warn("Storage.getItem error:",dC)}}},{key:"setItem",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(n){var o=r?this._getKey(e):e;this.isWX?wx.setStorageSync(o,t):localStorage.setItem(o,JSON.stringify(t))}else this.storageQueue.set(e,t)}},{key:"clear",value:function(){try{this.isWX?wx.clearStorageSync():localStorage.clear()}catch(dC){aa.warn("Storage.clear error:",dC)}}},{key:"removeItem",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];try{var n=t?this._getKey(e):e;this.isWX?wx.removeStorageSync(n):localStorage.removeItem(n)}catch(dC){aa.warn("Storage.removeItem error:",dC)}}},{key:"getSize",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"b";try{var r={size:0,limitSize:5242880,unit:n};if(Object.defineProperty(r,"leftSize",{enumerable:!0,get:function(){return r.limitSize-r.size}}),this.isWX&&(r.limitSize=1024*wx.getStorageInfoSync().limitSize),e)r.size=JSON.stringify(this.getItem(e)).length+this._getKey(e).length;else if(this.isWX){var o=wx.getStorageInfoSync(),i=o.keys;i.forEach((function(e){r.size+=JSON.stringify(wx.getStorageSync(e)).length+t._getKey(e).length}))}else for(var s in localStorage)localStorage.hasOwnProperty(s)&&(r.size+=localStorage.getItem(s).length+s.length);return this._convertUnit(r)}catch(dC){aa.warn("Storage.getSize error:",dC)}}},{key:"_convertUnit",value:function(e){var t={},n=e.unit;for(var r in t.unit=n,e)"number"==typeof e[r]&&("kb"===n.toLowerCase()?t[r]=Math.round(e[r]/1024):"mb"===n.toLowerCase()?t[r]=Math.round(e[r]/1024/1024):t[r]=e[r]);return t}},{key:"_getKey",value:function(e){return"".concat(this._getPrefix()).concat(e)}},{key:"reset",value:function(){this._doFlush(),this.checkTimes=0}}]),e}(),R_=n((function(e){var t=Object.prototype.hasOwnProperty,n="~";function r(){}function o(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function i(e,t,r,i,s){if("function"!=typeof r)throw new TypeError("The listener must be a function");var a=new o(r,i||e,s),u=n?n+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],a]:e._events[u].push(a):(e._events[u]=a,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new r:delete e._events[t]}function a(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),a.prototype.eventNames=function(){var e,r,o=[];if(0===this._eventsCount)return o;for(r in e=this._events)t.call(e,r)&&o.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},a.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var o=0,i=r.length,s=new Array(i);o<i;o++)s[o]=r[o].fn;return s},a.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},a.prototype.emit=function(e,t,r,o,i,s){var a=n?n+e:e;if(!this._events[a])return!1;var u,c,l=this._events[a],p=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),p){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,r),!0;case 4:return l.fn.call(l.context,t,r,o),!0;case 5:return l.fn.call(l.context,t,r,o,i),!0;case 6:return l.fn.call(l.context,t,r,o,i,s),!0}for(c=1,u=new Array(p-1);c<p;c++)u[c-1]=arguments[c];l.fn.apply(l.context,u)}else{var f,h=l.length;for(c=0;c<h;c++)switch(l[c].once&&this.removeListener(e,l[c].fn,void 0,!0),p){case 1:l[c].fn.call(l[c].context);break;case 2:l[c].fn.call(l[c].context,t);break;case 3:l[c].fn.call(l[c].context,t,r);break;case 4:l[c].fn.call(l[c].context,t,r,o);break;default:if(!u)for(f=1,u=new Array(p-1);f<p;f++)u[f-1]=arguments[f];l[c].fn.apply(l[c].context,u)}}return!0},a.prototype.on=function(e,t,n){return i(this,e,t,n,!1)},a.prototype.once=function(e,t,n){return i(this,e,t,n,!0)},a.prototype.removeListener=function(e,t,r,o){var i=n?n+e:e;if(!this._events[i])return this;if(!t)return s(this,i),this;var a=this._events[i];if(a.fn)a.fn!==t||o&&!a.once||r&&a.context!==r||s(this,i);else{for(var u=0,c=[],l=a.length;u<l;u++)(a[u].fn!==t||o&&!a[u].once||r&&a[u].context!==r)&&c.push(a[u]);c.length?this._events[i]=1===c.length?c[0]:c:s(this,i)}return this},a.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&s(this,t)):(this._events=new r,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=n,a.EventEmitter=a,e.exports=a})),O_=function(e){var t,n,r,o,i;return qa(e.context)?(t="",n=0,r=0,o=0,i=1):(t=e.context.a2Key,n=e.context.tinyID,r=e.context.SDKAppID,o=e.context.contentType,i=e.context.apn),{platform:zh,websdkappid:Yh,v:$h,a2:t,tinyid:n,sdkappid:r,contentType:o,apn:i,reqtime:function(){return+new Date}}},L_=function(){function e(t){wn(this,e),this.tim=t,this.tim.innerEmitter.on(Zh,this._update,this),this.tim.innerEmitter.on(ed,this._update,this),this.tim.innerEmitter.on(nd,this._updateSpecifiedConfig,this),this._initConfig()}return bn(e,[{key:"_update",value:function(e){this._initConfig()}},{key:"_updateSpecifiedConfig",value:function(e){var t=this;e.data.forEach((function(e){t._set(e)}))}},{key:"get",value:function(e){var t=e.name,n=e.action,r=e.param,o=e.tjgID;if(da(this.config[t])||da(this.config[t][n]))throw new _p({code:Cf,message:"".concat(wh,": PackageConfig.").concat(t)});var i=function e(t){if(0===Object.getOwnPropertyNames(t).length)return Object.create(null);var n=Array.isArray(t)?[]:Object.create(null),r="";for(var o in t)null!==t[o]?void 0!==t[o]?(r=kn(t[o]),["string","number","function","boolean"].indexOf(r)>=0?n[o]=t[o]:n[o]=e(t[o])):n[o]=void 0:n[o]=null;return n}(this.config[t][n]);return i.requestData=this._initRequestData(r,i),i.encode=this._initEncoder(i),i.decode=this._initDecoder(i),o&&(i.queryString.tjg_id=o),i}},{key:"_set",value:function(e){var t=e.key,n=e.value;if(0!=!!t){var r=t.split(".");r.length<=0||function e(t,n,r,o){var i=n[r];"object"===kn(t[i])?e(t[i],n,r+1,o):t[i]=o}(this.config,r,0,n)}}},{key:"_initConfig",value:function(){var e;this.config={},this.config.accessLayer=(e=this.tim,{create:null,query:{serverName:Jh.NAME.WEB_IM,cmd:Jh.CMD.ACCESS_LAYER,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:{platform:zh,identifier:e.context.identifier,usersig:e.context.userSig,contentType:e.context.contentType,apn:null!==e.context?e.context.apn:1,websdkappid:Yh,v:$h},requestData:{}},update:null,delete:null}),this.config.login=function(e){return{create:null,query:{serverName:Jh.NAME.OPEN_IM,cmd:Jh.CMD.LOGIN,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:{websdkappid:Yh,v:$h,platform:zh,identifier:e.loginInfo.identifier,usersig:e.loginInfo.userSig,sdkappid:e.loginInfo.SDKAppID,accounttype:e.loginInfo.accountType,contentType:null!==e.context?e.context.contentType:0,apn:null!==e.context?e.context.apn:1,reqtime:+new Date/1e3},requestData:{state:"Online"},keyMaps:{request:{tinyID:"tinyId"},response:{TinyId:"tinyID"}}},update:null,delete:null}}(this.tim),this.config.logout=function(e){return{create:null,query:{serverName:Jh.NAME.OPEN_IM,cmd:Jh.CMD.LOGOUT_ALL,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:{websdkappid:Yh,v:$h,platform:zh,a2:null!==e.context?e.context.a2Key:"",tinyid:null!==e.context?e.context.tinyID:"",sdkappid:null!==e.loginInfo?e.loginInfo.SDKAppID:0,contentType:null!==e.context?e.context.contentType:0,apn:null!==e.context?e.context.apn:"",reqtime:+new Date/1e3},requestData:{}},update:null,delete:null}}(this.tim),this.config.longPollLogout=function(e){return{create:null,query:{serverName:Jh.NAME.OPEN_IM,cmd:Jh.CMD.LOGOUT_LONG_POLL,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:{websdkappid:Yh,v:$h,platform:zh,a2:null!==e.context?e.context.a2Key:"",tinyid:null!==e.context?e.context.tinyID:0,sdkappid:null!==e.context?e.context.SDKAppID:0,contentType:null!==e.context?e.context.contentType:0,apn:null!==e.context?e.context.apn:1,reqtime:function(){return Date.now()}},requestData:{longPollID:""},keyMaps:{request:{longPollID:"LongPollingId"}}},update:null,delete:null}}(this.tim),this.config.profile=function(e){var t=O_(e),n=Jh.NAME.PROFILE,r=Jh.CHANNEL.XHR,o=Xh;return{query:{serverName:n,cmd:Jh.CMD.PORTRAIT_GET,channel:r,protocol:o,method:"POST",queryString:t,requestData:{fromAccount:"",userItem:[]},keyMaps:{request:{toAccount:"To_Account",standardSequence:"StandardSequence",customSequence:"CustomSequence"}}},update:{serverName:n,cmd:Jh.CMD.PORTRAIT_SET,channel:r,protocol:o,method:"POST",queryString:t,requestData:{fromAccount:"",profileItem:[{tag:tp.NICK,value:""},{tag:tp.GENDER,value:""},{tag:tp.ALLOWTYPE,value:""},{tag:tp.AVATAR,value:""}]}}}}(this.tim),this.config.group=function(e){var t={websdkappid:Yh,v:$h,platform:zh,a2:null!==e.context&&e.context.a2Key?e.context.a2Key:void 0,tinyid:null!==e.context&&e.context.tinyID?e.context.tinyID:void 0,sdkappid:null!==e.context?e.context.SDKAppID:0,contentType:null!==e.context?e.context.contentType:0,accounttype:null!==e.context?e.context.accountType:0},n={request:{ownerID:"Owner_Account",userID:"Member_Account",newOwnerID:"NewOwner_Account",maxMemberNum:"MaxMemberCount",groupCustomField:"AppDefinedData",memberCustomField:"AppMemberDefinedData",groupCustomFieldFilter:"AppDefinedDataFilter_Group",memberCustomFieldFilter:"AppDefinedDataFilter_GroupMember",messageRemindType:"MsgFlag",userIDList:"MemberList",groupIDList:"GroupIdList",applyMessage:"ApplyMsg",muteTime:"ShutUpTime",muteAllMembers:"ShutUpAllMember",joinOption:"ApplyJoinOption"},response:{GroupIdList:"groups",MsgFlag:"messageRemindType",AppDefinedData:"groupCustomField",AppMemberDefinedData:"memberCustomField",AppDefinedDataFilter_Group:"groupCustomFieldFilter",AppDefinedDataFilter_GroupMember:"memberCustomFieldFilter",InfoSeq:"infoSequence",MemberList:"members",GroupInfo:"groups",ShutUpUntil:"muteUntil",ShutUpAllMember:"muteAllMembers",ApplyJoinOption:"joinOption"}};return{create:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.CREATE_GROUP,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{type:fn.GRP_PRIVATE,name:void 0,groupID:void 0,ownerID:e.loginInfo.identifier,introduction:void 0,notification:void 0,avatar:void 0,maxMemberNum:void 0,joinOption:void 0,memberList:void 0,groupCustomField:void 0},keyMaps:n},list:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.GET_JOINED_GROUPS,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{userID:e.loginInfo.identifier,limit:void 0,offset:void 0,groupType:void 0,responseFilter:void 0},keyMaps:n},query:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.GET_GROUP_INFO,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{groupIDList:void 0,responseFilter:void 0},keyMaps:n},getGroupMemberProfile:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.GET_GROUP_MEMBER_INFO,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{groupID:void 0,userIDList:void 0,memberInfoFilter:void 0,memberCustomFieldFilter:void 0},keyMaps:{request:Ln({},n.request,{userIDList:"Member_List_Account"}),response:n.response}},getGroupMemberList:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.GET_GROUP_MEMBER_LIST,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{groupID:void 0,limit:0,offset:0,memberRoleFilter:void 0,memberInfoFilter:void 0},keyMaps:n},quitGroup:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.QUIT_GROUP,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{groupID:void 0}},changeGroupOwner:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.CHANGE_GROUP_OWNER,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{groupID:void 0,newOwnerID:void 0},keyMaps:n},destroyGroup:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.DESTROY_GROUP,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{groupID:void 0}},updateGroupProfile:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.MODIFY_GROUP_INFO,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{groupID:void 0,name:void 0,introduction:void 0,notification:void 0,avatar:void 0,maxMemberNum:void 0,joinOption:void 0,groupCustomField:void 0,muteAllMembers:void 0},keyMaps:{request:Ln({},n.request,{groupCustomField:"AppDefinedData"}),response:n.response}},modifyGroupMemberInfo:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.MODIFY_GROUP_MEMBER_INFO,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{groupID:void 0,userID:void 0,messageRemindType:void 0,nameCard:void 0,role:void 0,memberCustomField:void 0,muteTime:void 0},keyMaps:n},addGroupMember:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.ADD_GROUP_MEMBER,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{groupID:void 0,silence:void 0,userIDList:void 0},keyMaps:n},deleteGroupMember:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.DELETE_GROUP_MEMBER,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{groupID:void 0,userIDList:void 0,reason:void 0},keyMaps:{request:{userIDList:"MemberToDel_Account"}}},searchGroupByID:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.SEARCH_GROUP_BY_ID,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{groupIDList:void 0,responseFilter:{groupBasePublicInfoFilter:["Type","Name","Introduction","Notification","FaceUrl","CreateTime","Owner_Account","LastInfoTime","LastMsgTime","NextMsgSeq","MemberNum","MaxMemberNum","ApplyJoinOption"]}},keyMaps:{request:{groupIDList:"GroupIdList"}}},applyJoinGroup:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.APPLY_JOIN_GROUP,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{groupID:void 0,applyMessage:void 0,userDefinedField:void 0},keyMaps:n},applyJoinAVChatRoom:{serverName:Jh.NAME.BIG_GROUP_NO_AUTH,cmd:Jh.CMD.APPLY_JOIN_GROUP,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:{websdkappid:Yh,v:$h,platform:zh,sdkappid:null!==e.context?e.context.SDKAppID:0,contentType:null!==e.context?e.context.contentType:0,accounttype:null!==e.context?e.context.accountType:0},requestData:{groupID:void 0,applyMessage:void 0,userDefinedField:void 0},keyMaps:n},handleApplyJoinGroup:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.HANDLE_APPLY_JOIN_GROUP,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{groupID:void 0,applicant:void 0,handleAction:void 0,handleMessage:void 0,authentication:void 0,messageKey:void 0,userDefinedField:void 0},keyMaps:{request:{applicant:"Applicant_Account",handleAction:"HandleMsg",handleMessage:"ApprovalMsg",messageKey:"MsgKey"},response:{MsgKey:"messageKey"}}},deleteGroupSystemNotice:{serverName:Jh.NAME.OPEN_IM,cmd:Jh.CMD.DELETE_GROUP_SYSTEM_MESSAGE,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{messageListToDelete:void 0},keyMaps:{request:{messageListToDelete:"DelMsgList",messageSeq:"MsgSeq",messageRandom:"MsgRandom"}}},getGroupPendency:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.GET_GROUP_PENDENCY,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:t,requestData:{startTime:void 0,limit:void 0,handleAccount:void 0},keyMaps:{request:{handleAccount:"Handle_Account"}}}}}(this.tim),this.config.longPollID=function(e){return{create:{},query:{serverName:Jh.NAME.OPEN_IM,cmd:Jh.CMD.GET_LONG_POLL_ID,channel:Jh.CHANNEL.XHR,protocol:Xh,queryString:{websdkappid:Yh,v:$h,platform:zh,a2:null!==e.context?e.context.a2Key:"",tinyid:null!==e.context?e.context.tinyID:0,sdkappid:null!==e.context?e.context.SDKAppID:0,contentType:null!==e.context?e.context.contentType:0,apn:null!==e.context?e.context.apn:1,reqtime:+new Date/1e3},requestData:{},keyMaps:{response:{LongPollingId:"longPollingID"}}},update:{},delete:{}}}(this.tim),this.config.longPoll=function(e){var t={websdkappid:Yh,v:$h,platform:zh,a2:null!==e.context?e.context.a2Key:"",tinyid:null!==e.context?e.context.tinyID:0,sdkappid:null!==e.context?e.context.SDKAppID:0,contentType:null!==e.context?e.context.contentType:0,accounttype:null!==e.context?e.loginInfo.accountType:0,apn:null!==e.context?e.context.apn:1,reqtime:Math.ceil(+new Date/1e3)};return{create:{},query:{serverName:Jh.NAME.OPEN_IM,cmd:Jh.CMD.LONG_POLL,channel:Jh.CHANNEL.AUTO,protocol:Xh,queryString:t,requestData:{timeout:null,cookie:{notifySeq:0,noticeSeq:0,longPollingID:0}},keyMaps:{response:{C2cMsgArray:"C2CMessageArray",GroupMsgArray:"groupMessageArray",GroupTips:"groupTips",C2cNotifyMsgArray:"C2CNotifyMessageArray",ClientSeq:"clientSequence",MsgPriority:"priority",NoticeSeq:"noticeSequence",MsgContent:"content",MsgType:"type",MsgBody:"elements",ToGroupId:"to",Desc:"description",Ext:"extension"}}},update:{},delete:{}}}(this.tim),this.config.applyC2C=function(e){var t=O_(e),n=Jh.NAME.FRIEND,r=Jh.CHANNEL.XHR,o=Xh;return{create:{serverName:n,cmd:Jh.CMD.FRIEND_ADD,channel:r,protocol:o,queryString:t,requestData:{fromAccount:"",addFriendItem:[]}},get:{serverName:n,cmd:Jh.CMD.GET_PENDENCY,channel:r,protocol:o,queryString:t,requestData:{fromAccount:"",pendencyType:"Pendency_Type_ComeIn"}},update:{serverName:n,cmd:Jh.CMD.RESPONSE_PENDENCY,channel:r,protocol:o,queryString:t,requestData:{fromAccount:"",responseFriendItem:[]}},delete:{serverName:n,cmd:Jh.CMD.DELETE_PENDENCY,channel:r,protocol:o,queryString:t,requestData:{fromAccount:"",toAccount:[],pendencyType:"Pendency_Type_ComeIn"}}}}(this.tim),this.config.friend=function(e){var t=O_(e),n=Jh.NAME.FRIEND,r=Jh.CHANNEL.XHR,o=Xh;return{get:{serverName:n,cmd:Jh.CMD.FRIEND_GET_ALL,channel:r,protocol:o,method:"POST",queryString:t,requestData:{fromAccount:"",timeStamp:0,tagList:[tp.NICK,"Tag_SNS_IM_Remark",tp.AVATAR]},keyMaps:{request:{},response:{}}},delete:{serverName:n,cmd:Jh.CMD.FRIEND_DELETE,channel:r,protocol:o,method:"POST",queryString:t,requestData:{fromAccount:"",toAccount:[],deleteType:"Delete_Type_Single"}}}}(this.tim),this.config.blacklist=function(e){var t=O_(e);return{create:{serverName:Jh.NAME.FRIEND,cmd:Jh.CMD.ADD_BLACKLIST,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{fromAccount:"",toAccount:[]}},get:{serverName:Jh.NAME.FRIEND,cmd:Jh.CMD.GET_BLACKLIST,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{fromAccount:"",startIndex:0,maxLimited:30,lastSequence:0}},delete:{serverName:Jh.NAME.FRIEND,cmd:Jh.CMD.DELETE_BLACKLIST,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{fromAccount:"",toAccount:[]}},update:{}}}(this.tim),this.config.c2cMessage=function(e){var t={platform:zh,websdkappid:Yh,v:$h,a2:null!==e.context?e.context.a2Key:"",tinyid:null!==e.context?e.context.tinyID:0,sdkappid:null!==e.context?e.context.SDKAppID:0,contentType:null!==e.context?e.context.contentType:0,apn:null!==e.context?e.context.apn:1,reqtime:function(){return+new Date}},n={request:{fromAccount:"From_Account",toAccount:"To_Account",msgTimeStamp:"MsgTimeStamp",msgSeq:"MsgSeq",msgRandom:"MsgRandom",msgBody:"MsgBody",count:"MaxCnt",lastMessageTime:"LastMsgTime",messageKey:"MsgKey",peerAccount:"Peer_Account",data:"Data",description:"Desc",extension:"Ext",type:"MsgType",content:"MsgContent",sizeType:"Type",uuid:"UUID",imageUrl:"URL",fileUrl:"Url",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",videoUrl:"",downloadFlag:"Download_Flag"},response:{MsgContent:"content",MsgTime:"time",Data:"data",Desc:"description",Ext:"extension",MsgKey:"messageKey",MsgType:"type",MsgBody:"elements",Download_Flag:"downloadFlag",ThumbUUID:"thumbUUID",VideoUUID:"videoUUID"}};return{create:{serverName:Jh.NAME.OPEN_IM,cmd:Jh.CMD.SEND_MESSAGE,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{fromAccount:e.loginInfo.identifier,toAccount:"",msgTimeStamp:Math.ceil(+new Date/1e3),msgSeq:0,msgRandom:0,msgBody:[],msgLifeTime:void 0,offlinePushInfo:{pushFlag:0,title:"",desc:"",ext:"",apnsInfo:{badgeMode:0},androidInfo:{OPPOChannelID:""}}},keyMaps:n},query:{serverName:Jh.NAME.OPEN_IM,cmd:Jh.CMD.GET_C2C_ROAM_MESSAGES,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{peerAccount:"",count:15,lastMessageTime:0,messageKey:"",withRecalledMsg:1},keyMaps:n},update:null,delete:null}}(this.tim),this.config.c2cMessageWillBeRevoked=function(e){var t={platform:zh,websdkappid:Yh,v:$h,a2:null!==e.context?e.context.a2Key:"",tinyid:null!==e.context?e.context.tinyID:0,sdkappid:null!==e.context?e.context.SDKAppID:0,contentType:null!==e.context?e.context.contentType:0,apn:null!==e.context?e.context.apn:1,reqtime:function(){return+new Date}};return{create:{serverName:Jh.NAME.OPEN_IM,cmd:Jh.CMD.REVOKE_C2C_MESSAGE,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{msgInfo:{fromAccount:"",toAccount:"",msgTimeStamp:Math.ceil(+new Date/1e3),msgSeq:0,msgRandom:0}},keyMaps:{request:{msgInfo:"MsgInfo",fromAccount:"From_Account",toAccount:"To_Account",msgTimeStamp:"MsgTimeStamp",msgSeq:"MsgSeq",msgRandom:"MsgRandom",msgBody:"MsgBody"}}}}}(this.tim),this.config.groupMessage=function(e){var t={platform:zh,websdkappid:Yh,v:$h,a2:null!==e.context?e.context.a2Key:"",tinyid:null!==e.context?e.context.tinyID:0,sdkappid:null!==e.context?e.context.SDKAppID:0,contentType:null!==e.context?e.context.contentType:0,apn:null!==e.context?e.context.apn:1,reqtime:function(){return+new Date}},n={request:{to:"GroupId",extension:"Ext",data:"Data",description:"Desc",random:"Random",sequence:"ReqMsgSeq",count:"ReqMsgNumber",type:"MsgType",priority:"MsgPriority",content:"MsgContent",elements:"MsgBody",sizeType:"Type",uuid:"UUID",imageUrl:"URL",fileUrl:"Url",remoteAudioUrl:"Url",remoteVideoUrl:"VideoUrl",thumbUUID:"ThumbUUID",videoUUID:"VideoUUID",videoUrl:"",downloadFlag:"Download_Flag",clientSequence:"ClientSeq"},response:{Random:"random",MsgTime:"time",MsgSeq:"sequence",ReqMsgSeq:"sequence",RspMsgList:"messageList",IsPlaceMsg:"isPlaceMessage",IsSystemMsg:"isSystemMessage",ToGroupId:"to",EnumFrom_AccountType:"fromAccountType",EnumTo_AccountType:"toAccountType",GroupCode:"groupCode",MsgPriority:"priority",MsgBody:"elements",MsgType:"type",MsgContent:"content",IsFinished:"complete",Download_Flag:"downloadFlag",ClientSeq:"clientSequence",ThumbUUID:"thumbUUID",VideoUUID:"videoUUID"}};return{create:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.SEND_GROUP_MESSAGE,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{groupID:"",fromAccount:e.loginInfo.identifier,random:0,clientSequence:0,priority:"",msgBody:[],onlineOnlyFlag:0,offlinePushInfo:{pushFlag:0,title:"",desc:"",ext:"",apnsInfo:{badgeMode:0},androidInfo:{OPPOChannelID:""}}},keyMaps:n},query:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.GET_GROUP_ROAM_MESSAGES,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{withRecalledMsg:1,groupID:"",count:15,sequence:""},keyMaps:n},update:null,delete:null}}(this.tim),this.config.groupMessageWillBeRevoked=function(e){var t={platform:zh,websdkappid:Yh,v:$h,a2:null!==e.context?e.context.a2Key:"",tinyid:null!==e.context?e.context.tinyID:0,sdkappid:null!==e.context?e.context.SDKAppID:0,contentType:null!==e.context?e.context.contentType:0,apn:null!==e.context?e.context.apn:1,reqtime:function(){return+new Date}};return{create:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.REVOKE_GROUP_MESSAGE,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{to:"",msgSeqList:[]},keyMaps:{request:{to:"GroupId",msgSeqList:"MsgSeqList",msgSeq:"MsgSeq"}}}}}(this.tim),this.config.conversation=function(e){var t={platform:zh,websdkappid:Yh,v:$h,a2:null!==e.context?e.context.a2Key:"",tinyid:null!==e.context?e.context.tinyID:0,sdkappid:null!==e.context?e.context.SDKAppID:0,contentType:null!==e.context?e.context.contentType:0,apn:null!==e.context?e.context.apn:1};return{query:{serverName:Jh.NAME.RECENT_CONTACT,cmd:Jh.CMD.GET_CONVERSATION_LIST,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{fromAccount:e.loginInfo.identifier,count:0},keyMaps:{request:{},response:{SessionItem:"conversations",ToAccount:"groupID",To_Account:"userID",UnreadMsgCount:"unreadCount",MsgGroupReadedSeq:"messageReadSeq"}}},pagingQuery:{serverName:Jh.NAME.RECENT_CONTACT,cmd:Jh.CMD.PAGING_GET_CONVERSATION_LIST,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{fromAccount:void 0,timeStamp:void 0,orderType:void 0},keyMaps:{request:{},response:{SessionItem:"conversations",ToAccount:"groupID",To_Account:"userID",UnreadMsgCount:"unreadCount",MsgGroupReadedSeq:"messageReadSeq"}}},delete:{serverName:Jh.NAME.RECENT_CONTACT,cmd:Jh.CMD.DELETE_CONVERSATION,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{fromAccount:e.loginInfo.identifier,toAccount:void 0,type:1,toGroupID:void 0},keyMaps:{request:{toGroupID:"ToGroupid"}}},setC2CMessageRead:{serverName:Jh.NAME.OPEN_IM,cmd:Jh.CMD.SET_C2C_MESSAGE_READ,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{C2CMsgReaded:void 0},keyMaps:{request:{lastMessageTime:"LastedMsgTime"}}},setGroupMessageRead:{serverName:Jh.NAME.GROUP,cmd:Jh.CMD.SET_GROUP_MESSAGE_READ,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{groupID:void 0,messageReadSeq:void 0},keyMaps:{request:{messageReadSeq:"MsgReadedSeq"}}}}}(this.tim),this.config.syncMessage=function(e){var t={platform:zh,websdkappid:Yh,v:$h,a2:null!==e.context?e.context.a2Key:"",tinyid:null!==e.context?e.context.tinyID:0,sdkappid:null!==e.context?e.context.SDKAppID:0,contentType:null!==e.context?e.context.contentType:0,apn:null!==e.context?e.context.apn:1,reqtime:function(){return[Math.ceil(+new Date),Math.random()].join("")}};return{create:null,query:{serverName:Jh.NAME.OPEN_IM,cmd:Jh.CMD.GET_MESSAGES,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{cookie:"",syncFlag:0,needAbstract:1},keyMaps:{request:{fromAccount:"From_Account",toAccount:"To_Account",from:"From_Account",to:"To_Account",time:"MsgTimeStamp",sequence:"MsgSeq",random:"MsgRandom",elements:"MsgBody"},response:{MsgList:"messageList",SyncFlag:"syncFlag",To_Account:"to",From_Account:"from",ClientSeq:"clientSequence",MsgSeq:"sequence",NoticeSeq:"noticeSequence",NotifySeq:"notifySequence",MsgRandom:"random",MsgTimeStamp:"time",MsgContent:"content",ToGroupId:"groupID",MsgKey:"messageKey",GroupTips:"groupTips",MsgBody:"elements",MsgType:"type",C2CRemainingUnreadCount:"C2CRemainingUnreadList"}}},update:null,delete:null}}(this.tim),this.config.AVChatRoom=function(e){return{startLongPoll:{serverName:Jh.NAME.BIG_GROUP_LONG_POLLING_NO_AUTH,cmd:Jh.CMD.AVCHATROOM_LONG_POLL,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:{websdkappid:Yh,v:$h,platform:zh,sdkappid:e.loginInfo.SDKAppID,accounttype:"792",apn:null!==e.context?e.context.apn:1,reqtime:function(){return+new Date}},requestData:{USP:1,startSeq:1,holdTime:90,key:void 0},keyMaps:{request:{USP:"USP"},response:{ToGroupId:"groupID",MsgPriority:"priority"}}}}}(this.tim),this.config.cosUpload=function(e){var t={platform:zh,websdkappid:Yh,v:$h,a2:null!==e.context?e.context.a2Key:"",tinyid:null!==e.context?e.context.tinyID:0,sdkappid:null!==e.context?e.context.SDKAppID:0,contentType:null!==e.context?e.context.contentType:0,apn:null!==e.context?e.context.apn:1,reqtime:function(){return Date.now()}};return{create:{serverName:Jh.NAME.OPEN_IM,cmd:Jh.CMD.FILE_UPLOAD,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{appVersion:"2.1",fromAccount:"",toAccount:"",sequence:0,time:function(){return Math.ceil(Date.now()/1e3)},random:function(){return Da()},fileStrMd5:"",fileSize:"",serverVer:1,authKey:"",busiId:1,pkgFlag:1,sliceOffset:0,sliceSize:0,sliceData:"",contentType:"application/x-www-form-urlencoded"},keyMaps:{request:{},response:{}}},update:null,delete:null}}(this.tim),this.config.cosSig=function(e){var t={sdkappid:function(){return e.loginInfo.SDKAppID},identifier:function(){return e.loginInfo.identifier},userSig:function(){return e.context.userSig}};return{create:null,query:{serverName:Jh.NAME.IM_COS_SIGN,cmd:Jh.CMD.COS_SIGN,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:t,requestData:{cmd:"open_im_cos_svc",subCmd:"get_cos_token",duration:300,version:2},keyMaps:{request:{userSig:"usersig",subCmd:"sub_cmd",cmd:"cmd",duration:"duration",version:"version"},response:{expired_time:"expiredTime",bucket_name:"bucketName",session_token:"sessionToken",tmp_secret_id:"secretId",tmp_secret_key:"secretKey"}}},update:null,delete:null}}(this.tim),this.config.bigDataHallwayAuthKey=function(e){return{create:null,query:{serverName:Jh.NAME.OPEN_IM,cmd:Jh.CMD.BIG_DATA_HALLWAY_AUTH_KEY,channel:Jh.CHANNEL.XHR,protocol:Xh,method:"POST",queryString:{websdkappid:Yh,v:$h,platform:zh,sdkappid:e.loginInfo.SDKAppID,accounttype:"792",apn:null!==e.context?e.context.apn:1,reqtime:function(){return+new Date}},requestData:{}}}}(this.tim),this.config.ssoEventStat=function(e){var t={sdkappid:e.loginInfo.SDKAppID,reqtime:Math.ceil(+new Date/1e3)};return{create:{serverName:Jh.NAME.IM_OPEN_STAT,cmd:Jh.CMD.TIM_WEB_REPORT,channel:Jh.CHANNEL.AUTO,protocol:Xh,queryString:t,requestData:{table:"",report:[]},keyMaps:{request:{table:"table",report:"report",SDKAppID:"sdkappid",version:"version",tinyID:"tinyid",userID:"userid",platform:"platform",method:"method",time:"time",start:"start",end:"end",cost:"cost",status:"status",codeint:"codeint",message:"message",pointer:"pointer",text:"text",msgType:"msgtype",networkType:"networktype",startts:"startts",endts:"endts",timespan:"timespan"}}},query:{},update:{},delete:{}}}(this.tim),this.config.ssoSumStat=function(e){var t=null;return null!==e.context&&(t={sdkappid:e.context.SDKAppID,reqtime:Math.ceil(+new Date/1e3)}),{create:{serverName:Jh.NAME.IM_OPEN_STAT,cmd:Jh.CMD.TIM_WEB_REPORT,channel:Jh.CHANNEL.AUTO,protocol:Xh,queryString:t,requestData:{table:"",report:[]},keyMaps:{request:{table:"table",report:"report",SDKAppID:"sdkappid",version:"version",tinyID:"tinyid",userID:"userid",item:"item",lpID:"lpid",platform:"platform",networkType:"networktype",total:"total",successRate:"successrate",avg:"avg",timespan:"timespan",time:"time"}}},query:{},update:{},delete:{}}}(this.tim)}},{key:"_initRequestData",value:function(e,t){if(void 0===e)return Wd(t.requestData,this._getRequestMap(t),this.tim);var n=t.requestData,r=Object.create(null);for(var o in n)if(Object.prototype.hasOwnProperty.call(n,o)){if(r[o]="function"==typeof n[o]?n[o]():n[o],void 0===e[o])continue;r[o]=e[o]}return Wd(r,this._getRequestMap(t),this.tim)}},{key:"_getRequestMap",value:function(e){if(e.keyMaps&&e.keyMaps.request&&Object.keys(e.keyMaps.request).length>0)return e.keyMaps.request}},{key:"_initEncoder",value:function(e){switch(e.protocol){case Xh:return function(e){if("string"===kn(e))try{return JSON.parse(e)}catch(dC){return e}return e};case Wh:return function(e){return e};default:return function(e){return aa.warn("PackageConfig._initEncoder(), unknow response type, data: ",JSON.stringify(e)),e}}}},{key:"_initDecoder",value:function(e){switch(e.protocol){case Xh:return function(e){if("string"===kn(e))try{return JSON.parse(e)}catch(dC){return e}return e};case Wh:return function(e){return e};default:return function(e){return aa.warn("PackageConfig._initDecoder(), unknow response type, data: ",e),e}}}}]),e}(),N_=Math.floor;be({target:"Number",stat:!0},{isInteger:function(e){return!v(e)&&isFinite(e)&&N_(e)===e}});var P_=function(){for(var e=[],t=G_(arguments),n=0;n<arguments.length;n++)Number.isInteger(arguments[n])?e.push(arguments[n]):e.push(1==!!arguments[n]?"1":"0");return e.join(t)},G_=function(e){var t=e.length,n=e[t-1];if("string"!=typeof n)return"";if(n.length>1)return"";var r=e[t-1];return delete e[t-1],e.length-=t===e.length?1:0,r},x_={C2CMessageArray:1,groupMessageArray:1,groupTips:1,C2CNotifyMessageArray:1,profileModify:1,friendListMod:1},U_=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;return wn(this,n),(r=t.call(this,e))._initialization(),r}return bn(n,[{key:"_initialization",value:function(){this._syncOffset="",this._syncNoticeList=[],this._syncEventArray=[],this._syncMessagesIsRunning=!1,this._syncMessagesFinished=!1,this._isLongPoll=!1,this._longPollID=0,this._noticeSequence=0,this._initializeListener(),this._runLoop=null,this._initShuntChannels()}},{key:"_initShuntChannels",value:function(){this._shuntChannels=Object.create(null),this._shuntChannels.C2CMessageArray=this._C2CMessageArrayChannel.bind(this),this._shuntChannels.groupMessageArray=this._groupMessageArrayChannel.bind(this),this._shuntChannels.groupTips=this._groupTipsChannel.bind(this),this._shuntChannels.C2CNotifyMessageArray=this._C2CNotifyMessageArrayChannel.bind(this),this._shuntChannels.profileModify=this._profileModifyChannel.bind(this),this._shuntChannels.friendListMod=this._friendListModChannel.bind(this)}},{key:"_C2CMessageArrayChannel",value:function(e,t,n){this.emitInnerEvent(_d,t)}},{key:"_groupMessageArrayChannel",value:function(e,t,n){this.emitInnerEvent(Cd,t)}},{key:"_groupTipsChannel",value:function(e,t,n){var r=this;switch(e){case 4:case 6:this.emitInnerEvent(Id,t);break;case 5:t.forEach((function(e){ha(e.elements.revokedInfos)?r.emitInnerEvent(Dd,t):r.emitInnerEvent(Md,{groupSystemNotices:t,type:n})}));break;default:aa.log("NotificationController._groupTipsChannel unknown event=".concat(e," type=").concat(n),t)}}},{key:"_C2CNotifyMessageArrayChannel",value:function(e,t,n){this._isKickedoutNotice(t)?this.emitInnerEvent(yd):this._isSysCmdMsgNotify(t)?this.emitInnerEvent(Ed):this._isC2CMessageRevokedNotify(t)&&this.emitInnerEvent(kd,t)}},{key:"_profileModifyChannel",value:function(e,t,n){this.emitInnerEvent(Td,t)}},{key:"_friendListModChannel",value:function(e,t,n){this.emitInnerEvent(Sd,t)}},{key:"_dispatchNotice",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"poll";if(ha(e))for(var n=null,r=null,o="",i="",s="",a=0,u=0,c=e.length;u<c;u++)a=(n=e[u]).event,o=Object.keys(n).find((function(e){return void 0!==x_[e]})),ma(this._shuntChannels[o])?(r=n[o],"poll"===t&&this._updatenoticeSequence(r),this._shuntChannels[o](a,r,t)):("poll"===t&&this._updatenoticeSequence(),i="".concat(Sf),s="".concat(Rh,": ").concat(a,", ").concat(o),this.emitInnerEvent(Pd,new _p({code:i,message:s,data:{payloadName:o,event:a}})),i="",s="")}},{key:"getLongPollID",value:function(){return this._longPollID}},{key:"_IAmReady",value:function(){this.triggerReady()}},{key:"reset",value:function(){this._noticeSequence=0,this._resetSync(),this.closeNoticeChannel()}},{key:"_resetSync",value:function(){this._syncOffset="",this._syncNoticeList=[],this._syncEventArray=[],this._syncMessagesIsRunning=!1,this._syncMessagesFinished=!1}},{key:"_setNoticeSeqInRequestData",value:function(e){e.Cookie.NoticeSeq=this._noticeSequence,this.tim.sumStatController.addTotalCount(fg)}},{key:"_updatenoticeSequence",value:function(e){if(e){var t=e[e.length-1].noticeSequence;t&&"number"==typeof t?t<=this._noticeSequence||(this._noticeSequence=t):this._noticeSequence++}else this._noticeSequence++}},{key:"_initializeListener",value:function(){var e=this.tim.innerEmitter;e.on(td,this._startSyncMessages,this),e.on(Ld,this.closeNoticeChannel,this),e.on(gd,this._onFastStart,this)}},{key:"openNoticeChannel",value:function(){aa.log("NotificationController.openNoticeChannel"),this._getLongPollID()}},{key:"closeNoticeChannel",value:function(){aa.log("NotificationController.closeNoticeChannel"),(this._runLoop instanceof Gy||this._runLoop instanceof xy)&&(this._runLoop.abort(),this._runLoop.stop()),this._longPollID=0,this._isLongPoll=!1}},{key:"_getLongPollID",value:function(){var e=this;if(0===this._longPollID){var t=new Cg;t.setMethod(cm).setStart(),this.request({name:"longPollID",action:"query"}).then((function(n){var r=n.data.longPollingID;e._onGetLongPollIDSuccess(r),t.setCode(0).setText("longPollingID=".concat(r)).setNetworkType(e.getNetworkType()).setEnd()})).catch((function(n){var r=new _p({code:n.code||Df,message:n.message||Lh});e.emitInnerEvent(fd),e.emitInnerEvent(Pd,r),e.probeNetwork().then((function(e){var n=Hn(e,2),o=n[0],i=n[1];t.setError(r,o,i).setEnd()}))}))}else this._onGetLongPollIDSuccess(this._longPollID)}},{key:"_onGetLongPollIDSuccess",value:function(e){this.emitInnerEvent(nd,[{key:"long_poll_logout.query.requestData.longPollingID",value:e},{key:"longPoll.query.requestData.cookie.longPollingID",value:e}]),this._longPollID=e,this._startLongPoll(),this._IAmReady(),this.tim.sumStatController.recordLongPollingID(this._longPollID)}},{key:"_startLongPoll",value:function(){if(!0!==this._isLongPoll){aa.log("NotificationController._startLongPoll...");var e=this.tim.connectionController,t=this.createTransportCapsule({name:"longPoll",action:"query"});this._isLongPoll=!0,this._runLoop=e.createRunLoop({pack:t,before:this._setNoticeSeqInRequestData.bind(this),success:this._onNoticeReceived.bind(this),fail:this._onNoticeFail.bind(this)}),this._runLoop.start()}else aa.log("NotificationController._startLongPoll is running...")}},{key:"_onFastStart",value:function(){this.closeNoticeChannel(),this.syncMessage()}},{key:"_onNoticeReceived",value:function(e){var t=e.data;if(t.errorCode!==Ru.SUCCESS){var n=new Cg;n.setMethod(lm).setStart(),n.setMessage(t.errorInfo||JSON.stringify(t)).setCode(t.errorCode).setNetworkType(this.getNetworkType()).setEnd(!0),this._onResponseError(t)}else this.emitInnerEvent(dd);this.tim.sumStatController.addSuccessCount(fg),this.tim.sumStatController.addCost(fg,t.timecost),e.data.eventArray&&this._dispatchNotice(e.data.eventArray)}},{key:"_onResponseError",value:function(e){switch(e.errorCode){case wf:aa.warn("NotificationController._onResponseError, longPollingID=".concat(this._longPollID," was kicked out")),this.emitInnerEvent(vd),this.closeNoticeChannel();break;case Af:case bf:this.emitInnerEvent(Nd);break;default:da(e.errorCode)||da(e.errorInfo)?aa.log("NotificationController._onResponseError, errorCode or errorInfo undefined!",e):this.emitInnerEvent(Pd,new _p({code:e.errorCode,message:e.errorInfo}))}}},{key:"_onNoticeFail",value:function(e){if(e.error)if("ECONNABORTED"===e.error.code||e.error.code===vf)if(e.error.config){var t=e.error.config.url,n=e.error.config.data;aa.log("NotificationController._onNoticeFail request timed out. url=".concat(t," data=").concat(n))}else aa.log("NotificationController._onNoticeFail request timed out.");else aa.log("NotificationController._onNoticeFail request failed due to network error");this.emitInnerEvent(hd)}},{key:"_isKickedoutNotice",value:function(e){return!!e[0].hasOwnProperty("kickoutMsgNotify")}},{key:"_isSysCmdMsgNotify",value:function(e){if(!e[0])return!1;var t=e[0];return!!Object.prototype.hasOwnProperty.call(t,"sysCmdMsgNotify")}},{key:"_isC2CMessageRevokedNotify",value:function(e){var t=e[0];return!!Object.prototype.hasOwnProperty.call(t,"c2cMessageRevokedNotify")}},{key:"_startSyncMessages",value:function(e){!0!==this._syncMessagesFinished&&this.syncMessage()}},{key:"syncMessage",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this._syncMessagesIsRunning=!0,this.request({name:"syncMessage",action:"query",param:{cookie:t,syncFlag:n}}).then((function(t){var n=t.data;switch(P_(n.cookie,n.syncFlag)){case"00":case"01":e.emitInnerEvent(Pd,{code:Tf,message:Oh});break;case"10":case"11":n.eventArray&&e._dispatchNotice(n.eventArray,"sync"),e._syncNoticeList=e._syncNoticeList.concat(n.messageList),e.emitInnerEvent(rd,{data:n.messageList,C2CRemainingUnreadList:n.C2CRemainingUnreadList}),e._syncOffset=n.cookie,e.syncMessage(n.cookie,n.syncFlag);break;case"12":n.eventArray&&e._dispatchNotice(n.eventArray,"sync"),e.openNoticeChannel(),e._syncNoticeList=e._syncNoticeList.concat(n.messageList),e.emitInnerEvent(od,{messageList:n.messageList,C2CRemainingUnreadList:n.C2CRemainingUnreadList}),e._syncOffset=n.cookie,e._syncNoticeList=[],e._syncMessagesIsRunning=!1,e._syncMessagesFinished=!0}})).catch((function(t){e._syncMessagesIsRunning=!1,aa.error("NotificationController.syncMessage failed. error:".concat(t))}))}}]),n}(ng),q_=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;return wn(this,n),(r=t.call(this,e)).COSSDK=null,r._cosUploadMethod=null,r.expiredTimeLimit=300,r.appid=0,r.bucketName="",r.ciUrl="",r.directory="",r.downloadUrl="",r.uploadUrl="",r.expiredTimeOut=r.expiredTimeLimit,r.region="ap-shanghai",r.cos=null,r.cosOptions={secretId:"",secretKey:"",sessionToken:"",expiredTime:0},r._timer=0,r.tim.innerEmitter.on(td,r._init,Fn(r)),r.triggerReady(),r}return bn(n,[{key:"_expiredTimer",value:function(){var e=this;this._timer=setInterval((function(){Math.ceil(Date.now()/1e3)>=e.cosOptions.expiredTime-60&&(e._getAuthorizationKey(),clearInterval(e._timer))}),3e4)}},{key:"_init",value:function(){var e=Os?"cos-wx-sdk":"cos-js-sdk";this.COSSDK=this.tim.getPlugin(e),this.COSSDK?this._getAuthorizationKey():aa.warn("UploadController._init 没有检测到上传插件，将无法发送图片、音频、视频、文件等类型的消息。详细请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#registerPlugin")}},{key:"_getAuthorizationKey",value:function(){var e=this,t=Math.ceil(Date.now()/1e3),n=new Cg;n.setMethod(wg).setStart(),this.request({name:"cosSig",action:"query",param:{duration:this.expiredTimeLimit}}).then((function(r){aa.log("UploadController._getAuthorizationKey ok. data:",r.data);var o=r.data,i=o.expiredTime-t;n.setCode(0).setText("timeout=".concat(i,"s")).setNetworkType(e.getNetworkType()).setEnd(),e.appid=o.appid,e.bucketName=o.bucketName,e.ciUrl=o.ciUrl,e.directory=o.directory,e.downloadUrl=o.downloadUrl,e.uploadUrl=o.uploadUrl,e.expiredTimeOut=i,e.cosOptions={secretId:o.secretId,secretKey:o.secretKey,sessionToken:o.sessionToken,expiredTime:o.expiredTime},e._initUploaderMethod(),e._expiredTimer()})).catch((function(t){e.probeNetwork().then((function(n){var r=Hn(n,2),o=r[0],i=r[1];e.setError(t,o,i).setEnd()})),aa.warn("UploadController._getAuthorizationKey failed. error:",t)}))}},{key:"_initUploaderMethod",value:function(){var e=this;this.appid&&(this.cos=Os?new this.COSSDK({ForcePathStyle:!0,getAuthorization:this._getAuthorization.bind(this)}):new this.COSSDK({getAuthorization:this._getAuthorization.bind(this)}),this._cosUploadMethod=Os?function(t,n){e.cos.postObject(t,n)}:function(t,n){e.cos.uploadFiles(t,n)})}},{key:"_getAuthorization",value:function(e,t){t({TmpSecretId:this.cosOptions.secretId,TmpSecretKey:this.cosOptions.secretKey,XCosSecurityToken:this.cosOptions.sessionToken,ExpiredTime:this.cosOptions.expiredTime})}},{key:"uploadImage",value:function(e){if(!e.file)return cg(new _p({code:Np,message:Kf}));var t=this._checkImageType(e.file);if(!0!==t)return t;var n=this._checkImageMime(e.file);if(!0!==n)return n;var r=this._checkImageSize(e.file);return!0!==r?r:this.upload(e)}},{key:"_checkImageType",value:function(e){var t="";return t=Os?e.url.slice(e.url.lastIndexOf(".")+1):e.files[0].name.slice(e.files[0].name.lastIndexOf(".")+1),Ph.indexOf(t.toLowerCase())>=0||cg(new _p({coe:Pp,message:$f}))}},{key:"_checkImageMime",value:function(e){return!0}},{key:"_checkImageSize",value:function(e){var t=0;return 0===(t=Os?e.size:e.files[0].size)?cg(new _p({code:Rp,message:"".concat(Bf)})):t<20971520||cg(new _p({coe:Gp,message:"".concat(Yf)}))}},{key:"uploadFile",value:function(e){var t=null;return e.file?e.file.files[0].size>104857600?(t=new _p({code:Vp,message:th}),cg(t)):0===e.file.files[0].size?(t=new _p({code:Rp,message:"".concat(Bf)}),cg(t)):this.upload(e):(t=new _p({code:Hp,message:eh}),cg(t))}},{key:"uploadVideo",value:function(e){return e.file.videoFile.size>104857600?cg(new _p({code:Fp,message:"".concat(Jf)})):0===e.file.videoFile.size?cg(new _p({code:Rp,message:"".concat(Bf)})):-1===Gh.indexOf(e.file.videoFile.type)?cg(new _p({code:jp,message:"".concat(Qf)})):Os?this.handleVideoUpload({file:e.file.videoFile}):Rs?this.handleVideoUpload(e):void 0}},{key:"handleVideoUpload",value:function(e){var t=this;return new Promise((function(n,r){t.upload(e).then((function(e){n(e)})).catch((function(){t.upload(e).then((function(e){n(e)})).catch((function(){r(new _p({code:qp,message:Xf}))}))}))}))}},{key:"uploadAudio",value:function(e){return e.file?e.file.size>20971520?cg(new _p({code:Up,message:"".concat(Wf)})):0===e.file.size?cg(new _p({code:Rp,message:"".concat(Bf)})):this.upload(e):cg(new _p({code:xp,message:zf}))}},{key:"upload",value:function(e){var t=this;if(!ma(this._cosUploadMethod))return aa.warn("UploadController.upload 没有检测到上传插件，将无法发送图片、音频、视频、文件等类型的消息。详细请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html#registerPlugin"),cg(new _p({code:Dp,message:xf}));var n=new Cg;n.setMethod(Ag).setStart(),aa.time(_g);var r=Os?e.file:e.file.files[0];return new Promise((function(o,i){var s=Os?t._createCosOptionsWXMiniApp(e):t._createCosOptionsWeb(e),a=t;t._cosUploadMethod(s,(function(e,s){var u=Object.create(null);if(s){if(e||ha(s.files)&&s.files[0].error){var c=new _p({code:Bp,message:Zf});return n.setError(c,!0,t.getNetworkType()).setEnd(),aa.log("UploadController.upload failed, error:",s.files[0].error),403===s.files[0].error.statusCode&&(aa.warn("UploadController.upload failed. cos AccessKeyId was invalid, regain auth key!"),t._getAuthorizationKey()),void i(c)}u.fileName=r.name,u.fileSize=r.size,u.fileType=r.type.slice(r.type.indexOf("/")+1).toLowerCase(),u.location=Os?s.Location:s.files[0].data.Location;var l=aa.timeEnd(_g),p=a._formatFileSize(r.size),f=a._formatSpeed(1e3*r.size/l),h="size=".concat(p,",time=").concat(l,"ms,speed=").concat(f);return aa.log("UploadController.upload success name=".concat(r.name,",").concat(h)),o(u),void n.setCode(0).setNetworkType(t.getNetworkType()).setText(h).setEnd()}var d=new _p({code:Bp,message:Zf});n.setError(d,!0,a.getNetworkType()).setEnd(),aa.warn("UploadController.upload failed, error:",e),403===e.statusCode&&(aa.warn("UploadController.upload failed. cos AccessKeyId was invalid, regain auth key!"),t._getAuthorizationKey()),i(d)}))}))}},{key:"_formatFileSize",value:function(e){return e<1024?e+"B":e<1048576?Math.floor(e/1024)+"KB":Math.floor(e/1048576)+"MB"}},{key:"_formatSpeed",value:function(e){return e<=1048576?(e/1024).toFixed(1)+"KB/s":(e/1048576).toFixed(1)+"MB/s"}},{key:"_createCosOptionsWeb",value:function(e){var t=this.tim.context.identifier,n=this._genFileName(t,e.to,e.file.files[0].name);return{files:[{Bucket:"".concat(this.bucketName,"-").concat(this.appid),Region:this.region,Key:"".concat(this.directory,"/").concat(n),Body:e.file.files[0]}],SliceSize:1048576,onProgress:function(t){if("function"==typeof e.onProgress)try{e.onProgress(t.percent)}catch(n){aa.warn("onProgress callback error:"),aa.error(n)}},onFileFinish:function(e,t,n){}}}},{key:"_createCosOptionsWXMiniApp",value:function(e){var t=this.tim.context.identifier,n=this._genFileName(t,e.to,e.file.name),r=e.file.url;return{Bucket:"".concat(this.bucketName,"-").concat(this.appid),Region:this.region,Key:"".concat(this.directory,"/").concat(n),FilePath:r,onProgress:function(t){if(aa.log(JSON.stringify(t)),"function"==typeof e.onProgress)try{e.onProgress(t.percent)}catch(n){aa.warn("onProgress callback error:"),aa.error(n)}}}}},{key:"_genFileName",value:function(e,t,n){return"".concat(e,"-").concat(t,"-").concat(Da(99999),"-").concat(n)}},{key:"reset",value:function(){this._timer&&(clearInterval(this._timer),this._timer=0)}}]),n}(ng),F_=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;return wn(this,n),(r=t.call(this,e)).FILETYPE={SOUND:2106,FILE:2107,VIDEO:2113},r._bdh_download_server="grouptalk.c2c.qq.com",r._BDHBizID=10001,r._authKey="",r._expireTime=0,r.tim.innerEmitter.on(td,r._getAuthKey,Fn(r)),r}return bn(n,[{key:"_getAuthKey",value:function(){var e=this;this.request({name:"bigDataHallwayAuthKey",action:"query"}).then((function(t){t.data.authKey&&(e._authKey=t.data.authKey,e._expireTime=parseInt(t.data.expireTime))}))}},{key:"_isFromOlderVersion",value:function(e){return 2!==e.content.downloadFlag}},{key:"parseElements",value:function(e,t){if(!ha(e)||!t)return[];for(var n=[],r=null,o=0;o<e.length;o++)r=e[o],this._needParse(r)?n.push(this._parseElement(r,t)):n.push(e[o]);return n}},{key:"_needParse",value:function(e){return!(!this._isFromOlderVersion(e)||e.type!==fn.MSG_AUDIO&&e.type!==fn.MSG_FILE&&e.type!==fn.MSG_VIDEO)}},{key:"_parseElement",value:function(e,t){switch(e.type){case fn.MSG_AUDIO:return this._parseAudioElement(e,t);case fn.MSG_FILE:return this._parseFileElement(e,t);case fn.MSG_VIDEO:return this._parseVideoElement(e,t)}}},{key:"_parseAudioElement",value:function(e,t){return e.content.url=this._genAudioUrl(e.content.uuid,t),e}},{key:"_parseFileElement",value:function(e,t){return e.content.url=this._genFileUrl(e.content.uuid,t,e.content.fileName),e}},{key:"_parseVideoElement",value:function(e,t){return e.content.url=this._genVideoUrl(e.content.uuid,t),e}},{key:"_genAudioUrl",value:function(e,t){return""===this._authKey?(aa.warn("BigDataHallwayController._genAudioUrl no authKey!"),""):"https://".concat(this._bdh_download_server,"/asn.com/stddownload_common_file?authkey=").concat(this._authKey,"&bid=").concat(this._BDHBizID,"&subbid=").concat(this.tim.context.SDKAppID,"&fileid=").concat(e,"&filetype=").concat(this.FILETYPE.SOUND,"&openid=").concat(t,"&ver=0")}},{key:"_genFileUrl",value:function(e,t,n){return""===this._authKey?(aa.warn("BigDataHallwayController._genFileUrl no authKey!"),""):(n||(n="".concat(Math.floor(1e5*Math.random()),"-").concat(Date.now())),"https://".concat(this._bdh_download_server,"/asn.com/stddownload_common_file?authkey=").concat(this._authKey,"&bid=").concat(this._BDHBizID,"&subbid=").concat(this.tim.context.SDKAppID,"&fileid=").concat(e,"&filetype=").concat(this.FILETYPE.FILE,"&openid=").concat(t,"&ver=0&filename=").concat(encodeURIComponent(n)))}},{key:"_genVideoUrl",value:function(e,t){return""===this._authKey?(aa.warn("BigDataHallwayController._genVideoUrl no authKey!"),""):"https://".concat(this._bdh_download_server,"/asn.com/stddownload_common_file?authkey=").concat(this._authKey,"&bid=").concat(this._BDHBizID,"&subbid=").concat(this.tim.context.SDKAppID,"&fileid=").concat(e,"&filetype=").concat(this.FILETYPE.VIDEO,"&openid=").concat(t,"&ver=0")}},{key:"reset",value:function(){this._authKey="",this.expireTime=0}}]),n}(ng),j_={app_id:"",event_id:"",api_base:"https://pingtas.qq.com/pingd",prefix:"_mta_",version:"1.3.9",stat_share_app:!1,stat_pull_down_fresh:!1,stat_reach_bottom:!1,stat_param:!0};function B_(){try{var e="s"+H_();return wx.setStorageSync(j_.prefix+"ssid",e),e}catch(n){}}function H_(e){for(var t=[0,1,2,3,4,5,6,7,8,9],n=10;1<n;n--){var r=Math.floor(10*Math.random()),o=t[r];t[r]=t[n-1],t[n-1]=o}for(n=r=0;5>n;n++)r=10*r+t[n];return(e||"")+(r+"")+ +new Date}function V_(){try{var e=getCurrentPages(),t="/";return 0<e.length&&(t=e.pop().__route__),t}catch(r){console.log("get current page path error:"+r)}}function K_(){var e,t={dm:"wechat.apps.xx",url:encodeURIComponent(V_()+z_(W_.Data.pageQuery)),pvi:"",si:"",ty:0};return t.pvi=((e=function(){try{return wx.getStorageSync(j_.prefix+"auid")}catch(t){}}())||(e=function(){try{var t=H_();return wx.setStorageSync(j_.prefix+"auid",t),t}catch(e){}}(),t.ty=1),e),t.si=function(){var e=function(){try{return wx.getStorageSync(j_.prefix+"ssid")}catch(e){}}();return e||(e=B_()),e}(),t}function $_(){var e=function(){var e=wx.getSystemInfoSync();return{adt:encodeURIComponent(e.model),scl:e.pixelRatio,scr:e.windowWidth+"x"+e.windowHeight,lg:e.language,fl:e.version,jv:encodeURIComponent(e.system),tz:encodeURIComponent(e.platform)}}();return function(e){wx.getNetworkType({success:function(t){e(t.networkType)}})}((function(e){try{wx.setStorageSync(j_.prefix+"ntdata",e)}catch(n){}})),e.ct=wx.getStorageSync(j_.prefix+"ntdata")||"4g",e}function Y_(){var e,t=W_.Data.userInfo,n=[];for(e in t)t.hasOwnProperty(e)&&n.push(e+"="+t[e]);return t=n.join(";"),{r2:j_.app_id,r4:"wx",ext:"v="+j_.version+(null!==t&&""!==t?";ui="+encodeURIComponent(t):"")}}function z_(e){if(!j_.stat_param||!e)return"";e=function(e){if(1>j_.ignore_params.length)return e;var t,n={};for(t in e)0<=j_.ignore_params.indexOf(t)||(n[t]=e[t]);return n}(e);var t,n=[];for(t in e)n.push(t+"="+e[t]);return 0<n.length?"?"+n.join("&"):""}var W_={App:{init:function(e){"appID"in e&&(j_.app_id=e.appID),"eventID"in e&&(j_.event_id=e.eventID),"statShareApp"in e&&(j_.stat_share_app=e.statShareApp),"statPullDownFresh"in e&&(j_.stat_pull_down_fresh=e.statPullDownFresh),"statReachBottom"in e&&(j_.stat_reach_bottom=e.statReachBottom),"ignoreParams"in e&&(j_.ignore_params=e.ignoreParams),"statParam"in e&&(j_.stat_param=e.statParam),B_();try{"lauchOpts"in e&&(W_.Data.lanchInfo=e.lauchOpts,W_.Data.lanchInfo.landing=1)}catch(n){}"autoReport"in e&&e.autoReport&&function(){var e=Page;Page=function(t){var n=t.onLoad;t.onLoad=function(e){n&&n.call(this,e),W_.Data.lastPageQuery=W_.Data.pageQuery,W_.Data.pageQuery=e,W_.Data.lastPageUrl=W_.Data.pageUrl,W_.Data.pageUrl=V_(),W_.Data.show=!1,W_.Page.init()},e(t)}}()}},Page:{init:function(){var e,t=getCurrentPages()[getCurrentPages().length-1];t.onShow&&(e=t.onShow,t.onShow=function(){if(!0===W_.Data.show){var t=W_.Data.lastPageQuery;W_.Data.lastPageQuery=W_.Data.pageQuery,W_.Data.pageQuery=t,W_.Data.lastPageUrl=W_.Data.pageUrl,W_.Data.pageUrl=V_()}W_.Data.show=!0,W_.Page.stat(),e.apply(this,arguments)}),j_.stat_pull_down_fresh&&t.onPullDownRefresh&&function(){var e=t.onPullDownRefresh;t.onPullDownRefresh=function(){W_.Event.stat(j_.prefix+"pulldownfresh",{url:t.__route__}),e.apply(this,arguments)}}(),j_.stat_reach_bottom&&t.onReachBottom&&function(){var e=t.onReachBottom;t.onReachBottom=function(){W_.Event.stat(j_.prefix+"reachbottom",{url:t.__route__}),e.apply(this,arguments)}}(),j_.stat_share_app&&t.onShareAppMessage&&function(){var e=t.onShareAppMessage;t.onShareAppMessage=function(){return W_.Event.stat(j_.prefix+"shareapp",{url:t.__route__}),e.apply(this,arguments)}}()},multiStat:function(e,t){if(1==t)W_.Page.stat(e);else{var n=getCurrentPages()[getCurrentPages().length-1];n.onShow&&function(){var t=n.onShow;n.onShow=function(){W_.Page.stat(e),t.call(this,arguments)}}()}},stat:function(e){if(""!=j_.app_id){var t=[],n=Y_();if(e&&(n.r2=e),e=[K_(),n,$_()],W_.Data.lanchInfo){e.push({ht:W_.Data.lanchInfo.scene}),W_.Data.pageQuery&&W_.Data.pageQuery._mta_ref_id&&e.push({rarg:W_.Data.pageQuery._mta_ref_id});try{1==W_.Data.lanchInfo.landing&&(n.ext+=";lp=1",W_.Data.lanchInfo.landing=0)}catch(s){}}e.push({rdm:"/",rurl:0>=W_.Data.lastPageUrl.length?W_.Data.pageUrl+z_(W_.Data.lastPageQuery):encodeURIComponent(W_.Data.lastPageUrl+z_(W_.Data.lastPageQuery))}),e.push({rand:+new Date}),n=0;for(var r=e.length;n<r;n++)for(var o in e[n])e[n].hasOwnProperty(o)&&t.push(o+"="+(void 0===e[n][o]?"":e[n][o]));wx.request({url:j_.api_base+"?"+t.join("&").toLowerCase()})}}},Event:{stat:function(e,t){if(""!=j_.event_id){var n=[],r=K_(),o=Y_();r.dm="wxapps.click",r.url=e,o.r2=j_.event_id;var i,s=void 0===t?{}:t,a=[];for(i in s)s.hasOwnProperty(i)&&a.push(encodeURIComponent(i)+"="+encodeURIComponent(s[i]));for(s=a.join(";"),o.r5=s,s=0,o=(r=[r,o,$_(),{rand:+new Date}]).length;s<o;s++)for(var u in r[s])r[s].hasOwnProperty(u)&&n.push(u+"="+(void 0===r[s][u]?"":r[s][u]));wx.request({url:j_.api_base+"?"+n.join("&").toLowerCase()})}}},Data:{userInfo:null,lanchInfo:null,pageQuery:null,lastPageQuery:null,pageUrl:"",lastPageUrl:"",show:!1}},X_=W_,J_=function(){function e(){wn(this,e),this.cache=[],this.MtaWX=null,this._init()}return bn(e,[{key:"report",value:function(e,t){var n=this;try{Rs?window.MtaH5?(window.MtaH5.clickStat(e,t),this.cache.forEach((function(e){var t=e.name,r=e.param;window.MtaH5.clickStat(t,r),n.cache.shift()}))):this.cache.push({name:e,param:t}):Os&&(this.MtaWX?(this.MtaWX.Event.stat(e,t),this.cache.forEach((function(e){var t=e.name,r=e.param;n.MtaWX.stat(t,r),n.cache.shift()}))):this.cache.push({name:e,param:t}))}catch(dC){}}},{key:"stat",value:function(){try{Rs&&window.MtaH5?window.MtaH5.pgv():Os&&this.MtaWX&&this.MtaWX.Page.stat()}catch(dC){}}},{key:"_init",value:function(){try{if(Rs){window._mtac={autoReport:0};var e=document.createElement("script"),t=Ra();e.src="".concat(t,"//pingjs.qq.com/h5/stats.js?v2.0.4"),e.setAttribute("name","MTAH5"),e.setAttribute("sid","500690998"),e.setAttribute("cid","500691017");var n=document.getElementsByTagName("script")[0];n.parentNode.insertBefore(e,n)}else Os&&(this.MtaWX=X_,this.MtaWX.App.init({appID:"500690995",eventID:"500691014",autoReport:!1,statParam:!0}))}catch(dC){}}}]),e}(),Q_=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;wn(this,n),(r=t.call(this,e)).MTA=new J_;var o=r.tim.innerEmitter;return o.on(Bd,r._stat,Fn(r)),o.on(jd,r._stat,Fn(r)),r}return bn(n,[{key:"_stat",value:function(){this.MTA.report("sdkappid",{value:this.tim.context.SDKAppID}),this.MTA.report("version",{value:hC.VERSION}),this.MTA.stat()}}]),n}(ng),Z_=function(){function e(t){wn(this,e),this._table="timwebii",this._report=[]}return bn(e,[{key:"pushIn",value:function(e){aa.debug("SSOLogBody.pushIn",this._report.length,e),this._report.push(e)}},{key:"backfill",value:function(e){var t;ha(e)&&0!==e.length&&(aa.debug("SSOLogBody.backfill",this._report.length,e.length),(t=this._report).unshift.apply(t,Vn(e)))}},{key:"getLogsNumInMemory",value:function(){return this._report.length}},{key:"isEmpty",value:function(){return 0===this._report.length}},{key:"_reset",value:function(){this._report.length=0,this._report=[]}},{key:"getTable",value:function(){return this._table}},{key:"getLogsInMemory",value:function(){var e=this._report.slice();return this._reset(),e}}]),e}(),eC=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;return wn(this,n),(r=t.call(this,e)).TAG="im-ssolog-event",r._reportBody=new Z_,r._version="2.6.6",r.MIN_THRESHOLD=20,r.MAX_THRESHOLD=100,r.WAITING_TIME=6e4,r.INTERVAL=2e4,r._timerID=0,r._resetLastReportTime(),r._startReportTimer(),r._retryCount=0,r.MAX_RETRY_COUNT=3,r.tim.innerEmitter.on(Rd,r._onLoginSuccess,Fn(r)),r}return bn(n,[{key:"reportAtOnce",value:function(){aa.debug("EventStatController.reportAtOnce"),this._report()}},{key:"_onLoginSuccess",value:function(){var e=this,t=this.tim.storage,n=t.getItem(this.TAG,!1);qa(n)||(aa.log("EventStatController._onLoginSuccess get ssolog in storage, nums="+n.length),n.forEach((function(t){e._reportBody.pushIn(t)})),t.removeItem(this.TAG,!1))}},{key:"pushIn",value:function(e){e instanceof Cg&&(e.setCommonInfo(this.tim.context.SDKAppID,this._version,this.tim.context.tinyID,this.tim.loginInfo.identifier,this.getPlatform()),this._reportBody.pushIn(e),this._reportBody.getLogsNumInMemory()>=this.MIN_THRESHOLD&&this._report())}},{key:"_resetLastReportTime",value:function(){this._lastReportTime=Date.now()}},{key:"_startReportTimer",value:function(){var e=this;this._timerID=setInterval((function(){Date.now()<e._lastReportTime+e.WAITING_TIME||e._reportBody.isEmpty()||e._report()}),this.INTERVAL)}},{key:"_stopReportTimer",value:function(){this._timerID>0&&(clearInterval(this._timerID),this._timerID=0)}},{key:"_report",value:function(){var e=this;if(!this._reportBody.isEmpty()){var t=this._reportBody.getLogsInMemory();this.request({name:"ssoEventStat",action:"create",param:{table:this._reportBody.getTable(),report:t}}).then((function(){e._resetLastReportTime(),e._retryCount>0&&(aa.debug("EventStatController.report retry success"),e._retryCount=0)})).catch((function(n){if(aa.warn("EventStatController.report, online:".concat(e.getNetworkType()," error:").concat(n)),e._reportBody.backfill(t),e._reportBody.getLogsNumInMemory()>e.MAX_THRESHOLD||e._retryCount===e.MAX_RETRY_COUNT||0===e._timerID)return e._retryCount=0,void e._flushAtOnce();e._retryCount+=1}))}}},{key:"_flushAtOnce",value:function(){var e=this.tim.storage,t=e.getItem(this.TAG,!1),n=this._reportBody.getLogsInMemory();if(qa(t))aa.log("EventStatController._flushAtOnce nums="+n.length),e.setItem(this.TAG,n,!0,!1);else{var r=n.concat(t);r.length>this.MAX_THRESHOLD&&(r=r.slice(0,this.MAX_THRESHOLD)),aa.log("EventStatController._flushAtOnce nums="+r.length),e.setItem(this.TAG,r,!0,!1)}}},{key:"reset",value:function(){aa.log("EventStatController.reset"),this._stopReportTimer(),this._report()}}]),n}(ng),tC="none",nC="online",rC=function(){function e(){wn(this,e),this._networkType="",this.maxWaitTime=3e3}return bn(e,[{key:"start",value:function(){var e=this;Os?(wx.getNetworkType({success:function(t){e._networkType=t.networkType,t.networkType===tC?aa.warn("NetMonitor no network, please check!"):aa.info("NetMonitor networkType:".concat(t.networkType))}}),wx.onNetworkStatusChange(this._onWxNetworkStatusChange.bind(this))):this._networkType=nC}},{key:"_onWxNetworkStatusChange",value:function(e){this._networkType=e.networkType,e.isConnected?aa.info("NetMonitor networkType:".concat(e.networkType)):aa.warn("NetMonitor no network, please check!")}},{key:"probe",value:function(){var e=this;return new Promise((function(t,n){if(Os)wx.getNetworkType({success:function(n){e._networkType=n.networkType,n.networkType===tC?(aa.warn("NetMonitor no network, please check!"),t([!1,n.networkType])):(aa.info("NetMonitor networkType:".concat(n.networkType)),t([!0,n.networkType]))}});else if(window&&window.fetch)fetch("".concat(Ra(),"//webim-**********.file.myqcloud.com/assets/test/speed.xml?random=").concat(Math.random())).then((function(e){e.ok?t([!0,nC]):t([!1,tC])})).catch((function(e){t([!1,tC])}));else{var r=new XMLHttpRequest,o=setTimeout((function(){aa.warn("NetMonitor fetch timeout. Probably no network, please check!"),r.abort(),e._networkType=tC,t([!1,tC])}),e.maxWaitTime);r.onreadystatechange=function(){4===r.readyState&&(clearTimeout(o),200===r.status||304===r.status?(this._networkType=nC,t([!0,nC])):(aa.warn("NetMonitor fetch status:".concat(r.status,". Probably no network, please check!")),this._networkType=tC,t([!1,tC])))},r.open("GET","".concat(Ra(),"//webim-**********.file.myqcloud.com/assets/test/speed.xml?random=").concat(Math.random())),r.send()}}))}},{key:"getNetworkType",value:function(){return this._networkType}},{key:"reset",value:function(){this._networkType=""}}]),e}(),oC=function(){function e(t){var n=this;wn(this,e),ha(t)?(this._map=new Map,t.forEach((function(e){n._map.set(e,[])}))):aa.warn("AverageCalculator.constructor need keys")}return bn(e,[{key:"push",value:function(e,t){return!(da(e)||!this._map.has(e)||!ca(t))&&(this._map.get(e).push(t),!0)}},{key:"getSize",value:function(e){return da(e)||!this._map.has(e)?-1:this._map.get(e).length}},{key:"getAvg",value:function(e){if(da(e)||!this._map.has(e))return-1;var t=this._map.get(e),n=t.length;if(0===n)return 0;var r=0;return t.forEach((function(e){r+=e})),t.length=0,this._map.set(e,[]),parseInt(r/n)}},{key:"getMax",value:function(e){return da(e)||!this._map.has(e)?-1:Math.max.apply(null,this._map.get(e))}},{key:"getMin",value:function(e){return da(e)||!this._map.has(e)?-1:Math.min.apply(null,this._map.get(e))}},{key:"reset",value:function(){this._map.forEach((function(e){e.length=0}))}}]),e}(),iC=function(){function e(t){var n=this;wn(this,e),ha(t)?(this._map=new Map,t.forEach((function(e){n._map.set(e,{totalCount:0,successCount:0})}))):aa.warn("SuccessRateCalculator.constructor need keys")}return bn(e,[{key:"addTotalCount",value:function(e){return!(da(e)||!this._map.has(e))&&(this._map.get(e).totalCount+=1,!0)}},{key:"addSuccessCount",value:function(e){return!(da(e)||!this._map.has(e))&&(this._map.get(e).successCount+=1,!0)}},{key:"getSuccessRate",value:function(e){if(da(e)||!this._map.has(e))return-1;var t=this._map.get(e);if(0===t.totalCount)return 1;var n=parseFloat((t.successCount/t.totalCount).toFixed(2));return t.totalCount=t.successCount=0,n}},{key:"getTotalCount",value:function(e){return da(e)||!this._map.has(e)?-1:this._map.get(e).totalCount}},{key:"reset",value:function(){this._map.forEach((function(e){e.totalCount=0,e.successCount=0}))}}]),e}(),sC=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;return wn(this,n),(r=t.call(this,e)).TABLE="timwebsum",r.TAG="im-ssolog-sumstat",r._items=[fg,hg,dg,gg,mg],r._thresholdMap=new Map,r._thresholdMap.set(fg,100),r._thresholdMap.set(hg,150),r._thresholdMap.set(dg,15),r._thresholdMap.set(gg,50),r._thresholdMap.set(mg,50),r._lpID="",r._platform=r.getPlatform(),r._lastReportTime=0,r._statInfoArr=[],r._retryCount=0,r._avgCalc=new oC(r._items),r._successRateCalc=new iC(r._items),r.tim.innerEmitter.on(Rd,r._onLoginSuccess,Fn(r)),r}return bn(n,[{key:"_onLoginSuccess",value:function(){var e=this,t=this.tim.storage,n=t.getItem(this.TAG,!1);qa(n)||(aa.log("SumStatController._onLoginSuccess get sumstatlog in storage, nums="+n.length),n.forEach((function(t){e._statInfoArr.pushIn(t)})),t.removeItem(this.TAG,!1))}},{key:"recordLongPollingID",value:function(e){this._lpID=e}},{key:"addTotalCount",value:function(e){this._successRateCalc.addTotalCount(e)?1===this._successRateCalc.getTotalCount(e)&&(this._lastReportTime=Date.now()):aa.warn("SumStatController.addTotalCount invalid key:",e)}},{key:"addSuccessCount",value:function(e){this._successRateCalc.addSuccessCount(e)||aa.warn("SumStatController.addSuccessCount invalid key:",e)}},{key:"addCost",value:function(e,t){this._avgCalc.push(e,t)?(aa.debug("SumStatController.addCost",e,t,this._avgCalc.getSize(e)),this._avgCalc.getSize(e)>=this._thresholdMap.get(e)&&this._report(e)):aa.warn("SumStatController.addCost invalid key or cost:",e,t)}},{key:"_getItemNum",value:function(e){switch(e){case fg:return 1;case hg:return 2;case dg:return 3;case gg:return 4;case mg:return 5;default:return 100}}},{key:"_getStatInfo",value:function(e){var t=null;return this._avgCalc.getSize(e)>0&&(t={SDKAppID:"".concat(this.tim.context.SDKAppID),version:"".concat("2.6.6"),tinyID:this.tim.context.tinyID,userID:this.tim.loginInfo.identifier,item:this._getItemNum(e),lpID:e===fg?this._lpID:"",platform:this._platform,networkType:this.getNetworkType(),total:this._successRateCalc.getTotalCount(e),successRate:this._successRateCalc.getSuccessRate(e),avg:this._avgCalc.getAvg(e),timespan:Date.now()-this._lastReportTime,time:Ta()}),t}},{key:"_report",value:function(e){var t=this,n=[],r=null;da(e)?this._items.forEach((function(e){null!==(r=t._getStatInfo(e))&&n.push(r)})):null!==(r=this._getStatInfo(e))&&n.push(r),aa.debug("SumStatController._report",n),this._statInfoArr.length>0&&(n=n.concat(this.statInfoArr),this._statInfoArr=[]),this._doReport(n)}},{key:"_doReport",value:function(e){var t=this;qa(e)?aa.warn("SumStatController._doReport statInfoArr is empty, do nothing"):this.request({name:"ssoSumStat",action:"create",param:{table:this.TABLE,report:e}}).then((function(){t._lastReportTime=Date.now(),t._retryCount>0&&(aa.debug("SumStatController._doReport retry success"),t._retryCount=0)})).catch((function(n){aa.warn("SumStatController._doReport, online:".concat(t.getNetworkType()," error:"),n,e),t._retryCount<=1?setTimeout((function(){aa.info("SumStatController._doReport retry",e),t._retryCount+=1,t._doReport(e)}),5e3):(t._retryCount=0,t._statInfoArr=t._statInfoArr.concat(e),t._flusgAtOnce())}))}},{key:"_flushAtOnce",value:function(){var e=this.tim.storage,t=e.getItem(this.TAG,!1),n=this._statInfoArr;if(qa(t))aa.log("SumStatController._flushAtOnce nums="+n.length),e.setItem(this.TAG,n,!0,!1);else{var r=n.concat(t);r.length>10&&(r=r.slice(0,10)),aa.log("SumStatController._flushAtOnce nums="+r.length),e.setItem(this.TAG,r,!0,!1)}this._statInfoArr=[]}},{key:"reset",value:function(){aa.info("SumStatController.reset"),this._report(),this._avgCalc.reset(),this._successRateCalc.reset()}}]),n}(ng),aC=function(){function e(){wn(this,e),this._funcMap=new Map}return bn(e,[{key:"defense",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;if("string"!=typeof e)return null;if(0===e.length)return null;if("function"!=typeof t)return null;if(this._funcMap.has(e)&&this._funcMap.get(e).has(t))return this._funcMap.get(e).get(t);this._funcMap.has(e)||this._funcMap.set(e,new Map);var r=null;return this._funcMap.get(e).has(t)?r=this._funcMap.get(e).get(t):(r=this._pack(e,t,n),this._funcMap.get(e).set(t,r)),r}},{key:"defenseOnce",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;return"function"!=typeof t?null:this._pack(e,t,n)}},{key:"find",value:function(e,t){return"string"!=typeof e||0===e.length||"function"!=typeof t?null:this._funcMap.has(e)?this._funcMap.get(e).has(t)?this._funcMap.get(e).get(t):(aa.log("SafetyCallback.find: 找不到 func —— ".concat(e,"/").concat(""!==t.name?t.name:"[anonymous]")),null):(aa.log("SafetyCallback.find: 找不到 eventName-".concat(e," 对应的 func")),null)}},{key:"delete",value:function(e,t){return"function"==typeof t&&!!this._funcMap.has(e)&&!!this._funcMap.get(e).has(t)&&(this._funcMap.get(e).delete(t),0===this._funcMap.get(e).size&&this._funcMap.delete(e),!0)}},{key:"_pack",value:function(e,t,n){return function(){try{t.apply(n,Array.from(arguments))}catch(i){var r=new Cg;r.setMethod(Im).setText("eventName=".concat(e)).setStart(),r.setCode(0).setMessage(i.message).setEnd()}}}}]),e}(),uC=function(e){Nn(n,e);var t=Bn(n);function n(e){var r;return wn(this,n),(r=t.call(this,e))._maybeLostSequencesMap=new Map,r}return bn(n,[{key:"onMessageMaybeLost",value:function(e,t,n){this._maybeLostSequencesMap.has(e)||this._maybeLostSequencesMap.set(e,[]);for(var r=this._maybeLostSequencesMap.get(e),o=0;o<n;o++)r.push(t+o);aa.debug("MessageLossController.onMessageMaybeLost. maybeLostSequences:".concat(r))}},{key:"detectMessageLoss",value:function(e,t){var n=this._maybeLostSequencesMap.get(e);if(!qa(n)&&!qa(t)){var r=t.filter((function(e){return-1!==n.indexOf(e)}));if(aa.debug("MessageLossController.detectMessageLoss. matchedSequences:".concat(r)),n.length===r.length)aa.info("MessageLossController.detectMessageLoss no message loss. conversationID=".concat(e));else{var o,i=n.filter((function(e){return-1===r.indexOf(e)})),s=i.length;s<=5?o=e+"-"+i.join("-"):(i.sort((function(e,t){return e-t})),o=e+" start:"+i[0]+" end:"+i[s-1]+" count:"+s);var a=new Cg;a.setMethod(dm).setStart(),a.setCode(0).setText(o).setNetworkType(this.getNetworkType()).setEnd(),aa.warn("MessageLossController.detectMessageLoss message loss detected. conversationID:".concat(e," lostSequences:").concat(i))}n.length=0}}},{key:"reset",value:function(){aa.log("MessageLossController.reset"),this._maybeLostSequencesMap.clear()}}]),n}(ng),cC=function(){function e(t){wn(this,e);var n=new Cg;n.setMethod(Ig).setStart(),tg.mixin(this),this._initOptions(t),this._initMemberVariables(),this._initControllers(),this._initListener(),Cg.bindController(this.eventStatController),n.setCode(0).setText("mp=".concat(Os,"-ua=").concat(Ls)).setEnd(),aa.info("SDK inWxMiniApp:".concat(Os,", SDKAppID:").concat(t.SDKAppID,", UserAgent:").concat(Ls)),this._safetyCallbackFactory=new aC}return bn(e,[{key:"login",value:function(e){return aa.time(lg),this._ssoLog=new Cg,this._ssoLog.setMethod(Mg).setStart(),this.netMonitor.start(),this.loginInfo.identifier=e.identifier||e.userID,this.loginInfo.userSig=e.userSig,this.signController.login(this.loginInfo)}},{key:"logout",value:function(){var e=this.signController.logout();return this.resetSDK(),e}},{key:"on",value:function(e,t,n){e===pn.GROUP_SYSTEM_NOTICE_RECEIVED&&aa.warn("！！！TIM.EVENT.GROUP_SYSTEM_NOTICE_RECEIVED v2.6.0起弃用，为了更好的体验，请在 TIM.EVENT.MESSAGE_RECEIVED 事件回调内接收处理群系统通知，详细请参考：https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/Message.html#.GroupSystemNoticePayload"),aa.debug("on","eventName:".concat(e)),this.outerEmitter.on(e,this._safetyCallbackFactory.defense(e,t,n),n)}},{key:"once",value:function(e,t,n){aa.debug("once","eventName:".concat(e)),this.outerEmitter.once(e,this._safetyCallbackFactory.defenseOnce(e,t,n),n||this)}},{key:"off",value:function(e,t,n,r){aa.debug("off","eventName:".concat(e));var o=this._safetyCallbackFactory.find(e,t);null!==o&&(this.outerEmitter.off(e,o,n,r),this._safetyCallbackFactory.delete(e,t))}},{key:"registerPlugin",value:function(e){var t=this;this.plugins||(this.plugins={}),Object.keys(e).forEach((function(n){t.plugins[n]=e[n]}));var n=new Cg;n.setMethod(kg).setStart(),n.setCode(0).setText("key=".concat(Object.keys(e))).setEnd()}},{key:"getPlugin",value:function(e){return this.plugins[e]||void 0}},{key:"setLogLevel",value:function(e){e<=0&&(console.log([""," ________  ______  __       __  __       __  ________  _______","|        \\|      \\|  \\     /  \\|  \\  _  |  \\|        \\|       \\"," \\$$$$$$$$ \\$$$$$$| $$\\   /  $$| $$ / \\ | $$| $$$$$$$$| $$$$$$$\\","   | $$     | $$  | $$$\\ /  $$$| $$/  $\\| $$| $$__    | $$__/ $$","   | $$     | $$  | $$$$\\  $$$$| $$  $$$\\ $$| $$  \\   | $$    $$","   | $$     | $$  | $$\\$$ $$ $$| $$ $$\\$$\\$$| $$$$$   | $$$$$$$\\","   | $$    _| $$_ | $$ \\$$$| $$| $$$$  \\$$$$| $$_____ | $$__/ $$","   | $$   |   $$ \\| $$  \\$ | $$| $$$    \\$$$| $$     \\| $$    $$","    \\$$    \\$$$$$$ \\$$      \\$$ \\$$      \\$$ \\$$$$$$$$ \\$$$$$$$","",""].join("\n")),console.log("%cIM 智能客服，随时随地解决您的问题 →_→ https://cloud.tencent.com/act/event/smarty-service?from=im-doc","color:#ff0000"),console.log(["","参考以下文档，会更快解决问题哦！(#^.^#)\n","SDK 更新日志: https://cloud.tencent.com/document/product/269/38492\n","SDK 接口文档: https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html\n","常见问题: https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/tutorial-01-faq.html\n","反馈问题？戳我提 issue: https://github.com/tencentyun/TIMSDK/issues\n","如果您需要在生产环境关闭上面的日志，请 tim.setLogLevel(1)\n"].join("\n"))),aa.setLevel(e)}},{key:"downloadLog",value:function(){var e=document.createElement("a"),t=new Date,n=new Blob(this.getLog());e.download="TIM-"+t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate()+"-"+this.loginInfo.SDKAppID+"-"+this.context.identifier+".txt",e.href=URL.createObjectURL(n),e.click(),URL.revokeObjectURL(n)}},{key:"destroy",value:function(){this.logout(),this.outerEmitter.emit(pn.SDK_DESTROY,{SDKAppID:this.loginInfo.SDKAppID})}},{key:"createTextMessage",value:function(e){return this.messageController.createTextMessage(e)}},{key:"createImageMessage",value:function(e){return this.messageController.createImageMessage(e)}},{key:"createAudioMessage",value:function(e){return this.messageController.createAudioMessage(e)}},{key:"createVideoMessage",value:function(e){return this.messageController.createVideoMessage(e)}},{key:"createCustomMessage",value:function(e){return this.messageController.createCustomMessage(e)}},{key:"createFaceMessage",value:function(e){return this.messageController.createFaceMessage(e)}},{key:"createFileMessage",value:function(e){return this.messageController.createFileMessage(e)}},{key:"sendMessage",value:function(e,t){return e instanceof Bh?this.messageController.sendMessageInstance(e,t):cg(new _p({code:Ap,message:Ff}))}},{key:"revokeMessage",value:function(e){return this.messageController.revokeMessage(e)}},{key:"resendMessage",value:function(e){return this.messageController.resendMessage(e)}},{key:"getMessageList",value:function(e){return this.messageController.getMessageList(e)}},{key:"setMessageRead",value:function(e){return this.messageController.setMessageRead(e)}},{key:"getConversationList",value:function(){return this.conversationController.getConversationList()}},{key:"getConversationProfile",value:function(e){return this.conversationController.getConversationProfile(e)}},{key:"deleteConversation",value:function(e){return this.conversationController.deleteConversation(e)}},{key:"getMyProfile",value:function(){return this.userController.getMyProfile()}},{key:"getUserProfile",value:function(e){return this.userController.getUserProfile(e)}},{key:"updateMyProfile",value:function(e){return this.userController.updateMyProfile(e)}},{key:"getFriendList",value:function(){return this.userController.getFriendList()}},{key:"deleteFriend",value:function(e){return this.userController.deleteFriend(e)}},{key:"getBlacklist",value:function(){return this.userController.getBlacklist()}},{key:"addToBlacklist",value:function(e){return this.userController.addBlacklist(e)}},{key:"removeFromBlacklist",value:function(e){return this.userController.deleteBlacklist(e)}},{key:"getGroupList",value:function(e){return this.groupController.getGroupList(e)}},{key:"getGroupProfile",value:function(e){return this.groupController.getGroupProfile(e)}},{key:"createGroup",value:function(e){return this.groupController.createGroup(e)}},{key:"dismissGroup",value:function(e){return this.groupController.dismissGroup(e)}},{key:"updateGroupProfile",value:function(e){return this.groupController.updateGroupProfile(e)}},{key:"joinGroup",value:function(e){return this.groupController.joinGroup(e)}},{key:"quitGroup",value:function(e){return this.groupController.quitGroup(e)}},{key:"searchGroupByID",value:function(e){return this.groupController.searchGroupByID(e)}},{key:"changeGroupOwner",value:function(e){return this.groupController.changeGroupOwner(e)}},{key:"handleGroupApplication",value:function(e){return this.groupController.handleGroupApplication(e)}},{key:"setMessageRemindType",value:function(e){return this.groupController.setMessageRemindType(e)}},{key:"getGroupMemberList",value:function(e){return this.groupController.getGroupMemberList(e)}},{key:"getGroupMemberProfile",value:function(e){return this.groupController.getGroupMemberProfile(e)}},{key:"addGroupMember",value:function(e){return this.groupController.addGroupMember(e)}},{key:"deleteGroupMember",value:function(e){return this.groupController.deleteGroupMember(e)}},{key:"setGroupMemberMuteTime",value:function(e){return this.groupController.setGroupMemberMuteTime(e)}},{key:"setGroupMemberRole",value:function(e){return this.groupController.setGroupMemberRole(e)}},{key:"setGroupMemberNameCard",value:function(e){return this.groupController.setGroupMemberNameCard(e)}},{key:"setGroupMemberCustomField",value:function(e){return this.groupController.setGroupMemberCustomField(e)}},{key:"_initOptions",value:function(e){this.plugins={};var t=e.SDKAppID||0,n=Da();this.context={SDKAppID:t,accountType:n},this.loginInfo={SDKAppID:t,accountType:n,identifier:null,userSig:null},this.options={runLoopNetType:e.runLoopNetType||Ql,enablePointer:e.enablePointer||!1}}},{key:"_initMemberVariables",value:function(){this.innerEmitter=new R_,this.outerEmitter=new R_,ag(this.outerEmitter),this.packageConfig=new L_(this),this.storage=new b_(this),this.netMonitor=new rC,this.outerEmitter._emit=this.outerEmitter.emit,this.outerEmitter.emit=function(e,t){var n=arguments[0],r=[n,{name:arguments[0],data:arguments[1]}];aa.debug("emit outer event:".concat(n),r[1]),this.outerEmitter._emit.apply(this.outerEmitter,r)}.bind(this),this.innerEmitter._emit=this.innerEmitter.emit,this.innerEmitter.emit=function(e,t){var n;fa(arguments[1])&&arguments[1].data?(aa.warn("inner eventData has data property, please check!"),n=[e,{name:arguments[0],data:arguments[1].data}]):n=[e,{name:arguments[0],data:arguments[1]}],aa.debug("emit inner event:".concat(e),n[1]),this.innerEmitter._emit.apply(this.innerEmitter,n)}.bind(this)}},{key:"_initControllers",value:function(){this.exceptionController=new qy(this),this.connectionController=new Uy(this),this.contextController=new og(this),this.context=this.contextController.getContext(),this.signController=new Sm(this),this.messageController=new I_(this),this.conversationController=new o_(this),this.userController=new zy(this),this.groupController=new k_(this),this.notificationController=new U_(this),this.bigDataHallwayController=new F_(this),this.statusController=new w_(this),this.uploadController=new q_(this),this.messageLossController=new uC(this),this.eventStatController=new eC(this),this.sumStatController=new sC(this),this.mtaReportController=new Q_(this),this._initReadyListener()}},{key:"_initListener",value:function(){var e=this;if(this.innerEmitter.on(md,this._onSlowStart,this),Os&&"function"==typeof wx.onAppShow&&"function"==typeof wx.onAppHide){var t=null;wx.onAppHide((function(){(t=new Cg).setMethod(Cm).setStart()})),wx.onAppShow((function(){null!==t&&t.setCode(0).setNetworkType(e.netMonitor.getNetworkType()).setEnd()}))}}},{key:"_initReadyListener",value:function(){for(var e=this,t=this.readyList,n=0,r=t.length;n<r;n++)this[t[n]].ready((function(){return e._readyHandle()}))}},{key:"_onSlowStart",value:function(){aa.log("slow start longpolling..."),this.resetSDK(),this.login(this.loginInfo)}},{key:"resetSDK",value:function(){var e=this;this.initList.forEach((function(t){e[t].reset&&e[t].reset()})),this.netMonitor.reset(),this.storage.reset(),this.resetReady(),this._initReadyListener(),this.outerEmitter.emit(pn.SDK_NOT_READY)}},{key:"_readyHandle",value:function(){for(var e=this.readyList,t=!0,n=0,r=e.length;n<r;n++)if(!this[e[n]].isReady()){t=!1;break}if(t){var o=aa.timeEnd(lg);aa.warn("SDK is ready. cost=".concat(o,"ms")),this.triggerReady(),this.innerEmitter.emit(Bd),this.outerEmitter.emit(pn.SDK_READY),this._ssoLog.setCode(0).setNetworkType(this.netMonitor.getNetworkType()).setText(o).setEnd()}}}]),e}();cC.prototype.readyList=["conversationController"],cC.prototype.initList=["exceptionController","connectionController","signController","contextController","messageController","conversationController","userController","groupController","notificationController","eventStatController","sumStatController","messageLossController","statusController"];var lC={login:"login",on:"on",off:"off",ready:"ready",setLogLevel:"setLogLevel",joinGroup:"joinGroup",quitGroup:"quitGroup",registerPlugin:"registerPlugin"};function pC(e,t){return!(!e.isReady()&&void 0===lC[t])||(e.innerEmitter.emit(Pd,new _p({code:kf,message:"".concat(t," ").concat(Nh,"，请参考 https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/module-EVENT.html#.SDK_READY")})),!1)}var fC={},hC={create:function(e){if(e.SDKAppID&&fC[e.SDKAppID])return fC[e.SDKAppID];aa.log("TIM.create");var t=new cC(e);t.on(pn.SDK_DESTROY,(function(e){fC[e.data.SDKAppID]=null,delete fC[e.data.SDKAppID]}));var n=function(e){var t=Object.create(null);return Object.keys(Kh).forEach((function(n){if(e[n]){var r=Kh[n],o=new zn;t[r]=function(){var t=Array.from(arguments);return o.use((function(t,r){if(pC(e,n))return r()})).use((function(e,t){if(!0===Fa(e,Vh[n],r))return t()})).use((function(t,r){return e[n].apply(e,t)})),o.run(t)}}})),t}(t);return fC[e.SDKAppID]=n,aa.log("TIM.create ok"),n}};return hC.TYPES=fn,hC.EVENT=pn,hC.VERSION="2.6.6",aa.log("TIM.VERSION: ".concat(hC.VERSION)),hC}))}).call(this,n("9edd"))}}]);
server {
    listen 80;
    server_name localhost www.zhongnanhui.vip;

    # 项目根目录
    root C:/Users/<USER>/Desktop/fsdownload/www.zhongnanhui.vip;
    index index.php index.html index.htm;

    # 客户端最大上传文件大小
    client_max_body_size 50m;

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|css|js|ico|svg|woff2?|ttf|eot)$ {
        expires 30d;
        access_log off;
        try_files $uri =404;
    }

    # 主要路由处理
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP文件处理
    location ~ \.php$ {
        include        fastcgi_params;
        fastcgi_index  index.php;
        fastcgi_param  SCRIPT_FILENAME $document_root$fastcgi_script_name;
        # 假设PHP-FPM运行在9000端口，如果不是请修改
        fastcgi_pass   127.0.0.1:9000;
        fastcgi_read_timeout 300;
    }

    # 安全配置 - 禁止访问敏感文件和目录
    location ~* /(vendor|storage|\.env|composer\.(json|lock)|package(-lock)?\.json|node_modules|artisan|bootstrap/cache)$ {
        deny all;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
    }

    # 日志配置
    access_log  D:/nginx-1.29.1/logs/zhongnanhui_access.log;
    error_log   D:/nginx-1.29.1/logs/zhongnanhui_error.log;
}

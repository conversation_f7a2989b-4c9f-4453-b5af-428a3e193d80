services:
  PhpMy<PERSON>dmin\Controllers\AjaxController:
    class: 'PhpMyAdmin\Controllers\AjaxController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      config: '@config'

  PhpMyAdmin\Controllers\BrowseForeignersController:
    class: 'PhpMyAdmin\Controllers\BrowseForeignersController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      browseForeigners: '@browse_foreigners'
      relations: '@relation'

  PhpMyAdmin\Controllers\Database\CentralColumnsController:
    class: 'PhpMyAdmin\Controllers\Database\CentralColumnsController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'
      centralColumns: '@central_columns'

  PhpMyAdmin\Controllers\Database\DataDictionaryController:
    class: 'PhpMyAdmin\Controllers\Database\DataDictionaryController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'
      relation: '@relation'
      transformations: '@transformations'

  PhpMyAdmin\Controllers\Database\EventsController:
    class: 'PhpMyAdmin\Controllers\Database\EventsController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'

  PhpMyAdmin\Controllers\Database\MultiTableQueryController:
    class: 'PhpMyAdmin\Controllers\Database\MultiTableQueryController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'

  PhpMyAdmin\Controllers\Database\RoutinesController:
    class: 'PhpMyAdmin\Controllers\Database\RoutinesController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'

  PhpMyAdmin\Controllers\Database\SqlController:
    class: 'PhpMyAdmin\Controllers\Database\SqlController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'

  PhpMyAdmin\Controllers\Database\StructureController:
    class: 'PhpMyAdmin\Controllers\Database\StructureController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'
      relation: '@relation'
      replication: '@replication'

  PhpMyAdmin\Controllers\Database\TriggersController:
    class: 'PhpMyAdmin\Controllers\Database\TriggersController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'

  PhpMyAdmin\Controllers\HomeController:
    class: 'PhpMyAdmin\Controllers\HomeController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      config: '@config'
      themeManager: '@theme_manager'

  PhpMyAdmin\Controllers\Server\DatabasesController:
    class: 'PhpMyAdmin\Controllers\Server\DatabasesController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'

  PhpMyAdmin\Controllers\Server\BinlogController:
    class: 'PhpMyAdmin\Controllers\Server\BinlogController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'

  PhpMyAdmin\Controllers\Server\CollationsController:
    class: 'PhpMyAdmin\Controllers\Server\CollationsController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'

  PhpMyAdmin\Controllers\Server\EnginesController:
    class: 'PhpMyAdmin\Controllers\Server\EnginesController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'

  PhpMyAdmin\Controllers\Server\PluginsController:
    class: 'PhpMyAdmin\Controllers\Server\PluginsController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      plugins: '@server_plugins'

  PhpMyAdmin\Controllers\Server\ReplicationController:
    class: 'PhpMyAdmin\Controllers\Server\ReplicationController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'

  PhpMyAdmin\Controllers\Server\SqlController:
    class: 'PhpMyAdmin\Controllers\Server\SqlController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'

  PhpMyAdmin\Controllers\Server\Status\AdvisorController:
    class: 'PhpMyAdmin\Controllers\Server\Status\AdvisorController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      data: '@status_data'
      advisor: '@advisor'

  PhpMyAdmin\Controllers\Server\Status\MonitorController:
    class: 'PhpMyAdmin\Controllers\Server\Status\MonitorController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      data: '@status_data'
      monitor: '@status_monitor'

  PhpMyAdmin\Controllers\Server\Status\ProcessesController:
    class: 'PhpMyAdmin\Controllers\Server\Status\ProcessesController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      data: '@status_data'

  PhpMyAdmin\Controllers\Server\Status\QueriesController:
    class: 'PhpMyAdmin\Controllers\Server\Status\QueriesController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      data: '@status_data'

  PhpMyAdmin\Controllers\Server\Status\StatusController:
    class: 'PhpMyAdmin\Controllers\Server\Status\StatusController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      data: '@status_data'

  PhpMyAdmin\Controllers\Server\Status\VariablesController:
    class: 'PhpMyAdmin\Controllers\Server\Status\VariablesController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      data: '@status_data'

  PhpMyAdmin\Controllers\Server\VariablesController:
    class: 'PhpMyAdmin\Controllers\Server\VariablesController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'

  PhpMyAdmin\Controllers\Table\ChartController:
    class: 'PhpMyAdmin\Controllers\Table\ChartController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'
      table: '%table%'
      sql_query: null
      url_query: null
      cfg: null

  PhpMyAdmin\Controllers\Table\GisVisualizationController:
    class: 'PhpMyAdmin\Controllers\Table\GisVisualizationController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'
      table: '%table%'
      sql_query: null
      url_params: null
      goto: null
      back: null
      visualizationSettings: null

  PhpMyAdmin\Controllers\Table\IndexesController:
    class: 'PhpMyAdmin\Controllers\Table\IndexesController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'
      table: '%table%'
      index: '%index%'

  PhpMyAdmin\Controllers\Table\RelationController:
    class: 'PhpMyAdmin\Controllers\Table\RelationController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'
      table: '%table%'
      options_array: null
      cfgRelation: null
      tbl_storage_engine: null
      existrel: null
      existrel_foreign: null
      upd_query: null
      relation: '@relation'

  PhpMyAdmin\Controllers\Table\SearchController:
    class: 'PhpMyAdmin\Controllers\Table\SearchController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'
      table: '%table%'
      searchType: null
      url_query: null
      relation: '@relation'

  PhpMyAdmin\Controllers\Table\SqlController:
    class: 'PhpMyAdmin\Controllers\Table\SqlController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'
      table: '%table%'

  PhpMyAdmin\Controllers\Table\StructureController:
    class: 'PhpMyAdmin\Controllers\Table\StructureController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      db: '%db%'
      table: '%table%'
      db_is_system_schema: null
      tbl_is_view: null
      tbl_storage_engine: null
      table_info_num_rows: null
      tbl_collation: null
      showtable: null
      relation: '@relation'
      transformations: '@transformations'
      create_add_field: '@create_add_field'

  PhpMyAdmin\Controllers\TransformationOverviewController:
    class: 'PhpMyAdmin\Controllers\TransformationOverviewController'
    arguments:
      response: '@response'
      dbi: '@dbi'
      template: '@template'
      transformations: '@transformations'

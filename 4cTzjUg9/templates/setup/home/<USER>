{% extends 'setup/base.twig' %}
{% block content %}

<form id="select_lang" method="post">
  {{ get_hidden_inputs() }}
  <bdo lang="en" dir="ltr">
    <label for="lang">
      {% trans 'Language' %}
      {{ 'Language'|trans != 'Language' ? ' - Language' }}
    </label>
  </bdo>
  <br>
  <select id="lang" name="lang" class="autosubmit" lang="en" dir="ltr">
    {% for language in languages %}
      <option value="{{ language.code }}"{{ language.is_active ? ' selected' }}>{{ language.name|raw }}</option>
    {% endfor %}
  </select>
</form>

<h2>{% trans 'Overview' %}</h2>

<a href="#" id="show_hidden_messages" class="hide">
  {% trans 'Show hidden messages' %} (#MSG_COUNT)
</a>

{% for message in messages %}
  <div class="{{ message.type }}{{ message.is_hidden ? ' hiddenmessage' }}" id="{{ message.id }}">
    <h4>{{ message.title }}</h4>
    {{ message.message|raw }}
  </div>
{% endfor %}

<fieldset class="simple">
  <legend>{% trans 'Servers' %}</legend>

  {{ servers_form_top_html|raw }}

  <div class="form">
    {% if server_count > 0 %}
      <table cellspacing="0" class="datatable">
        <tr>
          <th>#</th>
          <th>{% trans 'Name' %}</th>
          <th>{% trans 'Authentication type' %}</th>
          <th colspan="2">DSN</th>
        </tr>

        {% for server in servers %}
          <tr>
            <td>{{ server.id }}</td>
            <td>{{ server.name }}</td>
            <td>{{ server.auth_type }}</td>
            <td>{{ server.dsn }}</td>
            <td class="nowrap">
              <small>
                <a href="{{ get_common(server.params.edit) }}">
                  {% trans 'Edit' %}
                </a>
                |
                <a class="delete-server" href="{{ get_common(server.params.remove) }}" data-post="
                  {{- get_common({ token: server.params.token }, '') }}">
                  {% trans 'Delete' %}
                </a>
              </small>
            </td>
          </tr>
        {% endfor %}
      </table>
    {% else %}
      <table width="100%">
        <tr>
          <td>
            <em>{% trans 'There are no configured servers' %}</em>
          </td>
        </tr>
      </table>
    {% endif %}

    <table width="100%">
      <tr>
        <td class="lastrow left">
          <input type="submit" name="submit" value="{% trans 'New server' %}">
        </td>
      </tr>
    </table>
  </div>

  {{ form_bottom_html|raw }}

</fieldset>

<fieldset class="simple">
  <legend>{% trans 'Configuration file' %}</legend>

  {{ config_form_top_html|raw }}

  <table width="100%" cellspacing="0">
    {{ default_language_input|raw }}
    {{ server_default_input|raw }}
    {{ eol_input|raw }}

    <tr>
      <td colspan="2" class="lastrow left">
        <input type="submit" name="submit_display" value="{% trans 'Display' %}">
        <input type="submit" name="submit_download" value="{% trans 'Download' %}">
        <input class="red" type="submit" name="submit_clear" value="{% trans 'Clear' %}">
      </td>
    </tr>
  </table>

  {{ form_bottom_html|raw }}

</fieldset>

<div id="footer">
  <a href="../url.php?url=https://www.phpmyadmin.net/">{% trans 'phpMyAdmin homepage' %}</a>
  <a href="../url.php?url=https://www.phpmyadmin.net/donate/">{% trans 'Donate' %}</a>
  <a href="{{ get_common({'version_check': '1'}) }}">{% trans 'Check for latest version' %}</a>
</div>

{% endblock %}

language: php

before_install:
    - openssl aes-256-cbc -K $encrypted_ab8f92ccaef0_key -iv $encrypted_ab8f92ccaef0_iv -in scripts/sudo-bot/keys.tar.enc -out scripts/sudo-bot/keys.tar -d
    - tar -C scripts/sudo-bot/ -xvf scripts/sudo-bot/keys.tar
before_script:
    - './scripts/ci/install-reporters.sh'
    - './scripts/ci/install.sh'
script:
    - ./scripts/ci/ci-$CI_TYPE.sh
install:
    - if [ "$CI_COMPOSER" = "yes" ]; then composer install --ansi --prefer-dist --no-interaction --optimize-autoloader --no-suggest --no-progress; fi
    - if [ "$CI_NPM" = "yes" ]; then npm ci; fi
    - if [ "$CI_OLD_NPM" = "yes" ]; then npm install; fi
after_script:
    - if [ "$TRAVIS_BUILD_STAGE_NAME" = "tests" ] && [ "$CI_TYPE" != "mocha" ]; then ./codacy-coverage.phar clover build/logs/clover.xml; else echo "skipped codacy"; fi
stages:
    - name: lint
      if: NOT type = cron
    - name: tests
      if: NOT type = cron
    - name: robot
      if: type = cron
jobs:
    include:
        - stage: lint
          php: '7.3'
          env:
            - CI_TYPE=phpstan
            - CI_COMPOSER=yes
          name: 'PHPSTAN - PHP 7.3'
        - stage: lint
          php: '7.3'
          env:
            - CI_TYPE=phpcs
            - CI_COMPOSER=yes
          name: 'PHPCS - PHP 7.3'
        - stage: tests
          php: '7.1'
          env:
            - CI_TYPE=phpunit
            - CI_COMPOSER=yes
          name: 'PHPUNIT - PHP 7.1'
          after_success:
              - bash <(curl -s https://codecov.io/bash) -cF php
        - stage: tests
          php: '7.2'
          env:
            - CI_TYPE=phpunit
            - CI_COMPOSER=yes
          name: 'PHPUNIT - PHP 7.2'
          after_success:
              - bash <(curl -s https://codecov.io/bash) -cF php
        - stage: tests
          php: '7.3'
          env:
            - CI_TYPE=phpunit
            - CI_COMPOSER=yes
          name: 'PHPUNIT - PHP 7.3'
          after_success:
              - bash <(curl -s https://codecov.io/bash) -cF php
        - stage: tests
          os: osx
          language: node_js
          node_js: '10'
          env:
            - CI_TYPE=phpunit
            - CI_COMPOSER=yes
          name: 'PHPUNIT - PHP 7.1'
          addons:
              homebrew:
                  update: true
                  packages:
                      - php@7.1
                      - composer
          before_install:
            - mkdir ~/.homebrew_logs
            - export HOMEBREW_LOGS="~/.homebrew_logs"
            - export HOMEBREW_TEMP="/tmp"
            - export HOMEBREW_INSTALL_BADGE="🌻"
          after_success:
              - bash <(curl -s https://codecov.io/bash) -cF php
        - stage: tests
          os: osx
          language: node_js
          node_js: '10'
          env:
            - CI_TYPE=phpunit
            - CI_COMPOSER=yes
          name: 'PHPUNIT - PHP 7.2'
          addons:
              homebrew:
                  update: true
                  packages:
                      - php@7.2
                      - composer
          before_install:
            - mkdir ~/.homebrew_logs
            - export HOMEBREW_LOGS="~/.homebrew_logs"
            - export HOMEBREW_TEMP="/tmp"
            - export HOMEBREW_INSTALL_BADGE="🌻"
          after_success:
              - bash <(curl -s https://codecov.io/bash) -cF php
        - stage: tests
          os: osx
          language: node_js
          node_js: '10'
          env:
            - CI_TYPE=phpunit
            - CI_COMPOSER=yes
          name: 'PHPUNIT - PHP 7.3'
          addons:
              homebrew:
                  update: true
                  packages:
                      - php@7.3
                      - composer
          before_install:
            - echo "memory_limit=-1" > /usr/local/etc/php/7.3/conf.d/50-travis-ci.ini
            - echo "pcre.jit=0" >> /usr/local/etc/php/7.3/conf.d/50-travis-ci.ini
            - mkdir ~/.homebrew_logs
            - export HOMEBREW_LOGS="~/.homebrew_logs"
            - export HOMEBREW_TEMP="/tmp"
            - export HOMEBREW_INSTALL_BADGE="🌻"
          after_success:
              - bash <(curl -s https://codecov.io/bash) -cF php
        - stage: robot
          php: '7.2'
          node_js: 'stable'
          env:
            - CI_TYPE=cron
            - CI_OLD_NPM=yes
            - CI_COMPOSER=yes
          name: 'Create a pull-request to update data'
        - stage: tests
          language: node_js
          node_js: 'stable'
          env:
            - CI_TYPE=mocha
            - CI_NPM=yes
          name: 'MOCHA - NODE stable'
          after_success:
              - bash <(curl -s https://codecov.io/bash) -cF javascript
        - stage: lint
          language: node_js
          node_js: 'stable'
          env:
            - CI_TYPE=lint
            - CI_NPM=yes
          name: 'LINT - NODE stable'
        - stage: lint
          language: php
          php: '7.3'
          env:
            - CI_TYPE=lint
            - CI_COMPOSER=yes
          name: 'LINT - PHP 7.3'
          script: composer run phpcs
cache:
    ccache: true
    directories:
        - $HOME/.composer/cache/
        - $HOME/.cache/bower
        - $HOME/.npm
        - $HOME/.cache/ci
        - /var/cache/apt
        - $HOME/Library/Caches/Homebrew
before_cache:
    - if [[ "$TRAVIS_OS_NAME" == "osx" ]]; then brew cleanup; fi
addons:
    apt:
        update: false
git:
    submodules: false

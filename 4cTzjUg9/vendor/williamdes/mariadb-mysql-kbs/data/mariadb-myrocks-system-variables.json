{"data": [{"cli": "--rocksdb-access-hint-on-compaction-start=#", "default": "1", "dynamic": false, "id": "rocksdb_access_hint_on_compaction_start", "name": "rocksdb_access_hint_on_compaction_start", "range": {"from": 0, "to": 3}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-advise-random-on-open={0|1}", "default": "ON", "dynamic": false, "id": "rocksdb_advise_random_on_open", "name": "rocksdb_advise_random_on_open", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-allow-concurrent-memtable-write={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_allow_concurrent_memtable_write", "name": "rocksdb_allow_concurrent_memtable_write", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-allow-mmap-reads={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_allow_mmap_reads", "name": "rocksdb_allow_mmap_reads", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-allow-mmap-writes={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_allow_mmap_writes", "name": "rocksdb_allow_mmap_writes", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-background-sync={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_background_sync", "name": "rocksdb_background_sync", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-base-background-compactions=#", "default": "1", "dynamic": false, "id": "rocksdb_base_background_compactions", "name": "rocksdb_base_background_compactions", "range": {"from": -1, "to": 64}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-blind-delete-primary-key={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_blind_delete_primary_key", "name": "rocksdb_blind_delete_primary_key", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-block-cache-size=#", "default": "536870912", "dynamic": false, "id": "rocksdb_block_cache_size", "name": "rocksdb_block_cache_size", "range": {"from": 1024, "to": 9223372036854776000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-block-restart-interval=#", "default": "16", "dynamic": false, "id": "rocksdb_block_restart_interval", "name": "rocksdb_block_restart_interval", "range": {"from": 1, "to": 2147483647}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-block-size=#", "default": "4096", "dynamic": false, "id": "rocksdb_block_size", "name": "rocksdb_block_size", "range": {"from": 1, "to": 18446744073709552000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-block-size-deviation=#", "default": "10", "dynamic": false, "id": "rocksdb_block_size_deviation", "name": "rocksdb_block_size_deviation", "range": {"from": 0, "to": 2147483647}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-bulk-load={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_bulk_load", "name": "rocksdb_bulk_load", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-bulk-load_allow_unsorted={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_bulk_load_allow_unsorted", "name": "rocksdb_bulk_load_allow_unsorted", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-bulk-load-size=#", "default": "1000", "dynamic": true, "id": "rocksdb_bulk_load_size", "name": "rocksdb_bulk_load_size", "range": {"from": 1, "to": 1073741824}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-bytes-per-sync=#", "default": "0", "dynamic": false, "id": "rocksdb_bytes_per_sync", "name": "rocksdb_bytes_per_sync", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-cache-index-and-filter_blocks={0|1}", "default": "ON", "dynamic": false, "id": "rocksdb_cache_index_and_filter_blocks", "name": "rocksdb_cache_index_and_filter_blocks", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-checksums-pct=#", "default": "100", "dynamic": true, "id": "rocksdb_checksums_pct", "name": "rocksdb_checksums_pct", "range": {"from": 0, "to": 100}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-collect-sst-properties={0|1}", "default": "ON", "dynamic": false, "id": "rocksdb_collect_sst_properties", "name": "rocksdb_collect_sst_properties", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-commit-in-the-middle={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_commit_in_the_middle", "name": "rocksdb_commit_in_the_middle", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-compact-cf=value", "default": "(Empty)", "dynamic": true, "id": "rocksdb_compact_cf", "name": "rocksdb_compact_cf", "scope": ["global"], "type": "string"}, {"cli": "--rocksdb-compaction-readahead-size=#", "default": "0", "dynamic": true, "id": "rocksdb_compaction_readahead_size", "name": "rocksdb_compaction_readahead_size", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-compaction-sequential-deletes=#", "default": "0", "dynamic": true, "id": "rocksdb_compaction_sequential_deletes", "name": "rocksdb_compaction_sequential_deletes", "range": {"from": 0, "to": 2000000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-compaction-sequential-deletes-count-sd={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_compaction_sequential_deletes_count_sd", "name": "rocksdb_compaction_sequential_deletes_count_sd", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-compaction-sequential-deletes-file-size=#", "default": "0", "dynamic": true, "id": "rocksdb_compaction_sequential_deletes_file_size", "name": "rocksdb_compaction_sequential_deletes_file_size", "range": {"from": -1, "to": 9223372036854776000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-compaction-sequential-deletes-window=#", "default": "0", "dynamic": true, "id": "rocksdb_compaction_sequential_deletes_window", "name": "rocksdb_compaction_sequential_deletes_window", "range": {"from": 0, "to": 2000000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-coconcurrent-prepare={0|1}", "default": "1", "dynamic": false, "id": "rocksdb_concurrent_prepare", "name": "rocksdb_concurrent_prepare", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-create-checkpoint=value", "default": "(Empty)", "dynamic": true, "id": "rocksdb_create_checkpoint", "name": "rocksdb_create_checkpoint", "scope": ["global"], "type": "string"}, {"cli": "--rocksdb-create-if-missing={0|1}", "default": "ON", "dynamic": false, "id": "rocksdb_create_if_missing", "name": "rocksdb_create_if_missing", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-create-missing-column-families={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_create_missing_column_families", "name": "rocksdb_create_missing_column_families", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-datadir[=value]", "default": "./.rocksdb", "dynamic": false, "id": "rocksdb_datadir", "name": "rocksdb_datadir", "scope": ["global"], "type": "string"}, {"cli": "--rocksdb-db-write-buffer-size=#", "default": "0", "dynamic": false, "id": "rocksdb_db_write_buffer_size", "name": "rocksdb_db_write_buffer_size", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-deadlock-detect={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_deadlock_detect", "name": "rocksdb_deadlock_detect", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-deadlock-detect-depth=#", "default": "50", "dynamic": true, "id": "rocksdb_deadlock_detect_depth", "name": "rocksdb_deadlock_detect_depth", "range": {"from": 2, "to": 18446744073709552000}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-debug-optimizer-no-zero-cardinality={0|1}", "default": "ON", "dynamic": true, "id": "rocksdb_debug_optimizer_no_zero_cardinality", "name": "rocksdb_debug_optimizer_no_zero_cardinality", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-debug-ttl-ignore-pk={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_debug_ttl_ignore_pk", "name": "rocksdb_debug_ttl_ignore_pk", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-debug-ttl-read-filter-ts=#", "default": "0", "dynamic": true, "id": "rocksdb_debug_ttl_read_filter_ts", "name": "rocksdb_debug_ttl_read_filter_ts", "range": {"from": -3600, "to": 3600}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-debug-ttl-read-filter-ts=#", "default": "0", "dynamic": true, "id": "rocksdb_debug_ttl_rec_ts", "name": "rocksdb_debug_ttl_rec_ts", "range": {"from": -3600, "to": 3600}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-debug-ttl-snapshot-ts=#", "default": "0", "dynamic": true, "id": "rocksdb_debug_ttl_snapshot_ts", "name": "rocksdb_debug_ttl_snapshot_ts", "range": {"from": -3600, "to": 3600}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-default-cf-options=value", "default": "(Empty)", "dynamic": false, "id": "rocksdb_default_cf_options", "name": "rocksdb_default_cf_options", "scope": ["global"], "type": "string"}, {"cli": "--rocksdb-delayed-write-rate=#", "default": "16777216", "dynamic": true, "id": "rocksdb_delayed_write_rate", "name": "rocksdb_delayed_write_rate", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-delete-obsolete-files-period-micros=#", "default": "21600000000", "dynamic": false, "id": "rocksdb_delete_obsolete_files_period_micros", "name": "rocksdb_delete_obsolete_files_period_micros", "range": {"from": 0, "to": 9223372036854776000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-enable-2pc={0|1}", "default": "ON", "dynamic": true, "id": "rocksdb_enable_2pc", "name": "rocksdb_enable_2pc", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-enable-bulk-load-api={0|1}", "default": "ON", "dynamic": false, "id": "rocksdb_enable_bulk_load_api", "name": "rocksdb_enable_bulk_load_api", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-enable-thread-tracking={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_enable_thread_tracking", "name": "rocksdb_enable_thread_tracking", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-enable-ttl={0|1}", "default": "ON", "dynamic": true, "id": "rocksdb_enable_ttl", "name": "rocksdb_enable_ttl", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-enable-ttl-read-filtering={0|1}", "default": "ON", "dynamic": true, "id": "rocksdb_enable_ttl_read_filtering", "name": "rocksdb_enable_ttl_read_filtering", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-enable-write-thread-adaptive-yield={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_enable_write_thread_adaptive_yield", "name": "rocksdb_enable_write_thread_adaptive_yield", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-error-if-exists={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_error_if_exists", "name": "rocksdb_error_if_exists", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-flush-log-at-trx-commit=#", "default": "1", "dynamic": true, "id": "rocksdb_flush_log_at_trx_commit", "name": "rocksdb_flush_log_at_trx_commit", "range": {"from": 0, "to": 2}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-flush-memtable-on-analyze={0|1}", "default": "ON", "dynamic": true, "id": "rocksdb_flush_memtable_on_analyze", "name": "rocksdb_flush_memtable_on_analyze", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-force-compute-memtable-stats={0|1}", "default": "ON", "dynamic": true, "id": "rocksdb_force_compute_memtable_stats", "name": "rocksdb_force_compute_memtable_stats", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-force-compute-memtable-stats-cachetime=#", "default": "60000000", "dynamic": true, "id": "rocksdb_force_compute_memtable_stats_cachetime", "name": "rocksdb_force_compute_memtable_stats_cachetime", "range": {"from": 0, "to": 2147483647}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-force-flush-memtable-and-lzero-now={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_force_flush_memtable_and_lzero_now", "name": "rocksdb_force_flush_memtable_and_lzero_now", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-force-flush-memtable-now={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_force_flush_memtable_now", "name": "rocksdb_force_flush_memtable_now", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-force-index-records-in-range=#", "default": "1", "dynamic": true, "id": "rocksdb_force_index_records_in_range", "name": "rocksdb_force_index_records_in_range", "range": {"from": 0, "to": 2147483647}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-git-hash=value=#", "default": "As per git revision.", "dynamic": false, "id": "rocksdb_git_hash", "name": "rocksdb_git_hash", "scope": ["global"], "type": "string"}, {"cli": "--rocksdb-hash-index-allow-collision={0|1}", "default": "ON", "dynamic": false, "id": "rocksdb_hash_index_allow_collision", "name": "rocksdb_hash_index_allow_collision", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-index-type=value", "default": "kBinarySearch", "dynamic": false, "id": "rocksdb_index_type", "name": "rocksdb_index_type", "scope": ["global"], "validValues": ["kBinarySearch", "kHashSearch"]}, {"cli": "--rocksdb-info-log-level=value", "default": "error_level", "dynamic": true, "id": "rocksdb_info_log_level", "name": "rocksdb_info_log_level", "scope": ["global"], "validValues": ["error_level", "debug_level", "info_level", "warn_level", "fatal_level"]}, {"cli": "--rocksdb-io-write-timeout=#", "default": "0", "dynamic": true, "id": "rocksdb_io_write_timeout", "name": "rocksdb_io_write_timeout", "scope": ["global"], "type": "integer", "validValues": ["0", "4294967295"]}, {"cli": "--rocksdb-is-fd-close-on-exec={0|1}", "default": "ON", "dynamic": false, "id": "rocksdb_is_fd_close_on_exec", "name": "rocksdb_is_fd_close_on_exec", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-keep-log-file-num=#", "default": "1000", "dynamic": false, "id": "rocksdb_keep_log_file_num", "name": "rocksdb_keep_log_file_num", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-large_prefix={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_large_prefix", "name": "rocksdb_large_prefix", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-lock-scanned-rows={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_lock_scanned_rows", "name": "rocksdb_lock_scanned_rows", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-lock-wait-timeout=#", "default": "1", "dynamic": true, "id": "rocksdb_lock_wait_timeout", "name": "rocksdb_lock_wait_timeout", "range": {"from": 1, "to": 1073741824}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-log-file-time-to_roll=#", "default": "0", "dynamic": false, "id": "rocksdb_log_file_time_to_roll", "name": "rocksdb_log_file_time_to_roll", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-manifest-preallocation-size=#", "default": "0", "dynamic": false, "id": "rocksdb_manifest_preallocation_size", "name": "rocksdb_manifest_preallocation_size", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-manual-wal-flush={0|1}", "default": "ON", "dynamic": false, "id": "rocksdb_manual_wal_flush", "name": "rocksdb_manual_wal_flush", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-master-skip-tx-api={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_master_skip_tx_api", "name": "rocksdb_master_skip_tx_api", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-max-background-compactions=#", "default": "1", "dynamic": true, "id": "rocksdb_max_background_compactions", "name": "rocksdb_max_background_compactions", "range": {"from": 1, "to": 64}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-max-background-flushes=#", "default": "1", "dynamic": false, "id": "rocksdb_max_background_flushes", "name": "rocksdb_max_background_flushes", "range": {"from": 1, "to": 64}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-max-background-jobs=#", "default": "2", "dynamic": true, "id": "rocksdb_max_background_jobs", "name": "rocksdb_max_background_jobs", "range": {"from": -1, "to": 64}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-max-latest-deadlocks=#", "default": "5", "dynamic": true, "id": "rocksdb_max_latest_deadlocks", "name": "rocksdb_max_latest_deadlocks", "range": {"from": 0, "to": 4294967295}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-max-log-file-size=#", "default": "0", "dynamic": false, "id": "rocksdb_max_log_file_size", "name": "rocksdb_max_log_file_size", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-manifest-log-file-size=#", "default": "18446744073709551615", "dynamic": false, "id": "rocksdb_max_manifest_file_size", "name": "rocksdb_max_manifest_file_size", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-max-open-files=#", "default": "-1", "dynamic": false, "id": "rocksdb_max_open_files", "name": "rocksdb_max_open_files", "range": {"from": -1, "to": 2147483647}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-max-row-locks=#", "default": "1048576", "dynamic": true, "id": "rocksdb_max_row_locks", "name": "rocksdb_max_row_locks", "range": {"from": 1, "to": 1073741824}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-max-subcompactions=#", "default": "1", "dynamic": false, "id": "rocksdb_max_subcompactions", "name": "rocksdb_max_subcompactions", "range": {"from": 1, "to": 64}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-max-total-wal-size=#", "default": "0", "dynamic": false, "id": "rocksdb_max_total_wal_size", "name": "rocksdb_max_total_wal_size", "range": {"from": 0, "to": 9223372036854776000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-merge-buf-size=#", "default": "67108864", "dynamic": true, "id": "rocksdb_merge_buf_size", "name": "rocksdb_merge_buf_size", "range": {"from": 100, "to": 18446744073709552000}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-merge-combine-read-size=#", "default": "1073741824", "dynamic": true, "id": "rocksdb_merge_combine_read_size", "name": "rocksdb_merge_combine_read_size", "range": {"from": 100, "to": 18446744073709552000}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-merge-tmp-file-removal-delay-ms=#", "default": "0", "dynamic": true, "id": "rocksdb_merge_tmp_file_removal_delay_ms", "name": "rocksdb_merge_tmp_file_removal_delay_ms", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-new-table-reader-for-compaction-inputs={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_new_table_reader_for_compaction_inputs", "name": "rocksdb_new_table_reader_for_compaction_inputs", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-no-block-cache={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_no_block_cache", "name": "rocksdb_no_block_cache", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-override-cf-options=value", "default": "(Empty)", "dynamic": false, "id": "rocksdb_override_cf_options", "name": "rocksdb_override_cf_options", "scope": ["global"], "type": "string"}, {"cli": "--rocksdb-paranoid-checks={0|1}", "default": "ON", "dynamic": false, "id": "rocksdb_paranoid_checks", "name": "rocksdb_paranoid_checks", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-pause-background-work={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_pause_background_work", "name": "rocksdb_pause_background_work", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-perf-context-level=#", "default": "0", "dynamic": true, "id": "rocksdb_perf_context_level", "name": "rocksdb_perf_context_level", "range": {"from": 0, "to": 4}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-persistent-cache-path=value", "default": "(Empty)", "dynamic": false, "id": "rocksdb_persistent_cache_path", "name": "rocksdb_persistent_cache_path", "scope": ["global"], "type": "string"}, {"cli": "--rocksdb-persistent-cache-size-mb=#", "default": "0", "dynamic": false, "id": "rocksdb_persistent_cache_size_mb", "name": "rocksdb_persistent_cache_size_mb", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-pin-l0-filter-and-index-blocks-in-cache={0|1}", "default": "ON", "dynamic": false, "id": "rocksdb_pin_l0_filter_and_index_blocks_in_cache", "name": "rocksdb_pin_l0_filter_and_index_blocks_in_cache", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-print-snapshot-conflict-queries={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_print_snapshot_conflict_queries", "name": "rocksdb_print_snapshot_conflict_queries", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-rate-limiter-bytes-per-sec=#", "default": "0", "dynamic": true, "id": "rocksdb_rate_limiter_bytes_per_sec", "name": "rocksdb_rate_limiter_bytes_per_sec", "range": {"from": 0, "to": 9223372036854776000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-read-free-rpl-tables=value", "default": "(Empty)", "dynamic": true, "id": "rocksdb_read_free_rpl_tables", "name": "rocksdb_read_free_rpl_tables", "scope": ["global", "session"], "type": "string"}, {"cli": "--rocksdb-records-in-range=#", "default": "0", "dynamic": true, "id": "rocksdb_records_in_range", "name": "rocksdb_records_in_range", "range": {"from": 0, "to": 2147483647}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-reset-stats={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_reset_stats", "name": "rocksdb_reset_stats", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-seconds-between-stat-computes=#", "default": "3600", "dynamic": true, "id": "rocksdb_seconds_between_stat_computes", "name": "rocksdb_seconds_between_stat_computes", "range": {"from": 0, "to": 4294967295}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-signal-drop-index-thread={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_signal_drop_index_thread", "name": "rocksdb_signal_drop_index_thread", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-sim-cache-size=#", "default": "0", "dynamic": false, "id": "rocksdb_sim_cache_size", "name": "rocksdb_sim_cache_size", "range": {"from": 0, "to": 9223372036854776000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-skip-bloom-filter-on_read={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_skip_bloom_filter_on_read", "name": "rocksdb_skip_bloom_filter_on_read", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-skip-fill-cache={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_skip_fill_cache", "name": "rocksdb_skip_fill_cache", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-skip-unique-check-tables=value", "default": ".*", "dynamic": true, "id": "rocksdb_skip_unique_check_tables", "name": "rocksdb_skip_unique_check_tables", "scope": ["global", "session"], "type": "string"}, {"cli": "--rocksdb-sst-mgr-rate-bytes-per-sec=#", "default": "0", "dynamic": true, "id": "rocksdb_sst_mgr_rate_bytes_per_sec", "name": "rocksdb_sst_mgr_rate_bytes_per_sec", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-stats-dump-period-sec=#", "default": "600", "dynamic": false, "id": "rocksdb_stats_dump_period_sec", "name": "rocksdb_stats_dump_period_sec", "range": {"from": 0, "to": 2147483647}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-store-row-debug-checksums={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_store_row_debug_checksums", "name": "rocksdb_store_row_debug_checksums", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-strict-collation-check={0|1}", "default": "ON", "dynamic": true, "id": "rocksdb_strict_collation_check", "name": "rocksdb_strict_collation_check", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-strict-collation-exceptions=value", "default": "(Empty)", "dynamic": true, "id": "rocksdb_strict_collation_exceptions", "name": "rocksdb_strict_collation_exceptions", "scope": ["global"], "type": "string"}, {"cli": "--rocksdb-supported-compression-types=value", "default": "<PERSON><PERSON><PERSON>,Zlib", "dynamic": false, "id": "rocksdb_supported_compression_types", "name": "rocksdb_supported_compression_types", "scope": ["global"], "type": "string"}, {"cli": "--rocksdb-table-cache-numshardbits=#", "default": "6", "dynamic": false, "id": "rocksdb_table_cache_numshardbits", "name": "rocksdb_table_cache_numshardbits", "range": {"from": 0, "to": 2147483647}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-table-stats-sampling-pct=#", "default": "10", "dynamic": true, "id": "rocksdb_table_stats_sampling_pct", "name": "rocksdb_table_stats_sampling_pct", "range": {"from": 0, "to": 100}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-tmpdir[=value]", "default": "(Empty)", "dynamic": true, "id": "rocksdb_tmpdir", "name": "rocksdb_tmpdir", "scope": ["global", "session"], "type": "string"}, {"cli": "--rocksdb-trace-sst-api={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_trace_sst_api", "name": "rocksdb_trace_sst_api", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-unsafe-for-binlog={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_unsafe_for_binlog", "name": "rocksdb_unsafe_for_binlog", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-update-cf-options=value", "default": "(Empty)", "dynamic": true, "id": "rocksdb_update_cf_options", "name": "rocksdb_update_cf_options", "scope": ["global"]}, {"cli": "--rocksdb-use-adaptive-mutex={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_use_adaptive_mutex", "name": "rocksdb_use_adaptive_mutex", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-use-clock-cache={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_use_clock_cache", "name": "rocksdb_use_clock_cache", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-use-direct-io-for-flush-and-compaction={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_use_direct_io_for_flush_and_compaction", "name": "rocksdb_use_direct_io_for_flush_and_compaction", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-use-direct-reads={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_use_direct_reads", "name": "rocksdb_use_direct_reads", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-use-direct-reads={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_use_direct_writes", "name": "rocksdb_use_direct_writes", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-use-fsync={0|1}", "default": "OFF", "dynamic": false, "id": "rocksdb_use_fsync", "name": "rocksdb_use_fsync", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-validate-tables=#", "default": "1", "dynamic": false, "id": "rocksdb_validate_tables", "name": "rocksdb_validate_tables", "range": {"from": 0, "to": 2}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-verify-row-debug-checksums={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_verify_row_debug_checksums", "name": "rocksdb_verify_row_debug_checksums", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-wal-bytes-per-sync=#", "default": "0", "dynamic": false, "id": "rocksdb_wal_bytes_per_sync", "name": "rocksdb_wal_bytes_per_sync", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-wal-dir=value", "default": "(Empty)", "dynamic": false, "id": "rocksdb_wal_dir", "name": "rocksdb_wal_dir", "scope": ["global"], "type": "string"}, {"cli": "--rocksdb-wal-recovery-mode=#", "default": "1", "dynamic": true, "id": "rocksdb_wal_recovery_mode", "name": "rocksdb_wal_recovery_mode", "range": {"from": 0, "to": 3}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-wal-size-limit-mb=#", "default": "0", "dynamic": false, "id": "rocksdb_wal_size_limit_mb", "name": "rocksdb_wal_size_limit_mb", "range": {"from": 0, "to": 9223372036854776000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-wal-ttl-seconds=#", "default": "0", "dynamic": false, "id": "rocksdb_wal_ttl_seconds", "name": "rocksdb_wal_ttl_seconds", "range": {"from": 0, "to": 9223372036854776000}, "scope": ["global"], "type": "integer"}, {"cli": "--rocksdb-whole-key-filtering={0|1}", "default": "ON", "dynamic": false, "id": "rocksdb_whole_key_filtering", "name": "rocksdb_whole_key_filtering", "scope": ["global"], "type": "boolean"}, {"cli": "--rocksdb-write-batch-max-bytes=#", "default": "0", "dynamic": true, "id": "rocksdb_write_batch_max_bytes", "name": "rocksdb_write_batch_max_bytes", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--rocksdb-write-disable-wal={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_write_disable_wal", "name": "rocksdb_write_disable_wal", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--rocksdb-write-ignore-missing-column-familiesl={0|1}", "default": "OFF", "dynamic": true, "id": "rocksdb_write_ignore_missing_column_families", "name": "rocksdb_write_ignore_missing_column_families", "scope": ["global", "session"], "type": "boolean"}], "name": "myrocks-system-variables", "url": "https://mariadb.com/kb/en/library/documentation/columns-storage-engines-and-plugins/storage-engines/myrocks/myrocks-system-variables/"}
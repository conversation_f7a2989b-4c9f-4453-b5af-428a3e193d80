{"data": [{"id": "innodb_adaptive_hash_cells", "name": "Innodb_adaptive_hash_cells", "scope": ["global"], "type": "integer"}, {"id": "innodb_adaptive_hash_hash_searches", "name": "Innodb_adaptive_hash_hash_searches", "scope": ["global"], "type": "integer"}, {"id": "innodb_adaptive_hash_heap_buffers", "name": "Innodb_adaptive_hash_heap_buffers", "scope": ["global"], "type": "integer"}, {"id": "innodb_adaptive_hash_non_hash_searches", "name": "Innodb_adaptive_hash_non_hash_searches", "scope": ["global"], "type": "integer"}, {"id": "innodb_available_undo_logs", "name": "Innodb_available_undo_logs", "scope": ["global"], "type": "integer"}, {"id": "innodb_background_log_sync", "name": "Innodb_background_log_sync", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_bytes_data", "name": "Innodb_buffer_pool_bytes_data", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_bytes_dirty", "name": "Innodb_buffer_pool_bytes_dirty", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_dump_status", "name": "Innodb_buffer_pool_dump_status", "scope": ["global"], "type": "string"}, {"id": "innodb_buffer_pool_load_incomplete", "name": "Innodb_buffer_pool_load_incomplete", "scope": ["global"], "type": "boolean"}, {"id": "innodb_buffer_pool_load_status", "name": "Innodb_buffer_pool_load_status", "scope": ["global"], "type": "string"}, {"id": "innodb_buffer_pool_pages_data", "name": "Innodb_buffer_pool_pages_data", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_pages_dirty", "name": "Innodb_buffer_pool_pages_dirty", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_pages_flushed", "name": "Innodb_buffer_pool_pages_flushed", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_pages_lru_flushed", "name": "Innodb_buffer_pool_pages_LRU_flushed", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_pages_free", "name": "Innodb_buffer_pool_pages_free", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_pages_made_not_young", "name": "Innodb_buffer_pool_pages_made_not_young", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_pages_made_young", "name": "Innodb_buffer_pool_pages_made_young", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_pages_misc", "name": "Innodb_buffer_pool_pages_misc", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_pages_old", "name": "Innodb_buffer_pool_pages_old", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_pages_total", "name": "Innodb_buffer_pool_pages_total", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_read_ahead", "name": "Innodb_buffer_pool_read_ahead", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_read_ahead_evicted", "name": "Innodb_buffer_pool_read_ahead_evicted", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_read_ahead_rnd", "name": "Innodb_buffer_pool_read_ahead_rnd", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_read_requests", "name": "Innodb_buffer_pool_read_requests", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_reads", "name": "Innodb_buffer_pool_reads", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_resize_status", "name": "Innodb_buffer_pool_resize_status", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_wait_free", "name": "Innodb_buffer_pool_wait_free", "scope": ["global"], "type": "integer"}, {"id": "innodb_buffer_pool_write_requests", "name": "Innodb_buffer_pool_write_requests", "scope": ["global"], "type": "integer"}, {"id": "innodb_checkpoint_age", "name": "Innodb_checkpoint_age", "scope": ["global"], "type": "integer"}, {"id": "innodb_checkpoint_max_age", "name": "Innodb_checkpoint_max_age", "scope": ["global"], "type": "integer"}, {"id": "innodb_checkpoint_target_age", "name": "Innodb_checkpoint_target_age", "scope": ["global"], "type": "integer"}, {"id": "innodb_current_row_locks", "name": "Innodb_current_row_locks", "scope": ["global"], "type": "integer"}, {"id": "innodb_data_fsyncs", "name": "Innodb_data_fsyncs", "scope": ["global"], "type": "integer"}, {"id": "innodb_data_pending_fsyncs", "name": "Innodb_data_pending_fsyncs", "scope": ["global"], "type": "integer"}, {"id": "innodb_data_pending_reads", "name": "Innodb_data_pending_reads", "scope": ["global"], "type": "integer"}, {"id": "innodb_data_pending_writes", "name": "Innodb_data_pending_writes", "scope": ["global"], "type": "integer"}, {"id": "innodb_data_read", "name": "Innodb_data_read", "scope": ["global"], "type": "integer"}, {"id": "innodb_data_reads", "name": "Innodb_data_reads", "scope": ["global"], "type": "integer"}, {"id": "innodb_data_writes", "name": "Innodb_data_writes", "scope": ["global"], "type": "integer"}, {"id": "innodb_data_written", "name": "Innodb_data_written", "scope": ["global"], "type": "integer"}, {"id": "innodb_dblwr_pages_written", "name": "Innodb_dblwr_pages_written", "scope": ["global"], "type": "integer"}, {"id": "innodb_dblwr_writes", "name": "Innodb_dblwr_writes", "scope": ["global"], "type": "integer"}, {"id": "innodb_deadlocks", "name": "Innodb_deadlocks", "scope": ["global"], "type": "integer"}, {"id": "innodb_defragment_compression_failures", "name": "Innodb_defragment_compression_failures", "scope": ["global"], "type": "integer"}, {"id": "innodb_defragment_count", "name": "Innodb_defragment_count", "scope": ["global"], "type": "integer"}, {"id": "innodb_defragment_failures", "name": "Innodb_defragment_failures", "scope": ["global"], "type": "integer"}, {"id": "innodb_dict_tables", "name": "Innodb_dict_tables", "scope": ["global"], "type": "integer"}, {"id": "innodb_encryption_n_merge_blocks_decrypted", "name": "Innodb_encryption_n_merge_blocks_decrypted", "scope": ["global"], "type": "integer"}, {"id": "innodb_encryption_n_merge_blocks_encrypted", "name": "Innodb_encryption_n_merge_blocks_encrypted", "scope": ["global"], "type": "integer"}, {"id": "innodb_encryption_n_rowlog_blocks_decrypted", "name": "Innodb_encryption_n_rowlog_blocks_decrypted", "scope": ["global"], "type": "integer"}, {"id": "innodb_encryption_n_rowlog_blocks_encrypted", "name": "Innodb_encryption_n_rowlog_blocks_encrypted", "scope": ["global"], "type": "integer"}, {"id": "innodb_encryption_num_key_requests", "name": "Innodb_encryption_num_key_requests", "scope": ["global"], "type": "integer"}, {"id": "innodb_encryption_rotation_estimated_iops", "name": "Innodb_encryption_rotation_estimated_iops", "scope": ["global"], "type": "integer"}, {"id": "innodb_encryption_rotation_pages_flushed", "name": "Innodb_encryption_rotation_pages_flushed", "scope": ["global"], "type": "integer"}, {"id": "innodb_encryption_rotation_pages_modified", "name": "Innodb_encryption_rotation_pages_modified", "scope": ["global"], "type": "integer"}, {"id": "innodb_encryption_rotation_pages_read_from_cache", "name": "Innodb_encryption_rotation_pages_read_from_cache", "scope": ["global"], "type": "integer"}, {"id": "innodb_encryption_rotation_pages_read_from_disk", "name": "Innodb_encryption_rotation_pages_read_from_disk", "scope": ["global"], "type": "integer"}, {"id": "innodb_have_atomic_builtins", "name": "Innodb_have_atomic_builtins", "scope": ["global"], "type": "boolean"}, {"id": "innodb_have_bzip2", "name": "Innodb_have_bzip2", "scope": ["global"], "type": "boolean"}, {"id": "innodb_have_lz4", "name": "Innodb_have_lz4", "scope": ["global"], "type": "boolean"}, {"id": "innodb_have_lzma", "name": "Innodb_have_lzma", "scope": ["global"], "type": "boolean"}, {"id": "innodb_have_lzo", "name": "Innodb_have_lzo", "scope": ["global"], "type": "boolean"}, {"id": "innodb_have_punch_hole", "name": "Innodb_have_punch_hole", "scope": ["global"], "type": "integer"}, {"id": "innodb_have_snappy", "name": "Innodb_have_snappy", "scope": ["global"], "type": "boolean"}, {"id": "innodb_history_list_length", "name": "Innodb_history_list_length", "scope": ["global"], "type": "integer"}, {"id": "innodb_ibuf_discarded_delete_marks", "name": "Innodb_ibuf_discarded_delete_marks", "scope": ["global"], "type": "integer"}, {"id": "innodb_ibuf_discarded_deletes", "name": "Innodb_ibuf_discarded_deletes", "scope": ["global"], "type": "integer"}, {"id": "innodb_ibuf_discarded_inserts", "name": "Innodb_ibuf_discarded_inserts", "scope": ["global"], "type": "integer"}, {"id": "innodb_ibuf_free_list", "name": "Innodb_ibuf_free_list", "scope": ["global"], "type": "integer"}, {"id": "innodb_ibuf_merged_delete_marks", "name": "Innodb_ibuf_merged_delete_marks", "scope": ["global"], "type": "integer"}, {"id": "innodb_ibuf_merged_deletes", "name": "Innodb_ibuf_merged_deletes", "scope": ["global"], "type": "integer"}, {"id": "innodb_ibuf_merged_inserts", "name": "Innodb_ibuf_merged_inserts", "scope": ["global"], "type": "integer"}, {"id": "innodb_ibuf_merges", "name": "Innodb_ibuf_merges", "scope": ["global"], "type": "integer"}, {"id": "innodb_ibuf_segment_size", "name": "Innodb_ibuf_segment_size", "scope": ["global"], "type": "integer"}, {"id": "innodb_ibuf_size", "name": "Innodb_ibuf_size", "scope": ["global"], "type": "integer"}, {"id": "innodb_instant_alter_column", "name": "Innodb_instant_alter_column", "scope": ["global"], "type": "integer"}, {"id": "innodb_log_waits", "name": "Innodb_log_waits", "scope": ["global"], "type": "integer"}, {"id": "innodb_log_write_requests", "name": "Innodb_log_write_requests", "scope": ["global"], "type": "integer"}, {"id": "innodb_log_writes", "name": "Innodb_log_writes", "scope": ["global"], "type": "integer"}, {"id": "innodb_lsn_current", "name": "Innodb_lsn_current", "scope": ["global"], "type": "integer"}, {"id": "innodb_lsn_flushed", "name": "Innodb_lsn_flushed", "scope": ["global"], "type": "integer"}, {"id": "innodb_lsn_last_checkpoint", "name": "Innodb_lsn_last_checkpoint", "scope": ["global"], "type": "integer"}, {"id": "innodb_master_thread_1_second_loops", "name": "Innodb_master_thread_1_second_loops", "scope": ["global"], "type": "integer"}, {"id": "innodb_master_thread_10_second_loops", "name": "Innodb_master_thread_10_second_loops", "scope": ["global"], "type": "integer"}, {"id": "innodb_master_thread_active_loops", "name": "Innodb_master_thread_active_loops", "scope": ["global"], "type": "integer"}, {"id": "innodb_master_thread_background_loops", "name": "Innodb_master_thread_background_loops", "scope": ["global"], "type": "integer"}, {"id": "innodb_master_thread_idle_loops", "name": "Innodb_master_thread_idle_loops", "scope": ["global"], "type": "integer"}, {"id": "innodb_master_thread_main_flush_loops", "name": "Innodb_master_thread_main_flush_loops", "scope": ["global"], "type": "integer"}, {"id": "innodb_master_thread_sleeps", "name": "Innodb_master_thread_sleeps", "scope": ["global"], "type": "integer"}, {"id": "innodb_max_trx_id", "name": "Innodb_max_trx_id", "scope": ["global"], "type": "integer"}, {"id": "innodb_mem_adaptive_hash", "name": "Innodb_mem_adaptive_hash", "scope": ["global"], "type": "integer"}, {"id": "innodb_mem_dictionary", "name": "Innodb_mem_dictionary", "scope": ["global"], "type": "integer"}, {"id": "innodb_mem_total", "name": "Innodb_mem_total", "scope": ["global"], "type": "integer"}, {"id": "innodb_mutex_os_waits", "name": "Innodb_mutex_os_waits", "scope": ["global"], "type": "integer"}, {"id": "innodb_mutex_spin_rounds", "name": "Innodb_mutex_spin_rounds", "scope": ["global"], "type": "integer"}, {"id": "innodb_mutex_spin_waits", "name": "Innodb_mutex_spin_waits", "scope": ["global"], "type": "integer"}, {"id": "innodb_num_index_pages_written", "name": "Innodb_num_index_pages_written", "scope": [], "type": "integer"}, {"id": "innodb_num_non_index_pages_written", "name": "Innodb_num_non_index_pages_written", "scope": [], "type": "integer"}, {"id": "innodb_num_open_files", "name": "Innodb_num_open_files", "scope": ["global"], "type": "integer"}, {"id": "innodb_num_page_compressed_trim_op", "name": "Innodb_num_page_compressed_trim_op", "scope": ["global"], "type": "integer"}, {"id": "innodb_num_page_compressed_trim_op_saved", "name": "Innodb_num_page_compressed_trim_op_saved", "scope": ["global"], "type": "integer"}, {"id": "innodb_num_pages_decrypted", "name": "Innodb_num_pages_decrypted", "scope": ["global"], "type": "integer"}, {"id": "innodb_num_pages_encrypted", "name": "Innodb_num_pages_encrypted", "scope": ["global"], "type": "integer"}, {"id": "innodb_num_pages_page_compressed", "name": "Innodb_num_pages_page_compressed", "scope": ["global"], "type": "integer"}, {"id": "innodb_num_pages_page_compression_error", "name": "Innodb_num_pages_page_compression_error", "scope": ["global"], "type": "integer"}, {"id": "innodb_num_pages_page_decompressed", "name": "Innodb_num_pages_page_decompressed", "scope": ["global"], "type": "integer"}, {"id": "innodb_num_pages_page_encryption_error", "name": "Innodb_num_pages_page_encryption_error", "scope": ["global"], "type": "integer"}, {"id": "innodb_oldest_view_low_limit_trx_id", "name": "Innodb_oldest_view_low_limit_trx_id", "scope": ["global"], "type": "integer"}, {"id": "innodb_onlineddl_pct_progress", "name": "Innodb_onlineddl_pct_progress", "scope": ["global"], "type": "integer"}, {"id": "innodb_onlineddl_rowlog_pct_used", "name": "Innodb_onlineddl_rowlog_pct_used", "scope": ["global"], "type": "integer"}, {"id": "innodb_onlineddl_rowlog_rows", "name": "Innodb_onlineddl_rowlog_rows", "scope": ["global"], "type": "integer"}, {"id": "innodb_os_log_fsyncs", "name": "Innodb_os_log_fsyncs", "scope": ["global"], "type": "integer"}, {"id": "innodb_os_log_pending_fsyncs", "name": "Innodb_os_log_pending_fsyncs", "scope": ["global"], "type": "integer"}, {"id": "innodb_os_log_pending_writes", "name": "Innodb_os_log_pending_writes", "scope": ["global"], "type": "integer"}, {"id": "innodb_os_log_written", "name": "Innodb_os_log_written", "scope": ["global"], "type": "integer"}, {"id": "innodb_page_compression_saved", "name": "Innodb_page_compression_saved", "scope": [], "type": "byte"}, {"id": "innodb_page_compression_trim_sect512", "name": "Innodb_page_compression_trim_sect512", "scope": [], "type": "integer"}, {"id": "innodb_page_compression_trim_sect1024", "name": "Innodb_page_compression_trim_sect1024", "scope": [], "type": "integer"}, {"id": "innodb_page_compression_trim_sect2048", "name": "Innodb_page_compression_trim_sect2048", "scope": [], "type": "integer"}, {"id": "innodb_page_compression_trim_sect4096", "name": "Innodb_page_compression_trim_sect4096", "scope": [], "type": "integer"}, {"id": "innodb_page_compression_trim_sect8192", "name": "Innodb_page_compression_trim_sect8192", "scope": [], "type": "integer"}, {"id": "innodb_page_compression_trim_sect16384", "name": "Innodb_page_compression_trim_sect16384", "scope": [], "type": "integer"}, {"id": "innodb_page_compression_trim_sect32768", "name": "Innodb_page_compression_trim_sect32768", "scope": [], "type": "integer"}, {"id": "innodb_page_size", "name": "Innodb_page_size", "scope": ["global"], "type": "integer"}, {"id": "innodb_pages_created", "name": "Innodb_pages_created", "scope": ["global"], "type": "integer"}, {"id": "innodb_pages_read", "name": "Innodb_pages_read", "scope": ["global"], "type": "integer"}, {"id": "innodb_pages0_read", "name": "Innodb_pages0_read", "scope": ["global"], "type": "integer"}, {"id": "innodb_pages_written", "name": "Innodb_pages_written", "scope": ["global"], "type": "integer"}, {"id": "innodb_purge_trx_id", "name": "Innodb_purge_trx_id", "scope": ["global"], "type": "integer"}, {"id": "innodb_purge_undo_no", "name": "Innodb_purge_undo_no", "scope": ["global"], "type": "integer"}, {"id": "innodb_read_views_memory", "name": "Innodb_read_views_memory", "scope": ["global"], "type": "integer"}, {"id": "innodb_row_lock_current_waits", "name": "Innodb_row_lock_current_waits", "scope": ["global"], "type": "integer"}, {"id": "innodb_row_lock_numbers", "name": "Innodb_row_lock_numbers", "scope": ["global"], "type": "integer"}, {"id": "innodb_row_lock_time", "name": "Innodb_row_lock_time", "scope": ["global"], "type": "integer"}, {"id": "innodb_row_lock_time_avg", "name": "Innodb_row_lock_time_avg", "scope": ["global"], "type": "integer"}, {"id": "innodb_row_lock_time_max", "name": "Innodb_row_lock_time_max", "scope": ["global"], "type": "integer"}, {"id": "innodb_row_lock_time_waits", "name": "Innodb_row_lock_time_waits", "scope": ["global"], "type": "integer"}, {"id": "innodb_rows_deleted", "name": "Innodb_rows_deleted", "scope": ["global"], "type": "integer"}, {"id": "innodb_rows_inserted", "name": "Innodb_rows_inserted", "scope": ["global"], "type": "integer"}, {"id": "innodb_rows_read", "name": "Innodb_rows_read", "scope": ["global"], "type": "integer"}, {"id": "innodb_rows_updated", "name": "Innodb_rows_updated", "scope": ["global"], "type": "integer"}, {"id": "innodb_s_lock_os_waits", "name": "Innodb_s_lock_os_waits", "scope": ["global"], "type": "integer"}, {"id": "innodb_s_lock_spin_rounds", "name": "Innodb_s_lock_spin_rounds", "scope": ["global"], "type": "integer"}, {"id": "innodb_s_lock_spin_waits", "name": "Innodb_s_lock_spin_waits", "scope": ["global"], "type": "integer"}, {"id": "innodb_scrub_background_page_reorganizations", "name": "Innodb_scrub_background_page_reorganizations", "scope": ["global"], "type": "integer"}, {"id": "innodb_scrub_background_page_split_failures_missing_index", "name": "Innodb_scrub_background_page_split_failures_missing_index", "scope": ["global"], "type": "integer"}, {"id": "innodb_scrub_background_page_split_failures_out_of_filespace", "name": "Innodb_scrub_background_page_split_failures_out_of_filespace", "scope": ["global"], "type": "integer"}, {"id": "innodb_scrub_background_page_split_failures_underflow", "name": "Innodb_scrub_background_page_split_failures_underflow", "scope": ["global"], "type": "integer"}, {"id": "innodb_scrub_background_page_split_failures_unknown", "name": "Innodb_scrub_background_page_split_failures_unknown", "scope": ["global"], "type": "integer"}, {"id": "innodb_scrub_background_page_splits", "name": "Innodb_scrub_background_page_splits", "scope": ["global"], "type": "integer"}, {"id": "innodb_scrub_log", "name": "Innodb_scrub_log", "scope": ["global"], "type": "integer"}, {"id": "innodb_secondary_index_triggered_cluster_reads", "name": "Innodb_secondary_index_triggered_cluster_reads", "scope": ["global"], "type": "integer"}, {"id": "innodb_secondary_index_triggered_cluster_reads_avoided", "name": "Innodb_secondary_index_triggered_cluster_reads_avoided", "scope": ["global"], "type": "integer"}, {"id": "innodb_system_rows_deleted", "name": "Innodb_system_rows_deleted", "scope": [], "type": "integer"}, {"id": "innodb_system_rows_inserted", "name": "Innodb_system_rows_inserted", "scope": [], "type": "integer"}, {"id": "innodb_system_rows_read", "name": "Innodb_system_rows_read", "scope": [], "type": "integer"}, {"id": "innodb_system_rows_updated", "name": "Innodb_system_rows_updated", "scope": [], "type": "integer"}, {"id": "innodb_truncated_status_writes", "name": "Innodb_truncated_status_writes", "scope": ["global"], "type": "integer"}, {"id": "innodb_undo_truncations", "name": "Innodb_undo_truncations", "scope": ["global"], "type": "integer"}, {"id": "innodb_x_lock_os_waits", "name": "Innodb_x_lock_os_waits", "scope": ["global"], "type": "integer"}, {"id": "innodb_x_lock_spin_rounds", "name": "Innodb_x_lock_spin_rounds", "scope": ["global"], "type": "integer"}, {"id": "innodb_x_lock_spin_waits", "name": "Innodb_x_lock_spin_waits", "scope": ["global"], "type": "integer"}], "name": "xtradbinnodb-server-status-variables", "url": "https://mariadb.com/kb/en/library/documentation/xtradbinnodb-server-status-variables/"}
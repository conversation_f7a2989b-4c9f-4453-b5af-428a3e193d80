{"data": [{"cli": "--activate-all-roles-on-login[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_activate_all_roles_on_login", "name": "activate_all_roles_on_login", "scope": ["global"], "type": "boolean"}, {"cli": "--admin-address=addr", "dynamic": false, "id": "sysvar_admin_address", "name": "admin_address", "scope": ["global"], "type": "string"}, {"cli": "--admin-port=port_num", "default": "33062", "dynamic": false, "id": "sysvar_admin_port", "name": "admin_port", "range": {"from": 0, "to": 65535}, "scope": ["global"], "type": "integer"}, {"cli": "--authentication-windows-log-level=#", "default": "2", "dynamic": false, "id": "sysvar_authentication_windows_log_level", "name": "authentication_windows_log_level", "range": {"from": 0, "to": 4}, "scope": ["global"], "type": "integer"}, {"cli": "--authentication-windows-use-principal-name[={OFF|ON}]", "default": "ON", "dynamic": false, "id": "sysvar_authentication_windows_use_principal_name", "name": "authentication_windows_use_principal_name", "scope": ["global"], "type": "boolean"}, {"cli": "--autocommit[={OFF|ON}]", "default": "ON", "dynamic": true, "id": "sysvar_autocommit", "name": "autocommit", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--automatic-sp-privileges[={OFF|ON}]", "default": "ON", "dynamic": true, "id": "sysvar_automatic_sp_privileges", "name": "automatic_sp_privileges", "scope": ["global"], "type": "boolean"}, {"cli": "--auto-generate-certs[={OFF|ON}]", "default": "ON", "dynamic": false, "id": "sysvar_auto_generate_certs", "name": "auto_generate_certs", "scope": ["global"], "type": "boolean"}, {"cli": "--avoid-temporal-upgrade[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_avoid_temporal_upgrade", "name": "avoid_temporal_upgrade", "scope": ["global"], "type": "boolean"}, {"cli": "--back-log=#", "default": "(-1 signifies autosizing; do not use -1)", "dynamic": false, "id": "sysvar_back_log", "name": "back_log", "range": {"from": 1, "to": 65535}, "scope": ["global"], "type": "integer"}, {"cli": "--basedir=dir_name", "dynamic": false, "id": "sysvar_basedir", "name": "basedir", "scope": ["global"], "type": "directory name"}, {"cli": "--big-tables[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_big_tables", "name": "big_tables", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--bind-address=addr", "default": "*", "dynamic": false, "id": "sysvar_bind_address", "name": "bind_address", "scope": ["global"], "type": "string"}, {"cli": "--block-encryption-mode=#", "default": "aes-128-ecb", "dynamic": true, "id": "sysvar_block_encryption_mode", "name": "block_encryption_mode", "scope": ["global", "session"], "type": "string"}, {"cli": "--bulk-insert-buffer-size=#", "default": "8388608", "dynamic": true, "id": "sysvar_bulk_insert_buffer_size", "name": "bulk_insert_buffer_size", "range": {"from": 0}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--caching-sha2-password-auto-generate-rsa-keys[={OFF|ON}]", "default": "ON", "dynamic": false, "id": "sysvar_caching_sha2_password_auto_generate_rsa_keys", "name": "caching_sha2_password_auto_generate_rsa_keys", "scope": ["global"], "type": "boolean"}, {"cli": "--caching-sha2-password-private-key-path=file_name", "default": "private_key.pem", "dynamic": false, "id": "sysvar_caching_sha2_password_private_key_path", "name": "caching_sha2_password_private_key_path", "scope": ["global"], "type": "file name"}, {"cli": "--caching-sha2-password-public-key-path=file_name", "default": "public_key.pem", "dynamic": false, "id": "sysvar_caching_sha2_password_public_key_path", "name": "caching_sha2_password_public_key_path", "scope": ["global"], "type": "file name"}, {"dynamic": true, "id": "sysvar_character_set_client", "name": "character_set_client", "scope": ["global", "session"], "type": "string"}, {"dynamic": true, "id": "sysvar_character_set_connection", "name": "character_set_connection", "scope": ["global", "session"], "type": "string"}, {"dynamic": true, "id": "sysvar_character_set_database", "name": "character_set_database", "scope": ["global", "session"], "type": "string"}, {"cli": "--character-set-filesystem=name", "default": "binary", "dynamic": true, "id": "sysvar_character_set_filesystem", "name": "character_set_filesystem", "scope": ["global", "session"], "type": "string"}, {"dynamic": true, "id": "sysvar_character_set_results", "name": "character_set_results", "scope": ["global", "session"], "type": "string"}, {"cli": "--character-set-server=name", "dynamic": true, "id": "sysvar_character_set_server", "name": "character_set_server", "scope": ["global", "session"], "type": "string"}, {"default": "utf8", "dynamic": false, "id": "sysvar_character_set_system", "name": "character_set_system", "scope": ["global"], "type": "string"}, {"cli": "--character-sets-dir=dir_name", "dynamic": false, "id": "sysvar_character_sets_dir", "name": "character_sets_dir", "scope": ["global"], "type": "directory name"}, {"cli": "--check-proxy-users[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_check_proxy_users", "name": "check_proxy_users", "scope": ["global"], "type": "boolean"}, {"dynamic": true, "id": "sysvar_collation_connection", "name": "collation_connection", "scope": ["global", "session"], "type": "string"}, {"dynamic": true, "id": "sysvar_collation_database", "name": "collation_database", "scope": ["global", "session"], "type": "string"}, {"cli": "--collation-server=name", "dynamic": true, "id": "sysvar_collation_server", "name": "collation_server", "scope": ["global", "session"], "type": "string"}, {"cli": "--completion-type=#", "default": "NO_CHAIN", "dynamic": true, "id": "sysvar_completion_type", "name": "completion_type", "scope": ["global", "session"], "type": "enumeration", "validValues": ["NO_CHAIN", "CHAIN", "RELEASE", "0", "1", "2"]}, {"cli": "--concurrent-insert[=value]", "default": "AUTO", "dynamic": true, "id": "sysvar_concurrent_insert", "name": "concurrent_insert", "scope": ["global"], "type": "enumeration", "validValues": ["NEVER", "AUTO", "ALWAYS", "0", "1", "2"]}, {"cli": "--connect-timeout=#", "default": "10", "dynamic": true, "id": "sysvar_connect_timeout", "name": "connect_timeout", "range": {"from": 2, "to": 31536000}, "scope": ["global"], "type": "integer"}, {"default": "OFF", "dynamic": false, "id": "sysvar_core_file", "name": "core_file", "scope": ["global"], "type": "boolean"}, {"cli": "--create-admin-listener-thread[={OFF|ON}]", "default": "OFF", "dynamic": false, "id": "sysvar_create_admin_listener_thread", "name": "create_admin_listener_thread", "scope": ["global"], "type": "boolean"}, {"cli": "--cte-max-recursion-depth=#", "default": "1000", "dynamic": true, "id": "sysvar_cte_max_recursion_depth", "name": "cte_max_recursion_depth", "range": {"from": 0, "to": 4294967295}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--datadir=dir_name", "dynamic": false, "id": "sysvar_datadir", "name": "datadir", "scope": ["global"], "type": "directory name"}, {"cli": "--debug[=debug_options]", "dynamic": true, "id": "sysvar_debug", "name": "debug", "scope": ["global", "session"], "type": "string"}, {"dynamic": true, "id": "sysvar_debug_sync", "name": "debug_sync", "scope": ["session"], "type": "string"}, {"cli": "--default-authentication-plugin=plugin_name", "dynamic": false, "id": "sysvar_default_authentication_plugin", "name": "default_authentication_plugin", "scope": ["global"], "type": "enumeration"}, {"dynamic": true, "id": "sysvar_default_collation_for_utf8mb4", "name": "default_collation_for_utf8mb4", "scope": ["global", "session"], "type": "enumeration", "validValues": ["utf8mb4_0900_ai_ci", "utf8mb4_general_ci"]}, {"cli": "--default-password-lifetime=#", "default": "0", "dynamic": true, "id": "sysvar_default_password_lifetime", "name": "default_password_lifetime", "range": {"from": 0, "to": 65535}, "scope": ["global"], "type": "integer"}, {"cli": "--default-storage-engine=name", "default": "InnoDB", "dynamic": true, "id": "sysvar_default_storage_engine", "name": "default_storage_engine", "scope": ["global", "session"], "type": "enumeration"}, {"cli": "--default-table-encryption[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_default_table_encryption", "name": "default_table_encryption", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--default-tmp-storage-engine=name", "default": "InnoDB", "dynamic": true, "id": "sysvar_default_tmp_storage_engine", "name": "default_tmp_storage_engine", "scope": ["global", "session"], "type": "enumeration"}, {"cli": "--default-week-format=#", "default": "0", "dynamic": true, "id": "sysvar_default_week_format", "name": "default_week_format", "range": {"from": 0, "to": 7}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--delay-key-write[={OFF|ON|ALL}]", "default": "ON", "dynamic": true, "id": "sysvar_delay_key_write", "name": "delay_key_write", "scope": ["global"], "type": "enumeration", "validValues": ["ON", "OFF", "ALL"]}, {"cli": "--delayed-insert-limit=#", "default": "100", "dynamic": true, "id": "sysvar_delayed_insert_limit", "name": "delayed_insert_limit", "range": {"from": 1}, "scope": ["global"], "type": "integer"}, {"cli": "--delayed-insert-timeout=#", "default": "300", "dynamic": true, "id": "sysvar_delayed_insert_timeout", "name": "delayed_insert_timeout", "scope": ["global"], "type": "integer"}, {"cli": "--delayed-queue-size=#", "default": "1000", "dynamic": true, "id": "sysvar_delayed_queue_size", "name": "delayed_queue_size", "range": {"from": 1}, "scope": ["global"], "type": "integer"}, {"cli": "--disabled-storage-engines=engine[,engine]...", "default": "empty string", "dynamic": false, "id": "sysvar_disabled_storage_engines", "name": "disabled_storage_engines", "scope": ["global"], "type": "string"}, {"cli": "--disconnect-on-expired-password[={OFF|ON}]", "default": "ON", "dynamic": false, "id": "sysvar_disconnect_on_expired_password", "name": "disconnect_on_expired_password", "scope": ["global"], "type": "boolean"}, {"cli": "--div-precision-increment=#", "default": "4", "dynamic": true, "id": "sysvar_div_precision_increment", "name": "div_precision_increment", "range": {"from": 0, "to": 30}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--dragnet.log-error-filter-rules=value", "default": "IF prio>=INFORMATION THEN drop. IF EXISTS source_line THEN unset source_line.", "dynamic": true, "id": "sysvar_dragnet.log_error_filter_rules", "name": "dragnet.log_error_filter_rules", "scope": ["global"], "type": "string"}, {"cli": "--end-markers-in-json[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_end_markers_in_json", "name": "end_markers_in_json", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--eq-range-index-dive-limit=#", "default": "200", "dynamic": true, "id": "sysvar_eq_range_index_dive_limit", "name": "eq_range_index_dive_limit", "range": {"from": 0, "to": 4294967295}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--event-scheduler[=value]", "dynamic": true, "id": "sysvar_event_scheduler", "name": "event_scheduler", "scope": ["global"], "type": "enumeration", "validValues": ["ON", "OFF", "DISABLED"]}, {"cli": "--explicit-defaults-for-timestamp[={OFF|ON}]", "dynamic": true, "id": "sysvar_explicit_defaults_for_timestamp", "name": "explicit_defaults_for_timestamp", "scope": ["global", "session"], "type": "boolean"}, {"dynamic": false, "id": "sysvar_external_user", "name": "external_user", "scope": ["session"], "type": "string"}, {"cli": "--flush[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_flush", "name": "flush", "scope": ["global"], "type": "boolean"}, {"cli": "--flush-time=#", "default": "0", "dynamic": true, "id": "sysvar_flush_time", "name": "flush_time", "range": {"from": 0}, "scope": ["global"], "type": "integer"}, {"default": "ON", "dynamic": true, "id": "sysvar_foreign_key_checks", "name": "foreign_key_checks", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--ft-boolean-syntax=name", "default": "+ -><()~*:\"\"&|", "dynamic": true, "id": "sysvar_ft_boolean_syntax", "name": "ft_boolean_syntax", "scope": ["global"], "type": "string"}, {"cli": "--ft-max-word-len=#", "dynamic": false, "id": "sysvar_ft_max_word_len", "name": "ft_max_word_len", "range": {"from": 10}, "scope": ["global"], "type": "integer"}, {"cli": "--ft-min-word-len=#", "default": "4", "dynamic": false, "id": "sysvar_ft_min_word_len", "name": "ft_min_word_len", "range": {"from": 1}, "scope": ["global"], "type": "integer"}, {"cli": "--ft-query-expansion-limit=#", "default": "20", "dynamic": false, "id": "sysvar_ft_query_expansion_limit", "name": "ft_query_expansion_limit", "range": {"from": 0, "to": 1000}, "scope": ["global"], "type": "integer"}, {"cli": "--ft-stopword-file=file_name", "dynamic": false, "id": "sysvar_ft_stopword_file", "name": "ft_stopword_file", "scope": ["global"], "type": "file name"}, {"cli": "--general-log[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_general_log", "name": "general_log", "scope": ["global"], "type": "boolean"}, {"cli": "--general-log-file=file_name", "default": "host_name.log", "dynamic": true, "id": "sysvar_general_log_file", "name": "general_log_file", "scope": ["global"], "type": "file name"}, {"cli": "--generated-random-password-length=#", "default": "20", "dynamic": true, "id": "sysvar_generated_random_password_length", "name": "generated_random_password_length", "range": {"from": 5, "to": 255}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--group-concat-max-len=#", "default": "1024", "dynamic": true, "id": "sysvar_group_concat_max_len", "name": "group_concat_max_len", "range": {"from": 4}, "scope": ["global", "session"], "type": "integer"}, {"dynamic": false, "id": "sysvar_have_ssl", "name": "have_ssl", "scope": ["global"], "type": "string", "validValues": ["YES", "DISABLED"]}, {"dynamic": false, "id": "sysvar_have_statement_timeout", "name": "have_statement_timeout", "scope": ["global"], "type": "boolean"}, {"cli": "--histogram-generation-max-mem-size=#", "default": "20000000", "dynamic": true, "id": "sysvar_histogram_generation_max_mem_size", "name": "histogram_generation_max_mem_size", "range": {"from": 1000000}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--host-cache-size=#", "default": "(-1 signifies autosizing; do not use -1)", "dynamic": true, "id": "sysvar_host_cache_size", "name": "host_cache_size", "range": {"from": 0, "to": 65536}, "scope": ["global"], "type": "integer"}, {"dynamic": false, "id": "sysvar_hostname", "name": "hostname", "scope": ["global"], "type": "string"}, {"cli": "--init-connect=name", "dynamic": true, "id": "sysvar_init_connect", "name": "init_connect", "scope": ["global"], "type": "string"}, {"cli": "--information-schema-stats-expiry=#", "default": "86400", "dynamic": true, "id": "sysvar_information_schema_stats_expiry", "name": "information_schema_stats_expiry", "range": {"from": 0, "to": 31536000}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--init-file=file_name", "dynamic": false, "id": "sysvar_init_file", "name": "init_file", "scope": ["global"], "type": "file name"}, {"cli": "--interactive-timeout=#", "default": "28800", "dynamic": true, "id": "sysvar_interactive_timeout", "name": "interactive_timeout", "range": {"from": 1}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--internal-tmp-disk-storage-engine=#", "default": "INNODB", "dynamic": true, "id": "sysvar_internal_tmp_disk_storage_engine", "name": "internal_tmp_disk_storage_engine", "scope": ["global"], "type": "enumeration", "validValues": ["MYISAM", "INNODB"]}, {"cli": "--internal-tmp-mem-storage-engine=#", "default": "TempTable", "dynamic": true, "id": "sysvar_internal_tmp_mem_storage_engine", "name": "internal_tmp_mem_storage_engine", "scope": ["global", "session"], "type": "enumeration", "validValues": ["TempTable", "MEMORY"]}, {"cli": "--join-buffer-size=#", "default": "262144", "dynamic": true, "id": "sysvar_join_buffer_size", "name": "join_buffer_size", "range": {"from": 128}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--keep-files-on-create[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_keep_files_on_create", "name": "keep_files_on_create", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--key-buffer-size=#", "default": "8388608", "dynamic": true, "id": "sysvar_key_buffer_size", "name": "key_buffer_size", "range": {"from": 8}, "scope": ["global"], "type": "integer"}, {"cli": "--key-cache-age-threshold=#", "default": "300", "dynamic": true, "id": "sysvar_key_cache_age_threshold", "name": "key_cache_age_threshold", "range": {"from": 100}, "scope": ["global"], "type": "integer"}, {"cli": "--key-cache-block-size=#", "default": "1024", "dynamic": true, "id": "sysvar_key_cache_block_size", "name": "key_cache_block_size", "range": {"from": 512, "to": 16384}, "scope": ["global"], "type": "integer"}, {"cli": "--key-cache-division-limit=#", "default": "100", "dynamic": true, "id": "sysvar_key_cache_division_limit", "name": "key_cache_division_limit", "range": {"from": 1, "to": 100}, "scope": ["global"], "type": "integer"}, {"dynamic": false, "id": "sysvar_large_files_support", "name": "large_files_support", "scope": ["global"]}, {"cli": "--large-pages[={OFF|ON}]", "default": "OFF", "dynamic": false, "id": "sysvar_large_pages", "name": "large_pages", "scope": ["global"], "type": "boolean"}, {"default": "0", "dynamic": false, "id": "sysvar_large_page_size", "name": "large_page_size", "scope": ["global"], "type": "integer"}, {"cli": "--lc-messages=name", "default": "en_US", "dynamic": true, "id": "sysvar_lc_messages", "name": "lc_messages", "scope": ["global", "session"], "type": "string"}, {"cli": "--lc-messages-dir=dir_name", "dynamic": false, "id": "sysvar_lc_messages_dir", "name": "lc_messages_dir", "scope": ["global"], "type": "directory name"}, {"cli": "--lc-time-names=value", "dynamic": true, "id": "sysvar_lc_time_names", "name": "lc_time_names", "scope": ["global", "session"], "type": "string"}, {"default": "GPL", "dynamic": false, "id": "sysvar_license", "name": "license", "scope": ["global"], "type": "string"}, {"cli": "--local-infile[={OFF|ON}]", "dynamic": true, "id": "sysvar_local_infile", "name": "local_infile", "scope": ["global"], "type": "boolean"}, {"cli": "--lock-wait-timeout=#", "default": "31536000", "dynamic": true, "id": "sysvar_lock_wait_timeout", "name": "lock_wait_timeout", "range": {"from": 1, "to": 31536000}, "scope": ["global", "session"], "type": "integer"}, {"dynamic": false, "id": "sysvar_locked_in_memory", "name": "locked_in_memory", "scope": ["global"]}, {"cli": "--log-error[=file_name]", "dynamic": false, "id": "sysvar_log_error", "name": "log_error", "scope": ["global"], "type": "file name"}, {"cli": "--log-error-filter-rules=value", "default": "set by server", "dynamic": true, "id": "sysvar_log_error_filter_rules", "name": "log_error_filter_rules", "scope": ["global"], "type": "string"}, {"cli": "--log-error-services=value", "default": "log_filter_internal; log_sink_internal", "dynamic": true, "id": "sysvar_log_error_services", "name": "log_error_services", "scope": ["global"], "type": "string"}, {"cli": "--log-error-suppression-list=value", "default": "empty string", "dynamic": true, "id": "sysvar_log_error_suppression_list", "name": "log_error_suppression_list", "scope": ["global"], "type": "string"}, {"cli": "--log-error-verbosity=#", "dynamic": true, "id": "sysvar_log_error_verbosity", "name": "log_error_verbosity", "range": {"from": 1, "to": 3}, "scope": ["global"], "type": "integer"}, {"cli": "--log-output=name", "default": "FILE", "dynamic": true, "id": "sysvar_log_output", "name": "log_output", "scope": ["global"], "type": "set", "validValues": ["TABLE", "FILE", "NONE"]}, {"cli": "--log-queries-not-using-indexes[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_log_queries_not_using_indexes", "name": "log_queries_not_using_indexes", "scope": ["global"], "type": "boolean"}, {"cli": "--log-raw[={OFF|ON}]", "default": "OFF", "id": "sysvar_log_raw", "name": "log_raw", "type": "boolean"}, {"cli": "--log-slow-admin-statements[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_log_slow_admin_statements", "name": "log_slow_admin_statements", "scope": ["global"], "type": "boolean"}, {"cli": "--log-slow-extra[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_log_slow_extra", "name": "log_slow_extra", "scope": ["global"], "type": "boolean"}, {"cli": "--log-syslog[={OFF|ON}]", "dynamic": true, "id": "sysvar_log_syslog", "name": "log_syslog", "scope": ["global"], "type": "boolean"}, {"cli": "--log-syslog-facility=value", "default": "daemon", "dynamic": true, "id": "sysvar_log_syslog_facility", "name": "log_syslog_facility", "scope": ["global"], "type": "string"}, {"cli": "--log-syslog-include-pid[={OFF|ON}]", "default": "ON", "dynamic": true, "id": "sysvar_log_syslog_include_pid", "name": "log_syslog_include_pid", "scope": ["global"], "type": "boolean"}, {"cli": "--log-syslog-tag=tag", "default": "empty string", "dynamic": true, "id": "sysvar_log_syslog_tag", "name": "log_syslog_tag", "scope": ["global"], "type": "string"}, {"cli": "--log-timestamps=#", "default": "UTC", "dynamic": true, "id": "sysvar_log_timestamps", "name": "log_timestamps", "scope": ["global"], "type": "enumeration", "validValues": ["UTC", "SYSTEM"]}, {"cli": "--log-throttle-queries-not-using-indexes=#", "default": "0", "dynamic": true, "id": "sysvar_log_throttle_queries_not_using_indexes", "name": "log_throttle_queries_not_using_indexes", "scope": ["global"], "type": "integer"}, {"cli": "--log-warnings[=#]", "default": "2", "dynamic": true, "id": "sysvar_log_warnings", "name": "log_warnings", "range": {"from": 0}, "scope": ["global"], "type": "integer"}, {"cli": "--long-query-time=#", "default": "10", "dynamic": true, "id": "sysvar_long_query_time", "name": "long_query_time", "range": {"from": 0}, "scope": ["global", "session"], "type": "numeric"}, {"cli": "--low-priority-updates[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_low_priority_updates", "name": "low_priority_updates", "scope": ["global", "session"], "type": "boolean"}, {"dynamic": false, "id": "sysvar_lower_case_file_system", "name": "lower_case_file_system", "scope": ["global"], "type": "boolean"}, {"cli": "--lower-case-table-names[=#]", "default": "0", "dynamic": false, "id": "sysvar_lower_case_table_names", "name": "lower_case_table_names", "range": {"from": 0, "to": 2}, "scope": ["global"], "type": "integer"}, {"cli": "--mandatory-roles=value", "default": "empty string", "dynamic": true, "id": "sysvar_mandatory_roles", "name": "mandatory_roles", "scope": ["global"], "type": "string"}, {"cli": "--max-allowed-packet=#", "dynamic": true, "id": "sysvar_max_allowed_packet", "name": "max_allowed_packet", "range": {"from": 1024, "to": 1073741824}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--max-connect-errors=#", "default": "100", "dynamic": true, "id": "sysvar_max_connect_errors", "name": "max_connect_errors", "range": {"from": 1}, "scope": ["global"], "type": "integer"}, {"cli": "--max-connections=#", "default": "151", "dynamic": true, "id": "sysvar_max_connections", "name": "max_connections", "range": {"from": 1, "to": 100000}, "scope": ["global"], "type": "integer"}, {"cli": "--max-delayed-threads=#", "default": "20", "dynamic": true, "id": "sysvar_max_delayed_threads", "name": "max_delayed_threads", "range": {"from": 0, "to": 16384}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--max-digest-length=#", "default": "1024", "dynamic": false, "id": "sysvar_max_digest_length", "name": "max_digest_length", "range": {"from": 0, "to": 1048576}, "scope": ["global"], "type": "integer"}, {"cli": "--max-error-count=#", "dynamic": true, "id": "sysvar_max_error_count", "name": "max_error_count", "range": {"from": 0, "to": 65535}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--max-execution-time=#", "default": "0", "dynamic": true, "id": "sysvar_max_execution_time", "name": "max_execution_time", "scope": ["global", "session"], "type": "integer"}, {"cli": "--max-heap-table-size=#", "default": "16777216", "dynamic": true, "id": "sysvar_max_heap_table_size", "name": "max_heap_table_size", "range": {"from": 16384}, "scope": ["global", "session"], "type": "integer"}, {"dynamic": true, "id": "sysvar_max_insert_delayed_threads", "name": "max_insert_delayed_threads", "scope": ["global", "session"], "type": "integer"}, {"cli": "--max-join-size=#", "default": "18446744073709551615", "dynamic": true, "id": "sysvar_max_join_size", "name": "max_join_size", "range": {"from": 1, "to": 18446744073709552000}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--max-length-for-sort-data=#", "dynamic": true, "id": "sysvar_max_length_for_sort_data", "name": "max_length_for_sort_data", "range": {"from": 4, "to": 8388608}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--max-points-in-geometry=#", "default": "65536", "dynamic": true, "id": "sysvar_max_points_in_geometry", "name": "max_points_in_geometry", "range": {"from": 3, "to": 1048576}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--max-prepared-stmt-count=#", "default": "16382", "dynamic": true, "id": "sysvar_max_prepared_stmt_count", "name": "max_prepared_stmt_count", "range": {"from": 0}, "scope": ["global"], "type": "integer"}, {"cli": "--max-seeks-for-key=#", "dynamic": true, "id": "sysvar_max_seeks_for_key", "name": "max_seeks_for_key", "range": {"from": 1}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--max-sort-length=#", "default": "1024", "dynamic": true, "id": "sysvar_max_sort_length", "name": "max_sort_length", "range": {"from": 4, "to": 8388608}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--max-sp-recursion-depth[=#]", "default": "0", "dynamic": true, "id": "sysvar_max_sp_recursion_depth", "name": "max_sp_recursion_depth", "range": {"to": 255}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--max-user-connections=#", "default": "0", "dynamic": true, "id": "sysvar_max_user_connections", "name": "max_user_connections", "range": {"from": 0, "to": 4294967295}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--max-write-lock-count=#", "dynamic": true, "id": "sysvar_max_write_lock_count", "name": "max_write_lock_count", "range": {"from": 1}, "scope": ["global"], "type": "integer"}, {"cli": "--mecab-rc-file=file_name", "dynamic": false, "id": "sysvar_mecab_rc_file", "name": "mecab_rc_file", "scope": ["global"], "type": "file name"}, {"cli": "--metadata-locks-cache-size=#", "default": "1024", "dynamic": false, "id": "sysvar_metadata_locks_cache_size", "name": "metadata_locks_cache_size", "range": {"from": 1, "to": 1048576}, "scope": ["global"], "type": "integer"}, {"cli": "--metadata-locks-hash-instances=#", "default": "8", "dynamic": false, "id": "sysvar_metadata_locks_hash_instances", "name": "metadata_locks_hash_instances", "range": {"from": 1, "to": 1024}, "scope": ["global"], "type": "integer"}, {"cli": "--min-examined-row-limit=#", "default": "0", "dynamic": true, "id": "sysvar_min_examined_row_limit", "name": "min_examined_row_limit", "range": {"from": 0}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--multi-range-count=#", "default": "256", "dynamic": true, "id": "sysvar_multi_range_count", "name": "multi_range_count", "range": {"from": 1, "to": 4294967295}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--myisam-data-pointer-size=#", "default": "6", "dynamic": true, "id": "sysvar_myisam_data_pointer_size", "name": "myisam_data_pointer_size", "range": {"from": 2, "to": 7}, "scope": ["global"], "type": "integer"}, {"cli": "--myisam-max-sort-file-size=#", "dynamic": true, "id": "sysvar_myisam_max_sort_file_size", "name": "myisam_max_sort_file_size", "scope": ["global"], "type": "integer"}, {"cli": "--myisam-mmap-size=#", "dynamic": false, "id": "sysvar_myisam_mmap_size", "name": "myisam_mmap_size", "range": {"from": 7}, "scope": ["global"], "type": "integer"}, {"cli": "--myisam-recover-options[=list]", "default": "OFF", "dynamic": false, "id": "sysvar_myisam_recover_options", "name": "myisam_recover_options", "scope": ["global"], "type": "enumeration", "validValues": ["OFF", "DEFAULT", "BACKUP", "FORCE", "QUICK"]}, {"cli": "--myisam-repair-threads=#", "default": "1", "dynamic": true, "id": "sysvar_myisam_repair_threads", "name": "myisam_repair_threads", "range": {"from": 1}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--myisam-sort-buffer-size=#", "default": "8388608", "dynamic": true, "id": "sysvar_myisam_sort_buffer_size", "name": "myisam_sort_buffer_size", "range": {"from": 4096}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--myisam-stats-method=name", "default": "nulls_unequal", "dynamic": true, "id": "sysvar_myisam_stats_method", "name": "myisam_stats_method", "scope": ["global", "session"], "type": "enumeration", "validValues": ["nulls_equal", "nulls_unequal", "nulls_ignored"]}, {"cli": "--myisam-use-mmap[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_myisam_use_mmap", "name": "myisam_use_mmap", "scope": ["global"], "type": "boolean"}, {"cli": "--mysql-native-password-proxy-users[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_mysql_native_password_proxy_users", "name": "mysql_native_password_proxy_users", "scope": ["global"], "type": "boolean"}, {"cli": "--named-pipe[={OFF|ON}]", "default": "OFF", "dynamic": false, "id": "sysvar_named_pipe", "name": "named_pipe", "scope": ["global"], "type": "boolean"}, {"cli": "--named-pipe-full-access-group=value", "default": "*everyone*", "dynamic": false, "id": "sysvar_named_pipe_full_access_group", "name": "named_pipe_full_access_group", "scope": ["global"], "type": "string", "validValues": ["*everyone*", "empty string"]}, {"cli": "--net-buffer-length=#", "default": "16384", "dynamic": true, "id": "sysvar_net_buffer_length", "name": "net_buffer_length", "range": {"from": 1024, "to": 1048576}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--net-read-timeout=#", "default": "30", "dynamic": true, "id": "sysvar_net_read_timeout", "name": "net_read_timeout", "range": {"from": 1}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--net-retry-count=#", "default": "10", "dynamic": true, "id": "sysvar_net_retry_count", "name": "net_retry_count", "range": {"from": 1}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--net-write-timeout=#", "default": "60", "dynamic": true, "id": "sysvar_net_write_timeout", "name": "net_write_timeout", "range": {"from": 1}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--new[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_new", "name": "new", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--ngram-token-size=#", "default": "2", "dynamic": false, "id": "sysvar_ngram_token_size", "name": "ngram_token_size", "range": {"from": 1, "to": 10}, "scope": ["global"], "type": "integer"}, {"cli": "--offline-mode[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_offline_mode", "name": "offline_mode", "scope": ["global"], "type": "boolean"}, {"cli": "--old[={OFF|ON}]", "default": "OFF", "dynamic": false, "id": "sysvar_old", "name": "old", "scope": ["global"], "type": "boolean"}, {"cli": "--old-alter-table[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_old_alter_table", "name": "old_alter_table", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--old-passwords=value", "default": "0", "dynamic": true, "id": "sysvar_old_passwords", "name": "old_passwords", "scope": ["global", "session"], "type": "enumeration", "validValues": ["0", "2"]}, {"cli": "--open-files-limit=#", "default": "5000, with possible adjustment", "dynamic": false, "id": "sysvar_open_files_limit", "name": "open_files_limit", "range": {"from": 0}, "scope": ["global"], "type": "integer"}, {"cli": "--optimizer-prune-level=#", "default": "1", "dynamic": true, "id": "sysvar_optimizer_prune_level", "name": "optimizer_prune_level", "range": {"from": 0, "to": 1}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--optimizer-search-depth=#", "default": "62", "dynamic": true, "id": "sysvar_optimizer_search_depth", "name": "optimizer_search_depth", "range": {"from": 0, "to": 62}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--optimizer-switch=value", "dynamic": true, "id": "sysvar_optimizer_switch", "name": "optimizer_switch", "scope": ["global", "session"], "type": "set"}, {"cli": "--optimizer-trace=value", "dynamic": true, "id": "sysvar_optimizer_trace", "name": "optimizer_trace", "scope": ["global", "session"], "type": "string"}, {"cli": "--optimizer-trace-features=value", "dynamic": true, "id": "sysvar_optimizer_trace_features", "name": "optimizer_trace_features", "scope": ["global", "session"], "type": "string"}, {"cli": "--optimizer-trace-limit=#", "default": "1", "dynamic": true, "id": "sysvar_optimizer_trace_limit", "name": "optimizer_trace_limit", "scope": ["global", "session"], "type": "integer"}, {"cli": "--optimizer-trace-max-mem-size=#", "dynamic": true, "id": "sysvar_optimizer_trace_max_mem_size", "name": "optimizer_trace_max_mem_size", "scope": ["global", "session"], "type": "integer"}, {"cli": "--optimizer-trace-offset=#", "default": "-1", "dynamic": true, "id": "sysvar_optimizer_trace_offset", "name": "optimizer_trace_offset", "scope": ["global", "session"], "type": "integer"}, {"cli": "--parser-max-mem-size=#", "dynamic": true, "id": "sysvar_parser_max_mem_size", "name": "parser_max_mem_size", "range": {"from": 10000000}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--partial-revokes[={OFF|ON}]", "default": "OFF (if partial revokes do not exist)ON (if partial revokes exist)", "dynamic": true, "id": "sysvar_partial_revokes", "name": "partial_revokes", "scope": ["global"], "type": "boolean"}, {"cli": "--password-history=#", "default": "0", "dynamic": true, "id": "sysvar_password_history", "name": "password_history", "range": {"from": 0, "to": 4294967295}, "scope": ["global"], "type": "integer"}, {"cli": "--password-require-current[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_password_require_current", "name": "password_require_current", "scope": ["global"], "type": "boolean"}, {"cli": "--password-reuse-interval=#", "default": "0", "dynamic": true, "id": "sysvar_password_reuse_interval", "name": "password_reuse_interval", "range": {"from": 0, "to": 4294967295}, "scope": ["global"], "type": "integer"}, {"cli": "--persisted-globals-load[={OFF|ON}]", "default": "ON", "dynamic": false, "id": "sysvar_persisted_globals_load", "name": "persisted_globals_load", "scope": ["global"], "type": "boolean"}, {"cli": "--persist-only-admin-x509-subject=string", "default": "empty string", "dynamic": false, "id": "sysvar_persist_only_admin_x509_subject", "name": "persist_only_admin_x509_subject", "scope": ["global"], "type": "string"}, {"cli": "--pid-file=file_name", "dynamic": false, "id": "sysvar_pid_file", "name": "pid_file", "scope": ["global"], "type": "file name"}, {"cli": "--plugin-dir=dir_name", "default": "BASEDIR/lib/plugin", "dynamic": false, "id": "sysvar_plugin_dir", "name": "plugin_dir", "scope": ["global"], "type": "directory name"}, {"cli": "--port=port_num", "default": "3306", "dynamic": false, "id": "sysvar_port", "name": "port", "range": {"from": 0, "to": 65535}, "scope": ["global"], "type": "integer"}, {"cli": "--preload-buffer-size=#", "default": "32768", "dynamic": true, "id": "sysvar_preload_buffer_size", "name": "preload_buffer_size", "range": {"from": 1024, "to": 1073741824}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--print-identified-with-as-hex[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_print_identified_with_as_hex", "name": "print_identified_with_as_hex", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--protocol-compression-algorithms=value", "default": "zlib,zstd,uncompressed", "dynamic": true, "id": "sysvar_protocol_compression_algorithms", "name": "protocol_compression_algorithms", "scope": ["global"], "type": "set", "validValues": ["zlib", "zstd", "uncompressed"]}, {"dynamic": false, "id": "sysvar_protocol_version", "name": "protocol_version", "scope": ["global"], "type": "integer"}, {"dynamic": false, "id": "sysvar_proxy_user", "name": "proxy_user", "scope": ["session"], "type": "string"}, {"dynamic": true, "id": "sysvar_pseudo_slave_mode", "name": "pseudo_slave_mode", "scope": ["session"], "type": "integer"}, {"dynamic": true, "id": "sysvar_pseudo_thread_id", "name": "pseudo_thread_id", "scope": ["session"], "type": "integer"}, {"cli": "--query-alloc-block-size=#", "default": "8192", "dynamic": true, "id": "sysvar_query_alloc_block_size", "name": "query_alloc_block_size", "range": {"from": 1024, "to": 4294967295}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--query-cache-limit=#", "default": "1048576", "dynamic": true, "id": "sysvar_query_cache_limit", "name": "query_cache_limit", "range": {"from": 0}, "scope": ["global"], "type": "integer"}, {"cli": "--query-cache-min-res-unit=#", "default": "4096", "dynamic": true, "id": "sysvar_query_cache_min_res_unit", "name": "query_cache_min_res_unit", "range": {"from": 512}, "scope": ["global"], "type": "integer"}, {"cli": "--query-cache-size=#", "dynamic": true, "id": "sysvar_query_cache_size", "name": "query_cache_size", "range": {"from": 0}, "scope": ["global"], "type": "integer"}, {"cli": "--query-cache-type=#", "default": "0", "dynamic": true, "id": "sysvar_query_cache_type", "name": "query_cache_type", "scope": ["global", "session"], "type": "enumeration", "validValues": ["0", "1", "2"]}, {"cli": "--query-cache-wlock-invalidate[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_query_cache_wlock_invalidate", "name": "query_cache_wlock_invalidate", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--query-prealloc-size=#", "default": "8192", "dynamic": true, "id": "sysvar_query_prealloc_size", "name": "query_prealloc_size", "range": {"from": 8192}, "scope": ["global", "session"], "type": "integer"}, {"dynamic": true, "id": "sysvar_rand_seed1", "name": "rand_seed1", "scope": ["session"], "type": "integer"}, {"cli": "--range-alloc-block-size=#", "default": "4096", "dynamic": true, "id": "sysvar_range_alloc_block_size", "name": "range_alloc_block_size", "range": {"from": 4096, "to": 4294967295}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--range-optimizer-max-mem-size=#", "default": "8388608", "dynamic": true, "id": "sysvar_range_optimizer_max_mem_size", "name": "range_optimizer_max_mem_size", "range": {"from": 0, "to": 18446744073709552000}, "scope": ["global", "session"], "type": "integer"}, {"default": "STRICT", "dynamic": true, "id": "sysvar_rbr_exec_mode", "name": "rbr_exec_mode", "scope": ["global", "session"], "type": "enumeration", "validValues": ["IDEMPOTENT", "STRICT"]}, {"cli": "--read-buffer-size=#", "default": "131072", "dynamic": true, "id": "sysvar_read_buffer_size", "name": "read_buffer_size", "range": {"from": 8200, "to": 2147479552}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--read-only[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_read_only", "name": "read_only", "scope": ["global"], "type": "boolean"}, {"cli": "--read-rnd-buffer-size=#", "default": "262144", "dynamic": true, "id": "sysvar_read_rnd_buffer_size", "name": "read_rnd_buffer_size", "range": {"from": 1, "to": 2147483647}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--regexp-stack-limit=#", "default": "8000000", "dynamic": true, "id": "sysvar_regexp_stack_limit", "name": "regexp_stack_limit", "range": {"from": 0, "to": 2147483647}, "scope": ["global"], "type": "integer"}, {"cli": "--regexp-time-limit=#", "default": "32", "dynamic": true, "id": "sysvar_regexp_time_limit", "name": "regexp_time_limit", "range": {"from": 0, "to": 2147483647}, "scope": ["global"], "type": "integer"}, {"cli": "--require-secure-transport[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_require_secure_transport", "name": "require_secure_transport", "scope": ["global"], "type": "boolean"}, {"default": "FULL", "dynamic": true, "id": "sysvar_resultset_metadata", "name": "resultset_metadata", "scope": ["session"], "type": "enumeration", "validValues": ["FULL", "NONE"]}, {"default": "100000.000000", "dynamic": true, "id": "sysvar_secondary_engine_cost_threshold", "name": "secondary_engine_cost_threshold", "range": {"from": 0}, "scope": ["session"], "type": "numeric"}, {"cli": "--schema-definition-cache=#", "default": "256", "dynamic": true, "id": "sysvar_schema_definition_cache", "name": "schema_definition_cache", "range": {"from": 256, "to": 524288}, "scope": ["global"], "type": "integer"}, {"cli": "--secure-auth[={OFF|ON}]", "default": "ON", "dynamic": true, "id": "sysvar_secure_auth", "name": "secure_auth", "scope": ["global"], "type": "boolean", "validValues": ["ON"]}, {"cli": "--secure-file-priv=dir_name", "default": "platform specific", "dynamic": false, "id": "sysvar_secure_file_priv", "name": "secure_file_priv", "scope": ["global"], "type": "string", "validValues": ["empty string", "dirname", "NULL"]}, {"cli": "--session-track-gtids=value", "default": "OFF", "dynamic": true, "id": "sysvar_session_track_gtids", "name": "session_track_gtids", "scope": ["global", "session"], "type": "enumeration", "validValues": ["OFF", "OWN_GTID", "ALL_GTIDS"]}, {"cli": "--session-track-schema[={OFF|ON}]", "default": "ON", "dynamic": true, "id": "sysvar_session_track_schema", "name": "session_track_schema", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--session-track-state-change[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_session_track_state_change", "name": "session_track_state_change", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--session-track-system-variables=#", "default": "time_zone, autocommit, character_set_client, character_set_results, character_set_connection", "dynamic": true, "id": "sysvar_session_track_system_variables", "name": "session_track_system_variables", "scope": ["global", "session"], "type": "string"}, {"cli": "--session-track-transaction-info=value", "default": "OFF", "dynamic": true, "id": "sysvar_session_track_transaction_info", "name": "session_track_transaction_info", "scope": ["global", "session"], "type": "enumeration", "validValues": ["OFF", "STATE", "CHARACTERISTICS"]}, {"cli": "--sha256-password-auto-generate-rsa-keys[={OFF|ON}]", "default": "ON", "dynamic": false, "id": "sysvar_sha256_password_auto_generate_rsa_keys", "name": "sha256_password_auto_generate_rsa_keys", "scope": ["global"], "type": "boolean"}, {"cli": "--sha256-password-private-key-path=file_name", "default": "private_key.pem", "dynamic": false, "id": "sysvar_sha256_password_private_key_path", "name": "sha256_password_private_key_path", "scope": ["global"], "type": "file name"}, {"cli": "--sha256-password-proxy-users[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_sha256_password_proxy_users", "name": "sha256_password_proxy_users", "scope": ["global"], "type": "boolean"}, {"cli": "--sha256-password-public-key-path=file_name", "default": "public_key.pem", "dynamic": false, "id": "sysvar_sha256_password_public_key_path", "name": "sha256_password_public_key_path", "scope": ["global"], "type": "file name"}, {"cli": "--shared-memory[={OFF|ON}]", "default": "OFF", "dynamic": false, "id": "sysvar_shared_memory", "name": "shared_memory", "scope": ["global"], "type": "boolean"}, {"cli": "--shared-memory-base-name=name", "default": "MYSQL", "dynamic": false, "id": "sysvar_shared_memory_base_name", "name": "shared_memory_base_name", "scope": ["global"], "type": "string"}, {"cli": "--show-compatibility-56[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_show_compatibility_56", "name": "show_compatibility_56", "scope": ["global"], "type": "boolean"}, {"cli": "--show-create-table-skip-secondary-engine[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_show_create_table_skip_secondary_engine", "name": "show_create_table_skip_secondary_engine", "scope": ["session"], "type": "boolean"}, {"cli": "--show-create-table-verbosity[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_show_create_table_verbosity", "name": "show_create_table_verbosity", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--show-old-temporals[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_show_old_temporals", "name": "show_old_temporals", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--skip-external-locking[={OFF|ON}]", "default": "ON", "dynamic": false, "id": "sysvar_skip_external_locking", "name": "skip_external_locking", "scope": ["global"], "type": "boolean"}, {"cli": "--skip-name-resolve[={OFF|ON}]", "default": "OFF", "dynamic": false, "id": "sysvar_skip_name_resolve", "name": "skip_name_resolve", "scope": ["global"], "type": "boolean"}, {"cli": "--skip-networking[={OFF|ON}]", "default": "OFF", "dynamic": false, "id": "sysvar_skip_networking", "name": "skip_networking", "scope": ["global"], "type": "boolean"}, {"cli": "--skip-show-database", "dynamic": false, "id": "sysvar_skip_show_database", "name": "skip_show_database", "scope": ["global"]}, {"cli": "--slow-launch-time=#", "default": "2", "dynamic": true, "id": "sysvar_slow_launch_time", "name": "slow_launch_time", "scope": ["global"], "type": "integer"}, {"cli": "--slow-query-log[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_slow_query_log", "name": "slow_query_log", "scope": ["global"], "type": "boolean"}, {"cli": "--slow-query-log-file=file_name", "default": "host_name-slow.log", "dynamic": true, "id": "sysvar_slow_query_log_file", "name": "slow_query_log_file", "scope": ["global"], "type": "file name"}, {"cli": "--socket={file_name|pipe_name}", "dynamic": false, "id": "sysvar_socket", "name": "socket", "scope": ["global"], "type": "string"}, {"cli": "--sort-buffer-size=#", "default": "262144", "dynamic": true, "id": "sysvar_sort_buffer_size", "name": "sort_buffer_size", "range": {"from": 32768}, "scope": ["global", "session"], "type": "integer"}, {"default": "OFF", "dynamic": true, "id": "sysvar_sql_auto_is_null", "name": "sql_auto_is_null", "scope": ["global", "session"], "type": "boolean"}, {"default": "ON", "dynamic": true, "id": "sysvar_sql_big_selects", "name": "sql_big_selects", "scope": ["global", "session"], "type": "boolean"}, {"default": "OFF", "dynamic": true, "id": "sysvar_sql_buffer_result", "name": "sql_buffer_result", "scope": ["global", "session"], "type": "boolean"}, {"default": "OFF", "dynamic": true, "id": "sysvar_sql_log_off", "name": "sql_log_off", "scope": ["global", "session"], "type": "boolean", "validValues": ["OFF", "ON"]}, {"cli": "--sql-mode=name", "dynamic": true, "id": "sysvar_sql_mode", "name": "sql_mode", "scope": ["global", "session"], "type": "set"}, {"default": "ON", "dynamic": true, "id": "sysvar_sql_notes", "name": "sql_notes", "scope": ["global", "session"], "type": "boolean"}, {"default": "ON", "dynamic": true, "id": "sysvar_sql_quote_show_create", "name": "sql_quote_show_create", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--sql-require-primary-key[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_sql_require_primary_key", "name": "sql_require_primary_key", "scope": ["global", "session"], "type": "boolean"}, {"default": "OFF", "dynamic": true, "id": "sysvar_sql_safe_updates", "name": "sql_safe_updates", "scope": ["global", "session"], "type": "boolean"}, {"dynamic": true, "id": "sysvar_sql_select_limit", "name": "sql_select_limit", "scope": ["global", "session"], "type": "integer"}, {"default": "OFF", "dynamic": true, "id": "sysvar_sql_warnings", "name": "sql_warnings", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--ssl-ca=file_name", "id": "sysvar_ssl_ca", "name": "ssl_ca", "scope": ["global"], "type": "file name"}, {"cli": "--ssl-capath=dir_name", "id": "sysvar_ssl_capath", "name": "ssl_capath", "scope": ["global"], "type": "directory name"}, {"cli": "--ssl-cert=file_name", "id": "sysvar_ssl_cert", "name": "ssl_cert", "scope": ["global"], "type": "file name"}, {"cli": "--ssl-cipher=name", "id": "sysvar_ssl_cipher", "name": "ssl_cipher", "scope": ["global"], "type": "string"}, {"cli": "--ssl-crl=file_name", "id": "sysvar_ssl_crl", "name": "ssl_crl", "scope": ["global"], "type": "file name"}, {"cli": "--ssl-crlpath=dir_name", "id": "sysvar_ssl_crlpath", "name": "ssl_crlpath", "scope": ["global"], "type": "directory name"}, {"cli": "--ssl-fips-mode={OFF|ON|STRICT}", "default": "OFF", "dynamic": true, "id": "sysvar_ssl_fips_mode", "name": "ssl_fips_mode", "scope": ["global"], "type": "enumeration", "validValues": ["OFF", "ON", "STRICT"]}, {"cli": "--ssl-key=file_name", "id": "sysvar_ssl_key", "name": "ssl_key", "scope": ["global"], "type": "file name"}, {"cli": "--stored-program-cache=#", "default": "256", "dynamic": true, "id": "sysvar_stored_program_cache", "name": "stored_program_cache", "range": {"from": 16, "to": 524288}, "scope": ["global"], "type": "integer"}, {"cli": "--stored-program-definition-cache=#", "default": "256", "dynamic": true, "id": "sysvar_stored_program_definition_cache", "name": "stored_program_definition_cache", "range": {"from": 256, "to": 524288}, "scope": ["global"], "type": "integer"}, {"cli": "--super-read-only[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_super_read_only", "name": "super_read_only", "scope": ["global"], "type": "boolean"}, {"cli": "--syseventlog.facility=value", "default": "daemon", "dynamic": true, "id": "sysvar_syseventlog.facility", "name": "syseventlog.facility", "scope": ["global"], "type": "string"}, {"cli": "--syseventlog.include-pid[={OFF|ON}]", "default": "ON", "dynamic": true, "id": "sysvar_syseventlog.include_pid", "name": "syseventlog.include_pid", "scope": ["global"], "type": "boolean"}, {"cli": "--syseventlog.tag=tag", "default": "empty string", "dynamic": true, "id": "sysvar_syseventlog.tag", "name": "syseventlog.tag", "scope": ["global"], "type": "string"}, {"dynamic": false, "id": "sysvar_system_time_zone", "name": "system_time_zone", "scope": ["global"], "type": "string"}, {"cli": "--table-definition-cache=#", "default": "(-1 signifies autosizing; do not use -1)", "dynamic": true, "id": "sysvar_table_definition_cache", "name": "table_definition_cache", "range": {"from": 400, "to": 524288}, "scope": ["global"], "type": "integer"}, {"cli": "--table-encryption-privilege-check[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_table_encryption_privilege_check", "name": "table_encryption_privilege_check", "scope": ["global"], "type": "boolean"}, {"cli": "--table-open-cache=#", "dynamic": true, "id": "sysvar_table_open_cache", "name": "table_open_cache", "range": {"from": 1, "to": 524288}, "scope": ["global"], "type": "integer"}, {"cli": "--table-open-cache-instances=#", "default": "16", "dynamic": false, "id": "sysvar_table_open_cache_instances", "name": "table_open_cache_instances", "range": {"from": 1, "to": 64}, "scope": ["global"], "type": "integer"}, {"cli": "--tablespace-definition-cache=#", "default": "256", "dynamic": true, "id": "sysvar_tablespace_definition_cache", "name": "tablespace_definition_cache", "range": {"from": 256, "to": 524288}, "scope": ["global"], "type": "integer"}, {"cli": "--temptable-max-ram=#", "default": "1073741824", "dynamic": true, "id": "sysvar_temptable_max_ram", "name": "temptable_max_ram", "range": {"from": 2097152, "to": 2}, "scope": ["global"], "type": "integer"}, {"cli": "--temptable-use-mmap[={OFF|ON}]", "default": "ON", "dynamic": true, "id": "sysvar_temptable_use_mmap", "name": "temptable_use_mmap", "scope": ["global"], "type": "boolean"}, {"cli": "--thread-cache-size=#", "default": "(-1 signifies autosizing; do not use -1)", "dynamic": true, "id": "sysvar_thread_cache_size", "name": "thread_cache_size", "range": {"from": 0, "to": 16384}, "scope": ["global"], "type": "integer"}, {"cli": "--thread-handling=name", "default": "one-thread-per-connection", "dynamic": false, "id": "sysvar_thread_handling", "name": "thread_handling", "scope": ["global"], "type": "enumeration", "validValues": ["no-threads", "one-thread-per-connection", "loaded-dynamically"]}, {"cli": "--thread-pool-algorithm=#", "default": "0", "dynamic": false, "id": "sysvar_thread_pool_algorithm", "name": "thread_pool_algorithm", "range": {"from": 0, "to": 1}, "scope": ["global"], "type": "integer"}, {"cli": "--thread-pool-high-priority-connection=#", "default": "0", "dynamic": true, "id": "sysvar_thread_pool_high_priority_connection", "name": "thread_pool_high_priority_connection", "range": {"from": 0, "to": 1}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--thread-pool-max-active-query-threads", "default": "0", "dynamic": true, "id": "sysvar_thread_pool_max_active_query_threads", "name": "thread_pool_max_active_query_threads", "range": {"from": 0, "to": 512}, "scope": ["global"], "type": "integer"}, {"cli": "--thread-pool-max-unused-threads=#", "default": "0", "dynamic": true, "id": "sysvar_thread_pool_max_unused_threads", "name": "thread_pool_max_unused_threads", "range": {"from": 0, "to": 4096}, "scope": ["global"], "type": "integer"}, {"cli": "--thread-pool-prio-kickup-timer=#", "default": "1000", "dynamic": true, "id": "sysvar_thread_pool_prio_kickup_timer", "name": "thread_pool_prio_kickup_timer", "range": {"from": 0, "to": 4294967294}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--thread-pool-size=#", "default": "16", "dynamic": false, "id": "sysvar_thread_pool_size", "name": "thread_pool_size", "range": {"from": 1}, "scope": ["global"], "type": "integer"}, {"cli": "--thread-pool-stall-limit=#", "default": "6", "dynamic": true, "id": "sysvar_thread_pool_stall_limit", "name": "thread_pool_stall_limit", "range": {"from": 4, "to": 600}, "scope": ["global"], "type": "integer"}, {"cli": "--thread-stack=#", "dynamic": false, "id": "sysvar_thread_stack", "name": "thread_stack", "range": {"from": 131072}, "scope": ["global"], "type": "integer"}, {"dynamic": true, "id": "sysvar_time_zone", "name": "time_zone", "scope": ["global", "session"], "type": "string"}, {"dynamic": true, "id": "sysvar_timestamp", "name": "timestamp", "scope": ["session"], "type": "numeric"}, {"cli": "--tls-ciphersuites=ciphersuite_list", "default": "empty string", "dynamic": true, "id": "sysvar_tls_ciphersuites", "name": "tls_ciphersuites", "scope": ["global"], "type": "string"}, {"cli": "--tls-version=protocol_list", "id": "sysvar_tls_version", "name": "tls_version", "scope": ["global"], "type": "string"}, {"cli": "--tmp-table-size=#", "default": "16777216", "dynamic": true, "id": "sysvar_tmp_table_size", "name": "tmp_table_size", "range": {"from": 1024, "to": 18446744073709552000}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--tmpdir=dir_name", "dynamic": false, "id": "sysvar_tmpdir", "name": "tmpdir", "scope": ["global"], "type": "directory name"}, {"cli": "--transaction-alloc-block-size=#", "default": "8192", "dynamic": true, "id": "sysvar_transaction_alloc_block_size", "name": "transaction_alloc_block_size", "range": {"from": 1024, "to": 131072}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--transaction-isolation=name", "default": "REPEATABLE-READ", "dynamic": true, "id": "sysvar_transaction_isolation", "name": "transaction_isolation", "scope": ["global", "session"], "type": "enumeration", "validValues": ["READ-UNCOMMITTED", "READ-COMMITTED", "REPEATABLE-READ", "SERIALIZABLE"]}, {"cli": "--transaction-prealloc-size=#", "default": "4096", "dynamic": true, "id": "sysvar_transaction_prealloc_size", "name": "transaction_prealloc_size", "range": {"from": 1024, "to": 131072}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--transaction-read-only[={OFF|ON}]", "default": "OFF", "dynamic": true, "id": "sysvar_transaction_read_only", "name": "transaction_read_only", "scope": ["global", "session"], "type": "boolean"}, {"default": "REPEATABLE-READ", "dynamic": true, "id": "sysvar_tx_isolation", "name": "tx_isolation", "scope": ["global", "session"], "type": "enumeration", "validValues": ["READ-UNCOMMITTED", "READ-COMMITTED", "REPEATABLE-READ", "SERIALIZABLE"]}, {"default": "OFF", "dynamic": true, "id": "sysvar_tx_read_only", "name": "tx_read_only", "scope": ["global", "session"], "type": "boolean"}, {"default": "ON", "dynamic": true, "id": "sysvar_unique_checks", "name": "unique_checks", "scope": ["global", "session"], "type": "boolean"}, {"cli": "--updatable-views-with-limit[={OFF|ON}]", "default": "1", "dynamic": true, "id": "sysvar_updatable_views_with_limit", "name": "updatable_views_with_limit", "scope": ["global", "session"], "type": "boolean"}, {"default": "ON", "dynamic": true, "id": "sysvar_use_secondary_engine", "name": "use_secondary_engine", "scope": ["session"], "type": "enumeration", "validValues": ["OFF", "ON", "FORCED"]}, {"cli": "--validate-user-plugins[={OFF|ON}]", "default": "ON", "dynamic": false, "id": "sysvar_validate_user_plugins", "name": "validate_user_plugins", "scope": ["global"], "type": "boolean"}, {"dynamic": false, "id": "sysvar_version_comment", "name": "version_comment", "scope": ["global"], "type": "string"}, {"dynamic": false, "id": "sysvar_version_compile_machine", "name": "version_compile_machine", "scope": ["global"], "type": "string"}, {"dynamic": false, "id": "sysvar_version_compile_os", "name": "version_compile_os", "scope": ["global"], "type": "string"}, {"dynamic": false, "id": "sysvar_version_compile_zlib", "name": "version_compile_zlib", "scope": ["global"], "type": "string"}, {"cli": "--wait-timeout=#", "default": "28800", "dynamic": true, "id": "sysvar_wait_timeout", "name": "wait_timeout", "range": {"from": 1}, "scope": ["global", "session"], "type": "integer"}, {"cli": "--windowing-use-high-precision[={OFF|ON}]", "default": "ON", "dynamic": true, "id": "sysvar_windowing_use_high_precision", "name": "windowing_use_high_precision", "scope": ["global", "session"], "type": "boolean"}], "name": "server-system-variables", "url": "https://dev.mysql.com/doc/refman/8.0/en/server-system-variables.html"}